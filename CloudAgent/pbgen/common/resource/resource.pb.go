// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/resource.proto

package resource

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatusCode int32

const (
	StatusCode_UNKNOWN StatusCode = 0
	// Resource doesn't exist in the cluster.
	StatusCode_NOT_FOUND StatusCode = 1
	// Resource exists but is not ready for usage yet.
	StatusCode_NOT_READY StatusCode = 2
	// Resource exists and is fully ready.
	StatusCode_READY StatusCode = 3
	// Resource exists but is in a non-recoverable error state.
	StatusCode_ERROR StatusCode = 4
)

// Enum value maps for StatusCode.
var (
	StatusCode_name = map[int32]string{
		0: "UNKNOWN",
		1: "NOT_FOUND",
		2: "NOT_READY",
		3: "READY",
		4: "ERROR",
	}
	StatusCode_value = map[string]int32{
		"UNKNOWN":   0,
		"NOT_FOUND": 1,
		"NOT_READY": 2,
		"READY":     3,
		"ERROR":     4,
	}
)

func (x StatusCode) Enum() *StatusCode {
	p := new(StatusCode)
	*p = x
	return p
}

func (x StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_resource_proto_enumTypes[0].Descriptor()
}

func (StatusCode) Type() protoreflect.EnumType {
	return &file_common_resource_proto_enumTypes[0]
}

func (x StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusCode.Descriptor instead.
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_common_resource_proto_rawDescGZIP(), []int{0}
}

type Status struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          StatusCode             `protobuf:"varint,1,opt,name=code,proto3,enum=common.resource.StatusCode" json:"code,omitempty"`
	Message       string                 `protobuf:"bytes,2,opt,name=message,proto3" json:"message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Status) Reset() {
	*x = Status{}
	mi := &file_common_resource_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status) ProtoMessage() {}

func (x *Status) ProtoReflect() protoreflect.Message {
	mi := &file_common_resource_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status.ProtoReflect.Descriptor instead.
func (*Status) Descriptor() ([]byte, []int) {
	return file_common_resource_proto_rawDescGZIP(), []int{0}
}

func (x *Status) GetCode() StatusCode {
	if x != nil {
		return x.Code
	}
	return StatusCode_UNKNOWN
}

func (x *Status) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

// Meta contains metadata of a resource, which Cloud Agent uses to unqiuely
// create, identify and locate a resource for Create/Get/Delete
//
// CloudAgent expect this value to be specified by the upstream caller and will
// use this to construct the underlying provisioning. Different types of
// resources may have different constrainsts. CloudAgent expects the upstream
// provided metadata can meet these constraints.
type Meta struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// id uniqely identifies a resouce in a namespace. Depending on the type of
	// resource, there might be other constraints over id. For example some
	// resources requires the id to be unique across namespaces and some resource
	// requires id to be within certain length.
	//
	// Detailed requirements for a certain type of resource should be documented
	// in the resource sepcific APIs.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The namesapce a resource belongs to.  Not all objects are required to be
	// scoped to a namespace -- the value of this field for those objects will be
	// empty.
	Namespace     string `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Meta) Reset() {
	*x = Meta{}
	mi := &file_common_resource_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Meta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Meta) ProtoMessage() {}

func (x *Meta) ProtoReflect() protoreflect.Message {
	mi := &file_common_resource_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Meta.ProtoReflect.Descriptor instead.
func (*Meta) Descriptor() ([]byte, []int) {
	return file_common_resource_proto_rawDescGZIP(), []int{1}
}

func (x *Meta) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Meta) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

var File_common_resource_proto protoreflect.FileDescriptor

var file_common_resource_proto_rawDesc = string([]byte{
	0x0a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0x53, 0x0a, 0x06, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x2f, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63,
	0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x34, 0x0a,
	0x04, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x2a, 0x4d, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64,
	0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d,
	0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a,
	0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x02, 0x12, 0x09, 0x0a, 0x05,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x03, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52,
	0x10, 0x04, 0x42, 0x3c, 0x5a, 0x3a, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_resource_proto_rawDescOnce sync.Once
	file_common_resource_proto_rawDescData []byte
)

func file_common_resource_proto_rawDescGZIP() []byte {
	file_common_resource_proto_rawDescOnce.Do(func() {
		file_common_resource_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_resource_proto_rawDesc), len(file_common_resource_proto_rawDesc)))
	})
	return file_common_resource_proto_rawDescData
}

var file_common_resource_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_resource_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_common_resource_proto_goTypes = []any{
	(StatusCode)(0), // 0: common.resource.StatusCode
	(*Status)(nil),  // 1: common.resource.Status
	(*Meta)(nil),    // 2: common.resource.Meta
}
var file_common_resource_proto_depIdxs = []int32{
	0, // 0: common.resource.Status.code:type_name -> common.resource.StatusCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_resource_proto_init() }
func file_common_resource_proto_init() {
	if File_common_resource_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_resource_proto_rawDesc), len(file_common_resource_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_resource_proto_goTypes,
		DependencyIndexes: file_common_resource_proto_depIdxs,
		EnumInfos:         file_common_resource_proto_enumTypes,
		MessageInfos:      file_common_resource_proto_msgTypes,
	}.Build()
	File_common_resource_proto = out.File
	file_common_resource_proto_goTypes = nil
	file_common_resource_proto_depIdxs = nil
}
