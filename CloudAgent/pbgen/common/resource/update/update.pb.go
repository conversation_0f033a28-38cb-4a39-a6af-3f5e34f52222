// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/update.proto

package update

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type StatusCode int32

const (
	StatusCode_UNKNOWN StatusCode = 0
	// The update request is acknowledged by CloudAgent and will be scheduled
	// for update. This code is for resources requiring a long running update.
	StatusCode_SCHEDULED StatusCode = 1
	// The resource to update not found.
	StatusCode_NOT_FOUND StatusCode = 2
	// The resource update already exists. But may not completed, requires to
	// check resource status.
	StatusCode_ALREADY_EXISTS StatusCode = 3
	// The update is done within the creation RPC. This status is for short
	// running creation that doesn't need to be run async.
	StatusCode_UPDATED StatusCode = 4
)

// Enum value maps for StatusCode.
var (
	StatusCode_name = map[int32]string{
		0: "UNKNOWN",
		1: "SCHEDULED",
		2: "NOT_FOUND",
		3: "ALREADY_EXISTS",
		4: "UPDATED",
	}
	StatusCode_value = map[string]int32{
		"UNKNOWN":        0,
		"SCHEDULED":      1,
		"NOT_FOUND":      2,
		"ALREADY_EXISTS": 3,
		"UPDATED":        4,
	}
)

func (x StatusCode) Enum() *StatusCode {
	p := new(StatusCode)
	*p = x
	return p
}

func (x StatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (StatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_update_proto_enumTypes[0].Descriptor()
}

func (StatusCode) Type() protoreflect.EnumType {
	return &file_common_update_proto_enumTypes[0]
}

func (x StatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use StatusCode.Descriptor instead.
func (StatusCode) EnumDescriptor() ([]byte, []int) {
	return file_common_update_proto_rawDescGZIP(), []int{0}
}

type Status struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Code          StatusCode             `protobuf:"varint,1,opt,name=code,proto3,enum=common.resource.update.StatusCode" json:"code,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Status) Reset() {
	*x = Status{}
	mi := &file_common_update_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Status) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Status) ProtoMessage() {}

func (x *Status) ProtoReflect() protoreflect.Message {
	mi := &file_common_update_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Status.ProtoReflect.Descriptor instead.
func (*Status) Descriptor() ([]byte, []int) {
	return file_common_update_proto_rawDescGZIP(), []int{0}
}

func (x *Status) GetCode() StatusCode {
	if x != nil {
		return x.Code
	}
	return StatusCode_UNKNOWN
}

var File_common_update_proto protoreflect.FileDescriptor

var file_common_update_proto_rawDesc = string([]byte{
	0x0a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x16, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x22, 0x40, 0x0a,
	0x06, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x2a,
	0x58, 0x0a, 0x0a, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x43,
	0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x4c, 0x52, 0x45,
	0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x44, 0x10, 0x04, 0x42, 0x43, 0x5a, 0x41, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61,
	0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_update_proto_rawDescOnce sync.Once
	file_common_update_proto_rawDescData []byte
)

func file_common_update_proto_rawDescGZIP() []byte {
	file_common_update_proto_rawDescOnce.Do(func() {
		file_common_update_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_update_proto_rawDesc), len(file_common_update_proto_rawDesc)))
	})
	return file_common_update_proto_rawDescData
}

var file_common_update_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_update_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_common_update_proto_goTypes = []any{
	(StatusCode)(0), // 0: common.resource.update.StatusCode
	(*Status)(nil),  // 1: common.resource.update.Status
}
var file_common_update_proto_depIdxs = []int32{
	0, // 0: common.resource.update.Status.code:type_name -> common.resource.update.StatusCode
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_update_proto_init() }
func file_common_update_proto_init() {
	if File_common_update_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_update_proto_rawDesc), len(file_common_update_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_update_proto_goTypes,
		DependencyIndexes: file_common_update_proto_depIdxs,
		EnumInfos:         file_common_update_proto_enumTypes,
		MessageInfos:      file_common_update_proto_msgTypes,
	}.Build()
	File_common_update_proto = out.File
	file_common_update_proto_goTypes = nil
	file_common_update_proto_depIdxs = nil
}
