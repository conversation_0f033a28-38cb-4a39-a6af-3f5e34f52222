// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/aws.proto

package aws

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type DBInstanceStatus int32

const (
	DBInstanceStatus_UNKNOWN   DBInstanceStatus = 0
	DBInstanceStatus_NOT_READY DBInstanceStatus = 1
	DBInstanceStatus_AVAILABLE DBInstanceStatus = 2
	DBInstanceStatus_STOPPED   DBInstanceStatus = 3
)

// Enum value maps for DBInstanceStatus.
var (
	DBInstanceStatus_name = map[int32]string{
		0: "UNKNOWN",
		1: "NOT_READY",
		2: "AVAILABLE",
		3: "STOPPED",
	}
	DBInstanceStatus_value = map[string]int32{
		"UNKNOWN":   0,
		"NOT_READY": 1,
		"AVAILABLE": 2,
		"STOPPED":   3,
	}
)

func (x DBInstanceStatus) Enum() *DBInstanceStatus {
	p := new(DBInstanceStatus)
	*p = x
	return p
}

func (x DBInstanceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DBInstanceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_aws_proto_enumTypes[0].Descriptor()
}

func (DBInstanceStatus) Type() protoreflect.EnumType {
	return &file_common_aws_proto_enumTypes[0]
}

func (x DBInstanceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DBInstanceStatus.Descriptor instead.
func (DBInstanceStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_aws_proto_rawDescGZIP(), []int{0}
}

type DBInstanceSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The DB instance identifier. This parameter is stored as a lowercase string. -required-
	DbInstanceIdentifier string `protobuf:"bytes,1,opt,name=db_instance_identifier,json=dbInstanceIdentifier,proto3" json:"db_instance_identifier,omitempty"`
	// The compute and memory capacity of the DB instance. -required-
	DbInstanceClass string `protobuf:"bytes,2,opt,name=db_instance_class,json=dbInstanceClass,proto3" json:"db_instance_class,omitempty"`
	// The amount of storage in gibibytes (GiB) to allocate for the DB instance.
	AllocatedStorage uint32 `protobuf:"varint,3,opt,name=allocated_storage,json=allocatedStorage,proto3" json:"allocated_storage,omitempty"`
	// The name of the database engine to be used for this instance. -required-
	Engine string `protobuf:"bytes,4,opt,name=engine,proto3" json:"engine,omitempty"`
	// The version number of the database engine to use.
	EngineVersion string `protobuf:"bytes,5,opt,name=engine_version,json=engineVersion,proto3" json:"engine_version,omitempty"`
	// The meaning of this parameter differs according to the database engine you use.
	DbName string `protobuf:"bytes,6,opt,name=db_name,json=dbName,proto3" json:"db_name,omitempty"`
	// The name for the master user.
	MasterUsername string `protobuf:"bytes,7,opt,name=master_username,json=masterUsername,proto3" json:"master_username,omitempty"`
	// The password for the master user.
	MasterUserPassword *PasswordSecretRef `protobuf:"bytes,8,opt,name=master_user_password,json=masterUserPassword,proto3" json:"master_user_password,omitempty"`
	// A DB subnet group to associate with this DB instance.
	DbSubnetGroupName string `protobuf:"bytes,9,opt,name=db_subnet_group_name,json=dbSubnetGroupName,proto3" json:"db_subnet_group_name,omitempty"`
	// A list of Amazon EC2 VPC security groups to associate with this DB instance.
	VpcSecurityGroupIds []string `protobuf:"bytes,10,rep,name=vpc_security_group_ids,json=vpcSecurityGroupIds,proto3" json:"vpc_security_group_ids,omitempty"`
	// Tags to assign to the DB instance.
	Tags map[string]string `protobuf:"bytes,11,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// A value that indicates whether the DB instance is encrypted.
	StorageEncrypted bool `protobuf:"varint,12,opt,name=storageEncrypted,proto3" json:"storageEncrypted,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *DBInstanceSpec) Reset() {
	*x = DBInstanceSpec{}
	mi := &file_common_aws_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DBInstanceSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBInstanceSpec) ProtoMessage() {}

func (x *DBInstanceSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_aws_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBInstanceSpec.ProtoReflect.Descriptor instead.
func (*DBInstanceSpec) Descriptor() ([]byte, []int) {
	return file_common_aws_proto_rawDescGZIP(), []int{0}
}

func (x *DBInstanceSpec) GetDbInstanceIdentifier() string {
	if x != nil {
		return x.DbInstanceIdentifier
	}
	return ""
}

func (x *DBInstanceSpec) GetDbInstanceClass() string {
	if x != nil {
		return x.DbInstanceClass
	}
	return ""
}

func (x *DBInstanceSpec) GetAllocatedStorage() uint32 {
	if x != nil {
		return x.AllocatedStorage
	}
	return 0
}

func (x *DBInstanceSpec) GetEngine() string {
	if x != nil {
		return x.Engine
	}
	return ""
}

func (x *DBInstanceSpec) GetEngineVersion() string {
	if x != nil {
		return x.EngineVersion
	}
	return ""
}

func (x *DBInstanceSpec) GetDbName() string {
	if x != nil {
		return x.DbName
	}
	return ""
}

func (x *DBInstanceSpec) GetMasterUsername() string {
	if x != nil {
		return x.MasterUsername
	}
	return ""
}

func (x *DBInstanceSpec) GetMasterUserPassword() *PasswordSecretRef {
	if x != nil {
		return x.MasterUserPassword
	}
	return nil
}

func (x *DBInstanceSpec) GetDbSubnetGroupName() string {
	if x != nil {
		return x.DbSubnetGroupName
	}
	return ""
}

func (x *DBInstanceSpec) GetVpcSecurityGroupIds() []string {
	if x != nil {
		return x.VpcSecurityGroupIds
	}
	return nil
}

func (x *DBInstanceSpec) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *DBInstanceSpec) GetStorageEncrypted() bool {
	if x != nil {
		return x.StorageEncrypted
	}
	return false
}

type PasswordSecretRef struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Namespace     string                 `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	Key           string                 `protobuf:"bytes,3,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PasswordSecretRef) Reset() {
	*x = PasswordSecretRef{}
	mi := &file_common_aws_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PasswordSecretRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PasswordSecretRef) ProtoMessage() {}

func (x *PasswordSecretRef) ProtoReflect() protoreflect.Message {
	mi := &file_common_aws_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PasswordSecretRef.ProtoReflect.Descriptor instead.
func (*PasswordSecretRef) Descriptor() ([]byte, []int) {
	return file_common_aws_proto_rawDescGZIP(), []int{1}
}

func (x *PasswordSecretRef) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *PasswordSecretRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PasswordSecretRef) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type DBInstanceEndpoint struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Address       string                 `protobuf:"bytes,1,opt,name=address,proto3" json:"address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DBInstanceEndpoint) Reset() {
	*x = DBInstanceEndpoint{}
	mi := &file_common_aws_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DBInstanceEndpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DBInstanceEndpoint) ProtoMessage() {}

func (x *DBInstanceEndpoint) ProtoReflect() protoreflect.Message {
	mi := &file_common_aws_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DBInstanceEndpoint.ProtoReflect.Descriptor instead.
func (*DBInstanceEndpoint) Descriptor() ([]byte, []int) {
	return file_common_aws_proto_rawDescGZIP(), []int{2}
}

func (x *DBInstanceEndpoint) GetAddress() string {
	if x != nil {
		return x.Address
	}
	return ""
}

var File_common_aws_proto protoreflect.FileDescriptor

var file_common_aws_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x77, 0x73, 0x22, 0xf6,
	0x04, 0x0a, 0x0e, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x34, 0x0a, 0x16, 0x64, 0x62, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x14, 0x64, 0x62, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x66, 0x69, 0x65, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x64, 0x62, 0x5f, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x64, 0x62, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x43, 0x6c,
	0x61, 0x73, 0x73, 0x12, 0x2b, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64,
	0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x10,
	0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x6e, 0x67, 0x69,
	0x6e, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x65, 0x6e, 0x67, 0x69, 0x6e, 0x65, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12,
	0x17, 0x0a, 0x07, 0x64, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x6d, 0x61, 0x73, 0x74,
	0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x4f, 0x0a, 0x14, 0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x50, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x66, 0x52, 0x12,
	0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x55, 0x73, 0x65, 0x72, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x12, 0x2f, 0x0a, 0x14, 0x64, 0x62, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x64, 0x62, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x16, 0x76, 0x70, 0x63, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x0a, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x13, 0x76, 0x70, 0x63, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x38, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x0b, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x61, 0x77, 0x73, 0x2e, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x12, 0x2a, 0x0a, 0x10, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x63,
	0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x08, 0x52, 0x10, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x45, 0x6e, 0x63, 0x72, 0x79, 0x70, 0x74, 0x65, 0x64, 0x1a, 0x37,
	0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x57, 0x0a, 0x11, 0x50, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x66, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x22, 0x2e, 0x0a, 0x12, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x2a, 0x4a, 0x0a, 0x10, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01,
	0x12, 0x0d, 0x0a, 0x09, 0x41, 0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x02, 0x12,
	0x0b, 0x0a, 0x07, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x03, 0x42, 0x37, 0x5a, 0x35,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x61, 0x77, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_aws_proto_rawDescOnce sync.Once
	file_common_aws_proto_rawDescData []byte
)

func file_common_aws_proto_rawDescGZIP() []byte {
	file_common_aws_proto_rawDescOnce.Do(func() {
		file_common_aws_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_aws_proto_rawDesc), len(file_common_aws_proto_rawDesc)))
	})
	return file_common_aws_proto_rawDescData
}

var file_common_aws_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_aws_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_common_aws_proto_goTypes = []any{
	(DBInstanceStatus)(0),      // 0: common.aws.DBInstanceStatus
	(*DBInstanceSpec)(nil),     // 1: common.aws.DBInstanceSpec
	(*PasswordSecretRef)(nil),  // 2: common.aws.PasswordSecretRef
	(*DBInstanceEndpoint)(nil), // 3: common.aws.DBInstanceEndpoint
	nil,                        // 4: common.aws.DBInstanceSpec.TagsEntry
}
var file_common_aws_proto_depIdxs = []int32{
	2, // 0: common.aws.DBInstanceSpec.master_user_password:type_name -> common.aws.PasswordSecretRef
	4, // 1: common.aws.DBInstanceSpec.tags:type_name -> common.aws.DBInstanceSpec.TagsEntry
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_common_aws_proto_init() }
func file_common_aws_proto_init() {
	if File_common_aws_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_aws_proto_rawDesc), len(file_common_aws_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_aws_proto_goTypes,
		DependencyIndexes: file_common_aws_proto_depIdxs,
		EnumInfos:         file_common_aws_proto_enumTypes,
		MessageInfos:      file_common_aws_proto_msgTypes,
	}.Build()
	File_common_aws_proto = out.File
	file_common_aws_proto_goTypes = nil
	file_common_aws_proto_depIdxs = nil
}
