// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/azr.proto

package azr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PGServerState int32

const (
	PGServerState_UNKNOWN  PGServerState = 0
	PGServerState_UPDATING PGServerState = 1
	PGServerState_READY    PGServerState = 2
	PGServerState_STOPPED  PGServerState = 3
)

// Enum value maps for PGServerState.
var (
	PGServerState_name = map[int32]string{
		0: "UNKNOWN",
		1: "UPDATING",
		2: "READY",
		3: "STOPPED",
	}
	PGServerState_value = map[string]int32{
		"UNKNOWN":  0,
		"UPDATING": 1,
		"READY":    2,
		"STOPPED":  3,
	}
)

func (x PGServerState) Enum() *PGServerState {
	p := new(PGServerState)
	*p = x
	return p
}

func (x PGServerState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PGServerState) Descriptor() protoreflect.EnumDescriptor {
	return file_common_azr_proto_enumTypes[0].Descriptor()
}

func (PGServerState) Type() protoreflect.EnumType {
	return &file_common_azr_proto_enumTypes[0]
}

func (x PGServerState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PGServerState.Descriptor instead.
func (PGServerState) EnumDescriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{0}
}

type PGServerSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AzureName: The name of the resource in Azure. This is often the same as the
	// name of the resource in Kubernetes but it
	// doesn't have to be.
	AzureName string `protobuf:"bytes,1,opt,name=azure_name,json=azureName,proto3" json:"azure_name,omitempty"`
	// Owner: The owner of the resource. The owner controls where the resource goes
	// when it is deployed. The owner also
	// controls the resources lifecycle. When the owner is deleted the resource
	// will also be deleted. Owner is expected to be a
	// reference to a resources.azure.com/ResourceGroup resource
	Owner *ResourceReference `protobuf:"bytes,2,opt,name=owner,proto3" json:"owner,omitempty"`
	// Version: PostgreSQL Server version.
	Version string `protobuf:"bytes,3,opt,name=version,proto3" json:"version,omitempty"`
	// Location: The geo-location where the resource lives -required-
	Location string `protobuf:"bytes,4,opt,name=location,proto3" json:"location,omitempty"`
	// Sku: The SKU (pricing tier) of the server.
	Sku *PGServerSku `protobuf:"bytes,5,opt,name=sku,proto3" json:"sku,omitempty"`
	// Storage: Storage properties of a server.
	Storage *PGServerStorage `protobuf:"bytes,6,opt,name=storage,proto3" json:"storage,omitempty"`
	// AdministratorLogin: The administrator's login name of a server. Can only be
	// specified when the server is being created
	// (and is required for creation).
	AdministratorLogin string `protobuf:"bytes,7,opt,name=administrator_login,json=administratorLogin,proto3" json:"administrator_login,omitempty"`
	// AdministratorLoginPassword: The administrator login password (required for
	// server creation).
	AdministratorLoginPassword *SecretKeyRef `protobuf:"bytes,8,opt,name=administrator_login_password,json=administratorLoginPassword,proto3" json:"administrator_login_password,omitempty"`
	// Network: Network properties of a server.
	Network *PGServerNetwork `protobuf:"bytes,9,opt,name=network,proto3" json:"network,omitempty"`
	// Tags: Resource tags.
	Tags          map[string]string `protobuf:"bytes,10,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PGServerSpec) Reset() {
	*x = PGServerSpec{}
	mi := &file_common_azr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PGServerSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PGServerSpec) ProtoMessage() {}

func (x *PGServerSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_azr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PGServerSpec.ProtoReflect.Descriptor instead.
func (*PGServerSpec) Descriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{0}
}

func (x *PGServerSpec) GetAzureName() string {
	if x != nil {
		return x.AzureName
	}
	return ""
}

func (x *PGServerSpec) GetOwner() *ResourceReference {
	if x != nil {
		return x.Owner
	}
	return nil
}

func (x *PGServerSpec) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PGServerSpec) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *PGServerSpec) GetSku() *PGServerSku {
	if x != nil {
		return x.Sku
	}
	return nil
}

func (x *PGServerSpec) GetStorage() *PGServerStorage {
	if x != nil {
		return x.Storage
	}
	return nil
}

func (x *PGServerSpec) GetAdministratorLogin() string {
	if x != nil {
		return x.AdministratorLogin
	}
	return ""
}

func (x *PGServerSpec) GetAdministratorLoginPassword() *SecretKeyRef {
	if x != nil {
		return x.AdministratorLoginPassword
	}
	return nil
}

func (x *PGServerSpec) GetNetwork() *PGServerNetwork {
	if x != nil {
		return x.Network
	}
	return nil
}

func (x *PGServerSpec) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

type ResourceReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ArmId         string                 `protobuf:"bytes,1,opt,name=arm_id,json=armId,proto3" json:"arm_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceReference) Reset() {
	*x = ResourceReference{}
	mi := &file_common_azr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceReference) ProtoMessage() {}

func (x *ResourceReference) ProtoReflect() protoreflect.Message {
	mi := &file_common_azr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceReference.ProtoReflect.Descriptor instead.
func (*ResourceReference) Descriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{1}
}

func (x *ResourceReference) GetArmId() string {
	if x != nil {
		return x.ArmId
	}
	return ""
}

type PGServerSku struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name: The name of the sku, typically, tier + family + cores, e.g.
	// Standard_D4s_v3. -required-
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Tier: The tier of the particular SKU. e.g. Burstable. -required-
	Tier          string `protobuf:"bytes,2,opt,name=tier,proto3" json:"tier,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PGServerSku) Reset() {
	*x = PGServerSku{}
	mi := &file_common_azr_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PGServerSku) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PGServerSku) ProtoMessage() {}

func (x *PGServerSku) ProtoReflect() protoreflect.Message {
	mi := &file_common_azr_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PGServerSku.ProtoReflect.Descriptor instead.
func (*PGServerSku) Descriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{2}
}

func (x *PGServerSku) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *PGServerSku) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

type PGServerStorage struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// StorageSizeGB: Max storage allowed for a server.
	StorageSizeGb int32 `protobuf:"varint,1,opt,name=storage_size_gb,json=storageSizeGb,proto3" json:"storage_size_gb,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PGServerStorage) Reset() {
	*x = PGServerStorage{}
	mi := &file_common_azr_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PGServerStorage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PGServerStorage) ProtoMessage() {}

func (x *PGServerStorage) ProtoReflect() protoreflect.Message {
	mi := &file_common_azr_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PGServerStorage.ProtoReflect.Descriptor instead.
func (*PGServerStorage) Descriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{3}
}

func (x *PGServerStorage) GetStorageSizeGb() int32 {
	if x != nil {
		return x.StorageSizeGb
	}
	return 0
}

type SecretKeyRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Key that identifies the value to be extracted.
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Name of the Secret to extract a value from.
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretKeyRef) Reset() {
	*x = SecretKeyRef{}
	mi := &file_common_azr_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretKeyRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretKeyRef) ProtoMessage() {}

func (x *SecretKeyRef) ProtoReflect() protoreflect.Message {
	mi := &file_common_azr_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretKeyRef.ProtoReflect.Descriptor instead.
func (*SecretKeyRef) Descriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{4}
}

func (x *SecretKeyRef) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SecretKeyRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type PGServerNetwork struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// DelegatedSubnetResourceReference: delegated subnet arm resource id.
	DelegatedSubnet *ResourceReference `protobuf:"bytes,1,opt,name=delegated_subnet,json=delegatedSubnet,proto3" json:"delegated_subnet,omitempty"`
	// PrivateDnsZoneArmResourceReference: private dns zone arm resource id.
	PrivateDnsZone *ResourceReference `protobuf:"bytes,2,opt,name=private_dns_zone,json=privateDnsZone,proto3" json:"private_dns_zone,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PGServerNetwork) Reset() {
	*x = PGServerNetwork{}
	mi := &file_common_azr_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PGServerNetwork) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PGServerNetwork) ProtoMessage() {}

func (x *PGServerNetwork) ProtoReflect() protoreflect.Message {
	mi := &file_common_azr_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PGServerNetwork.ProtoReflect.Descriptor instead.
func (*PGServerNetwork) Descriptor() ([]byte, []int) {
	return file_common_azr_proto_rawDescGZIP(), []int{5}
}

func (x *PGServerNetwork) GetDelegatedSubnet() *ResourceReference {
	if x != nil {
		return x.DelegatedSubnet
	}
	return nil
}

func (x *PGServerNetwork) GetPrivateDnsZone() *ResourceReference {
	if x != nil {
		return x.PrivateDnsZone
	}
	return nil
}

var File_common_azr_proto protoreflect.FileDescriptor

var file_common_azr_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x7a, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x22, 0xaf,
	0x04, 0x0a, 0x0c, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12,
	0x1d, 0x0a, 0x0a, 0x61, 0x7a, 0x75, 0x72, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x61, 0x7a, 0x75, 0x72, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33,
	0x0a, 0x05, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x05, 0x6f, 0x77,
	0x6e, 0x65, 0x72, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x29, 0x0a, 0x03, 0x73, 0x6b, 0x75,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x61, 0x7a, 0x72, 0x2e, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x6b, 0x75, 0x52,
	0x03, 0x73, 0x6b, 0x75, 0x12, 0x35, 0x0a, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61,
	0x7a, 0x72, 0x2e, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x52, 0x07, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x2f, 0x0a, 0x13, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x6f, 0x67,
	0x69, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x69,
	0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e, 0x12, 0x5a, 0x0a, 0x1c,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x6c, 0x6f,
	0x67, 0x69, 0x6e, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x52, 0x1a, 0x61, 0x64,
	0x6d, 0x69, 0x6e, 0x69, 0x73, 0x74, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x4c, 0x6f, 0x67, 0x69, 0x6e,
	0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x35, 0x0a, 0x07, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x07, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12,
	0x36, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x50, 0x47, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x2a, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x61, 0x72, 0x6d, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x61, 0x72, 0x6d, 0x49, 0x64, 0x22, 0x35, 0x0a, 0x0b,
	0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x6b, 0x75, 0x12, 0x12, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x22, 0x39, 0x0a, 0x0f, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x12, 0x26, 0x0a, 0x0f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x67, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x0d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x47, 0x62, 0x22, 0x34,
	0x0a, 0x0c, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xa4, 0x01, 0x0a, 0x0f, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65,
	0x72, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x12, 0x48, 0x0a, 0x10, 0x64, 0x65, 0x6c, 0x65,
	0x67, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63,
	0x65, 0x52, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x67, 0x61, 0x74, 0x65, 0x64, 0x53, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x12, 0x47, 0x0a, 0x10, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x6e,
	0x73, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x52, 0x0e, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x44, 0x6e, 0x73, 0x5a, 0x6f, 0x6e, 0x65, 0x2a, 0x42, 0x0a, 0x0d, 0x50,
	0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x52, 0x45, 0x41, 0x44, 0x59,
	0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x03, 0x42,
	0x37, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x7a, 0x72, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_azr_proto_rawDescOnce sync.Once
	file_common_azr_proto_rawDescData []byte
)

func file_common_azr_proto_rawDescGZIP() []byte {
	file_common_azr_proto_rawDescOnce.Do(func() {
		file_common_azr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_azr_proto_rawDesc), len(file_common_azr_proto_rawDesc)))
	})
	return file_common_azr_proto_rawDescData
}

var file_common_azr_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_azr_proto_msgTypes = make([]protoimpl.MessageInfo, 7)
var file_common_azr_proto_goTypes = []any{
	(PGServerState)(0),        // 0: common.azr.PGServerState
	(*PGServerSpec)(nil),      // 1: common.azr.PGServerSpec
	(*ResourceReference)(nil), // 2: common.azr.ResourceReference
	(*PGServerSku)(nil),       // 3: common.azr.PGServerSku
	(*PGServerStorage)(nil),   // 4: common.azr.PGServerStorage
	(*SecretKeyRef)(nil),      // 5: common.azr.SecretKeyRef
	(*PGServerNetwork)(nil),   // 6: common.azr.PGServerNetwork
	nil,                       // 7: common.azr.PGServerSpec.TagsEntry
}
var file_common_azr_proto_depIdxs = []int32{
	2, // 0: common.azr.PGServerSpec.owner:type_name -> common.azr.ResourceReference
	3, // 1: common.azr.PGServerSpec.sku:type_name -> common.azr.PGServerSku
	4, // 2: common.azr.PGServerSpec.storage:type_name -> common.azr.PGServerStorage
	5, // 3: common.azr.PGServerSpec.administrator_login_password:type_name -> common.azr.SecretKeyRef
	6, // 4: common.azr.PGServerSpec.network:type_name -> common.azr.PGServerNetwork
	7, // 5: common.azr.PGServerSpec.tags:type_name -> common.azr.PGServerSpec.TagsEntry
	2, // 6: common.azr.PGServerNetwork.delegated_subnet:type_name -> common.azr.ResourceReference
	2, // 7: common.azr.PGServerNetwork.private_dns_zone:type_name -> common.azr.ResourceReference
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_common_azr_proto_init() }
func file_common_azr_proto_init() {
	if File_common_azr_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_azr_proto_rawDesc), len(file_common_azr_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   7,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_azr_proto_goTypes,
		DependencyIndexes: file_common_azr_proto_depIdxs,
		EnumInfos:         file_common_azr_proto_enumTypes,
		MessageInfos:      file_common_azr_proto_msgTypes,
	}.Build()
	File_common_azr_proto = out.File
	file_common_azr_proto_goTypes = nil
	file_common_azr_proto_depIdxs = nil
}
