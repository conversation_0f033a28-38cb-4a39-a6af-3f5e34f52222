// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/postgresql.proto

package postgresql

import (
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type UpdateStatusCode int32

const (
	UpdateStatusCode_UPDATE_UNKNOWN UpdateStatusCode = 0
	// The update request is acknowledged by CloudAgent and will be scheduled
	// for update. This code is for resources requiring a long running update.
	UpdateStatusCode_UPDATE_SCHEDULED UpdateStatusCode = 1
	// The resource to update not found.
	UpdateStatusCode_UPDATE_NOT_FOUND UpdateStatusCode = 2
	// The resource update already exists. But may not completed, requires to
	// check resource status.
	UpdateStatusCode_UPDATE_ALREADY_EXISTS UpdateStatusCode = 3
)

// Enum value maps for UpdateStatusCode.
var (
	UpdateStatusCode_name = map[int32]string{
		0: "UPDATE_UNKNOWN",
		1: "UPDATE_SCHEDULED",
		2: "UPDATE_NOT_FOUND",
		3: "UPDATE_ALREADY_EXISTS",
	}
	UpdateStatusCode_value = map[string]int32{
		"UPDATE_UNKNOWN":        0,
		"UPDATE_SCHEDULED":      1,
		"UPDATE_NOT_FOUND":      2,
		"UPDATE_ALREADY_EXISTS": 3,
	}
)

func (x UpdateStatusCode) Enum() *UpdateStatusCode {
	p := new(UpdateStatusCode)
	*p = x
	return p
}

func (x UpdateStatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpdateStatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_postgresql_proto_enumTypes[0].Descriptor()
}

func (UpdateStatusCode) Type() protoreflect.EnumType {
	return &file_common_postgresql_proto_enumTypes[0]
}

func (x UpdateStatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpdateStatusCode.Descriptor instead.
func (UpdateStatusCode) EnumDescriptor() ([]byte, []int) {
	return file_common_postgresql_proto_rawDescGZIP(), []int{0}
}

type PostgreSqlSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// name of the team the cluster belongs to. Required field.
	// postgresql-operator support teams API and OAuth2 token to connect. But we don't enable teams API and don't use the field.
	TeamId string `protobuf:"bytes,1,opt,name=team_id,json=teamId,proto3" json:"team_id,omitempty"`
	// custom Docker image that overrides the docker_image operator parameter. It should be a Spilo image. Optional.
	DockerImage string `protobuf:"bytes,2,opt,name=docker_image,json=dockerImage,proto3" json:"docker_image,omitempty"`
	// CPU and memory requests and limits for the Postgres container. Optional.
	Resources *k8s.ResourceRequirements `protobuf:"bytes,3,opt,name=resources,proto3" json:"resources,omitempty"`
	// total number of instances for a given cluster. The operator parameters max_instances and min_instances may also adjust this number. Required field.
	NumberOfInstances uint32 `protobuf:"varint,4,opt,name=number_of_instances,json=numberOfInstances,proto3" json:"number_of_instances,omitempty"`
	// properties of the persistent storage that stores Postgres data. Required field.
	Volume *PostgreSqlVolume `protobuf:"bytes,5,opt,name=volume,proto3" json:"volume,omitempty"`
	// a map of usernames to user flags for the users that should be created in the cluster by the operator. Optional.
	Users map[string]*StringArray `protobuf:"bytes,6,rep,name=users,proto3" json:"users,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// a map of database names to database owners for the databases that should be created by the operator. Optional.
	Databases map[string]string `protobuf:"bytes,7,rep,name=databases,proto3" json:"databases,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// Required field.
	Postgresql *PostgreSqlParam `protobuf:"bytes,8,opt,name=postgresql,proto3" json:"postgresql,omitempty"`
	// a list of tolerations that apply to the cluster pods. Optional.
	Tolerations []*k8s.Toleration `protobuf:"bytes,9,rep,name=tolerations,proto3" json:"tolerations,omitempty"`
	// Optional.
	NodeAffinity *k8s.NodeAffinity `protobuf:"bytes,10,opt,name=node_affinity,json=nodeAffinity,proto3" json:"node_affinity,omitempty"`
	// a map of key value pairs that gets attached as annotations to each pod created for the database. Optional.
	PodAnnotations map[string]string `protobuf:"bytes,11,rep,name=pod_annotations,json=podAnnotations,proto3" json:"pod_annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	// a map of key value pairs that gets attached as annotations to the services created for the database cluster. Optional.
	ServiceAnnotations map[string]string `protobuf:"bytes,12,rep,name=service_annotations,json=serviceAnnotations,proto3" json:"service_annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *PostgreSqlSpec) Reset() {
	*x = PostgreSqlSpec{}
	mi := &file_common_postgresql_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostgreSqlSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostgreSqlSpec) ProtoMessage() {}

func (x *PostgreSqlSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_postgresql_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostgreSqlSpec.ProtoReflect.Descriptor instead.
func (*PostgreSqlSpec) Descriptor() ([]byte, []int) {
	return file_common_postgresql_proto_rawDescGZIP(), []int{0}
}

func (x *PostgreSqlSpec) GetTeamId() string {
	if x != nil {
		return x.TeamId
	}
	return ""
}

func (x *PostgreSqlSpec) GetDockerImage() string {
	if x != nil {
		return x.DockerImage
	}
	return ""
}

func (x *PostgreSqlSpec) GetResources() *k8s.ResourceRequirements {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *PostgreSqlSpec) GetNumberOfInstances() uint32 {
	if x != nil {
		return x.NumberOfInstances
	}
	return 0
}

func (x *PostgreSqlSpec) GetVolume() *PostgreSqlVolume {
	if x != nil {
		return x.Volume
	}
	return nil
}

func (x *PostgreSqlSpec) GetUsers() map[string]*StringArray {
	if x != nil {
		return x.Users
	}
	return nil
}

func (x *PostgreSqlSpec) GetDatabases() map[string]string {
	if x != nil {
		return x.Databases
	}
	return nil
}

func (x *PostgreSqlSpec) GetPostgresql() *PostgreSqlParam {
	if x != nil {
		return x.Postgresql
	}
	return nil
}

func (x *PostgreSqlSpec) GetTolerations() []*k8s.Toleration {
	if x != nil {
		return x.Tolerations
	}
	return nil
}

func (x *PostgreSqlSpec) GetNodeAffinity() *k8s.NodeAffinity {
	if x != nil {
		return x.NodeAffinity
	}
	return nil
}

func (x *PostgreSqlSpec) GetPodAnnotations() map[string]string {
	if x != nil {
		return x.PodAnnotations
	}
	return nil
}

func (x *PostgreSqlSpec) GetServiceAnnotations() map[string]string {
	if x != nil {
		return x.ServiceAnnotations
	}
	return nil
}

type StringArray struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         []string               `protobuf:"bytes,1,rep,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StringArray) Reset() {
	*x = StringArray{}
	mi := &file_common_postgresql_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StringArray) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StringArray) ProtoMessage() {}

func (x *StringArray) ProtoReflect() protoreflect.Message {
	mi := &file_common_postgresql_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StringArray.ProtoReflect.Descriptor instead.
func (*StringArray) Descriptor() ([]byte, []int) {
	return file_common_postgresql_proto_rawDescGZIP(), []int{1}
}

func (x *StringArray) GetValue() []string {
	if x != nil {
		return x.Value
	}
	return nil
}

type PostgreSqlVolume struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the size of the target volume. Usual Kubernetes size modifiers, i.e. Gi or Mi, apply. Required.
	Size string `protobuf:"bytes,1,opt,name=size,proto3" json:"size,omitempty"`
	// the name of the Kubernetes storage class to draw the persistent volume from. Optional.
	StorageClass  string `protobuf:"bytes,2,opt,name=storage_class,json=storageClass,proto3" json:"storage_class,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostgreSqlVolume) Reset() {
	*x = PostgreSqlVolume{}
	mi := &file_common_postgresql_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostgreSqlVolume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostgreSqlVolume) ProtoMessage() {}

func (x *PostgreSqlVolume) ProtoReflect() protoreflect.Message {
	mi := &file_common_postgresql_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostgreSqlVolume.ProtoReflect.Descriptor instead.
func (*PostgreSqlVolume) Descriptor() ([]byte, []int) {
	return file_common_postgresql_proto_rawDescGZIP(), []int{2}
}

func (x *PostgreSqlVolume) GetSize() string {
	if x != nil {
		return x.Size
	}
	return ""
}

func (x *PostgreSqlVolume) GetStorageClass() string {
	if x != nil {
		return x.StorageClass
	}
	return ""
}

type PostgreSqlParam struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the Postgres major version of the cluster. Required field.
	Version string `protobuf:"bytes,1,opt,name=version,proto3" json:"version,omitempty"`
	// a dictionary of Postgres parameter names and values to apply to the resulting cluster. Optional.
	Parameters    map[string]string `protobuf:"bytes,2,rep,name=parameters,proto3" json:"parameters,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostgreSqlParam) Reset() {
	*x = PostgreSqlParam{}
	mi := &file_common_postgresql_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostgreSqlParam) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostgreSqlParam) ProtoMessage() {}

func (x *PostgreSqlParam) ProtoReflect() protoreflect.Message {
	mi := &file_common_postgresql_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostgreSqlParam.ProtoReflect.Descriptor instead.
func (*PostgreSqlParam) Descriptor() ([]byte, []int) {
	return file_common_postgresql_proto_rawDescGZIP(), []int{3}
}

func (x *PostgreSqlParam) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

func (x *PostgreSqlParam) GetParameters() map[string]string {
	if x != nil {
		return x.Parameters
	}
	return nil
}

type Credentials struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Username      string                 `protobuf:"bytes,1,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,2,opt,name=password,proto3" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Credentials) Reset() {
	*x = Credentials{}
	mi := &file_common_postgresql_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Credentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Credentials) ProtoMessage() {}

func (x *Credentials) ProtoReflect() protoreflect.Message {
	mi := &file_common_postgresql_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Credentials.ProtoReflect.Descriptor instead.
func (*Credentials) Descriptor() ([]byte, []int) {
	return file_common_postgresql_proto_rawDescGZIP(), []int{4}
}

func (x *Credentials) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *Credentials) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

var File_common_postgresql_proto protoreflect.FileDescriptor

var file_common_postgresql_proto_rawDesc = string([]byte{
	0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65,
	0x73, 0x71, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x1a, 0x10, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb8,
	0x08, 0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x65, 0x61, 0x6d, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x65, 0x61, 0x6d, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x6f,
	0x63, 0x6b, 0x65, 0x72, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x6f, 0x63, 0x6b, 0x65, 0x72, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x3e, 0x0a,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x2e, 0x0a,
	0x13, 0x6e, 0x75, 0x6d, 0x62, 0x65, 0x72, 0x5f, 0x6f, 0x66, 0x5f, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x11, 0x6e, 0x75, 0x6d, 0x62,
	0x65, 0x72, 0x4f, 0x66, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x73, 0x12, 0x3b, 0x0a,
	0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71,
	0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x52, 0x06, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x42, 0x0a, 0x05, 0x75, 0x73,
	0x65, 0x72, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x55, 0x73, 0x65,
	0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x05, 0x75, 0x73, 0x65, 0x72, 0x73, 0x12, 0x4e,
	0x0a, 0x09, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67,
	0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c,
	0x53, 0x70, 0x65, 0x63, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x09, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x12, 0x42,
	0x0a, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71,
	0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x52, 0x0a, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73,
	0x71, 0x6c, 0x12, 0x38, 0x0a, 0x0b, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x0b, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3d, 0x0a, 0x0d,
	0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x0a, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x6e,
	0x6f, 0x64, 0x65, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x5e, 0x0a, 0x0f, 0x70,
	0x6f, 0x64, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x0b,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65,
	0x53, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x50, 0x6f, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0e, 0x70, 0x6f, 0x64,
	0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x6a, 0x0a, 0x13, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f, 0x73,
	0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x58, 0x0a, 0x0a, 0x55, 0x73, 0x65, 0x72, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x34, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e,
	0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x3c, 0x0a, 0x0e, 0x44, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a,
	0x41, 0x0a, 0x13, 0x50, 0x6f, 0x64, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x45, 0x0a, 0x17, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x23, 0x0a, 0x0b, 0x53, 0x74, 0x72,
	0x69, 0x6e, 0x67, 0x41, 0x72, 0x72, 0x61, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x4b,
	0x0a, 0x10, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67,
	0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73,
	0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x0f,
	0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x12,
	0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x0a, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71,
	0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x50, 0x61, 0x72, 0x61,
	0x6d, 0x2e, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x0a, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x1a, 0x3d, 0x0a,
	0x0f, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x65, 0x74, 0x65, 0x72, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x45, 0x0a, 0x0b,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x1a, 0x0a, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77,
	0x6f, 0x72, 0x64, 0x2a, 0x6d, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x12, 0x0a, 0x0e, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x14, 0x0a, 0x10, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x5f, 0x4e, 0x4f, 0x54, 0x5f,
	0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x45, 0x5f, 0x41, 0x4c, 0x52, 0x45, 0x41, 0x44, 0x59, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x53,
	0x10, 0x03, 0x42, 0x3e, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73,
	0x71, 0x6c, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_postgresql_proto_rawDescOnce sync.Once
	file_common_postgresql_proto_rawDescData []byte
)

func file_common_postgresql_proto_rawDescGZIP() []byte {
	file_common_postgresql_proto_rawDescOnce.Do(func() {
		file_common_postgresql_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_postgresql_proto_rawDesc), len(file_common_postgresql_proto_rawDesc)))
	})
	return file_common_postgresql_proto_rawDescData
}

var file_common_postgresql_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_common_postgresql_proto_msgTypes = make([]protoimpl.MessageInfo, 10)
var file_common_postgresql_proto_goTypes = []any{
	(UpdateStatusCode)(0),            // 0: common.postgresql.UpdateStatusCode
	(*PostgreSqlSpec)(nil),           // 1: common.postgresql.PostgreSqlSpec
	(*StringArray)(nil),              // 2: common.postgresql.StringArray
	(*PostgreSqlVolume)(nil),         // 3: common.postgresql.PostgreSqlVolume
	(*PostgreSqlParam)(nil),          // 4: common.postgresql.PostgreSqlParam
	(*Credentials)(nil),              // 5: common.postgresql.Credentials
	nil,                              // 6: common.postgresql.PostgreSqlSpec.UsersEntry
	nil,                              // 7: common.postgresql.PostgreSqlSpec.DatabasesEntry
	nil,                              // 8: common.postgresql.PostgreSqlSpec.PodAnnotationsEntry
	nil,                              // 9: common.postgresql.PostgreSqlSpec.ServiceAnnotationsEntry
	nil,                              // 10: common.postgresql.PostgreSqlParam.ParametersEntry
	(*k8s.ResourceRequirements)(nil), // 11: common.k8s.ResourceRequirements
	(*k8s.Toleration)(nil),           // 12: common.k8s.Toleration
	(*k8s.NodeAffinity)(nil),         // 13: common.k8s.NodeAffinity
}
var file_common_postgresql_proto_depIdxs = []int32{
	11, // 0: common.postgresql.PostgreSqlSpec.resources:type_name -> common.k8s.ResourceRequirements
	3,  // 1: common.postgresql.PostgreSqlSpec.volume:type_name -> common.postgresql.PostgreSqlVolume
	6,  // 2: common.postgresql.PostgreSqlSpec.users:type_name -> common.postgresql.PostgreSqlSpec.UsersEntry
	7,  // 3: common.postgresql.PostgreSqlSpec.databases:type_name -> common.postgresql.PostgreSqlSpec.DatabasesEntry
	4,  // 4: common.postgresql.PostgreSqlSpec.postgresql:type_name -> common.postgresql.PostgreSqlParam
	12, // 5: common.postgresql.PostgreSqlSpec.tolerations:type_name -> common.k8s.Toleration
	13, // 6: common.postgresql.PostgreSqlSpec.node_affinity:type_name -> common.k8s.NodeAffinity
	8,  // 7: common.postgresql.PostgreSqlSpec.pod_annotations:type_name -> common.postgresql.PostgreSqlSpec.PodAnnotationsEntry
	9,  // 8: common.postgresql.PostgreSqlSpec.service_annotations:type_name -> common.postgresql.PostgreSqlSpec.ServiceAnnotationsEntry
	10, // 9: common.postgresql.PostgreSqlParam.parameters:type_name -> common.postgresql.PostgreSqlParam.ParametersEntry
	2,  // 10: common.postgresql.PostgreSqlSpec.UsersEntry.value:type_name -> common.postgresql.StringArray
	11, // [11:11] is the sub-list for method output_type
	11, // [11:11] is the sub-list for method input_type
	11, // [11:11] is the sub-list for extension type_name
	11, // [11:11] is the sub-list for extension extendee
	0,  // [0:11] is the sub-list for field type_name
}

func init() { file_common_postgresql_proto_init() }
func file_common_postgresql_proto_init() {
	if File_common_postgresql_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_postgresql_proto_rawDesc), len(file_common_postgresql_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   10,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_postgresql_proto_goTypes,
		DependencyIndexes: file_common_postgresql_proto_depIdxs,
		EnumInfos:         file_common_postgresql_proto_enumTypes,
		MessageInfos:      file_common_postgresql_proto_msgTypes,
	}.Build()
	File_common_postgresql_proto = out.File
	file_common_postgresql_proto_goTypes = nil
	file_common_postgresql_proto_depIdxs = nil
}
