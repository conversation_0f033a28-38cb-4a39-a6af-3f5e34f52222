// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/byoc.proto

package byoc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ModuleOptions struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	ModulePath            string                 `protobuf:"bytes,1,opt,name=module_path,json=modulePath,proto3" json:"module_path,omitempty"`
	BackendConfigFileName string                 `protobuf:"bytes,2,opt,name=backend_config_file_name,json=backendConfigFileName,proto3" json:"backend_config_file_name,omitempty"`
	BackendConfig         []byte                 `protobuf:"bytes,3,opt,name=backend_config,json=backendConfig,proto3" json:"backend_config,omitempty"`
	SensitiveVariables    map[string]string      `protobuf:"bytes,8,rep,name=sensitive_variables,json=sensitiveVariables,proto3" json:"sensitive_variables,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	VariableFileName      string                 `protobuf:"bytes,4,opt,name=variable_file_name,json=variableFileName,proto3" json:"variable_file_name,omitempty"`
	VariablePayload       []byte                 `protobuf:"bytes,5,opt,name=variable_payload,json=variablePayload,proto3" json:"variable_payload,omitempty"`
	// If specified, for each key, in increasing order, replace all occurrences of
	// the key with the mapped environment variable values.
	// If the environment variable is not set, an empty string will be used for
	// replacement.
	BackendConfigEnvVarOverrides map[string]string `protobuf:"bytes,6,rep,name=backend_config_env_var_overrides,json=backendConfigEnvVarOverrides,proto3" json:"backend_config_env_var_overrides,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	TaskrunnerEnvOverrides       map[string]string `protobuf:"bytes,7,rep,name=taskrunner_env_overrides,json=taskrunnerEnvOverrides,proto3" json:"taskrunner_env_overrides,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *ModuleOptions) Reset() {
	*x = ModuleOptions{}
	mi := &file_common_byoc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ModuleOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ModuleOptions) ProtoMessage() {}

func (x *ModuleOptions) ProtoReflect() protoreflect.Message {
	mi := &file_common_byoc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ModuleOptions.ProtoReflect.Descriptor instead.
func (*ModuleOptions) Descriptor() ([]byte, []int) {
	return file_common_byoc_proto_rawDescGZIP(), []int{0}
}

func (x *ModuleOptions) GetModulePath() string {
	if x != nil {
		return x.ModulePath
	}
	return ""
}

func (x *ModuleOptions) GetBackendConfigFileName() string {
	if x != nil {
		return x.BackendConfigFileName
	}
	return ""
}

func (x *ModuleOptions) GetBackendConfig() []byte {
	if x != nil {
		return x.BackendConfig
	}
	return nil
}

func (x *ModuleOptions) GetSensitiveVariables() map[string]string {
	if x != nil {
		return x.SensitiveVariables
	}
	return nil
}

func (x *ModuleOptions) GetVariableFileName() string {
	if x != nil {
		return x.VariableFileName
	}
	return ""
}

func (x *ModuleOptions) GetVariablePayload() []byte {
	if x != nil {
		return x.VariablePayload
	}
	return nil
}

func (x *ModuleOptions) GetBackendConfigEnvVarOverrides() map[string]string {
	if x != nil {
		return x.BackendConfigEnvVarOverrides
	}
	return nil
}

func (x *ModuleOptions) GetTaskrunnerEnvOverrides() map[string]string {
	if x != nil {
		return x.TaskrunnerEnvOverrides
	}
	return nil
}

type TFInitOptions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Retry         int32                  `protobuf:"varint,1,opt,name=retry,proto3" json:"retry,omitempty"`
	RetryInterval *durationpb.Duration   `protobuf:"bytes,2,opt,name=retry_interval,json=retryInterval,proto3" json:"retry_interval,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TFInitOptions) Reset() {
	*x = TFInitOptions{}
	mi := &file_common_byoc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TFInitOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TFInitOptions) ProtoMessage() {}

func (x *TFInitOptions) ProtoReflect() protoreflect.Message {
	mi := &file_common_byoc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TFInitOptions.ProtoReflect.Descriptor instead.
func (*TFInitOptions) Descriptor() ([]byte, []int) {
	return file_common_byoc_proto_rawDescGZIP(), []int{1}
}

func (x *TFInitOptions) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *TFInitOptions) GetRetryInterval() *durationpb.Duration {
	if x != nil {
		return x.RetryInterval
	}
	return nil
}

type ApplyOptions struct {
	state                  protoimpl.MessageState `protogen:"open.v1"`
	Retry                  int32                  `protobuf:"varint,1,opt,name=retry,proto3" json:"retry,omitempty"`
	RetryInterval          *durationpb.Duration   `protobuf:"bytes,2,opt,name=retry_interval,json=retryInterval,proto3" json:"retry_interval,omitempty"`
	GracefulShutdownPeriod *durationpb.Duration   `protobuf:"bytes,3,opt,name=graceful_shutdown_period,json=gracefulShutdownPeriod,proto3" json:"graceful_shutdown_period,omitempty"`
	LockExpirationDuration *durationpb.Duration   `protobuf:"bytes,4,opt,name=lock_expiration_duration,json=lockExpirationDuration,proto3" json:"lock_expiration_duration,omitempty"`
	InitOptions            *TFInitOptions         `protobuf:"bytes,5,opt,name=init_options,json=initOptions,proto3" json:"init_options,omitempty"`
	unknownFields          protoimpl.UnknownFields
	sizeCache              protoimpl.SizeCache
}

func (x *ApplyOptions) Reset() {
	*x = ApplyOptions{}
	mi := &file_common_byoc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyOptions) ProtoMessage() {}

func (x *ApplyOptions) ProtoReflect() protoreflect.Message {
	mi := &file_common_byoc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyOptions.ProtoReflect.Descriptor instead.
func (*ApplyOptions) Descriptor() ([]byte, []int) {
	return file_common_byoc_proto_rawDescGZIP(), []int{2}
}

func (x *ApplyOptions) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *ApplyOptions) GetRetryInterval() *durationpb.Duration {
	if x != nil {
		return x.RetryInterval
	}
	return nil
}

func (x *ApplyOptions) GetGracefulShutdownPeriod() *durationpb.Duration {
	if x != nil {
		return x.GracefulShutdownPeriod
	}
	return nil
}

func (x *ApplyOptions) GetLockExpirationDuration() *durationpb.Duration {
	if x != nil {
		return x.LockExpirationDuration
	}
	return nil
}

func (x *ApplyOptions) GetInitOptions() *TFInitOptions {
	if x != nil {
		return x.InitOptions
	}
	return nil
}

type PackageOptions struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	RootPath          string                 `protobuf:"bytes,1,opt,name=root_path,json=rootPath,proto3" json:"root_path,omitempty"`
	TfVersionFilePath string                 `protobuf:"bytes,2,opt,name=tf_version_file_path,json=tfVersionFilePath,proto3" json:"tf_version_file_path,omitempty"`
	PackageUrl        string                 `protobuf:"bytes,3,opt,name=package_url,json=packageUrl,proto3" json:"package_url,omitempty"`
	PackageDestName   string                 `protobuf:"bytes,4,opt,name=package_dest_name,json=packageDestName,proto3" json:"package_dest_name,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PackageOptions) Reset() {
	*x = PackageOptions{}
	mi := &file_common_byoc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PackageOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PackageOptions) ProtoMessage() {}

func (x *PackageOptions) ProtoReflect() protoreflect.Message {
	mi := &file_common_byoc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PackageOptions.ProtoReflect.Descriptor instead.
func (*PackageOptions) Descriptor() ([]byte, []int) {
	return file_common_byoc_proto_rawDescGZIP(), []int{3}
}

func (x *PackageOptions) GetRootPath() string {
	if x != nil {
		return x.RootPath
	}
	return ""
}

func (x *PackageOptions) GetTfVersionFilePath() string {
	if x != nil {
		return x.TfVersionFilePath
	}
	return ""
}

func (x *PackageOptions) GetPackageUrl() string {
	if x != nil {
		return x.PackageUrl
	}
	return ""
}

func (x *PackageOptions) GetPackageDestName() string {
	if x != nil {
		return x.PackageDestName
	}
	return ""
}

type OutputOptions struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Retry         int32                  `protobuf:"varint,1,opt,name=retry,proto3" json:"retry,omitempty"`
	RetryInterval *durationpb.Duration   `protobuf:"bytes,2,opt,name=retry_interval,json=retryInterval,proto3" json:"retry_interval,omitempty"`
	InitOptions   *TFInitOptions         `protobuf:"bytes,3,opt,name=init_options,json=initOptions,proto3" json:"init_options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *OutputOptions) Reset() {
	*x = OutputOptions{}
	mi := &file_common_byoc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *OutputOptions) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OutputOptions) ProtoMessage() {}

func (x *OutputOptions) ProtoReflect() protoreflect.Message {
	mi := &file_common_byoc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OutputOptions.ProtoReflect.Descriptor instead.
func (*OutputOptions) Descriptor() ([]byte, []int) {
	return file_common_byoc_proto_rawDescGZIP(), []int{4}
}

func (x *OutputOptions) GetRetry() int32 {
	if x != nil {
		return x.Retry
	}
	return 0
}

func (x *OutputOptions) GetRetryInterval() *durationpb.Duration {
	if x != nil {
		return x.RetryInterval
	}
	return nil
}

func (x *OutputOptions) GetInitOptions() *TFInitOptions {
	if x != nil {
		return x.InitOptions
	}
	return nil
}

var File_common_byoc_proto protoreflect.FileDescriptor

var file_common_byoc_proto_rawDesc = string([]byte{
	0x0a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x0b, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63,
	0x1a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xaa, 0x06, 0x0a, 0x0d, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x50,
	0x61, 0x74, 0x68, 0x12, 0x37, 0x0a, 0x18, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x46, 0x69, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x25, 0x0a, 0x0e,
	0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x0d, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x63, 0x0a, 0x13, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65,
	0x5f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x53, 0x65, 0x6e,
	0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x12, 0x73, 0x65, 0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x56,
	0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73, 0x12, 0x2c, 0x0a, 0x12, 0x76, 0x61, 0x72, 0x69,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x46, 0x69,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62,
	0x6c, 0x65, 0x5f, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0c,
	0x52, 0x0f, 0x76, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x50, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x84, 0x01, 0x0a, 0x20, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x76, 0x61, 0x72, 0x5f, 0x6f, 0x76, 0x65,
	0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x4f, 0x76, 0x65, 0x72,
	0x72, 0x69, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x1c, 0x62, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x12, 0x70, 0x0a, 0x18, 0x74, 0x61, 0x73, 0x6b,
	0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x6f, 0x76, 0x65, 0x72, 0x72,
	0x69, 0x64, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x54, 0x61, 0x73, 0x6b, 0x72, 0x75, 0x6e, 0x6e, 0x65,
	0x72, 0x45, 0x6e, 0x76, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x16, 0x74, 0x61, 0x73, 0x6b, 0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72, 0x45, 0x6e,
	0x76, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x1a, 0x45, 0x0a, 0x17, 0x53, 0x65,
	0x6e, 0x73, 0x69, 0x74, 0x69, 0x76, 0x65, 0x56, 0x61, 0x72, 0x69, 0x61, 0x62, 0x6c, 0x65, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x4f, 0x0a, 0x21, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x1a, 0x49, 0x0a, 0x1b, 0x54, 0x61, 0x73, 0x6b, 0x72, 0x75, 0x6e, 0x6e, 0x65, 0x72,
	0x45, 0x6e, 0x76, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x67, 0x0a,
	0x0d, 0x54, 0x46, 0x49, 0x6e, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14,
	0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72,
	0x65, 0x74, 0x72, 0x79, 0x12, 0x40, 0x0a, 0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x72, 0x79, 0x49, 0x6e,
	0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x22, 0xcf, 0x02, 0x0a, 0x0c, 0x41, 0x70, 0x70, 0x6c, 0x79,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x40, 0x0a,
	0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x0d, 0x72, 0x65, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12,
	0x53, 0x0a, 0x18, 0x67, 0x72, 0x61, 0x63, 0x65, 0x66, 0x75, 0x6c, 0x5f, 0x73, 0x68, 0x75, 0x74,
	0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x70, 0x65, 0x72, 0x69, 0x6f, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x16, 0x67, 0x72,
	0x61, 0x63, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x68, 0x75, 0x74, 0x64, 0x6f, 0x77, 0x6e, 0x50, 0x65,
	0x72, 0x69, 0x6f, 0x64, 0x12, 0x53, 0x0a, 0x18, 0x6c, 0x6f, 0x63, 0x6b, 0x5f, 0x65, 0x78, 0x70,
	0x69, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x16, 0x6c, 0x6f, 0x63, 0x6b, 0x45, 0x78, 0x70, 0x69, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x69,
	0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x54, 0x46,
	0x49, 0x6e, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0b, 0x69, 0x6e, 0x69,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0xab, 0x01, 0x0a, 0x0e, 0x50, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x72,
	0x6f, 0x6f, 0x74, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x72, 0x6f, 0x6f, 0x74, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2f, 0x0a, 0x14, 0x74, 0x66, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x5f, 0x66, 0x69, 0x6c, 0x65, 0x5f, 0x70, 0x61, 0x74, 0x68,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x74, 0x66, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x46, 0x69, 0x6c, 0x65, 0x50, 0x61, 0x74, 0x68, 0x12, 0x1f, 0x0a, 0x0b, 0x70, 0x61, 0x63,
	0x6b, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2a, 0x0a, 0x11, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x44, 0x65,
	0x73, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x0d, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x74, 0x72,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x72, 0x65, 0x74, 0x72, 0x79, 0x12, 0x40,
	0x0a, 0x0e, 0x72, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x52, 0x0d, 0x72, 0x65, 0x74, 0x72, 0x79, 0x49, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x12, 0x3d, 0x0a, 0x0c, 0x69, 0x6e, 0x69, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x62, 0x79, 0x6f, 0x63, 0x2e, 0x54, 0x46, 0x49, 0x6e, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x52, 0x0b, 0x69, 0x6e, 0x69, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42,
	0x38, 0x5a, 0x36, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f,
	0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
})

var (
	file_common_byoc_proto_rawDescOnce sync.Once
	file_common_byoc_proto_rawDescData []byte
)

func file_common_byoc_proto_rawDescGZIP() []byte {
	file_common_byoc_proto_rawDescOnce.Do(func() {
		file_common_byoc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_byoc_proto_rawDesc), len(file_common_byoc_proto_rawDesc)))
	})
	return file_common_byoc_proto_rawDescData
}

var file_common_byoc_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_common_byoc_proto_goTypes = []any{
	(*ModuleOptions)(nil),       // 0: common.byoc.ModuleOptions
	(*TFInitOptions)(nil),       // 1: common.byoc.TFInitOptions
	(*ApplyOptions)(nil),        // 2: common.byoc.ApplyOptions
	(*PackageOptions)(nil),      // 3: common.byoc.PackageOptions
	(*OutputOptions)(nil),       // 4: common.byoc.OutputOptions
	nil,                         // 5: common.byoc.ModuleOptions.SensitiveVariablesEntry
	nil,                         // 6: common.byoc.ModuleOptions.BackendConfigEnvVarOverridesEntry
	nil,                         // 7: common.byoc.ModuleOptions.TaskrunnerEnvOverridesEntry
	(*durationpb.Duration)(nil), // 8: google.protobuf.Duration
}
var file_common_byoc_proto_depIdxs = []int32{
	5,  // 0: common.byoc.ModuleOptions.sensitive_variables:type_name -> common.byoc.ModuleOptions.SensitiveVariablesEntry
	6,  // 1: common.byoc.ModuleOptions.backend_config_env_var_overrides:type_name -> common.byoc.ModuleOptions.BackendConfigEnvVarOverridesEntry
	7,  // 2: common.byoc.ModuleOptions.taskrunner_env_overrides:type_name -> common.byoc.ModuleOptions.TaskrunnerEnvOverridesEntry
	8,  // 3: common.byoc.TFInitOptions.retry_interval:type_name -> google.protobuf.Duration
	8,  // 4: common.byoc.ApplyOptions.retry_interval:type_name -> google.protobuf.Duration
	8,  // 5: common.byoc.ApplyOptions.graceful_shutdown_period:type_name -> google.protobuf.Duration
	8,  // 6: common.byoc.ApplyOptions.lock_expiration_duration:type_name -> google.protobuf.Duration
	1,  // 7: common.byoc.ApplyOptions.init_options:type_name -> common.byoc.TFInitOptions
	8,  // 8: common.byoc.OutputOptions.retry_interval:type_name -> google.protobuf.Duration
	1,  // 9: common.byoc.OutputOptions.init_options:type_name -> common.byoc.TFInitOptions
	10, // [10:10] is the sub-list for method output_type
	10, // [10:10] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_common_byoc_proto_init() }
func file_common_byoc_proto_init() {
	if File_common_byoc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_byoc_proto_rawDesc), len(file_common_byoc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_byoc_proto_goTypes,
		DependencyIndexes: file_common_byoc_proto_depIdxs,
		MessageInfos:      file_common_byoc_proto_msgTypes,
	}.Build()
	File_common_byoc_proto = out.File
	file_common_byoc_proto_goTypes = nil
	file_common_byoc_proto_depIdxs = nil
}
