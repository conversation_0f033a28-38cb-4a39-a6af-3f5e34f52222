// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/prometheus.proto

package prometheus

import (
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Endpoint struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Port     string                 `protobuf:"bytes,1,opt,name=port,proto3" json:"port,omitempty"`
	Interval string                 `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// A prometheus parseable string representation of Duration
	// https://github.com/prometheus/common/blob/94bf9828e56d9670579b28a9f78237d3cd8d0395/model/time.go#L204
	ScrapeTimeout     string           `protobuf:"bytes,3,opt,name=scrape_timeout,json=scrapeTimeout,proto3" json:"scrape_timeout,omitempty"`
	MetricRelabelings []*RelabelConfig `protobuf:"bytes,4,rep,name=metric_relabelings,json=metricRelabelings,proto3" json:"metric_relabelings,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *Endpoint) Reset() {
	*x = Endpoint{}
	mi := &file_common_prometheus_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Endpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Endpoint) ProtoMessage() {}

func (x *Endpoint) ProtoReflect() protoreflect.Message {
	mi := &file_common_prometheus_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Endpoint.ProtoReflect.Descriptor instead.
func (*Endpoint) Descriptor() ([]byte, []int) {
	return file_common_prometheus_proto_rawDescGZIP(), []int{0}
}

func (x *Endpoint) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *Endpoint) GetInterval() string {
	if x != nil {
		return x.Interval
	}
	return ""
}

func (x *Endpoint) GetScrapeTimeout() string {
	if x != nil {
		return x.ScrapeTimeout
	}
	return ""
}

func (x *Endpoint) GetMetricRelabelings() []*RelabelConfig {
	if x != nil {
		return x.MetricRelabelings
	}
	return nil
}

type ServiceMonitorSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	JobLabel      string                 `protobuf:"bytes,1,opt,name=job_label,json=jobLabel,proto3" json:"job_label,omitempty"`
	TargetLabels  []string               `protobuf:"bytes,2,rep,name=target_labels,json=targetLabels,proto3" json:"target_labels,omitempty"`
	Endpoints     []*Endpoint            `protobuf:"bytes,3,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	Selector      *k8s.LabelSelector     `protobuf:"bytes,4,opt,name=selector,proto3" json:"selector,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceMonitorSpec) Reset() {
	*x = ServiceMonitorSpec{}
	mi := &file_common_prometheus_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceMonitorSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceMonitorSpec) ProtoMessage() {}

func (x *ServiceMonitorSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_prometheus_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceMonitorSpec.ProtoReflect.Descriptor instead.
func (*ServiceMonitorSpec) Descriptor() ([]byte, []int) {
	return file_common_prometheus_proto_rawDescGZIP(), []int{1}
}

func (x *ServiceMonitorSpec) GetJobLabel() string {
	if x != nil {
		return x.JobLabel
	}
	return ""
}

func (x *ServiceMonitorSpec) GetTargetLabels() []string {
	if x != nil {
		return x.TargetLabels
	}
	return nil
}

func (x *ServiceMonitorSpec) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *ServiceMonitorSpec) GetSelector() *k8s.LabelSelector {
	if x != nil {
		return x.Selector
	}
	return nil
}

type RelabelConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SourceLabels  []string               `protobuf:"bytes,1,rep,name=source_labels,json=sourceLabels,proto3" json:"source_labels,omitempty"`
	Regex         string                 `protobuf:"bytes,2,opt,name=regex,proto3" json:"regex,omitempty"`
	Action        string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelabelConfig) Reset() {
	*x = RelabelConfig{}
	mi := &file_common_prometheus_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelabelConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelabelConfig) ProtoMessage() {}

func (x *RelabelConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_prometheus_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelabelConfig.ProtoReflect.Descriptor instead.
func (*RelabelConfig) Descriptor() ([]byte, []int) {
	return file_common_prometheus_proto_rawDescGZIP(), []int{2}
}

func (x *RelabelConfig) GetSourceLabels() []string {
	if x != nil {
		return x.SourceLabels
	}
	return nil
}

func (x *RelabelConfig) GetRegex() string {
	if x != nil {
		return x.Regex
	}
	return ""
}

func (x *RelabelConfig) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

var File_common_prometheus_proto protoreflect.FileDescriptor

var file_common_prometheus_proto_rawDesc = string([]byte{
	0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68,
	0x65, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x1a, 0x10, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2,
	0x01, 0x0a, 0x08, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x25, 0x0a, 0x0e, 0x73,
	0x63, 0x72, 0x61, 0x70, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x63, 0x72, 0x61, 0x70, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x6f,
	0x75, 0x74, 0x12, 0x4f, 0x0a, 0x12, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65,
	0x75, 0x73, 0x2e, 0x52, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x52, 0x11, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x69,
	0x6e, 0x67, 0x73, 0x22, 0xc8, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1b, 0x0a, 0x09, 0x6a, 0x6f,
	0x62, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6a,
	0x6f, 0x62, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x12, 0x23, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x39, 0x0a, 0x09,
	0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68,
	0x65, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x09, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22, 0x62,
	0x0a, 0x0d, 0x52, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69,
	0x6f, 0x6e, 0x42, 0x3e, 0x5a, 0x3c, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65,
	0x75, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_prometheus_proto_rawDescOnce sync.Once
	file_common_prometheus_proto_rawDescData []byte
)

func file_common_prometheus_proto_rawDescGZIP() []byte {
	file_common_prometheus_proto_rawDescOnce.Do(func() {
		file_common_prometheus_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_prometheus_proto_rawDesc), len(file_common_prometheus_proto_rawDesc)))
	})
	return file_common_prometheus_proto_rawDescData
}

var file_common_prometheus_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_common_prometheus_proto_goTypes = []any{
	(*Endpoint)(nil),           // 0: common.prometheus.Endpoint
	(*ServiceMonitorSpec)(nil), // 1: common.prometheus.ServiceMonitorSpec
	(*RelabelConfig)(nil),      // 2: common.prometheus.RelabelConfig
	(*k8s.LabelSelector)(nil),  // 3: common.k8s.LabelSelector
}
var file_common_prometheus_proto_depIdxs = []int32{
	2, // 0: common.prometheus.Endpoint.metric_relabelings:type_name -> common.prometheus.RelabelConfig
	0, // 1: common.prometheus.ServiceMonitorSpec.endpoints:type_name -> common.prometheus.Endpoint
	3, // 2: common.prometheus.ServiceMonitorSpec.selector:type_name -> common.k8s.LabelSelector
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_common_prometheus_proto_init() }
func file_common_prometheus_proto_init() {
	if File_common_prometheus_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_prometheus_proto_rawDesc), len(file_common_prometheus_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_prometheus_proto_goTypes,
		DependencyIndexes: file_common_prometheus_proto_depIdxs,
		MessageInfos:      file_common_prometheus_proto_msgTypes,
	}.Build()
	File_common_prometheus_proto = out.File
	file_common_prometheus_proto_goTypes = nil
	file_common_prometheus_proto_depIdxs = nil
}
