// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/gmp.proto

package gmp

import (
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// API definition: https://github.com/GoogleCloudPlatform/prometheus-engine/blob/main/doc/api.md#podmonitoringspec
type PodMonitoringSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Endpoints     []*Endpoint            `protobuf:"bytes,1,rep,name=endpoints,proto3" json:"endpoints,omitempty"`
	Selector      *k8s.LabelSelector     `protobuf:"bytes,2,opt,name=selector,proto3" json:"selector,omitempty"`
	TargetLabels  *TargetLabels          `protobuf:"bytes,3,opt,name=target_labels,json=targetLabels,proto3" json:"target_labels,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PodMonitoringSpec) Reset() {
	*x = PodMonitoringSpec{}
	mi := &file_common_gmp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PodMonitoringSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodMonitoringSpec) ProtoMessage() {}

func (x *PodMonitoringSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_gmp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodMonitoringSpec.ProtoReflect.Descriptor instead.
func (*PodMonitoringSpec) Descriptor() ([]byte, []int) {
	return file_common_gmp_proto_rawDescGZIP(), []int{0}
}

func (x *PodMonitoringSpec) GetEndpoints() []*Endpoint {
	if x != nil {
		return x.Endpoints
	}
	return nil
}

func (x *PodMonitoringSpec) GetSelector() *k8s.LabelSelector {
	if x != nil {
		return x.Selector
	}
	return nil
}

func (x *PodMonitoringSpec) GetTargetLabels() *TargetLabels {
	if x != nil {
		return x.TargetLabels
	}
	return nil
}

type Endpoint struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Port     string                 `protobuf:"bytes,1,opt,name=port,proto3" json:"port,omitempty"`
	Interval string                 `protobuf:"bytes,2,opt,name=interval,proto3" json:"interval,omitempty"`
	// A prometheus parseable string representation of Duration
	// https://github.com/prometheus/common/blob/94bf9828e56d9670579b28a9f78237d3cd8d0395/model/time.go#L204
	Timeout          string            `protobuf:"bytes,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	MetricRelabeling []*RelabelingRule `protobuf:"bytes,4,rep,name=metric_relabeling,json=metricRelabeling,proto3" json:"metric_relabeling,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *Endpoint) Reset() {
	*x = Endpoint{}
	mi := &file_common_gmp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Endpoint) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Endpoint) ProtoMessage() {}

func (x *Endpoint) ProtoReflect() protoreflect.Message {
	mi := &file_common_gmp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Endpoint.ProtoReflect.Descriptor instead.
func (*Endpoint) Descriptor() ([]byte, []int) {
	return file_common_gmp_proto_rawDescGZIP(), []int{1}
}

func (x *Endpoint) GetPort() string {
	if x != nil {
		return x.Port
	}
	return ""
}

func (x *Endpoint) GetInterval() string {
	if x != nil {
		return x.Interval
	}
	return ""
}

func (x *Endpoint) GetTimeout() string {
	if x != nil {
		return x.Timeout
	}
	return ""
}

func (x *Endpoint) GetMetricRelabeling() []*RelabelingRule {
	if x != nil {
		return x.MetricRelabeling
	}
	return nil
}

type RelabelingRule struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SourceLabels  []string               `protobuf:"bytes,1,rep,name=source_labels,json=sourceLabels,proto3" json:"source_labels,omitempty"`
	Regex         string                 `protobuf:"bytes,2,opt,name=regex,proto3" json:"regex,omitempty"`
	Action        string                 `protobuf:"bytes,3,opt,name=action,proto3" json:"action,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RelabelingRule) Reset() {
	*x = RelabelingRule{}
	mi := &file_common_gmp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RelabelingRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RelabelingRule) ProtoMessage() {}

func (x *RelabelingRule) ProtoReflect() protoreflect.Message {
	mi := &file_common_gmp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RelabelingRule.ProtoReflect.Descriptor instead.
func (*RelabelingRule) Descriptor() ([]byte, []int) {
	return file_common_gmp_proto_rawDescGZIP(), []int{2}
}

func (x *RelabelingRule) GetSourceLabels() []string {
	if x != nil {
		return x.SourceLabels
	}
	return nil
}

func (x *RelabelingRule) GetRegex() string {
	if x != nil {
		return x.Regex
	}
	return ""
}

func (x *RelabelingRule) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

type TargetLabels struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	FromPod       []*LabelMapping        `protobuf:"bytes,1,rep,name=fromPod,proto3" json:"fromPod,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TargetLabels) Reset() {
	*x = TargetLabels{}
	mi := &file_common_gmp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TargetLabels) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetLabels) ProtoMessage() {}

func (x *TargetLabels) ProtoReflect() protoreflect.Message {
	mi := &file_common_gmp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetLabels.ProtoReflect.Descriptor instead.
func (*TargetLabels) Descriptor() ([]byte, []int) {
	return file_common_gmp_proto_rawDescGZIP(), []int{3}
}

func (x *TargetLabels) GetFromPod() []*LabelMapping {
	if x != nil {
		return x.FromPod
	}
	return nil
}

type LabelMapping struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	From          string                 `protobuf:"bytes,1,opt,name=from,proto3" json:"from,omitempty"`
	To            string                 `protobuf:"bytes,2,opt,name=to,proto3" json:"to,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelMapping) Reset() {
	*x = LabelMapping{}
	mi := &file_common_gmp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelMapping) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelMapping) ProtoMessage() {}

func (x *LabelMapping) ProtoReflect() protoreflect.Message {
	mi := &file_common_gmp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelMapping.ProtoReflect.Descriptor instead.
func (*LabelMapping) Descriptor() ([]byte, []int) {
	return file_common_gmp_proto_rawDescGZIP(), []int{4}
}

func (x *LabelMapping) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *LabelMapping) GetTo() string {
	if x != nil {
		return x.To
	}
	return ""
}

var File_common_gmp_proto protoreflect.FileDescriptor

var file_common_gmp_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x6d, 0x70, 0x1a, 0x10,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xbd, 0x01, 0x0a, 0x11, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69,
	0x6e, 0x67, 0x53, 0x70, 0x65, 0x63, 0x12, 0x32, 0x0a, 0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x67, 0x6d, 0x70, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x09, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x3d, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x67, 0x6d, 0x70, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65,
	0x6c, 0x73, 0x52, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x22, 0x9d, 0x01, 0x0a, 0x08, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x12, 0x18, 0x0a,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x47, 0x0a, 0x11, 0x6d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x5f, 0x72, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x69, 0x6e, 0x67, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x6d, 0x70, 0x2e,
	0x52, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x10,
	0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x52, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x69, 0x6e, 0x67,
	0x22, 0x63, 0x0a, 0x0e, 0x52, 0x65, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x69, 0x6e, 0x67, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x14, 0x0a, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x72, 0x65, 0x67, 0x65, 0x78, 0x12, 0x16, 0x0a,
	0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61,
	0x63, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x42, 0x0a, 0x0c, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x32, 0x0a, 0x07, 0x66, 0x72, 0x6f, 0x6d, 0x50, 0x6f, 0x64,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x67, 0x6d, 0x70, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67,
	0x52, 0x07, 0x66, 0x72, 0x6f, 0x6d, 0x50, 0x6f, 0x64, 0x22, 0x32, 0x0a, 0x0c, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x4d, 0x61, 0x70, 0x70, 0x69, 0x6e, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x66, 0x72, 0x6f,
	0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x12, 0x0e, 0x0a,
	0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x74, 0x6f, 0x42, 0x37, 0x5a,
	0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2f, 0x67, 0x6d, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_gmp_proto_rawDescOnce sync.Once
	file_common_gmp_proto_rawDescData []byte
)

func file_common_gmp_proto_rawDescGZIP() []byte {
	file_common_gmp_proto_rawDescOnce.Do(func() {
		file_common_gmp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_gmp_proto_rawDesc), len(file_common_gmp_proto_rawDesc)))
	})
	return file_common_gmp_proto_rawDescData
}

var file_common_gmp_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_common_gmp_proto_goTypes = []any{
	(*PodMonitoringSpec)(nil), // 0: common.gmp.PodMonitoringSpec
	(*Endpoint)(nil),          // 1: common.gmp.Endpoint
	(*RelabelingRule)(nil),    // 2: common.gmp.RelabelingRule
	(*TargetLabels)(nil),      // 3: common.gmp.TargetLabels
	(*LabelMapping)(nil),      // 4: common.gmp.LabelMapping
	(*k8s.LabelSelector)(nil), // 5: common.k8s.LabelSelector
}
var file_common_gmp_proto_depIdxs = []int32{
	1, // 0: common.gmp.PodMonitoringSpec.endpoints:type_name -> common.gmp.Endpoint
	5, // 1: common.gmp.PodMonitoringSpec.selector:type_name -> common.k8s.LabelSelector
	3, // 2: common.gmp.PodMonitoringSpec.target_labels:type_name -> common.gmp.TargetLabels
	2, // 3: common.gmp.Endpoint.metric_relabeling:type_name -> common.gmp.RelabelingRule
	4, // 4: common.gmp.TargetLabels.fromPod:type_name -> common.gmp.LabelMapping
	5, // [5:5] is the sub-list for method output_type
	5, // [5:5] is the sub-list for method input_type
	5, // [5:5] is the sub-list for extension type_name
	5, // [5:5] is the sub-list for extension extendee
	0, // [0:5] is the sub-list for field type_name
}

func init() { file_common_gmp_proto_init() }
func file_common_gmp_proto_init() {
	if File_common_gmp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_gmp_proto_rawDesc), len(file_common_gmp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_gmp_proto_goTypes,
		DependencyIndexes: file_common_gmp_proto_depIdxs,
		MessageInfos:      file_common_gmp_proto_msgTypes,
	}.Build()
	File_common_gmp_proto = out.File
	file_common_gmp_proto_goTypes = nil
	file_common_gmp_proto_depIdxs = nil
}
