// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/gcp.proto

package gcp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CloudSQLSSLMode int32

const (
	CloudSQLSSLMode_CloudSQLSSLMode_UNKNOWN                             CloudSQLSSLMode = 0
	CloudSQLSSLMode_CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED     CloudSQLSSLMode = 1
	CloudSQLSSLMode_CloudSQLSSLMode_ENCRYPTED_ONLY                      CloudSQLSSLMode = 2
	CloudSQLSSLMode_CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED CloudSQLSSLMode = 3
)

// Enum value maps for CloudSQLSSLMode.
var (
	CloudSQLSSLMode_name = map[int32]string{
		0: "CloudSQLSSLMode_UNKNOWN",
		1: "CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED",
		2: "CloudSQLSSLMode_ENCRYPTED_ONLY",
		3: "CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED",
	}
	CloudSQLSSLMode_value = map[string]int32{
		"CloudSQLSSLMode_UNKNOWN":                             0,
		"CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED":     1,
		"CloudSQLSSLMode_ENCRYPTED_ONLY":                      2,
		"CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED": 3,
	}
)

func (x CloudSQLSSLMode) Enum() *CloudSQLSSLMode {
	p := new(CloudSQLSSLMode)
	*p = x
	return p
}

func (x CloudSQLSSLMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (CloudSQLSSLMode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_gcp_proto_enumTypes[0].Descriptor()
}

func (CloudSQLSSLMode) Type() protoreflect.EnumType {
	return &file_common_gcp_proto_enumTypes[0]
}

func (x CloudSQLSSLMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use CloudSQLSSLMode.Descriptor instead.
func (CloudSQLSSLMode) EnumDescriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{0}
}

type SQLInstanceStatus int32

const (
	SQLInstanceStatus_UNKNOWN    SQLInstanceStatus = 0
	SQLInstanceStatus_UPDATING   SQLInstanceStatus = 1
	SQLInstanceStatus_UP_TO_DATE SQLInstanceStatus = 2
	SQLInstanceStatus_STOPPED    SQLInstanceStatus = 3
)

// Enum value maps for SQLInstanceStatus.
var (
	SQLInstanceStatus_name = map[int32]string{
		0: "UNKNOWN",
		1: "UPDATING",
		2: "UP_TO_DATE",
		3: "STOPPED",
	}
	SQLInstanceStatus_value = map[string]int32{
		"UNKNOWN":    0,
		"UPDATING":   1,
		"UP_TO_DATE": 2,
		"STOPPED":    3,
	}
)

func (x SQLInstanceStatus) Enum() *SQLInstanceStatus {
	p := new(SQLInstanceStatus)
	*p = x
	return p
}

func (x SQLInstanceStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SQLInstanceStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_gcp_proto_enumTypes[1].Descriptor()
}

func (SQLInstanceStatus) Type() protoreflect.EnumType {
	return &file_common_gcp_proto_enumTypes[1]
}

func (x SQLInstanceStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SQLInstanceStatus.Descriptor instead.
func (SQLInstanceStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{1}
}

type SQLInstanceSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Immutable. Optional. The name of the resource. Used for creation and
	// acquisition. When unset, the value of `metadata.name` is used as the
	// default.
	ResourceId string `protobuf:"bytes,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	// The type of the instance. The valid values are:-
	// 'SQL_INSTANCE_TYPE_UNSPECIFIED', 'CLOUD_SQL_INSTANCE',
	// 'ON_PREMISES_INSTANCE' and 'READ_REPLICA_INSTANCE'.
	InstanceType string `protobuf:"bytes,2,opt,name=instance_type,json=instanceType,proto3" json:"instance_type,omitempty"`
	// The MySQL, PostgreSQL or SQL Server (beta) version to use. Supported values
	// include MYSQL_5_6, MYSQL_5_7, MYSQL_8_0, POSTGRES_9_6, POSTGRES_10,
	// POSTGRES_11, POSTGRES_12, POSTGRES_13, POSTGRES_14,
	// SQLSERVER_2017_STANDARD, SQLSERVER_2017_ENTERPRISE, SQLSERVER_2017_EXPRESS,
	// SQLSERVER_2017_WEB. Database Version Policies includes an up-to-date
	// reference of supported versions.
	DatabaseVersion string `protobuf:"bytes,3,opt,name=database_version,json=databaseVersion,proto3" json:"database_version,omitempty"`
	// Immutable. The region the instance will sit in. Note, Cloud SQL is not
	// available in all regions. A valid region must be provided to use this
	// resource. If a region is not provided in the resource definition, the
	// provider region will be used instead, but this will be an apply-time error
	// for instances if the provider region is not supported with Cloud SQL. If
	// you choose not to provide the region argument for this resource, make sure
	// you understand this.
	Region string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	// Initial root password. Required for MS SQL Server.
	RootPassword *RootPassword `protobuf:"bytes,5,opt,name=root_password,json=rootPassword,proto3" json:"root_password,omitempty"`
	// The settings to use for the database. The configuration is detailed below.
	Settings      *SQLInstanceSettings `protobuf:"bytes,6,opt,name=settings,proto3" json:"settings,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SQLInstanceSpec) Reset() {
	*x = SQLInstanceSpec{}
	mi := &file_common_gcp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SQLInstanceSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SQLInstanceSpec) ProtoMessage() {}

func (x *SQLInstanceSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SQLInstanceSpec.ProtoReflect.Descriptor instead.
func (*SQLInstanceSpec) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{0}
}

func (x *SQLInstanceSpec) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *SQLInstanceSpec) GetInstanceType() string {
	if x != nil {
		return x.InstanceType
	}
	return ""
}

func (x *SQLInstanceSpec) GetDatabaseVersion() string {
	if x != nil {
		return x.DatabaseVersion
	}
	return ""
}

func (x *SQLInstanceSpec) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SQLInstanceSpec) GetRootPassword() *RootPassword {
	if x != nil {
		return x.RootPassword
	}
	return nil
}

func (x *SQLInstanceSpec) GetSettings() *SQLInstanceSettings {
	if x != nil {
		return x.Settings
	}
	return nil
}

type SQLInstanceSettings struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// -required-
	// The machine type to use. See tiers for more details and supported versions.
	// Postgres supports only shared-core machine types, and custom machine types
	// such as db-custom-2-13312. See the Custom Machine Type Documentation to
	// learn about specifying custom machine types.
	Tier string `protobuf:"bytes,1,opt,name=tier,proto3" json:"tier,omitempty"`
	// The size of data disk, in GB. Size of a running instance cannot be reduced
	// but can be increased. The minimum value is 10GB.
	DiskSize        int32            `protobuf:"varint,2,opt,name=disk_size,json=diskSize,proto3" json:"disk_size,omitempty"`
	IpConfiguration *IPConfiguration `protobuf:"bytes,3,opt,name=ip_configuration,json=ipConfiguration,proto3" json:"ip_configuration,omitempty"`
	// Configuration to protect against accidental instance deletion.
	DeletionProtectionEnabled bool            `protobuf:"varint,4,opt,name=deletion_protection_enabled,json=deletionProtectionEnabled,proto3" json:"deletion_protection_enabled,omitempty"`
	DatabaseFlags             []*DatabaseFlag `protobuf:"bytes,5,rep,name=database_flags,json=databaseFlags,proto3" json:"database_flags,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *SQLInstanceSettings) Reset() {
	*x = SQLInstanceSettings{}
	mi := &file_common_gcp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SQLInstanceSettings) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SQLInstanceSettings) ProtoMessage() {}

func (x *SQLInstanceSettings) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SQLInstanceSettings.ProtoReflect.Descriptor instead.
func (*SQLInstanceSettings) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{1}
}

func (x *SQLInstanceSettings) GetTier() string {
	if x != nil {
		return x.Tier
	}
	return ""
}

func (x *SQLInstanceSettings) GetDiskSize() int32 {
	if x != nil {
		return x.DiskSize
	}
	return 0
}

func (x *SQLInstanceSettings) GetIpConfiguration() *IPConfiguration {
	if x != nil {
		return x.IpConfiguration
	}
	return nil
}

func (x *SQLInstanceSettings) GetDeletionProtectionEnabled() bool {
	if x != nil {
		return x.DeletionProtectionEnabled
	}
	return false
}

func (x *SQLInstanceSettings) GetDatabaseFlags() []*DatabaseFlag {
	if x != nil {
		return x.DatabaseFlags
	}
	return nil
}

type DatabaseFlag struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Value         string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DatabaseFlag) Reset() {
	*x = DatabaseFlag{}
	mi := &file_common_gcp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DatabaseFlag) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DatabaseFlag) ProtoMessage() {}

func (x *DatabaseFlag) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DatabaseFlag.ProtoReflect.Descriptor instead.
func (*DatabaseFlag) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{2}
}

func (x *DatabaseFlag) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DatabaseFlag) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

type RootPassword struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Value         *string                `protobuf:"bytes,1,opt,name=value,proto3,oneof" json:"value,omitempty"`
	ValueFrom     *RootPasswordValueFrom `protobuf:"bytes,2,opt,name=value_from,json=valueFrom,proto3,oneof" json:"value_from,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RootPassword) Reset() {
	*x = RootPassword{}
	mi := &file_common_gcp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RootPassword) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RootPassword) ProtoMessage() {}

func (x *RootPassword) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RootPassword.ProtoReflect.Descriptor instead.
func (*RootPassword) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{3}
}

func (x *RootPassword) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *RootPassword) GetValueFrom() *RootPasswordValueFrom {
	if x != nil {
		return x.ValueFrom
	}
	return nil
}

type RootPasswordValueFrom struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Reference to a value with the given key in the given Secret in the
	// resource's namespace.
	SecretKeyRef  *SecretKeyRef `protobuf:"bytes,1,opt,name=secretKeyRef,proto3" json:"secretKeyRef,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RootPasswordValueFrom) Reset() {
	*x = RootPasswordValueFrom{}
	mi := &file_common_gcp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RootPasswordValueFrom) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RootPasswordValueFrom) ProtoMessage() {}

func (x *RootPasswordValueFrom) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RootPasswordValueFrom.ProtoReflect.Descriptor instead.
func (*RootPasswordValueFrom) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{4}
}

func (x *RootPasswordValueFrom) GetSecretKeyRef() *SecretKeyRef {
	if x != nil {
		return x.SecretKeyRef
	}
	return nil
}

type SecretKeyRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Key that identifies the value to be extracted.
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Name of the Secret to extract a value from.
	Name          string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretKeyRef) Reset() {
	*x = SecretKeyRef{}
	mi := &file_common_gcp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretKeyRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretKeyRef) ProtoMessage() {}

func (x *SecretKeyRef) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretKeyRef.ProtoReflect.Descriptor instead.
func (*SecretKeyRef) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{5}
}

func (x *SecretKeyRef) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *SecretKeyRef) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type IPConfiguration struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	Ipv4Enabled       bool                   `protobuf:"varint,1,opt,name=ipv4_enabled,json=ipv4Enabled,proto3" json:"ipv4_enabled,omitempty"`
	PrivateNetworkRef *PrivateNetworkRef     `protobuf:"bytes,2,opt,name=private_network_ref,json=privateNetworkRef,proto3" json:"private_network_ref,omitempty"`
	AllocatedIpRange  string                 `protobuf:"bytes,3,opt,name=allocated_ip_range,json=allocatedIpRange,proto3" json:"allocated_ip_range,omitempty"`
	SslMode           CloudSQLSSLMode        `protobuf:"varint,4,opt,name=ssl_mode,json=sslMode,proto3,enum=common.gcp.CloudSQLSSLMode" json:"ssl_mode,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *IPConfiguration) Reset() {
	*x = IPConfiguration{}
	mi := &file_common_gcp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPConfiguration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPConfiguration) ProtoMessage() {}

func (x *IPConfiguration) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPConfiguration.ProtoReflect.Descriptor instead.
func (*IPConfiguration) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{6}
}

func (x *IPConfiguration) GetIpv4Enabled() bool {
	if x != nil {
		return x.Ipv4Enabled
	}
	return false
}

func (x *IPConfiguration) GetPrivateNetworkRef() *PrivateNetworkRef {
	if x != nil {
		return x.PrivateNetworkRef
	}
	return nil
}

func (x *IPConfiguration) GetAllocatedIpRange() string {
	if x != nil {
		return x.AllocatedIpRange
	}
	return ""
}

func (x *IPConfiguration) GetSslMode() CloudSQLSSLMode {
	if x != nil {
		return x.SslMode
	}
	return CloudSQLSSLMode_CloudSQLSSLMode_UNKNOWN
}

type PrivateNetworkRef struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Allowed value: The `selfLink` field of a `ComputeNetwork` resource.
	External      string `protobuf:"bytes,1,opt,name=external,proto3" json:"external,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PrivateNetworkRef) Reset() {
	*x = PrivateNetworkRef{}
	mi := &file_common_gcp_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PrivateNetworkRef) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PrivateNetworkRef) ProtoMessage() {}

func (x *PrivateNetworkRef) ProtoReflect() protoreflect.Message {
	mi := &file_common_gcp_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PrivateNetworkRef.ProtoReflect.Descriptor instead.
func (*PrivateNetworkRef) Descriptor() ([]byte, []int) {
	return file_common_gcp_proto_rawDescGZIP(), []int{7}
}

func (x *PrivateNetworkRef) GetExternal() string {
	if x != nil {
		return x.External
	}
	return ""
}

var File_common_gcp_proto protoreflect.FileDescriptor

var file_common_gcp_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x63, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x22, 0x96,
	0x02, 0x0a, 0x0f, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x49, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x56, 0x65, 0x72, 0x73,
	0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x3d, 0x0a, 0x0d, 0x72,
	0x6f, 0x6f, 0x74, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x52, 0x6f, 0x6f, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x52, 0x0c, 0x72, 0x6f,
	0x6f, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x12, 0x3b, 0x0a, 0x08, 0x73, 0x65,
	0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x52, 0x08, 0x73,
	0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x22, 0x8f, 0x02, 0x0a, 0x13, 0x53, 0x51, 0x4c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x65, 0x74, 0x74, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x69, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x69, 0x65, 0x72, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x64, 0x69, 0x73, 0x6b, 0x53, 0x69, 0x7a, 0x65,
	0x12, 0x46, 0x0a, 0x10, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x49, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x69, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x3e, 0x0a, 0x1b, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x52, 0x19, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x65, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x3f, 0x0a, 0x0e, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x61,
	0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61,
	0x62, 0x61, 0x73, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x73, 0x22, 0x38, 0x0a, 0x0c, 0x44, 0x61, 0x74,
	0x61, 0x62, 0x61, 0x73, 0x65, 0x46, 0x6c, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x22, 0x89, 0x01, 0x0a, 0x0c, 0x52, 0x6f, 0x6f, 0x74, 0x50, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x12, 0x19, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x45, 0x0a, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70,
	0x2e, 0x52, 0x6f, 0x6f, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x48, 0x01, 0x52, 0x09, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x46,
	0x72, 0x6f, 0x6d, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x5f, 0x66, 0x72, 0x6f, 0x6d, 0x22,
	0x55, 0x0a, 0x15, 0x52, 0x6f, 0x6f, 0x74, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x46, 0x72, 0x6f, 0x6d, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x52, 0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x22, 0x34, 0x0a, 0x0c, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xe9, 0x01, 0x0a,
	0x0f, 0x49, 0x50, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x70, 0x76, 0x34, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x64, 0x12, 0x4d, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x66, 0x52,
	0x11, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52,
	0x65, 0x66, 0x12, 0x2c, 0x0a, 0x12, 0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f,
	0x69, 0x70, 0x5f, 0x72, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x61, 0x6c, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x65, 0x64, 0x49, 0x70, 0x52, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x36, 0x0a, 0x08, 0x73, 0x73, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x51, 0x4c, 0x53, 0x53, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x52,
	0x07, 0x73, 0x73, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x2f, 0x0a, 0x11, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x52, 0x65, 0x66, 0x12, 0x1a, 0x0a,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x2a, 0xc0, 0x01, 0x0a, 0x0f, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x53, 0x51, 0x4c, 0x53, 0x53, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1b, 0x0a,
	0x17, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x51, 0x4c, 0x53, 0x53, 0x4c, 0x4d, 0x6f, 0x64, 0x65,
	0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x33, 0x0a, 0x2f, 0x43, 0x6c,
	0x6f, 0x75, 0x64, 0x53, 0x51, 0x4c, 0x53, 0x53, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x5f, 0x41, 0x4c,
	0x4c, 0x4f, 0x57, 0x5f, 0x55, 0x4e, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x45, 0x44, 0x5f,
	0x41, 0x4e, 0x44, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12,
	0x22, 0x0a, 0x1e, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x51, 0x4c, 0x53, 0x53, 0x4c, 0x4d, 0x6f,
	0x64, 0x65, 0x5f, 0x45, 0x4e, 0x43, 0x52, 0x59, 0x50, 0x54, 0x45, 0x44, 0x5f, 0x4f, 0x4e, 0x4c,
	0x59, 0x10, 0x02, 0x12, 0x37, 0x0a, 0x33, 0x43, 0x6c, 0x6f, 0x75, 0x64, 0x53, 0x51, 0x4c, 0x53,
	0x53, 0x4c, 0x4d, 0x6f, 0x64, 0x65, 0x5f, 0x54, 0x52, 0x55, 0x53, 0x54, 0x45, 0x44, 0x5f, 0x43,
	0x4c, 0x49, 0x45, 0x4e, 0x54, 0x5f, 0x43, 0x45, 0x52, 0x54, 0x49, 0x46, 0x49, 0x43, 0x41, 0x54,
	0x45, 0x5f, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45, 0x44, 0x10, 0x03, 0x2a, 0x4b, 0x0a, 0x11,
	0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0c,
	0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0e, 0x0a, 0x0a,
	0x55, 0x50, 0x5f, 0x54, 0x4f, 0x5f, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x03, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61,
	0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67,
	0x63, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_gcp_proto_rawDescOnce sync.Once
	file_common_gcp_proto_rawDescData []byte
)

func file_common_gcp_proto_rawDescGZIP() []byte {
	file_common_gcp_proto_rawDescOnce.Do(func() {
		file_common_gcp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_gcp_proto_rawDesc), len(file_common_gcp_proto_rawDesc)))
	})
	return file_common_gcp_proto_rawDescData
}

var file_common_gcp_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_common_gcp_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_common_gcp_proto_goTypes = []any{
	(CloudSQLSSLMode)(0),          // 0: common.gcp.CloudSQLSSLMode
	(SQLInstanceStatus)(0),        // 1: common.gcp.SQLInstanceStatus
	(*SQLInstanceSpec)(nil),       // 2: common.gcp.SQLInstanceSpec
	(*SQLInstanceSettings)(nil),   // 3: common.gcp.SQLInstanceSettings
	(*DatabaseFlag)(nil),          // 4: common.gcp.DatabaseFlag
	(*RootPassword)(nil),          // 5: common.gcp.RootPassword
	(*RootPasswordValueFrom)(nil), // 6: common.gcp.RootPasswordValueFrom
	(*SecretKeyRef)(nil),          // 7: common.gcp.SecretKeyRef
	(*IPConfiguration)(nil),       // 8: common.gcp.IPConfiguration
	(*PrivateNetworkRef)(nil),     // 9: common.gcp.PrivateNetworkRef
}
var file_common_gcp_proto_depIdxs = []int32{
	5, // 0: common.gcp.SQLInstanceSpec.root_password:type_name -> common.gcp.RootPassword
	3, // 1: common.gcp.SQLInstanceSpec.settings:type_name -> common.gcp.SQLInstanceSettings
	8, // 2: common.gcp.SQLInstanceSettings.ip_configuration:type_name -> common.gcp.IPConfiguration
	4, // 3: common.gcp.SQLInstanceSettings.database_flags:type_name -> common.gcp.DatabaseFlag
	6, // 4: common.gcp.RootPassword.value_from:type_name -> common.gcp.RootPasswordValueFrom
	7, // 5: common.gcp.RootPasswordValueFrom.secretKeyRef:type_name -> common.gcp.SecretKeyRef
	9, // 6: common.gcp.IPConfiguration.private_network_ref:type_name -> common.gcp.PrivateNetworkRef
	0, // 7: common.gcp.IPConfiguration.ssl_mode:type_name -> common.gcp.CloudSQLSSLMode
	8, // [8:8] is the sub-list for method output_type
	8, // [8:8] is the sub-list for method input_type
	8, // [8:8] is the sub-list for extension type_name
	8, // [8:8] is the sub-list for extension extendee
	0, // [0:8] is the sub-list for field type_name
}

func init() { file_common_gcp_proto_init() }
func file_common_gcp_proto_init() {
	if File_common_gcp_proto != nil {
		return
	}
	file_common_gcp_proto_msgTypes[3].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_gcp_proto_rawDesc), len(file_common_gcp_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_gcp_proto_goTypes,
		DependencyIndexes: file_common_gcp_proto_depIdxs,
		EnumInfos:         file_common_gcp_proto_enumTypes,
		MessageInfos:      file_common_gcp_proto_msgTypes,
	}.Build()
	File_common_gcp_proto = out.File
	file_common_gcp_proto_goTypes = nil
	file_common_gcp_proto_depIdxs = nil
}
