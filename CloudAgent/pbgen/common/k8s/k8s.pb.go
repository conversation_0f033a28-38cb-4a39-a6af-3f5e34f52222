// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/k8s.proto

package k8s

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type HelmReleaseStatus int32

const (
	HelmReleaseStatus_UNKNOWN      HelmReleaseStatus = 0
	HelmReleaseStatus_DEPLOYED     HelmReleaseStatus = 1
	HelmReleaseStatus_UNINSTALLING HelmReleaseStatus = 2
	HelmReleaseStatus_INSTALLING   HelmReleaseStatus = 3
	HelmReleaseStatus_UPGRADING    HelmReleaseStatus = 4
	HelmReleaseStatus_FAILED       HelmReleaseStatus = 5
	HelmReleaseStatus_UNINSTALLED  HelmReleaseStatus = 6
)

// Enum value maps for HelmReleaseStatus.
var (
	HelmReleaseStatus_name = map[int32]string{
		0: "UNKNOWN",
		1: "DEPLOYED",
		2: "UNINSTALLING",
		3: "INSTALLING",
		4: "UPGRADING",
		5: "FAILED",
		6: "UNINSTALLED",
	}
	HelmReleaseStatus_value = map[string]int32{
		"UNKNOWN":      0,
		"DEPLOYED":     1,
		"UNINSTALLING": 2,
		"INSTALLING":   3,
		"UPGRADING":    4,
		"FAILED":       5,
		"UNINSTALLED":  6,
	}
)

func (x HelmReleaseStatus) Enum() *HelmReleaseStatus {
	p := new(HelmReleaseStatus)
	*p = x
	return p
}

func (x HelmReleaseStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HelmReleaseStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[0].Descriptor()
}

func (HelmReleaseStatus) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[0]
}

func (x HelmReleaseStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HelmReleaseStatus.Descriptor instead.
func (HelmReleaseStatus) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{0}
}

type ServiceType int32

const (
	ServiceType_SERVICE_TYPE_UNKNOWN       ServiceType = 0
	ServiceType_SERVICE_TYPE_CLUSTER_IP    ServiceType = 1
	ServiceType_SERVICE_TYPE_NODE_PORT     ServiceType = 2
	ServiceType_SERVICE_TYPE_LOAD_BALANCER ServiceType = 3
	ServiceType_SERVICE_TYPE_EXTERNAL_NAME ServiceType = 4
)

// Enum value maps for ServiceType.
var (
	ServiceType_name = map[int32]string{
		0: "SERVICE_TYPE_UNKNOWN",
		1: "SERVICE_TYPE_CLUSTER_IP",
		2: "SERVICE_TYPE_NODE_PORT",
		3: "SERVICE_TYPE_LOAD_BALANCER",
		4: "SERVICE_TYPE_EXTERNAL_NAME",
	}
	ServiceType_value = map[string]int32{
		"SERVICE_TYPE_UNKNOWN":       0,
		"SERVICE_TYPE_CLUSTER_IP":    1,
		"SERVICE_TYPE_NODE_PORT":     2,
		"SERVICE_TYPE_LOAD_BALANCER": 3,
		"SERVICE_TYPE_EXTERNAL_NAME": 4,
	}
)

func (x ServiceType) Enum() *ServiceType {
	p := new(ServiceType)
	*p = x
	return p
}

func (x ServiceType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ServiceType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[1].Descriptor()
}

func (ServiceType) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[1]
}

func (x ServiceType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ServiceType.Descriptor instead.
func (ServiceType) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{1}
}

type TaintEffect int32

const (
	TaintEffect_TAINT_EFFECT_UNKNOWN            TaintEffect = 0
	TaintEffect_TAINT_EFFECT_NO_SCHEDULE        TaintEffect = 1
	TaintEffect_TAINT_EFFECT_PREFER_NO_SCHEDULE TaintEffect = 2
	TaintEffect_TAINT_EFFECT_NO_EXECUTE         TaintEffect = 3
)

// Enum value maps for TaintEffect.
var (
	TaintEffect_name = map[int32]string{
		0: "TAINT_EFFECT_UNKNOWN",
		1: "TAINT_EFFECT_NO_SCHEDULE",
		2: "TAINT_EFFECT_PREFER_NO_SCHEDULE",
		3: "TAINT_EFFECT_NO_EXECUTE",
	}
	TaintEffect_value = map[string]int32{
		"TAINT_EFFECT_UNKNOWN":            0,
		"TAINT_EFFECT_NO_SCHEDULE":        1,
		"TAINT_EFFECT_PREFER_NO_SCHEDULE": 2,
		"TAINT_EFFECT_NO_EXECUTE":         3,
	}
)

func (x TaintEffect) Enum() *TaintEffect {
	p := new(TaintEffect)
	*p = x
	return p
}

func (x TaintEffect) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TaintEffect) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[2].Descriptor()
}

func (TaintEffect) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[2]
}

func (x TaintEffect) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TaintEffect.Descriptor instead.
func (TaintEffect) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{2}
}

type TolerationOperator int32

const (
	TolerationOperator_TOLERATION_OPERATOR_UNKNOWN TolerationOperator = 0
	TolerationOperator_TOLERATION_OPERATOR_EXISTS  TolerationOperator = 1
	TolerationOperator_TOLERATION_OPERATOR_EQUAL   TolerationOperator = 2
)

// Enum value maps for TolerationOperator.
var (
	TolerationOperator_name = map[int32]string{
		0: "TOLERATION_OPERATOR_UNKNOWN",
		1: "TOLERATION_OPERATOR_EXISTS",
		2: "TOLERATION_OPERATOR_EQUAL",
	}
	TolerationOperator_value = map[string]int32{
		"TOLERATION_OPERATOR_UNKNOWN": 0,
		"TOLERATION_OPERATOR_EXISTS":  1,
		"TOLERATION_OPERATOR_EQUAL":   2,
	}
)

func (x TolerationOperator) Enum() *TolerationOperator {
	p := new(TolerationOperator)
	*p = x
	return p
}

func (x TolerationOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TolerationOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[3].Descriptor()
}

func (TolerationOperator) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[3]
}

func (x TolerationOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TolerationOperator.Descriptor instead.
func (TolerationOperator) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{3}
}

type NodeSelectorOperator int32

const (
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_UNKNOWN        NodeSelectorOperator = 0
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN             NodeSelectorOperator = 1
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN         NodeSelectorOperator = 2
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_EXISTS         NodeSelectorOperator = 3
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST NodeSelectorOperator = 4
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_GT             NodeSelectorOperator = 5
	NodeSelectorOperator_NODE_SELECTOR_OPERATOR_LT             NodeSelectorOperator = 6
)

// Enum value maps for NodeSelectorOperator.
var (
	NodeSelectorOperator_name = map[int32]string{
		0: "NODE_SELECTOR_OPERATOR_UNKNOWN",
		1: "NODE_SELECTOR_OPERATOR_IN",
		2: "NODE_SELECTOR_OPERATOR_NOT_IN",
		3: "NODE_SELECTOR_OPERATOR_EXISTS",
		4: "NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST",
		5: "NODE_SELECTOR_OPERATOR_GT",
		6: "NODE_SELECTOR_OPERATOR_LT",
	}
	NodeSelectorOperator_value = map[string]int32{
		"NODE_SELECTOR_OPERATOR_UNKNOWN":        0,
		"NODE_SELECTOR_OPERATOR_IN":             1,
		"NODE_SELECTOR_OPERATOR_NOT_IN":         2,
		"NODE_SELECTOR_OPERATOR_EXISTS":         3,
		"NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST": 4,
		"NODE_SELECTOR_OPERATOR_GT":             5,
		"NODE_SELECTOR_OPERATOR_LT":             6,
	}
)

func (x NodeSelectorOperator) Enum() *NodeSelectorOperator {
	p := new(NodeSelectorOperator)
	*p = x
	return p
}

func (x NodeSelectorOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NodeSelectorOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[4].Descriptor()
}

func (NodeSelectorOperator) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[4]
}

func (x NodeSelectorOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NodeSelectorOperator.Descriptor instead.
func (NodeSelectorOperator) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{4}
}

type LabelSelectorOperator int32

const (
	LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_UNKNOWN        LabelSelectorOperator = 0
	LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN             LabelSelectorOperator = 1
	LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN         LabelSelectorOperator = 2
	LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS         LabelSelectorOperator = 3
	LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST LabelSelectorOperator = 4
)

// Enum value maps for LabelSelectorOperator.
var (
	LabelSelectorOperator_name = map[int32]string{
		0: "LABEL_SELECTOR_OPERATOR_UNKNOWN",
		1: "LABEL_SELECTOR_OPERATOR_IN",
		2: "LABEL_SELECTOR_OPERATOR_NOT_IN",
		3: "LABEL_SELECTOR_OPERATOR_EXISTS",
		4: "LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST",
	}
	LabelSelectorOperator_value = map[string]int32{
		"LABEL_SELECTOR_OPERATOR_UNKNOWN":        0,
		"LABEL_SELECTOR_OPERATOR_IN":             1,
		"LABEL_SELECTOR_OPERATOR_NOT_IN":         2,
		"LABEL_SELECTOR_OPERATOR_EXISTS":         3,
		"LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST": 4,
	}
)

func (x LabelSelectorOperator) Enum() *LabelSelectorOperator {
	p := new(LabelSelectorOperator)
	*p = x
	return p
}

func (x LabelSelectorOperator) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LabelSelectorOperator) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[5].Descriptor()
}

func (LabelSelectorOperator) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[5]
}

func (x LabelSelectorOperator) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LabelSelectorOperator.Descriptor instead.
func (LabelSelectorOperator) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{5}
}

type PodPhase int32

const (
	PodPhase_POD_PHASE_UNKNOWN   PodPhase = 0
	PodPhase_POD_PHASE_PENDING   PodPhase = 1
	PodPhase_POD_PHASE_RUNNING   PodPhase = 2
	PodPhase_POD_PHASE_SUCCEEDED PodPhase = 3
	PodPhase_POD_PHASE_FAILED    PodPhase = 4
)

// Enum value maps for PodPhase.
var (
	PodPhase_name = map[int32]string{
		0: "POD_PHASE_UNKNOWN",
		1: "POD_PHASE_PENDING",
		2: "POD_PHASE_RUNNING",
		3: "POD_PHASE_SUCCEEDED",
		4: "POD_PHASE_FAILED",
	}
	PodPhase_value = map[string]int32{
		"POD_PHASE_UNKNOWN":   0,
		"POD_PHASE_PENDING":   1,
		"POD_PHASE_RUNNING":   2,
		"POD_PHASE_SUCCEEDED": 3,
		"POD_PHASE_FAILED":    4,
	}
)

func (x PodPhase) Enum() *PodPhase {
	p := new(PodPhase)
	*p = x
	return p
}

func (x PodPhase) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PodPhase) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[6].Descriptor()
}

func (PodPhase) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[6]
}

func (x PodPhase) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PodPhase.Descriptor instead.
func (PodPhase) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{6}
}

type NetworkProtocol int32

const (
	NetworkProtocol_NETWORK_PROTOCOL_UNKNOWN NetworkProtocol = 0
	NetworkProtocol_NETWORK_PROTOCOL_TCP     NetworkProtocol = 1
	NetworkProtocol_NETWORK_PROTOCOL_UDP     NetworkProtocol = 2
	NetworkProtocol_NETWORK_PROTOCOL_SCTP    NetworkProtocol = 3
)

// Enum value maps for NetworkProtocol.
var (
	NetworkProtocol_name = map[int32]string{
		0: "NETWORK_PROTOCOL_UNKNOWN",
		1: "NETWORK_PROTOCOL_TCP",
		2: "NETWORK_PROTOCOL_UDP",
		3: "NETWORK_PROTOCOL_SCTP",
	}
	NetworkProtocol_value = map[string]int32{
		"NETWORK_PROTOCOL_UNKNOWN": 0,
		"NETWORK_PROTOCOL_TCP":     1,
		"NETWORK_PROTOCOL_UDP":     2,
		"NETWORK_PROTOCOL_SCTP":    3,
	}
)

func (x NetworkProtocol) Enum() *NetworkProtocol {
	p := new(NetworkProtocol)
	*p = x
	return p
}

func (x NetworkProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[7].Descriptor()
}

func (NetworkProtocol) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[7]
}

func (x NetworkProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkProtocol.Descriptor instead.
func (NetworkProtocol) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{7}
}

type NetworkPolicyType int32

const (
	NetworkPolicyType_NETWORK_POLICY_UNKNOWN NetworkPolicyType = 0
	NetworkPolicyType_NETWORK_POLICY_INGRESS NetworkPolicyType = 1
	NetworkPolicyType_NETWORK_POLICY_EGRESS  NetworkPolicyType = 2
)

// Enum value maps for NetworkPolicyType.
var (
	NetworkPolicyType_name = map[int32]string{
		0: "NETWORK_POLICY_UNKNOWN",
		1: "NETWORK_POLICY_INGRESS",
		2: "NETWORK_POLICY_EGRESS",
	}
	NetworkPolicyType_value = map[string]int32{
		"NETWORK_POLICY_UNKNOWN": 0,
		"NETWORK_POLICY_INGRESS": 1,
		"NETWORK_POLICY_EGRESS":  2,
	}
)

func (x NetworkPolicyType) Enum() *NetworkPolicyType {
	p := new(NetworkPolicyType)
	*p = x
	return p
}

func (x NetworkPolicyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (NetworkPolicyType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[8].Descriptor()
}

func (NetworkPolicyType) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[8]
}

func (x NetworkPolicyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use NetworkPolicyType.Descriptor instead.
func (NetworkPolicyType) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{8}
}

type PVCAccessMode int32

const (
	PVCAccessMode_PVC_ACCESS_MODE_UNSPECIFIED         PVCAccessMode = 0
	PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE     PVCAccessMode = 1
	PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_MANY     PVCAccessMode = 2
	PVCAccessMode_PVC_ACCESS_MODE_READ_ONLY_MANY      PVCAccessMode = 3
	PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE_POD PVCAccessMode = 4
)

// Enum value maps for PVCAccessMode.
var (
	PVCAccessMode_name = map[int32]string{
		0: "PVC_ACCESS_MODE_UNSPECIFIED",
		1: "PVC_ACCESS_MODE_READ_WRITE_ONCE",
		2: "PVC_ACCESS_MODE_READ_WRITE_MANY",
		3: "PVC_ACCESS_MODE_READ_ONLY_MANY",
		4: "PVC_ACCESS_MODE_READ_WRITE_ONCE_POD",
	}
	PVCAccessMode_value = map[string]int32{
		"PVC_ACCESS_MODE_UNSPECIFIED":         0,
		"PVC_ACCESS_MODE_READ_WRITE_ONCE":     1,
		"PVC_ACCESS_MODE_READ_WRITE_MANY":     2,
		"PVC_ACCESS_MODE_READ_ONLY_MANY":      3,
		"PVC_ACCESS_MODE_READ_WRITE_ONCE_POD": 4,
	}
)

func (x PVCAccessMode) Enum() *PVCAccessMode {
	p := new(PVCAccessMode)
	*p = x
	return p
}

func (x PVCAccessMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PVCAccessMode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_k8s_proto_enumTypes[9].Descriptor()
}

func (PVCAccessMode) Type() protoreflect.EnumType {
	return &file_common_k8s_proto_enumTypes[9]
}

func (x PVCAccessMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PVCAccessMode.Descriptor instead.
func (PVCAccessMode) EnumDescriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{9}
}

type ConfigMap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Immutable     bool                   `protobuf:"varint,1,opt,name=immutable,proto3" json:"immutable,omitempty"`
	Data          map[string]string      `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	BinaryData    map[string][]byte      `protobuf:"bytes,3,rep,name=binary_data,json=binaryData,proto3" json:"binary_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ConfigMap) Reset() {
	*x = ConfigMap{}
	mi := &file_common_k8s_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ConfigMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ConfigMap) ProtoMessage() {}

func (x *ConfigMap) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ConfigMap.ProtoReflect.Descriptor instead.
func (*ConfigMap) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{0}
}

func (x *ConfigMap) GetImmutable() bool {
	if x != nil {
		return x.Immutable
	}
	return false
}

func (x *ConfigMap) GetData() map[string]string {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *ConfigMap) GetBinaryData() map[string][]byte {
	if x != nil {
		return x.BinaryData
	}
	return nil
}

type Secret struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Immutable     bool                   `protobuf:"varint,1,opt,name=immutable,proto3" json:"immutable,omitempty"`
	Data          map[string][]byte      `protobuf:"bytes,2,rep,name=data,proto3" json:"data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	StringData    map[string]string      `protobuf:"bytes,3,rep,name=string_data,json=stringData,proto3" json:"string_data,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Type          string                 `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Secret) Reset() {
	*x = Secret{}
	mi := &file_common_k8s_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Secret) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Secret) ProtoMessage() {}

func (x *Secret) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Secret.ProtoReflect.Descriptor instead.
func (*Secret) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{1}
}

func (x *Secret) GetImmutable() bool {
	if x != nil {
		return x.Immutable
	}
	return false
}

func (x *Secret) GetData() map[string][]byte {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *Secret) GetStringData() map[string]string {
	if x != nil {
		return x.StringData
	}
	return nil
}

func (x *Secret) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

type ServiceAccount struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Namespace     string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceAccount) Reset() {
	*x = ServiceAccount{}
	mi := &file_common_k8s_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceAccount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceAccount) ProtoMessage() {}

func (x *ServiceAccount) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceAccount.ProtoReflect.Descriptor instead.
func (*ServiceAccount) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{2}
}

func (x *ServiceAccount) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServiceAccount) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type HelmRelease struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReleaseName   string                 `protobuf:"bytes,1,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	Namespace     string                 `protobuf:"bytes,2,opt,name=namespace,proto3" json:"namespace,omitempty"`
	Status        HelmReleaseStatus      `protobuf:"varint,3,opt,name=status,proto3,enum=common.k8s.HelmReleaseStatus" json:"status,omitempty"`
	Version       string                 `protobuf:"bytes,4,opt,name=version,proto3" json:"version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *HelmRelease) Reset() {
	*x = HelmRelease{}
	mi := &file_common_k8s_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *HelmRelease) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HelmRelease) ProtoMessage() {}

func (x *HelmRelease) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HelmRelease.ProtoReflect.Descriptor instead.
func (*HelmRelease) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{3}
}

func (x *HelmRelease) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *HelmRelease) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *HelmRelease) GetStatus() HelmReleaseStatus {
	if x != nil {
		return x.Status
	}
	return HelmReleaseStatus_UNKNOWN
}

func (x *HelmRelease) GetVersion() string {
	if x != nil {
		return x.Version
	}
	return ""
}

type Toleration struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Key            string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value          string                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	Effect         TaintEffect            `protobuf:"varint,3,opt,name=effect,proto3,enum=common.k8s.TaintEffect" json:"effect,omitempty"`
	Operator       TolerationOperator     `protobuf:"varint,4,opt,name=operator,proto3,enum=common.k8s.TolerationOperator" json:"operator,omitempty"`
	TolerationSecs *int64                 `protobuf:"varint,5,opt,name=toleration_secs,json=tolerationSecs,proto3,oneof" json:"toleration_secs,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *Toleration) Reset() {
	*x = Toleration{}
	mi := &file_common_k8s_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Toleration) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Toleration) ProtoMessage() {}

func (x *Toleration) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Toleration.ProtoReflect.Descriptor instead.
func (*Toleration) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{4}
}

func (x *Toleration) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *Toleration) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

func (x *Toleration) GetEffect() TaintEffect {
	if x != nil {
		return x.Effect
	}
	return TaintEffect_TAINT_EFFECT_UNKNOWN
}

func (x *Toleration) GetOperator() TolerationOperator {
	if x != nil {
		return x.Operator
	}
	return TolerationOperator_TOLERATION_OPERATOR_UNKNOWN
}

func (x *Toleration) GetTolerationSecs() int64 {
	if x != nil && x.TolerationSecs != nil {
		return *x.TolerationSecs
	}
	return 0
}

// Affinity is a group of affinity scheduling rules.
type Affinity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Describes node affinity scheduling rules for the pod.
	NodeAffinity *NodeAffinity `protobuf:"bytes,1,opt,name=node_affinity,json=nodeAffinity,proto3" json:"node_affinity,omitempty"`
	// Describes pod affinity scheduling rules (e.g. co-locate this pod in the
	// same node, zone, etc. as some other pod(s)).
	PodAffinity *PodAffinity `protobuf:"bytes,2,opt,name=pod_affinity,json=podAffinity,proto3" json:"pod_affinity,omitempty"`
	// Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod
	// in the same node, zone, etc. as some other pod(s)).
	PodAntiAffinity *PodAntiAffinity `protobuf:"bytes,3,opt,name=pod_anti_affinity,json=podAntiAffinity,proto3" json:"pod_anti_affinity,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Affinity) Reset() {
	*x = Affinity{}
	mi := &file_common_k8s_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Affinity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Affinity) ProtoMessage() {}

func (x *Affinity) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Affinity.ProtoReflect.Descriptor instead.
func (*Affinity) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{5}
}

func (x *Affinity) GetNodeAffinity() *NodeAffinity {
	if x != nil {
		return x.NodeAffinity
	}
	return nil
}

func (x *Affinity) GetPodAffinity() *PodAffinity {
	if x != nil {
		return x.PodAffinity
	}
	return nil
}

func (x *Affinity) GetPodAntiAffinity() *PodAntiAffinity {
	if x != nil {
		return x.PodAntiAffinity
	}
	return nil
}

// Node affinity is a group of node affinity scheduling rules.
type NodeAffinity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If the affinity requirements specified by this field are not met at
	// scheduling time, the pod will not be scheduled onto the node.
	// If the affinity requirements specified by this field cease to be met
	// at some point during pod execution (e.g. due to an update), the system
	// may or may not try to eventually evict the pod from its node.
	// +optional
	RequiredDuringSchedulingIgnoredDuringExecution *NodeSelector `protobuf:"bytes,1,opt,name=required_during_scheduling_ignored_during_execution,json=requiredDuringSchedulingIgnoredDuringExecution,proto3" json:"required_during_scheduling_ignored_during_execution,omitempty"`
	// The scheduler will prefer to schedule pods to nodes that satisfy
	// the affinity expressions specified by this field, but it may choose
	// a node that violates one or more of the expressions. The node that is
	// most preferred is the one with the greatest sum of weights, i.e.
	// for each node that meets all of the scheduling requirements (resource
	// request, requiredDuringScheduling affinity expressions, etc.),
	// compute a sum by iterating through the elements of this field and adding
	// "weight" to the sum if the node matches the corresponding matchExpressions;
	// the node(s) with the highest sum are the most preferred. +optional
	PreferredDuringSchedulingIgnoredDuringExecution []*PreferredSchedulingTerm `protobuf:"bytes,2,rep,name=preferred_during_scheduling_ignored_during_execution,json=preferredDuringSchedulingIgnoredDuringExecution,proto3" json:"preferred_during_scheduling_ignored_during_execution,omitempty"`
	unknownFields                                   protoimpl.UnknownFields
	sizeCache                                       protoimpl.SizeCache
}

func (x *NodeAffinity) Reset() {
	*x = NodeAffinity{}
	mi := &file_common_k8s_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeAffinity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeAffinity) ProtoMessage() {}

func (x *NodeAffinity) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeAffinity.ProtoReflect.Descriptor instead.
func (*NodeAffinity) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{6}
}

func (x *NodeAffinity) GetRequiredDuringSchedulingIgnoredDuringExecution() *NodeSelector {
	if x != nil {
		return x.RequiredDuringSchedulingIgnoredDuringExecution
	}
	return nil
}

func (x *NodeAffinity) GetPreferredDuringSchedulingIgnoredDuringExecution() []*PreferredSchedulingTerm {
	if x != nil {
		return x.PreferredDuringSchedulingIgnoredDuringExecution
	}
	return nil
}

// A node selector represents the union of the results of one or more label
// queries over a set of nodes; that is, it represents the OR of the selectors
// represented by the node selector terms.
type NodeSelector struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A list of node selector terms. The terms are ORed.
	NodeSelectorTerms []*NodeSelectorTerm `protobuf:"bytes,1,rep,name=node_selector_terms,json=nodeSelectorTerms,proto3" json:"node_selector_terms,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *NodeSelector) Reset() {
	*x = NodeSelector{}
	mi := &file_common_k8s_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSelector) ProtoMessage() {}

func (x *NodeSelector) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSelector.ProtoReflect.Descriptor instead.
func (*NodeSelector) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{7}
}

func (x *NodeSelector) GetNodeSelectorTerms() []*NodeSelectorTerm {
	if x != nil {
		return x.NodeSelectorTerms
	}
	return nil
}

// An empty preferred scheduling term matches all objects with implicit weight 0
// (i.e. it's a no-op). A null preferred scheduling term matches no objects
// (i.e. is also a no-op).
type PreferredSchedulingTerm struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Weight associated with matching the corresponding nodeSelectorTerm, in the
	// range 1-100.
	Weight int32 `protobuf:"varint,1,opt,name=weight,proto3" json:"weight,omitempty"`
	// A node selector term, associated with the corresponding weight.
	Preference    *NodeSelectorTerm `protobuf:"bytes,2,opt,name=preference,proto3" json:"preference,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PreferredSchedulingTerm) Reset() {
	*x = PreferredSchedulingTerm{}
	mi := &file_common_k8s_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PreferredSchedulingTerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PreferredSchedulingTerm) ProtoMessage() {}

func (x *PreferredSchedulingTerm) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PreferredSchedulingTerm.ProtoReflect.Descriptor instead.
func (*PreferredSchedulingTerm) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{8}
}

func (x *PreferredSchedulingTerm) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *PreferredSchedulingTerm) GetPreference() *NodeSelectorTerm {
	if x != nil {
		return x.Preference
	}
	return nil
}

// A null or empty node selector term matches no objects. The requirements of
// them are ANDed.
// The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
type NodeSelectorTerm struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A list of node selector requirements by node's labels.
	// +optional
	MatchExpressions []*NodeSelectorRequirement `protobuf:"bytes,1,rep,name=match_expressions,json=matchExpressions,proto3" json:"match_expressions,omitempty"`
	// A list of node selector requirements by node's fields.
	// +optional
	MatchFields   []*NodeSelectorRequirement `protobuf:"bytes,2,rep,name=match_fields,json=matchFields,proto3" json:"match_fields,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeSelectorTerm) Reset() {
	*x = NodeSelectorTerm{}
	mi := &file_common_k8s_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeSelectorTerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSelectorTerm) ProtoMessage() {}

func (x *NodeSelectorTerm) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSelectorTerm.ProtoReflect.Descriptor instead.
func (*NodeSelectorTerm) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{9}
}

func (x *NodeSelectorTerm) GetMatchExpressions() []*NodeSelectorRequirement {
	if x != nil {
		return x.MatchExpressions
	}
	return nil
}

func (x *NodeSelectorTerm) GetMatchFields() []*NodeSelectorRequirement {
	if x != nil {
		return x.MatchFields
	}
	return nil
}

// A node selector requirement is a selector that contains values, a key, and an
// operator that relates the key and values.
type NodeSelectorRequirement struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The label key that the selector applies to.
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// Represents a key's relationship to a set of values.
	// Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
	Operator NodeSelectorOperator `protobuf:"varint,2,opt,name=operator,proto3,enum=common.k8s.NodeSelectorOperator" json:"operator,omitempty"`
	// An array of string values. If the operator is In or NotIn,
	// the values array must be non-empty. If the operator is Exists or
	// DoesNotExist, the values array must be empty. If the operator is Gt or Lt,
	// the values array must have a single element, which will be interpreted as
	// an integer. This array is replaced during a strategic merge patch.
	// +optional
	Values        []string `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeSelectorRequirement) Reset() {
	*x = NodeSelectorRequirement{}
	mi := &file_common_k8s_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeSelectorRequirement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeSelectorRequirement) ProtoMessage() {}

func (x *NodeSelectorRequirement) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeSelectorRequirement.ProtoReflect.Descriptor instead.
func (*NodeSelectorRequirement) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{10}
}

func (x *NodeSelectorRequirement) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *NodeSelectorRequirement) GetOperator() NodeSelectorOperator {
	if x != nil {
		return x.Operator
	}
	return NodeSelectorOperator_NODE_SELECTOR_OPERATOR_UNKNOWN
}

func (x *NodeSelectorRequirement) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

// Pod affinity is a group of inter pod affinity scheduling rules.
type PodAffinity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If the affinity requirements specified by this field are not met at
	// scheduling time, the pod will not be scheduled onto the node.
	// If the affinity requirements specified by this field cease to be met
	// at some point during pod execution (e.g. due to a pod label update), the
	// system may or may not try to eventually evict the pod from its node.
	// When there are multiple elements, the lists of nodes corresponding to each
	// podAffinityTerm are intersected, i.e. all terms must be satisfied.
	// +optional
	RequiredDuringSchedulingIgnoredDuringExecution []*PodAffinityTerm `protobuf:"bytes,1,rep,name=required_during_scheduling_ignored_during_execution,json=requiredDuringSchedulingIgnoredDuringExecution,proto3" json:"required_during_scheduling_ignored_during_execution,omitempty"`
	// The scheduler will prefer to schedule pods to nodes that satisfy
	// the affinity expressions specified by this field, but it may choose
	// a node that violates one or more of the expressions. The node that is
	// most preferred is the one with the greatest sum of weights, i.e.
	// for each node that meets all of the scheduling requirements (resource
	// request, requiredDuringScheduling affinity expressions, etc.),
	// compute a sum by iterating through the elements of this field and adding
	// "weight" to the sum if the node has pods which matches the corresponding
	// podAffinityTerm; the node(s) with the highest sum are the most preferred.
	// +optional
	PreferredDuringSchedulingIgnoredDuringExecution []*WeightedPodAffinityTerm `protobuf:"bytes,2,rep,name=preferred_during_scheduling_ignored_during_execution,json=preferredDuringSchedulingIgnoredDuringExecution,proto3" json:"preferred_during_scheduling_ignored_during_execution,omitempty"`
	unknownFields                                   protoimpl.UnknownFields
	sizeCache                                       protoimpl.SizeCache
}

func (x *PodAffinity) Reset() {
	*x = PodAffinity{}
	mi := &file_common_k8s_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PodAffinity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodAffinity) ProtoMessage() {}

func (x *PodAffinity) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodAffinity.ProtoReflect.Descriptor instead.
func (*PodAffinity) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{11}
}

func (x *PodAffinity) GetRequiredDuringSchedulingIgnoredDuringExecution() []*PodAffinityTerm {
	if x != nil {
		return x.RequiredDuringSchedulingIgnoredDuringExecution
	}
	return nil
}

func (x *PodAffinity) GetPreferredDuringSchedulingIgnoredDuringExecution() []*WeightedPodAffinityTerm {
	if x != nil {
		return x.PreferredDuringSchedulingIgnoredDuringExecution
	}
	return nil
}

// Pod anti affinity is a group of inter pod anti affinity scheduling rules.
type PodAntiAffinity struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If the anti-affinity requirements specified by this field are not met at
	// scheduling time, the pod will not be scheduled onto the node.
	// If the anti-affinity requirements specified by this field cease to be met
	// at some point during pod execution (e.g. due to a pod label update), the
	// system may or may not try to eventually evict the pod from its node.
	// When there are multiple elements, the lists of nodes corresponding to each
	// podAffinityTerm are intersected, i.e. all terms must be satisfied.
	RequiredDuringSchedulingIgnoredDuringExecution []*PodAffinityTerm `protobuf:"bytes,1,rep,name=required_during_scheduling_ignored_during_execution,json=requiredDuringSchedulingIgnoredDuringExecution,proto3" json:"required_during_scheduling_ignored_during_execution,omitempty"`
	// The scheduler will prefer to schedule pods to nodes that satisfy
	// the anti-affinity expressions specified by this field, but it may choose
	// a node that violates one or more of the expressions. The node that is
	// most preferred is the one with the greatest sum of weights, i.e.
	// for each node that meets all of the scheduling requirements (resource
	// request, requiredDuringScheduling anti-affinity expressions, etc.),
	// compute a sum by iterating through the elements of this field and adding
	// "weight" to the sum if the node has pods which matches the corresponding
	// podAffinityTerm; the node(s) with the highest sum are the most preferred.
	PreferredDuringSchedulingIgnoredDuringExecution []*WeightedPodAffinityTerm `protobuf:"bytes,2,rep,name=preferred_during_scheduling_ignored_during_execution,json=preferredDuringSchedulingIgnoredDuringExecution,proto3" json:"preferred_during_scheduling_ignored_during_execution,omitempty"`
	unknownFields                                   protoimpl.UnknownFields
	sizeCache                                       protoimpl.SizeCache
}

func (x *PodAntiAffinity) Reset() {
	*x = PodAntiAffinity{}
	mi := &file_common_k8s_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PodAntiAffinity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodAntiAffinity) ProtoMessage() {}

func (x *PodAntiAffinity) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodAntiAffinity.ProtoReflect.Descriptor instead.
func (*PodAntiAffinity) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{12}
}

func (x *PodAntiAffinity) GetRequiredDuringSchedulingIgnoredDuringExecution() []*PodAffinityTerm {
	if x != nil {
		return x.RequiredDuringSchedulingIgnoredDuringExecution
	}
	return nil
}

func (x *PodAntiAffinity) GetPreferredDuringSchedulingIgnoredDuringExecution() []*WeightedPodAffinityTerm {
	if x != nil {
		return x.PreferredDuringSchedulingIgnoredDuringExecution
	}
	return nil
}

type PodAffinityTerm struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// A label query over a set of resources, in this case pods.
	// +optional
	LabelSelector *LabelSelector `protobuf:"bytes,1,opt,name=label_selector,json=labelSelector,proto3" json:"label_selector,omitempty"`
	// namespaces specifies a static list of namespace names that the term applies
	// to. The term is applied to the union of the namespaces listed in this field
	// and the ones selected by namespaceSelector.
	// null or empty namespaces list and null namespaceSelector means "this pod's
	// namespace". +optional
	Namespaces []string `protobuf:"bytes,2,rep,name=namespaces,proto3" json:"namespaces,omitempty"`
	// This pod should be co-located (affinity) or not co-located (anti-affinity)
	// with the pods matching the labelSelector in the specified namespaces, where
	// co-located is defined as running on a node whose value of the label with
	// key topologyKey matches that of any node on which any of the selected pods
	// is running. Empty topologyKey is not allowed.
	TopologyKey string `protobuf:"bytes,3,opt,name=topology_key,json=topologyKey,proto3" json:"topology_key,omitempty"`
	// A label query over the set of namespaces that the term applies to.
	// The term is applied to the union of the namespaces selected by this field
	// and the ones listed in the namespaces field.
	// null selector and null or empty namespaces list means "this pod's
	// namespace". An empty selector ({}) matches all namespaces. +optional
	NamespaceSelector *LabelSelector `protobuf:"bytes,4,opt,name=namespace_selector,json=namespaceSelector,proto3" json:"namespace_selector,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *PodAffinityTerm) Reset() {
	*x = PodAffinityTerm{}
	mi := &file_common_k8s_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PodAffinityTerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PodAffinityTerm) ProtoMessage() {}

func (x *PodAffinityTerm) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PodAffinityTerm.ProtoReflect.Descriptor instead.
func (*PodAffinityTerm) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{13}
}

func (x *PodAffinityTerm) GetLabelSelector() *LabelSelector {
	if x != nil {
		return x.LabelSelector
	}
	return nil
}

func (x *PodAffinityTerm) GetNamespaces() []string {
	if x != nil {
		return x.Namespaces
	}
	return nil
}

func (x *PodAffinityTerm) GetTopologyKey() string {
	if x != nil {
		return x.TopologyKey
	}
	return ""
}

func (x *PodAffinityTerm) GetNamespaceSelector() *LabelSelector {
	if x != nil {
		return x.NamespaceSelector
	}
	return nil
}

type WeightedPodAffinityTerm struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// weight associated with matching the corresponding podAffinityTerm,
	// in the range 1-100.
	Weight int32 `protobuf:"varint,1,opt,name=weight,proto3" json:"weight,omitempty"`
	// Required. A pod affinity term, associated with the corresponding weight.
	PodAffinityTerm *PodAffinityTerm `protobuf:"bytes,2,opt,name=pod_affinity_term,json=podAffinityTerm,proto3" json:"pod_affinity_term,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *WeightedPodAffinityTerm) Reset() {
	*x = WeightedPodAffinityTerm{}
	mi := &file_common_k8s_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *WeightedPodAffinityTerm) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*WeightedPodAffinityTerm) ProtoMessage() {}

func (x *WeightedPodAffinityTerm) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use WeightedPodAffinityTerm.ProtoReflect.Descriptor instead.
func (*WeightedPodAffinityTerm) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{14}
}

func (x *WeightedPodAffinityTerm) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *WeightedPodAffinityTerm) GetPodAffinityTerm() *PodAffinityTerm {
	if x != nil {
		return x.PodAffinityTerm
	}
	return nil
}

type LabelSelector struct {
	state            protoimpl.MessageState      `protogen:"open.v1"`
	MatchLabels      map[string]string           `protobuf:"bytes,1,rep,name=match_labels,json=matchLabels,proto3" json:"match_labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	MatchExpressions []*LabelSelectorRequirement `protobuf:"bytes,2,rep,name=match_expressions,json=matchExpressions,proto3" json:"match_expressions,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *LabelSelector) Reset() {
	*x = LabelSelector{}
	mi := &file_common_k8s_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelSelector) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelSelector) ProtoMessage() {}

func (x *LabelSelector) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelSelector.ProtoReflect.Descriptor instead.
func (*LabelSelector) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{15}
}

func (x *LabelSelector) GetMatchLabels() map[string]string {
	if x != nil {
		return x.MatchLabels
	}
	return nil
}

func (x *LabelSelector) GetMatchExpressions() []*LabelSelectorRequirement {
	if x != nil {
		return x.MatchExpressions
	}
	return nil
}

type LabelSelectorRequirement struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           string                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Operator      LabelSelectorOperator  `protobuf:"varint,2,opt,name=operator,proto3,enum=common.k8s.LabelSelectorOperator" json:"operator,omitempty"`
	Values        []string               `protobuf:"bytes,3,rep,name=values,proto3" json:"values,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelSelectorRequirement) Reset() {
	*x = LabelSelectorRequirement{}
	mi := &file_common_k8s_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelSelectorRequirement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelSelectorRequirement) ProtoMessage() {}

func (x *LabelSelectorRequirement) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelSelectorRequirement.ProtoReflect.Descriptor instead.
func (*LabelSelectorRequirement) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{16}
}

func (x *LabelSelectorRequirement) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *LabelSelectorRequirement) GetOperator() LabelSelectorOperator {
	if x != nil {
		return x.Operator
	}
	return LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_UNKNOWN
}

func (x *LabelSelectorRequirement) GetValues() []string {
	if x != nil {
		return x.Values
	}
	return nil
}

type ResourceRequirements struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	CpuRequest    string                 `protobuf:"bytes,1,opt,name=cpu_request,json=cpuRequest,proto3" json:"cpu_request,omitempty"`
	CpuLimit      string                 `protobuf:"bytes,2,opt,name=cpu_limit,json=cpuLimit,proto3" json:"cpu_limit,omitempty"`
	MemoryRequest string                 `protobuf:"bytes,3,opt,name=memory_request,json=memoryRequest,proto3" json:"memory_request,omitempty"`
	MemoryLimit   string                 `protobuf:"bytes,4,opt,name=memory_limit,json=memoryLimit,proto3" json:"memory_limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResourceRequirements) Reset() {
	*x = ResourceRequirements{}
	mi := &file_common_k8s_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResourceRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceRequirements) ProtoMessage() {}

func (x *ResourceRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceRequirements.ProtoReflect.Descriptor instead.
func (*ResourceRequirements) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{17}
}

func (x *ResourceRequirements) GetCpuRequest() string {
	if x != nil {
		return x.CpuRequest
	}
	return ""
}

func (x *ResourceRequirements) GetCpuLimit() string {
	if x != nil {
		return x.CpuLimit
	}
	return ""
}

func (x *ResourceRequirements) GetMemoryRequest() string {
	if x != nil {
		return x.MemoryRequest
	}
	return ""
}

func (x *ResourceRequirements) GetMemoryLimit() string {
	if x != nil {
		return x.MemoryLimit
	}
	return ""
}

type Pod struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	StatusPhase   PodPhase               `protobuf:"varint,3,opt,name=status_phase,json=statusPhase,proto3,enum=common.k8s.PodPhase" json:"status_phase,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Pod) Reset() {
	*x = Pod{}
	mi := &file_common_k8s_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Pod) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Pod) ProtoMessage() {}

func (x *Pod) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Pod.ProtoReflect.Descriptor instead.
func (*Pod) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{18}
}

func (x *Pod) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Pod) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *Pod) GetStatusPhase() PodPhase {
	if x != nil {
		return x.StatusPhase
	}
	return PodPhase_POD_PHASE_UNKNOWN
}

type ServiceSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ports         []*ServicePort         `protobuf:"bytes,1,rep,name=ports,proto3" json:"ports,omitempty"`
	Selector      map[string]string      `protobuf:"bytes,2,rep,name=selector,proto3" json:"selector,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServiceSpec) Reset() {
	*x = ServiceSpec{}
	mi := &file_common_k8s_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServiceSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServiceSpec) ProtoMessage() {}

func (x *ServiceSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServiceSpec.ProtoReflect.Descriptor instead.
func (*ServiceSpec) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{19}
}

func (x *ServiceSpec) GetPorts() []*ServicePort {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *ServiceSpec) GetSelector() map[string]string {
	if x != nil {
		return x.Selector
	}
	return nil
}

type ServicePort struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ServicePort) Reset() {
	*x = ServicePort{}
	mi := &file_common_k8s_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ServicePort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ServicePort) ProtoMessage() {}

func (x *ServicePort) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ServicePort.ProtoReflect.Descriptor instead.
func (*ServicePort) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{20}
}

func (x *ServicePort) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ServicePort) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

// NetworkPolicySpec provides the specification of a NetworkPolicy
type NetworkPolicySpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// podSelector selects the pods to which this NetworkPolicy object applies.
	// The array of ingress rules is applied to any pods selected by this field.
	// Multiple network policies can select the same set of pods. In this case,
	// the ingress rules for each are combined additively.
	// This field is NOT optional and follows standard label selector semantics.
	// An empty podSelector matches all pods in this namespace.
	PodSelector *LabelSelector `protobuf:"bytes,1,opt,name=pod_selector,json=podSelector,proto3" json:"pod_selector,omitempty"`
	// ingress is a list of ingress rules to be applied to the selected pods.
	// Traffic is allowed to a pod if there are no NetworkPolicies selecting the
	// pod (and cluster policy otherwise allows the traffic), OR if the traffic
	// source is the pod's local node, OR if the traffic matches at least one
	// ingress rule across all of the NetworkPolicy objects whose podSelector
	// matches the pod. If this field is empty then this NetworkPolicy does not
	// allow any traffic (and serves solely to ensure that the pods it selects are
	// isolated by default)
	Ingress []*NetworkPolicyIngressRule `protobuf:"bytes,2,rep,name=ingress,proto3" json:"ingress,omitempty"`
	// egress is a list of egress rules to be applied to the selected pods.
	// Outgoing traffic is allowed if there are no NetworkPolicies selecting the
	// pod (and cluster policy otherwise allows the traffic), OR if the traffic
	// matches at least one egress rule across all of the NetworkPolicy objects
	// whose podSelector matches the pod. If this field is empty then this
	// NetworkPolicy limits all outgoing traffic (and serves solely to ensure that
	// the pods it selects are isolated by default).
	Egress []*NetworkPolicyEgressRule `protobuf:"bytes,3,rep,name=egress,proto3" json:"egress,omitempty"`
	// policyTypes is a list of rule types that the NetworkPolicy relates to.
	// Valid options are ["Ingress"], ["Egress"], or ["Ingress", "Egress"].
	// If this field is not specified, it will default based on the existence of
	// ingress or egress rules; policies that contain an egress section are
	// assumed to affect egress, and all policies (whether or not they contain an
	// ingress section) are assumed to affect ingress. If you want to write an
	// egress-only policy, you must explicitly specify policyTypes [ "Egress" ].
	// Likewise, if you want to write a policy that specifies that no egress is
	// allowed, you must specify a policyTypes value that include "Egress" (since
	// such a policy would not include an egress section and would otherwise
	// default to just [ "Ingress" ]).
	PolicyTypes   []NetworkPolicyType `protobuf:"varint,4,rep,packed,name=policy_types,json=policyTypes,proto3,enum=common.k8s.NetworkPolicyType" json:"policy_types,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkPolicySpec) Reset() {
	*x = NetworkPolicySpec{}
	mi := &file_common_k8s_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkPolicySpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicySpec) ProtoMessage() {}

func (x *NetworkPolicySpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicySpec.ProtoReflect.Descriptor instead.
func (*NetworkPolicySpec) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{21}
}

func (x *NetworkPolicySpec) GetPodSelector() *LabelSelector {
	if x != nil {
		return x.PodSelector
	}
	return nil
}

func (x *NetworkPolicySpec) GetIngress() []*NetworkPolicyIngressRule {
	if x != nil {
		return x.Ingress
	}
	return nil
}

func (x *NetworkPolicySpec) GetEgress() []*NetworkPolicyEgressRule {
	if x != nil {
		return x.Egress
	}
	return nil
}

func (x *NetworkPolicySpec) GetPolicyTypes() []NetworkPolicyType {
	if x != nil {
		return x.PolicyTypes
	}
	return nil
}

// NetworkPolicyEgressRule describes a particular set of traffic that is allowed
// out of pods matched by a NetworkPolicySpec's podSelector. The traffic must
// match both ports and to.
type NetworkPolicyEgressRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ports is a list of destination ports for outgoing traffic.
	// Each item in this list is combined using a logical OR. If this field is
	// empty or missing, this rule matches all ports (traffic not restricted by
	// port). If this field is present and contains at least one item, then this
	// rule allows traffic only if the traffic matches at least one port in the
	// list. +optional
	Ports []*NetworkPolicyPort `protobuf:"bytes,1,rep,name=ports,proto3" json:"ports,omitempty"`
	// to is a list of destinations for outgoing traffic of pods selected for this
	// rule. Items in this list are combined using a logical OR operation. If this
	// field is empty or missing, this rule matches all destinations (traffic not
	// restricted by destination). If this field is present and contains at least
	// one item, this rule allows traffic only if the traffic matches at least one
	// item in the to list. +optional
	To            []*NetworkPolicyPeer `protobuf:"bytes,2,rep,name=to,proto3" json:"to,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkPolicyEgressRule) Reset() {
	*x = NetworkPolicyEgressRule{}
	mi := &file_common_k8s_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkPolicyEgressRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicyEgressRule) ProtoMessage() {}

func (x *NetworkPolicyEgressRule) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicyEgressRule.ProtoReflect.Descriptor instead.
func (*NetworkPolicyEgressRule) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{22}
}

func (x *NetworkPolicyEgressRule) GetPorts() []*NetworkPolicyPort {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *NetworkPolicyEgressRule) GetTo() []*NetworkPolicyPeer {
	if x != nil {
		return x.To
	}
	return nil
}

// NetworkPolicyIngressRule describes a particular set of traffic that is
// allowed to the pods matched by a NetworkPolicySpec's podSelector. The traffic
// must match both ports and from.
type NetworkPolicyIngressRule struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ports is a list of ports which should be made accessible on the pods
	// selected for this rule. Each item in this list is combined using a logical
	// OR. If this field is empty or missing, this rule matches all ports (traffic
	// not restricted by port). If this field is present and contains at least one
	// item, then this rule allows traffic only if the traffic matches at least
	// one port in the list. +optional
	Ports []*NetworkPolicyPort `protobuf:"bytes,1,rep,name=ports,proto3" json:"ports,omitempty"`
	// from is a list of sources which should be able to access the pods selected
	// for this rule. Items in this list are combined using a logical OR
	// operation. If this field is empty or missing, this rule matches all sources
	// (traffic not restricted by source). If this field is present and contains
	// at least one item, this rule allows traffic only if the traffic matches at
	// least one item in the from list. +optional
	From          []*NetworkPolicyPeer `protobuf:"bytes,2,rep,name=from,proto3" json:"from,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkPolicyIngressRule) Reset() {
	*x = NetworkPolicyIngressRule{}
	mi := &file_common_k8s_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkPolicyIngressRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicyIngressRule) ProtoMessage() {}

func (x *NetworkPolicyIngressRule) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicyIngressRule.ProtoReflect.Descriptor instead.
func (*NetworkPolicyIngressRule) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{23}
}

func (x *NetworkPolicyIngressRule) GetPorts() []*NetworkPolicyPort {
	if x != nil {
		return x.Ports
	}
	return nil
}

func (x *NetworkPolicyIngressRule) GetFrom() []*NetworkPolicyPeer {
	if x != nil {
		return x.From
	}
	return nil
}

// NetworkPolicyPort describes a port to allow traffic on
type NetworkPolicyPort struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// protocol represents the protocol (TCP, UDP, or SCTP) which traffic must
	// match. If not specified, this field defaults to TCP. +optional
	Protocol NetworkProtocol `protobuf:"varint,1,opt,name=protocol,proto3,enum=common.k8s.NetworkProtocol" json:"protocol,omitempty"`
	// port represents the port on the given protocol. This can either be a
	// numerical or named port on a pod. If this field is not provided, this
	// matches all port names and numbers. If present, only traffic on the
	// specified protocol AND port will be matched. +optional
	Port *IntOrString `protobuf:"bytes,2,opt,name=port,proto3" json:"port,omitempty"`
	// endPort indicates that the range of ports from port to endPort if set,
	// inclusive, should be allowed by the policy. This field cannot be defined if
	// the port field is not defined or if the port field is defined as a named
	// (string) port. The endPort must be equal or greater than port. +optional
	EndPort       int32 `protobuf:"varint,3,opt,name=endPort,proto3" json:"endPort,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkPolicyPort) Reset() {
	*x = NetworkPolicyPort{}
	mi := &file_common_k8s_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkPolicyPort) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicyPort) ProtoMessage() {}

func (x *NetworkPolicyPort) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicyPort.ProtoReflect.Descriptor instead.
func (*NetworkPolicyPort) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{24}
}

func (x *NetworkPolicyPort) GetProtocol() NetworkProtocol {
	if x != nil {
		return x.Protocol
	}
	return NetworkProtocol_NETWORK_PROTOCOL_UNKNOWN
}

func (x *NetworkPolicyPort) GetPort() *IntOrString {
	if x != nil {
		return x.Port
	}
	return nil
}

func (x *NetworkPolicyPort) GetEndPort() int32 {
	if x != nil {
		return x.EndPort
	}
	return 0
}

// NetworkPolicyPeer describes a peer to allow traffic to/from. Only certain
// combinations of fields are allowed
type NetworkPolicyPeer struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// podSelector is a label selector which selects pods. This field follows
	// standard label selector semantics; if present but empty, it selects all
	// pods.
	//
	// If namespaceSelector is also set, then the NetworkPolicyPeer as a whole
	// selects the pods matching podSelector in the Namespaces selected by
	// NamespaceSelector. Otherwise it selects the pods matching podSelector in
	// the policy's own namespace. +optional
	PodSelector *LabelSelector `protobuf:"bytes,1,opt,name=podSelector,proto3" json:"podSelector,omitempty"`
	// namespaceSelector selects namespaces using cluster-scoped labels. This
	// field follows standard label selector semantics; if present but empty, it
	// selects all namespaces.
	//
	// If podSelector is also set, then the NetworkPolicyPeer as a whole selects
	// the pods matching podSelector in the namespaces selected by
	// namespaceSelector. Otherwise it selects all pods in the namespaces selected
	// by namespaceSelector. +optional
	NamespaceSelector *LabelSelector `protobuf:"bytes,2,opt,name=namespaceSelector,proto3" json:"namespaceSelector,omitempty"`
	// ipBlock defines policy on a particular IPBlock. If this field is set then
	// neither of the other fields can be.
	IpBlock       *IPBlock `protobuf:"bytes,3,opt,name=ipBlock,proto3" json:"ipBlock,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NetworkPolicyPeer) Reset() {
	*x = NetworkPolicyPeer{}
	mi := &file_common_k8s_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NetworkPolicyPeer) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkPolicyPeer) ProtoMessage() {}

func (x *NetworkPolicyPeer) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkPolicyPeer.ProtoReflect.Descriptor instead.
func (*NetworkPolicyPeer) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{25}
}

func (x *NetworkPolicyPeer) GetPodSelector() *LabelSelector {
	if x != nil {
		return x.PodSelector
	}
	return nil
}

func (x *NetworkPolicyPeer) GetNamespaceSelector() *LabelSelector {
	if x != nil {
		return x.NamespaceSelector
	}
	return nil
}

func (x *NetworkPolicyPeer) GetIpBlock() *IPBlock {
	if x != nil {
		return x.IpBlock
	}
	return nil
}

type IPBlock struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// cidr is a string representing the IPBlock
	// Valid examples are "***********/24" or "2001:db8::/64"
	Cidr string `protobuf:"bytes,1,opt,name=cidr,proto3" json:"cidr,omitempty"`
	// except is a slice of CIDRs that should not be included within an IPBlock
	// Valid examples are "***********/24" or "2001:db8::/64"
	// Except values will be rejected if they are outside the cidr range
	Except        []string `protobuf:"bytes,2,rep,name=except,proto3" json:"except,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IPBlock) Reset() {
	*x = IPBlock{}
	mi := &file_common_k8s_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPBlock) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPBlock) ProtoMessage() {}

func (x *IPBlock) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPBlock.ProtoReflect.Descriptor instead.
func (*IPBlock) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{26}
}

func (x *IPBlock) GetCidr() string {
	if x != nil {
		return x.Cidr
	}
	return ""
}

func (x *IPBlock) GetExcept() []string {
	if x != nil {
		return x.Except
	}
	return nil
}

type IntOrString struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	IntVal        *int32                 `protobuf:"varint,1,opt,name=int_val,json=intVal,proto3,oneof" json:"int_val,omitempty"`
	StrVal        *string                `protobuf:"bytes,2,opt,name=str_val,json=strVal,proto3,oneof" json:"str_val,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IntOrString) Reset() {
	*x = IntOrString{}
	mi := &file_common_k8s_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IntOrString) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IntOrString) ProtoMessage() {}

func (x *IntOrString) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IntOrString.ProtoReflect.Descriptor instead.
func (*IntOrString) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{27}
}

func (x *IntOrString) GetIntVal() int32 {
	if x != nil && x.IntVal != nil {
		return *x.IntVal
	}
	return 0
}

func (x *IntOrString) GetStrVal() string {
	if x != nil && x.StrVal != nil {
		return *x.StrVal
	}
	return ""
}

type PersistentVolumeClaimSpec struct {
	state            protoimpl.MessageState      `protogen:"open.v1"`
	AccessModes      []PVCAccessMode             `protobuf:"varint,1,rep,packed,name=access_modes,json=accessModes,proto3,enum=common.k8s.PVCAccessMode" json:"access_modes,omitempty"`
	Selector         *LabelSelector              `protobuf:"bytes,2,opt,name=selector,proto3" json:"selector,omitempty"`
	Resources        *VolumeResourceRequirements `protobuf:"bytes,3,opt,name=resources,proto3" json:"resources,omitempty"`
	VolumeName       string                      `protobuf:"bytes,4,opt,name=volume_name,json=volumeName,proto3" json:"volume_name,omitempty"`
	StorageClassName string                      `protobuf:"bytes,5,opt,name=storage_class_name,json=storageClassName,proto3" json:"storage_class_name,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *PersistentVolumeClaimSpec) Reset() {
	*x = PersistentVolumeClaimSpec{}
	mi := &file_common_k8s_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersistentVolumeClaimSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersistentVolumeClaimSpec) ProtoMessage() {}

func (x *PersistentVolumeClaimSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersistentVolumeClaimSpec.ProtoReflect.Descriptor instead.
func (*PersistentVolumeClaimSpec) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{28}
}

func (x *PersistentVolumeClaimSpec) GetAccessModes() []PVCAccessMode {
	if x != nil {
		return x.AccessModes
	}
	return nil
}

func (x *PersistentVolumeClaimSpec) GetSelector() *LabelSelector {
	if x != nil {
		return x.Selector
	}
	return nil
}

func (x *PersistentVolumeClaimSpec) GetResources() *VolumeResourceRequirements {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *PersistentVolumeClaimSpec) GetVolumeName() string {
	if x != nil {
		return x.VolumeName
	}
	return ""
}

func (x *PersistentVolumeClaimSpec) GetStorageClassName() string {
	if x != nil {
		return x.StorageClassName
	}
	return ""
}

type VolumeResourceRequirements struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	StorageRequest string                 `protobuf:"bytes,1,opt,name=storage_request,json=storageRequest,proto3" json:"storage_request,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *VolumeResourceRequirements) Reset() {
	*x = VolumeResourceRequirements{}
	mi := &file_common_k8s_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VolumeResourceRequirements) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeResourceRequirements) ProtoMessage() {}

func (x *VolumeResourceRequirements) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeResourceRequirements.ProtoReflect.Descriptor instead.
func (*VolumeResourceRequirements) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{29}
}

func (x *VolumeResourceRequirements) GetStorageRequest() string {
	if x != nil {
		return x.StorageRequest
	}
	return ""
}

type VolumeMount struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// This must match the Name of a Volume.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Mounted read-only if true, read-write otherwise (false or unspecified).
	// Defaults to false.
	// +optional
	ReadOnly bool `protobuf:"varint,2,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	// Path within the container at which the volume should be mounted.  Must
	// not contain ':'.
	MountPath string `protobuf:"bytes,3,opt,name=mount_path,json=mountPath,proto3" json:"mount_path,omitempty"`
	// Path within the volume from which the container's volume should be mounted.
	// Defaults to "" (volume's root).
	// +optional
	SubPath string `protobuf:"bytes,4,opt,name=sub_path,json=subPath,proto3" json:"sub_path,omitempty"`
	// mountPropagation determines how mounts are propagated from the host
	// to container and the other way around.
	// When not set, MountPropagationNone is used.
	// This field is beta in 1.10.
	// +optional
	MountPropagation string `protobuf:"bytes,5,opt,name=mount_propagation,json=mountPropagation,proto3" json:"mount_propagation,omitempty"`
	// Expanded path within the volume from which the container's volume should be
	// mounted. Behaves similarly to SubPath but environment variable references
	// $(VAR_NAME) are expanded using the container's environment. Defaults to ""
	// (volume's root). SubPathExpr and SubPath are mutually exclusive. +optional
	SubPathExpr   string `protobuf:"bytes,6,opt,name=sub_path_expr,json=subPathExpr,proto3" json:"sub_path_expr,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VolumeMount) Reset() {
	*x = VolumeMount{}
	mi := &file_common_k8s_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VolumeMount) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VolumeMount) ProtoMessage() {}

func (x *VolumeMount) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VolumeMount.ProtoReflect.Descriptor instead.
func (*VolumeMount) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{30}
}

func (x *VolumeMount) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VolumeMount) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

func (x *VolumeMount) GetMountPath() string {
	if x != nil {
		return x.MountPath
	}
	return ""
}

func (x *VolumeMount) GetSubPath() string {
	if x != nil {
		return x.SubPath
	}
	return ""
}

func (x *VolumeMount) GetMountPropagation() string {
	if x != nil {
		return x.MountPropagation
	}
	return ""
}

func (x *VolumeMount) GetSubPathExpr() string {
	if x != nil {
		return x.SubPathExpr
	}
	return ""
}

type Volume struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	Name  string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// Types that are valid to be assigned to VolumneSource:
	//
	//	*Volume_PersistentVolumeClaim
	//	*Volume_EmptyDir
	//	*Volume_Secret
	VolumneSource isVolume_VolumneSource `protobuf_oneof:"volumne_source"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Volume) Reset() {
	*x = Volume{}
	mi := &file_common_k8s_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Volume) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Volume) ProtoMessage() {}

func (x *Volume) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Volume.ProtoReflect.Descriptor instead.
func (*Volume) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{31}
}

func (x *Volume) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Volume) GetVolumneSource() isVolume_VolumneSource {
	if x != nil {
		return x.VolumneSource
	}
	return nil
}

func (x *Volume) GetPersistentVolumeClaim() *PersistentVolumeClaimVolumeSource {
	if x != nil {
		if x, ok := x.VolumneSource.(*Volume_PersistentVolumeClaim); ok {
			return x.PersistentVolumeClaim
		}
	}
	return nil
}

func (x *Volume) GetEmptyDir() *EmptyDirVolumeSource {
	if x != nil {
		if x, ok := x.VolumneSource.(*Volume_EmptyDir); ok {
			return x.EmptyDir
		}
	}
	return nil
}

func (x *Volume) GetSecret() *SecretVolumeSource {
	if x != nil {
		if x, ok := x.VolumneSource.(*Volume_Secret); ok {
			return x.Secret
		}
	}
	return nil
}

type isVolume_VolumneSource interface {
	isVolume_VolumneSource()
}

type Volume_PersistentVolumeClaim struct {
	PersistentVolumeClaim *PersistentVolumeClaimVolumeSource `protobuf:"bytes,2,opt,name=persistentVolumeClaim,proto3,oneof"`
}

type Volume_EmptyDir struct {
	EmptyDir *EmptyDirVolumeSource `protobuf:"bytes,3,opt,name=emptyDir,proto3,oneof"`
}

type Volume_Secret struct {
	Secret *SecretVolumeSource `protobuf:"bytes,4,opt,name=secret,proto3,oneof"`
}

func (*Volume_PersistentVolumeClaim) isVolume_VolumneSource() {}

func (*Volume_EmptyDir) isVolume_VolumneSource() {}

func (*Volume_Secret) isVolume_VolumneSource() {}

type PersistentVolumeClaimVolumeSource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// claimName is the name of a PersistentVolumeClaim in the same namespace as
	// the pod using this volume. More info:
	// https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
	ClaimName string `protobuf:"bytes,1,opt,name=claim_name,json=claimName,proto3" json:"claim_name,omitempty"`
	// readOnly Will force the ReadOnly setting in VolumeMounts.
	// Default false.
	// +opional
	ReadOnly      bool `protobuf:"varint,2,opt,name=read_only,json=readOnly,proto3" json:"read_only,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PersistentVolumeClaimVolumeSource) Reset() {
	*x = PersistentVolumeClaimVolumeSource{}
	mi := &file_common_k8s_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersistentVolumeClaimVolumeSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersistentVolumeClaimVolumeSource) ProtoMessage() {}

func (x *PersistentVolumeClaimVolumeSource) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersistentVolumeClaimVolumeSource.ProtoReflect.Descriptor instead.
func (*PersistentVolumeClaimVolumeSource) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{32}
}

func (x *PersistentVolumeClaimVolumeSource) GetClaimName() string {
	if x != nil {
		return x.ClaimName
	}
	return ""
}

func (x *PersistentVolumeClaimVolumeSource) GetReadOnly() bool {
	if x != nil {
		return x.ReadOnly
	}
	return false
}

type EmptyDirVolumeSource struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The medium field controls where emptyDir volumes are stored.
	// default value is "" which means it will be aligned with node config.
	Medium        string  `protobuf:"bytes,1,opt,name=medium,proto3" json:"medium,omitempty"`
	SizeLimit     *string `protobuf:"bytes,2,opt,name=size_limit,json=sizeLimit,proto3,oneof" json:"size_limit,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EmptyDirVolumeSource) Reset() {
	*x = EmptyDirVolumeSource{}
	mi := &file_common_k8s_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EmptyDirVolumeSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EmptyDirVolumeSource) ProtoMessage() {}

func (x *EmptyDirVolumeSource) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EmptyDirVolumeSource.ProtoReflect.Descriptor instead.
func (*EmptyDirVolumeSource) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{33}
}

func (x *EmptyDirVolumeSource) GetMedium() string {
	if x != nil {
		return x.Medium
	}
	return ""
}

func (x *EmptyDirVolumeSource) GetSizeLimit() string {
	if x != nil && x.SizeLimit != nil {
		return *x.SizeLimit
	}
	return ""
}

type SecretVolumeSource struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SecretName    string                 `protobuf:"bytes,1,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
	Items         []*KeyToPath           `protobuf:"bytes,2,rep,name=items,proto3" json:"items,omitempty"`
	DefaultMode   *int32                 `protobuf:"varint,3,opt,name=default_mode,json=defaultMode,proto3,oneof" json:"default_mode,omitempty"`
	IsOptional    *bool                  `protobuf:"varint,4,opt,name=is_optional,json=isOptional,proto3,oneof" json:"is_optional,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretVolumeSource) Reset() {
	*x = SecretVolumeSource{}
	mi := &file_common_k8s_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretVolumeSource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretVolumeSource) ProtoMessage() {}

func (x *SecretVolumeSource) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretVolumeSource.ProtoReflect.Descriptor instead.
func (*SecretVolumeSource) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{34}
}

func (x *SecretVolumeSource) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

func (x *SecretVolumeSource) GetItems() []*KeyToPath {
	if x != nil {
		return x.Items
	}
	return nil
}

func (x *SecretVolumeSource) GetDefaultMode() int32 {
	if x != nil && x.DefaultMode != nil {
		return *x.DefaultMode
	}
	return 0
}

func (x *SecretVolumeSource) GetIsOptional() bool {
	if x != nil && x.IsOptional != nil {
		return *x.IsOptional
	}
	return false
}

// KeyToPath maps a string key to a path within a volume.
type KeyToPath struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The key to project.
	Key string `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	// The relative path of the file to map the key to.
	// May not be an absolute path.
	// May not contain the path element '..'.
	// May not start with the string '..'.
	Path string `protobuf:"bytes,2,opt,name=path,proto3" json:"path,omitempty"`
	// Optional: mode bits to use on this file, should be a value between 0
	// and 0777. If not specified, the volume defaultMode will be used.
	// This might be in conflict with other options that affect the file
	// mode, like fsGroup, and the result can be other mode bits set.
	// +optional
	Mode          *int32 `protobuf:"varint,3,opt,name=mode,proto3,oneof" json:"mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *KeyToPath) Reset() {
	*x = KeyToPath{}
	mi := &file_common_k8s_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KeyToPath) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KeyToPath) ProtoMessage() {}

func (x *KeyToPath) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KeyToPath.ProtoReflect.Descriptor instead.
func (*KeyToPath) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{35}
}

func (x *KeyToPath) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

func (x *KeyToPath) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *KeyToPath) GetMode() int32 {
	if x != nil && x.Mode != nil {
		return *x.Mode
	}
	return 0
}

// EnvVar represents an environment variable present in a Container.
type EnvVar struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Required: Name of the environment variable.
	// When the RelaxedEnvironmentVariableValidation feature gate is disabled,
	// this must consist of alphabetic characters, digits, '_', '-', or '.', and
	// must not start with a digit. When the RelaxedEnvironmentVariableValidation
	// feature gate is enabled, this may contain any printable ASCII characters
	// except '='.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// using the previously defined environment variables in the container and
	// any service environment variables.  If a variable cannot be resolved,
	// the reference in the input string will be unchanged.  Double $$ are
	// reduced to a single $, which allows for escaping the $(VAR_NAME)
	// syntax: i.e. "$$(VAR_NAME)" will produce the string literal
	// "$(VAR_NAME)".  Escaped references will never be expanded,
	// regardless of whether the variable exists or not.
	Value         string `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnvVar) Reset() {
	*x = EnvVar{}
	mi := &file_common_k8s_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnvVar) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvVar) ProtoMessage() {}

func (x *EnvVar) ProtoReflect() protoreflect.Message {
	mi := &file_common_k8s_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvVar.ProtoReflect.Descriptor instead.
func (*EnvVar) Descriptor() ([]byte, []int) {
	return file_common_k8s_proto_rawDescGZIP(), []int{36}
}

func (x *EnvVar) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EnvVar) GetValue() string {
	if x != nil {
		return x.Value
	}
	return ""
}

var File_common_k8s_proto protoreflect.FileDescriptor

var file_common_k8s_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x22, 0x9e,
	0x02, 0x0a, 0x09, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x1c, 0x0a, 0x09,
	0x69, 0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x09, 0x69, 0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x33, 0x0a, 0x04, 0x64, 0x61,
	0x74, 0x61, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e,
	0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12,
	0x46, 0x0a, 0x0b, 0x62, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x2e, 0x42, 0x69, 0x6e, 0x61,
	0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x62, 0x69, 0x6e,
	0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3d, 0x0a, 0x0f, 0x42, 0x69, 0x6e, 0x61, 0x72, 0x79, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0xa9, 0x02, 0x0a, 0x06, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x69, 0x6d,
	0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69,
	0x6d, 0x6d, 0x75, 0x74, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x30, 0x0a, 0x04, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x2e, 0x44, 0x61, 0x74, 0x61, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x64, 0x61, 0x74, 0x61, 0x12, 0x43, 0x0a, 0x0b, 0x73, 0x74,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x0a, 0x73, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x12,
	0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3d, 0x0a, 0x0f,
	0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x44, 0x61, 0x74, 0x61, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x42, 0x0a, 0x0e, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22,
	0x9f, 0x01, 0x0a, 0x0b, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x35, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x48, 0x65,
	0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x22, 0xe3, 0x01, 0x0a, 0x0a, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x65, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x61, 0x69, 0x6e, 0x74, 0x45, 0x66, 0x66, 0x65, 0x63,
	0x74, 0x52, 0x06, 0x65, 0x66, 0x66, 0x65, 0x63, 0x74, 0x12, 0x3a, 0x0a, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65,
	0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x2c, 0x0a, 0x0f, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x03, 0x48, 0x00,
	0x52, 0x0e, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x65, 0x63, 0x73,
	0x88, 0x01, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x73, 0x65, 0x63, 0x73, 0x22, 0xce, 0x01, 0x0a, 0x08, 0x41, 0x66, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x79, 0x12, 0x3d, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x61, 0x66, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x41, 0x66, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x0c, 0x6e, 0x6f, 0x64, 0x65, 0x41, 0x66, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x79, 0x12, 0x3a, 0x0a, 0x0c, 0x70, 0x6f, 0x64, 0x5f, 0x61, 0x66, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x79, 0x52, 0x0b, 0x70, 0x6f, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x12,
	0x47, 0x0a, 0x11, 0x70, 0x6f, 0x64, 0x5f, 0x61, 0x6e, 0x74, 0x69, 0x5f, 0x61, 0x66, 0x66, 0x69,
	0x6e, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x41, 0x6e, 0x74, 0x69, 0x41,
	0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x0f, 0x70, 0x6f, 0x64, 0x41, 0x6e, 0x74, 0x69,
	0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x22, 0xab, 0x02, 0x0a, 0x0c, 0x4e, 0x6f, 0x64,
	0x65, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x85, 0x01, 0x0a, 0x33, 0x72, 0x65,
	0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63,
	0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64,
	0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e,
	0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x67, 0x6e, 0x6f, 0x72,
	0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x92, 0x01, 0x0a, 0x34, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x5f,
	0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x72,
	0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x2f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65, 0x64,
	0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67,
	0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x65,
	0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x5c, 0x0a, 0x0c, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x4c, 0x0a, 0x13, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x72,
	0x6d, 0x52, 0x11, 0x6e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x54,
	0x65, 0x72, 0x6d, 0x73, 0x22, 0x6f, 0x0a, 0x17, 0x50, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x54, 0x65, 0x72, 0x6d, 0x12,
	0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12, 0x3c, 0x0a, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x0a, 0x70, 0x72, 0x65, 0x66, 0x65,
	0x72, 0x65, 0x6e, 0x63, 0x65, 0x22, 0xac, 0x01, 0x0a, 0x10, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x50, 0x0a, 0x11, 0x6d, 0x61,
	0x74, 0x63, 0x68, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x6d, 0x61, 0x74, 0x63,
	0x68, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x46, 0x0a, 0x0c,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x17, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b,
	0x65, 0x79, 0x12, 0x3c, 0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x12, 0x16, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0xad, 0x02, 0x0a, 0x0b, 0x50, 0x6f, 0x64,
	0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x88, 0x01, 0x0a, 0x33, 0x72, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x5f,
	0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x54,
	0x65, 0x72, 0x6d, 0x52, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72,
	0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x67, 0x6e,
	0x6f, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x92, 0x01, 0x0a, 0x34, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72, 0x65,
	0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c,
	0x69, 0x6e, 0x67, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x2f, 0x70, 0x72, 0x65, 0x66, 0x65, 0x72, 0x72,
	0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69,
	0x6e, 0x67, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x45,
	0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xb1, 0x02, 0x0a, 0x0f, 0x50, 0x6f, 0x64,
	0x41, 0x6e, 0x74, 0x69, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x12, 0x88, 0x01, 0x0a,
	0x33, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x73, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x67, 0x6e, 0x6f,
	0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x2e, 0x72, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65,
	0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e,
	0x67, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x45, 0x78,
	0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x92, 0x01, 0x0a, 0x34, 0x70, 0x72, 0x65, 0x66,
	0x65, 0x72, 0x72, 0x65, 0x64, 0x5f, 0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73, 0x63, 0x68,
	0x65, 0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x5f,
	0x64, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x65, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x41,
	0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x2f, 0x70, 0x72, 0x65,
	0x66, 0x65, 0x72, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x63, 0x68, 0x65,
	0x64, 0x75, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x67, 0x6e, 0x6f, 0x72, 0x65, 0x64, 0x44, 0x75, 0x72,
	0x69, 0x6e, 0x67, 0x45, 0x78, 0x65, 0x63, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe0, 0x01, 0x0a,
	0x0f, 0x50, 0x6f, 0x64, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d,
	0x12, 0x40, 0x0a, 0x0e, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x52, 0x0d, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x12, 0x1e, 0x0a, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f, 0x67, 0x79, 0x5f, 0x6b,
	0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x74, 0x6f, 0x70, 0x6f, 0x6c, 0x6f,
	0x67, 0x79, 0x4b, 0x65, 0x79, 0x12, 0x48, 0x0a, 0x12, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x11, 0x6e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x22,
	0x7a, 0x0a, 0x17, 0x57, 0x65, 0x69, 0x67, 0x68, 0x74, 0x65, 0x64, 0x50, 0x6f, 0x64, 0x41, 0x66,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65,
	0x69, 0x67, 0x68, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x12, 0x47, 0x0a, 0x11, 0x70, 0x6f, 0x64, 0x5f, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69,
	0x74, 0x79, 0x5f, 0x74, 0x65, 0x72, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x41, 0x66,
	0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x52, 0x0f, 0x70, 0x6f, 0x64, 0x41,
	0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x54, 0x65, 0x72, 0x6d, 0x22, 0xf1, 0x01, 0x0a, 0x0d,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x4d, 0x0a,
	0x0c, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x2e, 0x4d,
	0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0b, 0x6d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x51, 0x0a, 0x11,
	0x6d, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x10, 0x6d,
	0x61, 0x74, 0x63, 0x68, 0x45, 0x78, 0x70, 0x72, 0x65, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x1a,
	0x3e, 0x0a, 0x10, 0x4d, 0x61, 0x74, 0x63, 0x68, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x83, 0x01, 0x0a, 0x18, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x3d,
	0x0a, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61,
	0x74, 0x6f, 0x72, 0x52, 0x08, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x16, 0x0a,
	0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x22, 0x9e, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x63, 0x70, 0x75, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x70, 0x75, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x70, 0x75, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x63, 0x70, 0x75, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x12, 0x25, 0x0a, 0x0e,
	0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x6d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x5f, 0x6c, 0x69,
	0x6d, 0x69, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6d, 0x65, 0x6d, 0x6f, 0x72,
	0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x22, 0xc2, 0x01, 0x0a, 0x03, 0x50, 0x6f, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x33, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x50, 0x6f, 0x64, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x37, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x50, 0x68,
	0x61, 0x73, 0x65, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x50, 0x68, 0x61, 0x73, 0x65,
	0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbc, 0x01, 0x0a, 0x0b,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x2d, 0x0a, 0x05, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x50,
	0x6f, 0x72, 0x74, 0x52, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x41, 0x0a, 0x08, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a, 0x3b, 0x0a,
	0x0d, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x35, 0x0a, 0x0b, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x22, 0x90, 0x02, 0x0a, 0x11, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3c, 0x0a, 0x0c, 0x70, 0x6f, 0x64, 0x5f, 0x73,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x6f, 0x64, 0x53, 0x65, 0x6c,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x3e, 0x0a, 0x07, 0x69, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x07, 0x69, 0x6e,
	0x67, 0x72, 0x65, 0x73, 0x73, 0x12, 0x3b, 0x0a, 0x06, 0x65, 0x67, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x45, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x06, 0x65, 0x67, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x40, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54,
	0x79, 0x70, 0x65, 0x73, 0x22, 0x7d, 0x0a, 0x17, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x45, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x33, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x05, 0x70,
	0x6f, 0x72, 0x74, 0x73, 0x12, 0x2d, 0x0a, 0x02, 0x74, 0x6f, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x65, 0x65, 0x72, 0x52,
	0x02, 0x74, 0x6f, 0x22, 0x82, 0x01, 0x0a, 0x18, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x49, 0x6e, 0x67, 0x72, 0x65, 0x73, 0x73, 0x52, 0x75, 0x6c, 0x65,
	0x12, 0x33, 0x0a, 0x05, 0x70, 0x6f, 0x72, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x52, 0x05,
	0x70, 0x6f, 0x72, 0x74, 0x73, 0x12, 0x31, 0x0a, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x65,
	0x65, 0x72, 0x52, 0x04, 0x66, 0x72, 0x6f, 0x6d, 0x22, 0x93, 0x01, 0x0a, 0x11, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x37,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x2b, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x49, 0x6e, 0x74, 0x4f, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x04,
	0x70, 0x6f, 0x72, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x72, 0x74, 0x22, 0xc8,
	0x01, 0x0a, 0x11, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x50, 0x65, 0x65, 0x72, 0x12, 0x3b, 0x0a, 0x0b, 0x70, 0x6f, 0x64, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x52, 0x0b, 0x70, 0x6f, 0x64, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x12, 0x47, 0x0a, 0x11, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x11, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x2d, 0x0a, 0x07, 0x69, 0x70,
	0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x13, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x49, 0x50, 0x42, 0x6c, 0x6f, 0x63, 0x6b,
	0x52, 0x07, 0x69, 0x70, 0x42, 0x6c, 0x6f, 0x63, 0x6b, 0x22, 0x35, 0x0a, 0x07, 0x49, 0x50, 0x42,
	0x6c, 0x6f, 0x63, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x64, 0x72, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x78, 0x63, 0x65,
	0x70, 0x74, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x65, 0x78, 0x63, 0x65, 0x70, 0x74,
	0x22, 0x61, 0x0a, 0x0b, 0x49, 0x6e, 0x74, 0x4f, 0x72, 0x53, 0x74, 0x72, 0x69, 0x6e, 0x67, 0x12,
	0x1c, 0x0a, 0x07, 0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05,
	0x48, 0x00, 0x52, 0x06, 0x69, 0x6e, 0x74, 0x56, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x12, 0x1c, 0x0a,
	0x07, 0x73, 0x74, 0x72, 0x5f, 0x76, 0x61, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01,
	0x52, 0x06, 0x73, 0x74, 0x72, 0x56, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0a, 0x0a, 0x08, 0x5f,
	0x69, 0x6e, 0x74, 0x5f, 0x76, 0x61, 0x6c, 0x42, 0x0a, 0x0a, 0x08, 0x5f, 0x73, 0x74, 0x72, 0x5f,
	0x76, 0x61, 0x6c, 0x22, 0xa5, 0x02, 0x0a, 0x19, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x3c, 0x0a, 0x0c, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6d, 0x6f, 0x64, 0x65,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x56, 0x43, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x6f,
	0x64, 0x65, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x73, 0x12,
	0x35, 0x0a, 0x08, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c,
	0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x52, 0x08, 0x73, 0x65,
	0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x12, 0x44, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x1f, 0x0a, 0x0b,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a,
	0x12, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x73, 0x73, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x74, 0x6f, 0x72, 0x61,
	0x67, 0x65, 0x43, 0x6c, 0x61, 0x73, 0x73, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x45, 0x0a, 0x1a, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x5f, 0x72, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x22, 0xc9, 0x01, 0x0a, 0x0b, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x6f, 0x75,
	0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f,
	0x6e, 0x6c, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f,
	0x6e, 0x6c, 0x79, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x74,
	0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50, 0x61,
	0x74, 0x68, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x12, 0x2b, 0x0a,
	0x11, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x61, 0x67, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x50,
	0x72, 0x6f, 0x70, 0x61, 0x67, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22, 0x0a, 0x0d, 0x73, 0x75,
	0x62, 0x5f, 0x70, 0x61, 0x74, 0x68, 0x5f, 0x65, 0x78, 0x70, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x75, 0x62, 0x50, 0x61, 0x74, 0x68, 0x45, 0x78, 0x70, 0x72, 0x22, 0x8f,
	0x02, 0x0a, 0x06, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x65, 0x0a,
	0x15, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x15, 0x70,
	0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43,
	0x6c, 0x61, 0x69, 0x6d, 0x12, 0x3e, 0x0a, 0x08, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x69, 0x72,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x69, 0x72, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x08, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x44, 0x69, 0x72, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x48, 0x00, 0x52, 0x06, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x42, 0x10,
	0x0a, 0x0e, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x6e, 0x65, 0x5f, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x22, 0x5f, 0x0a, 0x21, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x61, 0x69, 0x6d,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x65, 0x61, 0x64, 0x5f, 0x6f, 0x6e, 0x6c,
	0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08, 0x72, 0x65, 0x61, 0x64, 0x4f, 0x6e, 0x6c,
	0x79, 0x22, 0x61, 0x0a, 0x14, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x44, 0x69, 0x72, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x6d, 0x65, 0x64,
	0x69, 0x75, 0x6d, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x6d, 0x65, 0x64, 0x69, 0x75,
	0x6d, 0x12, 0x22, 0x0a, 0x0a, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x09, 0x73, 0x69, 0x7a, 0x65, 0x4c, 0x69, 0x6d,
	0x69, 0x74, 0x88, 0x01, 0x01, 0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x5f, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x22, 0xd1, 0x01, 0x0a, 0x12, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x05,
	0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4b, 0x65, 0x79, 0x54, 0x6f, 0x50, 0x61,
	0x74, 0x68, 0x52, 0x05, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x26, 0x0a, 0x0c, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48,
	0x00, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x24, 0x0a, 0x0b, 0x69, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x08, 0x48, 0x01, 0x52, 0x0a, 0x69, 0x73, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x61, 0x6c, 0x88, 0x01, 0x01, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x65, 0x66, 0x61,
	0x75, 0x6c, 0x74, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x69, 0x73, 0x5f,
	0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x61, 0x6c, 0x22, 0x53, 0x0a, 0x09, 0x4b, 0x65, 0x79, 0x54,
	0x6f, 0x50, 0x61, 0x74, 0x68, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x70, 0x61, 0x74, 0x68, 0x12, 0x17, 0x0a, 0x04, 0x6d,
	0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x48, 0x00, 0x52, 0x04, 0x6d, 0x6f, 0x64,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x07, 0x0a, 0x05, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x32, 0x0a,
	0x06, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x2a, 0x7c, 0x0a, 0x11, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x50, 0x4c, 0x4f, 0x59, 0x45, 0x44, 0x10,
	0x01, 0x12, 0x10, 0x0a, 0x0c, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x49, 0x4e,
	0x47, 0x10, 0x02, 0x12, 0x0e, 0x0a, 0x0a, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47,
	0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0f,
	0x0a, 0x0b, 0x55, 0x4e, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4c, 0x4c, 0x45, 0x44, 0x10, 0x06, 0x2a,
	0xa0, 0x01, 0x0a, 0x0b, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x18, 0x0a, 0x14, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f,
	0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x53, 0x45, 0x52,
	0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x43, 0x4c, 0x55, 0x53, 0x54, 0x45,
	0x52, 0x5f, 0x49, 0x50, 0x10, 0x01, 0x12, 0x1a, 0x0a, 0x16, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43,
	0x45, 0x5f, 0x54, 0x59, 0x50, 0x45, 0x5f, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x50, 0x4f, 0x52, 0x54,
	0x10, 0x02, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x4c, 0x4f, 0x41, 0x44, 0x5f, 0x42, 0x41, 0x4c, 0x41, 0x4e, 0x43, 0x45, 0x52,
	0x10, 0x03, 0x12, 0x1e, 0x0a, 0x1a, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x54, 0x59,
	0x50, 0x45, 0x5f, 0x45, 0x58, 0x54, 0x45, 0x52, 0x4e, 0x41, 0x4c, 0x5f, 0x4e, 0x41, 0x4d, 0x45,
	0x10, 0x04, 0x2a, 0x87, 0x01, 0x0a, 0x0b, 0x54, 0x61, 0x69, 0x6e, 0x74, 0x45, 0x66, 0x66, 0x65,
	0x63, 0x74, 0x12, 0x18, 0x0a, 0x14, 0x54, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x46, 0x46, 0x45,
	0x43, 0x54, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1c, 0x0a, 0x18,
	0x54, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x46, 0x46, 0x45, 0x43, 0x54, 0x5f, 0x4e, 0x4f, 0x5f,
	0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x23, 0x0a, 0x1f, 0x54, 0x41,
	0x49, 0x4e, 0x54, 0x5f, 0x45, 0x46, 0x46, 0x45, 0x43, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x46, 0x45,
	0x52, 0x5f, 0x4e, 0x4f, 0x5f, 0x53, 0x43, 0x48, 0x45, 0x44, 0x55, 0x4c, 0x45, 0x10, 0x02, 0x12,
	0x1b, 0x0a, 0x17, 0x54, 0x41, 0x49, 0x4e, 0x54, 0x5f, 0x45, 0x46, 0x46, 0x45, 0x43, 0x54, 0x5f,
	0x4e, 0x4f, 0x5f, 0x45, 0x58, 0x45, 0x43, 0x55, 0x54, 0x45, 0x10, 0x03, 0x2a, 0x74, 0x0a, 0x12,
	0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x12, 0x1f, 0x0a, 0x1b, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f, 0x4e,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54,
	0x53, 0x10, 0x01, 0x12, 0x1d, 0x0a, 0x19, 0x54, 0x4f, 0x4c, 0x45, 0x52, 0x41, 0x54, 0x49, 0x4f,
	0x4e, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x51, 0x55, 0x41, 0x4c,
	0x10, 0x02, 0x2a, 0x88, 0x02, 0x0a, 0x14, 0x4e, 0x6f, 0x64, 0x65, 0x53, 0x65, 0x6c, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x4f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x22, 0x0a, 0x1e, 0x4e,
	0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x45,
	0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12,
	0x1d, 0x0a, 0x19, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x21,
	0x0a, 0x1d, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x10,
	0x02, 0x12, 0x21, 0x0a, 0x1d, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x53, 0x10, 0x03, 0x12, 0x29, 0x0a, 0x25, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c,
	0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x44,
	0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x04, 0x12,
	0x1d, 0x0a, 0x19, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52,
	0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x47, 0x54, 0x10, 0x05, 0x12, 0x1d,
	0x0a, 0x19, 0x4e, 0x4f, 0x44, 0x45, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f,
	0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4c, 0x54, 0x10, 0x06, 0x2a, 0xd0, 0x01,
	0x0a, 0x15, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x4f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x12, 0x23, 0x0a, 0x1f, 0x4c, 0x41, 0x42, 0x45, 0x4c,
	0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54,
	0x4f, 0x52, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x1e, 0x0a, 0x1a,
	0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x22, 0x0a, 0x1e,
	0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f,
	0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x49, 0x4e, 0x10, 0x02,
	0x12, 0x22, 0x0a, 0x1e, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x53, 0x45, 0x4c, 0x45, 0x43, 0x54,
	0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f, 0x45, 0x58, 0x49, 0x53,
	0x54, 0x53, 0x10, 0x03, 0x12, 0x2a, 0x0a, 0x26, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x53, 0x45,
	0x4c, 0x45, 0x43, 0x54, 0x4f, 0x52, 0x5f, 0x4f, 0x50, 0x45, 0x52, 0x41, 0x54, 0x4f, 0x52, 0x5f,
	0x44, 0x4f, 0x45, 0x53, 0x5f, 0x4e, 0x4f, 0x54, 0x5f, 0x45, 0x58, 0x49, 0x53, 0x54, 0x10, 0x04,
	0x2a, 0x7e, 0x0a, 0x08, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65, 0x12, 0x15, 0x0a, 0x11,
	0x50, 0x4f, 0x44, 0x5f, 0x50, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f, 0x44, 0x5f, 0x50, 0x48, 0x41, 0x53, 0x45,
	0x5f, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x15, 0x0a, 0x11, 0x50, 0x4f,
	0x44, 0x5f, 0x50, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x52, 0x55, 0x4e, 0x4e, 0x49, 0x4e, 0x47, 0x10,
	0x02, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x4f, 0x44, 0x5f, 0x50, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x53,
	0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x03, 0x12, 0x14, 0x0a, 0x10, 0x50, 0x4f,
	0x44, 0x5f, 0x50, 0x48, 0x41, 0x53, 0x45, 0x5f, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x04,
	0x2a, 0x7e, 0x0a, 0x0f, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x72, 0x6f, 0x74, 0x6f,
	0x63, 0x6f, 0x6c, 0x12, 0x1c, 0x0a, 0x18, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50,
	0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x52, 0x4f,
	0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x54, 0x43, 0x50, 0x10, 0x01, 0x12, 0x18, 0x0a, 0x14, 0x4e,
	0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f,
	0x55, 0x44, 0x50, 0x10, 0x02, 0x12, 0x19, 0x0a, 0x15, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c, 0x5f, 0x53, 0x43, 0x54, 0x50, 0x10, 0x03,
	0x2a, 0x66, 0x0a, 0x11, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x16, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b,
	0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10,
	0x00, 0x12, 0x1a, 0x0a, 0x16, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x4f, 0x4c,
	0x49, 0x43, 0x59, 0x5f, 0x49, 0x4e, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x19, 0x0a,
	0x15, 0x4e, 0x45, 0x54, 0x57, 0x4f, 0x52, 0x4b, 0x5f, 0x50, 0x4f, 0x4c, 0x49, 0x43, 0x59, 0x5f,
	0x45, 0x47, 0x52, 0x45, 0x53, 0x53, 0x10, 0x02, 0x2a, 0xc7, 0x01, 0x0a, 0x0d, 0x50, 0x56, 0x43,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x1b, 0x50, 0x56,
	0x43, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x23, 0x0a, 0x1f, 0x50,
	0x56, 0x43, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x52,
	0x45, 0x41, 0x44, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x43, 0x45, 0x10, 0x01,
	0x12, 0x23, 0x0a, 0x1f, 0x50, 0x56, 0x43, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4d,
	0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x4d,
	0x41, 0x4e, 0x59, 0x10, 0x02, 0x12, 0x22, 0x0a, 0x1e, 0x50, 0x56, 0x43, 0x5f, 0x41, 0x43, 0x43,
	0x45, 0x53, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x5f, 0x4f, 0x4e,
	0x4c, 0x59, 0x5f, 0x4d, 0x41, 0x4e, 0x59, 0x10, 0x03, 0x12, 0x27, 0x0a, 0x23, 0x50, 0x56, 0x43,
	0x5f, 0x41, 0x43, 0x43, 0x45, 0x53, 0x53, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x52, 0x45, 0x41,
	0x44, 0x5f, 0x57, 0x52, 0x49, 0x54, 0x45, 0x5f, 0x4f, 0x4e, 0x43, 0x45, 0x5f, 0x50, 0x4f, 0x44,
	0x10, 0x04, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e,
	0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
})

var (
	file_common_k8s_proto_rawDescOnce sync.Once
	file_common_k8s_proto_rawDescData []byte
)

func file_common_k8s_proto_rawDescGZIP() []byte {
	file_common_k8s_proto_rawDescOnce.Do(func() {
		file_common_k8s_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_k8s_proto_rawDesc), len(file_common_k8s_proto_rawDesc)))
	})
	return file_common_k8s_proto_rawDescData
}

var file_common_k8s_proto_enumTypes = make([]protoimpl.EnumInfo, 10)
var file_common_k8s_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_common_k8s_proto_goTypes = []any{
	(HelmReleaseStatus)(0),                    // 0: common.k8s.HelmReleaseStatus
	(ServiceType)(0),                          // 1: common.k8s.ServiceType
	(TaintEffect)(0),                          // 2: common.k8s.TaintEffect
	(TolerationOperator)(0),                   // 3: common.k8s.TolerationOperator
	(NodeSelectorOperator)(0),                 // 4: common.k8s.NodeSelectorOperator
	(LabelSelectorOperator)(0),                // 5: common.k8s.LabelSelectorOperator
	(PodPhase)(0),                             // 6: common.k8s.PodPhase
	(NetworkProtocol)(0),                      // 7: common.k8s.NetworkProtocol
	(NetworkPolicyType)(0),                    // 8: common.k8s.NetworkPolicyType
	(PVCAccessMode)(0),                        // 9: common.k8s.PVCAccessMode
	(*ConfigMap)(nil),                         // 10: common.k8s.ConfigMap
	(*Secret)(nil),                            // 11: common.k8s.Secret
	(*ServiceAccount)(nil),                    // 12: common.k8s.ServiceAccount
	(*HelmRelease)(nil),                       // 13: common.k8s.HelmRelease
	(*Toleration)(nil),                        // 14: common.k8s.Toleration
	(*Affinity)(nil),                          // 15: common.k8s.Affinity
	(*NodeAffinity)(nil),                      // 16: common.k8s.NodeAffinity
	(*NodeSelector)(nil),                      // 17: common.k8s.NodeSelector
	(*PreferredSchedulingTerm)(nil),           // 18: common.k8s.PreferredSchedulingTerm
	(*NodeSelectorTerm)(nil),                  // 19: common.k8s.NodeSelectorTerm
	(*NodeSelectorRequirement)(nil),           // 20: common.k8s.NodeSelectorRequirement
	(*PodAffinity)(nil),                       // 21: common.k8s.PodAffinity
	(*PodAntiAffinity)(nil),                   // 22: common.k8s.PodAntiAffinity
	(*PodAffinityTerm)(nil),                   // 23: common.k8s.PodAffinityTerm
	(*WeightedPodAffinityTerm)(nil),           // 24: common.k8s.WeightedPodAffinityTerm
	(*LabelSelector)(nil),                     // 25: common.k8s.LabelSelector
	(*LabelSelectorRequirement)(nil),          // 26: common.k8s.LabelSelectorRequirement
	(*ResourceRequirements)(nil),              // 27: common.k8s.ResourceRequirements
	(*Pod)(nil),                               // 28: common.k8s.Pod
	(*ServiceSpec)(nil),                       // 29: common.k8s.ServiceSpec
	(*ServicePort)(nil),                       // 30: common.k8s.ServicePort
	(*NetworkPolicySpec)(nil),                 // 31: common.k8s.NetworkPolicySpec
	(*NetworkPolicyEgressRule)(nil),           // 32: common.k8s.NetworkPolicyEgressRule
	(*NetworkPolicyIngressRule)(nil),          // 33: common.k8s.NetworkPolicyIngressRule
	(*NetworkPolicyPort)(nil),                 // 34: common.k8s.NetworkPolicyPort
	(*NetworkPolicyPeer)(nil),                 // 35: common.k8s.NetworkPolicyPeer
	(*IPBlock)(nil),                           // 36: common.k8s.IPBlock
	(*IntOrString)(nil),                       // 37: common.k8s.IntOrString
	(*PersistentVolumeClaimSpec)(nil),         // 38: common.k8s.PersistentVolumeClaimSpec
	(*VolumeResourceRequirements)(nil),        // 39: common.k8s.VolumeResourceRequirements
	(*VolumeMount)(nil),                       // 40: common.k8s.VolumeMount
	(*Volume)(nil),                            // 41: common.k8s.Volume
	(*PersistentVolumeClaimVolumeSource)(nil), // 42: common.k8s.PersistentVolumeClaimVolumeSource
	(*EmptyDirVolumeSource)(nil),              // 43: common.k8s.EmptyDirVolumeSource
	(*SecretVolumeSource)(nil),                // 44: common.k8s.SecretVolumeSource
	(*KeyToPath)(nil),                         // 45: common.k8s.KeyToPath
	(*EnvVar)(nil),                            // 46: common.k8s.EnvVar
	nil,                                       // 47: common.k8s.ConfigMap.DataEntry
	nil,                                       // 48: common.k8s.ConfigMap.BinaryDataEntry
	nil,                                       // 49: common.k8s.Secret.DataEntry
	nil,                                       // 50: common.k8s.Secret.StringDataEntry
	nil,                                       // 51: common.k8s.LabelSelector.MatchLabelsEntry
	nil,                                       // 52: common.k8s.Pod.LabelsEntry
	nil,                                       // 53: common.k8s.ServiceSpec.SelectorEntry
}
var file_common_k8s_proto_depIdxs = []int32{
	47, // 0: common.k8s.ConfigMap.data:type_name -> common.k8s.ConfigMap.DataEntry
	48, // 1: common.k8s.ConfigMap.binary_data:type_name -> common.k8s.ConfigMap.BinaryDataEntry
	49, // 2: common.k8s.Secret.data:type_name -> common.k8s.Secret.DataEntry
	50, // 3: common.k8s.Secret.string_data:type_name -> common.k8s.Secret.StringDataEntry
	0,  // 4: common.k8s.HelmRelease.status:type_name -> common.k8s.HelmReleaseStatus
	2,  // 5: common.k8s.Toleration.effect:type_name -> common.k8s.TaintEffect
	3,  // 6: common.k8s.Toleration.operator:type_name -> common.k8s.TolerationOperator
	16, // 7: common.k8s.Affinity.node_affinity:type_name -> common.k8s.NodeAffinity
	21, // 8: common.k8s.Affinity.pod_affinity:type_name -> common.k8s.PodAffinity
	22, // 9: common.k8s.Affinity.pod_anti_affinity:type_name -> common.k8s.PodAntiAffinity
	17, // 10: common.k8s.NodeAffinity.required_during_scheduling_ignored_during_execution:type_name -> common.k8s.NodeSelector
	18, // 11: common.k8s.NodeAffinity.preferred_during_scheduling_ignored_during_execution:type_name -> common.k8s.PreferredSchedulingTerm
	19, // 12: common.k8s.NodeSelector.node_selector_terms:type_name -> common.k8s.NodeSelectorTerm
	19, // 13: common.k8s.PreferredSchedulingTerm.preference:type_name -> common.k8s.NodeSelectorTerm
	20, // 14: common.k8s.NodeSelectorTerm.match_expressions:type_name -> common.k8s.NodeSelectorRequirement
	20, // 15: common.k8s.NodeSelectorTerm.match_fields:type_name -> common.k8s.NodeSelectorRequirement
	4,  // 16: common.k8s.NodeSelectorRequirement.operator:type_name -> common.k8s.NodeSelectorOperator
	23, // 17: common.k8s.PodAffinity.required_during_scheduling_ignored_during_execution:type_name -> common.k8s.PodAffinityTerm
	24, // 18: common.k8s.PodAffinity.preferred_during_scheduling_ignored_during_execution:type_name -> common.k8s.WeightedPodAffinityTerm
	23, // 19: common.k8s.PodAntiAffinity.required_during_scheduling_ignored_during_execution:type_name -> common.k8s.PodAffinityTerm
	24, // 20: common.k8s.PodAntiAffinity.preferred_during_scheduling_ignored_during_execution:type_name -> common.k8s.WeightedPodAffinityTerm
	25, // 21: common.k8s.PodAffinityTerm.label_selector:type_name -> common.k8s.LabelSelector
	25, // 22: common.k8s.PodAffinityTerm.namespace_selector:type_name -> common.k8s.LabelSelector
	23, // 23: common.k8s.WeightedPodAffinityTerm.pod_affinity_term:type_name -> common.k8s.PodAffinityTerm
	51, // 24: common.k8s.LabelSelector.match_labels:type_name -> common.k8s.LabelSelector.MatchLabelsEntry
	26, // 25: common.k8s.LabelSelector.match_expressions:type_name -> common.k8s.LabelSelectorRequirement
	5,  // 26: common.k8s.LabelSelectorRequirement.operator:type_name -> common.k8s.LabelSelectorOperator
	52, // 27: common.k8s.Pod.labels:type_name -> common.k8s.Pod.LabelsEntry
	6,  // 28: common.k8s.Pod.status_phase:type_name -> common.k8s.PodPhase
	30, // 29: common.k8s.ServiceSpec.ports:type_name -> common.k8s.ServicePort
	53, // 30: common.k8s.ServiceSpec.selector:type_name -> common.k8s.ServiceSpec.SelectorEntry
	25, // 31: common.k8s.NetworkPolicySpec.pod_selector:type_name -> common.k8s.LabelSelector
	33, // 32: common.k8s.NetworkPolicySpec.ingress:type_name -> common.k8s.NetworkPolicyIngressRule
	32, // 33: common.k8s.NetworkPolicySpec.egress:type_name -> common.k8s.NetworkPolicyEgressRule
	8,  // 34: common.k8s.NetworkPolicySpec.policy_types:type_name -> common.k8s.NetworkPolicyType
	34, // 35: common.k8s.NetworkPolicyEgressRule.ports:type_name -> common.k8s.NetworkPolicyPort
	35, // 36: common.k8s.NetworkPolicyEgressRule.to:type_name -> common.k8s.NetworkPolicyPeer
	34, // 37: common.k8s.NetworkPolicyIngressRule.ports:type_name -> common.k8s.NetworkPolicyPort
	35, // 38: common.k8s.NetworkPolicyIngressRule.from:type_name -> common.k8s.NetworkPolicyPeer
	7,  // 39: common.k8s.NetworkPolicyPort.protocol:type_name -> common.k8s.NetworkProtocol
	37, // 40: common.k8s.NetworkPolicyPort.port:type_name -> common.k8s.IntOrString
	25, // 41: common.k8s.NetworkPolicyPeer.podSelector:type_name -> common.k8s.LabelSelector
	25, // 42: common.k8s.NetworkPolicyPeer.namespaceSelector:type_name -> common.k8s.LabelSelector
	36, // 43: common.k8s.NetworkPolicyPeer.ipBlock:type_name -> common.k8s.IPBlock
	9,  // 44: common.k8s.PersistentVolumeClaimSpec.access_modes:type_name -> common.k8s.PVCAccessMode
	25, // 45: common.k8s.PersistentVolumeClaimSpec.selector:type_name -> common.k8s.LabelSelector
	39, // 46: common.k8s.PersistentVolumeClaimSpec.resources:type_name -> common.k8s.VolumeResourceRequirements
	42, // 47: common.k8s.Volume.persistentVolumeClaim:type_name -> common.k8s.PersistentVolumeClaimVolumeSource
	43, // 48: common.k8s.Volume.emptyDir:type_name -> common.k8s.EmptyDirVolumeSource
	44, // 49: common.k8s.Volume.secret:type_name -> common.k8s.SecretVolumeSource
	45, // 50: common.k8s.SecretVolumeSource.items:type_name -> common.k8s.KeyToPath
	51, // [51:51] is the sub-list for method output_type
	51, // [51:51] is the sub-list for method input_type
	51, // [51:51] is the sub-list for extension type_name
	51, // [51:51] is the sub-list for extension extendee
	0,  // [0:51] is the sub-list for field type_name
}

func init() { file_common_k8s_proto_init() }
func file_common_k8s_proto_init() {
	if File_common_k8s_proto != nil {
		return
	}
	file_common_k8s_proto_msgTypes[4].OneofWrappers = []any{}
	file_common_k8s_proto_msgTypes[27].OneofWrappers = []any{}
	file_common_k8s_proto_msgTypes[31].OneofWrappers = []any{
		(*Volume_PersistentVolumeClaim)(nil),
		(*Volume_EmptyDir)(nil),
		(*Volume_Secret)(nil),
	}
	file_common_k8s_proto_msgTypes[33].OneofWrappers = []any{}
	file_common_k8s_proto_msgTypes[34].OneofWrappers = []any{}
	file_common_k8s_proto_msgTypes[35].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_k8s_proto_rawDesc), len(file_common_k8s_proto_rawDesc)),
			NumEnums:      10,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_k8s_proto_goTypes,
		DependencyIndexes: file_common_k8s_proto_depIdxs,
		EnumInfos:         file_common_k8s_proto_enumTypes,
		MessageInfos:      file_common_k8s_proto_msgTypes,
	}.Build()
	File_common_k8s_proto = out.File
	file_common_k8s_proto_goTypes = nil
	file_common_k8s_proto_depIdxs = nil
}
