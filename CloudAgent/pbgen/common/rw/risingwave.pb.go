// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: common/risingwave.proto

package rw

import (
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PersistentVolumeClaimRetentionPolicyType int32

const (
	PersistentVolumeClaimRetentionPolicyType_UNKNOWN PersistentVolumeClaimRetentionPolicyType = 0
	PersistentVolumeClaimRetentionPolicyType_DELETE  PersistentVolumeClaimRetentionPolicyType = 1
	PersistentVolumeClaimRetentionPolicyType_RETAIN  PersistentVolumeClaimRetentionPolicyType = 2
)

// Enum value maps for PersistentVolumeClaimRetentionPolicyType.
var (
	PersistentVolumeClaimRetentionPolicyType_name = map[int32]string{
		0: "UNKNOWN",
		1: "DELETE",
		2: "RETAIN",
	}
	PersistentVolumeClaimRetentionPolicyType_value = map[string]int32{
		"UNKNOWN": 0,
		"DELETE":  1,
		"RETAIN":  2,
	}
)

func (x PersistentVolumeClaimRetentionPolicyType) Enum() *PersistentVolumeClaimRetentionPolicyType {
	p := new(PersistentVolumeClaimRetentionPolicyType)
	*p = x
	return p
}

func (x PersistentVolumeClaimRetentionPolicyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PersistentVolumeClaimRetentionPolicyType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_risingwave_proto_enumTypes[0].Descriptor()
}

func (PersistentVolumeClaimRetentionPolicyType) Type() protoreflect.EnumType {
	return &file_common_risingwave_proto_enumTypes[0]
}

func (x PersistentVolumeClaimRetentionPolicyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PersistentVolumeClaimRetentionPolicyType.Descriptor instead.
func (PersistentVolumeClaimRetentionPolicyType) EnumDescriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{0}
}

type UpgradeStrategyType int32

const (
	UpgradeStrategyType_UNKNOWN_STRATEGY     UpgradeStrategyType = 0
	UpgradeStrategyType_RECREATE             UpgradeStrategyType = 1
	UpgradeStrategyType_ROLLING_UPDATE       UpgradeStrategyType = 2
	UpgradeStrategyType_IN_PLACE_IF_POSSIBLE UpgradeStrategyType = 3
	UpgradeStrategyType_IN_PLACE_ONLY        UpgradeStrategyType = 4
)

// Enum value maps for UpgradeStrategyType.
var (
	UpgradeStrategyType_name = map[int32]string{
		0: "UNKNOWN_STRATEGY",
		1: "RECREATE",
		2: "ROLLING_UPDATE",
		3: "IN_PLACE_IF_POSSIBLE",
		4: "IN_PLACE_ONLY",
	}
	UpgradeStrategyType_value = map[string]int32{
		"UNKNOWN_STRATEGY":     0,
		"RECREATE":             1,
		"ROLLING_UPDATE":       2,
		"IN_PLACE_IF_POSSIBLE": 3,
		"IN_PLACE_ONLY":        4,
	}
)

func (x UpgradeStrategyType) Enum() *UpgradeStrategyType {
	p := new(UpgradeStrategyType)
	*p = x
	return p
}

func (x UpgradeStrategyType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (UpgradeStrategyType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_risingwave_proto_enumTypes[1].Descriptor()
}

func (UpgradeStrategyType) Type() protoreflect.EnumType {
	return &file_common_risingwave_proto_enumTypes[1]
}

func (x UpgradeStrategyType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use UpgradeStrategyType.Descriptor instead.
func (UpgradeStrategyType) EnumDescriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{1}
}

type RisingWaveStatusCode int32

const (
	RisingWaveStatusCode_UNKNOWN_RW_STATUS       RisingWaveStatusCode = 0
	RisingWaveStatusCode_RW_READY                RisingWaveStatusCode = 1
	RisingWaveStatusCode_RW_NOT_READY            RisingWaveStatusCode = 2
	RisingWaveStatusCode_RW_UPGRADING            RisingWaveStatusCode = 3
	RisingWaveStatusCode_RW_WAIT_FOR_OBSERVATION RisingWaveStatusCode = 4
)

// Enum value maps for RisingWaveStatusCode.
var (
	RisingWaveStatusCode_name = map[int32]string{
		0: "UNKNOWN_RW_STATUS",
		1: "RW_READY",
		2: "RW_NOT_READY",
		3: "RW_UPGRADING",
		4: "RW_WAIT_FOR_OBSERVATION",
	}
	RisingWaveStatusCode_value = map[string]int32{
		"UNKNOWN_RW_STATUS":       0,
		"RW_READY":                1,
		"RW_NOT_READY":            2,
		"RW_UPGRADING":            3,
		"RW_WAIT_FOR_OBSERVATION": 4,
	}
)

func (x RisingWaveStatusCode) Enum() *RisingWaveStatusCode {
	p := new(RisingWaveStatusCode)
	*p = x
	return p
}

func (x RisingWaveStatusCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (RisingWaveStatusCode) Descriptor() protoreflect.EnumDescriptor {
	return file_common_risingwave_proto_enumTypes[2].Descriptor()
}

func (RisingWaveStatusCode) Type() protoreflect.EnumType {
	return &file_common_risingwave_proto_enumTypes[2]
}

func (x RisingWaveStatusCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use RisingWaveStatusCode.Descriptor instead.
func (RisingWaveStatusCode) EnumDescriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{2}
}

type ComponentType int32

const (
	ComponentType_UNKNOWN_COMPONENT ComponentType = 0
	ComponentType_META              ComponentType = 1
	ComponentType_FRONTEND          ComponentType = 2
	ComponentType_COMPUTE           ComponentType = 3
	ComponentType_COMPACTOR         ComponentType = 4
	ComponentType_STANDALONE        ComponentType = 5
)

// Enum value maps for ComponentType.
var (
	ComponentType_name = map[int32]string{
		0: "UNKNOWN_COMPONENT",
		1: "META",
		2: "FRONTEND",
		3: "COMPUTE",
		4: "COMPACTOR",
		5: "STANDALONE",
	}
	ComponentType_value = map[string]int32{
		"UNKNOWN_COMPONENT": 0,
		"META":              1,
		"FRONTEND":          2,
		"COMPUTE":           3,
		"COMPACTOR":         4,
		"STANDALONE":        5,
	}
)

func (x ComponentType) Enum() *ComponentType {
	p := new(ComponentType)
	*p = x
	return p
}

func (x ComponentType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ComponentType) Descriptor() protoreflect.EnumDescriptor {
	return file_common_risingwave_proto_enumTypes[3].Descriptor()
}

func (ComponentType) Type() protoreflect.EnumType {
	return &file_common_risingwave_proto_enumTypes[3]
}

func (x ComponentType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ComponentType.Descriptor instead.
func (ComponentType) EnumDescriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{3}
}

type RisingWaveSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// RisingWave container image info
	Image string `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	// Whether to create the service monitor resources.
	EnableDefaultServiceMonitor bool `protobuf:"varint,2,opt,name=enable_default_service_monitor,json=enableDefaultServiceMonitor,proto3" json:"enable_default_service_monitor,omitempty"`
	// Specs for RW state storage
	StateStore *StateStoreSpec `protobuf:"bytes,3,opt,name=state_store,json=stateStore,proto3" json:"state_store,omitempty"`
	// Specs for RW meta storage
	MetaStoreSpec *MetaStoreSpec `protobuf:"bytes,4,opt,name=meta_store_spec,json=metaStoreSpec,proto3" json:"meta_store_spec,omitempty"`
	// Information about where to retrieve RisingWave configurations
	Config *Config `protobuf:"bytes,5,opt,name=config,proto3" json:"config,omitempty"`
	// How RW will expose its services.
	FrontendServiceType k8s.ServiceType `protobuf:"varint,6,opt,name=frontend_service_type,json=frontendServiceType,proto3,enum=common.k8s.ServiceType" json:"frontend_service_type,omitempty"`
	// Specs of RW components
	Components *ComponentsSpec `protobuf:"bytes,7,opt,name=components,proto3" json:"components,omitempty"`
	// Flag to indicate if full kubernetes address should be enabled for
	// components. If enabled, address will be [<pod>.]<service>.<namespace>.svc.
	// Otherwise, it will be [<pod>.]<service>. Enabling this flag on existing
	// RisingWave will cause incompatibility.
	EnableFullKubernetesAddr bool `protobuf:"varint,8,opt,name=enable_full_kubernetes_addr,json=enableFullKubernetesAddr,proto3" json:"enable_full_kubernetes_addr,omitempty"`
	// Flag to control whether to deploy in standalone mode or distributed mode.
	// If standalone mode is used, spec.components will be ignored. Standalone
	// mode can be turned on/off dynamically.
	EnableStandaloneMode bool `protobuf:"varint,9,opt,name=enable_standalone_mode,json=enableStandaloneMode,proto3" json:"enable_standalone_mode,omitempty"`
	// Flag to control whether to enable embedded serving mode. If enabled, the
	// frontend nodes will be created
	// with embedded serving node enabled, and the compute nodes will serve
	// streaming workload only.
	EnableEmbeddedServingMode bool `protobuf:"varint,10,opt,name=enable_embedded_serving_mode,json=enableEmbeddedServingMode,proto3" json:"enable_embedded_serving_mode,omitempty"`
	// TODO: CLOUD-3324
	ComputeConfig *Config `protobuf:"bytes,11,opt,name=compute_config,json=computeConfig,proto3" json:"compute_config,omitempty"`
	// License key used to load license from secret
	// and unlock premium features of tenants.
	LicenseKey    *LicenseKey  `protobuf:"bytes,12,opt,name=license_key,json=licenseKey,proto3" json:"license_key,omitempty"`
	SecretStore   *SecretStore `protobuf:"bytes,13,opt,name=secret_store,json=secretStore,proto3" json:"secret_store,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RisingWaveSpec) Reset() {
	*x = RisingWaveSpec{}
	mi := &file_common_risingwave_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RisingWaveSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RisingWaveSpec) ProtoMessage() {}

func (x *RisingWaveSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RisingWaveSpec.ProtoReflect.Descriptor instead.
func (*RisingWaveSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{0}
}

func (x *RisingWaveSpec) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *RisingWaveSpec) GetEnableDefaultServiceMonitor() bool {
	if x != nil {
		return x.EnableDefaultServiceMonitor
	}
	return false
}

func (x *RisingWaveSpec) GetStateStore() *StateStoreSpec {
	if x != nil {
		return x.StateStore
	}
	return nil
}

func (x *RisingWaveSpec) GetMetaStoreSpec() *MetaStoreSpec {
	if x != nil {
		return x.MetaStoreSpec
	}
	return nil
}

func (x *RisingWaveSpec) GetConfig() *Config {
	if x != nil {
		return x.Config
	}
	return nil
}

func (x *RisingWaveSpec) GetFrontendServiceType() k8s.ServiceType {
	if x != nil {
		return x.FrontendServiceType
	}
	return k8s.ServiceType(0)
}

func (x *RisingWaveSpec) GetComponents() *ComponentsSpec {
	if x != nil {
		return x.Components
	}
	return nil
}

func (x *RisingWaveSpec) GetEnableFullKubernetesAddr() bool {
	if x != nil {
		return x.EnableFullKubernetesAddr
	}
	return false
}

func (x *RisingWaveSpec) GetEnableStandaloneMode() bool {
	if x != nil {
		return x.EnableStandaloneMode
	}
	return false
}

func (x *RisingWaveSpec) GetEnableEmbeddedServingMode() bool {
	if x != nil {
		return x.EnableEmbeddedServingMode
	}
	return false
}

func (x *RisingWaveSpec) GetComputeConfig() *Config {
	if x != nil {
		return x.ComputeConfig
	}
	return nil
}

func (x *RisingWaveSpec) GetLicenseKey() *LicenseKey {
	if x != nil {
		return x.LicenseKey
	}
	return nil
}

func (x *RisingWaveSpec) GetSecretStore() *SecretStore {
	if x != nil {
		return x.SecretStore
	}
	return nil
}

type StateStoreSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	DataDirectory string                 `protobuf:"bytes,1,opt,name=data_directory,json=dataDirectory,proto3" json:"data_directory,omitempty"`
	// Types that are valid to be assigned to Backend:
	//
	//	*StateStoreSpec_S3StateStore
	//	*StateStoreSpec_GcsStateStore
	//	*StateStoreSpec_AzblobStateStore
	//	*StateStoreSpec_MemoryStateStore
	//	*StateStoreSpec_LocalDiskStateStore
	Backend       isStateStoreSpec_Backend `protobuf_oneof:"backend"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StateStoreSpec) Reset() {
	*x = StateStoreSpec{}
	mi := &file_common_risingwave_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StateStoreSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateStoreSpec) ProtoMessage() {}

func (x *StateStoreSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateStoreSpec.ProtoReflect.Descriptor instead.
func (*StateStoreSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{1}
}

func (x *StateStoreSpec) GetDataDirectory() string {
	if x != nil {
		return x.DataDirectory
	}
	return ""
}

func (x *StateStoreSpec) GetBackend() isStateStoreSpec_Backend {
	if x != nil {
		return x.Backend
	}
	return nil
}

func (x *StateStoreSpec) GetS3StateStore() *StateStoreBackendS3 {
	if x != nil {
		if x, ok := x.Backend.(*StateStoreSpec_S3StateStore); ok {
			return x.S3StateStore
		}
	}
	return nil
}

func (x *StateStoreSpec) GetGcsStateStore() *StateStoreBackendGCS {
	if x != nil {
		if x, ok := x.Backend.(*StateStoreSpec_GcsStateStore); ok {
			return x.GcsStateStore
		}
	}
	return nil
}

func (x *StateStoreSpec) GetAzblobStateStore() *StateStoreBackendAzblob {
	if x != nil {
		if x, ok := x.Backend.(*StateStoreSpec_AzblobStateStore); ok {
			return x.AzblobStateStore
		}
	}
	return nil
}

func (x *StateStoreSpec) GetMemoryStateStore() *StateStoreBackendMemory {
	if x != nil {
		if x, ok := x.Backend.(*StateStoreSpec_MemoryStateStore); ok {
			return x.MemoryStateStore
		}
	}
	return nil
}

func (x *StateStoreSpec) GetLocalDiskStateStore() *StateStoreBackendLocalDisk {
	if x != nil {
		if x, ok := x.Backend.(*StateStoreSpec_LocalDiskStateStore); ok {
			return x.LocalDiskStateStore
		}
	}
	return nil
}

type isStateStoreSpec_Backend interface {
	isStateStoreSpec_Backend()
}

type StateStoreSpec_S3StateStore struct {
	S3StateStore *StateStoreBackendS3 `protobuf:"bytes,2,opt,name=s3_state_store,json=s3StateStore,proto3,oneof"`
}

type StateStoreSpec_GcsStateStore struct {
	GcsStateStore *StateStoreBackendGCS `protobuf:"bytes,3,opt,name=gcs_state_store,json=gcsStateStore,proto3,oneof"`
}

type StateStoreSpec_AzblobStateStore struct {
	AzblobStateStore *StateStoreBackendAzblob `protobuf:"bytes,6,opt,name=azblob_state_store,json=azblobStateStore,proto3,oneof"`
}

type StateStoreSpec_MemoryStateStore struct {
	MemoryStateStore *StateStoreBackendMemory `protobuf:"bytes,4,opt,name=memory_state_store,json=memoryStateStore,proto3,oneof"`
}

type StateStoreSpec_LocalDiskStateStore struct {
	LocalDiskStateStore *StateStoreBackendLocalDisk `protobuf:"bytes,5,opt,name=local_disk_state_store,json=localDiskStateStore,proto3,oneof"`
}

func (*StateStoreSpec_S3StateStore) isStateStoreSpec_Backend() {}

func (*StateStoreSpec_GcsStateStore) isStateStoreSpec_Backend() {}

func (*StateStoreSpec_AzblobStateStore) isStateStoreSpec_Backend() {}

func (*StateStoreSpec_MemoryStateStore) isStateStoreSpec_Backend() {}

func (*StateStoreSpec_LocalDiskStateStore) isStateStoreSpec_Backend() {}

// StateStoreBackendS3 contains spec of a S3 backed RisingWave state store. It's
// only a subset of the RisingWave CRD:
// - We assume cloud agent will handle S3 access through IAM role so credential
// fields are omitted.
// - We assume the cloud agent and the S3 bucket are in the same region so
// region field is omitted.
type StateStoreBackendS3 struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bucket        string                 `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	Region        string                 `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StateStoreBackendS3) Reset() {
	*x = StateStoreBackendS3{}
	mi := &file_common_risingwave_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StateStoreBackendS3) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateStoreBackendS3) ProtoMessage() {}

func (x *StateStoreBackendS3) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateStoreBackendS3.ProtoReflect.Descriptor instead.
func (*StateStoreBackendS3) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{2}
}

func (x *StateStoreBackendS3) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *StateStoreBackendS3) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

// StateStoreBackendGCS contains spec of a GCS backed RisingWave state store.
// It's only a subset of the RisingWave CRD:
// - We assume cloud agent will handle GCS access through web identity so
// credential fields are omitted.
type StateStoreBackendGCS struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Bucket        string                 `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StateStoreBackendGCS) Reset() {
	*x = StateStoreBackendGCS{}
	mi := &file_common_risingwave_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StateStoreBackendGCS) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateStoreBackendGCS) ProtoMessage() {}

func (x *StateStoreBackendGCS) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateStoreBackendGCS.ProtoReflect.Descriptor instead.
func (*StateStoreBackendGCS) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{3}
}

func (x *StateStoreBackendGCS) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

type StateStoreBackendAzblob struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Container     string                 `protobuf:"bytes,1,opt,name=container,proto3" json:"container,omitempty"`
	Endpoint      string                 `protobuf:"bytes,2,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StateStoreBackendAzblob) Reset() {
	*x = StateStoreBackendAzblob{}
	mi := &file_common_risingwave_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StateStoreBackendAzblob) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateStoreBackendAzblob) ProtoMessage() {}

func (x *StateStoreBackendAzblob) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateStoreBackendAzblob.ProtoReflect.Descriptor instead.
func (*StateStoreBackendAzblob) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{4}
}

func (x *StateStoreBackendAzblob) GetContainer() string {
	if x != nil {
		return x.Container
	}
	return ""
}

func (x *StateStoreBackendAzblob) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

type StateStoreBackendMemory struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StateStoreBackendMemory) Reset() {
	*x = StateStoreBackendMemory{}
	mi := &file_common_risingwave_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StateStoreBackendMemory) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateStoreBackendMemory) ProtoMessage() {}

func (x *StateStoreBackendMemory) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateStoreBackendMemory.ProtoReflect.Descriptor instead.
func (*StateStoreBackendMemory) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{5}
}

type StateStoreBackendLocalDisk struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Root          string                 `protobuf:"bytes,1,opt,name=root,proto3" json:"root,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StateStoreBackendLocalDisk) Reset() {
	*x = StateStoreBackendLocalDisk{}
	mi := &file_common_risingwave_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StateStoreBackendLocalDisk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StateStoreBackendLocalDisk) ProtoMessage() {}

func (x *StateStoreBackendLocalDisk) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StateStoreBackendLocalDisk.ProtoReflect.Descriptor instead.
func (*StateStoreBackendLocalDisk) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{6}
}

func (x *StateStoreBackendLocalDisk) GetRoot() string {
	if x != nil {
		return x.Root
	}
	return ""
}

type MetaStoreSpec struct {
	state             protoimpl.MessageState      `protogen:"open.v1"`
	EtcdBackend       *MetaStoreBackendEtcd       `protobuf:"bytes,1,opt,name=etcd_backend,json=etcdBackend,proto3" json:"etcd_backend,omitempty"`
	PostgresqlBackend *MetaStoreBackendPostgreSql `protobuf:"bytes,2,opt,name=postgresql_backend,json=postgresqlBackend,proto3" json:"postgresql_backend,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *MetaStoreSpec) Reset() {
	*x = MetaStoreSpec{}
	mi := &file_common_risingwave_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaStoreSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaStoreSpec) ProtoMessage() {}

func (x *MetaStoreSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaStoreSpec.ProtoReflect.Descriptor instead.
func (*MetaStoreSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{7}
}

func (x *MetaStoreSpec) GetEtcdBackend() *MetaStoreBackendEtcd {
	if x != nil {
		return x.EtcdBackend
	}
	return nil
}

func (x *MetaStoreSpec) GetPostgresqlBackend() *MetaStoreBackendPostgreSql {
	if x != nil {
		return x.PostgresqlBackend
	}
	return nil
}

type MetaStoreBackendEtcd struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Endpoint      string                 `protobuf:"bytes,1,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaStoreBackendEtcd) Reset() {
	*x = MetaStoreBackendEtcd{}
	mi := &file_common_risingwave_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaStoreBackendEtcd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaStoreBackendEtcd) ProtoMessage() {}

func (x *MetaStoreBackendEtcd) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaStoreBackendEtcd.ProtoReflect.Descriptor instead.
func (*MetaStoreBackendEtcd) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{8}
}

func (x *MetaStoreBackendEtcd) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

type MetaStoreBackendPostgreSql struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Database      string                 `protobuf:"bytes,3,opt,name=database,proto3" json:"database,omitempty"`
	Host          string                 `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	Port          uint32                 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Credentials   *PostgreSqlCredentials `protobuf:"bytes,4,opt,name=credentials,proto3" json:"credentials,omitempty"`
	Options       map[string]string      `protobuf:"bytes,5,rep,name=options,proto3" json:"options,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaStoreBackendPostgreSql) Reset() {
	*x = MetaStoreBackendPostgreSql{}
	mi := &file_common_risingwave_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaStoreBackendPostgreSql) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaStoreBackendPostgreSql) ProtoMessage() {}

func (x *MetaStoreBackendPostgreSql) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaStoreBackendPostgreSql.ProtoReflect.Descriptor instead.
func (*MetaStoreBackendPostgreSql) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{9}
}

func (x *MetaStoreBackendPostgreSql) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *MetaStoreBackendPostgreSql) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *MetaStoreBackendPostgreSql) GetPort() uint32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *MetaStoreBackendPostgreSql) GetCredentials() *PostgreSqlCredentials {
	if x != nil {
		return x.Credentials
	}
	return nil
}

func (x *MetaStoreBackendPostgreSql) GetOptions() map[string]string {
	if x != nil {
		return x.Options
	}
	return nil
}

type PostgreSqlCredentials struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SecretName     string                 `protobuf:"bytes,1,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
	UsernameKeyRef string                 `protobuf:"bytes,2,opt,name=username_key_ref,json=usernameKeyRef,proto3" json:"username_key_ref,omitempty"`
	PasswordKeyRef string                 `protobuf:"bytes,3,opt,name=password_key_ref,json=passwordKeyRef,proto3" json:"password_key_ref,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *PostgreSqlCredentials) Reset() {
	*x = PostgreSqlCredentials{}
	mi := &file_common_risingwave_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostgreSqlCredentials) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostgreSqlCredentials) ProtoMessage() {}

func (x *PostgreSqlCredentials) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostgreSqlCredentials.ProtoReflect.Descriptor instead.
func (*PostgreSqlCredentials) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{10}
}

func (x *PostgreSqlCredentials) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

func (x *PostgreSqlCredentials) GetUsernameKeyRef() string {
	if x != nil {
		return x.UsernameKeyRef
	}
	return ""
}

func (x *PostgreSqlCredentials) GetPasswordKeyRef() string {
	if x != nil {
		return x.PasswordKeyRef
	}
	return ""
}

type Config struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	NodeConfig    *NodeConfig            `protobuf:"bytes,1,opt,name=node_config,json=nodeConfig,proto3" json:"node_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_common_risingwave_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{11}
}

func (x *Config) GetNodeConfig() *NodeConfig {
	if x != nil {
		return x.NodeConfig
	}
	return nil
}

type NodeConfig struct {
	state                      protoimpl.MessageState      `protogen:"open.v1"`
	NodeConfigurationConfigMap *NodeConfigurationConfigMap `protobuf:"bytes,1,opt,name=node_configuration_config_map,json=nodeConfigurationConfigMap,proto3" json:"node_configuration_config_map,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *NodeConfig) Reset() {
	*x = NodeConfig{}
	mi := &file_common_risingwave_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeConfig) ProtoMessage() {}

func (x *NodeConfig) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeConfig.ProtoReflect.Descriptor instead.
func (*NodeConfig) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{12}
}

func (x *NodeConfig) GetNodeConfigurationConfigMap() *NodeConfigurationConfigMap {
	if x != nil {
		return x.NodeConfigurationConfigMap
	}
	return nil
}

type NodeConfigurationConfigMap struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeConfigurationConfigMap) Reset() {
	*x = NodeConfigurationConfigMap{}
	mi := &file_common_risingwave_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeConfigurationConfigMap) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeConfigurationConfigMap) ProtoMessage() {}

func (x *NodeConfigurationConfigMap) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeConfigurationConfigMap.ProtoReflect.Descriptor instead.
func (*NodeConfigurationConfigMap) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{13}
}

func (x *NodeConfigurationConfigMap) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeConfigurationConfigMap) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

type ComponentSpec struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LogLevel      string                 `protobuf:"bytes,1,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"`
	NodeGroups    []*NodeGroupSpec       `protobuf:"bytes,2,rep,name=node_groups,json=nodeGroups,proto3" json:"node_groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ComponentSpec) Reset() {
	*x = ComponentSpec{}
	mi := &file_common_risingwave_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentSpec) ProtoMessage() {}

func (x *ComponentSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentSpec.ProtoReflect.Descriptor instead.
func (*ComponentSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{14}
}

func (x *ComponentSpec) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *ComponentSpec) GetNodeGroups() []*NodeGroupSpec {
	if x != nil {
		return x.NodeGroups
	}
	return nil
}

type PersistentVolumeClaimPartialObjectMeta struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PersistentVolumeClaimPartialObjectMeta) Reset() {
	*x = PersistentVolumeClaimPartialObjectMeta{}
	mi := &file_common_risingwave_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersistentVolumeClaimPartialObjectMeta) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersistentVolumeClaimPartialObjectMeta) ProtoMessage() {}

func (x *PersistentVolumeClaimPartialObjectMeta) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersistentVolumeClaimPartialObjectMeta.ProtoReflect.Descriptor instead.
func (*PersistentVolumeClaimPartialObjectMeta) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{15}
}

func (x *PersistentVolumeClaimPartialObjectMeta) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type PersistentVolumeClaimRetentionPolicy struct {
	state         protoimpl.MessageState                   `protogen:"open.v1"`
	WhenDeleted   PersistentVolumeClaimRetentionPolicyType `protobuf:"varint,1,opt,name=when_deleted,json=whenDeleted,proto3,enum=common.rw.PersistentVolumeClaimRetentionPolicyType" json:"when_deleted,omitempty"`
	WhenScaled    PersistentVolumeClaimRetentionPolicyType `protobuf:"varint,2,opt,name=when_scaled,json=whenScaled,proto3,enum=common.rw.PersistentVolumeClaimRetentionPolicyType" json:"when_scaled,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PersistentVolumeClaimRetentionPolicy) Reset() {
	*x = PersistentVolumeClaimRetentionPolicy{}
	mi := &file_common_risingwave_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersistentVolumeClaimRetentionPolicy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersistentVolumeClaimRetentionPolicy) ProtoMessage() {}

func (x *PersistentVolumeClaimRetentionPolicy) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersistentVolumeClaimRetentionPolicy.ProtoReflect.Descriptor instead.
func (*PersistentVolumeClaimRetentionPolicy) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{16}
}

func (x *PersistentVolumeClaimRetentionPolicy) GetWhenDeleted() PersistentVolumeClaimRetentionPolicyType {
	if x != nil {
		return x.WhenDeleted
	}
	return PersistentVolumeClaimRetentionPolicyType_UNKNOWN
}

func (x *PersistentVolumeClaimRetentionPolicy) GetWhenScaled() PersistentVolumeClaimRetentionPolicyType {
	if x != nil {
		return x.WhenScaled
	}
	return PersistentVolumeClaimRetentionPolicyType_UNKNOWN
}

type PersistentVolumeClaim struct {
	state         protoimpl.MessageState                  `protogen:"open.v1"`
	Metadata      *PersistentVolumeClaimPartialObjectMeta `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Spec          *k8s.PersistentVolumeClaimSpec          `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PersistentVolumeClaim) Reset() {
	*x = PersistentVolumeClaim{}
	mi := &file_common_risingwave_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PersistentVolumeClaim) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PersistentVolumeClaim) ProtoMessage() {}

func (x *PersistentVolumeClaim) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PersistentVolumeClaim.ProtoReflect.Descriptor instead.
func (*PersistentVolumeClaim) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{17}
}

func (x *PersistentVolumeClaim) GetMetadata() *PersistentVolumeClaimPartialObjectMeta {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *PersistentVolumeClaim) GetSpec() *k8s.PersistentVolumeClaimSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

type NodeGroupSpec struct {
	state                                protoimpl.MessageState                `protogen:"open.v1"`
	Name                                 string                                `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Replicas                             uint32                                `protobuf:"varint,2,opt,name=replicas,proto3" json:"replicas,omitempty"`
	UpgradeStrategy                      *NodeGroupUpgradeStrategy             `protobuf:"bytes,3,opt,name=upgrade_strategy,json=upgradeStrategy,proto3" json:"upgrade_strategy,omitempty"`
	NodePodSpec                          *NodePodSpec                          `protobuf:"bytes,4,opt,name=node_pod_spec,json=nodePodSpec,proto3" json:"node_pod_spec,omitempty"`
	VolumeClaimTemplates                 []*PersistentVolumeClaim              `protobuf:"bytes,5,rep,name=volume_claim_templates,json=volumeClaimTemplates,proto3" json:"volume_claim_templates,omitempty"`
	PersistentVolumeClaimRetentionPolicy *PersistentVolumeClaimRetentionPolicy `protobuf:"bytes,6,opt,name=persistent_volume_claim_retention_policy,json=persistentVolumeClaimRetentionPolicy,proto3" json:"persistent_volume_claim_retention_policy,omitempty"`
	NodeConfig                           *NodeConfig                           `protobuf:"bytes,7,opt,name=node_config,json=nodeConfig,proto3" json:"node_config,omitempty"`
	unknownFields                        protoimpl.UnknownFields
	sizeCache                            protoimpl.SizeCache
}

func (x *NodeGroupSpec) Reset() {
	*x = NodeGroupSpec{}
	mi := &file_common_risingwave_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeGroupSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGroupSpec) ProtoMessage() {}

func (x *NodeGroupSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGroupSpec.ProtoReflect.Descriptor instead.
func (*NodeGroupSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{18}
}

func (x *NodeGroupSpec) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NodeGroupSpec) GetReplicas() uint32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *NodeGroupSpec) GetUpgradeStrategy() *NodeGroupUpgradeStrategy {
	if x != nil {
		return x.UpgradeStrategy
	}
	return nil
}

func (x *NodeGroupSpec) GetNodePodSpec() *NodePodSpec {
	if x != nil {
		return x.NodePodSpec
	}
	return nil
}

func (x *NodeGroupSpec) GetVolumeClaimTemplates() []*PersistentVolumeClaim {
	if x != nil {
		return x.VolumeClaimTemplates
	}
	return nil
}

func (x *NodeGroupSpec) GetPersistentVolumeClaimRetentionPolicy() *PersistentVolumeClaimRetentionPolicy {
	if x != nil {
		return x.PersistentVolumeClaimRetentionPolicy
	}
	return nil
}

func (x *NodeGroupSpec) GetNodeConfig() *NodeConfig {
	if x != nil {
		return x.NodeConfig
	}
	return nil
}

type NodePodSpec struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Tolerations    []*k8s.Toleration      `protobuf:"bytes,1,rep,name=tolerations,proto3" json:"tolerations,omitempty"`
	Affinity       *k8s.Affinity          `protobuf:"bytes,2,opt,name=affinity,proto3" json:"affinity,omitempty"`
	ContainerSpec  *NodePodContainerSpec  `protobuf:"bytes,3,opt,name=container_spec,json=containerSpec,proto3" json:"container_spec,omitempty"`
	ServiceAccount string                 `protobuf:"bytes,4,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	Labels         map[string]string      `protobuf:"bytes,5,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Annotations    map[string]string      `protobuf:"bytes,6,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Volumes        []*k8s.Volume          `protobuf:"bytes,7,rep,name=volumes,proto3" json:"volumes,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *NodePodSpec) Reset() {
	*x = NodePodSpec{}
	mi := &file_common_risingwave_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodePodSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePodSpec) ProtoMessage() {}

func (x *NodePodSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePodSpec.ProtoReflect.Descriptor instead.
func (*NodePodSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{19}
}

func (x *NodePodSpec) GetTolerations() []*k8s.Toleration {
	if x != nil {
		return x.Tolerations
	}
	return nil
}

func (x *NodePodSpec) GetAffinity() *k8s.Affinity {
	if x != nil {
		return x.Affinity
	}
	return nil
}

func (x *NodePodSpec) GetContainerSpec() *NodePodContainerSpec {
	if x != nil {
		return x.ContainerSpec
	}
	return nil
}

func (x *NodePodSpec) GetServiceAccount() string {
	if x != nil {
		return x.ServiceAccount
	}
	return ""
}

func (x *NodePodSpec) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *NodePodSpec) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

func (x *NodePodSpec) GetVolumes() []*k8s.Volume {
	if x != nil {
		return x.Volumes
	}
	return nil
}

type NodePodContainerSpec struct {
	state         protoimpl.MessageState    `protogen:"open.v1"`
	Resources     *k8s.ResourceRequirements `protobuf:"bytes,1,opt,name=resources,proto3" json:"resources,omitempty"`
	Envs          map[string]string         `protobuf:"bytes,2,rep,name=envs,proto3" json:"envs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	VolumeMounts  []*k8s.VolumeMount        `protobuf:"bytes,3,rep,name=volume_mounts,json=volumeMounts,proto3" json:"volume_mounts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodePodContainerSpec) Reset() {
	*x = NodePodContainerSpec{}
	mi := &file_common_risingwave_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodePodContainerSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodePodContainerSpec) ProtoMessage() {}

func (x *NodePodContainerSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodePodContainerSpec.ProtoReflect.Descriptor instead.
func (*NodePodContainerSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{20}
}

func (x *NodePodContainerSpec) GetResources() *k8s.ResourceRequirements {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *NodePodContainerSpec) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

func (x *NodePodContainerSpec) GetVolumeMounts() []*k8s.VolumeMount {
	if x != nil {
		return x.VolumeMounts
	}
	return nil
}

type NodeGroupUpgradeStrategy struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Type          UpgradeStrategyType    `protobuf:"varint,1,opt,name=type,proto3,enum=common.rw.UpgradeStrategyType" json:"type,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *NodeGroupUpgradeStrategy) Reset() {
	*x = NodeGroupUpgradeStrategy{}
	mi := &file_common_risingwave_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *NodeGroupUpgradeStrategy) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeGroupUpgradeStrategy) ProtoMessage() {}

func (x *NodeGroupUpgradeStrategy) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeGroupUpgradeStrategy.ProtoReflect.Descriptor instead.
func (*NodeGroupUpgradeStrategy) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{21}
}

func (x *NodeGroupUpgradeStrategy) GetType() UpgradeStrategyType {
	if x != nil {
		return x.Type
	}
	return UpgradeStrategyType_UNKNOWN_STRATEGY
}

type ComponentsSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Specs for meta nodes.
	MetaSpec *ComponentSpec `protobuf:"bytes,1,opt,name=meta_spec,json=metaSpec,proto3" json:"meta_spec,omitempty"`
	// Specs for frontend nodes.
	FrontendSpec *ComponentSpec `protobuf:"bytes,2,opt,name=frontend_spec,json=frontendSpec,proto3" json:"frontend_spec,omitempty"`
	// Specs for compute nodes.
	ComputeSpec *ComponentSpec `protobuf:"bytes,3,opt,name=compute_spec,json=computeSpec,proto3" json:"compute_spec,omitempty"`
	// Specs for compactor nodes.
	CompactorSpec *ComponentSpec `protobuf:"bytes,4,opt,name=compactor_spec,json=compactorSpec,proto3" json:"compactor_spec,omitempty"`
	// Specs for connector nodes.
	//
	// Deprecated: Marked as deprecated in common/risingwave.proto.
	ConnectorSpec *ComponentSpec `protobuf:"bytes,5,opt,name=connector_spec,json=connectorSpec,proto3" json:"connector_spec,omitempty"`
	// Spec for standalone component.
	StandaloneComponent *StandaloneSpec `protobuf:"bytes,6,opt,name=standalone_component,json=standaloneComponent,proto3" json:"standalone_component,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ComponentsSpec) Reset() {
	*x = ComponentsSpec{}
	mi := &file_common_risingwave_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ComponentsSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentsSpec) ProtoMessage() {}

func (x *ComponentsSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentsSpec.ProtoReflect.Descriptor instead.
func (*ComponentsSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{22}
}

func (x *ComponentsSpec) GetMetaSpec() *ComponentSpec {
	if x != nil {
		return x.MetaSpec
	}
	return nil
}

func (x *ComponentsSpec) GetFrontendSpec() *ComponentSpec {
	if x != nil {
		return x.FrontendSpec
	}
	return nil
}

func (x *ComponentsSpec) GetComputeSpec() *ComponentSpec {
	if x != nil {
		return x.ComputeSpec
	}
	return nil
}

func (x *ComponentsSpec) GetCompactorSpec() *ComponentSpec {
	if x != nil {
		return x.CompactorSpec
	}
	return nil
}

// Deprecated: Marked as deprecated in common/risingwave.proto.
func (x *ComponentsSpec) GetConnectorSpec() *ComponentSpec {
	if x != nil {
		return x.ConnectorSpec
	}
	return nil
}

func (x *ComponentsSpec) GetStandaloneComponent() *StandaloneSpec {
	if x != nil {
		return x.StandaloneComponent
	}
	return nil
}

type StandaloneSpec struct {
	state           protoimpl.MessageState    `protogen:"open.v1"`
	LogLevel        string                    `protobuf:"bytes,1,opt,name=log_level,json=logLevel,proto3" json:"log_level,omitempty"`
	Replicas        uint32                    `protobuf:"varint,2,opt,name=replicas,proto3" json:"replicas,omitempty"`
	UpgradeStrategy *NodeGroupUpgradeStrategy `protobuf:"bytes,3,opt,name=upgrade_strategy,json=upgradeStrategy,proto3" json:"upgrade_strategy,omitempty"`
	NodePodSpec     *NodePodSpec              `protobuf:"bytes,4,opt,name=node_pod_spec,json=nodePodSpec,proto3" json:"node_pod_spec,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StandaloneSpec) Reset() {
	*x = StandaloneSpec{}
	mi := &file_common_risingwave_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StandaloneSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StandaloneSpec) ProtoMessage() {}

func (x *StandaloneSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StandaloneSpec.ProtoReflect.Descriptor instead.
func (*StandaloneSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{23}
}

func (x *StandaloneSpec) GetLogLevel() string {
	if x != nil {
		return x.LogLevel
	}
	return ""
}

func (x *StandaloneSpec) GetReplicas() uint32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *StandaloneSpec) GetUpgradeStrategy() *NodeGroupUpgradeStrategy {
	if x != nil {
		return x.UpgradeStrategy
	}
	return nil
}

func (x *StandaloneSpec) GetNodePodSpec() *NodePodSpec {
	if x != nil {
		return x.NodePodSpec
	}
	return nil
}

type RisingWaveStatus struct {
	state              protoimpl.MessageState `protogen:"open.v1"`
	StatusCode         RisingWaveStatusCode   `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3,enum=common.rw.RisingWaveStatusCode" json:"status_code,omitempty"`
	StateStoreRootPath string                 `protobuf:"bytes,2,opt,name=state_store_root_path,json=stateStoreRootPath,proto3" json:"state_store_root_path,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *RisingWaveStatus) Reset() {
	*x = RisingWaveStatus{}
	mi := &file_common_risingwave_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RisingWaveStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RisingWaveStatus) ProtoMessage() {}

func (x *RisingWaveStatus) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RisingWaveStatus.ProtoReflect.Descriptor instead.
func (*RisingWaveStatus) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{24}
}

func (x *RisingWaveStatus) GetStatusCode() RisingWaveStatusCode {
	if x != nil {
		return x.StatusCode
	}
	return RisingWaveStatusCode_UNKNOWN_RW_STATUS
}

func (x *RisingWaveStatus) GetStateStoreRootPath() string {
	if x != nil {
		return x.StateStoreRootPath
	}
	return ""
}

// ScaleSpec specifies the replica and pod resources for a RW node group
type ScaleSpec struct {
	state    protoimpl.MessageState `protogen:"open.v1"`
	Replicas uint32                 `protobuf:"varint,1,opt,name=replicas,proto3" json:"replicas,omitempty"`
	// If present, set pods with the specified resources.
	Resources *k8s.ResourceRequirements `protobuf:"bytes,2,opt,name=resources,proto3" json:"resources,omitempty"`
	// If present, override the existing affinity of the node group.
	Affinity      *k8s.Affinity `protobuf:"bytes,3,opt,name=affinity,proto3" json:"affinity,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScaleSpec) Reset() {
	*x = ScaleSpec{}
	mi := &file_common_risingwave_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScaleSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScaleSpec) ProtoMessage() {}

func (x *ScaleSpec) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScaleSpec.ProtoReflect.Descriptor instead.
func (*ScaleSpec) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{25}
}

func (x *ScaleSpec) GetReplicas() uint32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *ScaleSpec) GetResources() *k8s.ResourceRequirements {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *ScaleSpec) GetAffinity() *k8s.Affinity {
	if x != nil {
		return x.Affinity
	}
	return nil
}

type LicenseKey struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SecretName    string                 `protobuf:"bytes,1,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LicenseKey) Reset() {
	*x = LicenseKey{}
	mi := &file_common_risingwave_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LicenseKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LicenseKey) ProtoMessage() {}

func (x *LicenseKey) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LicenseKey.ProtoReflect.Descriptor instead.
func (*LicenseKey) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{26}
}

func (x *LicenseKey) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

type SecretStore struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PrivateKey    *SecretStorePrivateKey `protobuf:"bytes,1,opt,name=private_key,json=privateKey,proto3" json:"private_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretStore) Reset() {
	*x = SecretStore{}
	mi := &file_common_risingwave_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretStore) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretStore) ProtoMessage() {}

func (x *SecretStore) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretStore.ProtoReflect.Descriptor instead.
func (*SecretStore) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{27}
}

func (x *SecretStore) GetPrivateKey() *SecretStorePrivateKey {
	if x != nil {
		return x.PrivateKey
	}
	return nil
}

type SecretStorePrivateKey struct {
	state         protoimpl.MessageState                `protogen:"open.v1"`
	Value         *string                               `protobuf:"bytes,1,opt,name=value,proto3,oneof" json:"value,omitempty"`
	SecretRef     *SecretStorePrivateKeySecretReference `protobuf:"bytes,2,opt,name=secret_ref,json=secretRef,proto3,oneof" json:"secret_ref,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretStorePrivateKey) Reset() {
	*x = SecretStorePrivateKey{}
	mi := &file_common_risingwave_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretStorePrivateKey) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretStorePrivateKey) ProtoMessage() {}

func (x *SecretStorePrivateKey) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretStorePrivateKey.ProtoReflect.Descriptor instead.
func (*SecretStorePrivateKey) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{28}
}

func (x *SecretStorePrivateKey) GetValue() string {
	if x != nil && x.Value != nil {
		return *x.Value
	}
	return ""
}

func (x *SecretStorePrivateKey) GetSecretRef() *SecretStorePrivateKeySecretReference {
	if x != nil {
		return x.SecretRef
	}
	return nil
}

type SecretStorePrivateKeySecretReference struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	Key           string                 `protobuf:"bytes,2,opt,name=key,proto3" json:"key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SecretStorePrivateKeySecretReference) Reset() {
	*x = SecretStorePrivateKeySecretReference{}
	mi := &file_common_risingwave_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SecretStorePrivateKeySecretReference) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SecretStorePrivateKeySecretReference) ProtoMessage() {}

func (x *SecretStorePrivateKeySecretReference) ProtoReflect() protoreflect.Message {
	mi := &file_common_risingwave_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SecretStorePrivateKeySecretReference.ProtoReflect.Descriptor instead.
func (*SecretStorePrivateKeySecretReference) Descriptor() ([]byte, []int) {
	return file_common_risingwave_proto_rawDescGZIP(), []int{29}
}

func (x *SecretStorePrivateKeySecretReference) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SecretStorePrivateKeySecretReference) GetKey() string {
	if x != nil {
		return x.Key
	}
	return ""
}

var File_common_risingwave_proto protoreflect.FileDescriptor

var file_common_risingwave_proto_rawDesc = string([]byte{
	0x0a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77,
	0x61, 0x76, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x09, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x77, 0x1a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xff, 0x05, 0x0a, 0x0e, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61,
	0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x12,
	0x43, 0x0a, 0x1e, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x1b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x44,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x12, 0x3a, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x12, 0x40, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x12, 0x29, 0x0a, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4b, 0x0a,
	0x15, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x52, 0x13, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x39, 0x0a, 0x0a, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x73, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0a, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x3d, 0x0a, 0x1b, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f,
	0x66, 0x75, 0x6c, 0x6c, 0x5f, 0x6b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73, 0x5f,
	0x61, 0x64, 0x64, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x08, 0x52, 0x18, 0x65, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x46, 0x75, 0x6c, 0x6c, 0x4b, 0x75, 0x62, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x65, 0x73,
	0x41, 0x64, 0x64, 0x72, 0x12, 0x34, 0x0a, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73,
	0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x6e,
	0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x3f, 0x0a, 0x1c, 0x65, 0x6e,
	0x61, 0x62, 0x6c, 0x65, 0x5f, 0x65, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64, 0x5f, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x6e, 0x67, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08,
	0x52, 0x19, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x6d, 0x62, 0x65, 0x64, 0x64, 0x65, 0x64,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x6e, 0x67, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x38, 0x0a, 0x0e, 0x63,
	0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0b, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x11, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x0a, 0x0b, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x5f, 0x6b, 0x65, 0x79, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4b, 0x65,
	0x79, 0x52, 0x0a, 0x6c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x39, 0x0a,
	0x0c, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x0d, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x22, 0xdb, 0x03, 0x0a, 0x0e, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x25, 0x0a, 0x0e, 0x64,
	0x61, 0x74, 0x61, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x12, 0x46, 0x0a, 0x0e, 0x73, 0x33, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x33, 0x48, 0x00, 0x52, 0x0c, 0x73, 0x33,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x49, 0x0a, 0x0f, 0x67, 0x63,
	0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x47, 0x43, 0x53, 0x48, 0x00, 0x52, 0x0d, 0x67, 0x63, 0x73, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x61, 0x7a, 0x62, 0x6c, 0x6f, 0x62, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x41,
	0x7a, 0x62, 0x6c, 0x6f, 0x62, 0x48, 0x00, 0x52, 0x10, 0x61, 0x7a, 0x62, 0x6c, 0x6f, 0x62, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x52, 0x0a, 0x12, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b,
	0x65, 0x6e, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x48, 0x00, 0x52, 0x10, 0x6d, 0x65, 0x6d,
	0x6f, 0x72, 0x79, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x5c, 0x0a,
	0x16, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x64, 0x69, 0x73, 0x6b, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x44, 0x69, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x13, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x44, 0x69, 0x73,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x09, 0x0a, 0x07, 0x62,
	0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x22, 0x45, 0x0a, 0x13, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53,
	0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x53, 0x33, 0x12, 0x16, 0x0a,
	0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x22, 0x2e, 0x0a,
	0x14, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x47, 0x43, 0x53, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x22, 0x53, 0x0a,
	0x17, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x41, 0x7a, 0x62, 0x6c, 0x6f, 0x62, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74,
	0x61, 0x69, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e,
	0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x4d, 0x65, 0x6d, 0x6f, 0x72, 0x79, 0x22, 0x30, 0x0a,
	0x1a, 0x53, 0x74, 0x61, 0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65,
	0x6e, 0x64, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x44, 0x69, 0x73, 0x6b, 0x12, 0x12, 0x0a, 0x04, 0x72,
	0x6f, 0x6f, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x72, 0x6f, 0x6f, 0x74, 0x22,
	0xa9, 0x01, 0x0a, 0x0d, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x42, 0x0a, 0x0c, 0x65, 0x74, 0x63, 0x64, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x45, 0x74, 0x63, 0x64, 0x52, 0x0b, 0x65, 0x74, 0x63, 0x64, 0x42, 0x61,
	0x63, 0x6b, 0x65, 0x6e, 0x64, 0x12, 0x54, 0x0a, 0x12, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65,
	0x73, 0x71, 0x6c, 0x5f, 0x62, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x50, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x11, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72,
	0x65, 0x73, 0x71, 0x6c, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x22, 0x32, 0x0a, 0x14, 0x4d,
	0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x65, 0x6e, 0x64, 0x45,
	0x74, 0x63, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22,
	0xae, 0x02, 0x0a, 0x1a, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x68, 0x6f,
	0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x12, 0x42, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x43, 0x72,
	0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x4c, 0x0a, 0x07, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x42, 0x61, 0x63,
	0x6b, 0x65, 0x6e, 0x64, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x2e, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x07, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x3a, 0x0a, 0x0c, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x8c, 0x01, 0x0a, 0x15, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x4b,
	0x65, 0x79, 0x52, 0x65, 0x66, 0x12, 0x28, 0x0a, 0x10, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72,
	0x64, 0x5f, 0x6b, 0x65, 0x79, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0e, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x66, 0x22,
	0x40, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x36, 0x0a, 0x0b, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x22, 0x76, 0x0a, 0x0a, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x68, 0x0a, 0x1d, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x1a, 0x6e,
	0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x22, 0x42, 0x0a, 0x1a, 0x4e, 0x6f, 0x64,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x22, 0x67, 0x0a,
	0x0d, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1b,
	0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x39, 0x0a, 0x0b, 0x6e,
	0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x3c, 0x0a, 0x26, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50,
	0x61, 0x72, 0x74, 0x69, 0x61, 0x6c, 0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xd4, 0x01, 0x0a, 0x24, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65,
	0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x56, 0x0a,
	0x0c, 0x77, 0x68, 0x65, 0x6e, 0x5f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e,
	0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x77, 0x68, 0x65, 0x6e, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x54, 0x0a, 0x0b, 0x77, 0x68, 0x65, 0x6e, 0x5f, 0x73, 0x63,
	0x61, 0x6c, 0x65, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x33, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x74, 0x65,
	0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52,
	0x0a, 0x77, 0x68, 0x65, 0x6e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x64, 0x22, 0xa1, 0x01, 0x0a, 0x15,
	0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x4d, 0x0a, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x64, 0x61, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x50, 0x61, 0x72, 0x74, 0x69, 0x61, 0x6c,
	0x4f, 0x62, 0x6a, 0x65, 0x63, 0x74, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61,
	0x64, 0x61, 0x74, 0x61, 0x12, 0x39, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x22,
	0xe5, 0x03, 0x0a, 0x0d, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x70, 0x65,
	0x63, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x12, 0x4e, 0x0a, 0x10, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x72,
	0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79,
	0x52, 0x0f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x12, 0x3a, 0x0a, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x70, 0x6f, 0x64, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63,
	0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x12, 0x56, 0x0a,
	0x16, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69, 0x6d, 0x5f, 0x74, 0x65,
	0x6d, 0x70, 0x6c, 0x61, 0x74, 0x65, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52,
	0x14, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x54, 0x65, 0x6d, 0x70,
	0x6c, 0x61, 0x74, 0x65, 0x73, 0x12, 0x87, 0x01, 0x0a, 0x28, 0x70, 0x65, 0x72, 0x73, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x5f, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x63, 0x6c, 0x61, 0x69,
	0x6d, 0x5f, 0x72, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74,
	0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x24, 0x70, 0x65, 0x72, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x36, 0x0a, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6e, 0x6f, 0x64,
	0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x9a, 0x04, 0x0a, 0x0b, 0x4e, 0x6f, 0x64, 0x65,
	0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x12, 0x38, 0x0a, 0x0b, 0x74, 0x6f, 0x6c, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x30, 0x0a, 0x08, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x66, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x79, 0x12, 0x46, 0x0a, 0x0e, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72,
	0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x27, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3a, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x05,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x12, 0x49, 0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x2c, 0x0a, 0x07, 0x76,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x52, 0x07, 0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x8c, 0x02, 0x0a, 0x14, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64,
	0x43, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3e, 0x0a,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x3d, 0x0a,
	0x04, 0x65, 0x6e, 0x76, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x50, 0x6f, 0x64, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x2e, 0x45, 0x6e, 0x76,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x3c, 0x0a, 0x0d,
	0x76, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x5f, 0x6d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0c, 0x76, 0x6f,
	0x6c, 0x75, 0x6d, 0x65, 0x4d, 0x6f, 0x75, 0x6e, 0x74, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e,
	0x76, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x4e, 0x0a, 0x18, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12,
	0x32, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1e, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x22, 0x97, 0x03, 0x0a, 0x0e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x73, 0x53, 0x70, 0x65, 0x63, 0x12, 0x35, 0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3d, 0x0a,
	0x0d, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77,
	0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0c,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3b, 0x0a, 0x0c,
	0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x63, 0x6f,
	0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3f, 0x0a, 0x0e, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0d, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x43, 0x0a, 0x0e, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x53, 0x70, 0x65, 0x63, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x0d, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12,
	0x4c, 0x0a, 0x14, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x6c, 0x6f, 0x6e, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x13, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x6c, 0x6f, 0x6e, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0xd5, 0x01,
	0x0a, 0x0e, 0x53, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x12, 0x1b, 0x0a, 0x09, 0x6c, 0x6f, 0x67, 0x5f, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x67, 0x4c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x4e, 0x0a, 0x10, 0x75, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x5f, 0x73, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e,
	0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x52, 0x0f, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67, 0x79, 0x12, 0x3a, 0x0a, 0x0d, 0x6e, 0x6f, 0x64,
	0x65, 0x5f, 0x70, 0x6f, 0x64, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64,
	0x65, 0x50, 0x6f, 0x64, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x50, 0x6f,
	0x64, 0x53, 0x70, 0x65, 0x63, 0x22, 0x87, 0x01, 0x0a, 0x10, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x40, 0x0a, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x1f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x52, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65,
	0x52, 0x0a, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x31, 0x0a, 0x15,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x72, 0x6f, 0x6f, 0x74,
	0x5f, 0x70, 0x61, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x6f, 0x6f, 0x74, 0x50, 0x61, 0x74, 0x68, 0x22,
	0x99, 0x01, 0x0a, 0x09, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x1a, 0x0a,
	0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52,
	0x08, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x3e, 0x0a, 0x09, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x30, 0x0a, 0x08, 0x61, 0x66, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x79, 0x52, 0x08, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x22, 0x2d, 0x0a, 0x0a, 0x4c,
	0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x50, 0x0a, 0x0b, 0x53, 0x65,
	0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x41, 0x0a, 0x0b, 0x70, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79,
	0x52, 0x0a, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x22, 0xa0, 0x01, 0x0a,
	0x15, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x12, 0x19, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x53, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x77, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x66,
	0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x48, 0x01, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x52, 0x65, 0x66, 0x88, 0x01, 0x01, 0x42, 0x08, 0x0a, 0x06, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x42, 0x0d, 0x0a, 0x0b, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x22,
	0x4c, 0x0a, 0x24, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x4b, 0x65, 0x79, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65,
	0x66, 0x65, 0x72, 0x65, 0x6e, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x2a, 0x4f, 0x0a,
	0x28, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d,
	0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x74, 0x65, 0x6e, 0x74, 0x69, 0x6f, 0x6e, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x52, 0x45, 0x54, 0x41, 0x49, 0x4e, 0x10, 0x02, 0x2a, 0x7a,
	0x0a, 0x13, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x53, 0x74, 0x72, 0x61, 0x74, 0x65, 0x67,
	0x79, 0x54, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x10, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e,
	0x5f, 0x53, 0x54, 0x52, 0x41, 0x54, 0x45, 0x47, 0x59, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52,
	0x45, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x10, 0x01, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x4f, 0x4c,
	0x4c, 0x49, 0x4e, 0x47, 0x5f, 0x55, 0x50, 0x44, 0x41, 0x54, 0x45, 0x10, 0x02, 0x12, 0x18, 0x0a,
	0x14, 0x49, 0x4e, 0x5f, 0x50, 0x4c, 0x41, 0x43, 0x45, 0x5f, 0x49, 0x46, 0x5f, 0x50, 0x4f, 0x53,
	0x53, 0x49, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x11, 0x0a, 0x0d, 0x49, 0x4e, 0x5f, 0x50, 0x4c,
	0x41, 0x43, 0x45, 0x5f, 0x4f, 0x4e, 0x4c, 0x59, 0x10, 0x04, 0x2a, 0x7c, 0x0a, 0x14, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x52, 0x57,
	0x5f, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x57, 0x5f,
	0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x57, 0x5f, 0x4e, 0x4f,
	0x54, 0x5f, 0x52, 0x45, 0x41, 0x44, 0x59, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x52, 0x57, 0x5f,
	0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x1b, 0x0a, 0x17, 0x52,
	0x57, 0x5f, 0x57, 0x41, 0x49, 0x54, 0x5f, 0x46, 0x4f, 0x52, 0x5f, 0x4f, 0x42, 0x53, 0x45, 0x52,
	0x56, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x04, 0x2a, 0x6a, 0x0a, 0x0d, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x11, 0x55, 0x4e, 0x4b,
	0x4e, 0x4f, 0x57, 0x4e, 0x5f, 0x43, 0x4f, 0x4d, 0x50, 0x4f, 0x4e, 0x45, 0x4e, 0x54, 0x10, 0x00,
	0x12, 0x08, 0x0a, 0x04, 0x4d, 0x45, 0x54, 0x41, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x46, 0x52,
	0x4f, 0x4e, 0x54, 0x45, 0x4e, 0x44, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x4f, 0x4d, 0x50,
	0x55, 0x54, 0x45, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x43, 0x4f, 0x4d, 0x50, 0x41, 0x43, 0x54,
	0x4f, 0x52, 0x10, 0x04, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x54, 0x41, 0x4e, 0x44, 0x41, 0x4c, 0x4f,
	0x4e, 0x45, 0x10, 0x05, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62,
	0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67,
	0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x77, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_common_risingwave_proto_rawDescOnce sync.Once
	file_common_risingwave_proto_rawDescData []byte
)

func file_common_risingwave_proto_rawDescGZIP() []byte {
	file_common_risingwave_proto_rawDescOnce.Do(func() {
		file_common_risingwave_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_risingwave_proto_rawDesc), len(file_common_risingwave_proto_rawDesc)))
	})
	return file_common_risingwave_proto_rawDescData
}

var file_common_risingwave_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_common_risingwave_proto_msgTypes = make([]protoimpl.MessageInfo, 34)
var file_common_risingwave_proto_goTypes = []any{
	(PersistentVolumeClaimRetentionPolicyType)(0),  // 0: common.rw.PersistentVolumeClaimRetentionPolicyType
	(UpgradeStrategyType)(0),                       // 1: common.rw.UpgradeStrategyType
	(RisingWaveStatusCode)(0),                      // 2: common.rw.RisingWaveStatusCode
	(ComponentType)(0),                             // 3: common.rw.ComponentType
	(*RisingWaveSpec)(nil),                         // 4: common.rw.RisingWaveSpec
	(*StateStoreSpec)(nil),                         // 5: common.rw.StateStoreSpec
	(*StateStoreBackendS3)(nil),                    // 6: common.rw.StateStoreBackendS3
	(*StateStoreBackendGCS)(nil),                   // 7: common.rw.StateStoreBackendGCS
	(*StateStoreBackendAzblob)(nil),                // 8: common.rw.StateStoreBackendAzblob
	(*StateStoreBackendMemory)(nil),                // 9: common.rw.StateStoreBackendMemory
	(*StateStoreBackendLocalDisk)(nil),             // 10: common.rw.StateStoreBackendLocalDisk
	(*MetaStoreSpec)(nil),                          // 11: common.rw.MetaStoreSpec
	(*MetaStoreBackendEtcd)(nil),                   // 12: common.rw.MetaStoreBackendEtcd
	(*MetaStoreBackendPostgreSql)(nil),             // 13: common.rw.MetaStoreBackendPostgreSql
	(*PostgreSqlCredentials)(nil),                  // 14: common.rw.PostgreSqlCredentials
	(*Config)(nil),                                 // 15: common.rw.Config
	(*NodeConfig)(nil),                             // 16: common.rw.NodeConfig
	(*NodeConfigurationConfigMap)(nil),             // 17: common.rw.NodeConfigurationConfigMap
	(*ComponentSpec)(nil),                          // 18: common.rw.ComponentSpec
	(*PersistentVolumeClaimPartialObjectMeta)(nil), // 19: common.rw.PersistentVolumeClaimPartialObjectMeta
	(*PersistentVolumeClaimRetentionPolicy)(nil),   // 20: common.rw.PersistentVolumeClaimRetentionPolicy
	(*PersistentVolumeClaim)(nil),                  // 21: common.rw.PersistentVolumeClaim
	(*NodeGroupSpec)(nil),                          // 22: common.rw.NodeGroupSpec
	(*NodePodSpec)(nil),                            // 23: common.rw.NodePodSpec
	(*NodePodContainerSpec)(nil),                   // 24: common.rw.NodePodContainerSpec
	(*NodeGroupUpgradeStrategy)(nil),               // 25: common.rw.NodeGroupUpgradeStrategy
	(*ComponentsSpec)(nil),                         // 26: common.rw.ComponentsSpec
	(*StandaloneSpec)(nil),                         // 27: common.rw.StandaloneSpec
	(*RisingWaveStatus)(nil),                       // 28: common.rw.RisingWaveStatus
	(*ScaleSpec)(nil),                              // 29: common.rw.ScaleSpec
	(*LicenseKey)(nil),                             // 30: common.rw.LicenseKey
	(*SecretStore)(nil),                            // 31: common.rw.SecretStore
	(*SecretStorePrivateKey)(nil),                  // 32: common.rw.SecretStorePrivateKey
	(*SecretStorePrivateKeySecretReference)(nil),   // 33: common.rw.SecretStorePrivateKeySecretReference
	nil,                                   // 34: common.rw.MetaStoreBackendPostgreSql.OptionsEntry
	nil,                                   // 35: common.rw.NodePodSpec.LabelsEntry
	nil,                                   // 36: common.rw.NodePodSpec.AnnotationsEntry
	nil,                                   // 37: common.rw.NodePodContainerSpec.EnvsEntry
	(k8s.ServiceType)(0),                  // 38: common.k8s.ServiceType
	(*k8s.PersistentVolumeClaimSpec)(nil), // 39: common.k8s.PersistentVolumeClaimSpec
	(*k8s.Toleration)(nil),                // 40: common.k8s.Toleration
	(*k8s.Affinity)(nil),                  // 41: common.k8s.Affinity
	(*k8s.Volume)(nil),                    // 42: common.k8s.Volume
	(*k8s.ResourceRequirements)(nil),      // 43: common.k8s.ResourceRequirements
	(*k8s.VolumeMount)(nil),               // 44: common.k8s.VolumeMount
}
var file_common_risingwave_proto_depIdxs = []int32{
	5,  // 0: common.rw.RisingWaveSpec.state_store:type_name -> common.rw.StateStoreSpec
	11, // 1: common.rw.RisingWaveSpec.meta_store_spec:type_name -> common.rw.MetaStoreSpec
	15, // 2: common.rw.RisingWaveSpec.config:type_name -> common.rw.Config
	38, // 3: common.rw.RisingWaveSpec.frontend_service_type:type_name -> common.k8s.ServiceType
	26, // 4: common.rw.RisingWaveSpec.components:type_name -> common.rw.ComponentsSpec
	15, // 5: common.rw.RisingWaveSpec.compute_config:type_name -> common.rw.Config
	30, // 6: common.rw.RisingWaveSpec.license_key:type_name -> common.rw.LicenseKey
	31, // 7: common.rw.RisingWaveSpec.secret_store:type_name -> common.rw.SecretStore
	6,  // 8: common.rw.StateStoreSpec.s3_state_store:type_name -> common.rw.StateStoreBackendS3
	7,  // 9: common.rw.StateStoreSpec.gcs_state_store:type_name -> common.rw.StateStoreBackendGCS
	8,  // 10: common.rw.StateStoreSpec.azblob_state_store:type_name -> common.rw.StateStoreBackendAzblob
	9,  // 11: common.rw.StateStoreSpec.memory_state_store:type_name -> common.rw.StateStoreBackendMemory
	10, // 12: common.rw.StateStoreSpec.local_disk_state_store:type_name -> common.rw.StateStoreBackendLocalDisk
	12, // 13: common.rw.MetaStoreSpec.etcd_backend:type_name -> common.rw.MetaStoreBackendEtcd
	13, // 14: common.rw.MetaStoreSpec.postgresql_backend:type_name -> common.rw.MetaStoreBackendPostgreSql
	14, // 15: common.rw.MetaStoreBackendPostgreSql.credentials:type_name -> common.rw.PostgreSqlCredentials
	34, // 16: common.rw.MetaStoreBackendPostgreSql.options:type_name -> common.rw.MetaStoreBackendPostgreSql.OptionsEntry
	16, // 17: common.rw.Config.node_config:type_name -> common.rw.NodeConfig
	17, // 18: common.rw.NodeConfig.node_configuration_config_map:type_name -> common.rw.NodeConfigurationConfigMap
	22, // 19: common.rw.ComponentSpec.node_groups:type_name -> common.rw.NodeGroupSpec
	0,  // 20: common.rw.PersistentVolumeClaimRetentionPolicy.when_deleted:type_name -> common.rw.PersistentVolumeClaimRetentionPolicyType
	0,  // 21: common.rw.PersistentVolumeClaimRetentionPolicy.when_scaled:type_name -> common.rw.PersistentVolumeClaimRetentionPolicyType
	19, // 22: common.rw.PersistentVolumeClaim.metadata:type_name -> common.rw.PersistentVolumeClaimPartialObjectMeta
	39, // 23: common.rw.PersistentVolumeClaim.spec:type_name -> common.k8s.PersistentVolumeClaimSpec
	25, // 24: common.rw.NodeGroupSpec.upgrade_strategy:type_name -> common.rw.NodeGroupUpgradeStrategy
	23, // 25: common.rw.NodeGroupSpec.node_pod_spec:type_name -> common.rw.NodePodSpec
	21, // 26: common.rw.NodeGroupSpec.volume_claim_templates:type_name -> common.rw.PersistentVolumeClaim
	20, // 27: common.rw.NodeGroupSpec.persistent_volume_claim_retention_policy:type_name -> common.rw.PersistentVolumeClaimRetentionPolicy
	16, // 28: common.rw.NodeGroupSpec.node_config:type_name -> common.rw.NodeConfig
	40, // 29: common.rw.NodePodSpec.tolerations:type_name -> common.k8s.Toleration
	41, // 30: common.rw.NodePodSpec.affinity:type_name -> common.k8s.Affinity
	24, // 31: common.rw.NodePodSpec.container_spec:type_name -> common.rw.NodePodContainerSpec
	35, // 32: common.rw.NodePodSpec.labels:type_name -> common.rw.NodePodSpec.LabelsEntry
	36, // 33: common.rw.NodePodSpec.annotations:type_name -> common.rw.NodePodSpec.AnnotationsEntry
	42, // 34: common.rw.NodePodSpec.volumes:type_name -> common.k8s.Volume
	43, // 35: common.rw.NodePodContainerSpec.resources:type_name -> common.k8s.ResourceRequirements
	37, // 36: common.rw.NodePodContainerSpec.envs:type_name -> common.rw.NodePodContainerSpec.EnvsEntry
	44, // 37: common.rw.NodePodContainerSpec.volume_mounts:type_name -> common.k8s.VolumeMount
	1,  // 38: common.rw.NodeGroupUpgradeStrategy.type:type_name -> common.rw.UpgradeStrategyType
	18, // 39: common.rw.ComponentsSpec.meta_spec:type_name -> common.rw.ComponentSpec
	18, // 40: common.rw.ComponentsSpec.frontend_spec:type_name -> common.rw.ComponentSpec
	18, // 41: common.rw.ComponentsSpec.compute_spec:type_name -> common.rw.ComponentSpec
	18, // 42: common.rw.ComponentsSpec.compactor_spec:type_name -> common.rw.ComponentSpec
	18, // 43: common.rw.ComponentsSpec.connector_spec:type_name -> common.rw.ComponentSpec
	27, // 44: common.rw.ComponentsSpec.standalone_component:type_name -> common.rw.StandaloneSpec
	25, // 45: common.rw.StandaloneSpec.upgrade_strategy:type_name -> common.rw.NodeGroupUpgradeStrategy
	23, // 46: common.rw.StandaloneSpec.node_pod_spec:type_name -> common.rw.NodePodSpec
	2,  // 47: common.rw.RisingWaveStatus.status_code:type_name -> common.rw.RisingWaveStatusCode
	43, // 48: common.rw.ScaleSpec.resources:type_name -> common.k8s.ResourceRequirements
	41, // 49: common.rw.ScaleSpec.affinity:type_name -> common.k8s.Affinity
	32, // 50: common.rw.SecretStore.private_key:type_name -> common.rw.SecretStorePrivateKey
	33, // 51: common.rw.SecretStorePrivateKey.secret_ref:type_name -> common.rw.SecretStorePrivateKeySecretReference
	52, // [52:52] is the sub-list for method output_type
	52, // [52:52] is the sub-list for method input_type
	52, // [52:52] is the sub-list for extension type_name
	52, // [52:52] is the sub-list for extension extendee
	0,  // [0:52] is the sub-list for field type_name
}

func init() { file_common_risingwave_proto_init() }
func file_common_risingwave_proto_init() {
	if File_common_risingwave_proto != nil {
		return
	}
	file_common_risingwave_proto_msgTypes[1].OneofWrappers = []any{
		(*StateStoreSpec_S3StateStore)(nil),
		(*StateStoreSpec_GcsStateStore)(nil),
		(*StateStoreSpec_AzblobStateStore)(nil),
		(*StateStoreSpec_MemoryStateStore)(nil),
		(*StateStoreSpec_LocalDiskStateStore)(nil),
	}
	file_common_risingwave_proto_msgTypes[28].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_risingwave_proto_rawDesc), len(file_common_risingwave_proto_rawDesc)),
			NumEnums:      4,
			NumMessages:   34,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_risingwave_proto_goTypes,
		DependencyIndexes: file_common_risingwave_proto_depIdxs,
		EnumInfos:         file_common_risingwave_proto_enumTypes,
		MessageInfos:      file_common_risingwave_proto_msgTypes,
	}.Build()
	File_common_risingwave_proto = out.File
	file_common_risingwave_proto_goTypes = nil
	file_common_risingwave_proto_depIdxs = nil
}
