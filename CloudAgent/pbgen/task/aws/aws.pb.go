// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/aws.proto

package aws

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AWSDirectoryCleanUpTask represents a task deleting all objects in a AWS
// directory.
type AWSDirectoryCleanUpTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AWS bucket the AWS directory belongs to
	Bucket string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	// AWS directory to be deleted
	Directory     string `protobuf:"bytes,2,opt,name=directory,proto3" json:"directory,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AWSDirectoryCleanUpTask) Reset() {
	*x = AWSDirectoryCleanUpTask{}
	mi := &file_task_aws_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AWSDirectoryCleanUpTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AWSDirectoryCleanUpTask) ProtoMessage() {}

func (x *AWSDirectoryCleanUpTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_aws_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AWSDirectoryCleanUpTask.ProtoReflect.Descriptor instead.
func (*AWSDirectoryCleanUpTask) Descriptor() ([]byte, []int) {
	return file_task_aws_proto_rawDescGZIP(), []int{0}
}

func (x *AWSDirectoryCleanUpTask) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *AWSDirectoryCleanUpTask) GetDirectory() string {
	if x != nil {
		return x.Directory
	}
	return ""
}

type AWSSimpleDataReplicationTask struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	SourceBucket    string                 `protobuf:"bytes,1,opt,name=source_bucket,json=sourceBucket,proto3" json:"source_bucket,omitempty"`
	SourceDirectory string                 `protobuf:"bytes,2,opt,name=source_directory,json=sourceDirectory,proto3" json:"source_directory,omitempty"`
	SinkBucket      string                 `protobuf:"bytes,3,opt,name=sink_bucket,json=sinkBucket,proto3" json:"sink_bucket,omitempty"`
	SinkDirectory   string                 `protobuf:"bytes,4,opt,name=sink_directory,json=sinkDirectory,proto3" json:"sink_directory,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *AWSSimpleDataReplicationTask) Reset() {
	*x = AWSSimpleDataReplicationTask{}
	mi := &file_task_aws_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AWSSimpleDataReplicationTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AWSSimpleDataReplicationTask) ProtoMessage() {}

func (x *AWSSimpleDataReplicationTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_aws_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AWSSimpleDataReplicationTask.ProtoReflect.Descriptor instead.
func (*AWSSimpleDataReplicationTask) Descriptor() ([]byte, []int) {
	return file_task_aws_proto_rawDescGZIP(), []int{1}
}

func (x *AWSSimpleDataReplicationTask) GetSourceBucket() string {
	if x != nil {
		return x.SourceBucket
	}
	return ""
}

func (x *AWSSimpleDataReplicationTask) GetSourceDirectory() string {
	if x != nil {
		return x.SourceDirectory
	}
	return ""
}

func (x *AWSSimpleDataReplicationTask) GetSinkBucket() string {
	if x != nil {
		return x.SinkBucket
	}
	return ""
}

func (x *AWSSimpleDataReplicationTask) GetSinkDirectory() string {
	if x != nil {
		return x.SinkDirectory
	}
	return ""
}

type AWSDirectoryCloneTask struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	SourceDirectoryName      string                 `protobuf:"bytes,1,opt,name=source_directory_name,json=sourceDirectoryName,proto3" json:"source_directory_name,omitempty"`
	SourceBucketName         string                 `protobuf:"bytes,2,opt,name=source_bucket_name,json=sourceBucketName,proto3" json:"source_bucket_name,omitempty"`
	DestinationDirectoryName string                 `protobuf:"bytes,3,opt,name=destination_directory_name,json=destinationDirectoryName,proto3" json:"destination_directory_name,omitempty"`
	DestinationBucketName    string                 `protobuf:"bytes,4,opt,name=destination_bucket_name,json=destinationBucketName,proto3" json:"destination_bucket_name,omitempty"`
	Cursor                   string                 `protobuf:"bytes,5,opt,name=cursor,proto3" json:"cursor,omitempty"`
	CloneSize                int32                  `protobuf:"varint,6,opt,name=clone_size,json=cloneSize,proto3" json:"clone_size,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *AWSDirectoryCloneTask) Reset() {
	*x = AWSDirectoryCloneTask{}
	mi := &file_task_aws_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AWSDirectoryCloneTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AWSDirectoryCloneTask) ProtoMessage() {}

func (x *AWSDirectoryCloneTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_aws_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AWSDirectoryCloneTask.ProtoReflect.Descriptor instead.
func (*AWSDirectoryCloneTask) Descriptor() ([]byte, []int) {
	return file_task_aws_proto_rawDescGZIP(), []int{2}
}

func (x *AWSDirectoryCloneTask) GetSourceDirectoryName() string {
	if x != nil {
		return x.SourceDirectoryName
	}
	return ""
}

func (x *AWSDirectoryCloneTask) GetSourceBucketName() string {
	if x != nil {
		return x.SourceBucketName
	}
	return ""
}

func (x *AWSDirectoryCloneTask) GetDestinationDirectoryName() string {
	if x != nil {
		return x.DestinationDirectoryName
	}
	return ""
}

func (x *AWSDirectoryCloneTask) GetDestinationBucketName() string {
	if x != nil {
		return x.DestinationBucketName
	}
	return ""
}

func (x *AWSDirectoryCloneTask) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *AWSDirectoryCloneTask) GetCloneSize() int32 {
	if x != nil {
		return x.CloneSize
	}
	return 0
}

var File_task_aws_proto protoreflect.FileDescriptor

var file_task_aws_proto_rawDesc = string([]byte{
	0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x61, 0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x61, 0x77, 0x73, 0x22, 0x4f, 0x0a, 0x17, 0x41, 0x57,
	0x53, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55,
	0x70, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x22, 0xb6, 0x01, 0x0a, 0x1c,
	0x41, 0x57, 0x53, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x23, 0x0a, 0x0d,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x25, 0x0a,
	0x0e, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x6b, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x22, 0xa6, 0x02, 0x0a, 0x15, 0x41, 0x57, 0x53, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32,
	0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36,
	0x0a, 0x17, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x15, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x63, 0x6b,
	0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1d,
	0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x35, 0x5a,
	0x33, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b,
	0x2f, 0x61, 0x77, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_task_aws_proto_rawDescOnce sync.Once
	file_task_aws_proto_rawDescData []byte
)

func file_task_aws_proto_rawDescGZIP() []byte {
	file_task_aws_proto_rawDescOnce.Do(func() {
		file_task_aws_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_aws_proto_rawDesc), len(file_task_aws_proto_rawDesc)))
	})
	return file_task_aws_proto_rawDescData
}

var file_task_aws_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_task_aws_proto_goTypes = []any{
	(*AWSDirectoryCleanUpTask)(nil),      // 0: task.aws.AWSDirectoryCleanUpTask
	(*AWSSimpleDataReplicationTask)(nil), // 1: task.aws.AWSSimpleDataReplicationTask
	(*AWSDirectoryCloneTask)(nil),        // 2: task.aws.AWSDirectoryCloneTask
}
var file_task_aws_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_task_aws_proto_init() }
func file_task_aws_proto_init() {
	if File_task_aws_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_aws_proto_rawDesc), len(file_task_aws_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_aws_proto_goTypes,
		DependencyIndexes: file_task_aws_proto_depIdxs,
		MessageInfos:      file_task_aws_proto_msgTypes,
	}.Build()
	File_task_aws_proto = out.File
	file_task_aws_proto_goTypes = nil
	file_task_aws_proto_depIdxs = nil
}
