// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/byoc.proto

package byoc

import (
	byoc "github.com/risingwavelabs/cloudagent/pbgen/common/byoc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ApplyByocModuleTask struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ModuleOptions  *byoc.ModuleOptions    `protobuf:"bytes,1,opt,name=module_options,json=moduleOptions,proto3" json:"module_options,omitempty"`
	ApplyOptions   *byoc.ApplyOptions     `protobuf:"bytes,2,opt,name=apply_options,json=applyOptions,proto3" json:"apply_options,omitempty"`
	PackageOptions *byoc.PackageOptions   `protobuf:"bytes,3,opt,name=package_options,json=packageOptions,proto3" json:"package_options,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ApplyByocModuleTask) Reset() {
	*x = ApplyByocModuleTask{}
	mi := &file_task_byoc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ApplyByocModuleTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ApplyByocModuleTask) ProtoMessage() {}

func (x *ApplyByocModuleTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_byoc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ApplyByocModuleTask.ProtoReflect.Descriptor instead.
func (*ApplyByocModuleTask) Descriptor() ([]byte, []int) {
	return file_task_byoc_proto_rawDescGZIP(), []int{0}
}

func (x *ApplyByocModuleTask) GetModuleOptions() *byoc.ModuleOptions {
	if x != nil {
		return x.ModuleOptions
	}
	return nil
}

func (x *ApplyByocModuleTask) GetApplyOptions() *byoc.ApplyOptions {
	if x != nil {
		return x.ApplyOptions
	}
	return nil
}

func (x *ApplyByocModuleTask) GetPackageOptions() *byoc.PackageOptions {
	if x != nil {
		return x.PackageOptions
	}
	return nil
}

type RetrieveByocModuleOutputTask struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	OutputKey      string                 `protobuf:"bytes,1,opt,name=output_key,json=outputKey,proto3" json:"output_key,omitempty"`
	ModuleOptions  *byoc.ModuleOptions    `protobuf:"bytes,2,opt,name=module_options,json=moduleOptions,proto3" json:"module_options,omitempty"`
	OutputOptions  *byoc.OutputOptions    `protobuf:"bytes,3,opt,name=output_options,json=outputOptions,proto3" json:"output_options,omitempty"`
	PackageOptions *byoc.PackageOptions   `protobuf:"bytes,4,opt,name=package_options,json=packageOptions,proto3" json:"package_options,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *RetrieveByocModuleOutputTask) Reset() {
	*x = RetrieveByocModuleOutputTask{}
	mi := &file_task_byoc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RetrieveByocModuleOutputTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RetrieveByocModuleOutputTask) ProtoMessage() {}

func (x *RetrieveByocModuleOutputTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_byoc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RetrieveByocModuleOutputTask.ProtoReflect.Descriptor instead.
func (*RetrieveByocModuleOutputTask) Descriptor() ([]byte, []int) {
	return file_task_byoc_proto_rawDescGZIP(), []int{1}
}

func (x *RetrieveByocModuleOutputTask) GetOutputKey() string {
	if x != nil {
		return x.OutputKey
	}
	return ""
}

func (x *RetrieveByocModuleOutputTask) GetModuleOptions() *byoc.ModuleOptions {
	if x != nil {
		return x.ModuleOptions
	}
	return nil
}

func (x *RetrieveByocModuleOutputTask) GetOutputOptions() *byoc.OutputOptions {
	if x != nil {
		return x.OutputOptions
	}
	return nil
}

func (x *RetrieveByocModuleOutputTask) GetPackageOptions() *byoc.PackageOptions {
	if x != nil {
		return x.PackageOptions
	}
	return nil
}

var File_task_byoc_proto protoreflect.FileDescriptor

var file_task_byoc_proto_rawDesc = string([]byte{
	0x0a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x1a, 0x11, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xde, 0x01, 0x0a, 0x13, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x61, 0x70,
	0x70, 0x6c, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f,
	0x63, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x22, 0x89, 0x02, 0x0a, 0x1c, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x42, 0x79, 0x6f,
	0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4b, 0x65, 0x79,
	0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x6f, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67,
	0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x50, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0e, 0x70, 0x61,
	0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x42, 0x36, 0x5a, 0x34,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f,
	0x62, 0x79, 0x6f, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_task_byoc_proto_rawDescOnce sync.Once
	file_task_byoc_proto_rawDescData []byte
)

func file_task_byoc_proto_rawDescGZIP() []byte {
	file_task_byoc_proto_rawDescOnce.Do(func() {
		file_task_byoc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_byoc_proto_rawDesc), len(file_task_byoc_proto_rawDesc)))
	})
	return file_task_byoc_proto_rawDescData
}

var file_task_byoc_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_task_byoc_proto_goTypes = []any{
	(*ApplyByocModuleTask)(nil),          // 0: task.byoc.ApplyByocModuleTask
	(*RetrieveByocModuleOutputTask)(nil), // 1: task.byoc.RetrieveByocModuleOutputTask
	(*byoc.ModuleOptions)(nil),           // 2: common.byoc.ModuleOptions
	(*byoc.ApplyOptions)(nil),            // 3: common.byoc.ApplyOptions
	(*byoc.PackageOptions)(nil),          // 4: common.byoc.PackageOptions
	(*byoc.OutputOptions)(nil),           // 5: common.byoc.OutputOptions
}
var file_task_byoc_proto_depIdxs = []int32{
	2, // 0: task.byoc.ApplyByocModuleTask.module_options:type_name -> common.byoc.ModuleOptions
	3, // 1: task.byoc.ApplyByocModuleTask.apply_options:type_name -> common.byoc.ApplyOptions
	4, // 2: task.byoc.ApplyByocModuleTask.package_options:type_name -> common.byoc.PackageOptions
	2, // 3: task.byoc.RetrieveByocModuleOutputTask.module_options:type_name -> common.byoc.ModuleOptions
	5, // 4: task.byoc.RetrieveByocModuleOutputTask.output_options:type_name -> common.byoc.OutputOptions
	4, // 5: task.byoc.RetrieveByocModuleOutputTask.package_options:type_name -> common.byoc.PackageOptions
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_task_byoc_proto_init() }
func file_task_byoc_proto_init() {
	if File_task_byoc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_byoc_proto_rawDesc), len(file_task_byoc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_byoc_proto_goTypes,
		DependencyIndexes: file_task_byoc_proto_depIdxs,
		MessageInfos:      file_task_byoc_proto_msgTypes,
	}.Build()
	File_task_byoc_proto = out.File
	file_task_byoc_proto_goTypes = nil
	file_task_byoc_proto_depIdxs = nil
}
