// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/task.proto

package task

import (
	aws "github.com/risingwavelabs/cloudagent/pbgen/task/aws"
	azr "github.com/risingwavelabs/cloudagent/pbgen/task/azr"
	byoc "github.com/risingwavelabs/cloudagent/pbgen/task/byoc"
	gcp "github.com/risingwavelabs/cloudagent/pbgen/task/gcp"
	helm "github.com/risingwavelabs/cloudagent/pbgen/task/helm"
	rwc "github.com/risingwavelabs/cloudagent/pbgen/task/rwc"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Task wraps the configuration of a task, which will be run by task runner.
type Task struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Task:
	//
	//	*Task_RisectlTask
	//	*Task_GcsDirectoryCleanUpTask
	//	*Task_InstallHelmReleaseTask
	//	*Task_UpgradeHelmReleaseTask
	//	*Task_UninstallHelmReleaseTask
	//	*Task_AwsDirectoryCleanUpTask
	//	*Task_AzrDirectoryCleanUpTask
	//	*Task_AwsSimpleDataReplicationTask
	//	*Task_ApplyByocModuleTask
	//	*Task_RetrieveByocModuleOutputTask
	//	*Task_AwsDirectoryCloneTask
	//	*Task_GcpDirectoryCloneTask
	//	*Task_AzrDirectoryCloneTask
	Task          isTask_Task `protobuf_oneof:"task"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Task) Reset() {
	*x = Task{}
	mi := &file_task_task_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Task) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Task) ProtoMessage() {}

func (x *Task) ProtoReflect() protoreflect.Message {
	mi := &file_task_task_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Task.ProtoReflect.Descriptor instead.
func (*Task) Descriptor() ([]byte, []int) {
	return file_task_task_proto_rawDescGZIP(), []int{0}
}

func (x *Task) GetTask() isTask_Task {
	if x != nil {
		return x.Task
	}
	return nil
}

func (x *Task) GetRisectlTask() *rwc.RisectlTask {
	if x != nil {
		if x, ok := x.Task.(*Task_RisectlTask); ok {
			return x.RisectlTask
		}
	}
	return nil
}

func (x *Task) GetGcsDirectoryCleanUpTask() *gcp.GCSDirectoryCleanUpTask {
	if x != nil {
		if x, ok := x.Task.(*Task_GcsDirectoryCleanUpTask); ok {
			return x.GcsDirectoryCleanUpTask
		}
	}
	return nil
}

func (x *Task) GetInstallHelmReleaseTask() *helm.InstallReleaseTask {
	if x != nil {
		if x, ok := x.Task.(*Task_InstallHelmReleaseTask); ok {
			return x.InstallHelmReleaseTask
		}
	}
	return nil
}

func (x *Task) GetUpgradeHelmReleaseTask() *helm.UpgradeReleaseTask {
	if x != nil {
		if x, ok := x.Task.(*Task_UpgradeHelmReleaseTask); ok {
			return x.UpgradeHelmReleaseTask
		}
	}
	return nil
}

func (x *Task) GetUninstallHelmReleaseTask() *helm.UninstallReleaseTask {
	if x != nil {
		if x, ok := x.Task.(*Task_UninstallHelmReleaseTask); ok {
			return x.UninstallHelmReleaseTask
		}
	}
	return nil
}

func (x *Task) GetAwsDirectoryCleanUpTask() *aws.AWSDirectoryCleanUpTask {
	if x != nil {
		if x, ok := x.Task.(*Task_AwsDirectoryCleanUpTask); ok {
			return x.AwsDirectoryCleanUpTask
		}
	}
	return nil
}

func (x *Task) GetAzrDirectoryCleanUpTask() *azr.AZRDirectoryCleanUpTask {
	if x != nil {
		if x, ok := x.Task.(*Task_AzrDirectoryCleanUpTask); ok {
			return x.AzrDirectoryCleanUpTask
		}
	}
	return nil
}

func (x *Task) GetAwsSimpleDataReplicationTask() *aws.AWSSimpleDataReplicationTask {
	if x != nil {
		if x, ok := x.Task.(*Task_AwsSimpleDataReplicationTask); ok {
			return x.AwsSimpleDataReplicationTask
		}
	}
	return nil
}

func (x *Task) GetApplyByocModuleTask() *byoc.ApplyByocModuleTask {
	if x != nil {
		if x, ok := x.Task.(*Task_ApplyByocModuleTask); ok {
			return x.ApplyByocModuleTask
		}
	}
	return nil
}

func (x *Task) GetRetrieveByocModuleOutputTask() *byoc.RetrieveByocModuleOutputTask {
	if x != nil {
		if x, ok := x.Task.(*Task_RetrieveByocModuleOutputTask); ok {
			return x.RetrieveByocModuleOutputTask
		}
	}
	return nil
}

func (x *Task) GetAwsDirectoryCloneTask() *aws.AWSDirectoryCloneTask {
	if x != nil {
		if x, ok := x.Task.(*Task_AwsDirectoryCloneTask); ok {
			return x.AwsDirectoryCloneTask
		}
	}
	return nil
}

func (x *Task) GetGcpDirectoryCloneTask() *gcp.GCSDirectoryCloneTask {
	if x != nil {
		if x, ok := x.Task.(*Task_GcpDirectoryCloneTask); ok {
			return x.GcpDirectoryCloneTask
		}
	}
	return nil
}

func (x *Task) GetAzrDirectoryCloneTask() *azr.AZRDirectoryCloneTask {
	if x != nil {
		if x, ok := x.Task.(*Task_AzrDirectoryCloneTask); ok {
			return x.AzrDirectoryCloneTask
		}
	}
	return nil
}

type isTask_Task interface {
	isTask_Task()
}

type Task_RisectlTask struct {
	RisectlTask *rwc.RisectlTask `protobuf:"bytes,1,opt,name=risectl_task,json=risectlTask,proto3,oneof"`
}

type Task_GcsDirectoryCleanUpTask struct {
	GcsDirectoryCleanUpTask *gcp.GCSDirectoryCleanUpTask `protobuf:"bytes,2,opt,name=gcs_directory_clean_up_task,json=gcsDirectoryCleanUpTask,proto3,oneof"`
}

type Task_InstallHelmReleaseTask struct {
	InstallHelmReleaseTask *helm.InstallReleaseTask `protobuf:"bytes,3,opt,name=install_helm_release_task,json=installHelmReleaseTask,proto3,oneof"`
}

type Task_UpgradeHelmReleaseTask struct {
	UpgradeHelmReleaseTask *helm.UpgradeReleaseTask `protobuf:"bytes,4,opt,name=upgrade_helm_release_task,json=upgradeHelmReleaseTask,proto3,oneof"`
}

type Task_UninstallHelmReleaseTask struct {
	UninstallHelmReleaseTask *helm.UninstallReleaseTask `protobuf:"bytes,5,opt,name=uninstall_helm_release_task,json=uninstallHelmReleaseTask,proto3,oneof"`
}

type Task_AwsDirectoryCleanUpTask struct {
	AwsDirectoryCleanUpTask *aws.AWSDirectoryCleanUpTask `protobuf:"bytes,6,opt,name=aws_directory_clean_up_task,json=awsDirectoryCleanUpTask,proto3,oneof"`
}

type Task_AzrDirectoryCleanUpTask struct {
	AzrDirectoryCleanUpTask *azr.AZRDirectoryCleanUpTask `protobuf:"bytes,7,opt,name=azr_directory_clean_up_task,json=azrDirectoryCleanUpTask,proto3,oneof"`
}

type Task_AwsSimpleDataReplicationTask struct {
	AwsSimpleDataReplicationTask *aws.AWSSimpleDataReplicationTask `protobuf:"bytes,8,opt,name=aws_simple_data_replication_task,json=awsSimpleDataReplicationTask,proto3,oneof"`
}

type Task_ApplyByocModuleTask struct {
	ApplyByocModuleTask *byoc.ApplyByocModuleTask `protobuf:"bytes,9,opt,name=apply_byoc_module_task,json=applyByocModuleTask,proto3,oneof"`
}

type Task_RetrieveByocModuleOutputTask struct {
	RetrieveByocModuleOutputTask *byoc.RetrieveByocModuleOutputTask `protobuf:"bytes,10,opt,name=retrieve_byoc_module_output_task,json=retrieveByocModuleOutputTask,proto3,oneof"`
}

type Task_AwsDirectoryCloneTask struct {
	AwsDirectoryCloneTask *aws.AWSDirectoryCloneTask `protobuf:"bytes,11,opt,name=aws_directory_clone_task,json=awsDirectoryCloneTask,proto3,oneof"`
}

type Task_GcpDirectoryCloneTask struct {
	GcpDirectoryCloneTask *gcp.GCSDirectoryCloneTask `protobuf:"bytes,12,opt,name=gcp_directory_clone_task,json=gcpDirectoryCloneTask,proto3,oneof"`
}

type Task_AzrDirectoryCloneTask struct {
	AzrDirectoryCloneTask *azr.AZRDirectoryCloneTask `protobuf:"bytes,13,opt,name=azr_directory_clone_task,json=azrDirectoryCloneTask,proto3,oneof"`
}

func (*Task_RisectlTask) isTask_Task() {}

func (*Task_GcsDirectoryCleanUpTask) isTask_Task() {}

func (*Task_InstallHelmReleaseTask) isTask_Task() {}

func (*Task_UpgradeHelmReleaseTask) isTask_Task() {}

func (*Task_UninstallHelmReleaseTask) isTask_Task() {}

func (*Task_AwsDirectoryCleanUpTask) isTask_Task() {}

func (*Task_AzrDirectoryCleanUpTask) isTask_Task() {}

func (*Task_AwsSimpleDataReplicationTask) isTask_Task() {}

func (*Task_ApplyByocModuleTask) isTask_Task() {}

func (*Task_RetrieveByocModuleOutputTask) isTask_Task() {}

func (*Task_AwsDirectoryCloneTask) isTask_Task() {}

func (*Task_GcpDirectoryCloneTask) isTask_Task() {}

func (*Task_AzrDirectoryCloneTask) isTask_Task() {}

var File_task_task_proto protoreflect.FileDescriptor

var file_task_task_proto_rawDesc = string([]byte{
	0x0a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x1a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x72, 0x77,
	0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x67, 0x63,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x68, 0x65,
	0x6c, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x61,
	0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x61,
	0x7a, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x62,
	0x79, 0x6f, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdd, 0x09, 0x0a, 0x04, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x3a, 0x0a, 0x0c, 0x72, 0x69, 0x73, 0x65, 0x63, 0x74, 0x6c, 0x5f, 0x74, 0x61,
	0x73, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e,
	0x72, 0x77, 0x63, 0x2e, 0x52, 0x69, 0x73, 0x65, 0x63, 0x74, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x48,
	0x00, 0x52, 0x0b, 0x72, 0x69, 0x73, 0x65, 0x63, 0x74, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x61,
	0x0a, 0x1b, 0x67, 0x63, 0x73, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x63, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47,
	0x43, 0x53, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x55, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x17, 0x67, 0x63, 0x73, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x5a, 0x0a, 0x19, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x68, 0x65, 0x6c,
	0x6d, 0x5f, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x68, 0x65, 0x6c, 0x6d,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x16, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65,
	0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x5a, 0x0a,
	0x19, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x5f, 0x68, 0x65, 0x6c, 0x6d, 0x5f, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1d, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x68, 0x65, 0x6c, 0x6d, 0x2e, 0x55, 0x70, 0x67,
	0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x48,
	0x00, 0x52, 0x16, 0x75, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x60, 0x0a, 0x1b, 0x75, 0x6e, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x5f, 0x68, 0x65, 0x6c, 0x6d, 0x5f, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f,
	0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x68, 0x65, 0x6c, 0x6d, 0x2e, 0x55, 0x6e, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x48,
	0x00, 0x52, 0x18, 0x75, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x61, 0x0a, 0x1b, 0x61,
	0x77, 0x73, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6c, 0x65,
	0x61, 0x6e, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x41, 0x57, 0x53, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54,
	0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x17, 0x61, 0x77, 0x73, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x61,
	0x0a, 0x1b, 0x61, 0x7a, 0x72, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f,
	0x63, 0x6c, 0x65, 0x61, 0x6e, 0x5f, 0x75, 0x70, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x41,
	0x5a, 0x52, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e,
	0x55, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x17, 0x61, 0x7a, 0x72, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55, 0x70, 0x54, 0x61, 0x73,
	0x6b, 0x12, 0x70, 0x0a, 0x20, 0x61, 0x77, 0x73, 0x5f, 0x73, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x5f,
	0x64, 0x61, 0x74, 0x61, 0x5f, 0x72, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x41, 0x57, 0x53, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x1c, 0x61, 0x77, 0x73, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x55, 0x0a, 0x16, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x5f, 0x62, 0x79, 0x6f,
	0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e,
	0x41, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x13, 0x61, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x6f, 0x63,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x71, 0x0a, 0x20, 0x72, 0x65,
	0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x5f, 0x62, 0x79, 0x6f, 0x63, 0x5f, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x62, 0x79, 0x6f, 0x63,
	0x2e, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52,
	0x1c, 0x72, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x5a, 0x0a,
	0x18, 0x61, 0x77, 0x73, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63,
	0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1f, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x41, 0x57, 0x53, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x48, 0x00, 0x52, 0x15, 0x61, 0x77, 0x73, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x5a, 0x0a, 0x18, 0x67, 0x63, 0x70,
	0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6c, 0x6f, 0x6e, 0x65,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61,
	0x73, 0x6b, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x43, 0x53, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x15,
	0x67, 0x63, 0x70, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x5a, 0x0a, 0x18, 0x61, 0x7a, 0x72, 0x5f, 0x64, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x74, 0x61, 0x73,
	0x6b, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x61,
	0x7a, 0x72, 0x2e, 0x41, 0x5a, 0x52, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43,
	0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x48, 0x00, 0x52, 0x15, 0x61, 0x7a, 0x72, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73,
	0x6b, 0x42, 0x06, 0x0a, 0x04, 0x74, 0x61, 0x73, 0x6b, 0x42, 0x31, 0x5a, 0x2f, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61,
	0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_task_task_proto_rawDescOnce sync.Once
	file_task_task_proto_rawDescData []byte
)

func file_task_task_proto_rawDescGZIP() []byte {
	file_task_task_proto_rawDescOnce.Do(func() {
		file_task_task_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_task_proto_rawDesc), len(file_task_task_proto_rawDesc)))
	})
	return file_task_task_proto_rawDescData
}

var file_task_task_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_task_task_proto_goTypes = []any{
	(*Task)(nil),                              // 0: task.Task
	(*rwc.RisectlTask)(nil),                   // 1: task.rwc.RisectlTask
	(*gcp.GCSDirectoryCleanUpTask)(nil),       // 2: task.gcp.GCSDirectoryCleanUpTask
	(*helm.InstallReleaseTask)(nil),           // 3: task.helm.InstallReleaseTask
	(*helm.UpgradeReleaseTask)(nil),           // 4: task.helm.UpgradeReleaseTask
	(*helm.UninstallReleaseTask)(nil),         // 5: task.helm.UninstallReleaseTask
	(*aws.AWSDirectoryCleanUpTask)(nil),       // 6: task.aws.AWSDirectoryCleanUpTask
	(*azr.AZRDirectoryCleanUpTask)(nil),       // 7: task.azr.AZRDirectoryCleanUpTask
	(*aws.AWSSimpleDataReplicationTask)(nil),  // 8: task.aws.AWSSimpleDataReplicationTask
	(*byoc.ApplyByocModuleTask)(nil),          // 9: task.byoc.ApplyByocModuleTask
	(*byoc.RetrieveByocModuleOutputTask)(nil), // 10: task.byoc.RetrieveByocModuleOutputTask
	(*aws.AWSDirectoryCloneTask)(nil),         // 11: task.aws.AWSDirectoryCloneTask
	(*gcp.GCSDirectoryCloneTask)(nil),         // 12: task.gcp.GCSDirectoryCloneTask
	(*azr.AZRDirectoryCloneTask)(nil),         // 13: task.azr.AZRDirectoryCloneTask
}
var file_task_task_proto_depIdxs = []int32{
	1,  // 0: task.Task.risectl_task:type_name -> task.rwc.RisectlTask
	2,  // 1: task.Task.gcs_directory_clean_up_task:type_name -> task.gcp.GCSDirectoryCleanUpTask
	3,  // 2: task.Task.install_helm_release_task:type_name -> task.helm.InstallReleaseTask
	4,  // 3: task.Task.upgrade_helm_release_task:type_name -> task.helm.UpgradeReleaseTask
	5,  // 4: task.Task.uninstall_helm_release_task:type_name -> task.helm.UninstallReleaseTask
	6,  // 5: task.Task.aws_directory_clean_up_task:type_name -> task.aws.AWSDirectoryCleanUpTask
	7,  // 6: task.Task.azr_directory_clean_up_task:type_name -> task.azr.AZRDirectoryCleanUpTask
	8,  // 7: task.Task.aws_simple_data_replication_task:type_name -> task.aws.AWSSimpleDataReplicationTask
	9,  // 8: task.Task.apply_byoc_module_task:type_name -> task.byoc.ApplyByocModuleTask
	10, // 9: task.Task.retrieve_byoc_module_output_task:type_name -> task.byoc.RetrieveByocModuleOutputTask
	11, // 10: task.Task.aws_directory_clone_task:type_name -> task.aws.AWSDirectoryCloneTask
	12, // 11: task.Task.gcp_directory_clone_task:type_name -> task.gcp.GCSDirectoryCloneTask
	13, // 12: task.Task.azr_directory_clone_task:type_name -> task.azr.AZRDirectoryCloneTask
	13, // [13:13] is the sub-list for method output_type
	13, // [13:13] is the sub-list for method input_type
	13, // [13:13] is the sub-list for extension type_name
	13, // [13:13] is the sub-list for extension extendee
	0,  // [0:13] is the sub-list for field type_name
}

func init() { file_task_task_proto_init() }
func file_task_task_proto_init() {
	if File_task_task_proto != nil {
		return
	}
	file_task_task_proto_msgTypes[0].OneofWrappers = []any{
		(*Task_RisectlTask)(nil),
		(*Task_GcsDirectoryCleanUpTask)(nil),
		(*Task_InstallHelmReleaseTask)(nil),
		(*Task_UpgradeHelmReleaseTask)(nil),
		(*Task_UninstallHelmReleaseTask)(nil),
		(*Task_AwsDirectoryCleanUpTask)(nil),
		(*Task_AzrDirectoryCleanUpTask)(nil),
		(*Task_AwsSimpleDataReplicationTask)(nil),
		(*Task_ApplyByocModuleTask)(nil),
		(*Task_RetrieveByocModuleOutputTask)(nil),
		(*Task_AwsDirectoryCloneTask)(nil),
		(*Task_GcpDirectoryCloneTask)(nil),
		(*Task_AzrDirectoryCloneTask)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_task_proto_rawDesc), len(file_task_task_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_task_proto_goTypes,
		DependencyIndexes: file_task_task_proto_depIdxs,
		MessageInfos:      file_task_task_proto_msgTypes,
	}.Build()
	File_task_task_proto = out.File
	file_task_task_proto_goTypes = nil
	file_task_task_proto_depIdxs = nil
}
