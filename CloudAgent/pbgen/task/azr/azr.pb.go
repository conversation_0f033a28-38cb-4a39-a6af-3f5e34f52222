// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/azr.proto

package azr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// AWSDirectoryCleanUpTask represents a task deleting all objects in an AZR
// directory.
type AZRDirectoryCleanUpTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AZR container the directory belongs to
	Container string `protobuf:"bytes,1,opt,name=container,proto3" json:"container,omitempty"`
	// AZR directory to be deleted
	Directory string `protobuf:"bytes,2,opt,name=directory,proto3" json:"directory,omitempty"`
	// AZR storage account the container belongs to
	StorageAccount string `protobuf:"bytes,3,opt,name=storage_account,json=storageAccount,proto3" json:"storage_account,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *AZRDirectoryCleanUpTask) Reset() {
	*x = AZRDirectoryCleanUpTask{}
	mi := &file_task_azr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AZRDirectoryCleanUpTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AZRDirectoryCleanUpTask) ProtoMessage() {}

func (x *AZRDirectoryCleanUpTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_azr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AZRDirectoryCleanUpTask.ProtoReflect.Descriptor instead.
func (*AZRDirectoryCleanUpTask) Descriptor() ([]byte, []int) {
	return file_task_azr_proto_rawDescGZIP(), []int{0}
}

func (x *AZRDirectoryCleanUpTask) GetContainer() string {
	if x != nil {
		return x.Container
	}
	return ""
}

func (x *AZRDirectoryCleanUpTask) GetDirectory() string {
	if x != nil {
		return x.Directory
	}
	return ""
}

func (x *AZRDirectoryCleanUpTask) GetStorageAccount() string {
	if x != nil {
		return x.StorageAccount
	}
	return ""
}

type AZRDirectoryCloneTask struct {
	state                     protoimpl.MessageState `protogen:"open.v1"`
	SourceDirectoryName       string                 `protobuf:"bytes,1,opt,name=source_directory_name,json=sourceDirectoryName,proto3" json:"source_directory_name,omitempty"`
	SourceContainerName       string                 `protobuf:"bytes,2,opt,name=source_container_name,json=sourceContainerName,proto3" json:"source_container_name,omitempty"`
	DestinationDirectoryName  string                 `protobuf:"bytes,3,opt,name=destination_directory_name,json=destinationDirectoryName,proto3" json:"destination_directory_name,omitempty"`
	DestinationContainerName  string                 `protobuf:"bytes,4,opt,name=destination_container_name,json=destinationContainerName,proto3" json:"destination_container_name,omitempty"`
	SourceStorageAccount      string                 `protobuf:"bytes,5,opt,name=source_storage_account,json=sourceStorageAccount,proto3" json:"source_storage_account,omitempty"`
	DestinationStorageAccount string                 `protobuf:"bytes,6,opt,name=destination_storage_account,json=destinationStorageAccount,proto3" json:"destination_storage_account,omitempty"`
	Cursor                    string                 `protobuf:"bytes,7,opt,name=cursor,proto3" json:"cursor,omitempty"`
	CloneSize                 int32                  `protobuf:"varint,8,opt,name=clone_size,json=cloneSize,proto3" json:"clone_size,omitempty"`
	unknownFields             protoimpl.UnknownFields
	sizeCache                 protoimpl.SizeCache
}

func (x *AZRDirectoryCloneTask) Reset() {
	*x = AZRDirectoryCloneTask{}
	mi := &file_task_azr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AZRDirectoryCloneTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AZRDirectoryCloneTask) ProtoMessage() {}

func (x *AZRDirectoryCloneTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_azr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AZRDirectoryCloneTask.ProtoReflect.Descriptor instead.
func (*AZRDirectoryCloneTask) Descriptor() ([]byte, []int) {
	return file_task_azr_proto_rawDescGZIP(), []int{1}
}

func (x *AZRDirectoryCloneTask) GetSourceDirectoryName() string {
	if x != nil {
		return x.SourceDirectoryName
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetSourceContainerName() string {
	if x != nil {
		return x.SourceContainerName
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetDestinationDirectoryName() string {
	if x != nil {
		return x.DestinationDirectoryName
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetDestinationContainerName() string {
	if x != nil {
		return x.DestinationContainerName
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetSourceStorageAccount() string {
	if x != nil {
		return x.SourceStorageAccount
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetDestinationStorageAccount() string {
	if x != nil {
		return x.DestinationStorageAccount
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *AZRDirectoryCloneTask) GetCloneSize() int32 {
	if x != nil {
		return x.CloneSize
	}
	return 0
}

var File_task_azr_proto protoreflect.FileDescriptor

var file_task_azr_proto_rawDesc = string([]byte{
	0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x61, 0x7a, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x61, 0x7a, 0x72, 0x22, 0x7e, 0x0a, 0x17, 0x41, 0x5a,
	0x52, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55,
	0x70, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x12, 0x1c, 0x0a, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0xa8, 0x03, 0x0a, 0x15, 0x41,
	0x5a, 0x52, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63,
	0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x43,
	0x6f, 0x6e, 0x74, 0x61, 0x69, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x18, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x72,
	0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x65,
	0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18,
	0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x3e,
	0x0a, 0x1b, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74,
	0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x19, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x6e,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e,
	0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61,
	0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62,
	0x67, 0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x61, 0x7a, 0x72, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_task_azr_proto_rawDescOnce sync.Once
	file_task_azr_proto_rawDescData []byte
)

func file_task_azr_proto_rawDescGZIP() []byte {
	file_task_azr_proto_rawDescOnce.Do(func() {
		file_task_azr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_azr_proto_rawDesc), len(file_task_azr_proto_rawDesc)))
	})
	return file_task_azr_proto_rawDescData
}

var file_task_azr_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_task_azr_proto_goTypes = []any{
	(*AZRDirectoryCleanUpTask)(nil), // 0: task.azr.AZRDirectoryCleanUpTask
	(*AZRDirectoryCloneTask)(nil),   // 1: task.azr.AZRDirectoryCloneTask
}
var file_task_azr_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_task_azr_proto_init() }
func file_task_azr_proto_init() {
	if File_task_azr_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_azr_proto_rawDesc), len(file_task_azr_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_azr_proto_goTypes,
		DependencyIndexes: file_task_azr_proto_depIdxs,
		MessageInfos:      file_task_azr_proto_msgTypes,
	}.Build()
	File_task_azr_proto = out.File
	file_task_azr_proto_goTypes = nil
	file_task_azr_proto_depIdxs = nil
}
