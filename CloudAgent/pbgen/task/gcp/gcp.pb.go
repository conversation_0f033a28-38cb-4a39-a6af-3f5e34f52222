// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/gcp.proto

package gcp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// GCSDirectoryCleanUpTask represents a task deleting all objects in a GCS
// directory.
type GCSDirectoryCleanUpTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// GCS bucket the GCS directory belongs to
	Bucket string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	// GCS directory to be deleted
	Directory     string `protobuf:"bytes,2,opt,name=directory,proto3" json:"directory,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GCSDirectoryCleanUpTask) Reset() {
	*x = GCSDirectoryCleanUpTask{}
	mi := &file_task_gcp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GCSDirectoryCleanUpTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GCSDirectoryCleanUpTask) ProtoMessage() {}

func (x *GCSDirectoryCleanUpTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_gcp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GCSDirectoryCleanUpTask.ProtoReflect.Descriptor instead.
func (*GCSDirectoryCleanUpTask) Descriptor() ([]byte, []int) {
	return file_task_gcp_proto_rawDescGZIP(), []int{0}
}

func (x *GCSDirectoryCleanUpTask) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *GCSDirectoryCleanUpTask) GetDirectory() string {
	if x != nil {
		return x.Directory
	}
	return ""
}

type GCSDirectoryCloneTask struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	SourceDirectoryName      string                 `protobuf:"bytes,1,opt,name=source_directory_name,json=sourceDirectoryName,proto3" json:"source_directory_name,omitempty"`
	SourceBucketName         string                 `protobuf:"bytes,2,opt,name=source_bucket_name,json=sourceBucketName,proto3" json:"source_bucket_name,omitempty"`
	DestinationDirectoryName string                 `protobuf:"bytes,3,opt,name=destination_directory_name,json=destinationDirectoryName,proto3" json:"destination_directory_name,omitempty"`
	DestinationBucketName    string                 `protobuf:"bytes,4,opt,name=destination_bucket_name,json=destinationBucketName,proto3" json:"destination_bucket_name,omitempty"`
	Cursor                   string                 `protobuf:"bytes,5,opt,name=cursor,proto3" json:"cursor,omitempty"`
	CloneSize                int32                  `protobuf:"varint,6,opt,name=clone_size,json=cloneSize,proto3" json:"clone_size,omitempty"`
	unknownFields            protoimpl.UnknownFields
	sizeCache                protoimpl.SizeCache
}

func (x *GCSDirectoryCloneTask) Reset() {
	*x = GCSDirectoryCloneTask{}
	mi := &file_task_gcp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GCSDirectoryCloneTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GCSDirectoryCloneTask) ProtoMessage() {}

func (x *GCSDirectoryCloneTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_gcp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GCSDirectoryCloneTask.ProtoReflect.Descriptor instead.
func (*GCSDirectoryCloneTask) Descriptor() ([]byte, []int) {
	return file_task_gcp_proto_rawDescGZIP(), []int{1}
}

func (x *GCSDirectoryCloneTask) GetSourceDirectoryName() string {
	if x != nil {
		return x.SourceDirectoryName
	}
	return ""
}

func (x *GCSDirectoryCloneTask) GetSourceBucketName() string {
	if x != nil {
		return x.SourceBucketName
	}
	return ""
}

func (x *GCSDirectoryCloneTask) GetDestinationDirectoryName() string {
	if x != nil {
		return x.DestinationDirectoryName
	}
	return ""
}

func (x *GCSDirectoryCloneTask) GetDestinationBucketName() string {
	if x != nil {
		return x.DestinationBucketName
	}
	return ""
}

func (x *GCSDirectoryCloneTask) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *GCSDirectoryCloneTask) GetCloneSize() int32 {
	if x != nil {
		return x.CloneSize
	}
	return 0
}

var File_task_gcp_proto protoreflect.FileDescriptor

var file_task_gcp_proto_rawDesc = string([]byte{
	0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x67, 0x63, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x67, 0x63, 0x70, 0x22, 0x4f, 0x0a, 0x17, 0x47, 0x43,
	0x53, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x55,
	0x70, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x22, 0xa6, 0x02, 0x0a, 0x15,
	0x47, 0x43, 0x53, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x69, 0x72, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x65, 0x73, 0x74, 0x69,
	0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x65, 0x73,
	0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36, 0x0a, 0x17, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x63,
	0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x6f, 0x6e, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62,
	0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67,
	0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x67, 0x63, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x33,
})

var (
	file_task_gcp_proto_rawDescOnce sync.Once
	file_task_gcp_proto_rawDescData []byte
)

func file_task_gcp_proto_rawDescGZIP() []byte {
	file_task_gcp_proto_rawDescOnce.Do(func() {
		file_task_gcp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_gcp_proto_rawDesc), len(file_task_gcp_proto_rawDesc)))
	})
	return file_task_gcp_proto_rawDescData
}

var file_task_gcp_proto_msgTypes = make([]protoimpl.MessageInfo, 2)
var file_task_gcp_proto_goTypes = []any{
	(*GCSDirectoryCleanUpTask)(nil), // 0: task.gcp.GCSDirectoryCleanUpTask
	(*GCSDirectoryCloneTask)(nil),   // 1: task.gcp.GCSDirectoryCloneTask
}
var file_task_gcp_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_task_gcp_proto_init() }
func file_task_gcp_proto_init() {
	if File_task_gcp_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_gcp_proto_rawDesc), len(file_task_gcp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   2,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_gcp_proto_goTypes,
		DependencyIndexes: file_task_gcp_proto_depIdxs,
		MessageInfos:      file_task_gcp_proto_msgTypes,
	}.Build()
	File_task_gcp_proto = out.File
	file_task_gcp_proto_goTypes = nil
	file_task_gcp_proto_depIdxs = nil
}
