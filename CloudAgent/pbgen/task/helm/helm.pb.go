// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/helm.proto

package helm

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type InstallReleaseTask struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ReleaseName      string                 `protobuf:"bytes,1,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ReleaseNamespace string                 `protobuf:"bytes,2,opt,name=release_namespace,json=releaseNamespace,proto3" json:"release_namespace,omitempty"`
	ValuesJson       string                 `protobuf:"bytes,3,opt,name=values_json,json=valuesJson,proto3" json:"values_json,omitempty"`
	ChartUrl         string                 `protobuf:"bytes,4,opt,name=chart_url,json=chartUrl,proto3" json:"chart_url,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *InstallReleaseTask) Reset() {
	*x = InstallReleaseTask{}
	mi := &file_task_helm_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallReleaseTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallReleaseTask) ProtoMessage() {}

func (x *InstallReleaseTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_helm_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallReleaseTask.ProtoReflect.Descriptor instead.
func (*InstallReleaseTask) Descriptor() ([]byte, []int) {
	return file_task_helm_proto_rawDescGZIP(), []int{0}
}

func (x *InstallReleaseTask) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *InstallReleaseTask) GetReleaseNamespace() string {
	if x != nil {
		return x.ReleaseNamespace
	}
	return ""
}

func (x *InstallReleaseTask) GetValuesJson() string {
	if x != nil {
		return x.ValuesJson
	}
	return ""
}

func (x *InstallReleaseTask) GetChartUrl() string {
	if x != nil {
		return x.ChartUrl
	}
	return ""
}

type UpgradeReleaseTask struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ReleaseName      string                 `protobuf:"bytes,1,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ReleaseNamespace string                 `protobuf:"bytes,2,opt,name=release_namespace,json=releaseNamespace,proto3" json:"release_namespace,omitempty"`
	ValuesJson       string                 `protobuf:"bytes,3,opt,name=values_json,json=valuesJson,proto3" json:"values_json,omitempty"`
	ChartUrl         string                 `protobuf:"bytes,4,opt,name=chart_url,json=chartUrl,proto3" json:"chart_url,omitempty"`
	Install          bool                   `protobuf:"varint,5,opt,name=install,proto3" json:"install,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UpgradeReleaseTask) Reset() {
	*x = UpgradeReleaseTask{}
	mi := &file_task_helm_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpgradeReleaseTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeReleaseTask) ProtoMessage() {}

func (x *UpgradeReleaseTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_helm_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeReleaseTask.ProtoReflect.Descriptor instead.
func (*UpgradeReleaseTask) Descriptor() ([]byte, []int) {
	return file_task_helm_proto_rawDescGZIP(), []int{1}
}

func (x *UpgradeReleaseTask) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *UpgradeReleaseTask) GetReleaseNamespace() string {
	if x != nil {
		return x.ReleaseNamespace
	}
	return ""
}

func (x *UpgradeReleaseTask) GetValuesJson() string {
	if x != nil {
		return x.ValuesJson
	}
	return ""
}

func (x *UpgradeReleaseTask) GetChartUrl() string {
	if x != nil {
		return x.ChartUrl
	}
	return ""
}

func (x *UpgradeReleaseTask) GetInstall() bool {
	if x != nil {
		return x.Install
	}
	return false
}

type UninstallReleaseTask struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ReleaseName      string                 `protobuf:"bytes,1,opt,name=release_name,json=releaseName,proto3" json:"release_name,omitempty"`
	ReleaseNamespace string                 `protobuf:"bytes,2,opt,name=release_namespace,json=releaseNamespace,proto3" json:"release_namespace,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *UninstallReleaseTask) Reset() {
	*x = UninstallReleaseTask{}
	mi := &file_task_helm_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UninstallReleaseTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UninstallReleaseTask) ProtoMessage() {}

func (x *UninstallReleaseTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_helm_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UninstallReleaseTask.ProtoReflect.Descriptor instead.
func (*UninstallReleaseTask) Descriptor() ([]byte, []int) {
	return file_task_helm_proto_rawDescGZIP(), []int{2}
}

func (x *UninstallReleaseTask) GetReleaseName() string {
	if x != nil {
		return x.ReleaseName
	}
	return ""
}

func (x *UninstallReleaseTask) GetReleaseNamespace() string {
	if x != nil {
		return x.ReleaseNamespace
	}
	return ""
}

var File_task_helm_proto protoreflect.FileDescriptor

var file_task_helm_proto_rawDesc = string([]byte{
	0x0a, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x68, 0x65, 0x6c, 0x6d, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x12, 0x09, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x68, 0x65, 0x6c, 0x6d, 0x22, 0xa2, 0x01, 0x0a,
	0x12, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x5f, 0x6a, 0x73,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73,
	0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x74, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x74, 0x55, 0x72,
	0x6c, 0x22, 0xbc, 0x01, 0x0a, 0x12, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61,
	0x72, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68,
	0x61, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x22, 0x66, 0x0a, 0x14, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2b, 0x0a, 0x11, 0x72,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x42, 0x36, 0x5a, 0x34, 0x67, 0x69, 0x74, 0x68,
	0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76,
	0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74,
	0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x68, 0x65, 0x6c, 0x6d,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_task_helm_proto_rawDescOnce sync.Once
	file_task_helm_proto_rawDescData []byte
)

func file_task_helm_proto_rawDescGZIP() []byte {
	file_task_helm_proto_rawDescOnce.Do(func() {
		file_task_helm_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_helm_proto_rawDesc), len(file_task_helm_proto_rawDesc)))
	})
	return file_task_helm_proto_rawDescData
}

var file_task_helm_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_task_helm_proto_goTypes = []any{
	(*InstallReleaseTask)(nil),   // 0: task.helm.InstallReleaseTask
	(*UpgradeReleaseTask)(nil),   // 1: task.helm.UpgradeReleaseTask
	(*UninstallReleaseTask)(nil), // 2: task.helm.UninstallReleaseTask
}
var file_task_helm_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_task_helm_proto_init() }
func file_task_helm_proto_init() {
	if File_task_helm_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_helm_proto_rawDesc), len(file_task_helm_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_helm_proto_goTypes,
		DependencyIndexes: file_task_helm_proto_depIdxs,
		MessageInfos:      file_task_helm_proto_msgTypes,
	}.Build()
	File_task_helm_proto = out.File
	file_task_helm_proto_goTypes = nil
	file_task_helm_proto_depIdxs = nil
}
