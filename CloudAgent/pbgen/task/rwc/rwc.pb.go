// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: task/rwc.proto

package rwc

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// RisectlTask represents a task executing a longrunning risectl command.
type RisectlTask struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Name of the RisingWave instance
	RisingwaveName string `protobuf:"bytes,1,opt,name=risingwave_name,json=risingwaveName,proto3" json:"risingwave_name,omitempty"`
	// Namespace of the RisingWave instance
	RisingwaveNamespace string `protobuf:"bytes,2,opt,name=risingwave_namespace,json=risingwaveNamespace,proto3" json:"risingwave_namespace,omitempty"`
	// risectl command args
	Args          []string `protobuf:"bytes,3,rep,name=args,proto3" json:"args,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RisectlTask) Reset() {
	*x = RisectlTask{}
	mi := &file_task_rwc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RisectlTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RisectlTask) ProtoMessage() {}

func (x *RisectlTask) ProtoReflect() protoreflect.Message {
	mi := &file_task_rwc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RisectlTask.ProtoReflect.Descriptor instead.
func (*RisectlTask) Descriptor() ([]byte, []int) {
	return file_task_rwc_proto_rawDescGZIP(), []int{0}
}

func (x *RisectlTask) GetRisingwaveName() string {
	if x != nil {
		return x.RisingwaveName
	}
	return ""
}

func (x *RisectlTask) GetRisingwaveNamespace() string {
	if x != nil {
		return x.RisingwaveNamespace
	}
	return ""
}

func (x *RisectlTask) GetArgs() []string {
	if x != nil {
		return x.Args
	}
	return nil
}

var File_task_rwc_proto protoreflect.FileDescriptor

var file_task_rwc_proto_rawDesc = string([]byte{
	0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x72, 0x77, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x12, 0x08, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x72, 0x77, 0x63, 0x22, 0x7d, 0x0a, 0x0b, 0x52, 0x69,
	0x73, 0x65, 0x63, 0x74, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x27, 0x0a, 0x0f, 0x72, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x31, 0x0a, 0x14, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x13, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x61, 0x72, 0x67, 0x73, 0x18, 0x03, 0x20,
	0x03, 0x28, 0x09, 0x52, 0x04, 0x61, 0x72, 0x67, 0x73, 0x42, 0x35, 0x5a, 0x33, 0x67, 0x69, 0x74,
	0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61,
	0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e,
	0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2f, 0x72, 0x77, 0x63,
	0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_task_rwc_proto_rawDescOnce sync.Once
	file_task_rwc_proto_rawDescData []byte
)

func file_task_rwc_proto_rawDescGZIP() []byte {
	file_task_rwc_proto_rawDescOnce.Do(func() {
		file_task_rwc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_task_rwc_proto_rawDesc), len(file_task_rwc_proto_rawDesc)))
	})
	return file_task_rwc_proto_rawDescData
}

var file_task_rwc_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_task_rwc_proto_goTypes = []any{
	(*RisectlTask)(nil), // 0: task.rwc.RisectlTask
}
var file_task_rwc_proto_depIdxs = []int32{
	0, // [0:0] is the sub-list for method output_type
	0, // [0:0] is the sub-list for method input_type
	0, // [0:0] is the sub-list for extension type_name
	0, // [0:0] is the sub-list for extension extendee
	0, // [0:0] is the sub-list for field type_name
}

func init() { file_task_rwc_proto_init() }
func file_task_rwc_proto_init() {
	if File_task_rwc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_task_rwc_proto_rawDesc), len(file_task_rwc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_task_rwc_proto_goTypes,
		DependencyIndexes: file_task_rwc_proto_depIdxs,
		MessageInfos:      file_task_rwc_proto_msgTypes,
	}.Build()
	File_task_rwc_proto = out.File
	file_task_rwc_proto_goTypes = nil
	file_task_rwc_proto_depIdxs = nil
}
