// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/byoc.proto

package byoc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	ByocResourceManager_CreateApplyByocModuleTask_FullMethodName          = "/services.byoc.ByocResourceManager/CreateApplyByocModuleTask"
	ByocResourceManager_CreateRetrieveByocModuleOutputTask_FullMethodName = "/services.byoc.ByocResourceManager/CreateRetrieveByocModuleOutputTask"
)

// ByocResourceManagerClient is the client API for ByocResourceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ByocResourceManagerClient interface {
	// CreateUpdateByocTask creates a task to update the byoc terraform package.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	CreateApplyByocModuleTask(ctx context.Context, in *CreateApplyByocModuleTaskRequest, opts ...grpc.CallOption) (*CreateApplyByocModuleTaskResponse, error)
	CreateRetrieveByocModuleOutputTask(ctx context.Context, in *CreateRetrieveByocModuleOutputTaskRequest, opts ...grpc.CallOption) (*CreateRetrieveByocModuleOutputTaskResponse, error)
}

type byocResourceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewByocResourceManagerClient(cc grpc.ClientConnInterface) ByocResourceManagerClient {
	return &byocResourceManagerClient{cc}
}

func (c *byocResourceManagerClient) CreateApplyByocModuleTask(ctx context.Context, in *CreateApplyByocModuleTaskRequest, opts ...grpc.CallOption) (*CreateApplyByocModuleTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateApplyByocModuleTaskResponse)
	err := c.cc.Invoke(ctx, ByocResourceManager_CreateApplyByocModuleTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *byocResourceManagerClient) CreateRetrieveByocModuleOutputTask(ctx context.Context, in *CreateRetrieveByocModuleOutputTaskRequest, opts ...grpc.CallOption) (*CreateRetrieveByocModuleOutputTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRetrieveByocModuleOutputTaskResponse)
	err := c.cc.Invoke(ctx, ByocResourceManager_CreateRetrieveByocModuleOutputTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ByocResourceManagerServer is the server API for ByocResourceManager service.
// All implementations must embed UnimplementedByocResourceManagerServer
// for forward compatibility.
type ByocResourceManagerServer interface {
	// CreateUpdateByocTask creates a task to update the byoc terraform package.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	CreateApplyByocModuleTask(context.Context, *CreateApplyByocModuleTaskRequest) (*CreateApplyByocModuleTaskResponse, error)
	CreateRetrieveByocModuleOutputTask(context.Context, *CreateRetrieveByocModuleOutputTaskRequest) (*CreateRetrieveByocModuleOutputTaskResponse, error)
	mustEmbedUnimplementedByocResourceManagerServer()
}

// UnimplementedByocResourceManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedByocResourceManagerServer struct{}

func (UnimplementedByocResourceManagerServer) CreateApplyByocModuleTask(context.Context, *CreateApplyByocModuleTaskRequest) (*CreateApplyByocModuleTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateApplyByocModuleTask not implemented")
}
func (UnimplementedByocResourceManagerServer) CreateRetrieveByocModuleOutputTask(context.Context, *CreateRetrieveByocModuleOutputTaskRequest) (*CreateRetrieveByocModuleOutputTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRetrieveByocModuleOutputTask not implemented")
}
func (UnimplementedByocResourceManagerServer) mustEmbedUnimplementedByocResourceManagerServer() {}
func (UnimplementedByocResourceManagerServer) testEmbeddedByValue()                             {}

// UnsafeByocResourceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ByocResourceManagerServer will
// result in compilation errors.
type UnsafeByocResourceManagerServer interface {
	mustEmbedUnimplementedByocResourceManagerServer()
}

func RegisterByocResourceManagerServer(s grpc.ServiceRegistrar, srv ByocResourceManagerServer) {
	// If the following call pancis, it indicates UnimplementedByocResourceManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&ByocResourceManager_ServiceDesc, srv)
}

func _ByocResourceManager_CreateApplyByocModuleTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateApplyByocModuleTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ByocResourceManagerServer).CreateApplyByocModuleTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ByocResourceManager_CreateApplyByocModuleTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ByocResourceManagerServer).CreateApplyByocModuleTask(ctx, req.(*CreateApplyByocModuleTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ByocResourceManager_CreateRetrieveByocModuleOutputTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRetrieveByocModuleOutputTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ByocResourceManagerServer).CreateRetrieveByocModuleOutputTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: ByocResourceManager_CreateRetrieveByocModuleOutputTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ByocResourceManagerServer).CreateRetrieveByocModuleOutputTask(ctx, req.(*CreateRetrieveByocModuleOutputTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ByocResourceManager_ServiceDesc is the grpc.ServiceDesc for ByocResourceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ByocResourceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.byoc.ByocResourceManager",
	HandlerType: (*ByocResourceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateApplyByocModuleTask",
			Handler:    _ByocResourceManager_CreateApplyByocModuleTask_Handler,
		},
		{
			MethodName: "CreateRetrieveByocModuleOutputTask",
			Handler:    _ByocResourceManager_CreateRetrieveByocModuleOutputTask_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/byoc.proto",
}
