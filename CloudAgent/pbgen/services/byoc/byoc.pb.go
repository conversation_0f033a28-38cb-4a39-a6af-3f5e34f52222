// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/byoc.proto

package byoc

import (
	byoc "github.com/risingwavelabs/cloudagent/pbgen/common/byoc"
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateApplyByocModuleTaskRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta    *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ModuleOptions   *byoc.ModuleOptions    `protobuf:"bytes,2,opt,name=module_options,json=moduleOptions,proto3" json:"module_options,omitempty"`
	ApplyOptions    *byoc.ApplyOptions     `protobuf:"bytes,3,opt,name=apply_options,json=applyOptions,proto3" json:"apply_options,omitempty"`
	PackageOptions  *byoc.PackageOptions   `protobuf:"bytes,4,opt,name=package_options,json=packageOptions,proto3" json:"package_options,omitempty"`
	TaskTolerations []*k8s.Toleration      `protobuf:"bytes,5,rep,name=task_tolerations,json=taskTolerations,proto3" json:"task_tolerations,omitempty"`
	TaskAffinity    *k8s.Affinity          `protobuf:"bytes,6,opt,name=task_affinity,json=taskAffinity,proto3" json:"task_affinity,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateApplyByocModuleTaskRequest) Reset() {
	*x = CreateApplyByocModuleTaskRequest{}
	mi := &file_services_byoc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateApplyByocModuleTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateApplyByocModuleTaskRequest) ProtoMessage() {}

func (x *CreateApplyByocModuleTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_byoc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateApplyByocModuleTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateApplyByocModuleTaskRequest) Descriptor() ([]byte, []int) {
	return file_services_byoc_proto_rawDescGZIP(), []int{0}
}

func (x *CreateApplyByocModuleTaskRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateApplyByocModuleTaskRequest) GetModuleOptions() *byoc.ModuleOptions {
	if x != nil {
		return x.ModuleOptions
	}
	return nil
}

func (x *CreateApplyByocModuleTaskRequest) GetApplyOptions() *byoc.ApplyOptions {
	if x != nil {
		return x.ApplyOptions
	}
	return nil
}

func (x *CreateApplyByocModuleTaskRequest) GetPackageOptions() *byoc.PackageOptions {
	if x != nil {
		return x.PackageOptions
	}
	return nil
}

func (x *CreateApplyByocModuleTaskRequest) GetTaskTolerations() []*k8s.Toleration {
	if x != nil {
		return x.TaskTolerations
	}
	return nil
}

func (x *CreateApplyByocModuleTaskRequest) GetTaskAffinity() *k8s.Affinity {
	if x != nil {
		return x.TaskAffinity
	}
	return nil
}

type CreateApplyByocModuleTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateApplyByocModuleTaskResponse) Reset() {
	*x = CreateApplyByocModuleTaskResponse{}
	mi := &file_services_byoc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateApplyByocModuleTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateApplyByocModuleTaskResponse) ProtoMessage() {}

func (x *CreateApplyByocModuleTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_byoc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateApplyByocModuleTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateApplyByocModuleTaskResponse) Descriptor() ([]byte, []int) {
	return file_services_byoc_proto_rawDescGZIP(), []int{1}
}

func (x *CreateApplyByocModuleTaskResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateRetrieveByocModuleOutputTaskRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta    *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	OutputKey       string                 `protobuf:"bytes,2,opt,name=output_key,json=outputKey,proto3" json:"output_key,omitempty"`
	ModuleOptions   *byoc.ModuleOptions    `protobuf:"bytes,3,opt,name=module_options,json=moduleOptions,proto3" json:"module_options,omitempty"`
	OutputOptions   *byoc.OutputOptions    `protobuf:"bytes,4,opt,name=output_options,json=outputOptions,proto3" json:"output_options,omitempty"`
	PackageOptions  *byoc.PackageOptions   `protobuf:"bytes,5,opt,name=package_options,json=packageOptions,proto3" json:"package_options,omitempty"`
	TaskTolerations []*k8s.Toleration      `protobuf:"bytes,6,rep,name=task_tolerations,json=taskTolerations,proto3" json:"task_tolerations,omitempty"`
	TaskAffinity    *k8s.Affinity          `protobuf:"bytes,7,opt,name=task_affinity,json=taskAffinity,proto3" json:"task_affinity,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) Reset() {
	*x = CreateRetrieveByocModuleOutputTaskRequest{}
	mi := &file_services_byoc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRetrieveByocModuleOutputTaskRequest) ProtoMessage() {}

func (x *CreateRetrieveByocModuleOutputTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_byoc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRetrieveByocModuleOutputTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateRetrieveByocModuleOutputTaskRequest) Descriptor() ([]byte, []int) {
	return file_services_byoc_proto_rawDescGZIP(), []int{2}
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetOutputKey() string {
	if x != nil {
		return x.OutputKey
	}
	return ""
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetModuleOptions() *byoc.ModuleOptions {
	if x != nil {
		return x.ModuleOptions
	}
	return nil
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetOutputOptions() *byoc.OutputOptions {
	if x != nil {
		return x.OutputOptions
	}
	return nil
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetPackageOptions() *byoc.PackageOptions {
	if x != nil {
		return x.PackageOptions
	}
	return nil
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetTaskTolerations() []*k8s.Toleration {
	if x != nil {
		return x.TaskTolerations
	}
	return nil
}

func (x *CreateRetrieveByocModuleOutputTaskRequest) GetTaskAffinity() *k8s.Affinity {
	if x != nil {
		return x.TaskAffinity
	}
	return nil
}

type CreateRetrieveByocModuleOutputTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRetrieveByocModuleOutputTaskResponse) Reset() {
	*x = CreateRetrieveByocModuleOutputTaskResponse{}
	mi := &file_services_byoc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRetrieveByocModuleOutputTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRetrieveByocModuleOutputTaskResponse) ProtoMessage() {}

func (x *CreateRetrieveByocModuleOutputTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_byoc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRetrieveByocModuleOutputTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateRetrieveByocModuleOutputTaskResponse) Descriptor() ([]byte, []int) {
	return file_services_byoc_proto_rawDescGZIP(), []int{3}
}

func (x *CreateRetrieveByocModuleOutputTaskResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_services_byoc_proto protoreflect.FileDescriptor

var file_services_byoc_proto_rawDesc = string([]byte{
	0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x62, 0x79, 0x6f, 0x63, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xa5, 0x03, 0x0a, 0x20, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x75,
	0x6c, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x6d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x3e, 0x0a, 0x0d, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63,
	0x2e, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0c, 0x61,
	0x70, 0x70, 0x6c, 0x79, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a, 0x0f, 0x70,
	0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79,
	0x6f, 0x63, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x41, 0x0a, 0x10, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x61, 0x66, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x79, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x22,
	0x5d, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79,
	0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd0,
	0x03, 0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76,
	0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1d, 0x0a, 0x0a, 0x6f, 0x75, 0x74, 0x70,
	0x75, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x4b, 0x65, 0x79, 0x12, 0x41, 0x0a, 0x0e, 0x6d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d, 0x6d, 0x6f, 0x64,
	0x75, 0x6c, 0x65, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x0e, 0x6f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x62, 0x79, 0x6f, 0x63,
	0x2e, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52, 0x0d,
	0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x44, 0x0a,
	0x0f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x62, 0x79, 0x6f, 0x63, 0x2e, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x52, 0x0e, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x12, 0x41, 0x0a, 0x10, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x6c, 0x65,
	0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x61,
	0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6e,
	0x69, 0x74, 0x79, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x79, 0x22, 0x66, 0x0a, 0x2a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69,
	0x65, 0x76, 0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x75, 0x74,
	0x70, 0x75, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xb6, 0x02, 0x0a, 0x13, 0x42, 0x79,
	0x6f, 0x63, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x12, 0x80, 0x01, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c,
	0x79, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x6f, 0x63, 0x4d,
	0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x62, 0x79, 0x6f, 0x63,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x79, 0x42, 0x79, 0x6f, 0x63,
	0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x9b, 0x01, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c,
	0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x38, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x65, 0x74, 0x72, 0x69, 0x65, 0x76, 0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f,
	0x64, 0x75, 0x6c, 0x65, 0x4f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x62, 0x79, 0x6f, 0x63, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x65, 0x74, 0x72,
	0x69, 0x65, 0x76, 0x65, 0x42, 0x79, 0x6f, 0x63, 0x4d, 0x6f, 0x64, 0x75, 0x6c, 0x65, 0x4f, 0x75,
	0x74, 0x70, 0x75, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x62, 0x79, 0x6f, 0x63, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_byoc_proto_rawDescOnce sync.Once
	file_services_byoc_proto_rawDescData []byte
)

func file_services_byoc_proto_rawDescGZIP() []byte {
	file_services_byoc_proto_rawDescOnce.Do(func() {
		file_services_byoc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_byoc_proto_rawDesc), len(file_services_byoc_proto_rawDesc)))
	})
	return file_services_byoc_proto_rawDescData
}

var file_services_byoc_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_services_byoc_proto_goTypes = []any{
	(*CreateApplyByocModuleTaskRequest)(nil),           // 0: services.byoc.CreateApplyByocModuleTaskRequest
	(*CreateApplyByocModuleTaskResponse)(nil),          // 1: services.byoc.CreateApplyByocModuleTaskResponse
	(*CreateRetrieveByocModuleOutputTaskRequest)(nil),  // 2: services.byoc.CreateRetrieveByocModuleOutputTaskRequest
	(*CreateRetrieveByocModuleOutputTaskResponse)(nil), // 3: services.byoc.CreateRetrieveByocModuleOutputTaskResponse
	(*resource.Meta)(nil),                              // 4: common.resource.Meta
	(*byoc.ModuleOptions)(nil),                         // 5: common.byoc.ModuleOptions
	(*byoc.ApplyOptions)(nil),                          // 6: common.byoc.ApplyOptions
	(*byoc.PackageOptions)(nil),                        // 7: common.byoc.PackageOptions
	(*k8s.Toleration)(nil),                             // 8: common.k8s.Toleration
	(*k8s.Affinity)(nil),                               // 9: common.k8s.Affinity
	(*creation.Status)(nil),                            // 10: common.resource.creation.Status
	(*byoc.OutputOptions)(nil),                         // 11: common.byoc.OutputOptions
}
var file_services_byoc_proto_depIdxs = []int32{
	4,  // 0: services.byoc.CreateApplyByocModuleTaskRequest.resource_meta:type_name -> common.resource.Meta
	5,  // 1: services.byoc.CreateApplyByocModuleTaskRequest.module_options:type_name -> common.byoc.ModuleOptions
	6,  // 2: services.byoc.CreateApplyByocModuleTaskRequest.apply_options:type_name -> common.byoc.ApplyOptions
	7,  // 3: services.byoc.CreateApplyByocModuleTaskRequest.package_options:type_name -> common.byoc.PackageOptions
	8,  // 4: services.byoc.CreateApplyByocModuleTaskRequest.task_tolerations:type_name -> common.k8s.Toleration
	9,  // 5: services.byoc.CreateApplyByocModuleTaskRequest.task_affinity:type_name -> common.k8s.Affinity
	10, // 6: services.byoc.CreateApplyByocModuleTaskResponse.status:type_name -> common.resource.creation.Status
	4,  // 7: services.byoc.CreateRetrieveByocModuleOutputTaskRequest.resource_meta:type_name -> common.resource.Meta
	5,  // 8: services.byoc.CreateRetrieveByocModuleOutputTaskRequest.module_options:type_name -> common.byoc.ModuleOptions
	11, // 9: services.byoc.CreateRetrieveByocModuleOutputTaskRequest.output_options:type_name -> common.byoc.OutputOptions
	7,  // 10: services.byoc.CreateRetrieveByocModuleOutputTaskRequest.package_options:type_name -> common.byoc.PackageOptions
	8,  // 11: services.byoc.CreateRetrieveByocModuleOutputTaskRequest.task_tolerations:type_name -> common.k8s.Toleration
	9,  // 12: services.byoc.CreateRetrieveByocModuleOutputTaskRequest.task_affinity:type_name -> common.k8s.Affinity
	10, // 13: services.byoc.CreateRetrieveByocModuleOutputTaskResponse.status:type_name -> common.resource.creation.Status
	0,  // 14: services.byoc.ByocResourceManager.CreateApplyByocModuleTask:input_type -> services.byoc.CreateApplyByocModuleTaskRequest
	2,  // 15: services.byoc.ByocResourceManager.CreateRetrieveByocModuleOutputTask:input_type -> services.byoc.CreateRetrieveByocModuleOutputTaskRequest
	1,  // 16: services.byoc.ByocResourceManager.CreateApplyByocModuleTask:output_type -> services.byoc.CreateApplyByocModuleTaskResponse
	3,  // 17: services.byoc.ByocResourceManager.CreateRetrieveByocModuleOutputTask:output_type -> services.byoc.CreateRetrieveByocModuleOutputTaskResponse
	16, // [16:18] is the sub-list for method output_type
	14, // [14:16] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_services_byoc_proto_init() }
func file_services_byoc_proto_init() {
	if File_services_byoc_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_byoc_proto_rawDesc), len(file_services_byoc_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_byoc_proto_goTypes,
		DependencyIndexes: file_services_byoc_proto_depIdxs,
		MessageInfos:      file_services_byoc_proto_msgTypes,
	}.Build()
	File_services_byoc_proto = out.File
	file_services_byoc_proto_goTypes = nil
	file_services_byoc_proto_depIdxs = nil
}
