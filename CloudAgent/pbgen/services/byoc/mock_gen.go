// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/byoc (interfaces: ByocResourceManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/byoc -package=byoc -destination=pbgen/services/byoc/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/byoc ByocResourceManagerClient
//

// Package byoc is a generated GoMock package.
package byoc

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockByocResourceManagerClient is a mock of ByocResourceManagerClient interface.
type MockByocResourceManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockByocResourceManagerClientMockRecorder
	isgomock struct{}
}

// MockByocResourceManagerClientMockRecorder is the mock recorder for MockByocResourceManagerClient.
type MockByocResourceManagerClientMockRecorder struct {
	mock *MockByocResourceManagerClient
}

// NewMockByocResourceManagerClient creates a new mock instance.
func NewMockByocResourceManagerClient(ctrl *gomock.Controller) *MockByocResourceManagerClient {
	mock := &MockByocResourceManagerClient{ctrl: ctrl}
	mock.recorder = &MockByocResourceManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockByocResourceManagerClient) EXPECT() *MockByocResourceManagerClientMockRecorder {
	return m.recorder
}

// CreateApplyByocModuleTask mocks base method.
func (m *MockByocResourceManagerClient) CreateApplyByocModuleTask(ctx context.Context, in *CreateApplyByocModuleTaskRequest, opts ...grpc.CallOption) (*CreateApplyByocModuleTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateApplyByocModuleTask", varargs...)
	ret0, _ := ret[0].(*CreateApplyByocModuleTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateApplyByocModuleTask indicates an expected call of CreateApplyByocModuleTask.
func (mr *MockByocResourceManagerClientMockRecorder) CreateApplyByocModuleTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateApplyByocModuleTask", reflect.TypeOf((*MockByocResourceManagerClient)(nil).CreateApplyByocModuleTask), varargs...)
}

// CreateRetrieveByocModuleOutputTask mocks base method.
func (m *MockByocResourceManagerClient) CreateRetrieveByocModuleOutputTask(ctx context.Context, in *CreateRetrieveByocModuleOutputTaskRequest, opts ...grpc.CallOption) (*CreateRetrieveByocModuleOutputTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRetrieveByocModuleOutputTask", varargs...)
	ret0, _ := ret[0].(*CreateRetrieveByocModuleOutputTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRetrieveByocModuleOutputTask indicates an expected call of CreateRetrieveByocModuleOutputTask.
func (mr *MockByocResourceManagerClientMockRecorder) CreateRetrieveByocModuleOutputTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRetrieveByocModuleOutputTask", reflect.TypeOf((*MockByocResourceManagerClient)(nil).CreateRetrieveByocModuleOutputTask), varargs...)
}
