// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/telemetry/prometheus.proto

package prometheus

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// HTTP method of the Promethes API call.
type HTTPMethod int32

const (
	HTTPMethod_METHOD_UNSPECIFIED HTTPMethod = 0
	HTTPMethod_GET                HTTPMethod = 1
	HTTPMethod_POST               HTTPMethod = 2
)

// Enum value maps for HTTPMethod.
var (
	HTTPMethod_name = map[int32]string{
		0: "METHOD_UNSPECIFIED",
		1: "GET",
		2: "POST",
	}
	HTTPMethod_value = map[string]int32{
		"METHOD_UNSPECIFIED": 0,
		"GET":                1,
		"POST":               2,
	}
)

func (x HTTPMethod) Enum() *HTTPMethod {
	p := new(HTTPMethod)
	*p = x
	return p
}

func (x HTTPMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HTTPMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_services_telemetry_prometheus_proto_enumTypes[0].Descriptor()
}

func (HTTPMethod) Type() protoreflect.EnumType {
	return &file_services_telemetry_prometheus_proto_enumTypes[0]
}

func (x HTTPMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HTTPMethod.Descriptor instead.
func (HTTPMethod) EnumDescriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{0}
}

// Endpoint of the Promethes API call.
// Reference: https://prometheus.io/docs/prometheus/latest/querying/api/
type Endpoint int32

const (
	Endpoint_ENDPOINT_UNSPECIFIED Endpoint = 0
	Endpoint_QUERY                Endpoint = 1
	Endpoint_QUERY_RANGE          Endpoint = 2
	Endpoint_SERIES               Endpoint = 3
	Endpoint_LABEL_VALUES         Endpoint = 4
	Endpoint_LABELS               Endpoint = 5
)

// Enum value maps for Endpoint.
var (
	Endpoint_name = map[int32]string{
		0: "ENDPOINT_UNSPECIFIED",
		1: "QUERY",
		2: "QUERY_RANGE",
		3: "SERIES",
		4: "LABEL_VALUES",
		5: "LABELS",
	}
	Endpoint_value = map[string]int32{
		"ENDPOINT_UNSPECIFIED": 0,
		"QUERY":                1,
		"QUERY_RANGE":          2,
		"SERIES":               3,
		"LABEL_VALUES":         4,
		"LABELS":               5,
	}
)

func (x Endpoint) Enum() *Endpoint {
	p := new(Endpoint)
	*p = x
	return p
}

func (x Endpoint) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Endpoint) Descriptor() protoreflect.EnumDescriptor {
	return file_services_telemetry_prometheus_proto_enumTypes[1].Descriptor()
}

func (Endpoint) Type() protoreflect.EnumType {
	return &file_services_telemetry_prometheus_proto_enumTypes[1]
}

func (x Endpoint) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Endpoint.Descriptor instead.
func (Endpoint) EnumDescriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{1}
}

type ProxyRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	HttpMethod     HTTPMethod             `protobuf:"varint,1,opt,name=http_method,json=httpMethod,proto3,enum=services.telemetry.prometheus.HTTPMethod" json:"http_method,omitempty"`
	Endpoint       Endpoint               `protobuf:"varint,2,opt,name=endpoint,proto3,enum=services.telemetry.prometheus.Endpoint" json:"endpoint,omitempty"`
	Payload        []byte                 `protobuf:"bytes,3,opt,name=payload,proto3" json:"payload,omitempty"`
	EndpointParams *EndpointParams        `protobuf:"bytes,4,opt,name=endpoint_params,json=endpointParams,proto3" json:"endpoint_params,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *ProxyRequest) Reset() {
	*x = ProxyRequest{}
	mi := &file_services_telemetry_prometheus_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProxyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProxyRequest) ProtoMessage() {}

func (x *ProxyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_telemetry_prometheus_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProxyRequest.ProtoReflect.Descriptor instead.
func (*ProxyRequest) Descriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{0}
}

func (x *ProxyRequest) GetHttpMethod() HTTPMethod {
	if x != nil {
		return x.HttpMethod
	}
	return HTTPMethod_METHOD_UNSPECIFIED
}

func (x *ProxyRequest) GetEndpoint() Endpoint {
	if x != nil {
		return x.Endpoint
	}
	return Endpoint_ENDPOINT_UNSPECIFIED
}

func (x *ProxyRequest) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ProxyRequest) GetEndpointParams() *EndpointParams {
	if x != nil {
		return x.EndpointParams
	}
	return nil
}

type ProxyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	StatusCode    uint32                 `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	Payload       []byte                 `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ProxyResponse) Reset() {
	*x = ProxyResponse{}
	mi := &file_services_telemetry_prometheus_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ProxyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ProxyResponse) ProtoMessage() {}

func (x *ProxyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_telemetry_prometheus_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ProxyResponse.ProtoReflect.Descriptor instead.
func (*ProxyResponse) Descriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{1}
}

func (x *ProxyResponse) GetStatusCode() uint32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *ProxyResponse) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

type LabelValuesEndpointParams struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	LabelName     string                 `protobuf:"bytes,1,opt,name=label_name,json=labelName,proto3" json:"label_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelValuesEndpointParams) Reset() {
	*x = LabelValuesEndpointParams{}
	mi := &file_services_telemetry_prometheus_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelValuesEndpointParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelValuesEndpointParams) ProtoMessage() {}

func (x *LabelValuesEndpointParams) ProtoReflect() protoreflect.Message {
	mi := &file_services_telemetry_prometheus_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelValuesEndpointParams.ProtoReflect.Descriptor instead.
func (*LabelValuesEndpointParams) Descriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{2}
}

func (x *LabelValuesEndpointParams) GetLabelName() string {
	if x != nil {
		return x.LabelName
	}
	return ""
}

type EndpointParams struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Params:
	//
	//	*EndpointParams_LabelValuesParams
	Params        isEndpointParams_Params `protobuf_oneof:"params"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EndpointParams) Reset() {
	*x = EndpointParams{}
	mi := &file_services_telemetry_prometheus_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EndpointParams) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EndpointParams) ProtoMessage() {}

func (x *EndpointParams) ProtoReflect() protoreflect.Message {
	mi := &file_services_telemetry_prometheus_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EndpointParams.ProtoReflect.Descriptor instead.
func (*EndpointParams) Descriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{3}
}

func (x *EndpointParams) GetParams() isEndpointParams_Params {
	if x != nil {
		return x.Params
	}
	return nil
}

func (x *EndpointParams) GetLabelValuesParams() *LabelValuesEndpointParams {
	if x != nil {
		if x, ok := x.Params.(*EndpointParams_LabelValuesParams); ok {
			return x.LabelValuesParams
		}
	}
	return nil
}

type isEndpointParams_Params interface {
	isEndpointParams_Params()
}

type EndpointParams_LabelValuesParams struct {
	LabelValuesParams *LabelValuesEndpointParams `protobuf:"bytes,1,opt,name=label_values_params,json=labelValuesParams,proto3,oneof"`
}

func (*EndpointParams_LabelValuesParams) isEndpointParams_Params() {}

type ScrapeRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Namespace string                 `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	// If set, the scraping request should accept the specified encoding method
	// the request header.
	AcceptEncodingHeader string `protobuf:"bytes,2,opt,name=accept_encoding_header,json=acceptEncodingHeader,proto3" json:"accept_encoding_header,omitempty"`
	// Add query parameters to filter metrics
	// Note that `include` and `exclude` should not be used together.
	Include         []string `protobuf:"bytes,3,rep,name=include,proto3" json:"include,omitempty"`
	Exclude         []string `protobuf:"bytes,4,rep,name=exclude,proto3" json:"exclude,omitempty"`
	MaxResponseSize uint32   `protobuf:"varint,5,opt,name=max_response_size,json=maxResponseSize,proto3" json:"max_response_size,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ScrapeRequest) Reset() {
	*x = ScrapeRequest{}
	mi := &file_services_telemetry_prometheus_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScrapeRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrapeRequest) ProtoMessage() {}

func (x *ScrapeRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_telemetry_prometheus_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrapeRequest.ProtoReflect.Descriptor instead.
func (*ScrapeRequest) Descriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{4}
}

func (x *ScrapeRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *ScrapeRequest) GetAcceptEncodingHeader() string {
	if x != nil {
		return x.AcceptEncodingHeader
	}
	return ""
}

func (x *ScrapeRequest) GetInclude() []string {
	if x != nil {
		return x.Include
	}
	return nil
}

func (x *ScrapeRequest) GetExclude() []string {
	if x != nil {
		return x.Exclude
	}
	return nil
}

func (x *ScrapeRequest) GetMaxResponseSize() uint32 {
	if x != nil {
		return x.MaxResponseSize
	}
	return 0
}

type ScrapeResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Status code.
	//
	// Deprecated: Marked as deprecated in services/telemetry/prometheus.proto.
	StatusCode uint32 `protobuf:"varint,1,opt,name=status_code,json=statusCode,proto3" json:"status_code,omitempty"`
	// Metrics bytes data.
	Payload []byte `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	// The format of metrics data, need to negotiate the encoder based on the
	// content type.
	ContentTypeHeader string `protobuf:"bytes,3,opt,name=content_type_header,json=contentTypeHeader,proto3" json:"content_type_header,omitempty"`
	// If set, the payload is encoded in the specified encoding method.
	ContentEncodingHeader string `protobuf:"bytes,4,opt,name=content_encoding_header,json=contentEncodingHeader,proto3" json:"content_encoding_header,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *ScrapeResponse) Reset() {
	*x = ScrapeResponse{}
	mi := &file_services_telemetry_prometheus_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScrapeResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScrapeResponse) ProtoMessage() {}

func (x *ScrapeResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_telemetry_prometheus_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScrapeResponse.ProtoReflect.Descriptor instead.
func (*ScrapeResponse) Descriptor() ([]byte, []int) {
	return file_services_telemetry_prometheus_proto_rawDescGZIP(), []int{5}
}

// Deprecated: Marked as deprecated in services/telemetry/prometheus.proto.
func (x *ScrapeResponse) GetStatusCode() uint32 {
	if x != nil {
		return x.StatusCode
	}
	return 0
}

func (x *ScrapeResponse) GetPayload() []byte {
	if x != nil {
		return x.Payload
	}
	return nil
}

func (x *ScrapeResponse) GetContentTypeHeader() string {
	if x != nil {
		return x.ContentTypeHeader
	}
	return ""
}

func (x *ScrapeResponse) GetContentEncodingHeader() string {
	if x != nil {
		return x.ContentEncodingHeader
	}
	return ""
}

var File_services_telemetry_prometheus_proto protoreflect.FileDescriptor

var file_services_telemetry_prometheus_proto_rawDesc = string([]byte{
	0x0a, 0x23, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x74, 0x72, 0x79, 0x2f, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74,
	0x68, 0x65, 0x75, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x0c, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x4a, 0x0a, 0x0b, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x6d, 0x65,
	0x74, 0x68, 0x6f, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x0a, 0x68, 0x74, 0x74, 0x70, 0x4d, 0x65, 0x74, 0x68, 0x6f,
	0x64, 0x12, 0x43, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68,
	0x65, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61, 0x64,
	0x12, 0x56, 0x0a, 0x0f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x70, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x52, 0x0e, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0x4a, 0x0a, 0x0d, 0x50, 0x72, 0x6f, 0x78,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0a,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61,
	0x79, 0x6c, 0x6f, 0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x79,
	0x6c, 0x6f, 0x61, 0x64, 0x22, 0x3a, 0x0a, 0x19, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d,
	0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x86, 0x01, 0x0a, 0x0e, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72,
	0x61, 0x6d, 0x73, 0x12, 0x6a, 0x0a, 0x13, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x73, 0x5f, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x38, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73,
	0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x48, 0x00, 0x52, 0x11, 0x6c, 0x61,
	0x62, 0x65, 0x6c, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x50, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x42,
	0x08, 0x0a, 0x06, 0x70, 0x61, 0x72, 0x61, 0x6d, 0x73, 0x22, 0xc3, 0x01, 0x0a, 0x0d, 0x53, 0x63,
	0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x34, 0x0a, 0x16, 0x61, 0x63, 0x63,
	0x65, 0x70, 0x74, 0x5f, 0x65, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x65, 0x61,
	0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x14, 0x61, 0x63, 0x63, 0x65, 0x70,
	0x74, 0x45, 0x6e, 0x63, 0x6f, 0x64, 0x69, 0x6e, 0x67, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x12,
	0x18, 0x0a, 0x07, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x07, 0x69, 0x6e, 0x63, 0x6c, 0x75, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x65, 0x78, 0x63,
	0x6c, 0x75, 0x64, 0x65, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x65, 0x78, 0x63, 0x6c,
	0x75, 0x64, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x6d, 0x61, 0x78, 0x5f, 0x72, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x0f,
	0x6d, 0x61, 0x78, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22,
	0xb7, 0x01, 0x0a, 0x0e, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x64,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x42, 0x02, 0x18, 0x01, 0x52, 0x0a, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f,
	0x61, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07, 0x70, 0x61, 0x79, 0x6c, 0x6f, 0x61,
	0x64, 0x12, 0x2e, 0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x48, 0x65, 0x61, 0x64, 0x65,
	0x72, 0x12, 0x36, 0x0a, 0x17, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x5f, 0x65, 0x6e, 0x63,
	0x6f, 0x64, 0x69, 0x6e, 0x67, 0x5f, 0x68, 0x65, 0x61, 0x64, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x15, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x45, 0x6e, 0x63, 0x6f, 0x64,
	0x69, 0x6e, 0x67, 0x48, 0x65, 0x61, 0x64, 0x65, 0x72, 0x2a, 0x37, 0x0a, 0x0a, 0x48, 0x54, 0x54,
	0x50, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x16, 0x0a, 0x12, 0x4d, 0x45, 0x54, 0x48, 0x4f,
	0x44, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12,
	0x07, 0x0a, 0x03, 0x47, 0x45, 0x54, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x50, 0x4f, 0x53, 0x54,
	0x10, 0x02, 0x2a, 0x6a, 0x0a, 0x08, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x18,
	0x0a, 0x14, 0x45, 0x4e, 0x44, 0x50, 0x4f, 0x49, 0x4e, 0x54, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x51, 0x55, 0x45, 0x52,
	0x59, 0x10, 0x01, 0x12, 0x0f, 0x0a, 0x0b, 0x51, 0x55, 0x45, 0x52, 0x59, 0x5f, 0x52, 0x41, 0x4e,
	0x47, 0x45, 0x10, 0x02, 0x12, 0x0a, 0x0a, 0x06, 0x53, 0x45, 0x52, 0x49, 0x45, 0x53, 0x10, 0x03,
	0x12, 0x10, 0x0a, 0x0c, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x5f, 0x56, 0x41, 0x4c, 0x55, 0x45, 0x53,
	0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x4c, 0x41, 0x42, 0x45, 0x4c, 0x53, 0x10, 0x05, 0x32, 0xdb,
	0x01, 0x0a, 0x0a, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x12, 0x64, 0x0a,
	0x05, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x12, 0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x6d,
	0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68,
	0x65, 0x75, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x78, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x67, 0x0a, 0x06, 0x53, 0x63, 0x72, 0x61, 0x70, 0x65, 0x12, 0x2c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74,
	0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x53, 0x63,
	0x72, 0x61, 0x70, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79,
	0x2e, 0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x53, 0x63, 0x72, 0x61,
	0x70, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x4a, 0x5a, 0x48,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2f, 0x70, 0x72,
	0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_telemetry_prometheus_proto_rawDescOnce sync.Once
	file_services_telemetry_prometheus_proto_rawDescData []byte
)

func file_services_telemetry_prometheus_proto_rawDescGZIP() []byte {
	file_services_telemetry_prometheus_proto_rawDescOnce.Do(func() {
		file_services_telemetry_prometheus_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_telemetry_prometheus_proto_rawDesc), len(file_services_telemetry_prometheus_proto_rawDesc)))
	})
	return file_services_telemetry_prometheus_proto_rawDescData
}

var file_services_telemetry_prometheus_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_services_telemetry_prometheus_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_services_telemetry_prometheus_proto_goTypes = []any{
	(HTTPMethod)(0),                   // 0: services.telemetry.prometheus.HTTPMethod
	(Endpoint)(0),                     // 1: services.telemetry.prometheus.Endpoint
	(*ProxyRequest)(nil),              // 2: services.telemetry.prometheus.ProxyRequest
	(*ProxyResponse)(nil),             // 3: services.telemetry.prometheus.ProxyResponse
	(*LabelValuesEndpointParams)(nil), // 4: services.telemetry.prometheus.LabelValuesEndpointParams
	(*EndpointParams)(nil),            // 5: services.telemetry.prometheus.EndpointParams
	(*ScrapeRequest)(nil),             // 6: services.telemetry.prometheus.ScrapeRequest
	(*ScrapeResponse)(nil),            // 7: services.telemetry.prometheus.ScrapeResponse
}
var file_services_telemetry_prometheus_proto_depIdxs = []int32{
	0, // 0: services.telemetry.prometheus.ProxyRequest.http_method:type_name -> services.telemetry.prometheus.HTTPMethod
	1, // 1: services.telemetry.prometheus.ProxyRequest.endpoint:type_name -> services.telemetry.prometheus.Endpoint
	5, // 2: services.telemetry.prometheus.ProxyRequest.endpoint_params:type_name -> services.telemetry.prometheus.EndpointParams
	4, // 3: services.telemetry.prometheus.EndpointParams.label_values_params:type_name -> services.telemetry.prometheus.LabelValuesEndpointParams
	2, // 4: services.telemetry.prometheus.Prometheus.Proxy:input_type -> services.telemetry.prometheus.ProxyRequest
	6, // 5: services.telemetry.prometheus.Prometheus.Scrape:input_type -> services.telemetry.prometheus.ScrapeRequest
	3, // 6: services.telemetry.prometheus.Prometheus.Proxy:output_type -> services.telemetry.prometheus.ProxyResponse
	7, // 7: services.telemetry.prometheus.Prometheus.Scrape:output_type -> services.telemetry.prometheus.ScrapeResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_services_telemetry_prometheus_proto_init() }
func file_services_telemetry_prometheus_proto_init() {
	if File_services_telemetry_prometheus_proto != nil {
		return
	}
	file_services_telemetry_prometheus_proto_msgTypes[3].OneofWrappers = []any{
		(*EndpointParams_LabelValuesParams)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_telemetry_prometheus_proto_rawDesc), len(file_services_telemetry_prometheus_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_telemetry_prometheus_proto_goTypes,
		DependencyIndexes: file_services_telemetry_prometheus_proto_depIdxs,
		EnumInfos:         file_services_telemetry_prometheus_proto_enumTypes,
		MessageInfos:      file_services_telemetry_prometheus_proto_msgTypes,
	}.Build()
	File_services_telemetry_prometheus_proto = out.File
	file_services_telemetry_prometheus_proto_goTypes = nil
	file_services_telemetry_prometheus_proto_depIdxs = nil
}
