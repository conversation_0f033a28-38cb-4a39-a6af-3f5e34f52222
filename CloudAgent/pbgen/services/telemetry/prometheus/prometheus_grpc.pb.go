// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/telemetry/prometheus.proto

package prometheus

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	Prometheus_Proxy_FullMethodName  = "/services.telemetry.prometheus.Prometheus/Proxy"
	Prometheus_Scrape_FullMethodName = "/services.telemetry.prometheus.Prometheus/Scrape"
)

// PrometheusClient is the client API for Prometheus service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PrometheusClient interface {
	// Proxy sends the prometheus request to cluster's prometheus compatible
	// endpoint and returns the response payload and code.
	Proxy(ctx context.Context, in *ProxyRequest, opts ...grpc.CallOption) (*ProxyResponse, error)
	// Scrape will scrape the metrics endpoint under user namespace.
	Scrape(ctx context.Context, in *ScrapeRequest, opts ...grpc.CallOption) (*ScrapeResponse, error)
}

type prometheusClient struct {
	cc grpc.ClientConnInterface
}

func NewPrometheusClient(cc grpc.ClientConnInterface) PrometheusClient {
	return &prometheusClient{cc}
}

func (c *prometheusClient) Proxy(ctx context.Context, in *ProxyRequest, opts ...grpc.CallOption) (*ProxyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ProxyResponse)
	err := c.cc.Invoke(ctx, Prometheus_Proxy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *prometheusClient) Scrape(ctx context.Context, in *ScrapeRequest, opts ...grpc.CallOption) (*ScrapeResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScrapeResponse)
	err := c.cc.Invoke(ctx, Prometheus_Scrape_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PrometheusServer is the server API for Prometheus service.
// All implementations must embed UnimplementedPrometheusServer
// for forward compatibility.
type PrometheusServer interface {
	// Proxy sends the prometheus request to cluster's prometheus compatible
	// endpoint and returns the response payload and code.
	Proxy(context.Context, *ProxyRequest) (*ProxyResponse, error)
	// Scrape will scrape the metrics endpoint under user namespace.
	Scrape(context.Context, *ScrapeRequest) (*ScrapeResponse, error)
	mustEmbedUnimplementedPrometheusServer()
}

// UnimplementedPrometheusServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPrometheusServer struct{}

func (UnimplementedPrometheusServer) Proxy(context.Context, *ProxyRequest) (*ProxyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Proxy not implemented")
}
func (UnimplementedPrometheusServer) Scrape(context.Context, *ScrapeRequest) (*ScrapeResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method Scrape not implemented")
}
func (UnimplementedPrometheusServer) mustEmbedUnimplementedPrometheusServer() {}
func (UnimplementedPrometheusServer) testEmbeddedByValue()                    {}

// UnsafePrometheusServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PrometheusServer will
// result in compilation errors.
type UnsafePrometheusServer interface {
	mustEmbedUnimplementedPrometheusServer()
}

func RegisterPrometheusServer(s grpc.ServiceRegistrar, srv PrometheusServer) {
	// If the following call pancis, it indicates UnimplementedPrometheusServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&Prometheus_ServiceDesc, srv)
}

func _Prometheus_Proxy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ProxyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrometheusServer).Proxy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Prometheus_Proxy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrometheusServer).Proxy(ctx, req.(*ProxyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Prometheus_Scrape_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScrapeRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PrometheusServer).Scrape(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: Prometheus_Scrape_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PrometheusServer).Scrape(ctx, req.(*ScrapeRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Prometheus_ServiceDesc is the grpc.ServiceDesc for Prometheus service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Prometheus_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.telemetry.prometheus.Prometheus",
	HandlerType: (*PrometheusServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "Proxy",
			Handler:    _Prometheus_Proxy_Handler,
		},
		{
			MethodName: "Scrape",
			Handler:    _Prometheus_Scrape_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/telemetry/prometheus.proto",
}
