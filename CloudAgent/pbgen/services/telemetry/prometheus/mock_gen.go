// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus (interfaces: PrometheusClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus -package=prometheus -destination=pbgen/services/telemetry/prometheus/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus PrometheusClient
//

// Package prometheus is a generated GoMock package.
package prometheus

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPrometheusClient is a mock of PrometheusClient interface.
type MockPrometheusClient struct {
	ctrl     *gomock.Controller
	recorder *MockPrometheusClientMockRecorder
	isgomock struct{}
}

// MockPrometheusClientMockRecorder is the mock recorder for MockPrometheusClient.
type MockPrometheusClientMockRecorder struct {
	mock *MockPrometheusClient
}

// NewMockPrometheusClient creates a new mock instance.
func NewMockPrometheusClient(ctrl *gomock.Controller) *MockPrometheusClient {
	mock := &MockPrometheusClient{ctrl: ctrl}
	mock.recorder = &MockPrometheusClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPrometheusClient) EXPECT() *MockPrometheusClientMockRecorder {
	return m.recorder
}

// Proxy mocks base method.
func (m *MockPrometheusClient) Proxy(ctx context.Context, in *ProxyRequest, opts ...grpc.CallOption) (*ProxyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Proxy", varargs...)
	ret0, _ := ret[0].(*ProxyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Proxy indicates an expected call of Proxy.
func (mr *MockPrometheusClientMockRecorder) Proxy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Proxy", reflect.TypeOf((*MockPrometheusClient)(nil).Proxy), varargs...)
}

// Scrape mocks base method.
func (m *MockPrometheusClient) Scrape(ctx context.Context, in *ScrapeRequest, opts ...grpc.CallOption) (*ScrapeResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Scrape", varargs...)
	ret0, _ := ret[0].(*ScrapeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Scrape indicates an expected call of Scrape.
func (mr *MockPrometheusClientMockRecorder) Scrape(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scrape", reflect.TypeOf((*MockPrometheusClient)(nil).Scrape), varargs...)
}
