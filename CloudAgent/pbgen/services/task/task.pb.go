// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/task.proto

package task

import (
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	deletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	task "github.com/risingwavelabs/cloudagent/pbgen/common/resource/task"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Note: Tasks are not namespaced, the namespace value will be ignored.
type GetTaskStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskStatusRequest) Reset() {
	*x = GetTaskStatusRequest{}
	mi := &file_services_task_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskStatusRequest) ProtoMessage() {}

func (x *GetTaskStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_task_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskStatusRequest.ProtoReflect.Descriptor instead.
func (*GetTaskStatusRequest) Descriptor() ([]byte, []int) {
	return file_services_task_proto_rawDescGZIP(), []int{0}
}

func (x *GetTaskStatusRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetTaskStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *task.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetTaskStatusResponse) Reset() {
	*x = GetTaskStatusResponse{}
	mi := &file_services_task_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetTaskStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTaskStatusResponse) ProtoMessage() {}

func (x *GetTaskStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_task_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTaskStatusResponse.ProtoReflect.Descriptor instead.
func (*GetTaskStatusResponse) Descriptor() ([]byte, []int) {
	return file_services_task_proto_rawDescGZIP(), []int{1}
}

func (x *GetTaskStatusResponse) GetStatus() *task.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Note: Tasks are not namespaced, the namespace value will be ignored.
type CleanupTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanupTaskRequest) Reset() {
	*x = CleanupTaskRequest{}
	mi := &file_services_task_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanupTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupTaskRequest) ProtoMessage() {}

func (x *CleanupTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_task_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupTaskRequest.ProtoReflect.Descriptor instead.
func (*CleanupTaskRequest) Descriptor() ([]byte, []int) {
	return file_services_task_proto_rawDescGZIP(), []int{2}
}

func (x *CleanupTaskRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type CleanupTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CleanupTaskResponse) Reset() {
	*x = CleanupTaskResponse{}
	mi := &file_services_task_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CleanupTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CleanupTaskResponse) ProtoMessage() {}

func (x *CleanupTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_task_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CleanupTaskResponse.ProtoReflect.Descriptor instead.
func (*CleanupTaskResponse) Descriptor() ([]byte, []int) {
	return file_services_task_proto_rawDescGZIP(), []int{3}
}

func (x *CleanupTaskResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

var File_services_task_proto protoreflect.FileDescriptor

var file_services_task_proto_rawDesc = string([]byte{
	0x0a, 0x13, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x74, 0x61, 0x73, 0x6b, 0x1a, 0x11, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x4d, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x34, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x50, 0x0a, 0x12, 0x43, 0x6c, 0x65, 0x61,
	0x6e, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x4f, 0x0a, 0x13, 0x43, 0x6c,
	0x65, 0x61, 0x6e, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x32, 0xc3, 0x01, 0x0a, 0x0b,
	0x54, 0x61, 0x73, 0x6b, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72, 0x12, 0x5c, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x47, 0x65, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x61, 0x73,
	0x6b, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0b, 0x43, 0x6c, 0x65,
	0x61, 0x6e, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x43, 0x6c, 0x65, 0x61, 0x6e, 0x75, 0x70,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x74, 0x61, 0x73, 0x6b, 0x2e, 0x43, 0x6c, 0x65, 0x61,
	0x6e, 0x75, 0x70, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f,
	0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63,
	0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x74, 0x61, 0x73, 0x6b, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_task_proto_rawDescOnce sync.Once
	file_services_task_proto_rawDescData []byte
)

func file_services_task_proto_rawDescGZIP() []byte {
	file_services_task_proto_rawDescOnce.Do(func() {
		file_services_task_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_task_proto_rawDesc), len(file_services_task_proto_rawDesc)))
	})
	return file_services_task_proto_rawDescData
}

var file_services_task_proto_msgTypes = make([]protoimpl.MessageInfo, 4)
var file_services_task_proto_goTypes = []any{
	(*GetTaskStatusRequest)(nil),  // 0: services.task.GetTaskStatusRequest
	(*GetTaskStatusResponse)(nil), // 1: services.task.GetTaskStatusResponse
	(*CleanupTaskRequest)(nil),    // 2: services.task.CleanupTaskRequest
	(*CleanupTaskResponse)(nil),   // 3: services.task.CleanupTaskResponse
	(*resource.Meta)(nil),         // 4: common.resource.Meta
	(*task.Status)(nil),           // 5: common.resource.task.Status
	(*deletion.Status)(nil),       // 6: common.resource.deletion.Status
}
var file_services_task_proto_depIdxs = []int32{
	4, // 0: services.task.GetTaskStatusRequest.resource_meta:type_name -> common.resource.Meta
	5, // 1: services.task.GetTaskStatusResponse.status:type_name -> common.resource.task.Status
	4, // 2: services.task.CleanupTaskRequest.resource_meta:type_name -> common.resource.Meta
	6, // 3: services.task.CleanupTaskResponse.status:type_name -> common.resource.deletion.Status
	0, // 4: services.task.TaskManager.GetTaskStatus:input_type -> services.task.GetTaskStatusRequest
	2, // 5: services.task.TaskManager.CleanupTask:input_type -> services.task.CleanupTaskRequest
	1, // 6: services.task.TaskManager.GetTaskStatus:output_type -> services.task.GetTaskStatusResponse
	3, // 7: services.task.TaskManager.CleanupTask:output_type -> services.task.CleanupTaskResponse
	6, // [6:8] is the sub-list for method output_type
	4, // [4:6] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_services_task_proto_init() }
func file_services_task_proto_init() {
	if File_services_task_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_task_proto_rawDesc), len(file_services_task_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   4,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_task_proto_goTypes,
		DependencyIndexes: file_services_task_proto_depIdxs,
		MessageInfos:      file_services_task_proto_msgTypes,
	}.Build()
	File_services_task_proto = out.File
	file_services_task_proto_goTypes = nil
	file_services_task_proto_depIdxs = nil
}
