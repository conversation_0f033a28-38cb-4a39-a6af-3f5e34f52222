// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/task (interfaces: TaskManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/task -package=task -destination=pbgen/services/task/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/task TaskManagerClient
//

// Package task is a generated GoMock package.
package task

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockTaskManagerClient is a mock of TaskManagerClient interface.
type MockTaskManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockTaskManagerClientMockRecorder
	isgomock struct{}
}

// MockTaskManagerClientMockRecorder is the mock recorder for MockTaskManagerClient.
type MockTaskManagerClientMockRecorder struct {
	mock *MockTaskManagerClient
}

// NewMockTaskManagerClient creates a new mock instance.
func NewMockTaskManagerClient(ctrl *gomock.Controller) *MockTaskManagerClient {
	mock := &MockTaskManagerClient{ctrl: ctrl}
	mock.recorder = &MockTaskManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockTaskManagerClient) EXPECT() *MockTaskManagerClientMockRecorder {
	return m.recorder
}

// CleanupTask mocks base method.
func (m *MockTaskManagerClient) CleanupTask(ctx context.Context, in *CleanupTaskRequest, opts ...grpc.CallOption) (*CleanupTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CleanupTask", varargs...)
	ret0, _ := ret[0].(*CleanupTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CleanupTask indicates an expected call of CleanupTask.
func (mr *MockTaskManagerClientMockRecorder) CleanupTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CleanupTask", reflect.TypeOf((*MockTaskManagerClient)(nil).CleanupTask), varargs...)
}

// GetTaskStatus mocks base method.
func (m *MockTaskManagerClient) GetTaskStatus(ctx context.Context, in *GetTaskStatusRequest, opts ...grpc.CallOption) (*GetTaskStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetTaskStatus", varargs...)
	ret0, _ := ret[0].(*GetTaskStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStatus indicates an expected call of GetTaskStatus.
func (mr *MockTaskManagerClientMockRecorder) GetTaskStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStatus", reflect.TypeOf((*MockTaskManagerClient)(nil).GetTaskStatus), varargs...)
}
