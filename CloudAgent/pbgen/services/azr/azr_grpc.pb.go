// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/azr.proto

package azr

import (
	context "context"
	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AzrResourceManager_CreateUserAssignedIdentity_FullMethodName        = "/services.azr.AzrResourceManager/CreateUserAssignedIdentity"
	AzrResourceManager_DeleteUserAssignedIdentity_FullMethodName        = "/services.azr.AzrResourceManager/DeleteUserAssignedIdentity"
	AzrResourceManager_GetUserAssignedIdentity_FullMethodName           = "/services.azr.AzrResourceManager/GetUserAssignedIdentity"
	AzrResourceManager_CreateDataDirectoryDeletionTask_FullMethodName   = "/services.azr.AzrResourceManager/CreateDataDirectoryDeletionTask"
	AzrResourceManager_CreateDataDirectoryCloneTask_FullMethodName      = "/services.azr.AzrResourceManager/CreateDataDirectoryCloneTask"
	AzrResourceManager_CreateFederatedIdentityCredential_FullMethodName = "/services.azr.AzrResourceManager/CreateFederatedIdentityCredential"
	AzrResourceManager_DeleteFederatedIdentityCredential_FullMethodName = "/services.azr.AzrResourceManager/DeleteFederatedIdentityCredential"
	AzrResourceManager_GetFederatedIdentityCredential_FullMethodName    = "/services.azr.AzrResourceManager/GetFederatedIdentityCredential"
	AzrResourceManager_CreateRoleAssignment_FullMethodName              = "/services.azr.AzrResourceManager/CreateRoleAssignment"
	AzrResourceManager_DeleteRoleAssignment_FullMethodName              = "/services.azr.AzrResourceManager/DeleteRoleAssignment"
	AzrResourceManager_GetRoleAssignment_FullMethodName                 = "/services.azr.AzrResourceManager/GetRoleAssignment"
	AzrResourceManager_CreatePrivateEndpoint_FullMethodName             = "/services.azr.AzrResourceManager/CreatePrivateEndpoint"
	AzrResourceManager_DeletePrivateEndpoint_FullMethodName             = "/services.azr.AzrResourceManager/DeletePrivateEndpoint"
	AzrResourceManager_GetPrivateEndpoint_FullMethodName                = "/services.azr.AzrResourceManager/GetPrivateEndpoint"
	AzrResourceManager_CreatePGServer_FullMethodName                    = "/services.azr.AzrResourceManager/CreatePGServer"
	AzrResourceManager_DeletePGServer_FullMethodName                    = "/services.azr.AzrResourceManager/DeletePGServer"
	AzrResourceManager_StartPGServer_FullMethodName                     = "/services.azr.AzrResourceManager/StartPGServer"
	AzrResourceManager_StopPGServer_FullMethodName                      = "/services.azr.AzrResourceManager/StopPGServer"
	AzrResourceManager_GetPGServer_FullMethodName                       = "/services.azr.AzrResourceManager/GetPGServer"
	AzrResourceManager_GetManifest_FullMethodName                       = "/services.azr.AzrResourceManager/GetManifest"
)

// AzrResourceManagerClient is the client API for AzrResourceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AzrResourceManagerClient interface {
	// Expected a SCHEDULED status on success.
	CreateUserAssignedIdentity(ctx context.Context, in *CreateUserAssignedIdentityRequest, opts ...grpc.CallOption) (*CreateUserAssignedIdentityResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteUserAssignedIdentity(ctx context.Context, in *DeleteUserAssignedIdentityRequest, opts ...grpc.CallOption) (*DeleteUserAssignedIdentityResponse, error)
	GetUserAssignedIdentity(ctx context.Context, in *GetUserAssignedIdentityRequest, opts ...grpc.CallOption) (*GetUserAssignedIdentityResponse, error)
	// CreateDataDirectoryDeletionTask creates a task for AZR folder deletion.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error)
	CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateFederatedIdentityCredential(ctx context.Context, in *CreateFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*CreateFederatedIdentityCredentialResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteFederatedIdentityCredential(ctx context.Context, in *DeleteFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*DeleteFederatedIdentityCredentialResponse, error)
	GetFederatedIdentityCredential(ctx context.Context, in *GetFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*GetFederatedIdentityCredentialResponse, error)
	// Expected a SCHEDULED status on success.
	CreateRoleAssignment(ctx context.Context, in *CreateRoleAssignmentRequest, opts ...grpc.CallOption) (*CreateRoleAssignmentResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteRoleAssignment(ctx context.Context, in *DeleteRoleAssignmentRequest, opts ...grpc.CallOption) (*DeleteRoleAssignmentResponse, error)
	GetRoleAssignment(ctx context.Context, in *GetRoleAssignmentRequest, opts ...grpc.CallOption) (*GetRoleAssignmentResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePrivateEndpoint(ctx context.Context, in *CreatePrivateEndpointRequest, opts ...grpc.CallOption) (*CreatePrivateEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePrivateEndpoint(ctx context.Context, in *DeletePrivateEndpointRequest, opts ...grpc.CallOption) (*DeletePrivateEndpointResponse, error)
	GetPrivateEndpoint(ctx context.Context, in *GetPrivateEndpointRequest, opts ...grpc.CallOption) (*GetPrivateEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePGServer(ctx context.Context, in *CreatePGServerRequest, opts ...grpc.CallOption) (*CreatePGServerResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePGServer(ctx context.Context, in *DeletePGServerRequest, opts ...grpc.CallOption) (*DeletePGServerResponse, error)
	// Expected a SCHEDULED status on success.
	StartPGServer(ctx context.Context, in *StartPGServerRequest, opts ...grpc.CallOption) (*StartPGServerResponse, error)
	// Expected a SCHEDULED status on success.
	StopPGServer(ctx context.Context, in *StopPGServerRequest, opts ...grpc.CallOption) (*StopPGServerResponse, error)
	GetPGServer(ctx context.Context, in *GetPGServerRequest, opts ...grpc.CallOption) (*GetPGServerResponse, error)
	GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error)
}

type azrResourceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewAzrResourceManagerClient(cc grpc.ClientConnInterface) AzrResourceManagerClient {
	return &azrResourceManagerClient{cc}
}

func (c *azrResourceManagerClient) CreateUserAssignedIdentity(ctx context.Context, in *CreateUserAssignedIdentityRequest, opts ...grpc.CallOption) (*CreateUserAssignedIdentityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUserAssignedIdentityResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreateUserAssignedIdentity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) DeleteUserAssignedIdentity(ctx context.Context, in *DeleteUserAssignedIdentityRequest, opts ...grpc.CallOption) (*DeleteUserAssignedIdentityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteUserAssignedIdentityResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_DeleteUserAssignedIdentity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) GetUserAssignedIdentity(ctx context.Context, in *GetUserAssignedIdentityRequest, opts ...grpc.CallOption) (*GetUserAssignedIdentityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetUserAssignedIdentityResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_GetUserAssignedIdentity_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateDataDirectoryDeletionTaskResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreateDataDirectoryDeletionTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateDataDirectoryCloneTaskResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreateDataDirectoryCloneTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) CreateFederatedIdentityCredential(ctx context.Context, in *CreateFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*CreateFederatedIdentityCredentialResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateFederatedIdentityCredentialResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreateFederatedIdentityCredential_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) DeleteFederatedIdentityCredential(ctx context.Context, in *DeleteFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*DeleteFederatedIdentityCredentialResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteFederatedIdentityCredentialResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_DeleteFederatedIdentityCredential_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) GetFederatedIdentityCredential(ctx context.Context, in *GetFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*GetFederatedIdentityCredentialResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetFederatedIdentityCredentialResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_GetFederatedIdentityCredential_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) CreateRoleAssignment(ctx context.Context, in *CreateRoleAssignmentRequest, opts ...grpc.CallOption) (*CreateRoleAssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRoleAssignmentResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreateRoleAssignment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) DeleteRoleAssignment(ctx context.Context, in *DeleteRoleAssignmentRequest, opts ...grpc.CallOption) (*DeleteRoleAssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteRoleAssignmentResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_DeleteRoleAssignment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) GetRoleAssignment(ctx context.Context, in *GetRoleAssignmentRequest, opts ...grpc.CallOption) (*GetRoleAssignmentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRoleAssignmentResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_GetRoleAssignment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) CreatePrivateEndpoint(ctx context.Context, in *CreatePrivateEndpointRequest, opts ...grpc.CallOption) (*CreatePrivateEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePrivateEndpointResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreatePrivateEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) DeletePrivateEndpoint(ctx context.Context, in *DeletePrivateEndpointRequest, opts ...grpc.CallOption) (*DeletePrivateEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePrivateEndpointResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_DeletePrivateEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) GetPrivateEndpoint(ctx context.Context, in *GetPrivateEndpointRequest, opts ...grpc.CallOption) (*GetPrivateEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPrivateEndpointResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_GetPrivateEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) CreatePGServer(ctx context.Context, in *CreatePGServerRequest, opts ...grpc.CallOption) (*CreatePGServerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePGServerResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_CreatePGServer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) DeletePGServer(ctx context.Context, in *DeletePGServerRequest, opts ...grpc.CallOption) (*DeletePGServerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePGServerResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_DeletePGServer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) StartPGServer(ctx context.Context, in *StartPGServerRequest, opts ...grpc.CallOption) (*StartPGServerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartPGServerResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_StartPGServer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) StopPGServer(ctx context.Context, in *StopPGServerRequest, opts ...grpc.CallOption) (*StopPGServerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopPGServerResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_StopPGServer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) GetPGServer(ctx context.Context, in *GetPGServerRequest, opts ...grpc.CallOption) (*GetPGServerResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPGServerResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_GetPGServer_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *azrResourceManagerClient) GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.GetManifestResponse)
	err := c.cc.Invoke(ctx, AzrResourceManager_GetManifest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AzrResourceManagerServer is the server API for AzrResourceManager service.
// All implementations must embed UnimplementedAzrResourceManagerServer
// for forward compatibility.
type AzrResourceManagerServer interface {
	// Expected a SCHEDULED status on success.
	CreateUserAssignedIdentity(context.Context, *CreateUserAssignedIdentityRequest) (*CreateUserAssignedIdentityResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteUserAssignedIdentity(context.Context, *DeleteUserAssignedIdentityRequest) (*DeleteUserAssignedIdentityResponse, error)
	GetUserAssignedIdentity(context.Context, *GetUserAssignedIdentityRequest) (*GetUserAssignedIdentityResponse, error)
	// CreateDataDirectoryDeletionTask creates a task for AZR folder deletion.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	CreateDataDirectoryDeletionTask(context.Context, *data.CreateDataDirectoryDeletionTaskRequest) (*data.CreateDataDirectoryDeletionTaskResponse, error)
	CreateDataDirectoryCloneTask(context.Context, *data.CreateDataDirectoryCloneTaskRequest) (*data.CreateDataDirectoryCloneTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateFederatedIdentityCredential(context.Context, *CreateFederatedIdentityCredentialRequest) (*CreateFederatedIdentityCredentialResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteFederatedIdentityCredential(context.Context, *DeleteFederatedIdentityCredentialRequest) (*DeleteFederatedIdentityCredentialResponse, error)
	GetFederatedIdentityCredential(context.Context, *GetFederatedIdentityCredentialRequest) (*GetFederatedIdentityCredentialResponse, error)
	// Expected a SCHEDULED status on success.
	CreateRoleAssignment(context.Context, *CreateRoleAssignmentRequest) (*CreateRoleAssignmentResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteRoleAssignment(context.Context, *DeleteRoleAssignmentRequest) (*DeleteRoleAssignmentResponse, error)
	GetRoleAssignment(context.Context, *GetRoleAssignmentRequest) (*GetRoleAssignmentResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePrivateEndpoint(context.Context, *CreatePrivateEndpointRequest) (*CreatePrivateEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePrivateEndpoint(context.Context, *DeletePrivateEndpointRequest) (*DeletePrivateEndpointResponse, error)
	GetPrivateEndpoint(context.Context, *GetPrivateEndpointRequest) (*GetPrivateEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePGServer(context.Context, *CreatePGServerRequest) (*CreatePGServerResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePGServer(context.Context, *DeletePGServerRequest) (*DeletePGServerResponse, error)
	// Expected a SCHEDULED status on success.
	StartPGServer(context.Context, *StartPGServerRequest) (*StartPGServerResponse, error)
	// Expected a SCHEDULED status on success.
	StopPGServer(context.Context, *StopPGServerRequest) (*StopPGServerResponse, error)
	GetPGServer(context.Context, *GetPGServerRequest) (*GetPGServerResponse, error)
	GetManifest(context.Context, *data.GetManifestRequest) (*data.GetManifestResponse, error)
	mustEmbedUnimplementedAzrResourceManagerServer()
}

// UnimplementedAzrResourceManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAzrResourceManagerServer struct{}

func (UnimplementedAzrResourceManagerServer) CreateUserAssignedIdentity(context.Context, *CreateUserAssignedIdentityRequest) (*CreateUserAssignedIdentityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUserAssignedIdentity not implemented")
}
func (UnimplementedAzrResourceManagerServer) DeleteUserAssignedIdentity(context.Context, *DeleteUserAssignedIdentityRequest) (*DeleteUserAssignedIdentityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUserAssignedIdentity not implemented")
}
func (UnimplementedAzrResourceManagerServer) GetUserAssignedIdentity(context.Context, *GetUserAssignedIdentityRequest) (*GetUserAssignedIdentityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetUserAssignedIdentity not implemented")
}
func (UnimplementedAzrResourceManagerServer) CreateDataDirectoryDeletionTask(context.Context, *data.CreateDataDirectoryDeletionTaskRequest) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataDirectoryDeletionTask not implemented")
}
func (UnimplementedAzrResourceManagerServer) CreateDataDirectoryCloneTask(context.Context, *data.CreateDataDirectoryCloneTaskRequest) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataDirectoryCloneTask not implemented")
}
func (UnimplementedAzrResourceManagerServer) CreateFederatedIdentityCredential(context.Context, *CreateFederatedIdentityCredentialRequest) (*CreateFederatedIdentityCredentialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateFederatedIdentityCredential not implemented")
}
func (UnimplementedAzrResourceManagerServer) DeleteFederatedIdentityCredential(context.Context, *DeleteFederatedIdentityCredentialRequest) (*DeleteFederatedIdentityCredentialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteFederatedIdentityCredential not implemented")
}
func (UnimplementedAzrResourceManagerServer) GetFederatedIdentityCredential(context.Context, *GetFederatedIdentityCredentialRequest) (*GetFederatedIdentityCredentialResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetFederatedIdentityCredential not implemented")
}
func (UnimplementedAzrResourceManagerServer) CreateRoleAssignment(context.Context, *CreateRoleAssignmentRequest) (*CreateRoleAssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRoleAssignment not implemented")
}
func (UnimplementedAzrResourceManagerServer) DeleteRoleAssignment(context.Context, *DeleteRoleAssignmentRequest) (*DeleteRoleAssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRoleAssignment not implemented")
}
func (UnimplementedAzrResourceManagerServer) GetRoleAssignment(context.Context, *GetRoleAssignmentRequest) (*GetRoleAssignmentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRoleAssignment not implemented")
}
func (UnimplementedAzrResourceManagerServer) CreatePrivateEndpoint(context.Context, *CreatePrivateEndpointRequest) (*CreatePrivateEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePrivateEndpoint not implemented")
}
func (UnimplementedAzrResourceManagerServer) DeletePrivateEndpoint(context.Context, *DeletePrivateEndpointRequest) (*DeletePrivateEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePrivateEndpoint not implemented")
}
func (UnimplementedAzrResourceManagerServer) GetPrivateEndpoint(context.Context, *GetPrivateEndpointRequest) (*GetPrivateEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrivateEndpoint not implemented")
}
func (UnimplementedAzrResourceManagerServer) CreatePGServer(context.Context, *CreatePGServerRequest) (*CreatePGServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePGServer not implemented")
}
func (UnimplementedAzrResourceManagerServer) DeletePGServer(context.Context, *DeletePGServerRequest) (*DeletePGServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePGServer not implemented")
}
func (UnimplementedAzrResourceManagerServer) StartPGServer(context.Context, *StartPGServerRequest) (*StartPGServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartPGServer not implemented")
}
func (UnimplementedAzrResourceManagerServer) StopPGServer(context.Context, *StopPGServerRequest) (*StopPGServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopPGServer not implemented")
}
func (UnimplementedAzrResourceManagerServer) GetPGServer(context.Context, *GetPGServerRequest) (*GetPGServerResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPGServer not implemented")
}
func (UnimplementedAzrResourceManagerServer) GetManifest(context.Context, *data.GetManifestRequest) (*data.GetManifestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetManifest not implemented")
}
func (UnimplementedAzrResourceManagerServer) mustEmbedUnimplementedAzrResourceManagerServer() {}
func (UnimplementedAzrResourceManagerServer) testEmbeddedByValue()                            {}

// UnsafeAzrResourceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AzrResourceManagerServer will
// result in compilation errors.
type UnsafeAzrResourceManagerServer interface {
	mustEmbedUnimplementedAzrResourceManagerServer()
}

func RegisterAzrResourceManagerServer(s grpc.ServiceRegistrar, srv AzrResourceManagerServer) {
	// If the following call pancis, it indicates UnimplementedAzrResourceManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AzrResourceManager_ServiceDesc, srv)
}

func _AzrResourceManager_CreateUserAssignedIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserAssignedIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreateUserAssignedIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreateUserAssignedIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreateUserAssignedIdentity(ctx, req.(*CreateUserAssignedIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_DeleteUserAssignedIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserAssignedIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).DeleteUserAssignedIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_DeleteUserAssignedIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).DeleteUserAssignedIdentity(ctx, req.(*DeleteUserAssignedIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_GetUserAssignedIdentity_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetUserAssignedIdentityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).GetUserAssignedIdentity(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_GetUserAssignedIdentity_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).GetUserAssignedIdentity(ctx, req.(*GetUserAssignedIdentityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_CreateDataDirectoryDeletionTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateDataDirectoryDeletionTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreateDataDirectoryDeletionTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreateDataDirectoryDeletionTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreateDataDirectoryDeletionTask(ctx, req.(*data.CreateDataDirectoryDeletionTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_CreateDataDirectoryCloneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateDataDirectoryCloneTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreateDataDirectoryCloneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreateDataDirectoryCloneTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreateDataDirectoryCloneTask(ctx, req.(*data.CreateDataDirectoryCloneTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_CreateFederatedIdentityCredential_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateFederatedIdentityCredentialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreateFederatedIdentityCredential(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreateFederatedIdentityCredential_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreateFederatedIdentityCredential(ctx, req.(*CreateFederatedIdentityCredentialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_DeleteFederatedIdentityCredential_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteFederatedIdentityCredentialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).DeleteFederatedIdentityCredential(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_DeleteFederatedIdentityCredential_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).DeleteFederatedIdentityCredential(ctx, req.(*DeleteFederatedIdentityCredentialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_GetFederatedIdentityCredential_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetFederatedIdentityCredentialRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).GetFederatedIdentityCredential(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_GetFederatedIdentityCredential_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).GetFederatedIdentityCredential(ctx, req.(*GetFederatedIdentityCredentialRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_CreateRoleAssignment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRoleAssignmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreateRoleAssignment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreateRoleAssignment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreateRoleAssignment(ctx, req.(*CreateRoleAssignmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_DeleteRoleAssignment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRoleAssignmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).DeleteRoleAssignment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_DeleteRoleAssignment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).DeleteRoleAssignment(ctx, req.(*DeleteRoleAssignmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_GetRoleAssignment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRoleAssignmentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).GetRoleAssignment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_GetRoleAssignment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).GetRoleAssignment(ctx, req.(*GetRoleAssignmentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_CreatePrivateEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePrivateEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreatePrivateEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreatePrivateEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreatePrivateEndpoint(ctx, req.(*CreatePrivateEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_DeletePrivateEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePrivateEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).DeletePrivateEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_DeletePrivateEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).DeletePrivateEndpoint(ctx, req.(*DeletePrivateEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_GetPrivateEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrivateEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).GetPrivateEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_GetPrivateEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).GetPrivateEndpoint(ctx, req.(*GetPrivateEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_CreatePGServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePGServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).CreatePGServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_CreatePGServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).CreatePGServer(ctx, req.(*CreatePGServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_DeletePGServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePGServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).DeletePGServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_DeletePGServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).DeletePGServer(ctx, req.(*DeletePGServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_StartPGServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartPGServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).StartPGServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_StartPGServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).StartPGServer(ctx, req.(*StartPGServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_StopPGServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopPGServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).StopPGServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_StopPGServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).StopPGServer(ctx, req.(*StopPGServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_GetPGServer_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPGServerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).GetPGServer(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_GetPGServer_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).GetPGServer(ctx, req.(*GetPGServerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AzrResourceManager_GetManifest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.GetManifestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AzrResourceManagerServer).GetManifest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AzrResourceManager_GetManifest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AzrResourceManagerServer).GetManifest(ctx, req.(*data.GetManifestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AzrResourceManager_ServiceDesc is the grpc.ServiceDesc for AzrResourceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AzrResourceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.azr.AzrResourceManager",
	HandlerType: (*AzrResourceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateUserAssignedIdentity",
			Handler:    _AzrResourceManager_CreateUserAssignedIdentity_Handler,
		},
		{
			MethodName: "DeleteUserAssignedIdentity",
			Handler:    _AzrResourceManager_DeleteUserAssignedIdentity_Handler,
		},
		{
			MethodName: "GetUserAssignedIdentity",
			Handler:    _AzrResourceManager_GetUserAssignedIdentity_Handler,
		},
		{
			MethodName: "CreateDataDirectoryDeletionTask",
			Handler:    _AzrResourceManager_CreateDataDirectoryDeletionTask_Handler,
		},
		{
			MethodName: "CreateDataDirectoryCloneTask",
			Handler:    _AzrResourceManager_CreateDataDirectoryCloneTask_Handler,
		},
		{
			MethodName: "CreateFederatedIdentityCredential",
			Handler:    _AzrResourceManager_CreateFederatedIdentityCredential_Handler,
		},
		{
			MethodName: "DeleteFederatedIdentityCredential",
			Handler:    _AzrResourceManager_DeleteFederatedIdentityCredential_Handler,
		},
		{
			MethodName: "GetFederatedIdentityCredential",
			Handler:    _AzrResourceManager_GetFederatedIdentityCredential_Handler,
		},
		{
			MethodName: "CreateRoleAssignment",
			Handler:    _AzrResourceManager_CreateRoleAssignment_Handler,
		},
		{
			MethodName: "DeleteRoleAssignment",
			Handler:    _AzrResourceManager_DeleteRoleAssignment_Handler,
		},
		{
			MethodName: "GetRoleAssignment",
			Handler:    _AzrResourceManager_GetRoleAssignment_Handler,
		},
		{
			MethodName: "CreatePrivateEndpoint",
			Handler:    _AzrResourceManager_CreatePrivateEndpoint_Handler,
		},
		{
			MethodName: "DeletePrivateEndpoint",
			Handler:    _AzrResourceManager_DeletePrivateEndpoint_Handler,
		},
		{
			MethodName: "GetPrivateEndpoint",
			Handler:    _AzrResourceManager_GetPrivateEndpoint_Handler,
		},
		{
			MethodName: "CreatePGServer",
			Handler:    _AzrResourceManager_CreatePGServer_Handler,
		},
		{
			MethodName: "DeletePGServer",
			Handler:    _AzrResourceManager_DeletePGServer_Handler,
		},
		{
			MethodName: "StartPGServer",
			Handler:    _AzrResourceManager_StartPGServer_Handler,
		},
		{
			MethodName: "StopPGServer",
			Handler:    _AzrResourceManager_StopPGServer_Handler,
		},
		{
			MethodName: "GetPGServer",
			Handler:    _AzrResourceManager_GetPGServer_Handler,
		},
		{
			MethodName: "GetManifest",
			Handler:    _AzrResourceManager_GetManifest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/azr.proto",
}
