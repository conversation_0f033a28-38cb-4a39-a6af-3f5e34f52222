// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/azr (interfaces: AzrResourceManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/azr -package=azr -destination=pbgen/services/azr/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/azr AzrResourceManagerClient
//

// Package azr is a generated GoMock package.
package azr

import (
	context "context"
	reflect "reflect"

	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAzrResourceManagerClient is a mock of AzrResourceManagerClient interface.
type MockAzrResourceManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockAzrResourceManagerClientMockRecorder
	isgomock struct{}
}

// MockAzrResourceManagerClientMockRecorder is the mock recorder for MockAzrResourceManagerClient.
type MockAzrResourceManagerClientMockRecorder struct {
	mock *MockAzrResourceManagerClient
}

// NewMockAzrResourceManagerClient creates a new mock instance.
func NewMockAzrResourceManagerClient(ctrl *gomock.Controller) *MockAzrResourceManagerClient {
	mock := &MockAzrResourceManagerClient{ctrl: ctrl}
	mock.recorder = &MockAzrResourceManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAzrResourceManagerClient) EXPECT() *MockAzrResourceManagerClientMockRecorder {
	return m.recorder
}

// CreateDataDirectoryCloneTask mocks base method.
func (m *MockAzrResourceManagerClient) CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDataDirectoryCloneTask", varargs...)
	ret0, _ := ret[0].(*data.CreateDataDirectoryCloneTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataDirectoryCloneTask indicates an expected call of CreateDataDirectoryCloneTask.
func (mr *MockAzrResourceManagerClientMockRecorder) CreateDataDirectoryCloneTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataDirectoryCloneTask", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreateDataDirectoryCloneTask), varargs...)
}

// CreateDataDirectoryDeletionTask mocks base method.
func (m *MockAzrResourceManagerClient) CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDataDirectoryDeletionTask", varargs...)
	ret0, _ := ret[0].(*data.CreateDataDirectoryDeletionTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataDirectoryDeletionTask indicates an expected call of CreateDataDirectoryDeletionTask.
func (mr *MockAzrResourceManagerClientMockRecorder) CreateDataDirectoryDeletionTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataDirectoryDeletionTask", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreateDataDirectoryDeletionTask), varargs...)
}

// CreateFederatedIdentityCredential mocks base method.
func (m *MockAzrResourceManagerClient) CreateFederatedIdentityCredential(ctx context.Context, in *CreateFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*CreateFederatedIdentityCredentialResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateFederatedIdentityCredential", varargs...)
	ret0, _ := ret[0].(*CreateFederatedIdentityCredentialResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateFederatedIdentityCredential indicates an expected call of CreateFederatedIdentityCredential.
func (mr *MockAzrResourceManagerClientMockRecorder) CreateFederatedIdentityCredential(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateFederatedIdentityCredential", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreateFederatedIdentityCredential), varargs...)
}

// CreatePGServer mocks base method.
func (m *MockAzrResourceManagerClient) CreatePGServer(ctx context.Context, in *CreatePGServerRequest, opts ...grpc.CallOption) (*CreatePGServerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePGServer", varargs...)
	ret0, _ := ret[0].(*CreatePGServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePGServer indicates an expected call of CreatePGServer.
func (mr *MockAzrResourceManagerClientMockRecorder) CreatePGServer(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePGServer", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreatePGServer), varargs...)
}

// CreatePrivateEndpoint mocks base method.
func (m *MockAzrResourceManagerClient) CreatePrivateEndpoint(ctx context.Context, in *CreatePrivateEndpointRequest, opts ...grpc.CallOption) (*CreatePrivateEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePrivateEndpoint", varargs...)
	ret0, _ := ret[0].(*CreatePrivateEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePrivateEndpoint indicates an expected call of CreatePrivateEndpoint.
func (mr *MockAzrResourceManagerClientMockRecorder) CreatePrivateEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePrivateEndpoint", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreatePrivateEndpoint), varargs...)
}

// CreateRoleAssignment mocks base method.
func (m *MockAzrResourceManagerClient) CreateRoleAssignment(ctx context.Context, in *CreateRoleAssignmentRequest, opts ...grpc.CallOption) (*CreateRoleAssignmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRoleAssignment", varargs...)
	ret0, _ := ret[0].(*CreateRoleAssignmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRoleAssignment indicates an expected call of CreateRoleAssignment.
func (mr *MockAzrResourceManagerClientMockRecorder) CreateRoleAssignment(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRoleAssignment", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreateRoleAssignment), varargs...)
}

// CreateUserAssignedIdentity mocks base method.
func (m *MockAzrResourceManagerClient) CreateUserAssignedIdentity(ctx context.Context, in *CreateUserAssignedIdentityRequest, opts ...grpc.CallOption) (*CreateUserAssignedIdentityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateUserAssignedIdentity", varargs...)
	ret0, _ := ret[0].(*CreateUserAssignedIdentityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUserAssignedIdentity indicates an expected call of CreateUserAssignedIdentity.
func (mr *MockAzrResourceManagerClientMockRecorder) CreateUserAssignedIdentity(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUserAssignedIdentity", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).CreateUserAssignedIdentity), varargs...)
}

// DeleteFederatedIdentityCredential mocks base method.
func (m *MockAzrResourceManagerClient) DeleteFederatedIdentityCredential(ctx context.Context, in *DeleteFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*DeleteFederatedIdentityCredentialResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteFederatedIdentityCredential", varargs...)
	ret0, _ := ret[0].(*DeleteFederatedIdentityCredentialResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteFederatedIdentityCredential indicates an expected call of DeleteFederatedIdentityCredential.
func (mr *MockAzrResourceManagerClientMockRecorder) DeleteFederatedIdentityCredential(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteFederatedIdentityCredential", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).DeleteFederatedIdentityCredential), varargs...)
}

// DeletePGServer mocks base method.
func (m *MockAzrResourceManagerClient) DeletePGServer(ctx context.Context, in *DeletePGServerRequest, opts ...grpc.CallOption) (*DeletePGServerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePGServer", varargs...)
	ret0, _ := ret[0].(*DeletePGServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePGServer indicates an expected call of DeletePGServer.
func (mr *MockAzrResourceManagerClientMockRecorder) DeletePGServer(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePGServer", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).DeletePGServer), varargs...)
}

// DeletePrivateEndpoint mocks base method.
func (m *MockAzrResourceManagerClient) DeletePrivateEndpoint(ctx context.Context, in *DeletePrivateEndpointRequest, opts ...grpc.CallOption) (*DeletePrivateEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePrivateEndpoint", varargs...)
	ret0, _ := ret[0].(*DeletePrivateEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePrivateEndpoint indicates an expected call of DeletePrivateEndpoint.
func (mr *MockAzrResourceManagerClientMockRecorder) DeletePrivateEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePrivateEndpoint", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).DeletePrivateEndpoint), varargs...)
}

// DeleteRoleAssignment mocks base method.
func (m *MockAzrResourceManagerClient) DeleteRoleAssignment(ctx context.Context, in *DeleteRoleAssignmentRequest, opts ...grpc.CallOption) (*DeleteRoleAssignmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRoleAssignment", varargs...)
	ret0, _ := ret[0].(*DeleteRoleAssignmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRoleAssignment indicates an expected call of DeleteRoleAssignment.
func (mr *MockAzrResourceManagerClientMockRecorder) DeleteRoleAssignment(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRoleAssignment", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).DeleteRoleAssignment), varargs...)
}

// DeleteUserAssignedIdentity mocks base method.
func (m *MockAzrResourceManagerClient) DeleteUserAssignedIdentity(ctx context.Context, in *DeleteUserAssignedIdentityRequest, opts ...grpc.CallOption) (*DeleteUserAssignedIdentityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteUserAssignedIdentity", varargs...)
	ret0, _ := ret[0].(*DeleteUserAssignedIdentityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteUserAssignedIdentity indicates an expected call of DeleteUserAssignedIdentity.
func (mr *MockAzrResourceManagerClientMockRecorder) DeleteUserAssignedIdentity(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUserAssignedIdentity", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).DeleteUserAssignedIdentity), varargs...)
}

// GetFederatedIdentityCredential mocks base method.
func (m *MockAzrResourceManagerClient) GetFederatedIdentityCredential(ctx context.Context, in *GetFederatedIdentityCredentialRequest, opts ...grpc.CallOption) (*GetFederatedIdentityCredentialResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetFederatedIdentityCredential", varargs...)
	ret0, _ := ret[0].(*GetFederatedIdentityCredentialResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetFederatedIdentityCredential indicates an expected call of GetFederatedIdentityCredential.
func (mr *MockAzrResourceManagerClientMockRecorder) GetFederatedIdentityCredential(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetFederatedIdentityCredential", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).GetFederatedIdentityCredential), varargs...)
}

// GetManifest mocks base method.
func (m *MockAzrResourceManagerClient) GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManifest", varargs...)
	ret0, _ := ret[0].(*data.GetManifestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManifest indicates an expected call of GetManifest.
func (mr *MockAzrResourceManagerClientMockRecorder) GetManifest(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManifest", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).GetManifest), varargs...)
}

// GetPGServer mocks base method.
func (m *MockAzrResourceManagerClient) GetPGServer(ctx context.Context, in *GetPGServerRequest, opts ...grpc.CallOption) (*GetPGServerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPGServer", varargs...)
	ret0, _ := ret[0].(*GetPGServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPGServer indicates an expected call of GetPGServer.
func (mr *MockAzrResourceManagerClientMockRecorder) GetPGServer(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPGServer", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).GetPGServer), varargs...)
}

// GetPrivateEndpoint mocks base method.
func (m *MockAzrResourceManagerClient) GetPrivateEndpoint(ctx context.Context, in *GetPrivateEndpointRequest, opts ...grpc.CallOption) (*GetPrivateEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPrivateEndpoint", varargs...)
	ret0, _ := ret[0].(*GetPrivateEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivateEndpoint indicates an expected call of GetPrivateEndpoint.
func (mr *MockAzrResourceManagerClientMockRecorder) GetPrivateEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivateEndpoint", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).GetPrivateEndpoint), varargs...)
}

// GetRoleAssignment mocks base method.
func (m *MockAzrResourceManagerClient) GetRoleAssignment(ctx context.Context, in *GetRoleAssignmentRequest, opts ...grpc.CallOption) (*GetRoleAssignmentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRoleAssignment", varargs...)
	ret0, _ := ret[0].(*GetRoleAssignmentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRoleAssignment indicates an expected call of GetRoleAssignment.
func (mr *MockAzrResourceManagerClientMockRecorder) GetRoleAssignment(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRoleAssignment", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).GetRoleAssignment), varargs...)
}

// GetUserAssignedIdentity mocks base method.
func (m *MockAzrResourceManagerClient) GetUserAssignedIdentity(ctx context.Context, in *GetUserAssignedIdentityRequest, opts ...grpc.CallOption) (*GetUserAssignedIdentityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetUserAssignedIdentity", varargs...)
	ret0, _ := ret[0].(*GetUserAssignedIdentityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetUserAssignedIdentity indicates an expected call of GetUserAssignedIdentity.
func (mr *MockAzrResourceManagerClientMockRecorder) GetUserAssignedIdentity(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetUserAssignedIdentity", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).GetUserAssignedIdentity), varargs...)
}

// StartPGServer mocks base method.
func (m *MockAzrResourceManagerClient) StartPGServer(ctx context.Context, in *StartPGServerRequest, opts ...grpc.CallOption) (*StartPGServerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartPGServer", varargs...)
	ret0, _ := ret[0].(*StartPGServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartPGServer indicates an expected call of StartPGServer.
func (mr *MockAzrResourceManagerClientMockRecorder) StartPGServer(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartPGServer", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).StartPGServer), varargs...)
}

// StopPGServer mocks base method.
func (m *MockAzrResourceManagerClient) StopPGServer(ctx context.Context, in *StopPGServerRequest, opts ...grpc.CallOption) (*StopPGServerResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopPGServer", varargs...)
	ret0, _ := ret[0].(*StopPGServerResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopPGServer indicates an expected call of StopPGServer.
func (mr *MockAzrResourceManagerClientMockRecorder) StopPGServer(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopPGServer", reflect.TypeOf((*MockAzrResourceManagerClient)(nil).StopPGServer), varargs...)
}
