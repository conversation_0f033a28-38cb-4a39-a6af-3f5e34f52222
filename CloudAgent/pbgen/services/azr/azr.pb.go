// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/azr.proto

package azr

import (
	azr "github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	deletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	update "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// ref: https://github.com/Azure/azure-service-operator/blob/main/v2/api/network/v1api20220701/private_endpoint_status_private_endpoint_sub_resource_embedded_arm_types_gen.go#L68
// ref: https://github.com/Azure/azure-service-operator/blob/main/v2/api/network/v1api20220701/application_gateway_types_gen.go#L9430-L9435
type PrivateEndpointStatus int32

const (
	PrivateEndpointStatus_STATUS_UNSPECIFIED PrivateEndpointStatus = 0
	PrivateEndpointStatus_DELETING           PrivateEndpointStatus = 1
	PrivateEndpointStatus_FAILED             PrivateEndpointStatus = 2
	PrivateEndpointStatus_SUCCEEDED          PrivateEndpointStatus = 3
	PrivateEndpointStatus_UPDATING           PrivateEndpointStatus = 4
)

// Enum value maps for PrivateEndpointStatus.
var (
	PrivateEndpointStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "DELETING",
		2: "FAILED",
		3: "SUCCEEDED",
		4: "UPDATING",
	}
	PrivateEndpointStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"DELETING":           1,
		"FAILED":             2,
		"SUCCEEDED":          3,
		"UPDATING":           4,
	}
)

func (x PrivateEndpointStatus) Enum() *PrivateEndpointStatus {
	p := new(PrivateEndpointStatus)
	*p = x
	return p
}

func (x PrivateEndpointStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PrivateEndpointStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_services_azr_proto_enumTypes[0].Descriptor()
}

func (PrivateEndpointStatus) Type() protoreflect.EnumType {
	return &file_services_azr_proto_enumTypes[0]
}

func (x PrivateEndpointStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PrivateEndpointStatus.Descriptor instead.
func (PrivateEndpointStatus) EnumDescriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{0}
}

type CreateUserAssignedIdentityRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the resource will be the name of the provisoned user assigned
	// identity.
	// The name length should be between 3 to 128 chars.
	// Reference:
	// https://github.com/hashicorp/terraform-provider-azurerm/blob/cd6d8e760903c06b4d4246090b54b0873e028ebb/internal/services/managedidentity/user_assigned_identity_data_source.go#L33
	ResourceMeta  *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserAssignedIdentityRequest) Reset() {
	*x = CreateUserAssignedIdentityRequest{}
	mi := &file_services_azr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserAssignedIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserAssignedIdentityRequest) ProtoMessage() {}

func (x *CreateUserAssignedIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserAssignedIdentityRequest.ProtoReflect.Descriptor instead.
func (*CreateUserAssignedIdentityRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{0}
}

func (x *CreateUserAssignedIdentityRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type CreateUserAssignedIdentityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateUserAssignedIdentityResponse) Reset() {
	*x = CreateUserAssignedIdentityResponse{}
	mi := &file_services_azr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateUserAssignedIdentityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateUserAssignedIdentityResponse) ProtoMessage() {}

func (x *CreateUserAssignedIdentityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateUserAssignedIdentityResponse.ProtoReflect.Descriptor instead.
func (*CreateUserAssignedIdentityResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{1}
}

func (x *CreateUserAssignedIdentityResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetUserAssignedIdentityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserAssignedIdentityRequest) Reset() {
	*x = GetUserAssignedIdentityRequest{}
	mi := &file_services_azr_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAssignedIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAssignedIdentityRequest) ProtoMessage() {}

func (x *GetUserAssignedIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAssignedIdentityRequest.ProtoReflect.Descriptor instead.
func (*GetUserAssignedIdentityRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{2}
}

func (x *GetUserAssignedIdentityRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetUserAssignedIdentityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PrincipalId   string                 `protobuf:"bytes,2,opt,name=principal_id,json=principalId,proto3" json:"principal_id,omitempty"`
	ClientId      string                 `protobuf:"bytes,3,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetUserAssignedIdentityResponse) Reset() {
	*x = GetUserAssignedIdentityResponse{}
	mi := &file_services_azr_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetUserAssignedIdentityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetUserAssignedIdentityResponse) ProtoMessage() {}

func (x *GetUserAssignedIdentityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetUserAssignedIdentityResponse.ProtoReflect.Descriptor instead.
func (*GetUserAssignedIdentityResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{3}
}

func (x *GetUserAssignedIdentityResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetUserAssignedIdentityResponse) GetPrincipalId() string {
	if x != nil {
		return x.PrincipalId
	}
	return ""
}

func (x *GetUserAssignedIdentityResponse) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

type DeleteUserAssignedIdentityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserAssignedIdentityRequest) Reset() {
	*x = DeleteUserAssignedIdentityRequest{}
	mi := &file_services_azr_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserAssignedIdentityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserAssignedIdentityRequest) ProtoMessage() {}

func (x *DeleteUserAssignedIdentityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserAssignedIdentityRequest.ProtoReflect.Descriptor instead.
func (*DeleteUserAssignedIdentityRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteUserAssignedIdentityRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteUserAssignedIdentityResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteUserAssignedIdentityResponse) Reset() {
	*x = DeleteUserAssignedIdentityResponse{}
	mi := &file_services_azr_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteUserAssignedIdentityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteUserAssignedIdentityResponse) ProtoMessage() {}

func (x *DeleteUserAssignedIdentityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteUserAssignedIdentityResponse.ProtoReflect.Descriptor instead.
func (*DeleteUserAssignedIdentityResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteUserAssignedIdentityResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateFederatedIdentityCredentialRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The name of the resource must follow the following pattern:
	// ^[a-zA-Z0-9]{1}[a-zA-Z0-9-_]{2,119}$
	// Reference:
	// https://github.com/Azure/azure-service-operator/blob/209b27aabe1e5685340309b4b24cca47368ca07d/v2/api/managedidentity/v1api20230131/federated_identity_credential_types_gen.go#L349-L352
	ResourceMeta *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Name of the UserAssignedIdentity resource this credential is associated
	// with. Must be in the same namespace of the created credential.
	UserAssignedIdentityName string `protobuf:"bytes,2,opt,name=user_assigned_identity_name,json=userAssignedIdentityName,proto3" json:"user_assigned_identity_name,omitempty"`
	// Which kubernetes service account to bind.
	ServiceAccount *k8s.ServiceAccount `protobuf:"bytes,3,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateFederatedIdentityCredentialRequest) Reset() {
	*x = CreateFederatedIdentityCredentialRequest{}
	mi := &file_services_azr_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFederatedIdentityCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFederatedIdentityCredentialRequest) ProtoMessage() {}

func (x *CreateFederatedIdentityCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFederatedIdentityCredentialRequest.ProtoReflect.Descriptor instead.
func (*CreateFederatedIdentityCredentialRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{6}
}

func (x *CreateFederatedIdentityCredentialRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateFederatedIdentityCredentialRequest) GetUserAssignedIdentityName() string {
	if x != nil {
		return x.UserAssignedIdentityName
	}
	return ""
}

func (x *CreateFederatedIdentityCredentialRequest) GetServiceAccount() *k8s.ServiceAccount {
	if x != nil {
		return x.ServiceAccount
	}
	return nil
}

type CreateFederatedIdentityCredentialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateFederatedIdentityCredentialResponse) Reset() {
	*x = CreateFederatedIdentityCredentialResponse{}
	mi := &file_services_azr_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateFederatedIdentityCredentialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateFederatedIdentityCredentialResponse) ProtoMessage() {}

func (x *CreateFederatedIdentityCredentialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateFederatedIdentityCredentialResponse.ProtoReflect.Descriptor instead.
func (*CreateFederatedIdentityCredentialResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{7}
}

func (x *CreateFederatedIdentityCredentialResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetFederatedIdentityCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFederatedIdentityCredentialRequest) Reset() {
	*x = GetFederatedIdentityCredentialRequest{}
	mi := &file_services_azr_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFederatedIdentityCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFederatedIdentityCredentialRequest) ProtoMessage() {}

func (x *GetFederatedIdentityCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFederatedIdentityCredentialRequest.ProtoReflect.Descriptor instead.
func (*GetFederatedIdentityCredentialRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{8}
}

func (x *GetFederatedIdentityCredentialRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetFederatedIdentityCredentialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetFederatedIdentityCredentialResponse) Reset() {
	*x = GetFederatedIdentityCredentialResponse{}
	mi := &file_services_azr_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetFederatedIdentityCredentialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetFederatedIdentityCredentialResponse) ProtoMessage() {}

func (x *GetFederatedIdentityCredentialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetFederatedIdentityCredentialResponse.ProtoReflect.Descriptor instead.
func (*GetFederatedIdentityCredentialResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{9}
}

func (x *GetFederatedIdentityCredentialResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteFederatedIdentityCredentialRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFederatedIdentityCredentialRequest) Reset() {
	*x = DeleteFederatedIdentityCredentialRequest{}
	mi := &file_services_azr_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFederatedIdentityCredentialRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFederatedIdentityCredentialRequest) ProtoMessage() {}

func (x *DeleteFederatedIdentityCredentialRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFederatedIdentityCredentialRequest.ProtoReflect.Descriptor instead.
func (*DeleteFederatedIdentityCredentialRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteFederatedIdentityCredentialRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteFederatedIdentityCredentialResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteFederatedIdentityCredentialResponse) Reset() {
	*x = DeleteFederatedIdentityCredentialResponse{}
	mi := &file_services_azr_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteFederatedIdentityCredentialResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteFederatedIdentityCredentialResponse) ProtoMessage() {}

func (x *DeleteFederatedIdentityCredentialResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteFederatedIdentityCredentialResponse.ProtoReflect.Descriptor instead.
func (*DeleteFederatedIdentityCredentialResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteFederatedIdentityCredentialResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateRoleAssignmentRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	PrincipalId  string                 `protobuf:"bytes,2,opt,name=principal_id,json=principalId,proto3" json:"principal_id,omitempty"`
	// Which roles to bind.
	RoleAssignment *RoleAssignment `protobuf:"bytes,3,opt,name=role_assignment,json=roleAssignment,proto3" json:"role_assignment,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateRoleAssignmentRequest) Reset() {
	*x = CreateRoleAssignmentRequest{}
	mi := &file_services_azr_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoleAssignmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleAssignmentRequest) ProtoMessage() {}

func (x *CreateRoleAssignmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleAssignmentRequest.ProtoReflect.Descriptor instead.
func (*CreateRoleAssignmentRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{12}
}

func (x *CreateRoleAssignmentRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateRoleAssignmentRequest) GetPrincipalId() string {
	if x != nil {
		return x.PrincipalId
	}
	return ""
}

func (x *CreateRoleAssignmentRequest) GetRoleAssignment() *RoleAssignment {
	if x != nil {
		return x.RoleAssignment
	}
	return nil
}

type CreateRoleAssignmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRoleAssignmentResponse) Reset() {
	*x = CreateRoleAssignmentResponse{}
	mi := &file_services_azr_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRoleAssignmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRoleAssignmentResponse) ProtoMessage() {}

func (x *CreateRoleAssignmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRoleAssignmentResponse.ProtoReflect.Descriptor instead.
func (*CreateRoleAssignmentResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{13}
}

func (x *CreateRoleAssignmentResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetRoleAssignmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoleAssignmentRequest) Reset() {
	*x = GetRoleAssignmentRequest{}
	mi := &file_services_azr_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoleAssignmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleAssignmentRequest) ProtoMessage() {}

func (x *GetRoleAssignmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleAssignmentRequest.ProtoReflect.Descriptor instead.
func (*GetRoleAssignmentRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{14}
}

func (x *GetRoleAssignmentRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetRoleAssignmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRoleAssignmentResponse) Reset() {
	*x = GetRoleAssignmentResponse{}
	mi := &file_services_azr_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRoleAssignmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRoleAssignmentResponse) ProtoMessage() {}

func (x *GetRoleAssignmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRoleAssignmentResponse.ProtoReflect.Descriptor instead.
func (*GetRoleAssignmentResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{15}
}

func (x *GetRoleAssignmentResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteRoleAssignmentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoleAssignmentRequest) Reset() {
	*x = DeleteRoleAssignmentRequest{}
	mi := &file_services_azr_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoleAssignmentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleAssignmentRequest) ProtoMessage() {}

func (x *DeleteRoleAssignmentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleAssignmentRequest.ProtoReflect.Descriptor instead.
func (*DeleteRoleAssignmentRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteRoleAssignmentRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteRoleAssignmentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRoleAssignmentResponse) Reset() {
	*x = DeleteRoleAssignmentResponse{}
	mi := &file_services_azr_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRoleAssignmentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRoleAssignmentResponse) ProtoMessage() {}

func (x *DeleteRoleAssignmentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRoleAssignmentResponse.ProtoReflect.Descriptor instead.
func (*DeleteRoleAssignmentResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteRoleAssignmentResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// RoleBinding specifies the type of role to bind to an IAM Service Account.
// Only AZBlob role is currently supported
type RoleAssignment struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to AccessOption:
	//
	//	*RoleAssignment_BlobAccessOption
	AccessOption  isRoleAssignment_AccessOption `protobuf_oneof:"access_option"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleAssignment) Reset() {
	*x = RoleAssignment{}
	mi := &file_services_azr_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleAssignment) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleAssignment) ProtoMessage() {}

func (x *RoleAssignment) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleAssignment.ProtoReflect.Descriptor instead.
func (*RoleAssignment) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{18}
}

func (x *RoleAssignment) GetAccessOption() isRoleAssignment_AccessOption {
	if x != nil {
		return x.AccessOption
	}
	return nil
}

func (x *RoleAssignment) GetBlobAccessOption() *BlobAccessOption {
	if x != nil {
		if x, ok := x.AccessOption.(*RoleAssignment_BlobAccessOption); ok {
			return x.BlobAccessOption
		}
	}
	return nil
}

type isRoleAssignment_AccessOption interface {
	isRoleAssignment_AccessOption()
}

type RoleAssignment_BlobAccessOption struct {
	// If specified, the create IAM policy will grant access to the specified
	// AZBlob location.
	BlobAccessOption *BlobAccessOption `protobuf:"bytes,1,opt,name=blob_access_option,json=blobAccessOption,proto3,oneof"`
}

func (*RoleAssignment_BlobAccessOption) isRoleAssignment_AccessOption() {}

// BlobAccessOption specifies an AZBlob directory a AZBlob IAM Policy should
// give access to. It will give access to all operations towards the directory.
type BlobAccessOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Blob container name.
	Container string `protobuf:"bytes,1,opt,name=container,proto3" json:"container,omitempty"`
	// List of directories of the AZBlob container.
	Dirs           []string `protobuf:"bytes,2,rep,name=dirs,proto3" json:"dirs,omitempty"`
	StorageAccount string   `protobuf:"bytes,3,opt,name=storage_account,json=storageAccount,proto3" json:"storage_account,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *BlobAccessOption) Reset() {
	*x = BlobAccessOption{}
	mi := &file_services_azr_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *BlobAccessOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BlobAccessOption) ProtoMessage() {}

func (x *BlobAccessOption) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BlobAccessOption.ProtoReflect.Descriptor instead.
func (*BlobAccessOption) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{19}
}

func (x *BlobAccessOption) GetContainer() string {
	if x != nil {
		return x.Container
	}
	return ""
}

func (x *BlobAccessOption) GetDirs() []string {
	if x != nil {
		return x.Dirs
	}
	return nil
}

func (x *BlobAccessOption) GetStorageAccount() string {
	if x != nil {
		return x.StorageAccount
	}
	return ""
}

type CreatePrivateEndpointRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta         *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	PrivateLinkServiceId string                 `protobuf:"bytes,2,opt,name=private_link_service_id,json=privateLinkServiceId,proto3" json:"private_link_service_id,omitempty"`
	PrivateLinkSubnetId  string                 `protobuf:"bytes,3,opt,name=private_link_subnet_id,json=privateLinkSubnetId,proto3" json:"private_link_subnet_id,omitempty"`
	ExtraTags            map[string]string      `protobuf:"bytes,4,rep,name=extra_tags,json=extraTags,proto3" json:"extra_tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *CreatePrivateEndpointRequest) Reset() {
	*x = CreatePrivateEndpointRequest{}
	mi := &file_services_azr_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePrivateEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePrivateEndpointRequest) ProtoMessage() {}

func (x *CreatePrivateEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePrivateEndpointRequest.ProtoReflect.Descriptor instead.
func (*CreatePrivateEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{20}
}

func (x *CreatePrivateEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreatePrivateEndpointRequest) GetPrivateLinkServiceId() string {
	if x != nil {
		return x.PrivateLinkServiceId
	}
	return ""
}

func (x *CreatePrivateEndpointRequest) GetPrivateLinkSubnetId() string {
	if x != nil {
		return x.PrivateLinkSubnetId
	}
	return ""
}

func (x *CreatePrivateEndpointRequest) GetExtraTags() map[string]string {
	if x != nil {
		return x.ExtraTags
	}
	return nil
}

type CreatePrivateEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePrivateEndpointResponse) Reset() {
	*x = CreatePrivateEndpointResponse{}
	mi := &file_services_azr_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePrivateEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePrivateEndpointResponse) ProtoMessage() {}

func (x *CreatePrivateEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePrivateEndpointResponse.ProtoReflect.Descriptor instead.
func (*CreatePrivateEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{21}
}

func (x *CreatePrivateEndpointResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPrivateEndpointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrivateEndpointRequest) Reset() {
	*x = GetPrivateEndpointRequest{}
	mi := &file_services_azr_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrivateEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrivateEndpointRequest) ProtoMessage() {}

func (x *GetPrivateEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrivateEndpointRequest.ProtoReflect.Descriptor instead.
func (*GetPrivateEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{22}
}

func (x *GetPrivateEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetPrivateEndpointResponse struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// The private endpoint connection status of the private endpoint. Possible values:
	// 'Deleting', 'Failed', 'Succeeded', 'Updating'.
	PrivateEndpointStatus PrivateEndpointStatus `protobuf:"varint,2,opt,name=private_endpoint_status,json=privateEndpointStatus,proto3,enum=services.azr.PrivateEndpointStatus" json:"private_endpoint_status,omitempty"`
	PrivateEndpointIp     string                `protobuf:"bytes,3,opt,name=private_endpoint_ip,json=privateEndpointIp,proto3" json:"private_endpoint_ip,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *GetPrivateEndpointResponse) Reset() {
	*x = GetPrivateEndpointResponse{}
	mi := &file_services_azr_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrivateEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrivateEndpointResponse) ProtoMessage() {}

func (x *GetPrivateEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrivateEndpointResponse.ProtoReflect.Descriptor instead.
func (*GetPrivateEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{23}
}

func (x *GetPrivateEndpointResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPrivateEndpointResponse) GetPrivateEndpointStatus() PrivateEndpointStatus {
	if x != nil {
		return x.PrivateEndpointStatus
	}
	return PrivateEndpointStatus_STATUS_UNSPECIFIED
}

func (x *GetPrivateEndpointResponse) GetPrivateEndpointIp() string {
	if x != nil {
		return x.PrivateEndpointIp
	}
	return ""
}

type DeletePrivateEndpointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePrivateEndpointRequest) Reset() {
	*x = DeletePrivateEndpointRequest{}
	mi := &file_services_azr_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePrivateEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePrivateEndpointRequest) ProtoMessage() {}

func (x *DeletePrivateEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePrivateEndpointRequest.ProtoReflect.Descriptor instead.
func (*DeletePrivateEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{24}
}

func (x *DeletePrivateEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeletePrivateEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePrivateEndpointResponse) Reset() {
	*x = DeletePrivateEndpointResponse{}
	mi := &file_services_azr_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePrivateEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePrivateEndpointResponse) ProtoMessage() {}

func (x *DeletePrivateEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePrivateEndpointResponse.ProtoReflect.Descriptor instead.
func (*DeletePrivateEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{25}
}

func (x *DeletePrivateEndpointResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreatePGServerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Spec          *azr.PGServerSpec      `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePGServerRequest) Reset() {
	*x = CreatePGServerRequest{}
	mi := &file_services_azr_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePGServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePGServerRequest) ProtoMessage() {}

func (x *CreatePGServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePGServerRequest.ProtoReflect.Descriptor instead.
func (*CreatePGServerRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{26}
}

func (x *CreatePGServerRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreatePGServerRequest) GetSpec() *azr.PGServerSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
type CreatePGServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePGServerResponse) Reset() {
	*x = CreatePGServerResponse{}
	mi := &file_services_azr_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePGServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePGServerResponse) ProtoMessage() {}

func (x *CreatePGServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePGServerResponse.ProtoReflect.Descriptor instead.
func (*CreatePGServerResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{27}
}

func (x *CreatePGServerResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeletePGServerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePGServerRequest) Reset() {
	*x = DeletePGServerRequest{}
	mi := &file_services_azr_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePGServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePGServerRequest) ProtoMessage() {}

func (x *DeletePGServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePGServerRequest.ProtoReflect.Descriptor instead.
func (*DeletePGServerRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{28}
}

func (x *DeletePGServerRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, NOT_FOUND.
type DeletePGServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePGServerResponse) Reset() {
	*x = DeletePGServerResponse{}
	mi := &file_services_azr_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePGServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePGServerResponse) ProtoMessage() {}

func (x *DeletePGServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePGServerResponse.ProtoReflect.Descriptor instead.
func (*DeletePGServerResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{29}
}

func (x *DeletePGServerResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StartPGServerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartPGServerRequest) Reset() {
	*x = StartPGServerRequest{}
	mi := &file_services_azr_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartPGServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPGServerRequest) ProtoMessage() {}

func (x *StartPGServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPGServerRequest.ProtoReflect.Descriptor instead.
func (*StartPGServerRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{30}
}

func (x *StartPGServerRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
type StartPGServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartPGServerResponse) Reset() {
	*x = StartPGServerResponse{}
	mi := &file_services_azr_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartPGServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartPGServerResponse) ProtoMessage() {}

func (x *StartPGServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartPGServerResponse.ProtoReflect.Descriptor instead.
func (*StartPGServerResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{31}
}

func (x *StartPGServerResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StopPGServerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopPGServerRequest) Reset() {
	*x = StopPGServerRequest{}
	mi := &file_services_azr_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopPGServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopPGServerRequest) ProtoMessage() {}

func (x *StopPGServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopPGServerRequest.ProtoReflect.Descriptor instead.
func (*StopPGServerRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{32}
}

func (x *StopPGServerRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
type StopPGServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopPGServerResponse) Reset() {
	*x = StopPGServerResponse{}
	mi := &file_services_azr_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopPGServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopPGServerResponse) ProtoMessage() {}

func (x *StopPGServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopPGServerResponse.ProtoReflect.Descriptor instead.
func (*StopPGServerResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{33}
}

func (x *StopPGServerResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPGServerRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPGServerRequest) Reset() {
	*x = GetPGServerRequest{}
	mi := &file_services_azr_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPGServerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPGServerRequest) ProtoMessage() {}

func (x *GetPGServerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPGServerRequest.ProtoReflect.Descriptor instead.
func (*GetPGServerRequest) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{34}
}

func (x *GetPGServerRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetPGServerResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	DomainName    *string                `protobuf:"bytes,2,opt,name=domain_name,json=domainName,proto3,oneof" json:"domain_name,omitempty"`
	ServerState   azr.PGServerState      `protobuf:"varint,3,opt,name=server_state,json=serverState,proto3,enum=common.azr.PGServerState" json:"server_state,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPGServerResponse) Reset() {
	*x = GetPGServerResponse{}
	mi := &file_services_azr_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPGServerResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPGServerResponse) ProtoMessage() {}

func (x *GetPGServerResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_azr_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPGServerResponse.ProtoReflect.Descriptor instead.
func (*GetPGServerResponse) Descriptor() ([]byte, []int) {
	return file_services_azr_proto_rawDescGZIP(), []int{35}
}

func (x *GetPGServerResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPGServerResponse) GetDomainName() string {
	if x != nil && x.DomainName != nil {
		return *x.DomainName
	}
	return ""
}

func (x *GetPGServerResponse) GetServerState() azr.PGServerState {
	if x != nil {
		return x.ServerState
	}
	return azr.PGServerState(0)
}

var File_services_azr_proto protoreflect.FileDescriptor

var file_services_azr_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x61, 0x7a, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61,
	0x7a, 0x72, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x7a, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x5f, 0x0a, 0x21,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x5e, 0x0a,
	0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5c, 0x0a,
	0x1e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x92, 0x01, 0x0a, 0x1f,
	0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x22, 0x5f, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x22, 0x5e, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xea, 0x01, 0x0a, 0x28, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x1b, 0x75, 0x73,
	0x65, 0x72, 0x5f, 0x61, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x5f, 0x69, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x18, 0x75, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x74, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x65,
	0x0a, 0x29, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x63, 0x0a, 0x25, 0x47, 0x65, 0x74, 0x46, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x59, 0x0a, 0x26, 0x47, 0x65,
	0x74, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x66, 0x0a, 0x28, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x65, 0x0a,
	0x29, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64,
	0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xc3, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x72, 0x69, 0x6e, 0x63, 0x69, 0x70, 0x61,
	0x6c, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x0f, 0x72, 0x6f, 0x6c, 0x65, 0x5f, 0x61, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x52, 0x6f, 0x6c, 0x65,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x0e, 0x72, 0x6f, 0x6c, 0x65,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x22, 0x58, 0x0a, 0x1c, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x56, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x4c, 0x0a, 0x19,
	0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x59, 0x0a, 0x1b, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x58, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x71, 0x0a, 0x0e, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e,
	0x74, 0x12, 0x4e, 0x0a, 0x12, 0x62, 0x6c, 0x6f, 0x62, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x42, 0x6c, 0x6f,
	0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52,
	0x10, 0x62, 0x6c, 0x6f, 0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0x6d, 0x0a, 0x10, 0x42, 0x6c, 0x6f, 0x62, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61, 0x69,
	0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6e, 0x74, 0x61,
	0x69, 0x6e, 0x65, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x72, 0x73, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x74, 0x6f, 0x72,
	0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x22, 0xde, 0x02, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x35,
	0x0a, 0x17, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x14, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x49, 0x64, 0x12, 0x33, 0x0a, 0x16, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x5f, 0x6c, 0x69, 0x6e, 0x6b, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x4c, 0x69,
	0x6e, 0x6b, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x58, 0x0a, 0x0a, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65, 0x78, 0x74, 0x72, 0x61,
	0x54, 0x61, 0x67, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72, 0x61, 0x54, 0x61, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x59, 0x0a, 0x1d, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x57, 0x0a,
	0x19, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0xda, 0x01, 0x0a, 0x1a, 0x47, 0x65, 0x74, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5b, 0x0a, 0x17, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x15, 0x70, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x65,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x49, 0x70, 0x22, 0x5a, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69,
	0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22,
	0x59, 0x0a, 0x1d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x81, 0x01, 0x0a, 0x15, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x2c, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x50, 0x47, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x22, 0x52,
	0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x53, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x47, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x52, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x52, 0x0a, 0x14, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22,
	0x4f, 0x0a, 0x15, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x51, 0x0a, 0x13, 0x53, 0x74, 0x6f, 0x70, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x4e, 0x0a, 0x14, 0x53, 0x74, 0x6f, 0x70, 0x50, 0x47, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x50, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0xba, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x47, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x24,
	0x0a, 0x0b, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x12, 0x3c, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x64, 0x6f, 0x6d, 0x61, 0x69, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x2a, 0x66, 0x0a, 0x15, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12, 0x53,
	0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45,
	0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0d, 0x0a,
	0x09, 0x53, 0x55, 0x43, 0x43, 0x45, 0x45, 0x44, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x32, 0xb7, 0x12, 0x0a, 0x12, 0x41,
	0x7a, 0x72, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72,
	0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x12, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e,
	0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73,
	0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x55, 0x73, 0x65, 0x72, 0x41,
	0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67,
	0x6e, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a,
	0x72, 0x2e, 0x47, 0x65, 0x74, 0x55, 0x73, 0x65, 0x72, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x65,
	0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f,
	0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x96, 0x01, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46,
	0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79,
	0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x37, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a,
	0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x96, 0x01,
	0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65,
	0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61,
	0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74,
	0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x8d, 0x01, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x46, 0x65,
	0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43,
	0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x12, 0x33, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x46, 0x65, 0x64, 0x65,
	0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x46, 0x65, 0x64, 0x65, 0x72, 0x61, 0x74, 0x65, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x74, 0x79, 0x43, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x29,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12,
	0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x52,
	0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74,
	0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69, 0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x6f, 0x6c, 0x65, 0x41, 0x73, 0x73, 0x69,
	0x67, 0x6e, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x72, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x72, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72,
	0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x2a, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x50,
	0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x27,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x47, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x47, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x47, 0x53, 0x65,
	0x72, 0x76, 0x65, 0x72, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50,
	0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a,
	0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72,
	0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a,
	0x0c, 0x53, 0x74, 0x6f, 0x70, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x21, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x53, 0x74, 0x6f,
	0x70, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e,
	0x53, 0x74, 0x6f, 0x70, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x50, 0x47, 0x53,
	0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76, 0x65, 0x72,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x47, 0x53, 0x65, 0x72, 0x76,
	0x65, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0b,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4d,
	0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62,
	0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67,
	0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x61, 0x7a, 0x72, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_azr_proto_rawDescOnce sync.Once
	file_services_azr_proto_rawDescData []byte
)

func file_services_azr_proto_rawDescGZIP() []byte {
	file_services_azr_proto_rawDescOnce.Do(func() {
		file_services_azr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_azr_proto_rawDesc), len(file_services_azr_proto_rawDesc)))
	})
	return file_services_azr_proto_rawDescData
}

var file_services_azr_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_services_azr_proto_msgTypes = make([]protoimpl.MessageInfo, 37)
var file_services_azr_proto_goTypes = []any{
	(PrivateEndpointStatus)(0),                           // 0: services.azr.PrivateEndpointStatus
	(*CreateUserAssignedIdentityRequest)(nil),            // 1: services.azr.CreateUserAssignedIdentityRequest
	(*CreateUserAssignedIdentityResponse)(nil),           // 2: services.azr.CreateUserAssignedIdentityResponse
	(*GetUserAssignedIdentityRequest)(nil),               // 3: services.azr.GetUserAssignedIdentityRequest
	(*GetUserAssignedIdentityResponse)(nil),              // 4: services.azr.GetUserAssignedIdentityResponse
	(*DeleteUserAssignedIdentityRequest)(nil),            // 5: services.azr.DeleteUserAssignedIdentityRequest
	(*DeleteUserAssignedIdentityResponse)(nil),           // 6: services.azr.DeleteUserAssignedIdentityResponse
	(*CreateFederatedIdentityCredentialRequest)(nil),     // 7: services.azr.CreateFederatedIdentityCredentialRequest
	(*CreateFederatedIdentityCredentialResponse)(nil),    // 8: services.azr.CreateFederatedIdentityCredentialResponse
	(*GetFederatedIdentityCredentialRequest)(nil),        // 9: services.azr.GetFederatedIdentityCredentialRequest
	(*GetFederatedIdentityCredentialResponse)(nil),       // 10: services.azr.GetFederatedIdentityCredentialResponse
	(*DeleteFederatedIdentityCredentialRequest)(nil),     // 11: services.azr.DeleteFederatedIdentityCredentialRequest
	(*DeleteFederatedIdentityCredentialResponse)(nil),    // 12: services.azr.DeleteFederatedIdentityCredentialResponse
	(*CreateRoleAssignmentRequest)(nil),                  // 13: services.azr.CreateRoleAssignmentRequest
	(*CreateRoleAssignmentResponse)(nil),                 // 14: services.azr.CreateRoleAssignmentResponse
	(*GetRoleAssignmentRequest)(nil),                     // 15: services.azr.GetRoleAssignmentRequest
	(*GetRoleAssignmentResponse)(nil),                    // 16: services.azr.GetRoleAssignmentResponse
	(*DeleteRoleAssignmentRequest)(nil),                  // 17: services.azr.DeleteRoleAssignmentRequest
	(*DeleteRoleAssignmentResponse)(nil),                 // 18: services.azr.DeleteRoleAssignmentResponse
	(*RoleAssignment)(nil),                               // 19: services.azr.RoleAssignment
	(*BlobAccessOption)(nil),                             // 20: services.azr.BlobAccessOption
	(*CreatePrivateEndpointRequest)(nil),                 // 21: services.azr.CreatePrivateEndpointRequest
	(*CreatePrivateEndpointResponse)(nil),                // 22: services.azr.CreatePrivateEndpointResponse
	(*GetPrivateEndpointRequest)(nil),                    // 23: services.azr.GetPrivateEndpointRequest
	(*GetPrivateEndpointResponse)(nil),                   // 24: services.azr.GetPrivateEndpointResponse
	(*DeletePrivateEndpointRequest)(nil),                 // 25: services.azr.DeletePrivateEndpointRequest
	(*DeletePrivateEndpointResponse)(nil),                // 26: services.azr.DeletePrivateEndpointResponse
	(*CreatePGServerRequest)(nil),                        // 27: services.azr.CreatePGServerRequest
	(*CreatePGServerResponse)(nil),                       // 28: services.azr.CreatePGServerResponse
	(*DeletePGServerRequest)(nil),                        // 29: services.azr.DeletePGServerRequest
	(*DeletePGServerResponse)(nil),                       // 30: services.azr.DeletePGServerResponse
	(*StartPGServerRequest)(nil),                         // 31: services.azr.StartPGServerRequest
	(*StartPGServerResponse)(nil),                        // 32: services.azr.StartPGServerResponse
	(*StopPGServerRequest)(nil),                          // 33: services.azr.StopPGServerRequest
	(*StopPGServerResponse)(nil),                         // 34: services.azr.StopPGServerResponse
	(*GetPGServerRequest)(nil),                           // 35: services.azr.GetPGServerRequest
	(*GetPGServerResponse)(nil),                          // 36: services.azr.GetPGServerResponse
	nil,                                                  // 37: services.azr.CreatePrivateEndpointRequest.ExtraTagsEntry
	(*resource.Meta)(nil),                                // 38: common.resource.Meta
	(*creation.Status)(nil),                              // 39: common.resource.creation.Status
	(*resource.Status)(nil),                              // 40: common.resource.Status
	(*deletion.Status)(nil),                              // 41: common.resource.deletion.Status
	(*k8s.ServiceAccount)(nil),                           // 42: common.k8s.ServiceAccount
	(*azr.PGServerSpec)(nil),                             // 43: common.azr.PGServerSpec
	(*update.Status)(nil),                                // 44: common.resource.update.Status
	(azr.PGServerState)(0),                               // 45: common.azr.PGServerState
	(*data.CreateDataDirectoryDeletionTaskRequest)(nil),  // 46: services.data.CreateDataDirectoryDeletionTaskRequest
	(*data.CreateDataDirectoryCloneTaskRequest)(nil),     // 47: services.data.CreateDataDirectoryCloneTaskRequest
	(*data.GetManifestRequest)(nil),                      // 48: services.data.GetManifestRequest
	(*data.CreateDataDirectoryDeletionTaskResponse)(nil), // 49: services.data.CreateDataDirectoryDeletionTaskResponse
	(*data.CreateDataDirectoryCloneTaskResponse)(nil),    // 50: services.data.CreateDataDirectoryCloneTaskResponse
	(*data.GetManifestResponse)(nil),                     // 51: services.data.GetManifestResponse
}
var file_services_azr_proto_depIdxs = []int32{
	38, // 0: services.azr.CreateUserAssignedIdentityRequest.resource_meta:type_name -> common.resource.Meta
	39, // 1: services.azr.CreateUserAssignedIdentityResponse.status:type_name -> common.resource.creation.Status
	38, // 2: services.azr.GetUserAssignedIdentityRequest.resource_meta:type_name -> common.resource.Meta
	40, // 3: services.azr.GetUserAssignedIdentityResponse.status:type_name -> common.resource.Status
	38, // 4: services.azr.DeleteUserAssignedIdentityRequest.resource_meta:type_name -> common.resource.Meta
	41, // 5: services.azr.DeleteUserAssignedIdentityResponse.status:type_name -> common.resource.deletion.Status
	38, // 6: services.azr.CreateFederatedIdentityCredentialRequest.resource_meta:type_name -> common.resource.Meta
	42, // 7: services.azr.CreateFederatedIdentityCredentialRequest.service_account:type_name -> common.k8s.ServiceAccount
	39, // 8: services.azr.CreateFederatedIdentityCredentialResponse.status:type_name -> common.resource.creation.Status
	38, // 9: services.azr.GetFederatedIdentityCredentialRequest.resource_meta:type_name -> common.resource.Meta
	40, // 10: services.azr.GetFederatedIdentityCredentialResponse.status:type_name -> common.resource.Status
	38, // 11: services.azr.DeleteFederatedIdentityCredentialRequest.resource_meta:type_name -> common.resource.Meta
	41, // 12: services.azr.DeleteFederatedIdentityCredentialResponse.status:type_name -> common.resource.deletion.Status
	38, // 13: services.azr.CreateRoleAssignmentRequest.resource_meta:type_name -> common.resource.Meta
	19, // 14: services.azr.CreateRoleAssignmentRequest.role_assignment:type_name -> services.azr.RoleAssignment
	39, // 15: services.azr.CreateRoleAssignmentResponse.status:type_name -> common.resource.creation.Status
	38, // 16: services.azr.GetRoleAssignmentRequest.resource_meta:type_name -> common.resource.Meta
	40, // 17: services.azr.GetRoleAssignmentResponse.status:type_name -> common.resource.Status
	38, // 18: services.azr.DeleteRoleAssignmentRequest.resource_meta:type_name -> common.resource.Meta
	41, // 19: services.azr.DeleteRoleAssignmentResponse.status:type_name -> common.resource.deletion.Status
	20, // 20: services.azr.RoleAssignment.blob_access_option:type_name -> services.azr.BlobAccessOption
	38, // 21: services.azr.CreatePrivateEndpointRequest.resource_meta:type_name -> common.resource.Meta
	37, // 22: services.azr.CreatePrivateEndpointRequest.extra_tags:type_name -> services.azr.CreatePrivateEndpointRequest.ExtraTagsEntry
	39, // 23: services.azr.CreatePrivateEndpointResponse.status:type_name -> common.resource.creation.Status
	38, // 24: services.azr.GetPrivateEndpointRequest.resource_meta:type_name -> common.resource.Meta
	40, // 25: services.azr.GetPrivateEndpointResponse.status:type_name -> common.resource.Status
	0,  // 26: services.azr.GetPrivateEndpointResponse.private_endpoint_status:type_name -> services.azr.PrivateEndpointStatus
	38, // 27: services.azr.DeletePrivateEndpointRequest.resource_meta:type_name -> common.resource.Meta
	41, // 28: services.azr.DeletePrivateEndpointResponse.status:type_name -> common.resource.deletion.Status
	38, // 29: services.azr.CreatePGServerRequest.resource_meta:type_name -> common.resource.Meta
	43, // 30: services.azr.CreatePGServerRequest.spec:type_name -> common.azr.PGServerSpec
	39, // 31: services.azr.CreatePGServerResponse.status:type_name -> common.resource.creation.Status
	38, // 32: services.azr.DeletePGServerRequest.resource_meta:type_name -> common.resource.Meta
	41, // 33: services.azr.DeletePGServerResponse.status:type_name -> common.resource.deletion.Status
	38, // 34: services.azr.StartPGServerRequest.resource_meta:type_name -> common.resource.Meta
	44, // 35: services.azr.StartPGServerResponse.status:type_name -> common.resource.update.Status
	38, // 36: services.azr.StopPGServerRequest.resource_meta:type_name -> common.resource.Meta
	44, // 37: services.azr.StopPGServerResponse.status:type_name -> common.resource.update.Status
	38, // 38: services.azr.GetPGServerRequest.resource_meta:type_name -> common.resource.Meta
	40, // 39: services.azr.GetPGServerResponse.status:type_name -> common.resource.Status
	45, // 40: services.azr.GetPGServerResponse.server_state:type_name -> common.azr.PGServerState
	1,  // 41: services.azr.AzrResourceManager.CreateUserAssignedIdentity:input_type -> services.azr.CreateUserAssignedIdentityRequest
	5,  // 42: services.azr.AzrResourceManager.DeleteUserAssignedIdentity:input_type -> services.azr.DeleteUserAssignedIdentityRequest
	3,  // 43: services.azr.AzrResourceManager.GetUserAssignedIdentity:input_type -> services.azr.GetUserAssignedIdentityRequest
	46, // 44: services.azr.AzrResourceManager.CreateDataDirectoryDeletionTask:input_type -> services.data.CreateDataDirectoryDeletionTaskRequest
	47, // 45: services.azr.AzrResourceManager.CreateDataDirectoryCloneTask:input_type -> services.data.CreateDataDirectoryCloneTaskRequest
	7,  // 46: services.azr.AzrResourceManager.CreateFederatedIdentityCredential:input_type -> services.azr.CreateFederatedIdentityCredentialRequest
	11, // 47: services.azr.AzrResourceManager.DeleteFederatedIdentityCredential:input_type -> services.azr.DeleteFederatedIdentityCredentialRequest
	9,  // 48: services.azr.AzrResourceManager.GetFederatedIdentityCredential:input_type -> services.azr.GetFederatedIdentityCredentialRequest
	13, // 49: services.azr.AzrResourceManager.CreateRoleAssignment:input_type -> services.azr.CreateRoleAssignmentRequest
	17, // 50: services.azr.AzrResourceManager.DeleteRoleAssignment:input_type -> services.azr.DeleteRoleAssignmentRequest
	15, // 51: services.azr.AzrResourceManager.GetRoleAssignment:input_type -> services.azr.GetRoleAssignmentRequest
	21, // 52: services.azr.AzrResourceManager.CreatePrivateEndpoint:input_type -> services.azr.CreatePrivateEndpointRequest
	25, // 53: services.azr.AzrResourceManager.DeletePrivateEndpoint:input_type -> services.azr.DeletePrivateEndpointRequest
	23, // 54: services.azr.AzrResourceManager.GetPrivateEndpoint:input_type -> services.azr.GetPrivateEndpointRequest
	27, // 55: services.azr.AzrResourceManager.CreatePGServer:input_type -> services.azr.CreatePGServerRequest
	29, // 56: services.azr.AzrResourceManager.DeletePGServer:input_type -> services.azr.DeletePGServerRequest
	31, // 57: services.azr.AzrResourceManager.StartPGServer:input_type -> services.azr.StartPGServerRequest
	33, // 58: services.azr.AzrResourceManager.StopPGServer:input_type -> services.azr.StopPGServerRequest
	35, // 59: services.azr.AzrResourceManager.GetPGServer:input_type -> services.azr.GetPGServerRequest
	48, // 60: services.azr.AzrResourceManager.GetManifest:input_type -> services.data.GetManifestRequest
	2,  // 61: services.azr.AzrResourceManager.CreateUserAssignedIdentity:output_type -> services.azr.CreateUserAssignedIdentityResponse
	6,  // 62: services.azr.AzrResourceManager.DeleteUserAssignedIdentity:output_type -> services.azr.DeleteUserAssignedIdentityResponse
	4,  // 63: services.azr.AzrResourceManager.GetUserAssignedIdentity:output_type -> services.azr.GetUserAssignedIdentityResponse
	49, // 64: services.azr.AzrResourceManager.CreateDataDirectoryDeletionTask:output_type -> services.data.CreateDataDirectoryDeletionTaskResponse
	50, // 65: services.azr.AzrResourceManager.CreateDataDirectoryCloneTask:output_type -> services.data.CreateDataDirectoryCloneTaskResponse
	8,  // 66: services.azr.AzrResourceManager.CreateFederatedIdentityCredential:output_type -> services.azr.CreateFederatedIdentityCredentialResponse
	12, // 67: services.azr.AzrResourceManager.DeleteFederatedIdentityCredential:output_type -> services.azr.DeleteFederatedIdentityCredentialResponse
	10, // 68: services.azr.AzrResourceManager.GetFederatedIdentityCredential:output_type -> services.azr.GetFederatedIdentityCredentialResponse
	14, // 69: services.azr.AzrResourceManager.CreateRoleAssignment:output_type -> services.azr.CreateRoleAssignmentResponse
	18, // 70: services.azr.AzrResourceManager.DeleteRoleAssignment:output_type -> services.azr.DeleteRoleAssignmentResponse
	16, // 71: services.azr.AzrResourceManager.GetRoleAssignment:output_type -> services.azr.GetRoleAssignmentResponse
	22, // 72: services.azr.AzrResourceManager.CreatePrivateEndpoint:output_type -> services.azr.CreatePrivateEndpointResponse
	26, // 73: services.azr.AzrResourceManager.DeletePrivateEndpoint:output_type -> services.azr.DeletePrivateEndpointResponse
	24, // 74: services.azr.AzrResourceManager.GetPrivateEndpoint:output_type -> services.azr.GetPrivateEndpointResponse
	28, // 75: services.azr.AzrResourceManager.CreatePGServer:output_type -> services.azr.CreatePGServerResponse
	30, // 76: services.azr.AzrResourceManager.DeletePGServer:output_type -> services.azr.DeletePGServerResponse
	32, // 77: services.azr.AzrResourceManager.StartPGServer:output_type -> services.azr.StartPGServerResponse
	34, // 78: services.azr.AzrResourceManager.StopPGServer:output_type -> services.azr.StopPGServerResponse
	36, // 79: services.azr.AzrResourceManager.GetPGServer:output_type -> services.azr.GetPGServerResponse
	51, // 80: services.azr.AzrResourceManager.GetManifest:output_type -> services.data.GetManifestResponse
	61, // [61:81] is the sub-list for method output_type
	41, // [41:61] is the sub-list for method input_type
	41, // [41:41] is the sub-list for extension type_name
	41, // [41:41] is the sub-list for extension extendee
	0,  // [0:41] is the sub-list for field type_name
}

func init() { file_services_azr_proto_init() }
func file_services_azr_proto_init() {
	if File_services_azr_proto != nil {
		return
	}
	file_services_azr_proto_msgTypes[18].OneofWrappers = []any{
		(*RoleAssignment_BlobAccessOption)(nil),
	}
	file_services_azr_proto_msgTypes[35].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_azr_proto_rawDesc), len(file_services_azr_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   37,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_azr_proto_goTypes,
		DependencyIndexes: file_services_azr_proto_depIdxs,
		EnumInfos:         file_services_azr_proto_enumTypes,
		MessageInfos:      file_services_azr_proto_msgTypes,
	}.Build()
	File_services_azr_proto = out.File
	file_services_azr_proto_goTypes = nil
	file_services_azr_proto_depIdxs = nil
}
