// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/gcp (interfaces: GcpResourceManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/gcp -package=gcp -destination=pbgen/services/gcp/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/gcp GcpResourceManagerClient
//

// Package gcp is a generated GoMock package.
package gcp

import (
	context "context"
	reflect "reflect"

	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockGcpResourceManagerClient is a mock of GcpResourceManagerClient interface.
type MockGcpResourceManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockGcpResourceManagerClientMockRecorder
	isgomock struct{}
}

// MockGcpResourceManagerClientMockRecorder is the mock recorder for MockGcpResourceManagerClient.
type MockGcpResourceManagerClientMockRecorder struct {
	mock *MockGcpResourceManagerClient
}

// NewMockGcpResourceManagerClient creates a new mock instance.
func NewMockGcpResourceManagerClient(ctrl *gomock.Controller) *MockGcpResourceManagerClient {
	mock := &MockGcpResourceManagerClient{ctrl: ctrl}
	mock.recorder = &MockGcpResourceManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGcpResourceManagerClient) EXPECT() *MockGcpResourceManagerClientMockRecorder {
	return m.recorder
}

// CreateDataDirectoryCloneTask mocks base method.
func (m *MockGcpResourceManagerClient) CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDataDirectoryCloneTask", varargs...)
	ret0, _ := ret[0].(*data.CreateDataDirectoryCloneTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataDirectoryCloneTask indicates an expected call of CreateDataDirectoryCloneTask.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateDataDirectoryCloneTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataDirectoryCloneTask", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateDataDirectoryCloneTask), varargs...)
}

// CreateDataDirectoryDeletionTask mocks base method.
func (m *MockGcpResourceManagerClient) CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDataDirectoryDeletionTask", varargs...)
	ret0, _ := ret[0].(*data.CreateDataDirectoryDeletionTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataDirectoryDeletionTask indicates an expected call of CreateDataDirectoryDeletionTask.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateDataDirectoryDeletionTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataDirectoryDeletionTask", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateDataDirectoryDeletionTask), varargs...)
}

// CreateIAMPolicyKSABinding mocks base method.
func (m *MockGcpResourceManagerClient) CreateIAMPolicyKSABinding(ctx context.Context, in *CreateIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*CreateIAMPolicyKSABindingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIAMPolicyKSABinding", varargs...)
	ret0, _ := ret[0].(*CreateIAMPolicyKSABindingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIAMPolicyKSABinding indicates an expected call of CreateIAMPolicyKSABinding.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateIAMPolicyKSABinding(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIAMPolicyKSABinding", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateIAMPolicyKSABinding), varargs...)
}

// CreateIAMPolicyRoleBinding mocks base method.
func (m *MockGcpResourceManagerClient) CreateIAMPolicyRoleBinding(ctx context.Context, in *CreateIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*CreateIAMPolicyRoleBindingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIAMPolicyRoleBinding", varargs...)
	ret0, _ := ret[0].(*CreateIAMPolicyRoleBindingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIAMPolicyRoleBinding indicates an expected call of CreateIAMPolicyRoleBinding.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateIAMPolicyRoleBinding(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIAMPolicyRoleBinding", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateIAMPolicyRoleBinding), varargs...)
}

// CreateIAMServiceAccount mocks base method.
func (m *MockGcpResourceManagerClient) CreateIAMServiceAccount(ctx context.Context, in *CreateIAMServiceAccountRequest, opts ...grpc.CallOption) (*CreateIAMServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIAMServiceAccount", varargs...)
	ret0, _ := ret[0].(*CreateIAMServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIAMServiceAccount indicates an expected call of CreateIAMServiceAccount.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateIAMServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIAMServiceAccount", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateIAMServiceAccount), varargs...)
}

// CreateIPAddress mocks base method.
func (m *MockGcpResourceManagerClient) CreateIPAddress(ctx context.Context, in *CreateIPAddressRequest, opts ...grpc.CallOption) (*CreateIPAddressResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIPAddress", varargs...)
	ret0, _ := ret[0].(*CreateIPAddressResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIPAddress indicates an expected call of CreateIPAddress.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateIPAddress(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIPAddress", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateIPAddress), varargs...)
}

// CreatePrivateServiceConnectEndpoint mocks base method.
func (m *MockGcpResourceManagerClient) CreatePrivateServiceConnectEndpoint(ctx context.Context, in *CreatePrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*CreatePrivateServiceConnectEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePrivateServiceConnectEndpoint", varargs...)
	ret0, _ := ret[0].(*CreatePrivateServiceConnectEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePrivateServiceConnectEndpoint indicates an expected call of CreatePrivateServiceConnectEndpoint.
func (mr *MockGcpResourceManagerClientMockRecorder) CreatePrivateServiceConnectEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePrivateServiceConnectEndpoint", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreatePrivateServiceConnectEndpoint), varargs...)
}

// CreateSQLInstance mocks base method.
func (m *MockGcpResourceManagerClient) CreateSQLInstance(ctx context.Context, in *CreateSQLInstanceRequest, opts ...grpc.CallOption) (*CreateSQLInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSQLInstance", varargs...)
	ret0, _ := ret[0].(*CreateSQLInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSQLInstance indicates an expected call of CreateSQLInstance.
func (mr *MockGcpResourceManagerClientMockRecorder) CreateSQLInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSQLInstance", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).CreateSQLInstance), varargs...)
}

// DeleteIAMPolicyKSABinding mocks base method.
func (m *MockGcpResourceManagerClient) DeleteIAMPolicyKSABinding(ctx context.Context, in *DeleteIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyKSABindingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIAMPolicyKSABinding", varargs...)
	ret0, _ := ret[0].(*DeleteIAMPolicyKSABindingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIAMPolicyKSABinding indicates an expected call of DeleteIAMPolicyKSABinding.
func (mr *MockGcpResourceManagerClientMockRecorder) DeleteIAMPolicyKSABinding(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIAMPolicyKSABinding", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).DeleteIAMPolicyKSABinding), varargs...)
}

// DeleteIAMPolicyRoleBinding mocks base method.
func (m *MockGcpResourceManagerClient) DeleteIAMPolicyRoleBinding(ctx context.Context, in *DeleteIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyRoleBindingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIAMPolicyRoleBinding", varargs...)
	ret0, _ := ret[0].(*DeleteIAMPolicyRoleBindingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIAMPolicyRoleBinding indicates an expected call of DeleteIAMPolicyRoleBinding.
func (mr *MockGcpResourceManagerClientMockRecorder) DeleteIAMPolicyRoleBinding(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIAMPolicyRoleBinding", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).DeleteIAMPolicyRoleBinding), varargs...)
}

// DeleteIAMServiceAccount mocks base method.
func (m *MockGcpResourceManagerClient) DeleteIAMServiceAccount(ctx context.Context, in *DeleteIAMServiceAccountRequest, opts ...grpc.CallOption) (*DeleteIAMServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIAMServiceAccount", varargs...)
	ret0, _ := ret[0].(*DeleteIAMServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIAMServiceAccount indicates an expected call of DeleteIAMServiceAccount.
func (mr *MockGcpResourceManagerClientMockRecorder) DeleteIAMServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIAMServiceAccount", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).DeleteIAMServiceAccount), varargs...)
}

// DeleteIPAddress mocks base method.
func (m *MockGcpResourceManagerClient) DeleteIPAddress(ctx context.Context, in *DeleteIPAddressRequest, opts ...grpc.CallOption) (*DeleteIPAddressResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIPAddress", varargs...)
	ret0, _ := ret[0].(*DeleteIPAddressResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIPAddress indicates an expected call of DeleteIPAddress.
func (mr *MockGcpResourceManagerClientMockRecorder) DeleteIPAddress(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIPAddress", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).DeleteIPAddress), varargs...)
}

// DeletePrivateServiceConnectEndpoint mocks base method.
func (m *MockGcpResourceManagerClient) DeletePrivateServiceConnectEndpoint(ctx context.Context, in *DeletePrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*DeletePrivateServiceConnectEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePrivateServiceConnectEndpoint", varargs...)
	ret0, _ := ret[0].(*DeletePrivateServiceConnectEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePrivateServiceConnectEndpoint indicates an expected call of DeletePrivateServiceConnectEndpoint.
func (mr *MockGcpResourceManagerClientMockRecorder) DeletePrivateServiceConnectEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePrivateServiceConnectEndpoint", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).DeletePrivateServiceConnectEndpoint), varargs...)
}

// DeleteSQLInstance mocks base method.
func (m *MockGcpResourceManagerClient) DeleteSQLInstance(ctx context.Context, in *DeleteSQLInstanceRequest, opts ...grpc.CallOption) (*DeleteSQLInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSQLInstance", varargs...)
	ret0, _ := ret[0].(*DeleteSQLInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSQLInstance indicates an expected call of DeleteSQLInstance.
func (mr *MockGcpResourceManagerClientMockRecorder) DeleteSQLInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSQLInstance", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).DeleteSQLInstance), varargs...)
}

// GetIAMPolicyKSABinding mocks base method.
func (m *MockGcpResourceManagerClient) GetIAMPolicyKSABinding(ctx context.Context, in *GetIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*GetIAMPolicyKSABindingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIAMPolicyKSABinding", varargs...)
	ret0, _ := ret[0].(*GetIAMPolicyKSABindingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIAMPolicyKSABinding indicates an expected call of GetIAMPolicyKSABinding.
func (mr *MockGcpResourceManagerClientMockRecorder) GetIAMPolicyKSABinding(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIAMPolicyKSABinding", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetIAMPolicyKSABinding), varargs...)
}

// GetIAMPolicyRoleBinding mocks base method.
func (m *MockGcpResourceManagerClient) GetIAMPolicyRoleBinding(ctx context.Context, in *GetIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*GetIAMPolicyRoleBindingResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIAMPolicyRoleBinding", varargs...)
	ret0, _ := ret[0].(*GetIAMPolicyRoleBindingResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIAMPolicyRoleBinding indicates an expected call of GetIAMPolicyRoleBinding.
func (mr *MockGcpResourceManagerClientMockRecorder) GetIAMPolicyRoleBinding(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIAMPolicyRoleBinding", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetIAMPolicyRoleBinding), varargs...)
}

// GetIAMServiceAccount mocks base method.
func (m *MockGcpResourceManagerClient) GetIAMServiceAccount(ctx context.Context, in *GetIAMServiceAccountRequest, opts ...grpc.CallOption) (*GetIAMServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIAMServiceAccount", varargs...)
	ret0, _ := ret[0].(*GetIAMServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIAMServiceAccount indicates an expected call of GetIAMServiceAccount.
func (mr *MockGcpResourceManagerClientMockRecorder) GetIAMServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIAMServiceAccount", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetIAMServiceAccount), varargs...)
}

// GetIPAddress mocks base method.
func (m *MockGcpResourceManagerClient) GetIPAddress(ctx context.Context, in *GetIPAddressRequest, opts ...grpc.CallOption) (*GetIPAddressResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIPAddress", varargs...)
	ret0, _ := ret[0].(*GetIPAddressResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIPAddress indicates an expected call of GetIPAddress.
func (mr *MockGcpResourceManagerClientMockRecorder) GetIPAddress(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIPAddress", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetIPAddress), varargs...)
}

// GetManifest mocks base method.
func (m *MockGcpResourceManagerClient) GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManifest", varargs...)
	ret0, _ := ret[0].(*data.GetManifestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManifest indicates an expected call of GetManifest.
func (mr *MockGcpResourceManagerClientMockRecorder) GetManifest(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManifest", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetManifest), varargs...)
}

// GetPrivateServiceConnectEndpoint mocks base method.
func (m *MockGcpResourceManagerClient) GetPrivateServiceConnectEndpoint(ctx context.Context, in *GetPrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*GetPrivateServiceConnectEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPrivateServiceConnectEndpoint", varargs...)
	ret0, _ := ret[0].(*GetPrivateServiceConnectEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPrivateServiceConnectEndpoint indicates an expected call of GetPrivateServiceConnectEndpoint.
func (mr *MockGcpResourceManagerClientMockRecorder) GetPrivateServiceConnectEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPrivateServiceConnectEndpoint", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetPrivateServiceConnectEndpoint), varargs...)
}

// GetSQLInstance mocks base method.
func (m *MockGcpResourceManagerClient) GetSQLInstance(ctx context.Context, in *GetSQLInstanceRequest, opts ...grpc.CallOption) (*GetSQLInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSQLInstance", varargs...)
	ret0, _ := ret[0].(*GetSQLInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSQLInstance indicates an expected call of GetSQLInstance.
func (mr *MockGcpResourceManagerClientMockRecorder) GetSQLInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSQLInstance", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).GetSQLInstance), varargs...)
}

// StartSQLInstance mocks base method.
func (m *MockGcpResourceManagerClient) StartSQLInstance(ctx context.Context, in *StartSQLInstanceRequest, opts ...grpc.CallOption) (*StartSQLInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartSQLInstance", varargs...)
	ret0, _ := ret[0].(*StartSQLInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartSQLInstance indicates an expected call of StartSQLInstance.
func (mr *MockGcpResourceManagerClientMockRecorder) StartSQLInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartSQLInstance", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).StartSQLInstance), varargs...)
}

// StopSQLInstance mocks base method.
func (m *MockGcpResourceManagerClient) StopSQLInstance(ctx context.Context, in *StopSQLInstanceRequest, opts ...grpc.CallOption) (*StopSQLInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopSQLInstance", varargs...)
	ret0, _ := ret[0].(*StopSQLInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopSQLInstance indicates an expected call of StopSQLInstance.
func (mr *MockGcpResourceManagerClientMockRecorder) StopSQLInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopSQLInstance", reflect.TypeOf((*MockGcpResourceManagerClient)(nil).StopSQLInstance), varargs...)
}
