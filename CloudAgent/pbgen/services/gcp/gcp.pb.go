// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/gcp.proto

package gcp

import (
	gcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	deletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	update "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type GCSRole int32

const (
	GCSRole_UNKNOWN       GCSRole = 0
	GCSRole_OBJECT_ADMIN  GCSRole = 1
	GCSRole_OBJECT_VIEWER GCSRole = 2
)

// Enum value maps for GCSRole.
var (
	GCSRole_name = map[int32]string{
		0: "UNKNOWN",
		1: "OBJECT_ADMIN",
		2: "OBJECT_VIEWER",
	}
	GCSRole_value = map[string]int32{
		"UNKNOWN":       0,
		"OBJECT_ADMIN":  1,
		"OBJECT_VIEWER": 2,
	}
)

func (x GCSRole) Enum() *GCSRole {
	p := new(GCSRole)
	*p = x
	return p
}

func (x GCSRole) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (GCSRole) Descriptor() protoreflect.EnumDescriptor {
	return file_services_gcp_proto_enumTypes[0].Descriptor()
}

func (GCSRole) Type() protoreflect.EnumType {
	return &file_services_gcp_proto_enumTypes[0]
}

func (x GCSRole) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use GCSRole.Descriptor instead.
func (GCSRole) EnumDescriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{0}
}

type PscStatus int32

const (
	PscStatus_STATUS_UNSPECIFIED PscStatus = 0
	PscStatus_PENDING            PscStatus = 1
	PscStatus_ACCEPTED           PscStatus = 2
	PscStatus_REJECTED           PscStatus = 3
	PscStatus_CLOSED             PscStatus = 4
)

// Enum value maps for PscStatus.
var (
	PscStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PENDING",
		2: "ACCEPTED",
		3: "REJECTED",
		4: "CLOSED",
	}
	PscStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING":            1,
		"ACCEPTED":           2,
		"REJECTED":           3,
		"CLOSED":             4,
	}
)

func (x PscStatus) Enum() *PscStatus {
	p := new(PscStatus)
	*p = x
	return p
}

func (x PscStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PscStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_services_gcp_proto_enumTypes[1].Descriptor()
}

func (PscStatus) Type() protoreflect.EnumType {
	return &file_services_gcp_proto_enumTypes[1]
}

func (x PscStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PscStatus.Descriptor instead.
func (PscStatus) EnumDescriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{1}
}

// Request for creating a rolebinding IAM policy. Empty policy is not accepted.
// So it's required to have at least one of the access options set.
type CreateIAMPolicyRoleBindingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource meta must meet the following restrictions:
	// - Name: as defined in RFC 1123. This is only used in k8 metadata name. i.e.:
	//  1. contain no more than 253 characters
	//  2. contain only lowercase alphanumeric characters, '-' or '.'
	//  3. start with an alphanumeric character
	//  4. end with an alphanumeric character
	//
	// - Namespace:
	//  1. Namespace is REQUIRED
	//  2. Must be valid RFC 1123 Label Names, i.e.:
	//     a. contain at most 63 characters
	//     b. contain only lowercase alphanumeric characters or '-'
	//     c. start with an alphanumeric character
	//     d. end with an alphanumeric character
	ResourceMeta *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Name of the IAMServiceAccount to be attached. Note that this is just the
	// name and not the entire account email. E.g, for the account
	// <EMAIL>, IAM_service_account_name
	// will be "testaccount"
	IAMServiceAccountName string `protobuf:"bytes,2,opt,name=IAM_service_account_name,json=IAMServiceAccountName,proto3" json:"IAM_service_account_name,omitempty"`
	// Which roles to bind.
	RoleBinding   *RoleBinding `protobuf:"bytes,3,opt,name=role_binding,json=roleBinding,proto3" json:"role_binding,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMPolicyRoleBindingRequest) Reset() {
	*x = CreateIAMPolicyRoleBindingRequest{}
	mi := &file_services_gcp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMPolicyRoleBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMPolicyRoleBindingRequest) ProtoMessage() {}

func (x *CreateIAMPolicyRoleBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMPolicyRoleBindingRequest.ProtoReflect.Descriptor instead.
func (*CreateIAMPolicyRoleBindingRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{0}
}

func (x *CreateIAMPolicyRoleBindingRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateIAMPolicyRoleBindingRequest) GetIAMServiceAccountName() string {
	if x != nil {
		return x.IAMServiceAccountName
	}
	return ""
}

func (x *CreateIAMPolicyRoleBindingRequest) GetRoleBinding() *RoleBinding {
	if x != nil {
		return x.RoleBinding
	}
	return nil
}

type CreateIAMPolicyRoleBindingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMPolicyRoleBindingResponse) Reset() {
	*x = CreateIAMPolicyRoleBindingResponse{}
	mi := &file_services_gcp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMPolicyRoleBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMPolicyRoleBindingResponse) ProtoMessage() {}

func (x *CreateIAMPolicyRoleBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMPolicyRoleBindingResponse.ProtoReflect.Descriptor instead.
func (*CreateIAMPolicyRoleBindingResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{1}
}

func (x *CreateIAMPolicyRoleBindingResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetIAMPolicyRoleBindingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMPolicyRoleBindingRequest) Reset() {
	*x = GetIAMPolicyRoleBindingRequest{}
	mi := &file_services_gcp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMPolicyRoleBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMPolicyRoleBindingRequest) ProtoMessage() {}

func (x *GetIAMPolicyRoleBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMPolicyRoleBindingRequest.ProtoReflect.Descriptor instead.
func (*GetIAMPolicyRoleBindingRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{2}
}

func (x *GetIAMPolicyRoleBindingRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetIAMPolicyRoleBindingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMPolicyRoleBindingResponse) Reset() {
	*x = GetIAMPolicyRoleBindingResponse{}
	mi := &file_services_gcp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMPolicyRoleBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMPolicyRoleBindingResponse) ProtoMessage() {}

func (x *GetIAMPolicyRoleBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMPolicyRoleBindingResponse.ProtoReflect.Descriptor instead.
func (*GetIAMPolicyRoleBindingResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{3}
}

func (x *GetIAMPolicyRoleBindingResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteIAMPolicyRoleBindingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMPolicyRoleBindingRequest) Reset() {
	*x = DeleteIAMPolicyRoleBindingRequest{}
	mi := &file_services_gcp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMPolicyRoleBindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMPolicyRoleBindingRequest) ProtoMessage() {}

func (x *DeleteIAMPolicyRoleBindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMPolicyRoleBindingRequest.ProtoReflect.Descriptor instead.
func (*DeleteIAMPolicyRoleBindingRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteIAMPolicyRoleBindingRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteIAMPolicyRoleBindingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMPolicyRoleBindingResponse) Reset() {
	*x = DeleteIAMPolicyRoleBindingResponse{}
	mi := &file_services_gcp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMPolicyRoleBindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMPolicyRoleBindingResponse) ProtoMessage() {}

func (x *DeleteIAMPolicyRoleBindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMPolicyRoleBindingResponse.ProtoReflect.Descriptor instead.
func (*DeleteIAMPolicyRoleBindingResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteIAMPolicyRoleBindingResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Request for creating a Kubernetes Service Account Binding IAM policy. Empty
// policy is not accepted. So it's required to have at least one of the access
// options set.
type CreateIAMPolicyKSABindingRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource meta must meet the following restrictions:
	// - Name: as defined in RFC 1123. This is only used in k8 metadata name. i.e.:
	//  1. contain no more than 253 characters
	//  2. contain only lowercase alphanumeric characters, '-' or '.'
	//  3. start with an alphanumeric character
	//  4. end with an alphanumeric character
	//
	// - Namespace:
	//  1. Namespace is REQUIRED
	//  2. Must be valid RFC 1123 Label Names, i.e.:
	//     a. contain at most 63 characters
	//     b. contain only lowercase alphanumeric characters or '-'
	//     c. start with an alphanumeric character
	//     d. end with an alphanumeric character
	ResourceMeta *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Name of the IAMServiceAccount to be attached. Note that this is just the
	// name and not the entire account email. E.g, for the account
	// <EMAIL>, IAM_service_account_name
	// will be "testaccount"
	IAMServiceAccountName string `protobuf:"bytes,2,opt,name=IAM_service_account_name,json=IAMServiceAccountName,proto3" json:"IAM_service_account_name,omitempty"`
	// Which kubernetes service account to bind.
	ServiceAccount *k8s.ServiceAccount `protobuf:"bytes,3,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateIAMPolicyKSABindingRequest) Reset() {
	*x = CreateIAMPolicyKSABindingRequest{}
	mi := &file_services_gcp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMPolicyKSABindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMPolicyKSABindingRequest) ProtoMessage() {}

func (x *CreateIAMPolicyKSABindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMPolicyKSABindingRequest.ProtoReflect.Descriptor instead.
func (*CreateIAMPolicyKSABindingRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{6}
}

func (x *CreateIAMPolicyKSABindingRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateIAMPolicyKSABindingRequest) GetIAMServiceAccountName() string {
	if x != nil {
		return x.IAMServiceAccountName
	}
	return ""
}

func (x *CreateIAMPolicyKSABindingRequest) GetServiceAccount() *k8s.ServiceAccount {
	if x != nil {
		return x.ServiceAccount
	}
	return nil
}

type CreateIAMPolicyKSABindingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMPolicyKSABindingResponse) Reset() {
	*x = CreateIAMPolicyKSABindingResponse{}
	mi := &file_services_gcp_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMPolicyKSABindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMPolicyKSABindingResponse) ProtoMessage() {}

func (x *CreateIAMPolicyKSABindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMPolicyKSABindingResponse.ProtoReflect.Descriptor instead.
func (*CreateIAMPolicyKSABindingResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{7}
}

func (x *CreateIAMPolicyKSABindingResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetIAMPolicyKSABindingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMPolicyKSABindingRequest) Reset() {
	*x = GetIAMPolicyKSABindingRequest{}
	mi := &file_services_gcp_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMPolicyKSABindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMPolicyKSABindingRequest) ProtoMessage() {}

func (x *GetIAMPolicyKSABindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMPolicyKSABindingRequest.ProtoReflect.Descriptor instead.
func (*GetIAMPolicyKSABindingRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{8}
}

func (x *GetIAMPolicyKSABindingRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetIAMPolicyKSABindingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMPolicyKSABindingResponse) Reset() {
	*x = GetIAMPolicyKSABindingResponse{}
	mi := &file_services_gcp_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMPolicyKSABindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMPolicyKSABindingResponse) ProtoMessage() {}

func (x *GetIAMPolicyKSABindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMPolicyKSABindingResponse.ProtoReflect.Descriptor instead.
func (*GetIAMPolicyKSABindingResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{9}
}

func (x *GetIAMPolicyKSABindingResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteIAMPolicyKSABindingRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMPolicyKSABindingRequest) Reset() {
	*x = DeleteIAMPolicyKSABindingRequest{}
	mi := &file_services_gcp_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMPolicyKSABindingRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMPolicyKSABindingRequest) ProtoMessage() {}

func (x *DeleteIAMPolicyKSABindingRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMPolicyKSABindingRequest.ProtoReflect.Descriptor instead.
func (*DeleteIAMPolicyKSABindingRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteIAMPolicyKSABindingRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteIAMPolicyKSABindingResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMPolicyKSABindingResponse) Reset() {
	*x = DeleteIAMPolicyKSABindingResponse{}
	mi := &file_services_gcp_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMPolicyKSABindingResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMPolicyKSABindingResponse) ProtoMessage() {}

func (x *DeleteIAMPolicyKSABindingResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMPolicyKSABindingResponse.ProtoReflect.Descriptor instead.
func (*DeleteIAMPolicyKSABindingResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{11}
}

func (x *DeleteIAMPolicyKSABindingResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateIAMServiceAccountRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource meta must meet the following restrictions:
	// - Name:
	//  1. contains between 6 and 30 characters
	//  2. contain only lowercase alphanumeric characters and '-'
	//  3. start with an alphanumeric character
	//  4. end with an alphanumeric character
	//
	// - Namespace:
	//  1. Namespace is REQUIRED
	//  2. Must be valid RFC 1123 Label Names, i.e.:
	//     a. contain at most 63 characters
	//     b. contain only lowercase alphanumeric characters or '-'
	//     c. start with an alphanumeric character
	//     d. end with an alphanumeric character
	ResourceMeta  *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMServiceAccountRequest) Reset() {
	*x = CreateIAMServiceAccountRequest{}
	mi := &file_services_gcp_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMServiceAccountRequest) ProtoMessage() {}

func (x *CreateIAMServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateIAMServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{12}
}

func (x *CreateIAMServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type CreateIAMServiceAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMServiceAccountResponse) Reset() {
	*x = CreateIAMServiceAccountResponse{}
	mi := &file_services_gcp_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMServiceAccountResponse) ProtoMessage() {}

func (x *CreateIAMServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateIAMServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{13}
}

func (x *CreateIAMServiceAccountResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetIAMServiceAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMServiceAccountRequest) Reset() {
	*x = GetIAMServiceAccountRequest{}
	mi := &file_services_gcp_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMServiceAccountRequest) ProtoMessage() {}

func (x *GetIAMServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*GetIAMServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{14}
}

func (x *GetIAMServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetIAMServiceAccountResponse struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// The e-mail address of the service account. This value should be referenced
	// from any google_iam_policy data sources that would grant the service
	// account privileges.
	AccountEmail  string `protobuf:"bytes,2,opt,name=account_email,json=accountEmail,proto3" json:"account_email,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMServiceAccountResponse) Reset() {
	*x = GetIAMServiceAccountResponse{}
	mi := &file_services_gcp_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMServiceAccountResponse) ProtoMessage() {}

func (x *GetIAMServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*GetIAMServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{15}
}

func (x *GetIAMServiceAccountResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetIAMServiceAccountResponse) GetAccountEmail() string {
	if x != nil {
		return x.AccountEmail
	}
	return ""
}

type DeleteIAMServiceAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMServiceAccountRequest) Reset() {
	*x = DeleteIAMServiceAccountRequest{}
	mi := &file_services_gcp_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMServiceAccountRequest) ProtoMessage() {}

func (x *DeleteIAMServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*DeleteIAMServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{16}
}

func (x *DeleteIAMServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteIAMServiceAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMServiceAccountResponse) Reset() {
	*x = DeleteIAMServiceAccountResponse{}
	mi := &file_services_gcp_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMServiceAccountResponse) ProtoMessage() {}

func (x *DeleteIAMServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*DeleteIAMServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{17}
}

func (x *DeleteIAMServiceAccountResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// RoleBinding specifies the type of role to bind to an IAM Service Account.
// Only GCS role is currently supported
type RoleBinding struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to AccessOption:
	//
	//	*RoleBinding_GcsAccessOption
	AccessOption  isRoleBinding_AccessOption `protobuf_oneof:"access_option"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RoleBinding) Reset() {
	*x = RoleBinding{}
	mi := &file_services_gcp_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RoleBinding) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RoleBinding) ProtoMessage() {}

func (x *RoleBinding) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RoleBinding.ProtoReflect.Descriptor instead.
func (*RoleBinding) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{18}
}

func (x *RoleBinding) GetAccessOption() isRoleBinding_AccessOption {
	if x != nil {
		return x.AccessOption
	}
	return nil
}

func (x *RoleBinding) GetGcsAccessOption() *GCSAccessOption {
	if x != nil {
		if x, ok := x.AccessOption.(*RoleBinding_GcsAccessOption); ok {
			return x.GcsAccessOption
		}
	}
	return nil
}

type isRoleBinding_AccessOption interface {
	isRoleBinding_AccessOption()
}

type RoleBinding_GcsAccessOption struct {
	// If specified, the create IAM policy will grant access to the specified
	// GCS location.
	GcsAccessOption *GCSAccessOption `protobuf:"bytes,1,opt,name=gcs_access_option,json=gcsAccessOption,proto3,oneof"`
}

func (*RoleBinding_GcsAccessOption) isRoleBinding_AccessOption() {}

// GCSAccessOption specifies a GCS directory a GCP IAM Policy should give
// access to. It will give access to all operations towards the directory.
type GCSAccessOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// GCS Bucket name.
	Bucket string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	// The directory of the GCS bucket.
	//
	// Deprecated: Marked as deprecated in services/gcp.proto.
	Dir string `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty"`
	// List of directories of the S3 bucket.
	Dirs []string `protobuf:"bytes,4,rep,name=dirs,proto3" json:"dirs,omitempty"`
	// GCS Role
	Role          GCSRole `protobuf:"varint,3,opt,name=role,proto3,enum=services.gcp.GCSRole" json:"role,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GCSAccessOption) Reset() {
	*x = GCSAccessOption{}
	mi := &file_services_gcp_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GCSAccessOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GCSAccessOption) ProtoMessage() {}

func (x *GCSAccessOption) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GCSAccessOption.ProtoReflect.Descriptor instead.
func (*GCSAccessOption) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{19}
}

func (x *GCSAccessOption) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

// Deprecated: Marked as deprecated in services/gcp.proto.
func (x *GCSAccessOption) GetDir() string {
	if x != nil {
		return x.Dir
	}
	return ""
}

func (x *GCSAccessOption) GetDirs() []string {
	if x != nil {
		return x.Dirs
	}
	return nil
}

func (x *GCSAccessOption) GetRole() GCSRole {
	if x != nil {
		return x.Role
	}
	return GCSRole_UNKNOWN
}

type CreateIPAddressRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// self-link to the subnet in which the ip will be provisioned
	IpSubnet      string `protobuf:"bytes,2,opt,name=ip_subnet,json=ipSubnet,proto3" json:"ip_subnet,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIPAddressRequest) Reset() {
	*x = CreateIPAddressRequest{}
	mi := &file_services_gcp_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIPAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIPAddressRequest) ProtoMessage() {}

func (x *CreateIPAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIPAddressRequest.ProtoReflect.Descriptor instead.
func (*CreateIPAddressRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{20}
}

func (x *CreateIPAddressRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateIPAddressRequest) GetIpSubnet() string {
	if x != nil {
		return x.IpSubnet
	}
	return ""
}

type CreateIPAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIPAddressResponse) Reset() {
	*x = CreateIPAddressResponse{}
	mi := &file_services_gcp_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIPAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIPAddressResponse) ProtoMessage() {}

func (x *CreateIPAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIPAddressResponse.ProtoReflect.Descriptor instead.
func (*CreateIPAddressResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{21}
}

func (x *CreateIPAddressResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetIPAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIPAddressRequest) Reset() {
	*x = GetIPAddressRequest{}
	mi := &file_services_gcp_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIPAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIPAddressRequest) ProtoMessage() {}

func (x *GetIPAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIPAddressRequest.ProtoReflect.Descriptor instead.
func (*GetIPAddressRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{22}
}

func (x *GetIPAddressRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetIPAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	IpSelflink    string                 `protobuf:"bytes,2,opt,name=ip_selflink,json=ipSelflink,proto3" json:"ip_selflink,omitempty"`
	IpAddress     string                 `protobuf:"bytes,3,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIPAddressResponse) Reset() {
	*x = GetIPAddressResponse{}
	mi := &file_services_gcp_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIPAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIPAddressResponse) ProtoMessage() {}

func (x *GetIPAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIPAddressResponse.ProtoReflect.Descriptor instead.
func (*GetIPAddressResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{23}
}

func (x *GetIPAddressResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetIPAddressResponse) GetIpSelflink() string {
	if x != nil {
		return x.IpSelflink
	}
	return ""
}

func (x *GetIPAddressResponse) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type DeleteIPAddressRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIPAddressRequest) Reset() {
	*x = DeleteIPAddressRequest{}
	mi := &file_services_gcp_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIPAddressRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIPAddressRequest) ProtoMessage() {}

func (x *DeleteIPAddressRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIPAddressRequest.ProtoReflect.Descriptor instead.
func (*DeleteIPAddressRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{24}
}

func (x *DeleteIPAddressRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteIPAddressResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIPAddressResponse) Reset() {
	*x = DeleteIPAddressResponse{}
	mi := &file_services_gcp_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIPAddressResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIPAddressResponse) ProtoMessage() {}

func (x *DeleteIPAddressResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIPAddressResponse.ProtoReflect.Descriptor instead.
func (*DeleteIPAddressResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{25}
}

func (x *DeleteIPAddressResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreatePrivateServiceConnectEndpointRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// self-link of private service conenct ip address
	PrivateServiceIp string `protobuf:"bytes,2,opt,name=private_service_ip,json=privateServiceIp,proto3" json:"private_service_ip,omitempty"`
	// target self-link
	Target        string            `protobuf:"bytes,3,opt,name=target,proto3" json:"target,omitempty"`
	ExtraLabels   map[string]string `protobuf:"bytes,4,rep,name=extra_labels,json=extraLabels,proto3" json:"extra_labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePrivateServiceConnectEndpointRequest) Reset() {
	*x = CreatePrivateServiceConnectEndpointRequest{}
	mi := &file_services_gcp_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePrivateServiceConnectEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePrivateServiceConnectEndpointRequest) ProtoMessage() {}

func (x *CreatePrivateServiceConnectEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePrivateServiceConnectEndpointRequest.ProtoReflect.Descriptor instead.
func (*CreatePrivateServiceConnectEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{26}
}

func (x *CreatePrivateServiceConnectEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreatePrivateServiceConnectEndpointRequest) GetPrivateServiceIp() string {
	if x != nil {
		return x.PrivateServiceIp
	}
	return ""
}

func (x *CreatePrivateServiceConnectEndpointRequest) GetTarget() string {
	if x != nil {
		return x.Target
	}
	return ""
}

func (x *CreatePrivateServiceConnectEndpointRequest) GetExtraLabels() map[string]string {
	if x != nil {
		return x.ExtraLabels
	}
	return nil
}

type CreatePrivateServiceConnectEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePrivateServiceConnectEndpointResponse) Reset() {
	*x = CreatePrivateServiceConnectEndpointResponse{}
	mi := &file_services_gcp_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePrivateServiceConnectEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePrivateServiceConnectEndpointResponse) ProtoMessage() {}

func (x *CreatePrivateServiceConnectEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePrivateServiceConnectEndpointResponse.ProtoReflect.Descriptor instead.
func (*CreatePrivateServiceConnectEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{27}
}

func (x *CreatePrivateServiceConnectEndpointResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPrivateServiceConnectEndpointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrivateServiceConnectEndpointRequest) Reset() {
	*x = GetPrivateServiceConnectEndpointRequest{}
	mi := &file_services_gcp_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrivateServiceConnectEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrivateServiceConnectEndpointRequest) ProtoMessage() {}

func (x *GetPrivateServiceConnectEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrivateServiceConnectEndpointRequest.ProtoReflect.Descriptor instead.
func (*GetPrivateServiceConnectEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{28}
}

func (x *GetPrivateServiceConnectEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetPrivateServiceConnectEndpointResponse struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// The PSC connection status of the PSC Forwarding Rule. Possible values:
	// 'STATUS_UNSPECIFIED', 'PENDING', 'ACCEPTED', 'REJECTED', 'CLOSED'.
	PscStatus PscStatus `protobuf:"varint,2,opt,name=psc_status,json=pscStatus,proto3,enum=services.gcp.PscStatus" json:"psc_status,omitempty"`
	// Deprecated: Marked as deprecated in services/gcp.proto.
	IpAddress     string `protobuf:"bytes,3,opt,name=ip_address,json=ipAddress,proto3" json:"ip_address,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPrivateServiceConnectEndpointResponse) Reset() {
	*x = GetPrivateServiceConnectEndpointResponse{}
	mi := &file_services_gcp_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPrivateServiceConnectEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPrivateServiceConnectEndpointResponse) ProtoMessage() {}

func (x *GetPrivateServiceConnectEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPrivateServiceConnectEndpointResponse.ProtoReflect.Descriptor instead.
func (*GetPrivateServiceConnectEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{29}
}

func (x *GetPrivateServiceConnectEndpointResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPrivateServiceConnectEndpointResponse) GetPscStatus() PscStatus {
	if x != nil {
		return x.PscStatus
	}
	return PscStatus_STATUS_UNSPECIFIED
}

// Deprecated: Marked as deprecated in services/gcp.proto.
func (x *GetPrivateServiceConnectEndpointResponse) GetIpAddress() string {
	if x != nil {
		return x.IpAddress
	}
	return ""
}

type DeletePrivateServiceConnectEndpointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePrivateServiceConnectEndpointRequest) Reset() {
	*x = DeletePrivateServiceConnectEndpointRequest{}
	mi := &file_services_gcp_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePrivateServiceConnectEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePrivateServiceConnectEndpointRequest) ProtoMessage() {}

func (x *DeletePrivateServiceConnectEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePrivateServiceConnectEndpointRequest.ProtoReflect.Descriptor instead.
func (*DeletePrivateServiceConnectEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{30}
}

func (x *DeletePrivateServiceConnectEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeletePrivateServiceConnectEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePrivateServiceConnectEndpointResponse) Reset() {
	*x = DeletePrivateServiceConnectEndpointResponse{}
	mi := &file_services_gcp_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePrivateServiceConnectEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePrivateServiceConnectEndpointResponse) ProtoMessage() {}

func (x *DeletePrivateServiceConnectEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePrivateServiceConnectEndpointResponse.ProtoReflect.Descriptor instead.
func (*DeletePrivateServiceConnectEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{31}
}

func (x *DeletePrivateServiceConnectEndpointResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateSQLInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Spec          *gcp.SQLInstanceSpec   `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSQLInstanceRequest) Reset() {
	*x = CreateSQLInstanceRequest{}
	mi := &file_services_gcp_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSQLInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSQLInstanceRequest) ProtoMessage() {}

func (x *CreateSQLInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSQLInstanceRequest.ProtoReflect.Descriptor instead.
func (*CreateSQLInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{32}
}

func (x *CreateSQLInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateSQLInstanceRequest) GetSpec() *gcp.SQLInstanceSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *CreateSQLInstanceRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
type CreateSQLInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSQLInstanceResponse) Reset() {
	*x = CreateSQLInstanceResponse{}
	mi := &file_services_gcp_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSQLInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSQLInstanceResponse) ProtoMessage() {}

func (x *CreateSQLInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSQLInstanceResponse.ProtoReflect.Descriptor instead.
func (*CreateSQLInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{33}
}

func (x *CreateSQLInstanceResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteSQLInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSQLInstanceRequest) Reset() {
	*x = DeleteSQLInstanceRequest{}
	mi := &file_services_gcp_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSQLInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSQLInstanceRequest) ProtoMessage() {}

func (x *DeleteSQLInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSQLInstanceRequest.ProtoReflect.Descriptor instead.
func (*DeleteSQLInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{34}
}

func (x *DeleteSQLInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, NOT_FOUND.
type DeleteSQLInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSQLInstanceResponse) Reset() {
	*x = DeleteSQLInstanceResponse{}
	mi := &file_services_gcp_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSQLInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSQLInstanceResponse) ProtoMessage() {}

func (x *DeleteSQLInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSQLInstanceResponse.ProtoReflect.Descriptor instead.
func (*DeleteSQLInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{35}
}

func (x *DeleteSQLInstanceResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StartSQLInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartSQLInstanceRequest) Reset() {
	*x = StartSQLInstanceRequest{}
	mi := &file_services_gcp_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartSQLInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartSQLInstanceRequest) ProtoMessage() {}

func (x *StartSQLInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartSQLInstanceRequest.ProtoReflect.Descriptor instead.
func (*StartSQLInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{36}
}

func (x *StartSQLInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
type StartSQLInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartSQLInstanceResponse) Reset() {
	*x = StartSQLInstanceResponse{}
	mi := &file_services_gcp_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartSQLInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartSQLInstanceResponse) ProtoMessage() {}

func (x *StartSQLInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartSQLInstanceResponse.ProtoReflect.Descriptor instead.
func (*StartSQLInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{37}
}

func (x *StartSQLInstanceResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StopSQLInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopSQLInstanceRequest) Reset() {
	*x = StopSQLInstanceRequest{}
	mi := &file_services_gcp_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopSQLInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopSQLInstanceRequest) ProtoMessage() {}

func (x *StopSQLInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopSQLInstanceRequest.ProtoReflect.Descriptor instead.
func (*StopSQLInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{38}
}

func (x *StopSQLInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
type StopSQLInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopSQLInstanceResponse) Reset() {
	*x = StopSQLInstanceResponse{}
	mi := &file_services_gcp_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopSQLInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopSQLInstanceResponse) ProtoMessage() {}

func (x *StopSQLInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopSQLInstanceResponse.ProtoReflect.Descriptor instead.
func (*StopSQLInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{39}
}

func (x *StopSQLInstanceResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSQLInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSQLInstanceRequest) Reset() {
	*x = GetSQLInstanceRequest{}
	mi := &file_services_gcp_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSQLInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSQLInstanceRequest) ProtoMessage() {}

func (x *GetSQLInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSQLInstanceRequest.ProtoReflect.Descriptor instead.
func (*GetSQLInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{40}
}

func (x *GetSQLInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetSQLInstanceResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Status           *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PrivateIpAddress *string                `protobuf:"bytes,2,opt,name=private_ip_address,json=privateIpAddress,proto3,oneof" json:"private_ip_address,omitempty"`
	InstanceStatus   gcp.SQLInstanceStatus  `protobuf:"varint,3,opt,name=instance_status,json=instanceStatus,proto3,enum=common.gcp.SQLInstanceStatus" json:"instance_status,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetSQLInstanceResponse) Reset() {
	*x = GetSQLInstanceResponse{}
	mi := &file_services_gcp_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSQLInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSQLInstanceResponse) ProtoMessage() {}

func (x *GetSQLInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_gcp_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSQLInstanceResponse.ProtoReflect.Descriptor instead.
func (*GetSQLInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_gcp_proto_rawDescGZIP(), []int{41}
}

func (x *GetSQLInstanceResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSQLInstanceResponse) GetPrivateIpAddress() string {
	if x != nil && x.PrivateIpAddress != nil {
		return *x.PrivateIpAddress
	}
	return ""
}

func (x *GetSQLInstanceResponse) GetInstanceStatus() gcp.SQLInstanceStatus {
	if x != nil {
		return x.InstanceStatus
	}
	return gcp.SQLInstanceStatus(0)
}

var File_services_gcp_proto protoreflect.FileDescriptor

var file_services_gcp_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x67, 0x63, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67,
	0x63, 0x70, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x63, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd6, 0x01, 0x0a,
	0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x37,
	0x0a, 0x18, 0x49, 0x41, 0x4d, 0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3c, 0x0a, 0x0c, 0x72, 0x6f, 0x6c, 0x65, 0x5f,
	0x62, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x52, 0x6f, 0x6c,
	0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x0b, 0x72, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x22, 0x5e, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49,
	0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5c, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x52, 0x0a, 0x1f, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5f, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x5e, 0x0a, 0x22, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdc, 0x01, 0x0a, 0x20, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42,
	0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x18, 0x49, 0x41, 0x4d,
	0x5f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x15, 0x49, 0x41, 0x4d,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x43, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x22, 0x5d, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5b, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5e, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x5d, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5c, 0x0a, 0x1e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49,
	0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x5b, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x59, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x74, 0x0a, 0x1c, 0x47,
	0x65, 0x74, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x23, 0x0a, 0x0d,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x65, 0x6d, 0x61, 0x69, 0x6c, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x45, 0x6d, 0x61, 0x69,
	0x6c, 0x22, 0x5c, 0x0a, 0x1e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22,
	0x5b, 0x0a, 0x1f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x6b, 0x0a, 0x0b,
	0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x4b, 0x0a, 0x11, 0x67,
	0x63, 0x73, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x43, 0x53, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0f, 0x67, 0x63, 0x73, 0x41, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65,
	0x73, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0x7e, 0x0a, 0x0f, 0x47, 0x43, 0x53,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06,
	0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x03, 0x64, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x64, 0x69, 0x72, 0x12, 0x12, 0x0a, 0x04, 0x64, 0x69,
	0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x64, 0x69, 0x72, 0x73, 0x12, 0x29,
	0x0a, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x15, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x43, 0x53, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x04, 0x72, 0x6f, 0x6c, 0x65, 0x22, 0x71, 0x0a, 0x16, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x1b, 0x0a, 0x09, 0x69, 0x70, 0x5f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x69, 0x70, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x22, 0x53, 0x0a, 0x17,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x51, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x87, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x49, 0x50, 0x41, 0x64,
	0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f,
	0x0a, 0x0b, 0x69, 0x70, 0x5f, 0x73, 0x65, 0x6c, 0x66, 0x6c, 0x69, 0x6e, 0x6b, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x70, 0x53, 0x65, 0x6c, 0x66, 0x6c, 0x69, 0x6e, 0x6b, 0x12,
	0x1d, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x54,
	0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xdc, 0x02, 0x0a, 0x2a, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x6c, 0x0a, 0x0c, 0x65, 0x78,
	0x74, 0x72, 0x61, 0x5f, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x49, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x78, 0x74, 0x72, 0x61,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x65, 0x78, 0x74,
	0x72, 0x61, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x3e, 0x0a, 0x10, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x67, 0x0a, 0x2b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x65, 0x0a, 0x27, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0xb6, 0x01, 0x0a, 0x28, 0x47, 0x65, 0x74,
	0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x36, 0x0a, 0x0a, 0x70, 0x73, 0x63, 0x5f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x17, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x50, 0x73, 0x63, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x09, 0x70, 0x73, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x21,
	0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x09, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x22, 0x68, 0x0a, 0x2a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x67, 0x0a, 0x2b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x8e, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x2f, 0x0a,
	0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x12, 0x4a,
	0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x32,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61,
	0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x55, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x56, 0x0a, 0x18,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x55, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x51,
	0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a, 0x17, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x22, 0x52, 0x0a, 0x18, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x51, 0x4c, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x54, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x51,
	0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x17,
	0x53, 0x74, 0x6f, 0x70, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x53, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0xdb, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x51, 0x4c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x31, 0x0a, 0x12, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x10,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x88, 0x01, 0x01, 0x12, 0x46, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x42, 0x15, 0x0a, 0x13, 0x5f,
	0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x2a, 0x3b, 0x0a, 0x07, 0x47, 0x43, 0x53, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x4f, 0x42,
	0x4a, 0x45, 0x43, 0x54, 0x5f, 0x41, 0x44, 0x4d, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d,
	0x4f, 0x42, 0x4a, 0x45, 0x43, 0x54, 0x5f, 0x56, 0x49, 0x45, 0x57, 0x45, 0x52, 0x10, 0x02, 0x2a,
	0x58, 0x0a, 0x09, 0x50, 0x73, 0x63, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x12,
	0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49,
	0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x01, 0x12, 0x0c, 0x0a, 0x08, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x45, 0x44, 0x10, 0x02, 0x12,
	0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x03, 0x12, 0x0a, 0x0a,
	0x06, 0x43, 0x4c, 0x4f, 0x53, 0x45, 0x44, 0x10, 0x04, 0x32, 0xd2, 0x15, 0x0a, 0x12, 0x47, 0x63,
	0x70, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x72,
	0x12, 0x92, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x61, 0x73, 0x6b, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f,
	0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74,
	0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c,
	0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x6f, 0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63,
	0x70, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x6f,
	0x6c, 0x65, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b,
	0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b,
	0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b,
	0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b,
	0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67, 0x12, 0x2b, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74,
	0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4b, 0x53, 0x41, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e, 0x67,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67,
	0x63, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x78, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41,
	0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12,
	0x2c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2d, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f,
	0x0a, 0x14, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70,
	0x2e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x60, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63,
	0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50,
	0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x60, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64,
	0x72, 0x65, 0x73, 0x73, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67,
	0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65,
	0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a,
	0x23, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x12, 0x38, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x67, 0x63, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x23,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x12, 0x38, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67,
	0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x93, 0x01, 0x0a, 0x20, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47,
	0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x66, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x26, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x67, 0x63, 0x70, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x51, 0x4c, 0x49, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x63, 0x0a, 0x10, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x67, 0x63, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74,
	0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x51, 0x4c,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x53, 0x51, 0x4c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x74,
	0x6f, 0x70, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x53, 0x51,
	0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x51, 0x4c, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x47, 0x65,
	0x74, 0x53, 0x51, 0x4c, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e,
	0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69,
	0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x39,
	0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75,
	0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x67, 0x63, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
})

var (
	file_services_gcp_proto_rawDescOnce sync.Once
	file_services_gcp_proto_rawDescData []byte
)

func file_services_gcp_proto_rawDescGZIP() []byte {
	file_services_gcp_proto_rawDescOnce.Do(func() {
		file_services_gcp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_gcp_proto_rawDesc), len(file_services_gcp_proto_rawDesc)))
	})
	return file_services_gcp_proto_rawDescData
}

var file_services_gcp_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_services_gcp_proto_msgTypes = make([]protoimpl.MessageInfo, 44)
var file_services_gcp_proto_goTypes = []any{
	(GCSRole)(0),   // 0: services.gcp.GCSRole
	(PscStatus)(0), // 1: services.gcp.PscStatus
	(*CreateIAMPolicyRoleBindingRequest)(nil),            // 2: services.gcp.CreateIAMPolicyRoleBindingRequest
	(*CreateIAMPolicyRoleBindingResponse)(nil),           // 3: services.gcp.CreateIAMPolicyRoleBindingResponse
	(*GetIAMPolicyRoleBindingRequest)(nil),               // 4: services.gcp.GetIAMPolicyRoleBindingRequest
	(*GetIAMPolicyRoleBindingResponse)(nil),              // 5: services.gcp.GetIAMPolicyRoleBindingResponse
	(*DeleteIAMPolicyRoleBindingRequest)(nil),            // 6: services.gcp.DeleteIAMPolicyRoleBindingRequest
	(*DeleteIAMPolicyRoleBindingResponse)(nil),           // 7: services.gcp.DeleteIAMPolicyRoleBindingResponse
	(*CreateIAMPolicyKSABindingRequest)(nil),             // 8: services.gcp.CreateIAMPolicyKSABindingRequest
	(*CreateIAMPolicyKSABindingResponse)(nil),            // 9: services.gcp.CreateIAMPolicyKSABindingResponse
	(*GetIAMPolicyKSABindingRequest)(nil),                // 10: services.gcp.GetIAMPolicyKSABindingRequest
	(*GetIAMPolicyKSABindingResponse)(nil),               // 11: services.gcp.GetIAMPolicyKSABindingResponse
	(*DeleteIAMPolicyKSABindingRequest)(nil),             // 12: services.gcp.DeleteIAMPolicyKSABindingRequest
	(*DeleteIAMPolicyKSABindingResponse)(nil),            // 13: services.gcp.DeleteIAMPolicyKSABindingResponse
	(*CreateIAMServiceAccountRequest)(nil),               // 14: services.gcp.CreateIAMServiceAccountRequest
	(*CreateIAMServiceAccountResponse)(nil),              // 15: services.gcp.CreateIAMServiceAccountResponse
	(*GetIAMServiceAccountRequest)(nil),                  // 16: services.gcp.GetIAMServiceAccountRequest
	(*GetIAMServiceAccountResponse)(nil),                 // 17: services.gcp.GetIAMServiceAccountResponse
	(*DeleteIAMServiceAccountRequest)(nil),               // 18: services.gcp.DeleteIAMServiceAccountRequest
	(*DeleteIAMServiceAccountResponse)(nil),              // 19: services.gcp.DeleteIAMServiceAccountResponse
	(*RoleBinding)(nil),                                  // 20: services.gcp.RoleBinding
	(*GCSAccessOption)(nil),                              // 21: services.gcp.GCSAccessOption
	(*CreateIPAddressRequest)(nil),                       // 22: services.gcp.CreateIPAddressRequest
	(*CreateIPAddressResponse)(nil),                      // 23: services.gcp.CreateIPAddressResponse
	(*GetIPAddressRequest)(nil),                          // 24: services.gcp.GetIPAddressRequest
	(*GetIPAddressResponse)(nil),                         // 25: services.gcp.GetIPAddressResponse
	(*DeleteIPAddressRequest)(nil),                       // 26: services.gcp.DeleteIPAddressRequest
	(*DeleteIPAddressResponse)(nil),                      // 27: services.gcp.DeleteIPAddressResponse
	(*CreatePrivateServiceConnectEndpointRequest)(nil),   // 28: services.gcp.CreatePrivateServiceConnectEndpointRequest
	(*CreatePrivateServiceConnectEndpointResponse)(nil),  // 29: services.gcp.CreatePrivateServiceConnectEndpointResponse
	(*GetPrivateServiceConnectEndpointRequest)(nil),      // 30: services.gcp.GetPrivateServiceConnectEndpointRequest
	(*GetPrivateServiceConnectEndpointResponse)(nil),     // 31: services.gcp.GetPrivateServiceConnectEndpointResponse
	(*DeletePrivateServiceConnectEndpointRequest)(nil),   // 32: services.gcp.DeletePrivateServiceConnectEndpointRequest
	(*DeletePrivateServiceConnectEndpointResponse)(nil),  // 33: services.gcp.DeletePrivateServiceConnectEndpointResponse
	(*CreateSQLInstanceRequest)(nil),                     // 34: services.gcp.CreateSQLInstanceRequest
	(*CreateSQLInstanceResponse)(nil),                    // 35: services.gcp.CreateSQLInstanceResponse
	(*DeleteSQLInstanceRequest)(nil),                     // 36: services.gcp.DeleteSQLInstanceRequest
	(*DeleteSQLInstanceResponse)(nil),                    // 37: services.gcp.DeleteSQLInstanceResponse
	(*StartSQLInstanceRequest)(nil),                      // 38: services.gcp.StartSQLInstanceRequest
	(*StartSQLInstanceResponse)(nil),                     // 39: services.gcp.StartSQLInstanceResponse
	(*StopSQLInstanceRequest)(nil),                       // 40: services.gcp.StopSQLInstanceRequest
	(*StopSQLInstanceResponse)(nil),                      // 41: services.gcp.StopSQLInstanceResponse
	(*GetSQLInstanceRequest)(nil),                        // 42: services.gcp.GetSQLInstanceRequest
	(*GetSQLInstanceResponse)(nil),                       // 43: services.gcp.GetSQLInstanceResponse
	nil,                                                  // 44: services.gcp.CreatePrivateServiceConnectEndpointRequest.ExtraLabelsEntry
	nil,                                                  // 45: services.gcp.CreateSQLInstanceRequest.LabelsEntry
	(*resource.Meta)(nil),                                // 46: common.resource.Meta
	(*creation.Status)(nil),                              // 47: common.resource.creation.Status
	(*resource.Status)(nil),                              // 48: common.resource.Status
	(*deletion.Status)(nil),                              // 49: common.resource.deletion.Status
	(*k8s.ServiceAccount)(nil),                           // 50: common.k8s.ServiceAccount
	(*gcp.SQLInstanceSpec)(nil),                          // 51: common.gcp.SQLInstanceSpec
	(*update.Status)(nil),                                // 52: common.resource.update.Status
	(gcp.SQLInstanceStatus)(0),                           // 53: common.gcp.SQLInstanceStatus
	(*data.CreateDataDirectoryDeletionTaskRequest)(nil),  // 54: services.data.CreateDataDirectoryDeletionTaskRequest
	(*data.CreateDataDirectoryCloneTaskRequest)(nil),     // 55: services.data.CreateDataDirectoryCloneTaskRequest
	(*data.GetManifestRequest)(nil),                      // 56: services.data.GetManifestRequest
	(*data.CreateDataDirectoryDeletionTaskResponse)(nil), // 57: services.data.CreateDataDirectoryDeletionTaskResponse
	(*data.CreateDataDirectoryCloneTaskResponse)(nil),    // 58: services.data.CreateDataDirectoryCloneTaskResponse
	(*data.GetManifestResponse)(nil),                     // 59: services.data.GetManifestResponse
}
var file_services_gcp_proto_depIdxs = []int32{
	46, // 0: services.gcp.CreateIAMPolicyRoleBindingRequest.resource_meta:type_name -> common.resource.Meta
	20, // 1: services.gcp.CreateIAMPolicyRoleBindingRequest.role_binding:type_name -> services.gcp.RoleBinding
	47, // 2: services.gcp.CreateIAMPolicyRoleBindingResponse.status:type_name -> common.resource.creation.Status
	46, // 3: services.gcp.GetIAMPolicyRoleBindingRequest.resource_meta:type_name -> common.resource.Meta
	48, // 4: services.gcp.GetIAMPolicyRoleBindingResponse.status:type_name -> common.resource.Status
	46, // 5: services.gcp.DeleteIAMPolicyRoleBindingRequest.resource_meta:type_name -> common.resource.Meta
	49, // 6: services.gcp.DeleteIAMPolicyRoleBindingResponse.status:type_name -> common.resource.deletion.Status
	46, // 7: services.gcp.CreateIAMPolicyKSABindingRequest.resource_meta:type_name -> common.resource.Meta
	50, // 8: services.gcp.CreateIAMPolicyKSABindingRequest.service_account:type_name -> common.k8s.ServiceAccount
	47, // 9: services.gcp.CreateIAMPolicyKSABindingResponse.status:type_name -> common.resource.creation.Status
	46, // 10: services.gcp.GetIAMPolicyKSABindingRequest.resource_meta:type_name -> common.resource.Meta
	48, // 11: services.gcp.GetIAMPolicyKSABindingResponse.status:type_name -> common.resource.Status
	46, // 12: services.gcp.DeleteIAMPolicyKSABindingRequest.resource_meta:type_name -> common.resource.Meta
	49, // 13: services.gcp.DeleteIAMPolicyKSABindingResponse.status:type_name -> common.resource.deletion.Status
	46, // 14: services.gcp.CreateIAMServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	47, // 15: services.gcp.CreateIAMServiceAccountResponse.status:type_name -> common.resource.creation.Status
	46, // 16: services.gcp.GetIAMServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	48, // 17: services.gcp.GetIAMServiceAccountResponse.status:type_name -> common.resource.Status
	46, // 18: services.gcp.DeleteIAMServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	49, // 19: services.gcp.DeleteIAMServiceAccountResponse.status:type_name -> common.resource.deletion.Status
	21, // 20: services.gcp.RoleBinding.gcs_access_option:type_name -> services.gcp.GCSAccessOption
	0,  // 21: services.gcp.GCSAccessOption.role:type_name -> services.gcp.GCSRole
	46, // 22: services.gcp.CreateIPAddressRequest.resource_meta:type_name -> common.resource.Meta
	47, // 23: services.gcp.CreateIPAddressResponse.status:type_name -> common.resource.creation.Status
	46, // 24: services.gcp.GetIPAddressRequest.resource_meta:type_name -> common.resource.Meta
	48, // 25: services.gcp.GetIPAddressResponse.status:type_name -> common.resource.Status
	46, // 26: services.gcp.DeleteIPAddressRequest.resource_meta:type_name -> common.resource.Meta
	49, // 27: services.gcp.DeleteIPAddressResponse.status:type_name -> common.resource.deletion.Status
	46, // 28: services.gcp.CreatePrivateServiceConnectEndpointRequest.resource_meta:type_name -> common.resource.Meta
	44, // 29: services.gcp.CreatePrivateServiceConnectEndpointRequest.extra_labels:type_name -> services.gcp.CreatePrivateServiceConnectEndpointRequest.ExtraLabelsEntry
	47, // 30: services.gcp.CreatePrivateServiceConnectEndpointResponse.status:type_name -> common.resource.creation.Status
	46, // 31: services.gcp.GetPrivateServiceConnectEndpointRequest.resource_meta:type_name -> common.resource.Meta
	48, // 32: services.gcp.GetPrivateServiceConnectEndpointResponse.status:type_name -> common.resource.Status
	1,  // 33: services.gcp.GetPrivateServiceConnectEndpointResponse.psc_status:type_name -> services.gcp.PscStatus
	46, // 34: services.gcp.DeletePrivateServiceConnectEndpointRequest.resource_meta:type_name -> common.resource.Meta
	49, // 35: services.gcp.DeletePrivateServiceConnectEndpointResponse.status:type_name -> common.resource.deletion.Status
	46, // 36: services.gcp.CreateSQLInstanceRequest.resource_meta:type_name -> common.resource.Meta
	51, // 37: services.gcp.CreateSQLInstanceRequest.spec:type_name -> common.gcp.SQLInstanceSpec
	45, // 38: services.gcp.CreateSQLInstanceRequest.labels:type_name -> services.gcp.CreateSQLInstanceRequest.LabelsEntry
	47, // 39: services.gcp.CreateSQLInstanceResponse.status:type_name -> common.resource.creation.Status
	46, // 40: services.gcp.DeleteSQLInstanceRequest.resource_meta:type_name -> common.resource.Meta
	49, // 41: services.gcp.DeleteSQLInstanceResponse.status:type_name -> common.resource.deletion.Status
	46, // 42: services.gcp.StartSQLInstanceRequest.resource_meta:type_name -> common.resource.Meta
	52, // 43: services.gcp.StartSQLInstanceResponse.status:type_name -> common.resource.update.Status
	46, // 44: services.gcp.StopSQLInstanceRequest.resource_meta:type_name -> common.resource.Meta
	52, // 45: services.gcp.StopSQLInstanceResponse.status:type_name -> common.resource.update.Status
	46, // 46: services.gcp.GetSQLInstanceRequest.resource_meta:type_name -> common.resource.Meta
	48, // 47: services.gcp.GetSQLInstanceResponse.status:type_name -> common.resource.Status
	53, // 48: services.gcp.GetSQLInstanceResponse.instance_status:type_name -> common.gcp.SQLInstanceStatus
	54, // 49: services.gcp.GcpResourceManager.CreateDataDirectoryDeletionTask:input_type -> services.data.CreateDataDirectoryDeletionTaskRequest
	55, // 50: services.gcp.GcpResourceManager.CreateDataDirectoryCloneTask:input_type -> services.data.CreateDataDirectoryCloneTaskRequest
	2,  // 51: services.gcp.GcpResourceManager.CreateIAMPolicyRoleBinding:input_type -> services.gcp.CreateIAMPolicyRoleBindingRequest
	6,  // 52: services.gcp.GcpResourceManager.DeleteIAMPolicyRoleBinding:input_type -> services.gcp.DeleteIAMPolicyRoleBindingRequest
	4,  // 53: services.gcp.GcpResourceManager.GetIAMPolicyRoleBinding:input_type -> services.gcp.GetIAMPolicyRoleBindingRequest
	8,  // 54: services.gcp.GcpResourceManager.CreateIAMPolicyKSABinding:input_type -> services.gcp.CreateIAMPolicyKSABindingRequest
	12, // 55: services.gcp.GcpResourceManager.DeleteIAMPolicyKSABinding:input_type -> services.gcp.DeleteIAMPolicyKSABindingRequest
	10, // 56: services.gcp.GcpResourceManager.GetIAMPolicyKSABinding:input_type -> services.gcp.GetIAMPolicyKSABindingRequest
	14, // 57: services.gcp.GcpResourceManager.CreateIAMServiceAccount:input_type -> services.gcp.CreateIAMServiceAccountRequest
	18, // 58: services.gcp.GcpResourceManager.DeleteIAMServiceAccount:input_type -> services.gcp.DeleteIAMServiceAccountRequest
	16, // 59: services.gcp.GcpResourceManager.GetIAMServiceAccount:input_type -> services.gcp.GetIAMServiceAccountRequest
	22, // 60: services.gcp.GcpResourceManager.CreateIPAddress:input_type -> services.gcp.CreateIPAddressRequest
	26, // 61: services.gcp.GcpResourceManager.DeleteIPAddress:input_type -> services.gcp.DeleteIPAddressRequest
	24, // 62: services.gcp.GcpResourceManager.GetIPAddress:input_type -> services.gcp.GetIPAddressRequest
	28, // 63: services.gcp.GcpResourceManager.CreatePrivateServiceConnectEndpoint:input_type -> services.gcp.CreatePrivateServiceConnectEndpointRequest
	32, // 64: services.gcp.GcpResourceManager.DeletePrivateServiceConnectEndpoint:input_type -> services.gcp.DeletePrivateServiceConnectEndpointRequest
	30, // 65: services.gcp.GcpResourceManager.GetPrivateServiceConnectEndpoint:input_type -> services.gcp.GetPrivateServiceConnectEndpointRequest
	34, // 66: services.gcp.GcpResourceManager.CreateSQLInstance:input_type -> services.gcp.CreateSQLInstanceRequest
	36, // 67: services.gcp.GcpResourceManager.DeleteSQLInstance:input_type -> services.gcp.DeleteSQLInstanceRequest
	38, // 68: services.gcp.GcpResourceManager.StartSQLInstance:input_type -> services.gcp.StartSQLInstanceRequest
	40, // 69: services.gcp.GcpResourceManager.StopSQLInstance:input_type -> services.gcp.StopSQLInstanceRequest
	42, // 70: services.gcp.GcpResourceManager.GetSQLInstance:input_type -> services.gcp.GetSQLInstanceRequest
	56, // 71: services.gcp.GcpResourceManager.GetManifest:input_type -> services.data.GetManifestRequest
	57, // 72: services.gcp.GcpResourceManager.CreateDataDirectoryDeletionTask:output_type -> services.data.CreateDataDirectoryDeletionTaskResponse
	58, // 73: services.gcp.GcpResourceManager.CreateDataDirectoryCloneTask:output_type -> services.data.CreateDataDirectoryCloneTaskResponse
	3,  // 74: services.gcp.GcpResourceManager.CreateIAMPolicyRoleBinding:output_type -> services.gcp.CreateIAMPolicyRoleBindingResponse
	7,  // 75: services.gcp.GcpResourceManager.DeleteIAMPolicyRoleBinding:output_type -> services.gcp.DeleteIAMPolicyRoleBindingResponse
	5,  // 76: services.gcp.GcpResourceManager.GetIAMPolicyRoleBinding:output_type -> services.gcp.GetIAMPolicyRoleBindingResponse
	9,  // 77: services.gcp.GcpResourceManager.CreateIAMPolicyKSABinding:output_type -> services.gcp.CreateIAMPolicyKSABindingResponse
	13, // 78: services.gcp.GcpResourceManager.DeleteIAMPolicyKSABinding:output_type -> services.gcp.DeleteIAMPolicyKSABindingResponse
	11, // 79: services.gcp.GcpResourceManager.GetIAMPolicyKSABinding:output_type -> services.gcp.GetIAMPolicyKSABindingResponse
	15, // 80: services.gcp.GcpResourceManager.CreateIAMServiceAccount:output_type -> services.gcp.CreateIAMServiceAccountResponse
	19, // 81: services.gcp.GcpResourceManager.DeleteIAMServiceAccount:output_type -> services.gcp.DeleteIAMServiceAccountResponse
	17, // 82: services.gcp.GcpResourceManager.GetIAMServiceAccount:output_type -> services.gcp.GetIAMServiceAccountResponse
	23, // 83: services.gcp.GcpResourceManager.CreateIPAddress:output_type -> services.gcp.CreateIPAddressResponse
	27, // 84: services.gcp.GcpResourceManager.DeleteIPAddress:output_type -> services.gcp.DeleteIPAddressResponse
	25, // 85: services.gcp.GcpResourceManager.GetIPAddress:output_type -> services.gcp.GetIPAddressResponse
	29, // 86: services.gcp.GcpResourceManager.CreatePrivateServiceConnectEndpoint:output_type -> services.gcp.CreatePrivateServiceConnectEndpointResponse
	33, // 87: services.gcp.GcpResourceManager.DeletePrivateServiceConnectEndpoint:output_type -> services.gcp.DeletePrivateServiceConnectEndpointResponse
	31, // 88: services.gcp.GcpResourceManager.GetPrivateServiceConnectEndpoint:output_type -> services.gcp.GetPrivateServiceConnectEndpointResponse
	35, // 89: services.gcp.GcpResourceManager.CreateSQLInstance:output_type -> services.gcp.CreateSQLInstanceResponse
	37, // 90: services.gcp.GcpResourceManager.DeleteSQLInstance:output_type -> services.gcp.DeleteSQLInstanceResponse
	39, // 91: services.gcp.GcpResourceManager.StartSQLInstance:output_type -> services.gcp.StartSQLInstanceResponse
	41, // 92: services.gcp.GcpResourceManager.StopSQLInstance:output_type -> services.gcp.StopSQLInstanceResponse
	43, // 93: services.gcp.GcpResourceManager.GetSQLInstance:output_type -> services.gcp.GetSQLInstanceResponse
	59, // 94: services.gcp.GcpResourceManager.GetManifest:output_type -> services.data.GetManifestResponse
	72, // [72:95] is the sub-list for method output_type
	49, // [49:72] is the sub-list for method input_type
	49, // [49:49] is the sub-list for extension type_name
	49, // [49:49] is the sub-list for extension extendee
	0,  // [0:49] is the sub-list for field type_name
}

func init() { file_services_gcp_proto_init() }
func file_services_gcp_proto_init() {
	if File_services_gcp_proto != nil {
		return
	}
	file_services_gcp_proto_msgTypes[18].OneofWrappers = []any{
		(*RoleBinding_GcsAccessOption)(nil),
	}
	file_services_gcp_proto_msgTypes[41].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_gcp_proto_rawDesc), len(file_services_gcp_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   44,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_gcp_proto_goTypes,
		DependencyIndexes: file_services_gcp_proto_depIdxs,
		EnumInfos:         file_services_gcp_proto_enumTypes,
		MessageInfos:      file_services_gcp_proto_msgTypes,
	}.Build()
	File_services_gcp_proto = out.File
	file_services_gcp_proto_goTypes = nil
	file_services_gcp_proto_depIdxs = nil
}
