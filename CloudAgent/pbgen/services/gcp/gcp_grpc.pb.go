// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/gcp.proto

package gcp

import (
	context "context"
	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	GcpResourceManager_CreateDataDirectoryDeletionTask_FullMethodName     = "/services.gcp.GcpResourceManager/CreateDataDirectoryDeletionTask"
	GcpResourceManager_CreateDataDirectoryCloneTask_FullMethodName        = "/services.gcp.GcpResourceManager/CreateDataDirectoryCloneTask"
	GcpResourceManager_CreateIAMPolicyRoleBinding_FullMethodName          = "/services.gcp.GcpResourceManager/CreateIAMPolicyRoleBinding"
	GcpResourceManager_DeleteIAMPolicyRoleBinding_FullMethodName          = "/services.gcp.GcpResourceManager/DeleteIAMPolicyRoleBinding"
	GcpResourceManager_GetIAMPolicyRoleBinding_FullMethodName             = "/services.gcp.GcpResourceManager/GetIAMPolicyRoleBinding"
	GcpResourceManager_CreateIAMPolicyKSABinding_FullMethodName           = "/services.gcp.GcpResourceManager/CreateIAMPolicyKSABinding"
	GcpResourceManager_DeleteIAMPolicyKSABinding_FullMethodName           = "/services.gcp.GcpResourceManager/DeleteIAMPolicyKSABinding"
	GcpResourceManager_GetIAMPolicyKSABinding_FullMethodName              = "/services.gcp.GcpResourceManager/GetIAMPolicyKSABinding"
	GcpResourceManager_CreateIAMServiceAccount_FullMethodName             = "/services.gcp.GcpResourceManager/CreateIAMServiceAccount"
	GcpResourceManager_DeleteIAMServiceAccount_FullMethodName             = "/services.gcp.GcpResourceManager/DeleteIAMServiceAccount"
	GcpResourceManager_GetIAMServiceAccount_FullMethodName                = "/services.gcp.GcpResourceManager/GetIAMServiceAccount"
	GcpResourceManager_CreateIPAddress_FullMethodName                     = "/services.gcp.GcpResourceManager/CreateIPAddress"
	GcpResourceManager_DeleteIPAddress_FullMethodName                     = "/services.gcp.GcpResourceManager/DeleteIPAddress"
	GcpResourceManager_GetIPAddress_FullMethodName                        = "/services.gcp.GcpResourceManager/GetIPAddress"
	GcpResourceManager_CreatePrivateServiceConnectEndpoint_FullMethodName = "/services.gcp.GcpResourceManager/CreatePrivateServiceConnectEndpoint"
	GcpResourceManager_DeletePrivateServiceConnectEndpoint_FullMethodName = "/services.gcp.GcpResourceManager/DeletePrivateServiceConnectEndpoint"
	GcpResourceManager_GetPrivateServiceConnectEndpoint_FullMethodName    = "/services.gcp.GcpResourceManager/GetPrivateServiceConnectEndpoint"
	GcpResourceManager_CreateSQLInstance_FullMethodName                   = "/services.gcp.GcpResourceManager/CreateSQLInstance"
	GcpResourceManager_DeleteSQLInstance_FullMethodName                   = "/services.gcp.GcpResourceManager/DeleteSQLInstance"
	GcpResourceManager_StartSQLInstance_FullMethodName                    = "/services.gcp.GcpResourceManager/StartSQLInstance"
	GcpResourceManager_StopSQLInstance_FullMethodName                     = "/services.gcp.GcpResourceManager/StopSQLInstance"
	GcpResourceManager_GetSQLInstance_FullMethodName                      = "/services.gcp.GcpResourceManager/GetSQLInstance"
	GcpResourceManager_GetManifest_FullMethodName                         = "/services.gcp.GcpResourceManager/GetManifest"
)

// GcpResourceManagerClient is the client API for GcpResourceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type GcpResourceManagerClient interface {
	// CreateDataDirectoryDeletionTask creates a task for GCP folder deletion.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error)
	CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMPolicyRoleBinding(ctx context.Context, in *CreateIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*CreateIAMPolicyRoleBindingResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMPolicyRoleBinding(ctx context.Context, in *DeleteIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyRoleBindingResponse, error)
	GetIAMPolicyRoleBinding(ctx context.Context, in *GetIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*GetIAMPolicyRoleBindingResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMPolicyKSABinding(ctx context.Context, in *CreateIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*CreateIAMPolicyKSABindingResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMPolicyKSABinding(ctx context.Context, in *DeleteIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyKSABindingResponse, error)
	GetIAMPolicyKSABinding(ctx context.Context, in *GetIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*GetIAMPolicyKSABindingResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMServiceAccount(ctx context.Context, in *CreateIAMServiceAccountRequest, opts ...grpc.CallOption) (*CreateIAMServiceAccountResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMServiceAccount(ctx context.Context, in *DeleteIAMServiceAccountRequest, opts ...grpc.CallOption) (*DeleteIAMServiceAccountResponse, error)
	GetIAMServiceAccount(ctx context.Context, in *GetIAMServiceAccountRequest, opts ...grpc.CallOption) (*GetIAMServiceAccountResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIPAddress(ctx context.Context, in *CreateIPAddressRequest, opts ...grpc.CallOption) (*CreateIPAddressResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIPAddress(ctx context.Context, in *DeleteIPAddressRequest, opts ...grpc.CallOption) (*DeleteIPAddressResponse, error)
	GetIPAddress(ctx context.Context, in *GetIPAddressRequest, opts ...grpc.CallOption) (*GetIPAddressResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePrivateServiceConnectEndpoint(ctx context.Context, in *CreatePrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*CreatePrivateServiceConnectEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePrivateServiceConnectEndpoint(ctx context.Context, in *DeletePrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*DeletePrivateServiceConnectEndpointResponse, error)
	GetPrivateServiceConnectEndpoint(ctx context.Context, in *GetPrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*GetPrivateServiceConnectEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	CreateSQLInstance(ctx context.Context, in *CreateSQLInstanceRequest, opts ...grpc.CallOption) (*CreateSQLInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteSQLInstance(ctx context.Context, in *DeleteSQLInstanceRequest, opts ...grpc.CallOption) (*DeleteSQLInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StartSQLInstance(ctx context.Context, in *StartSQLInstanceRequest, opts ...grpc.CallOption) (*StartSQLInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StopSQLInstance(ctx context.Context, in *StopSQLInstanceRequest, opts ...grpc.CallOption) (*StopSQLInstanceResponse, error)
	GetSQLInstance(ctx context.Context, in *GetSQLInstanceRequest, opts ...grpc.CallOption) (*GetSQLInstanceResponse, error)
	GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error)
}

type gcpResourceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewGcpResourceManagerClient(cc grpc.ClientConnInterface) GcpResourceManagerClient {
	return &gcpResourceManagerClient{cc}
}

func (c *gcpResourceManagerClient) CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateDataDirectoryDeletionTaskResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateDataDirectoryDeletionTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateDataDirectoryCloneTaskResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateDataDirectoryCloneTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreateIAMPolicyRoleBinding(ctx context.Context, in *CreateIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*CreateIAMPolicyRoleBindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIAMPolicyRoleBindingResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateIAMPolicyRoleBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) DeleteIAMPolicyRoleBinding(ctx context.Context, in *DeleteIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyRoleBindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIAMPolicyRoleBindingResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_DeleteIAMPolicyRoleBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetIAMPolicyRoleBinding(ctx context.Context, in *GetIAMPolicyRoleBindingRequest, opts ...grpc.CallOption) (*GetIAMPolicyRoleBindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIAMPolicyRoleBindingResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetIAMPolicyRoleBinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreateIAMPolicyKSABinding(ctx context.Context, in *CreateIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*CreateIAMPolicyKSABindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIAMPolicyKSABindingResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateIAMPolicyKSABinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) DeleteIAMPolicyKSABinding(ctx context.Context, in *DeleteIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyKSABindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIAMPolicyKSABindingResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_DeleteIAMPolicyKSABinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetIAMPolicyKSABinding(ctx context.Context, in *GetIAMPolicyKSABindingRequest, opts ...grpc.CallOption) (*GetIAMPolicyKSABindingResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIAMPolicyKSABindingResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetIAMPolicyKSABinding_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreateIAMServiceAccount(ctx context.Context, in *CreateIAMServiceAccountRequest, opts ...grpc.CallOption) (*CreateIAMServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIAMServiceAccountResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateIAMServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) DeleteIAMServiceAccount(ctx context.Context, in *DeleteIAMServiceAccountRequest, opts ...grpc.CallOption) (*DeleteIAMServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIAMServiceAccountResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_DeleteIAMServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetIAMServiceAccount(ctx context.Context, in *GetIAMServiceAccountRequest, opts ...grpc.CallOption) (*GetIAMServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIAMServiceAccountResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetIAMServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreateIPAddress(ctx context.Context, in *CreateIPAddressRequest, opts ...grpc.CallOption) (*CreateIPAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIPAddressResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateIPAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) DeleteIPAddress(ctx context.Context, in *DeleteIPAddressRequest, opts ...grpc.CallOption) (*DeleteIPAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIPAddressResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_DeleteIPAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetIPAddress(ctx context.Context, in *GetIPAddressRequest, opts ...grpc.CallOption) (*GetIPAddressResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIPAddressResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetIPAddress_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreatePrivateServiceConnectEndpoint(ctx context.Context, in *CreatePrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*CreatePrivateServiceConnectEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePrivateServiceConnectEndpointResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreatePrivateServiceConnectEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) DeletePrivateServiceConnectEndpoint(ctx context.Context, in *DeletePrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*DeletePrivateServiceConnectEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePrivateServiceConnectEndpointResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_DeletePrivateServiceConnectEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetPrivateServiceConnectEndpoint(ctx context.Context, in *GetPrivateServiceConnectEndpointRequest, opts ...grpc.CallOption) (*GetPrivateServiceConnectEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPrivateServiceConnectEndpointResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetPrivateServiceConnectEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) CreateSQLInstance(ctx context.Context, in *CreateSQLInstanceRequest, opts ...grpc.CallOption) (*CreateSQLInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSQLInstanceResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_CreateSQLInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) DeleteSQLInstance(ctx context.Context, in *DeleteSQLInstanceRequest, opts ...grpc.CallOption) (*DeleteSQLInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSQLInstanceResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_DeleteSQLInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) StartSQLInstance(ctx context.Context, in *StartSQLInstanceRequest, opts ...grpc.CallOption) (*StartSQLInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartSQLInstanceResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_StartSQLInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) StopSQLInstance(ctx context.Context, in *StopSQLInstanceRequest, opts ...grpc.CallOption) (*StopSQLInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopSQLInstanceResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_StopSQLInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetSQLInstance(ctx context.Context, in *GetSQLInstanceRequest, opts ...grpc.CallOption) (*GetSQLInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSQLInstanceResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetSQLInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *gcpResourceManagerClient) GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.GetManifestResponse)
	err := c.cc.Invoke(ctx, GcpResourceManager_GetManifest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// GcpResourceManagerServer is the server API for GcpResourceManager service.
// All implementations must embed UnimplementedGcpResourceManagerServer
// for forward compatibility.
type GcpResourceManagerServer interface {
	// CreateDataDirectoryDeletionTask creates a task for GCP folder deletion.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	CreateDataDirectoryDeletionTask(context.Context, *data.CreateDataDirectoryDeletionTaskRequest) (*data.CreateDataDirectoryDeletionTaskResponse, error)
	CreateDataDirectoryCloneTask(context.Context, *data.CreateDataDirectoryCloneTaskRequest) (*data.CreateDataDirectoryCloneTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMPolicyRoleBinding(context.Context, *CreateIAMPolicyRoleBindingRequest) (*CreateIAMPolicyRoleBindingResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMPolicyRoleBinding(context.Context, *DeleteIAMPolicyRoleBindingRequest) (*DeleteIAMPolicyRoleBindingResponse, error)
	GetIAMPolicyRoleBinding(context.Context, *GetIAMPolicyRoleBindingRequest) (*GetIAMPolicyRoleBindingResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMPolicyKSABinding(context.Context, *CreateIAMPolicyKSABindingRequest) (*CreateIAMPolicyKSABindingResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMPolicyKSABinding(context.Context, *DeleteIAMPolicyKSABindingRequest) (*DeleteIAMPolicyKSABindingResponse, error)
	GetIAMPolicyKSABinding(context.Context, *GetIAMPolicyKSABindingRequest) (*GetIAMPolicyKSABindingResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMServiceAccount(context.Context, *CreateIAMServiceAccountRequest) (*CreateIAMServiceAccountResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMServiceAccount(context.Context, *DeleteIAMServiceAccountRequest) (*DeleteIAMServiceAccountResponse, error)
	GetIAMServiceAccount(context.Context, *GetIAMServiceAccountRequest) (*GetIAMServiceAccountResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIPAddress(context.Context, *CreateIPAddressRequest) (*CreateIPAddressResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIPAddress(context.Context, *DeleteIPAddressRequest) (*DeleteIPAddressResponse, error)
	GetIPAddress(context.Context, *GetIPAddressRequest) (*GetIPAddressResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePrivateServiceConnectEndpoint(context.Context, *CreatePrivateServiceConnectEndpointRequest) (*CreatePrivateServiceConnectEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePrivateServiceConnectEndpoint(context.Context, *DeletePrivateServiceConnectEndpointRequest) (*DeletePrivateServiceConnectEndpointResponse, error)
	GetPrivateServiceConnectEndpoint(context.Context, *GetPrivateServiceConnectEndpointRequest) (*GetPrivateServiceConnectEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	CreateSQLInstance(context.Context, *CreateSQLInstanceRequest) (*CreateSQLInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteSQLInstance(context.Context, *DeleteSQLInstanceRequest) (*DeleteSQLInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StartSQLInstance(context.Context, *StartSQLInstanceRequest) (*StartSQLInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StopSQLInstance(context.Context, *StopSQLInstanceRequest) (*StopSQLInstanceResponse, error)
	GetSQLInstance(context.Context, *GetSQLInstanceRequest) (*GetSQLInstanceResponse, error)
	GetManifest(context.Context, *data.GetManifestRequest) (*data.GetManifestResponse, error)
	mustEmbedUnimplementedGcpResourceManagerServer()
}

// UnimplementedGcpResourceManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedGcpResourceManagerServer struct{}

func (UnimplementedGcpResourceManagerServer) CreateDataDirectoryDeletionTask(context.Context, *data.CreateDataDirectoryDeletionTaskRequest) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataDirectoryDeletionTask not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreateDataDirectoryCloneTask(context.Context, *data.CreateDataDirectoryCloneTaskRequest) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataDirectoryCloneTask not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreateIAMPolicyRoleBinding(context.Context, *CreateIAMPolicyRoleBindingRequest) (*CreateIAMPolicyRoleBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIAMPolicyRoleBinding not implemented")
}
func (UnimplementedGcpResourceManagerServer) DeleteIAMPolicyRoleBinding(context.Context, *DeleteIAMPolicyRoleBindingRequest) (*DeleteIAMPolicyRoleBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIAMPolicyRoleBinding not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetIAMPolicyRoleBinding(context.Context, *GetIAMPolicyRoleBindingRequest) (*GetIAMPolicyRoleBindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIAMPolicyRoleBinding not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreateIAMPolicyKSABinding(context.Context, *CreateIAMPolicyKSABindingRequest) (*CreateIAMPolicyKSABindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIAMPolicyKSABinding not implemented")
}
func (UnimplementedGcpResourceManagerServer) DeleteIAMPolicyKSABinding(context.Context, *DeleteIAMPolicyKSABindingRequest) (*DeleteIAMPolicyKSABindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIAMPolicyKSABinding not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetIAMPolicyKSABinding(context.Context, *GetIAMPolicyKSABindingRequest) (*GetIAMPolicyKSABindingResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIAMPolicyKSABinding not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreateIAMServiceAccount(context.Context, *CreateIAMServiceAccountRequest) (*CreateIAMServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIAMServiceAccount not implemented")
}
func (UnimplementedGcpResourceManagerServer) DeleteIAMServiceAccount(context.Context, *DeleteIAMServiceAccountRequest) (*DeleteIAMServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIAMServiceAccount not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetIAMServiceAccount(context.Context, *GetIAMServiceAccountRequest) (*GetIAMServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIAMServiceAccount not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreateIPAddress(context.Context, *CreateIPAddressRequest) (*CreateIPAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIPAddress not implemented")
}
func (UnimplementedGcpResourceManagerServer) DeleteIPAddress(context.Context, *DeleteIPAddressRequest) (*DeleteIPAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIPAddress not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetIPAddress(context.Context, *GetIPAddressRequest) (*GetIPAddressResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPAddress not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreatePrivateServiceConnectEndpoint(context.Context, *CreatePrivateServiceConnectEndpointRequest) (*CreatePrivateServiceConnectEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePrivateServiceConnectEndpoint not implemented")
}
func (UnimplementedGcpResourceManagerServer) DeletePrivateServiceConnectEndpoint(context.Context, *DeletePrivateServiceConnectEndpointRequest) (*DeletePrivateServiceConnectEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePrivateServiceConnectEndpoint not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetPrivateServiceConnectEndpoint(context.Context, *GetPrivateServiceConnectEndpointRequest) (*GetPrivateServiceConnectEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPrivateServiceConnectEndpoint not implemented")
}
func (UnimplementedGcpResourceManagerServer) CreateSQLInstance(context.Context, *CreateSQLInstanceRequest) (*CreateSQLInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSQLInstance not implemented")
}
func (UnimplementedGcpResourceManagerServer) DeleteSQLInstance(context.Context, *DeleteSQLInstanceRequest) (*DeleteSQLInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSQLInstance not implemented")
}
func (UnimplementedGcpResourceManagerServer) StartSQLInstance(context.Context, *StartSQLInstanceRequest) (*StartSQLInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartSQLInstance not implemented")
}
func (UnimplementedGcpResourceManagerServer) StopSQLInstance(context.Context, *StopSQLInstanceRequest) (*StopSQLInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopSQLInstance not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetSQLInstance(context.Context, *GetSQLInstanceRequest) (*GetSQLInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSQLInstance not implemented")
}
func (UnimplementedGcpResourceManagerServer) GetManifest(context.Context, *data.GetManifestRequest) (*data.GetManifestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetManifest not implemented")
}
func (UnimplementedGcpResourceManagerServer) mustEmbedUnimplementedGcpResourceManagerServer() {}
func (UnimplementedGcpResourceManagerServer) testEmbeddedByValue()                            {}

// UnsafeGcpResourceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to GcpResourceManagerServer will
// result in compilation errors.
type UnsafeGcpResourceManagerServer interface {
	mustEmbedUnimplementedGcpResourceManagerServer()
}

func RegisterGcpResourceManagerServer(s grpc.ServiceRegistrar, srv GcpResourceManagerServer) {
	// If the following call pancis, it indicates UnimplementedGcpResourceManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&GcpResourceManager_ServiceDesc, srv)
}

func _GcpResourceManager_CreateDataDirectoryDeletionTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateDataDirectoryDeletionTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateDataDirectoryDeletionTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateDataDirectoryDeletionTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateDataDirectoryDeletionTask(ctx, req.(*data.CreateDataDirectoryDeletionTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreateDataDirectoryCloneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateDataDirectoryCloneTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateDataDirectoryCloneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateDataDirectoryCloneTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateDataDirectoryCloneTask(ctx, req.(*data.CreateDataDirectoryCloneTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreateIAMPolicyRoleBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIAMPolicyRoleBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateIAMPolicyRoleBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateIAMPolicyRoleBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateIAMPolicyRoleBinding(ctx, req.(*CreateIAMPolicyRoleBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_DeleteIAMPolicyRoleBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIAMPolicyRoleBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).DeleteIAMPolicyRoleBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_DeleteIAMPolicyRoleBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).DeleteIAMPolicyRoleBinding(ctx, req.(*DeleteIAMPolicyRoleBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetIAMPolicyRoleBinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIAMPolicyRoleBindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetIAMPolicyRoleBinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetIAMPolicyRoleBinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetIAMPolicyRoleBinding(ctx, req.(*GetIAMPolicyRoleBindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreateIAMPolicyKSABinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIAMPolicyKSABindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateIAMPolicyKSABinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateIAMPolicyKSABinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateIAMPolicyKSABinding(ctx, req.(*CreateIAMPolicyKSABindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_DeleteIAMPolicyKSABinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIAMPolicyKSABindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).DeleteIAMPolicyKSABinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_DeleteIAMPolicyKSABinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).DeleteIAMPolicyKSABinding(ctx, req.(*DeleteIAMPolicyKSABindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetIAMPolicyKSABinding_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIAMPolicyKSABindingRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetIAMPolicyKSABinding(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetIAMPolicyKSABinding_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetIAMPolicyKSABinding(ctx, req.(*GetIAMPolicyKSABindingRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreateIAMServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIAMServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateIAMServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateIAMServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateIAMServiceAccount(ctx, req.(*CreateIAMServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_DeleteIAMServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIAMServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).DeleteIAMServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_DeleteIAMServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).DeleteIAMServiceAccount(ctx, req.(*DeleteIAMServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetIAMServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIAMServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetIAMServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetIAMServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetIAMServiceAccount(ctx, req.(*GetIAMServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreateIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateIPAddress(ctx, req.(*CreateIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_DeleteIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).DeleteIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_DeleteIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).DeleteIPAddress(ctx, req.(*DeleteIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetIPAddress_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIPAddressRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetIPAddress(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetIPAddress_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetIPAddress(ctx, req.(*GetIPAddressRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreatePrivateServiceConnectEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePrivateServiceConnectEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreatePrivateServiceConnectEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreatePrivateServiceConnectEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreatePrivateServiceConnectEndpoint(ctx, req.(*CreatePrivateServiceConnectEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_DeletePrivateServiceConnectEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePrivateServiceConnectEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).DeletePrivateServiceConnectEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_DeletePrivateServiceConnectEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).DeletePrivateServiceConnectEndpoint(ctx, req.(*DeletePrivateServiceConnectEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetPrivateServiceConnectEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPrivateServiceConnectEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetPrivateServiceConnectEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetPrivateServiceConnectEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetPrivateServiceConnectEndpoint(ctx, req.(*GetPrivateServiceConnectEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_CreateSQLInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSQLInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).CreateSQLInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_CreateSQLInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).CreateSQLInstance(ctx, req.(*CreateSQLInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_DeleteSQLInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSQLInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).DeleteSQLInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_DeleteSQLInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).DeleteSQLInstance(ctx, req.(*DeleteSQLInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_StartSQLInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartSQLInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).StartSQLInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_StartSQLInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).StartSQLInstance(ctx, req.(*StartSQLInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_StopSQLInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopSQLInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).StopSQLInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_StopSQLInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).StopSQLInstance(ctx, req.(*StopSQLInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetSQLInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSQLInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetSQLInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetSQLInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetSQLInstance(ctx, req.(*GetSQLInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _GcpResourceManager_GetManifest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.GetManifestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(GcpResourceManagerServer).GetManifest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: GcpResourceManager_GetManifest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(GcpResourceManagerServer).GetManifest(ctx, req.(*data.GetManifestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// GcpResourceManager_ServiceDesc is the grpc.ServiceDesc for GcpResourceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var GcpResourceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.gcp.GcpResourceManager",
	HandlerType: (*GcpResourceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDataDirectoryDeletionTask",
			Handler:    _GcpResourceManager_CreateDataDirectoryDeletionTask_Handler,
		},
		{
			MethodName: "CreateDataDirectoryCloneTask",
			Handler:    _GcpResourceManager_CreateDataDirectoryCloneTask_Handler,
		},
		{
			MethodName: "CreateIAMPolicyRoleBinding",
			Handler:    _GcpResourceManager_CreateIAMPolicyRoleBinding_Handler,
		},
		{
			MethodName: "DeleteIAMPolicyRoleBinding",
			Handler:    _GcpResourceManager_DeleteIAMPolicyRoleBinding_Handler,
		},
		{
			MethodName: "GetIAMPolicyRoleBinding",
			Handler:    _GcpResourceManager_GetIAMPolicyRoleBinding_Handler,
		},
		{
			MethodName: "CreateIAMPolicyKSABinding",
			Handler:    _GcpResourceManager_CreateIAMPolicyKSABinding_Handler,
		},
		{
			MethodName: "DeleteIAMPolicyKSABinding",
			Handler:    _GcpResourceManager_DeleteIAMPolicyKSABinding_Handler,
		},
		{
			MethodName: "GetIAMPolicyKSABinding",
			Handler:    _GcpResourceManager_GetIAMPolicyKSABinding_Handler,
		},
		{
			MethodName: "CreateIAMServiceAccount",
			Handler:    _GcpResourceManager_CreateIAMServiceAccount_Handler,
		},
		{
			MethodName: "DeleteIAMServiceAccount",
			Handler:    _GcpResourceManager_DeleteIAMServiceAccount_Handler,
		},
		{
			MethodName: "GetIAMServiceAccount",
			Handler:    _GcpResourceManager_GetIAMServiceAccount_Handler,
		},
		{
			MethodName: "CreateIPAddress",
			Handler:    _GcpResourceManager_CreateIPAddress_Handler,
		},
		{
			MethodName: "DeleteIPAddress",
			Handler:    _GcpResourceManager_DeleteIPAddress_Handler,
		},
		{
			MethodName: "GetIPAddress",
			Handler:    _GcpResourceManager_GetIPAddress_Handler,
		},
		{
			MethodName: "CreatePrivateServiceConnectEndpoint",
			Handler:    _GcpResourceManager_CreatePrivateServiceConnectEndpoint_Handler,
		},
		{
			MethodName: "DeletePrivateServiceConnectEndpoint",
			Handler:    _GcpResourceManager_DeletePrivateServiceConnectEndpoint_Handler,
		},
		{
			MethodName: "GetPrivateServiceConnectEndpoint",
			Handler:    _GcpResourceManager_GetPrivateServiceConnectEndpoint_Handler,
		},
		{
			MethodName: "CreateSQLInstance",
			Handler:    _GcpResourceManager_CreateSQLInstance_Handler,
		},
		{
			MethodName: "DeleteSQLInstance",
			Handler:    _GcpResourceManager_DeleteSQLInstance_Handler,
		},
		{
			MethodName: "StartSQLInstance",
			Handler:    _GcpResourceManager_StartSQLInstance_Handler,
		},
		{
			MethodName: "StopSQLInstance",
			Handler:    _GcpResourceManager_StopSQLInstance_Handler,
		},
		{
			MethodName: "GetSQLInstance",
			Handler:    _GcpResourceManager_GetSQLInstance_Handler,
		},
		{
			MethodName: "GetManifest",
			Handler:    _GcpResourceManager_GetManifest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/gcp.proto",
}
