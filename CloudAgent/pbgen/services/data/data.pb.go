// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/common/data.proto

package data

import (
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Note: Directory deletion tasks are not namespaced, the namespace value will
// be ignored.
type CreateDataDirectoryDeletionTaskRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	DirectoryName string                 `protobuf:"bytes,2,opt,name=directory_name,json=directoryName,proto3" json:"directory_name,omitempty"`
	BucketName    string                 `protobuf:"bytes,3,opt,name=bucket_name,json=bucketName,proto3" json:"bucket_name,omitempty"`
	// Deprecated: Marked as deprecated in services/common/data.proto.
	Region string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	// This is an Azure specific field to specify the storage account the
	// bucket(container) belongs to.
	StorageAccountName string `protobuf:"bytes,5,opt,name=storage_account_name,json=storageAccountName,proto3" json:"storage_account_name,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CreateDataDirectoryDeletionTaskRequest) Reset() {
	*x = CreateDataDirectoryDeletionTaskRequest{}
	mi := &file_services_common_data_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDataDirectoryDeletionTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDataDirectoryDeletionTaskRequest) ProtoMessage() {}

func (x *CreateDataDirectoryDeletionTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDataDirectoryDeletionTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateDataDirectoryDeletionTaskRequest) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDataDirectoryDeletionTaskRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateDataDirectoryDeletionTaskRequest) GetDirectoryName() string {
	if x != nil {
		return x.DirectoryName
	}
	return ""
}

func (x *CreateDataDirectoryDeletionTaskRequest) GetBucketName() string {
	if x != nil {
		return x.BucketName
	}
	return ""
}

// Deprecated: Marked as deprecated in services/common/data.proto.
func (x *CreateDataDirectoryDeletionTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateDataDirectoryDeletionTaskRequest) GetStorageAccountName() string {
	if x != nil {
		return x.StorageAccountName
	}
	return ""
}

type CreateDataDirectoryDeletionTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDataDirectoryDeletionTaskResponse) Reset() {
	*x = CreateDataDirectoryDeletionTaskResponse{}
	mi := &file_services_common_data_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDataDirectoryDeletionTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDataDirectoryDeletionTaskResponse) ProtoMessage() {}

func (x *CreateDataDirectoryDeletionTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDataDirectoryDeletionTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateDataDirectoryDeletionTaskResponse) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{1}
}

func (x *CreateDataDirectoryDeletionTaskResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// Note: Directory clone tasks are not namespaced, the namespace value will
// be ignored.
type CreateDataDirectoryCloneTaskRequest struct {
	state                    protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta             *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SourceDirectoryName      string                 `protobuf:"bytes,2,opt,name=source_directory_name,json=sourceDirectoryName,proto3" json:"source_directory_name,omitempty"`
	SourceBucketName         string                 `protobuf:"bytes,3,opt,name=source_bucket_name,json=sourceBucketName,proto3" json:"source_bucket_name,omitempty"`
	DestinationDirectoryName string                 `protobuf:"bytes,4,opt,name=destination_directory_name,json=destinationDirectoryName,proto3" json:"destination_directory_name,omitempty"`
	DestinationBucketName    string                 `protobuf:"bytes,5,opt,name=destination_bucket_name,json=destinationBucketName,proto3" json:"destination_bucket_name,omitempty"`
	// This is an Azure specific field to specify the storage account the
	// bucket(container) belongs to.
	SourceStorageAccountName      string `protobuf:"bytes,6,opt,name=source_storage_account_name,json=sourceStorageAccountName,proto3" json:"source_storage_account_name,omitempty"`
	DestinationStorageAccountName string `protobuf:"bytes,7,opt,name=destination_storage_account_name,json=destinationStorageAccountName,proto3" json:"destination_storage_account_name,omitempty"`
	// The object cursor to resume the cloning tasks
	Cursor string `protobuf:"bytes,8,opt,name=cursor,proto3" json:"cursor,omitempty"`
	// The number of objects to clone in the task
	CloneSize     int32 `protobuf:"varint,9,opt,name=clone_size,json=cloneSize,proto3" json:"clone_size,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDataDirectoryCloneTaskRequest) Reset() {
	*x = CreateDataDirectoryCloneTaskRequest{}
	mi := &file_services_common_data_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDataDirectoryCloneTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDataDirectoryCloneTaskRequest) ProtoMessage() {}

func (x *CreateDataDirectoryCloneTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDataDirectoryCloneTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateDataDirectoryCloneTaskRequest) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDataDirectoryCloneTaskRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateDataDirectoryCloneTaskRequest) GetSourceDirectoryName() string {
	if x != nil {
		return x.SourceDirectoryName
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetSourceBucketName() string {
	if x != nil {
		return x.SourceBucketName
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetDestinationDirectoryName() string {
	if x != nil {
		return x.DestinationDirectoryName
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetDestinationBucketName() string {
	if x != nil {
		return x.DestinationBucketName
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetSourceStorageAccountName() string {
	if x != nil {
		return x.SourceStorageAccountName
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetDestinationStorageAccountName() string {
	if x != nil {
		return x.DestinationStorageAccountName
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetCursor() string {
	if x != nil {
		return x.Cursor
	}
	return ""
}

func (x *CreateDataDirectoryCloneTaskRequest) GetCloneSize() int32 {
	if x != nil {
		return x.CloneSize
	}
	return 0
}

type CreateDataDirectoryCloneTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDataDirectoryCloneTaskResponse) Reset() {
	*x = CreateDataDirectoryCloneTaskResponse{}
	mi := &file_services_common_data_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDataDirectoryCloneTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDataDirectoryCloneTaskResponse) ProtoMessage() {}

func (x *CreateDataDirectoryCloneTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDataDirectoryCloneTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateDataDirectoryCloneTaskResponse) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{3}
}

func (x *CreateDataDirectoryCloneTaskResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateSimpleDataReplicationTaskRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta    *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SourceBucket    string                 `protobuf:"bytes,2,opt,name=source_bucket,json=sourceBucket,proto3" json:"source_bucket,omitempty"`
	SourceDirectory string                 `protobuf:"bytes,3,opt,name=source_directory,json=sourceDirectory,proto3" json:"source_directory,omitempty"`
	SinkBucket      string                 `protobuf:"bytes,4,opt,name=sink_bucket,json=sinkBucket,proto3" json:"sink_bucket,omitempty"`
	SinkDirectory   string                 `protobuf:"bytes,5,opt,name=sink_directory,json=sinkDirectory,proto3" json:"sink_directory,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *CreateSimpleDataReplicationTaskRequest) Reset() {
	*x = CreateSimpleDataReplicationTaskRequest{}
	mi := &file_services_common_data_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSimpleDataReplicationTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSimpleDataReplicationTaskRequest) ProtoMessage() {}

func (x *CreateSimpleDataReplicationTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSimpleDataReplicationTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateSimpleDataReplicationTaskRequest) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{4}
}

func (x *CreateSimpleDataReplicationTaskRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateSimpleDataReplicationTaskRequest) GetSourceBucket() string {
	if x != nil {
		return x.SourceBucket
	}
	return ""
}

func (x *CreateSimpleDataReplicationTaskRequest) GetSourceDirectory() string {
	if x != nil {
		return x.SourceDirectory
	}
	return ""
}

func (x *CreateSimpleDataReplicationTaskRequest) GetSinkBucket() string {
	if x != nil {
		return x.SinkBucket
	}
	return ""
}

func (x *CreateSimpleDataReplicationTaskRequest) GetSinkDirectory() string {
	if x != nil {
		return x.SinkDirectory
	}
	return ""
}

type CreateSimpleDataReplicationTaskResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSimpleDataReplicationTaskResponse) Reset() {
	*x = CreateSimpleDataReplicationTaskResponse{}
	mi := &file_services_common_data_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSimpleDataReplicationTaskResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSimpleDataReplicationTaskResponse) ProtoMessage() {}

func (x *CreateSimpleDataReplicationTaskResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSimpleDataReplicationTaskResponse.ProtoReflect.Descriptor instead.
func (*CreateSimpleDataReplicationTaskResponse) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{5}
}

func (x *CreateSimpleDataReplicationTaskResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetManifestRequest struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	Bucket    string                 `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	BackupDir string                 `protobuf:"bytes,2,opt,name=backup_dir,json=backupDir,proto3" json:"backup_dir,omitempty"`
	// This is an Azure specific field to specify the storage account the
	// bucket(container) belongs to.
	StorageAccountName string `protobuf:"bytes,3,opt,name=storage_account_name,json=storageAccountName,proto3" json:"storage_account_name,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *GetManifestRequest) Reset() {
	*x = GetManifestRequest{}
	mi := &file_services_common_data_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetManifestRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManifestRequest) ProtoMessage() {}

func (x *GetManifestRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManifestRequest.ProtoReflect.Descriptor instead.
func (*GetManifestRequest) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{6}
}

func (x *GetManifestRequest) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

func (x *GetManifestRequest) GetBackupDir() string {
	if x != nil {
		return x.BackupDir
	}
	return ""
}

func (x *GetManifestRequest) GetStorageAccountName() string {
	if x != nil {
		return x.StorageAccountName
	}
	return ""
}

type GetManifestResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ManifestJson  string                 `protobuf:"bytes,1,opt,name=manifest_json,json=manifestJson,proto3" json:"manifest_json,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetManifestResponse) Reset() {
	*x = GetManifestResponse{}
	mi := &file_services_common_data_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetManifestResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetManifestResponse) ProtoMessage() {}

func (x *GetManifestResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_common_data_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetManifestResponse.ProtoReflect.Descriptor instead.
func (*GetManifestResponse) Descriptor() ([]byte, []int) {
	return file_services_common_data_proto_rawDescGZIP(), []int{7}
}

func (x *GetManifestResponse) GetManifestJson() string {
	if x != nil {
		return x.ManifestJson
	}
	return ""
}

var File_services_common_data_proto protoreflect.FileDescriptor

var file_services_common_data_proto_rawDesc = string([]byte{
	0x0a, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0d, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x1a, 0x15, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xfa, 0x01, 0x0a, 0x26, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x25, 0x0a, 0x0e, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x62, 0x75, 0x63, 0x6b, 0x65,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f,
	0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x63, 0x0a, 0x27, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xf8, 0x03, 0x0a, 0x23,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x32, 0x0a, 0x15, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74,
	0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x3c, 0x0a, 0x1a, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x36, 0x0a, 0x17, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x62,
	0x75, 0x63, 0x6b, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x15, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x75, 0x63,
	0x6b, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x1b, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x18, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x47, 0x0a, 0x20, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x1d, 0x64, 0x65, 0x73, 0x74, 0x69, 0x6e, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x53, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x16, 0x0a, 0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x63, 0x75, 0x72, 0x73, 0x6f, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x6f, 0x6e, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x63, 0x6c, 0x6f,
	0x6e, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x60, 0x0a, 0x24, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f,
	0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfc, 0x01, 0x0a, 0x26, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x23, 0x0a, 0x0d, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x42, 0x75,
	0x63, 0x6b, 0x65, 0x74, 0x12, 0x29, 0x0a, 0x10, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x64,
	0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x69, 0x6e, 0x6b, 0x42, 0x75, 0x63, 0x6b, 0x65, 0x74,
	0x12, 0x25, 0x0a, 0x0e, 0x73, 0x69, 0x6e, 0x6b, 0x5f, 0x64, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x73, 0x69, 0x6e, 0x6b, 0x44, 0x69,
	0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x22, 0x63, 0x0a, 0x27, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x69,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7d, 0x0a, 0x12,
	0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x62, 0x61,
	0x63, 0x6b, 0x75, 0x70, 0x5f, 0x64, 0x69, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x44, 0x69, 0x72, 0x12, 0x30, 0x0a, 0x14, 0x73, 0x74, 0x6f,
	0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x3a, 0x0a, 0x13, 0x47,
	0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x6d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x5f, 0x6a,
	0x73, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6d, 0x61, 0x6e, 0x69, 0x66,
	0x65, 0x73, 0x74, 0x4a, 0x73, 0x6f, 0x6e, 0x42, 0x3a, 0x5a, 0x38, 0x67, 0x69, 0x74, 0x68, 0x75,
	0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65,
	0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f,
	0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_common_data_proto_rawDescOnce sync.Once
	file_services_common_data_proto_rawDescData []byte
)

func file_services_common_data_proto_rawDescGZIP() []byte {
	file_services_common_data_proto_rawDescOnce.Do(func() {
		file_services_common_data_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_common_data_proto_rawDesc), len(file_services_common_data_proto_rawDesc)))
	})
	return file_services_common_data_proto_rawDescData
}

var file_services_common_data_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_services_common_data_proto_goTypes = []any{
	(*CreateDataDirectoryDeletionTaskRequest)(nil),  // 0: services.data.CreateDataDirectoryDeletionTaskRequest
	(*CreateDataDirectoryDeletionTaskResponse)(nil), // 1: services.data.CreateDataDirectoryDeletionTaskResponse
	(*CreateDataDirectoryCloneTaskRequest)(nil),     // 2: services.data.CreateDataDirectoryCloneTaskRequest
	(*CreateDataDirectoryCloneTaskResponse)(nil),    // 3: services.data.CreateDataDirectoryCloneTaskResponse
	(*CreateSimpleDataReplicationTaskRequest)(nil),  // 4: services.data.CreateSimpleDataReplicationTaskRequest
	(*CreateSimpleDataReplicationTaskResponse)(nil), // 5: services.data.CreateSimpleDataReplicationTaskResponse
	(*GetManifestRequest)(nil),                      // 6: services.data.GetManifestRequest
	(*GetManifestResponse)(nil),                     // 7: services.data.GetManifestResponse
	(*resource.Meta)(nil),                           // 8: common.resource.Meta
	(*creation.Status)(nil),                         // 9: common.resource.creation.Status
}
var file_services_common_data_proto_depIdxs = []int32{
	8, // 0: services.data.CreateDataDirectoryDeletionTaskRequest.resource_meta:type_name -> common.resource.Meta
	9, // 1: services.data.CreateDataDirectoryDeletionTaskResponse.status:type_name -> common.resource.creation.Status
	8, // 2: services.data.CreateDataDirectoryCloneTaskRequest.resource_meta:type_name -> common.resource.Meta
	9, // 3: services.data.CreateDataDirectoryCloneTaskResponse.status:type_name -> common.resource.creation.Status
	8, // 4: services.data.CreateSimpleDataReplicationTaskRequest.resource_meta:type_name -> common.resource.Meta
	9, // 5: services.data.CreateSimpleDataReplicationTaskResponse.status:type_name -> common.resource.creation.Status
	6, // [6:6] is the sub-list for method output_type
	6, // [6:6] is the sub-list for method input_type
	6, // [6:6] is the sub-list for extension type_name
	6, // [6:6] is the sub-list for extension extendee
	0, // [0:6] is the sub-list for field type_name
}

func init() { file_services_common_data_proto_init() }
func file_services_common_data_proto_init() {
	if File_services_common_data_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_common_data_proto_rawDesc), len(file_services_common_data_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_services_common_data_proto_goTypes,
		DependencyIndexes: file_services_common_data_proto_depIdxs,
		MessageInfos:      file_services_common_data_proto_msgTypes,
	}.Build()
	File_services_common_data_proto = out.File
	file_services_common_data_proto_goTypes = nil
	file_services_common_data_proto_depIdxs = nil
}
