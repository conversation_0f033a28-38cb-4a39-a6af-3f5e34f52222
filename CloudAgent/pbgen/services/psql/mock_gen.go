// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/psql (interfaces: PsqlManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/psql -package=psql -destination=pbgen/services/psql/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/psql PsqlManagerClient
//

// Package psql is a generated GoMock package.
package psql

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockPsqlManagerClient is a mock of PsqlManagerClient interface.
type MockPsqlManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockPsqlManagerClientMockRecorder
	isgomock struct{}
}

// MockPsqlManagerClientMockRecorder is the mock recorder for MockPsqlManagerClient.
type MockPsqlManagerClientMockRecorder struct {
	mock *MockPsqlManagerClient
}

// NewMockPsqlManagerClient creates a new mock instance.
func NewMockPsqlManagerClient(ctrl *gomock.Controller) *MockPsqlManagerClient {
	mock := &MockPsqlManagerClient{ctrl: ctrl}
	mock.recorder = &MockPsqlManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPsqlManagerClient) EXPECT() *MockPsqlManagerClientMockRecorder {
	return m.recorder
}

// CloneDatabase mocks base method.
func (m *MockPsqlManagerClient) CloneDatabase(ctx context.Context, in *CloneDatabaseRequest, opts ...grpc.CallOption) (*CloneDatabaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CloneDatabase", varargs...)
	ret0, _ := ret[0].(*CloneDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CloneDatabase indicates an expected call of CloneDatabase.
func (mr *MockPsqlManagerClientMockRecorder) CloneDatabase(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloneDatabase", reflect.TypeOf((*MockPsqlManagerClient)(nil).CloneDatabase), varargs...)
}

// CreateDatabase mocks base method.
func (m *MockPsqlManagerClient) CreateDatabase(ctx context.Context, in *CreateDatabaseRequest, opts ...grpc.CallOption) (*CreateDatabaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDatabase", varargs...)
	ret0, _ := ret[0].(*CreateDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDatabase indicates an expected call of CreateDatabase.
func (mr *MockPsqlManagerClientMockRecorder) CreateDatabase(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDatabase", reflect.TypeOf((*MockPsqlManagerClient)(nil).CreateDatabase), varargs...)
}

// CreateUser mocks base method.
func (m *MockPsqlManagerClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateUser", varargs...)
	ret0, _ := ret[0].(*CreateUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockPsqlManagerClientMockRecorder) CreateUser(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockPsqlManagerClient)(nil).CreateUser), varargs...)
}

// DeleteDatabase mocks base method.
func (m *MockPsqlManagerClient) DeleteDatabase(ctx context.Context, in *DeleteDatabaseRequest, opts ...grpc.CallOption) (*DeleteDatabaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteDatabase", varargs...)
	ret0, _ := ret[0].(*DeleteDatabaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteDatabase indicates an expected call of DeleteDatabase.
func (mr *MockPsqlManagerClientMockRecorder) DeleteDatabase(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDatabase", reflect.TypeOf((*MockPsqlManagerClient)(nil).DeleteDatabase), varargs...)
}

// DeleteUser mocks base method.
func (m *MockPsqlManagerClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteUser", varargs...)
	ret0, _ := ret[0].(*DeleteUserResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteUser indicates an expected call of DeleteUser.
func (mr *MockPsqlManagerClientMockRecorder) DeleteUser(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUser", reflect.TypeOf((*MockPsqlManagerClient)(nil).DeleteUser), varargs...)
}

// TruncateTables mocks base method.
func (m *MockPsqlManagerClient) TruncateTables(ctx context.Context, in *TruncateTablesRequest, opts ...grpc.CallOption) (*TruncateTablesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "TruncateTables", varargs...)
	ret0, _ := ret[0].(*TruncateTablesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// TruncateTables indicates an expected call of TruncateTables.
func (mr *MockPsqlManagerClientMockRecorder) TruncateTables(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TruncateTables", reflect.TypeOf((*MockPsqlManagerClient)(nil).TruncateTables), varargs...)
}

// UpdateSystemParameters mocks base method.
func (m *MockPsqlManagerClient) UpdateSystemParameters(ctx context.Context, in *UpdateSystemParametersRequest, opts ...grpc.CallOption) (*UpdateSystemParametersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSystemParameters", varargs...)
	ret0, _ := ret[0].(*UpdateSystemParametersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSystemParameters indicates an expected call of UpdateSystemParameters.
func (mr *MockPsqlManagerClientMockRecorder) UpdateSystemParameters(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSystemParameters", reflect.TypeOf((*MockPsqlManagerClient)(nil).UpdateSystemParameters), varargs...)
}
