// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/psql.proto

package psql

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	PsqlManager_CreateDatabase_FullMethodName         = "/services.psql.PsqlManager/CreateDatabase"
	PsqlManager_DeleteDatabase_FullMethodName         = "/services.psql.PsqlManager/DeleteDatabase"
	PsqlManager_CreateUser_FullMethodName             = "/services.psql.PsqlManager/CreateUser"
	PsqlManager_DeleteUser_FullMethodName             = "/services.psql.PsqlManager/DeleteUser"
	PsqlManager_TruncateTables_FullMethodName         = "/services.psql.PsqlManager/TruncateTables"
	PsqlManager_UpdateSystemParameters_FullMethodName = "/services.psql.PsqlManager/UpdateSystemParameters"
	PsqlManager_CloneDatabase_FullMethodName          = "/services.psql.PsqlManager/CloneDatabase"
)

// PsqlManagerClient is the client API for PsqlManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type PsqlManagerClient interface {
	CreateDatabase(ctx context.Context, in *CreateDatabaseRequest, opts ...grpc.CallOption) (*CreateDatabaseResponse, error)
	DeleteDatabase(ctx context.Context, in *DeleteDatabaseRequest, opts ...grpc.CallOption) (*DeleteDatabaseResponse, error)
	CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error)
	DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserResponse, error)
	TruncateTables(ctx context.Context, in *TruncateTablesRequest, opts ...grpc.CallOption) (*TruncateTablesResponse, error)
	UpdateSystemParameters(ctx context.Context, in *UpdateSystemParametersRequest, opts ...grpc.CallOption) (*UpdateSystemParametersResponse, error)
	CloneDatabase(ctx context.Context, in *CloneDatabaseRequest, opts ...grpc.CallOption) (*CloneDatabaseResponse, error)
}

type psqlManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewPsqlManagerClient(cc grpc.ClientConnInterface) PsqlManagerClient {
	return &psqlManagerClient{cc}
}

func (c *psqlManagerClient) CreateDatabase(ctx context.Context, in *CreateDatabaseRequest, opts ...grpc.CallOption) (*CreateDatabaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDatabaseResponse)
	err := c.cc.Invoke(ctx, PsqlManager_CreateDatabase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psqlManagerClient) DeleteDatabase(ctx context.Context, in *DeleteDatabaseRequest, opts ...grpc.CallOption) (*DeleteDatabaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDatabaseResponse)
	err := c.cc.Invoke(ctx, PsqlManager_DeleteDatabase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psqlManagerClient) CreateUser(ctx context.Context, in *CreateUserRequest, opts ...grpc.CallOption) (*CreateUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateUserResponse)
	err := c.cc.Invoke(ctx, PsqlManager_CreateUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psqlManagerClient) DeleteUser(ctx context.Context, in *DeleteUserRequest, opts ...grpc.CallOption) (*DeleteUserResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteUserResponse)
	err := c.cc.Invoke(ctx, PsqlManager_DeleteUser_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psqlManagerClient) TruncateTables(ctx context.Context, in *TruncateTablesRequest, opts ...grpc.CallOption) (*TruncateTablesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(TruncateTablesResponse)
	err := c.cc.Invoke(ctx, PsqlManager_TruncateTables_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psqlManagerClient) UpdateSystemParameters(ctx context.Context, in *UpdateSystemParametersRequest, opts ...grpc.CallOption) (*UpdateSystemParametersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateSystemParametersResponse)
	err := c.cc.Invoke(ctx, PsqlManager_UpdateSystemParameters_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *psqlManagerClient) CloneDatabase(ctx context.Context, in *CloneDatabaseRequest, opts ...grpc.CallOption) (*CloneDatabaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CloneDatabaseResponse)
	err := c.cc.Invoke(ctx, PsqlManager_CloneDatabase_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// PsqlManagerServer is the server API for PsqlManager service.
// All implementations must embed UnimplementedPsqlManagerServer
// for forward compatibility.
type PsqlManagerServer interface {
	CreateDatabase(context.Context, *CreateDatabaseRequest) (*CreateDatabaseResponse, error)
	DeleteDatabase(context.Context, *DeleteDatabaseRequest) (*DeleteDatabaseResponse, error)
	CreateUser(context.Context, *CreateUserRequest) (*CreateUserResponse, error)
	DeleteUser(context.Context, *DeleteUserRequest) (*DeleteUserResponse, error)
	TruncateTables(context.Context, *TruncateTablesRequest) (*TruncateTablesResponse, error)
	UpdateSystemParameters(context.Context, *UpdateSystemParametersRequest) (*UpdateSystemParametersResponse, error)
	CloneDatabase(context.Context, *CloneDatabaseRequest) (*CloneDatabaseResponse, error)
	mustEmbedUnimplementedPsqlManagerServer()
}

// UnimplementedPsqlManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedPsqlManagerServer struct{}

func (UnimplementedPsqlManagerServer) CreateDatabase(context.Context, *CreateDatabaseRequest) (*CreateDatabaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDatabase not implemented")
}
func (UnimplementedPsqlManagerServer) DeleteDatabase(context.Context, *DeleteDatabaseRequest) (*DeleteDatabaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDatabase not implemented")
}
func (UnimplementedPsqlManagerServer) CreateUser(context.Context, *CreateUserRequest) (*CreateUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateUser not implemented")
}
func (UnimplementedPsqlManagerServer) DeleteUser(context.Context, *DeleteUserRequest) (*DeleteUserResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteUser not implemented")
}
func (UnimplementedPsqlManagerServer) TruncateTables(context.Context, *TruncateTablesRequest) (*TruncateTablesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TruncateTables not implemented")
}
func (UnimplementedPsqlManagerServer) UpdateSystemParameters(context.Context, *UpdateSystemParametersRequest) (*UpdateSystemParametersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSystemParameters not implemented")
}
func (UnimplementedPsqlManagerServer) CloneDatabase(context.Context, *CloneDatabaseRequest) (*CloneDatabaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CloneDatabase not implemented")
}
func (UnimplementedPsqlManagerServer) mustEmbedUnimplementedPsqlManagerServer() {}
func (UnimplementedPsqlManagerServer) testEmbeddedByValue()                     {}

// UnsafePsqlManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to PsqlManagerServer will
// result in compilation errors.
type UnsafePsqlManagerServer interface {
	mustEmbedUnimplementedPsqlManagerServer()
}

func RegisterPsqlManagerServer(s grpc.ServiceRegistrar, srv PsqlManagerServer) {
	// If the following call pancis, it indicates UnimplementedPsqlManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&PsqlManager_ServiceDesc, srv)
}

func _PsqlManager_CreateDatabase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDatabaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).CreateDatabase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_CreateDatabase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).CreateDatabase(ctx, req.(*CreateDatabaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsqlManager_DeleteDatabase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDatabaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).DeleteDatabase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_DeleteDatabase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).DeleteDatabase(ctx, req.(*DeleteDatabaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsqlManager_CreateUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).CreateUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_CreateUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).CreateUser(ctx, req.(*CreateUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsqlManager_DeleteUser_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteUserRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).DeleteUser(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_DeleteUser_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).DeleteUser(ctx, req.(*DeleteUserRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsqlManager_TruncateTables_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TruncateTablesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).TruncateTables(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_TruncateTables_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).TruncateTables(ctx, req.(*TruncateTablesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsqlManager_UpdateSystemParameters_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSystemParametersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).UpdateSystemParameters(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_UpdateSystemParameters_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).UpdateSystemParameters(ctx, req.(*UpdateSystemParametersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _PsqlManager_CloneDatabase_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CloneDatabaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(PsqlManagerServer).CloneDatabase(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: PsqlManager_CloneDatabase_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(PsqlManagerServer).CloneDatabase(ctx, req.(*CloneDatabaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// PsqlManager_ServiceDesc is the grpc.ServiceDesc for PsqlManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var PsqlManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.psql.PsqlManager",
	HandlerType: (*PsqlManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDatabase",
			Handler:    _PsqlManager_CreateDatabase_Handler,
		},
		{
			MethodName: "DeleteDatabase",
			Handler:    _PsqlManager_DeleteDatabase_Handler,
		},
		{
			MethodName: "CreateUser",
			Handler:    _PsqlManager_CreateUser_Handler,
		},
		{
			MethodName: "DeleteUser",
			Handler:    _PsqlManager_DeleteUser_Handler,
		},
		{
			MethodName: "TruncateTables",
			Handler:    _PsqlManager_TruncateTables_Handler,
		},
		{
			MethodName: "UpdateSystemParameters",
			Handler:    _PsqlManager_UpdateSystemParameters_Handler,
		},
		{
			MethodName: "CloneDatabase",
			Handler:    _PsqlManager_CloneDatabase_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/psql.proto",
}
