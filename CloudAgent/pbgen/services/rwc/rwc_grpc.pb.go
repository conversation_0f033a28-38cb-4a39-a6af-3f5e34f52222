// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/rwc.proto

package rwc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	RisingwaveControl_MetaNodeBackup_FullMethodName           = "/services.rwc.RisingwaveControl/MetaNodeBackup"
	RisingwaveControl_ValidateSource_FullMethodName           = "/services.rwc.RisingwaveControl/ValidateSource"
	RisingwaveControl_GetClusterInfo_FullMethodName           = "/services.rwc.RisingwaveControl/GetClusterInfo"
	RisingwaveControl_CordonWorkers_FullMethodName            = "/services.rwc.RisingwaveControl/CordonWorkers"
	RisingwaveControl_ResizeWorkers_FullMethodName            = "/services.rwc.RisingwaveControl/ResizeWorkers"
	RisingwaveControl_DeleteWorkers_FullMethodName            = "/services.rwc.RisingwaveControl/DeleteWorkers"
	RisingwaveControl_DeleteSnapshot_FullMethodName           = "/services.rwc.RisingwaveControl/DeleteSnapshot"
	RisingwaveControl_RestoreMeta_FullMethodName              = "/services.rwc.RisingwaveControl/RestoreMeta"
	RisingwaveControl_VacuumEtcdMeta_FullMethodName           = "/services.rwc.RisingwaveControl/VacuumEtcdMeta"
	RisingwaveControl_GenDiagnosisReport_FullMethodName       = "/services.rwc.RisingwaveControl/GenDiagnosisReport"
	RisingwaveControl_GenDiagnosisReportStream_FullMethodName = "/services.rwc.RisingwaveControl/GenDiagnosisReportStream"
	RisingwaveControl_MetaMigration_FullMethodName            = "/services.rwc.RisingwaveControl/MetaMigration"
	RisingwaveControl_FetchKafkaTopic_FullMethodName          = "/services.rwc.RisingwaveControl/FetchKafkaTopic"
	RisingwaveControl_FetchKafkaMessage_FullMethodName        = "/services.rwc.RisingwaveControl/FetchKafkaMessage"
	RisingwaveControl_FetchPostgresTable_FullMethodName       = "/services.rwc.RisingwaveControl/FetchPostgresTable"
	RisingwaveControl_FetchSourceSchema_FullMethodName        = "/services.rwc.RisingwaveControl/FetchSourceSchema"
)

// RisingwaveControlClient is the client API for RisingwaveControl service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type RisingwaveControlClient interface {
	// MetaNodeBackup creates a task for meta backup creation.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	MetaNodeBackup(ctx context.Context, in *MetaNodeBackupRequest, opts ...grpc.CallOption) (*MetaNodeBackupResponse, error)
	ValidateSource(ctx context.Context, in *ValidateSourceRequest, opts ...grpc.CallOption) (*ValidateSourceResponse, error)
	GetClusterInfo(ctx context.Context, in *GetClusterInfoRequest, opts ...grpc.CallOption) (*GetClusterInfoResponse, error)
	CordonWorkers(ctx context.Context, in *CordonWorkersRequest, opts ...grpc.CallOption) (*CordonWorkersResponse, error)
	ResizeWorkers(ctx context.Context, in *ResizeWorkersRequest, opts ...grpc.CallOption) (*ResizeWorkersResponse, error)
	DeleteWorkers(ctx context.Context, in *DeleteWorkersRequest, opts ...grpc.CallOption) (*DeleteWorkersResponse, error)
	DeleteSnapshot(ctx context.Context, in *DeleteSnapshotRequest, opts ...grpc.CallOption) (*DeleteSnapshotResponse, error)
	// RestoreMeta creates a task for restoring a snapshot.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	RestoreMeta(ctx context.Context, in *RestoreMetaRequest, opts ...grpc.CallOption) (*RestoreMetaResponse, error)
	VacuumEtcdMeta(ctx context.Context, in *VacuumEtcdMetaRequest, opts ...grpc.CallOption) (*VacuumEtcdMetaResponse, error)
	// RW Diagnosis Report
	GenDiagnosisReport(ctx context.Context, in *GenDiagnosisReportRequest, opts ...grpc.CallOption) (*GenDiagnosisReportResponse, error)
	GenDiagnosisReportStream(ctx context.Context, in *GenDiagnosisReportStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[GenDiagnosisReportStreamResponse], error)
	MetaMigration(ctx context.Context, in *MetaMigrationRequest, opts ...grpc.CallOption) (*MetaMigrationResponse, error)
	FetchKafkaTopic(ctx context.Context, in *FetchKafkaTopicRequest, opts ...grpc.CallOption) (*FetchKafkaTopicResponse, error)
	FetchKafkaMessage(ctx context.Context, in *FetchKafkaMessageRequest, opts ...grpc.CallOption) (*FetchKafkaMessageResponse, error)
	FetchPostgresTable(ctx context.Context, in *FetchPostgresTableRequest, opts ...grpc.CallOption) (*FetchPostgresTableResponse, error)
	FetchSourceSchema(ctx context.Context, in *FetchSourceSchemaRequest, opts ...grpc.CallOption) (*FetchSourceSchemaResponse, error)
}

type risingwaveControlClient struct {
	cc grpc.ClientConnInterface
}

func NewRisingwaveControlClient(cc grpc.ClientConnInterface) RisingwaveControlClient {
	return &risingwaveControlClient{cc}
}

func (c *risingwaveControlClient) MetaNodeBackup(ctx context.Context, in *MetaNodeBackupRequest, opts ...grpc.CallOption) (*MetaNodeBackupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MetaNodeBackupResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_MetaNodeBackup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) ValidateSource(ctx context.Context, in *ValidateSourceRequest, opts ...grpc.CallOption) (*ValidateSourceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ValidateSourceResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_ValidateSource_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) GetClusterInfo(ctx context.Context, in *GetClusterInfoRequest, opts ...grpc.CallOption) (*GetClusterInfoResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClusterInfoResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_GetClusterInfo_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) CordonWorkers(ctx context.Context, in *CordonWorkersRequest, opts ...grpc.CallOption) (*CordonWorkersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CordonWorkersResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_CordonWorkers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) ResizeWorkers(ctx context.Context, in *ResizeWorkersRequest, opts ...grpc.CallOption) (*ResizeWorkersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ResizeWorkersResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_ResizeWorkers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) DeleteWorkers(ctx context.Context, in *DeleteWorkersRequest, opts ...grpc.CallOption) (*DeleteWorkersResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteWorkersResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_DeleteWorkers_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) DeleteSnapshot(ctx context.Context, in *DeleteSnapshotRequest, opts ...grpc.CallOption) (*DeleteSnapshotResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSnapshotResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_DeleteSnapshot_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) RestoreMeta(ctx context.Context, in *RestoreMetaRequest, opts ...grpc.CallOption) (*RestoreMetaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RestoreMetaResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_RestoreMeta_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) VacuumEtcdMeta(ctx context.Context, in *VacuumEtcdMetaRequest, opts ...grpc.CallOption) (*VacuumEtcdMetaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(VacuumEtcdMetaResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_VacuumEtcdMeta_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) GenDiagnosisReport(ctx context.Context, in *GenDiagnosisReportRequest, opts ...grpc.CallOption) (*GenDiagnosisReportResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GenDiagnosisReportResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_GenDiagnosisReport_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) GenDiagnosisReportStream(ctx context.Context, in *GenDiagnosisReportStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[GenDiagnosisReportStreamResponse], error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	stream, err := c.cc.NewStream(ctx, &RisingwaveControl_ServiceDesc.Streams[0], RisingwaveControl_GenDiagnosisReportStream_FullMethodName, cOpts...)
	if err != nil {
		return nil, err
	}
	x := &grpc.GenericClientStream[GenDiagnosisReportStreamRequest, GenDiagnosisReportStreamResponse]{ClientStream: stream}
	if err := x.ClientStream.SendMsg(in); err != nil {
		return nil, err
	}
	if err := x.ClientStream.CloseSend(); err != nil {
		return nil, err
	}
	return x, nil
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type RisingwaveControl_GenDiagnosisReportStreamClient = grpc.ServerStreamingClient[GenDiagnosisReportStreamResponse]

func (c *risingwaveControlClient) MetaMigration(ctx context.Context, in *MetaMigrationRequest, opts ...grpc.CallOption) (*MetaMigrationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(MetaMigrationResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_MetaMigration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) FetchKafkaTopic(ctx context.Context, in *FetchKafkaTopicRequest, opts ...grpc.CallOption) (*FetchKafkaTopicResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FetchKafkaTopicResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_FetchKafkaTopic_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) FetchKafkaMessage(ctx context.Context, in *FetchKafkaMessageRequest, opts ...grpc.CallOption) (*FetchKafkaMessageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FetchKafkaMessageResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_FetchKafkaMessage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) FetchPostgresTable(ctx context.Context, in *FetchPostgresTableRequest, opts ...grpc.CallOption) (*FetchPostgresTableResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FetchPostgresTableResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_FetchPostgresTable_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *risingwaveControlClient) FetchSourceSchema(ctx context.Context, in *FetchSourceSchemaRequest, opts ...grpc.CallOption) (*FetchSourceSchemaResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(FetchSourceSchemaResponse)
	err := c.cc.Invoke(ctx, RisingwaveControl_FetchSourceSchema_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// RisingwaveControlServer is the server API for RisingwaveControl service.
// All implementations must embed UnimplementedRisingwaveControlServer
// for forward compatibility.
type RisingwaveControlServer interface {
	// MetaNodeBackup creates a task for meta backup creation.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	MetaNodeBackup(context.Context, *MetaNodeBackupRequest) (*MetaNodeBackupResponse, error)
	ValidateSource(context.Context, *ValidateSourceRequest) (*ValidateSourceResponse, error)
	GetClusterInfo(context.Context, *GetClusterInfoRequest) (*GetClusterInfoResponse, error)
	CordonWorkers(context.Context, *CordonWorkersRequest) (*CordonWorkersResponse, error)
	ResizeWorkers(context.Context, *ResizeWorkersRequest) (*ResizeWorkersResponse, error)
	DeleteWorkers(context.Context, *DeleteWorkersRequest) (*DeleteWorkersResponse, error)
	DeleteSnapshot(context.Context, *DeleteSnapshotRequest) (*DeleteSnapshotResponse, error)
	// RestoreMeta creates a task for restoring a snapshot.
	// Caller should call the task manager service to manage the created task.
	// Expected a CREATED status on success.
	RestoreMeta(context.Context, *RestoreMetaRequest) (*RestoreMetaResponse, error)
	VacuumEtcdMeta(context.Context, *VacuumEtcdMetaRequest) (*VacuumEtcdMetaResponse, error)
	// RW Diagnosis Report
	GenDiagnosisReport(context.Context, *GenDiagnosisReportRequest) (*GenDiagnosisReportResponse, error)
	GenDiagnosisReportStream(*GenDiagnosisReportStreamRequest, grpc.ServerStreamingServer[GenDiagnosisReportStreamResponse]) error
	MetaMigration(context.Context, *MetaMigrationRequest) (*MetaMigrationResponse, error)
	FetchKafkaTopic(context.Context, *FetchKafkaTopicRequest) (*FetchKafkaTopicResponse, error)
	FetchKafkaMessage(context.Context, *FetchKafkaMessageRequest) (*FetchKafkaMessageResponse, error)
	FetchPostgresTable(context.Context, *FetchPostgresTableRequest) (*FetchPostgresTableResponse, error)
	FetchSourceSchema(context.Context, *FetchSourceSchemaRequest) (*FetchSourceSchemaResponse, error)
	mustEmbedUnimplementedRisingwaveControlServer()
}

// UnimplementedRisingwaveControlServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedRisingwaveControlServer struct{}

func (UnimplementedRisingwaveControlServer) MetaNodeBackup(context.Context, *MetaNodeBackupRequest) (*MetaNodeBackupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MetaNodeBackup not implemented")
}
func (UnimplementedRisingwaveControlServer) ValidateSource(context.Context, *ValidateSourceRequest) (*ValidateSourceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ValidateSource not implemented")
}
func (UnimplementedRisingwaveControlServer) GetClusterInfo(context.Context, *GetClusterInfoRequest) (*GetClusterInfoResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClusterInfo not implemented")
}
func (UnimplementedRisingwaveControlServer) CordonWorkers(context.Context, *CordonWorkersRequest) (*CordonWorkersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CordonWorkers not implemented")
}
func (UnimplementedRisingwaveControlServer) ResizeWorkers(context.Context, *ResizeWorkersRequest) (*ResizeWorkersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResizeWorkers not implemented")
}
func (UnimplementedRisingwaveControlServer) DeleteWorkers(context.Context, *DeleteWorkersRequest) (*DeleteWorkersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteWorkers not implemented")
}
func (UnimplementedRisingwaveControlServer) DeleteSnapshot(context.Context, *DeleteSnapshotRequest) (*DeleteSnapshotResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSnapshot not implemented")
}
func (UnimplementedRisingwaveControlServer) RestoreMeta(context.Context, *RestoreMetaRequest) (*RestoreMetaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestoreMeta not implemented")
}
func (UnimplementedRisingwaveControlServer) VacuumEtcdMeta(context.Context, *VacuumEtcdMetaRequest) (*VacuumEtcdMetaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method VacuumEtcdMeta not implemented")
}
func (UnimplementedRisingwaveControlServer) GenDiagnosisReport(context.Context, *GenDiagnosisReportRequest) (*GenDiagnosisReportResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GenDiagnosisReport not implemented")
}
func (UnimplementedRisingwaveControlServer) GenDiagnosisReportStream(*GenDiagnosisReportStreamRequest, grpc.ServerStreamingServer[GenDiagnosisReportStreamResponse]) error {
	return status.Errorf(codes.Unimplemented, "method GenDiagnosisReportStream not implemented")
}
func (UnimplementedRisingwaveControlServer) MetaMigration(context.Context, *MetaMigrationRequest) (*MetaMigrationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method MetaMigration not implemented")
}
func (UnimplementedRisingwaveControlServer) FetchKafkaTopic(context.Context, *FetchKafkaTopicRequest) (*FetchKafkaTopicResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchKafkaTopic not implemented")
}
func (UnimplementedRisingwaveControlServer) FetchKafkaMessage(context.Context, *FetchKafkaMessageRequest) (*FetchKafkaMessageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchKafkaMessage not implemented")
}
func (UnimplementedRisingwaveControlServer) FetchPostgresTable(context.Context, *FetchPostgresTableRequest) (*FetchPostgresTableResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchPostgresTable not implemented")
}
func (UnimplementedRisingwaveControlServer) FetchSourceSchema(context.Context, *FetchSourceSchemaRequest) (*FetchSourceSchemaResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method FetchSourceSchema not implemented")
}
func (UnimplementedRisingwaveControlServer) mustEmbedUnimplementedRisingwaveControlServer() {}
func (UnimplementedRisingwaveControlServer) testEmbeddedByValue()                           {}

// UnsafeRisingwaveControlServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to RisingwaveControlServer will
// result in compilation errors.
type UnsafeRisingwaveControlServer interface {
	mustEmbedUnimplementedRisingwaveControlServer()
}

func RegisterRisingwaveControlServer(s grpc.ServiceRegistrar, srv RisingwaveControlServer) {
	// If the following call pancis, it indicates UnimplementedRisingwaveControlServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&RisingwaveControl_ServiceDesc, srv)
}

func _RisingwaveControl_MetaNodeBackup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MetaNodeBackupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).MetaNodeBackup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_MetaNodeBackup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).MetaNodeBackup(ctx, req.(*MetaNodeBackupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_ValidateSource_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ValidateSourceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).ValidateSource(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_ValidateSource_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).ValidateSource(ctx, req.(*ValidateSourceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_GetClusterInfo_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterInfoRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).GetClusterInfo(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_GetClusterInfo_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).GetClusterInfo(ctx, req.(*GetClusterInfoRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_CordonWorkers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CordonWorkersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).CordonWorkers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_CordonWorkers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).CordonWorkers(ctx, req.(*CordonWorkersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_ResizeWorkers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResizeWorkersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).ResizeWorkers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_ResizeWorkers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).ResizeWorkers(ctx, req.(*ResizeWorkersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_DeleteWorkers_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteWorkersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).DeleteWorkers(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_DeleteWorkers_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).DeleteWorkers(ctx, req.(*DeleteWorkersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_DeleteSnapshot_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSnapshotRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).DeleteSnapshot(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_DeleteSnapshot_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).DeleteSnapshot(ctx, req.(*DeleteSnapshotRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_RestoreMeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestoreMetaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).RestoreMeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_RestoreMeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).RestoreMeta(ctx, req.(*RestoreMetaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_VacuumEtcdMeta_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(VacuumEtcdMetaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).VacuumEtcdMeta(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_VacuumEtcdMeta_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).VacuumEtcdMeta(ctx, req.(*VacuumEtcdMetaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_GenDiagnosisReport_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GenDiagnosisReportRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).GenDiagnosisReport(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_GenDiagnosisReport_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).GenDiagnosisReport(ctx, req.(*GenDiagnosisReportRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_GenDiagnosisReportStream_Handler(srv interface{}, stream grpc.ServerStream) error {
	m := new(GenDiagnosisReportStreamRequest)
	if err := stream.RecvMsg(m); err != nil {
		return err
	}
	return srv.(RisingwaveControlServer).GenDiagnosisReportStream(m, &grpc.GenericServerStream[GenDiagnosisReportStreamRequest, GenDiagnosisReportStreamResponse]{ServerStream: stream})
}

// This type alias is provided for backwards compatibility with existing code that references the prior non-generic stream type by name.
type RisingwaveControl_GenDiagnosisReportStreamServer = grpc.ServerStreamingServer[GenDiagnosisReportStreamResponse]

func _RisingwaveControl_MetaMigration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(MetaMigrationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).MetaMigration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_MetaMigration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).MetaMigration(ctx, req.(*MetaMigrationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_FetchKafkaTopic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchKafkaTopicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).FetchKafkaTopic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_FetchKafkaTopic_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).FetchKafkaTopic(ctx, req.(*FetchKafkaTopicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_FetchKafkaMessage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchKafkaMessageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).FetchKafkaMessage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_FetchKafkaMessage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).FetchKafkaMessage(ctx, req.(*FetchKafkaMessageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_FetchPostgresTable_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchPostgresTableRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).FetchPostgresTable(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_FetchPostgresTable_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).FetchPostgresTable(ctx, req.(*FetchPostgresTableRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _RisingwaveControl_FetchSourceSchema_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(FetchSourceSchemaRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(RisingwaveControlServer).FetchSourceSchema(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: RisingwaveControl_FetchSourceSchema_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(RisingwaveControlServer).FetchSourceSchema(ctx, req.(*FetchSourceSchemaRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// RisingwaveControl_ServiceDesc is the grpc.ServiceDesc for RisingwaveControl service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var RisingwaveControl_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.rwc.RisingwaveControl",
	HandlerType: (*RisingwaveControlServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "MetaNodeBackup",
			Handler:    _RisingwaveControl_MetaNodeBackup_Handler,
		},
		{
			MethodName: "ValidateSource",
			Handler:    _RisingwaveControl_ValidateSource_Handler,
		},
		{
			MethodName: "GetClusterInfo",
			Handler:    _RisingwaveControl_GetClusterInfo_Handler,
		},
		{
			MethodName: "CordonWorkers",
			Handler:    _RisingwaveControl_CordonWorkers_Handler,
		},
		{
			MethodName: "ResizeWorkers",
			Handler:    _RisingwaveControl_ResizeWorkers_Handler,
		},
		{
			MethodName: "DeleteWorkers",
			Handler:    _RisingwaveControl_DeleteWorkers_Handler,
		},
		{
			MethodName: "DeleteSnapshot",
			Handler:    _RisingwaveControl_DeleteSnapshot_Handler,
		},
		{
			MethodName: "RestoreMeta",
			Handler:    _RisingwaveControl_RestoreMeta_Handler,
		},
		{
			MethodName: "VacuumEtcdMeta",
			Handler:    _RisingwaveControl_VacuumEtcdMeta_Handler,
		},
		{
			MethodName: "GenDiagnosisReport",
			Handler:    _RisingwaveControl_GenDiagnosisReport_Handler,
		},
		{
			MethodName: "MetaMigration",
			Handler:    _RisingwaveControl_MetaMigration_Handler,
		},
		{
			MethodName: "FetchKafkaTopic",
			Handler:    _RisingwaveControl_FetchKafkaTopic_Handler,
		},
		{
			MethodName: "FetchKafkaMessage",
			Handler:    _RisingwaveControl_FetchKafkaMessage_Handler,
		},
		{
			MethodName: "FetchPostgresTable",
			Handler:    _RisingwaveControl_FetchPostgresTable_Handler,
		},
		{
			MethodName: "FetchSourceSchema",
			Handler:    _RisingwaveControl_FetchSourceSchema_Handler,
		},
	},
	Streams: []grpc.StreamDesc{
		{
			StreamName:    "GenDiagnosisReportStream",
			Handler:       _RisingwaveControl_GenDiagnosisReportStream_Handler,
			ServerStreams: true,
		},
	},
	Metadata: "services/rwc.proto",
}
