// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/rwc.proto

package rwc

import (
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type KafkaSecurityProtocol int32

const (
	KafkaSecurityProtocol_PROTOCOL_UNSPECIFIED KafkaSecurityProtocol = 0
	KafkaSecurityProtocol_SASL_PLAINTEXT       KafkaSecurityProtocol = 1
	KafkaSecurityProtocol_SASL_SSL             KafkaSecurityProtocol = 2
	KafkaSecurityProtocol_SSL                  KafkaSecurityProtocol = 3
)

// Enum value maps for KafkaSecurityProtocol.
var (
	KafkaSecurityProtocol_name = map[int32]string{
		0: "PROTOCOL_UNSPECIFIED",
		1: "SASL_PLAINTEXT",
		2: "SASL_SSL",
		3: "SSL",
	}
	KafkaSecurityProtocol_value = map[string]int32{
		"PROTOCOL_UNSPECIFIED": 0,
		"SASL_PLAINTEXT":       1,
		"SASL_SSL":             2,
		"SSL":                  3,
	}
)

func (x KafkaSecurityProtocol) Enum() *KafkaSecurityProtocol {
	p := new(KafkaSecurityProtocol)
	*p = x
	return p
}

func (x KafkaSecurityProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KafkaSecurityProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_services_rwc_proto_enumTypes[0].Descriptor()
}

func (KafkaSecurityProtocol) Type() protoreflect.EnumType {
	return &file_services_rwc_proto_enumTypes[0]
}

func (x KafkaSecurityProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KafkaSecurityProtocol.Descriptor instead.
func (KafkaSecurityProtocol) EnumDescriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{0}
}

type KafkaSaslMechanism int32

const (
	KafkaSaslMechanism_MECHANISM_UNSPECIFIED KafkaSaslMechanism = 0
	KafkaSaslMechanism_PLAIN                 KafkaSaslMechanism = 1
	KafkaSaslMechanism_SCRAM_SHA_256         KafkaSaslMechanism = 2
	KafkaSaslMechanism_SCRAM_SHA_512         KafkaSaslMechanism = 3
)

// Enum value maps for KafkaSaslMechanism.
var (
	KafkaSaslMechanism_name = map[int32]string{
		0: "MECHANISM_UNSPECIFIED",
		1: "PLAIN",
		2: "SCRAM_SHA_256",
		3: "SCRAM_SHA_512",
	}
	KafkaSaslMechanism_value = map[string]int32{
		"MECHANISM_UNSPECIFIED": 0,
		"PLAIN":                 1,
		"SCRAM_SHA_256":         2,
		"SCRAM_SHA_512":         3,
	}
)

func (x KafkaSaslMechanism) Enum() *KafkaSaslMechanism {
	p := new(KafkaSaslMechanism)
	*p = x
	return p
}

func (x KafkaSaslMechanism) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (KafkaSaslMechanism) Descriptor() protoreflect.EnumDescriptor {
	return file_services_rwc_proto_enumTypes[1].Descriptor()
}

func (KafkaSaslMechanism) Type() protoreflect.EnumType {
	return &file_services_rwc_proto_enumTypes[1]
}

func (x KafkaSaslMechanism) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use KafkaSaslMechanism.Descriptor instead.
func (KafkaSaslMechanism) EnumDescriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{1}
}

type PostgresSslMode int32

const (
	PostgresSslMode_SSL_MODE_UNSPECIFIED PostgresSslMode = 0
	PostgresSslMode_DISABLED             PostgresSslMode = 1
	PostgresSslMode_PREFERRED            PostgresSslMode = 2
	PostgresSslMode_REQUIRED             PostgresSslMode = 3
	PostgresSslMode_VERIFY_CA            PostgresSslMode = 4
	PostgresSslMode_VERIFY_FULL          PostgresSslMode = 5
)

// Enum value maps for PostgresSslMode.
var (
	PostgresSslMode_name = map[int32]string{
		0: "SSL_MODE_UNSPECIFIED",
		1: "DISABLED",
		2: "PREFERRED",
		3: "REQUIRED",
		4: "VERIFY_CA",
		5: "VERIFY_FULL",
	}
	PostgresSslMode_value = map[string]int32{
		"SSL_MODE_UNSPECIFIED": 0,
		"DISABLED":             1,
		"PREFERRED":            2,
		"REQUIRED":             3,
		"VERIFY_CA":            4,
		"VERIFY_FULL":          5,
	}
)

func (x PostgresSslMode) Enum() *PostgresSslMode {
	p := new(PostgresSslMode)
	*p = x
	return p
}

func (x PostgresSslMode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PostgresSslMode) Descriptor() protoreflect.EnumDescriptor {
	return file_services_rwc_proto_enumTypes[2].Descriptor()
}

func (PostgresSslMode) Type() protoreflect.EnumType {
	return &file_services_rwc_proto_enumTypes[2]
}

func (x PostgresSslMode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PostgresSslMode.Descriptor instead.
func (PostgresSslMode) EnumDescriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{2}
}

type SchemaFormat int32

const (
	SchemaFormat_FORMAT_UNSPECIFIED SchemaFormat = 0
	SchemaFormat_AVRO               SchemaFormat = 1
	SchemaFormat_PROTOBUF           SchemaFormat = 2
	SchemaFormat_JSON               SchemaFormat = 3
)

// Enum value maps for SchemaFormat.
var (
	SchemaFormat_name = map[int32]string{
		0: "FORMAT_UNSPECIFIED",
		1: "AVRO",
		2: "PROTOBUF",
		3: "JSON",
	}
	SchemaFormat_value = map[string]int32{
		"FORMAT_UNSPECIFIED": 0,
		"AVRO":               1,
		"PROTOBUF":           2,
		"JSON":               3,
	}
)

func (x SchemaFormat) Enum() *SchemaFormat {
	p := new(SchemaFormat)
	*p = x
	return p
}

func (x SchemaFormat) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SchemaFormat) Descriptor() protoreflect.EnumDescriptor {
	return file_services_rwc_proto_enumTypes[3].Descriptor()
}

func (SchemaFormat) Type() protoreflect.EnumType {
	return &file_services_rwc_proto_enumTypes[3]
}

func (x SchemaFormat) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SchemaFormat.Descriptor instead.
func (SchemaFormat) EnumDescriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{3}
}

type SchemaLocation int32

const (
	SchemaLocation_LOCATION_UNSPECIFIED SchemaLocation = 0
	SchemaLocation_WEB_LOCATION         SchemaLocation = 1
	SchemaLocation_SCHEMA_REGISTRY      SchemaLocation = 2
	SchemaLocation_S3                   SchemaLocation = 3
)

// Enum value maps for SchemaLocation.
var (
	SchemaLocation_name = map[int32]string{
		0: "LOCATION_UNSPECIFIED",
		1: "WEB_LOCATION",
		2: "SCHEMA_REGISTRY",
		3: "S3",
	}
	SchemaLocation_value = map[string]int32{
		"LOCATION_UNSPECIFIED": 0,
		"WEB_LOCATION":         1,
		"SCHEMA_REGISTRY":      2,
		"S3":                   3,
	}
)

func (x SchemaLocation) Enum() *SchemaLocation {
	p := new(SchemaLocation)
	*p = x
	return p
}

func (x SchemaLocation) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SchemaLocation) Descriptor() protoreflect.EnumDescriptor {
	return file_services_rwc_proto_enumTypes[4].Descriptor()
}

func (SchemaLocation) Type() protoreflect.EnumType {
	return &file_services_rwc_proto_enumTypes[4]
}

func (x SchemaLocation) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SchemaLocation.Descriptor instead.
func (SchemaLocation) EnumDescriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{4}
}

// Note: Meta backup tasks are not namespaced, the namespace value will be
// ignored.
type MetaNodeBackupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	RwName        string                 `protobuf:"bytes,2,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace   string                 `protobuf:"bytes,3,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaNodeBackupRequest) Reset() {
	*x = MetaNodeBackupRequest{}
	mi := &file_services_rwc_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaNodeBackupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaNodeBackupRequest) ProtoMessage() {}

func (x *MetaNodeBackupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaNodeBackupRequest.ProtoReflect.Descriptor instead.
func (*MetaNodeBackupRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{0}
}

func (x *MetaNodeBackupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *MetaNodeBackupRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *MetaNodeBackupRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

type MetaNodeBackupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaNodeBackupResponse) Reset() {
	*x = MetaNodeBackupResponse{}
	mi := &file_services_rwc_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaNodeBackupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaNodeBackupResponse) ProtoMessage() {}

func (x *MetaNodeBackupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaNodeBackupResponse.ProtoReflect.Descriptor instead.
func (*MetaNodeBackupResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{1}
}

func (x *MetaNodeBackupResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type ValidateSourceRequest struct {
	state       protoimpl.MessageState `protogen:"open.v1"`
	RwName      string                 `protobuf:"bytes,1,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace string                 `protobuf:"bytes,2,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	// props field for `risectl meta validate-source --props <props>`
	// it contains the properties of the source creation.
	Props         string `protobuf:"bytes,3,opt,name=props,proto3" json:"props,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateSourceRequest) Reset() {
	*x = ValidateSourceRequest{}
	mi := &file_services_rwc_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateSourceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSourceRequest) ProtoMessage() {}

func (x *ValidateSourceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSourceRequest.ProtoReflect.Descriptor instead.
func (*ValidateSourceRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{2}
}

func (x *ValidateSourceRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *ValidateSourceRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

func (x *ValidateSourceRequest) GetProps() string {
	if x != nil {
		return x.Props
	}
	return ""
}

type ValidateSourceResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// this field is set when validate source cmd failed. and it is set to empty
	// if no error.
	ErrorMessage  string `protobuf:"bytes,1,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ValidateSourceResponse) Reset() {
	*x = ValidateSourceResponse{}
	mi := &file_services_rwc_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ValidateSourceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ValidateSourceResponse) ProtoMessage() {}

func (x *ValidateSourceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ValidateSourceResponse.ProtoReflect.Descriptor instead.
func (*ValidateSourceResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{3}
}

func (x *ValidateSourceResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type GetClusterInfoRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RwName        string                 `protobuf:"bytes,1,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace   string                 `protobuf:"bytes,2,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClusterInfoRequest) Reset() {
	*x = GetClusterInfoRequest{}
	mi := &file_services_rwc_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterInfoRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterInfoRequest) ProtoMessage() {}

func (x *GetClusterInfoRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterInfoRequest.ProtoReflect.Descriptor instead.
func (*GetClusterInfoRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{4}
}

func (x *GetClusterInfoRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *GetClusterInfoRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

type GetClusterInfoResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Output        string                 `protobuf:"bytes,1,opt,name=output,proto3" json:"output,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClusterInfoResponse) Reset() {
	*x = GetClusterInfoResponse{}
	mi := &file_services_rwc_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterInfoResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterInfoResponse) ProtoMessage() {}

func (x *GetClusterInfoResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterInfoResponse.ProtoReflect.Descriptor instead.
func (*GetClusterInfoResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{5}
}

func (x *GetClusterInfoResponse) GetOutput() string {
	if x != nil {
		return x.Output
	}
	return ""
}

type CordonWorkersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RwName        string                 `protobuf:"bytes,1,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace   string                 `protobuf:"bytes,2,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	WorkIds       []string               `protobuf:"bytes,3,rep,name=work_ids,json=workIds,proto3" json:"work_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CordonWorkersRequest) Reset() {
	*x = CordonWorkersRequest{}
	mi := &file_services_rwc_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CordonWorkersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CordonWorkersRequest) ProtoMessage() {}

func (x *CordonWorkersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CordonWorkersRequest.ProtoReflect.Descriptor instead.
func (*CordonWorkersRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{6}
}

func (x *CordonWorkersRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *CordonWorkersRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

func (x *CordonWorkersRequest) GetWorkIds() []string {
	if x != nil {
		return x.WorkIds
	}
	return nil
}

type CordonWorkersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CordonWorkersResponse) Reset() {
	*x = CordonWorkersResponse{}
	mi := &file_services_rwc_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CordonWorkersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CordonWorkersResponse) ProtoMessage() {}

func (x *CordonWorkersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CordonWorkersResponse.ProtoReflect.Descriptor instead.
func (*CordonWorkersResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{7}
}

type ResizeWorkersRequest struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	RwName          string                 `protobuf:"bytes,1,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace     string                 `protobuf:"bytes,2,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	AddedWorkIds    []string               `protobuf:"bytes,3,rep,name=added_work_ids,json=addedWorkIds,proto3" json:"added_work_ids,omitempty"`
	DeletingWorkIds []string               `protobuf:"bytes,4,rep,name=deleting_work_ids,json=deletingWorkIds,proto3" json:"deleting_work_ids,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *ResizeWorkersRequest) Reset() {
	*x = ResizeWorkersRequest{}
	mi := &file_services_rwc_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResizeWorkersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeWorkersRequest) ProtoMessage() {}

func (x *ResizeWorkersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeWorkersRequest.ProtoReflect.Descriptor instead.
func (*ResizeWorkersRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{8}
}

func (x *ResizeWorkersRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *ResizeWorkersRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

func (x *ResizeWorkersRequest) GetAddedWorkIds() []string {
	if x != nil {
		return x.AddedWorkIds
	}
	return nil
}

func (x *ResizeWorkersRequest) GetDeletingWorkIds() []string {
	if x != nil {
		return x.DeletingWorkIds
	}
	return nil
}

type ResizeWorkersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ResizeWorkersResponse) Reset() {
	*x = ResizeWorkersResponse{}
	mi := &file_services_rwc_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ResizeWorkersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeWorkersResponse) ProtoMessage() {}

func (x *ResizeWorkersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeWorkersResponse.ProtoReflect.Descriptor instead.
func (*ResizeWorkersResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{9}
}

type DeleteWorkersRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RwName        string                 `protobuf:"bytes,1,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace   string                 `protobuf:"bytes,2,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	WorkIds       []string               `protobuf:"bytes,3,rep,name=work_ids,json=workIds,proto3" json:"work_ids,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteWorkersRequest) Reset() {
	*x = DeleteWorkersRequest{}
	mi := &file_services_rwc_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteWorkersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkersRequest) ProtoMessage() {}

func (x *DeleteWorkersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkersRequest.ProtoReflect.Descriptor instead.
func (*DeleteWorkersRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteWorkersRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *DeleteWorkersRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

func (x *DeleteWorkersRequest) GetWorkIds() []string {
	if x != nil {
		return x.WorkIds
	}
	return nil
}

type DeleteWorkersResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteWorkersResponse) Reset() {
	*x = DeleteWorkersResponse{}
	mi := &file_services_rwc_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteWorkersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteWorkersResponse) ProtoMessage() {}

func (x *DeleteWorkersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteWorkersResponse.ProtoReflect.Descriptor instead.
func (*DeleteWorkersResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{11}
}

type DeleteSnapshotRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	RwName        string                 `protobuf:"bytes,1,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace   string                 `protobuf:"bytes,2,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	SnapshotId    int64                  `protobuf:"varint,3,opt,name=snapshot_id,json=snapshotId,proto3" json:"snapshot_id,omitempty"`
	RwVersion     string                 `protobuf:"bytes,4,opt,name=rw_version,json=rwVersion,proto3" json:"rw_version,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSnapshotRequest) Reset() {
	*x = DeleteSnapshotRequest{}
	mi := &file_services_rwc_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSnapshotRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSnapshotRequest) ProtoMessage() {}

func (x *DeleteSnapshotRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSnapshotRequest.ProtoReflect.Descriptor instead.
func (*DeleteSnapshotRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteSnapshotRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *DeleteSnapshotRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

func (x *DeleteSnapshotRequest) GetSnapshotId() int64 {
	if x != nil {
		return x.SnapshotId
	}
	return 0
}

func (x *DeleteSnapshotRequest) GetRwVersion() string {
	if x != nil {
		return x.RwVersion
	}
	return ""
}

type DeleteSnapshotResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSnapshotResponse) Reset() {
	*x = DeleteSnapshotResponse{}
	mi := &file_services_rwc_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSnapshotResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSnapshotResponse) ProtoMessage() {}

func (x *DeleteSnapshotResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSnapshotResponse.ProtoReflect.Descriptor instead.
func (*DeleteSnapshotResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{13}
}

type RestoreMetaRequestEtcd struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	EtcdEndpoints string                 `protobuf:"bytes,1,opt,name=etcd_endpoints,json=etcdEndpoints,proto3" json:"etcd_endpoints,omitempty"`
	// ignore if allowNoneAuthentication is enabled.
	EtcdAuth bool `protobuf:"varint,2,opt,name=etcd_auth,json=etcdAuth,proto3" json:"etcd_auth,omitempty"`
	// ignore if allowNoneAuthentication is enabled.
	EtcdUsername string `protobuf:"bytes,3,opt,name=etcd_username,json=etcdUsername,proto3" json:"etcd_username,omitempty"`
	// ignore if allowNoneAuthentication is enabled.
	EtcdPassword  string `protobuf:"bytes,4,opt,name=etcd_password,json=etcdPassword,proto3" json:"etcd_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreMetaRequestEtcd) Reset() {
	*x = RestoreMetaRequestEtcd{}
	mi := &file_services_rwc_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreMetaRequestEtcd) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreMetaRequestEtcd) ProtoMessage() {}

func (x *RestoreMetaRequestEtcd) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreMetaRequestEtcd.ProtoReflect.Descriptor instead.
func (*RestoreMetaRequestEtcd) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{14}
}

func (x *RestoreMetaRequestEtcd) GetEtcdEndpoints() string {
	if x != nil {
		return x.EtcdEndpoints
	}
	return ""
}

func (x *RestoreMetaRequestEtcd) GetEtcdAuth() bool {
	if x != nil {
		return x.EtcdAuth
	}
	return false
}

func (x *RestoreMetaRequestEtcd) GetEtcdUsername() string {
	if x != nil {
		return x.EtcdUsername
	}
	return ""
}

func (x *RestoreMetaRequestEtcd) GetEtcdPassword() string {
	if x != nil {
		return x.EtcdPassword
	}
	return ""
}

type RestoreMetaRequestSql struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	SqlEndpoint   string                 `protobuf:"bytes,1,opt,name=sql_endpoint,json=sqlEndpoint,proto3" json:"sql_endpoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreMetaRequestSql) Reset() {
	*x = RestoreMetaRequestSql{}
	mi := &file_services_rwc_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreMetaRequestSql) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreMetaRequestSql) ProtoMessage() {}

func (x *RestoreMetaRequestSql) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreMetaRequestSql.ProtoReflect.Descriptor instead.
func (*RestoreMetaRequestSql) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{15}
}

func (x *RestoreMetaRequestSql) GetSqlEndpoint() string {
	if x != nil {
		return x.SqlEndpoint
	}
	return ""
}

type RestoreMetaRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the name will be the name of the Job object
	// the namespace where the restore process will be hosted.
	// the namespace should be where the service account object is hosted.
	ResourceMeta *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// service_account is used to authenticate with the storage service (e.g. s3).
	ServiceAccount string `protobuf:"bytes,2,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	// rw_image_tag is for starting the restore process. this is needed to
	// guarantee the grpc payload is compatible.
	RwImageTag string `protobuf:"bytes,3,opt,name=rw_image_tag,json=rwImageTag,proto3" json:"rw_image_tag,omitempty"`
	// snapshot id in the kernel
	MetaSnapshotId int64 `protobuf:"varint,4,opt,name=meta_snapshot_id,json=metaSnapshotId,proto3" json:"meta_snapshot_id,omitempty"`
	// valid values: etcd, sql
	MetaStoreType     string `protobuf:"bytes,5,opt,name=meta_store_type,json=metaStoreType,proto3" json:"meta_store_type,omitempty"`
	BackupStorageUrl  string `protobuf:"bytes,6,opt,name=backup_storage_url,json=backupStorageUrl,proto3" json:"backup_storage_url,omitempty"`
	BackupStorageDir  string `protobuf:"bytes,7,opt,name=backup_storage_dir,json=backupStorageDir,proto3" json:"backup_storage_dir,omitempty"`
	HummockStorageUrl string `protobuf:"bytes,8,opt,name=hummock_storage_url,json=hummockStorageUrl,proto3" json:"hummock_storage_url,omitempty"`
	HummockStorageDir string `protobuf:"bytes,9,opt,name=hummock_storage_dir,json=hummockStorageDir,proto3" json:"hummock_storage_dir,omitempty"`
	// Types that are valid to be assigned to MetastoreConfig:
	//
	//	*RestoreMetaRequest_EtcdConfig
	//	*RestoreMetaRequest_SqlConfig
	MetastoreConfig isRestoreMetaRequest_MetastoreConfig `protobuf_oneof:"metastore_config"`
	Envs            map[string]string                    `protobuf:"bytes,14,rep,name=envs,proto3" json:"envs,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *RestoreMetaRequest) Reset() {
	*x = RestoreMetaRequest{}
	mi := &file_services_rwc_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreMetaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreMetaRequest) ProtoMessage() {}

func (x *RestoreMetaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreMetaRequest.ProtoReflect.Descriptor instead.
func (*RestoreMetaRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{16}
}

func (x *RestoreMetaRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *RestoreMetaRequest) GetServiceAccount() string {
	if x != nil {
		return x.ServiceAccount
	}
	return ""
}

func (x *RestoreMetaRequest) GetRwImageTag() string {
	if x != nil {
		return x.RwImageTag
	}
	return ""
}

func (x *RestoreMetaRequest) GetMetaSnapshotId() int64 {
	if x != nil {
		return x.MetaSnapshotId
	}
	return 0
}

func (x *RestoreMetaRequest) GetMetaStoreType() string {
	if x != nil {
		return x.MetaStoreType
	}
	return ""
}

func (x *RestoreMetaRequest) GetBackupStorageUrl() string {
	if x != nil {
		return x.BackupStorageUrl
	}
	return ""
}

func (x *RestoreMetaRequest) GetBackupStorageDir() string {
	if x != nil {
		return x.BackupStorageDir
	}
	return ""
}

func (x *RestoreMetaRequest) GetHummockStorageUrl() string {
	if x != nil {
		return x.HummockStorageUrl
	}
	return ""
}

func (x *RestoreMetaRequest) GetHummockStorageDir() string {
	if x != nil {
		return x.HummockStorageDir
	}
	return ""
}

func (x *RestoreMetaRequest) GetMetastoreConfig() isRestoreMetaRequest_MetastoreConfig {
	if x != nil {
		return x.MetastoreConfig
	}
	return nil
}

func (x *RestoreMetaRequest) GetEtcdConfig() *RestoreMetaRequestEtcd {
	if x != nil {
		if x, ok := x.MetastoreConfig.(*RestoreMetaRequest_EtcdConfig); ok {
			return x.EtcdConfig
		}
	}
	return nil
}

func (x *RestoreMetaRequest) GetSqlConfig() *RestoreMetaRequestSql {
	if x != nil {
		if x, ok := x.MetastoreConfig.(*RestoreMetaRequest_SqlConfig); ok {
			return x.SqlConfig
		}
	}
	return nil
}

func (x *RestoreMetaRequest) GetEnvs() map[string]string {
	if x != nil {
		return x.Envs
	}
	return nil
}

type isRestoreMetaRequest_MetastoreConfig interface {
	isRestoreMetaRequest_MetastoreConfig()
}

type RestoreMetaRequest_EtcdConfig struct {
	EtcdConfig *RestoreMetaRequestEtcd `protobuf:"bytes,10,opt,name=etcd_config,json=etcdConfig,proto3,oneof"`
}

type RestoreMetaRequest_SqlConfig struct {
	SqlConfig *RestoreMetaRequestSql `protobuf:"bytes,11,opt,name=sql_config,json=sqlConfig,proto3,oneof"`
}

func (*RestoreMetaRequest_EtcdConfig) isRestoreMetaRequest_MetastoreConfig() {}

func (*RestoreMetaRequest_SqlConfig) isRestoreMetaRequest_MetastoreConfig() {}

type RestoreMetaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestoreMetaResponse) Reset() {
	*x = RestoreMetaResponse{}
	mi := &file_services_rwc_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestoreMetaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestoreMetaResponse) ProtoMessage() {}

func (x *RestoreMetaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestoreMetaResponse.ProtoReflect.Descriptor instead.
func (*RestoreMetaResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{17}
}

func (x *RestoreMetaResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type VacuumEtcdMetaRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	PodName       string                 `protobuf:"bytes,1,opt,name=pod_name,json=podName,proto3" json:"pod_name,omitempty"`
	PodNamespace  string                 `protobuf:"bytes,2,opt,name=pod_namespace,json=podNamespace,proto3" json:"pod_namespace,omitempty"`
	EtcdUsername  string                 `protobuf:"bytes,3,opt,name=etcd_username,json=etcdUsername,proto3" json:"etcd_username,omitempty"`
	EtcdPassword  string                 `protobuf:"bytes,4,opt,name=etcd_password,json=etcdPassword,proto3" json:"etcd_password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VacuumEtcdMetaRequest) Reset() {
	*x = VacuumEtcdMetaRequest{}
	mi := &file_services_rwc_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VacuumEtcdMetaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VacuumEtcdMetaRequest) ProtoMessage() {}

func (x *VacuumEtcdMetaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VacuumEtcdMetaRequest.ProtoReflect.Descriptor instead.
func (*VacuumEtcdMetaRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{18}
}

func (x *VacuumEtcdMetaRequest) GetPodName() string {
	if x != nil {
		return x.PodName
	}
	return ""
}

func (x *VacuumEtcdMetaRequest) GetPodNamespace() string {
	if x != nil {
		return x.PodNamespace
	}
	return ""
}

func (x *VacuumEtcdMetaRequest) GetEtcdUsername() string {
	if x != nil {
		return x.EtcdUsername
	}
	return ""
}

func (x *VacuumEtcdMetaRequest) GetEtcdPassword() string {
	if x != nil {
		return x.EtcdPassword
	}
	return ""
}

type VacuumEtcdMetaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *VacuumEtcdMetaResponse) Reset() {
	*x = VacuumEtcdMetaResponse{}
	mi := &file_services_rwc_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *VacuumEtcdMetaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VacuumEtcdMetaResponse) ProtoMessage() {}

func (x *VacuumEtcdMetaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VacuumEtcdMetaResponse.ProtoReflect.Descriptor instead.
func (*VacuumEtcdMetaResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{19}
}

// RW Diagnosis Report
type GenDiagnosisReportRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Access the corresponding RW tenant via static service name + tenant
	// namespace
	ServiceName   string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	Namespace     string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenDiagnosisReportRequest) Reset() {
	*x = GenDiagnosisReportRequest{}
	mi := &file_services_rwc_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenDiagnosisReportRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenDiagnosisReportRequest) ProtoMessage() {}

func (x *GenDiagnosisReportRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenDiagnosisReportRequest.ProtoReflect.Descriptor instead.
func (*GenDiagnosisReportRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{20}
}

func (x *GenDiagnosisReportRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GenDiagnosisReportRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type GenDiagnosisReportResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Report        string                 `protobuf:"bytes,1,opt,name=report,proto3" json:"report,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenDiagnosisReportResponse) Reset() {
	*x = GenDiagnosisReportResponse{}
	mi := &file_services_rwc_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenDiagnosisReportResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenDiagnosisReportResponse) ProtoMessage() {}

func (x *GenDiagnosisReportResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenDiagnosisReportResponse.ProtoReflect.Descriptor instead.
func (*GenDiagnosisReportResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{21}
}

func (x *GenDiagnosisReportResponse) GetReport() string {
	if x != nil {
		return x.Report
	}
	return ""
}

type GenDiagnosisReportStreamRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Access the corresponding RW tenant via static service name + tenant
	// namespace
	Namespace      string `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	ServiceName    string `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	GzipCompressed bool   `protobuf:"varint,3,opt,name=gzip_compressed,json=gzipCompressed,proto3" json:"gzip_compressed,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GenDiagnosisReportStreamRequest) Reset() {
	*x = GenDiagnosisReportStreamRequest{}
	mi := &file_services_rwc_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenDiagnosisReportStreamRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenDiagnosisReportStreamRequest) ProtoMessage() {}

func (x *GenDiagnosisReportStreamRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenDiagnosisReportStreamRequest.ProtoReflect.Descriptor instead.
func (*GenDiagnosisReportStreamRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{22}
}

func (x *GenDiagnosisReportStreamRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *GenDiagnosisReportStreamRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *GenDiagnosisReportStreamRequest) GetGzipCompressed() bool {
	if x != nil {
		return x.GzipCompressed
	}
	return false
}

type GenDiagnosisReportStreamResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReportChunk   []byte                 `protobuf:"bytes,1,opt,name=report_chunk,json=reportChunk,proto3" json:"report_chunk,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GenDiagnosisReportStreamResponse) Reset() {
	*x = GenDiagnosisReportStreamResponse{}
	mi := &file_services_rwc_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GenDiagnosisReportStreamResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GenDiagnosisReportStreamResponse) ProtoMessage() {}

func (x *GenDiagnosisReportStreamResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GenDiagnosisReportStreamResponse.ProtoReflect.Descriptor instead.
func (*GenDiagnosisReportStreamResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{23}
}

func (x *GenDiagnosisReportStreamResponse) GetReportChunk() []byte {
	if x != nil {
		return x.ReportChunk
	}
	return nil
}

type MetaMigrationRequest struct {
	state           protoimpl.MessageState    `protogen:"open.v1"`
	ResourceMeta    *resource.Meta            `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	RwName          string                    `protobuf:"bytes,2,opt,name=rw_name,json=rwName,proto3" json:"rw_name,omitempty"`
	RwNamespace     string                    `protobuf:"bytes,3,opt,name=rw_namespace,json=rwNamespace,proto3" json:"rw_namespace,omitempty"`
	EtcdEndpoints   string                    `protobuf:"bytes,4,opt,name=etcd_endpoints,json=etcdEndpoints,proto3" json:"etcd_endpoints,omitempty"`
	SqlEndpoint     string                    `protobuf:"bytes,5,opt,name=sql_endpoint,json=sqlEndpoint,proto3" json:"sql_endpoint,omitempty"`
	TaskImage       string                    `protobuf:"bytes,6,opt,name=task_image,json=taskImage,proto3" json:"task_image,omitempty"`
	TaskResources   *k8s.ResourceRequirements `protobuf:"bytes,7,opt,name=task_resources,json=taskResources,proto3" json:"task_resources,omitempty"`
	TaskTolerations []*k8s.Toleration         `protobuf:"bytes,8,rep,name=task_tolerations,json=taskTolerations,proto3" json:"task_tolerations,omitempty"`
	TaskAffinity    *k8s.Affinity             `protobuf:"bytes,9,opt,name=task_affinity,json=taskAffinity,proto3" json:"task_affinity,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *MetaMigrationRequest) Reset() {
	*x = MetaMigrationRequest{}
	mi := &file_services_rwc_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaMigrationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaMigrationRequest) ProtoMessage() {}

func (x *MetaMigrationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaMigrationRequest.ProtoReflect.Descriptor instead.
func (*MetaMigrationRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{24}
}

func (x *MetaMigrationRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *MetaMigrationRequest) GetRwName() string {
	if x != nil {
		return x.RwName
	}
	return ""
}

func (x *MetaMigrationRequest) GetRwNamespace() string {
	if x != nil {
		return x.RwNamespace
	}
	return ""
}

func (x *MetaMigrationRequest) GetEtcdEndpoints() string {
	if x != nil {
		return x.EtcdEndpoints
	}
	return ""
}

func (x *MetaMigrationRequest) GetSqlEndpoint() string {
	if x != nil {
		return x.SqlEndpoint
	}
	return ""
}

func (x *MetaMigrationRequest) GetTaskImage() string {
	if x != nil {
		return x.TaskImage
	}
	return ""
}

func (x *MetaMigrationRequest) GetTaskResources() *k8s.ResourceRequirements {
	if x != nil {
		return x.TaskResources
	}
	return nil
}

func (x *MetaMigrationRequest) GetTaskTolerations() []*k8s.Toleration {
	if x != nil {
		return x.TaskTolerations
	}
	return nil
}

func (x *MetaMigrationRequest) GetTaskAffinity() *k8s.Affinity {
	if x != nil {
		return x.TaskAffinity
	}
	return nil
}

type MetaMigrationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MetaMigrationResponse) Reset() {
	*x = MetaMigrationResponse{}
	mi := &file_services_rwc_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MetaMigrationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MetaMigrationResponse) ProtoMessage() {}

func (x *MetaMigrationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MetaMigrationResponse.ProtoReflect.Descriptor instead.
func (*MetaMigrationResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{25}
}

func (x *MetaMigrationResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type KafkaConfig struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Server           string                 `protobuf:"bytes,1,opt,name=server,proto3" json:"server,omitempty"`
	SecurityProtocol *KafkaSecurityProtocol `protobuf:"varint,2,opt,name=security_protocol,json=securityProtocol,proto3,enum=services.rwc.KafkaSecurityProtocol,oneof" json:"security_protocol,omitempty"`
	SaslMechanism    *KafkaSaslMechanism    `protobuf:"varint,3,opt,name=sasl_mechanism,json=saslMechanism,proto3,enum=services.rwc.KafkaSaslMechanism,oneof" json:"sasl_mechanism,omitempty"`
	SaslUsername     *string                `protobuf:"bytes,4,opt,name=sasl_username,json=saslUsername,proto3,oneof" json:"sasl_username,omitempty"`
	SaslPassword     *string                `protobuf:"bytes,5,opt,name=sasl_password,json=saslPassword,proto3,oneof" json:"sasl_password,omitempty"`
	CaCertificate    *string                `protobuf:"bytes,6,opt,name=ca_certificate,json=caCertificate,proto3,oneof" json:"ca_certificate,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *KafkaConfig) Reset() {
	*x = KafkaConfig{}
	mi := &file_services_rwc_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *KafkaConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*KafkaConfig) ProtoMessage() {}

func (x *KafkaConfig) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use KafkaConfig.ProtoReflect.Descriptor instead.
func (*KafkaConfig) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{26}
}

func (x *KafkaConfig) GetServer() string {
	if x != nil {
		return x.Server
	}
	return ""
}

func (x *KafkaConfig) GetSecurityProtocol() KafkaSecurityProtocol {
	if x != nil && x.SecurityProtocol != nil {
		return *x.SecurityProtocol
	}
	return KafkaSecurityProtocol_PROTOCOL_UNSPECIFIED
}

func (x *KafkaConfig) GetSaslMechanism() KafkaSaslMechanism {
	if x != nil && x.SaslMechanism != nil {
		return *x.SaslMechanism
	}
	return KafkaSaslMechanism_MECHANISM_UNSPECIFIED
}

func (x *KafkaConfig) GetSaslUsername() string {
	if x != nil && x.SaslUsername != nil {
		return *x.SaslUsername
	}
	return ""
}

func (x *KafkaConfig) GetSaslPassword() string {
	if x != nil && x.SaslPassword != nil {
		return *x.SaslPassword
	}
	return ""
}

func (x *KafkaConfig) GetCaCertificate() string {
	if x != nil && x.CaCertificate != nil {
		return *x.CaCertificate
	}
	return ""
}

type FetchKafkaTopicRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kafka         *KafkaConfig           `protobuf:"bytes,1,opt,name=kafka,proto3" json:"kafka,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchKafkaTopicRequest) Reset() {
	*x = FetchKafkaTopicRequest{}
	mi := &file_services_rwc_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchKafkaTopicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchKafkaTopicRequest) ProtoMessage() {}

func (x *FetchKafkaTopicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchKafkaTopicRequest.ProtoReflect.Descriptor instead.
func (*FetchKafkaTopicRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{27}
}

func (x *FetchKafkaTopicRequest) GetKafka() *KafkaConfig {
	if x != nil {
		return x.Kafka
	}
	return nil
}

type FetchKafkaTopicResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topics        []string               `protobuf:"bytes,1,rep,name=topics,proto3" json:"topics,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchKafkaTopicResponse) Reset() {
	*x = FetchKafkaTopicResponse{}
	mi := &file_services_rwc_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchKafkaTopicResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchKafkaTopicResponse) ProtoMessage() {}

func (x *FetchKafkaTopicResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchKafkaTopicResponse.ProtoReflect.Descriptor instead.
func (*FetchKafkaTopicResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{28}
}

func (x *FetchKafkaTopicResponse) GetTopics() []string {
	if x != nil {
		return x.Topics
	}
	return nil
}

type FetchKafkaMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Kafka         *KafkaConfig           `protobuf:"bytes,1,opt,name=kafka,proto3" json:"kafka,omitempty"`
	Topic         string                 `protobuf:"bytes,2,opt,name=topic,proto3" json:"topic,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchKafkaMessageRequest) Reset() {
	*x = FetchKafkaMessageRequest{}
	mi := &file_services_rwc_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchKafkaMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchKafkaMessageRequest) ProtoMessage() {}

func (x *FetchKafkaMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchKafkaMessageRequest.ProtoReflect.Descriptor instead.
func (*FetchKafkaMessageRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{29}
}

func (x *FetchKafkaMessageRequest) GetKafka() *KafkaConfig {
	if x != nil {
		return x.Kafka
	}
	return nil
}

func (x *FetchKafkaMessageRequest) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

type FetchKafkaMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Key           []byte                 `protobuf:"bytes,1,opt,name=key,proto3" json:"key,omitempty"`
	Value         []byte                 `protobuf:"bytes,2,opt,name=value,proto3" json:"value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchKafkaMessageResponse) Reset() {
	*x = FetchKafkaMessageResponse{}
	mi := &file_services_rwc_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchKafkaMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchKafkaMessageResponse) ProtoMessage() {}

func (x *FetchKafkaMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchKafkaMessageResponse.ProtoReflect.Descriptor instead.
func (*FetchKafkaMessageResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{30}
}

func (x *FetchKafkaMessageResponse) GetKey() []byte {
	if x != nil {
		return x.Key
	}
	return nil
}

func (x *FetchKafkaMessageResponse) GetValue() []byte {
	if x != nil {
		return x.Value
	}
	return nil
}

type PostgresConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Hostname      string                 `protobuf:"bytes,1,opt,name=hostname,proto3" json:"hostname,omitempty"`
	Port          int32                  `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	Username      string                 `protobuf:"bytes,3,opt,name=username,proto3" json:"username,omitempty"`
	Password      string                 `protobuf:"bytes,4,opt,name=password,proto3" json:"password,omitempty"`
	Database      string                 `protobuf:"bytes,5,opt,name=database,proto3" json:"database,omitempty"`
	SslMode       PostgresSslMode        `protobuf:"varint,6,opt,name=ssl_mode,json=sslMode,proto3,enum=services.rwc.PostgresSslMode" json:"ssl_mode,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PostgresConfig) Reset() {
	*x = PostgresConfig{}
	mi := &file_services_rwc_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PostgresConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PostgresConfig) ProtoMessage() {}

func (x *PostgresConfig) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PostgresConfig.ProtoReflect.Descriptor instead.
func (*PostgresConfig) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{31}
}

func (x *PostgresConfig) GetHostname() string {
	if x != nil {
		return x.Hostname
	}
	return ""
}

func (x *PostgresConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *PostgresConfig) GetUsername() string {
	if x != nil {
		return x.Username
	}
	return ""
}

func (x *PostgresConfig) GetPassword() string {
	if x != nil {
		return x.Password
	}
	return ""
}

func (x *PostgresConfig) GetDatabase() string {
	if x != nil {
		return x.Database
	}
	return ""
}

func (x *PostgresConfig) GetSslMode() PostgresSslMode {
	if x != nil {
		return x.SslMode
	}
	return PostgresSslMode_SSL_MODE_UNSPECIFIED
}

type FetchPostgresTableRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Postgres      *PostgresConfig        `protobuf:"bytes,1,opt,name=postgres,proto3" json:"postgres,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchPostgresTableRequest) Reset() {
	*x = FetchPostgresTableRequest{}
	mi := &file_services_rwc_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchPostgresTableRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchPostgresTableRequest) ProtoMessage() {}

func (x *FetchPostgresTableRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchPostgresTableRequest.ProtoReflect.Descriptor instead.
func (*FetchPostgresTableRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{32}
}

func (x *FetchPostgresTableRequest) GetPostgres() *PostgresConfig {
	if x != nil {
		return x.Postgres
	}
	return nil
}

type FetchPostgresTableResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Tables        []string               `protobuf:"bytes,1,rep,name=tables,proto3" json:"tables,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchPostgresTableResponse) Reset() {
	*x = FetchPostgresTableResponse{}
	mi := &file_services_rwc_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchPostgresTableResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchPostgresTableResponse) ProtoMessage() {}

func (x *FetchPostgresTableResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchPostgresTableResponse.ProtoReflect.Descriptor instead.
func (*FetchPostgresTableResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{33}
}

func (x *FetchPostgresTableResponse) GetTables() []string {
	if x != nil {
		return x.Tables
	}
	return nil
}

type SchemaSchemaRegistryConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Topic         string                 `protobuf:"bytes,1,opt,name=topic,proto3" json:"topic,omitempty"`
	Username      *string                `protobuf:"bytes,2,opt,name=username,proto3,oneof" json:"username,omitempty"`
	Password      *string                `protobuf:"bytes,3,opt,name=password,proto3,oneof" json:"password,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SchemaSchemaRegistryConfig) Reset() {
	*x = SchemaSchemaRegistryConfig{}
	mi := &file_services_rwc_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaSchemaRegistryConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaSchemaRegistryConfig) ProtoMessage() {}

func (x *SchemaSchemaRegistryConfig) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaSchemaRegistryConfig.ProtoReflect.Descriptor instead.
func (*SchemaSchemaRegistryConfig) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{34}
}

func (x *SchemaSchemaRegistryConfig) GetTopic() string {
	if x != nil {
		return x.Topic
	}
	return ""
}

func (x *SchemaSchemaRegistryConfig) GetUsername() string {
	if x != nil && x.Username != nil {
		return *x.Username
	}
	return ""
}

func (x *SchemaSchemaRegistryConfig) GetPassword() string {
	if x != nil && x.Password != nil {
		return *x.Password
	}
	return ""
}

type SchemaS3Config struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Region          string                 `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	AccessKeyId     *string                `protobuf:"bytes,2,opt,name=accessKeyId,proto3,oneof" json:"accessKeyId,omitempty"`
	SecretAccessKey *string                `protobuf:"bytes,3,opt,name=secretAccessKey,proto3,oneof" json:"secretAccessKey,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SchemaS3Config) Reset() {
	*x = SchemaS3Config{}
	mi := &file_services_rwc_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SchemaS3Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SchemaS3Config) ProtoMessage() {}

func (x *SchemaS3Config) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SchemaS3Config.ProtoReflect.Descriptor instead.
func (*SchemaS3Config) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{35}
}

func (x *SchemaS3Config) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *SchemaS3Config) GetAccessKeyId() string {
	if x != nil && x.AccessKeyId != nil {
		return *x.AccessKeyId
	}
	return ""
}

func (x *SchemaS3Config) GetSecretAccessKey() string {
	if x != nil && x.SecretAccessKey != nil {
		return *x.SecretAccessKey
	}
	return ""
}

type FetchSourceSchemaRequest struct {
	state          protoimpl.MessageState      `protogen:"open.v1"`
	Format         SchemaFormat                `protobuf:"varint,1,opt,name=format,proto3,enum=services.rwc.SchemaFormat" json:"format,omitempty"`
	Location       SchemaLocation              `protobuf:"varint,2,opt,name=location,proto3,enum=services.rwc.SchemaLocation" json:"location,omitempty"`
	Url            string                      `protobuf:"bytes,3,opt,name=url,proto3" json:"url,omitempty"`
	SchemaRegistry *SchemaSchemaRegistryConfig `protobuf:"bytes,4,opt,name=schema_registry,json=schemaRegistry,proto3,oneof" json:"schema_registry,omitempty"`
	S3             *SchemaS3Config             `protobuf:"bytes,5,opt,name=s3,proto3,oneof" json:"s3,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *FetchSourceSchemaRequest) Reset() {
	*x = FetchSourceSchemaRequest{}
	mi := &file_services_rwc_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchSourceSchemaRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchSourceSchemaRequest) ProtoMessage() {}

func (x *FetchSourceSchemaRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchSourceSchemaRequest.ProtoReflect.Descriptor instead.
func (*FetchSourceSchemaRequest) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{36}
}

func (x *FetchSourceSchemaRequest) GetFormat() SchemaFormat {
	if x != nil {
		return x.Format
	}
	return SchemaFormat_FORMAT_UNSPECIFIED
}

func (x *FetchSourceSchemaRequest) GetLocation() SchemaLocation {
	if x != nil {
		return x.Location
	}
	return SchemaLocation_LOCATION_UNSPECIFIED
}

func (x *FetchSourceSchemaRequest) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FetchSourceSchemaRequest) GetSchemaRegistry() *SchemaSchemaRegistryConfig {
	if x != nil {
		return x.SchemaRegistry
	}
	return nil
}

func (x *FetchSourceSchemaRequest) GetS3() *SchemaS3Config {
	if x != nil {
		return x.S3
	}
	return nil
}

type FetchSourceSchemaResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Files         []*RawSchemaFile       `protobuf:"bytes,1,rep,name=files,proto3" json:"files,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FetchSourceSchemaResponse) Reset() {
	*x = FetchSourceSchemaResponse{}
	mi := &file_services_rwc_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FetchSourceSchemaResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FetchSourceSchemaResponse) ProtoMessage() {}

func (x *FetchSourceSchemaResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FetchSourceSchemaResponse.ProtoReflect.Descriptor instead.
func (*FetchSourceSchemaResponse) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{37}
}

func (x *FetchSourceSchemaResponse) GetFiles() []*RawSchemaFile {
	if x != nil {
		return x.Files
	}
	return nil
}

type RawSchemaFile struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Content       []byte                 `protobuf:"bytes,1,opt,name=content,proto3" json:"content,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RawSchemaFile) Reset() {
	*x = RawSchemaFile{}
	mi := &file_services_rwc_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RawSchemaFile) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RawSchemaFile) ProtoMessage() {}

func (x *RawSchemaFile) ProtoReflect() protoreflect.Message {
	mi := &file_services_rwc_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RawSchemaFile.ProtoReflect.Descriptor instead.
func (*RawSchemaFile) Descriptor() ([]byte, []int) {
	return file_services_rwc_proto_rawDescGZIP(), []int{38}
}

func (x *RawSchemaFile) GetContent() []byte {
	if x != nil {
		return x.Content
	}
	return nil
}

func (x *RawSchemaFile) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

var File_services_rwc_proto protoreflect.FileDescriptor

var file_services_rwc_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x72, 0x77, 0x63, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72,
	0x77, 0x63, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x8f, 0x01, 0x0a, 0x15, 0x4d, 0x65, 0x74, 0x61, 0x4e, 0x6f, 0x64, 0x65, 0x42,
	0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x22, 0x52, 0x0a, 0x16, 0x4d, 0x65, 0x74, 0x61, 0x4e, 0x6f, 0x64, 0x65,
	0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x69, 0x0a, 0x15, 0x56, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x77,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x14, 0x0a,
	0x05, 0x70, 0x72, 0x6f, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x72,
	0x6f, 0x70, 0x73, 0x22, 0x3d, 0x0a, 0x16, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x22, 0x53, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72,
	0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x77,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x22, 0x30, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x6f, 0x75, 0x74, 0x70, 0x75, 0x74, 0x22, 0x6d, 0x0a, 0x14, 0x43, 0x6f, 0x72,
	0x64, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x77,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52,
	0x07, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x43, 0x6f, 0x72, 0x64,
	0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xa4, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x57, 0x6f, 0x72, 0x6b,
	0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x61, 0x64, 0x64, 0x65, 0x64, 0x5f,
	0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0c,
	0x61, 0x64, 0x64, 0x65, 0x64, 0x57, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x73, 0x12, 0x2a, 0x0a, 0x11,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6e, 0x67, 0x5f, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6e,
	0x67, 0x57, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x73, 0x22, 0x17, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x6d, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x07, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x64, 0x73,
	0x22, 0x17, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x15, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x1f, 0x0a, 0x0b, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x0a, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x49, 0x64,
	0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x77, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x72, 0x77, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x22,
	0x18, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x16, 0x52, 0x65,
	0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x45, 0x74, 0x63, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x65, 0x74, 0x63, 0x64, 0x5f, 0x65, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x74,
	0x63, 0x64, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x65,
	0x74, 0x63, 0x64, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x08,
	0x65, 0x74, 0x63, 0x64, 0x41, 0x75, 0x74, 0x68, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x74, 0x63, 0x64,
	0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x65, 0x74, 0x63, 0x64, 0x55, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a,
	0x0d, 0x65, 0x74, 0x63, 0x64, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x74, 0x63, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f,
	0x72, 0x64, 0x22, 0x3a, 0x0a, 0x15, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x71, 0x6c, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x71, 0x6c, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x71, 0x6c, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0xc5,
	0x05, 0x0a, 0x12, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x20, 0x0a, 0x0c, 0x72, 0x77,
	0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x72, 0x77, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x12, 0x28, 0x0a, 0x10,
	0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0e, 0x6d, 0x65, 0x74, 0x61, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x49, 0x64, 0x12, 0x26, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x6d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x2c,
	0x0a, 0x12, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65,
	0x5f, 0x75, 0x72, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b,
	0x75, 0x70, 0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2c, 0x0a, 0x12,
	0x62, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x64,
	0x69, 0x72, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x62, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x69, 0x72, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x75,
	0x6d, 0x6d, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x75, 0x72,
	0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x68, 0x75, 0x6d, 0x6d, 0x6f, 0x63, 0x6b,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x55, 0x72, 0x6c, 0x12, 0x2e, 0x0a, 0x13, 0x68, 0x75,
	0x6d, 0x6d, 0x6f, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x64, 0x69,
	0x72, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x68, 0x75, 0x6d, 0x6d, 0x6f, 0x63, 0x6b,
	0x53, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x44, 0x69, 0x72, 0x12, 0x47, 0x0a, 0x0b, 0x65, 0x74,
	0x63, 0x64, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x45, 0x74, 0x63, 0x64, 0x48, 0x00, 0x52, 0x0a, 0x65, 0x74, 0x63, 0x64, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x44, 0x0a, 0x0a, 0x73, 0x71, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x53, 0x71, 0x6c, 0x48, 0x00, 0x52, 0x09,
	0x73, 0x71, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x3e, 0x0a, 0x04, 0x65, 0x6e, 0x76,
	0x73, 0x18, 0x0e, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45, 0x6e, 0x76, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x1a, 0x37, 0x0a, 0x09, 0x45, 0x6e, 0x76,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x42, 0x12, 0x0a, 0x10, 0x6d, 0x65, 0x74, 0x61, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x4f, 0x0a, 0x13, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x15, 0x56, 0x61, 0x63, 0x75,
	0x75, 0x6d, 0x45, 0x74, 0x63, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x19, 0x0a, 0x08, 0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d,
	0x70, 0x6f, 0x64, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x70, 0x6f, 0x64, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x74, 0x63, 0x64, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x74, 0x63, 0x64, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x74, 0x63, 0x64, 0x5f, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65,
	0x74, 0x63, 0x64, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x22, 0x18, 0x0a, 0x16, 0x56,
	0x61, 0x63, 0x75, 0x75, 0x6d, 0x45, 0x74, 0x63, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x5c, 0x0a, 0x19, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x22, 0x34, 0x0a, 0x1a, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x22, 0x8b, 0x01, 0x0a, 0x1f, 0x47, 0x65,
	0x6e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x27,
	0x0a, 0x0f, 0x67, 0x7a, 0x69, 0x70, 0x5f, 0x63, 0x6f, 0x6d, 0x70, 0x72, 0x65, 0x73, 0x73, 0x65,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0e, 0x67, 0x7a, 0x69, 0x70, 0x43, 0x6f, 0x6d,
	0x70, 0x72, 0x65, 0x73, 0x73, 0x65, 0x64, 0x22, 0x45, 0x0a, 0x20, 0x47, 0x65, 0x6e, 0x44, 0x69,
	0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72,
	0x65, 0x61, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x72,
	0x65, 0x70, 0x6f, 0x72, 0x74, 0x5f, 0x63, 0x68, 0x75, 0x6e, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0c, 0x52, 0x0b, 0x72, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x43, 0x68, 0x75, 0x6e, 0x6b, 0x22, 0xbe,
	0x03, 0x0a, 0x14, 0x4d, 0x65, 0x74, 0x61, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x17, 0x0a, 0x07, 0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c,
	0x72, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x72, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12,
	0x25, 0x0a, 0x0e, 0x65, 0x74, 0x63, 0x64, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x74, 0x63, 0x64, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x71, 0x6c, 0x5f, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x71,
	0x6c, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x73,
	0x6b, 0x5f, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x47, 0x0a, 0x0e, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x69, 0x72, 0x65, 0x6d, 0x65, 0x6e,
	0x74, 0x73, 0x52, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x12, 0x41, 0x0a, 0x10, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x0f, 0x74, 0x61, 0x73, 0x6b, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x12, 0x39, 0x0a, 0x0d, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x61, 0x66, 0x66,
	0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x79, 0x52, 0x0c, 0x74, 0x61, 0x73, 0x6b, 0x41, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x22,
	0x51, 0x0a, 0x15, 0x4d, 0x65, 0x74, 0x61, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0xaa, 0x03, 0x0a, 0x0b, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x73, 0x65, 0x72, 0x76, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x11, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x72, 0x77, 0x63, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x48, 0x00, 0x52, 0x10, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x88, 0x01,
	0x01, 0x12, 0x4c, 0x0a, 0x0e, 0x73, 0x61, 0x73, 0x6c, 0x5f, 0x6d, 0x65, 0x63, 0x68, 0x61, 0x6e,
	0x69, 0x73, 0x6d, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x53, 0x61,
	0x73, 0x6c, 0x4d, 0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x48, 0x01, 0x52, 0x0d, 0x73,
	0x61, 0x73, 0x6c, 0x4d, 0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x88, 0x01, 0x01, 0x12,
	0x28, 0x0a, 0x0d, 0x73, 0x61, 0x73, 0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x48, 0x02, 0x52, 0x0c, 0x73, 0x61, 0x73, 0x6c, 0x55, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x28, 0x0a, 0x0d, 0x73, 0x61, 0x73,
	0x6c, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x48, 0x03, 0x52, 0x0c, 0x73, 0x61, 0x73, 0x6c, 0x50, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x88, 0x01, 0x01, 0x12, 0x2a, 0x0a, 0x0e, 0x63, 0x61, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66,
	0x69, 0x63, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x48, 0x04, 0x52, 0x0d, 0x63,
	0x61, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x88, 0x01, 0x01, 0x42,
	0x14, 0x0a, 0x12, 0x5f, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x42, 0x11, 0x0a, 0x0f, 0x5f, 0x73, 0x61, 0x73, 0x6c, 0x5f, 0x6d,
	0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73, 0x61, 0x73,
	0x6c, 0x5f, 0x75, 0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x10, 0x0a, 0x0e, 0x5f, 0x73,
	0x61, 0x73, 0x6c, 0x5f, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x42, 0x11, 0x0a, 0x0f,
	0x5f, 0x63, 0x61, 0x5f, 0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x22,
	0x49, 0x0a, 0x16, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x54, 0x6f, 0x70,
	0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x6b, 0x61, 0x66,
	0x6b, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x05, 0x6b, 0x61, 0x66, 0x6b, 0x61, 0x22, 0x31, 0x0a, 0x17, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x73, 0x22, 0x61, 0x0a,
	0x18, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61,
	0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2f, 0x0a, 0x05, 0x6b, 0x61, 0x66,
	0x6b, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x05, 0x6b, 0x61, 0x66, 0x6b, 0x61, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f,
	0x70, 0x69, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63,
	0x22, 0x43, 0x0a, 0x19, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xce, 0x01, 0x0a, 0x0e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72,
	0x65, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1a, 0x0a, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64,
	0x12, 0x1a, 0x0a, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x64, 0x61, 0x74, 0x61, 0x62, 0x61, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x08,
	0x73, 0x73, 0x6c, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1d,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x50, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x53, 0x73, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x52, 0x07, 0x73,
	0x73, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x22, 0x55, 0x0a, 0x19, 0x46, 0x65, 0x74, 0x63, 0x68, 0x50,
	0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x72, 0x77, 0x63, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x08, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x22, 0x34, 0x0a,
	0x1a, 0x46, 0x65, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x54, 0x61,
	0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x74,
	0x61, 0x62, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x62,
	0x6c, 0x65, 0x73, 0x22, 0x8e, 0x01, 0x0a, 0x1a, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x74, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x1f, 0x0a, 0x08, 0x75, 0x73, 0x65, 0x72,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x08, 0x75, 0x73,
	0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x1f, 0x0a, 0x08, 0x70, 0x61, 0x73,
	0x73, 0x77, 0x6f, 0x72, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x08, 0x70,
	0x61, 0x73, 0x73, 0x77, 0x6f, 0x72, 0x64, 0x88, 0x01, 0x01, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x75,
	0x73, 0x65, 0x72, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x70, 0x61, 0x73, 0x73,
	0x77, 0x6f, 0x72, 0x64, 0x22, 0xa2, 0x01, 0x0a, 0x0e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x53,
	0x33, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f,
	0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12,
	0x25, 0x0a, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65,
	0x79, 0x49, 0x64, 0x88, 0x01, 0x01, 0x12, 0x2d, 0x0a, 0x0f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x01, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b,
	0x65, 0x79, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x4b, 0x65, 0x79, 0x49, 0x64, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b, 0x65, 0x79, 0x22, 0xc0, 0x02, 0x0a, 0x18, 0x46, 0x65,
	0x74, 0x63, 0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x32, 0x0a, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x46, 0x6f, 0x72, 0x6d,
	0x61, 0x74, 0x52, 0x06, 0x66, 0x6f, 0x72, 0x6d, 0x61, 0x74, 0x12, 0x38, 0x0a, 0x08, 0x6c, 0x6f,
	0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x53, 0x63, 0x68, 0x65,
	0x6d, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x75, 0x72, 0x6c, 0x12, 0x56, 0x0a, 0x0f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x5f, 0x72, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x53,
	0x63, 0x68, 0x65, 0x6d, 0x61, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x67, 0x69, 0x73,
	0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x52, 0x65, 0x67, 0x69, 0x73, 0x74, 0x72, 0x79, 0x88, 0x01, 0x01, 0x12, 0x31,
	0x0a, 0x02, 0x73, 0x33, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61,
	0x53, 0x33, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x01, 0x52, 0x02, 0x73, 0x33, 0x88, 0x01,
	0x01, 0x42, 0x12, 0x0a, 0x10, 0x5f, 0x73, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x5f, 0x72, 0x65, 0x67,
	0x69, 0x73, 0x74, 0x72, 0x79, 0x42, 0x05, 0x0a, 0x03, 0x5f, 0x73, 0x33, 0x22, 0x4e, 0x0a, 0x19,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x31, 0x0a, 0x05, 0x66, 0x69, 0x6c,
	0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52, 0x61, 0x77, 0x53, 0x63, 0x68, 0x65, 0x6d,
	0x61, 0x46, 0x69, 0x6c, 0x65, 0x52, 0x05, 0x66, 0x69, 0x6c, 0x65, 0x73, 0x22, 0x3d, 0x0a, 0x0d,
	0x52, 0x61, 0x77, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x46, 0x69, 0x6c, 0x65, 0x12, 0x18, 0x0a,
	0x07, 0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0c, 0x52, 0x07,
	0x63, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x2a, 0x5c, 0x0a, 0x15, 0x4b,
	0x61, 0x66, 0x6b, 0x61, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x43, 0x4f, 0x4c,
	0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x12,
	0x0a, 0x0e, 0x53, 0x41, 0x53, 0x4c, 0x5f, 0x50, 0x4c, 0x41, 0x49, 0x4e, 0x54, 0x45, 0x58, 0x54,
	0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x41, 0x53, 0x4c, 0x5f, 0x53, 0x53, 0x4c, 0x10, 0x02,
	0x12, 0x07, 0x0a, 0x03, 0x53, 0x53, 0x4c, 0x10, 0x03, 0x2a, 0x60, 0x0a, 0x12, 0x4b, 0x61, 0x66,
	0x6b, 0x61, 0x53, 0x61, 0x73, 0x6c, 0x4d, 0x65, 0x63, 0x68, 0x61, 0x6e, 0x69, 0x73, 0x6d, 0x12,
	0x19, 0x0a, 0x15, 0x4d, 0x45, 0x43, 0x48, 0x41, 0x4e, 0x49, 0x53, 0x4d, 0x5f, 0x55, 0x4e, 0x53,
	0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x50, 0x4c,
	0x41, 0x49, 0x4e, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x41, 0x4d, 0x5f, 0x53,
	0x48, 0x41, 0x5f, 0x32, 0x35, 0x36, 0x10, 0x02, 0x12, 0x11, 0x0a, 0x0d, 0x53, 0x43, 0x52, 0x41,
	0x4d, 0x5f, 0x53, 0x48, 0x41, 0x5f, 0x35, 0x31, 0x32, 0x10, 0x03, 0x2a, 0x76, 0x0a, 0x0f, 0x50,
	0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x53, 0x73, 0x6c, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x18,
	0x0a, 0x14, 0x53, 0x53, 0x4c, 0x5f, 0x4d, 0x4f, 0x44, 0x45, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45,
	0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x49, 0x53, 0x41,
	0x42, 0x4c, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x45, 0x46, 0x45, 0x52,
	0x52, 0x45, 0x44, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x51, 0x55, 0x49, 0x52, 0x45,
	0x44, 0x10, 0x03, 0x12, 0x0d, 0x0a, 0x09, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x43, 0x41,
	0x10, 0x04, 0x12, 0x0f, 0x0a, 0x0b, 0x56, 0x45, 0x52, 0x49, 0x46, 0x59, 0x5f, 0x46, 0x55, 0x4c,
	0x4c, 0x10, 0x05, 0x2a, 0x48, 0x0a, 0x0c, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x46, 0x6f, 0x72,
	0x6d, 0x61, 0x74, 0x12, 0x16, 0x0a, 0x12, 0x46, 0x4f, 0x52, 0x4d, 0x41, 0x54, 0x5f, 0x55, 0x4e,
	0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x41,
	0x56, 0x52, 0x4f, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x50, 0x52, 0x4f, 0x54, 0x4f, 0x42, 0x55,
	0x46, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4a, 0x53, 0x4f, 0x4e, 0x10, 0x03, 0x2a, 0x59, 0x0a,
	0x0e, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x4c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x18, 0x0a, 0x14, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x5f, 0x55, 0x4e, 0x53, 0x50,
	0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x10, 0x0a, 0x0c, 0x57, 0x45, 0x42,
	0x5f, 0x4c, 0x4f, 0x43, 0x41, 0x54, 0x49, 0x4f, 0x4e, 0x10, 0x01, 0x12, 0x13, 0x0a, 0x0f, 0x53,
	0x43, 0x48, 0x45, 0x4d, 0x41, 0x5f, 0x52, 0x45, 0x47, 0x49, 0x53, 0x54, 0x52, 0x59, 0x10, 0x02,
	0x12, 0x06, 0x0a, 0x02, 0x53, 0x33, 0x10, 0x03, 0x32, 0xbb, 0x0c, 0x0a, 0x11, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6e, 0x74, 0x72, 0x6f, 0x6c, 0x12, 0x5d,
	0x0a, 0x0e, 0x4d, 0x65, 0x74, 0x61, 0x4e, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70,
	0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x4e, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x63, 0x6b, 0x75, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x72, 0x77, 0x63, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x4e, 0x6f, 0x64, 0x65, 0x42, 0x61, 0x63,
	0x6b, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a,
	0x0e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x56,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x72, 0x77, 0x63, 0x2e, 0x56, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x53, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x23,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x47, 0x65,
	0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72,
	0x77, 0x63, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x43,
	0x6f, 0x72, 0x64, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x22, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x43, 0x6f, 0x72, 0x64,
	0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e,
	0x43, 0x6f, 0x72, 0x64, 0x6f, 0x6e, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x52, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72,
	0x6b, 0x65, 0x72, 0x73, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x72, 0x77, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f, 0x72, 0x6b, 0x65, 0x72,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x57, 0x6f,
	0x72, 0x6b, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x5d, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f,
	0x74, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6e, 0x61, 0x70, 0x73, 0x68, 0x6f, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x6e, 0x61, 0x70,
	0x73, 0x68, 0x6f, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x54,
	0x0a, 0x0b, 0x52, 0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x20, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52, 0x65, 0x73,
	0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x52,
	0x65, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x56, 0x61, 0x63, 0x75, 0x75, 0x6d, 0x45, 0x74,
	0x63, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x56, 0x61, 0x63, 0x75, 0x75, 0x6d, 0x45, 0x74, 0x63, 0x64,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x56, 0x61, 0x63, 0x75, 0x75,
	0x6d, 0x45, 0x74, 0x63, 0x64, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f,
	0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77,
	0x63, 0x2e, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7d,
	0x0a, 0x18, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65,
	0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x12, 0x2d, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61,
	0x67, 0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x65,
	0x61, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x47, 0x65, 0x6e, 0x44, 0x69, 0x61, 0x67,
	0x6e, 0x6f, 0x73, 0x69, 0x73, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x53, 0x74, 0x72, 0x65, 0x61,
	0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x30, 0x01, 0x12, 0x5a, 0x0a,
	0x0d, 0x4d, 0x65, 0x74, 0x61, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x22,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77,
	0x63, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x4d, 0x69, 0x67, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x46, 0x65, 0x74,
	0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x12, 0x24, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63,
	0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x54, 0x6f, 0x70, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77,
	0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x54, 0x6f, 0x70, 0x69,
	0x63, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x46,
	0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65,
	0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e,
	0x46, 0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66, 0x6b, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x4b, 0x61, 0x66,
	0x6b, 0x61, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x46, 0x65, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x50, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x54, 0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77,
	0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x54,
	0x61, 0x62, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66,
	0x0a, 0x11, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x63, 0x68,
	0x65, 0x6d, 0x61, 0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72,
	0x77, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68, 0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x63,
	0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x72, 0x77, 0x63, 0x2e, 0x46, 0x65, 0x74, 0x63, 0x68,
	0x53, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x53, 0x63, 0x68, 0x65, 0x6d, 0x61, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c,
	0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x62, 0x67, 0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x72, 0x77,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_rwc_proto_rawDescOnce sync.Once
	file_services_rwc_proto_rawDescData []byte
)

func file_services_rwc_proto_rawDescGZIP() []byte {
	file_services_rwc_proto_rawDescOnce.Do(func() {
		file_services_rwc_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_rwc_proto_rawDesc), len(file_services_rwc_proto_rawDesc)))
	})
	return file_services_rwc_proto_rawDescData
}

var file_services_rwc_proto_enumTypes = make([]protoimpl.EnumInfo, 5)
var file_services_rwc_proto_msgTypes = make([]protoimpl.MessageInfo, 40)
var file_services_rwc_proto_goTypes = []any{
	(KafkaSecurityProtocol)(0),               // 0: services.rwc.KafkaSecurityProtocol
	(KafkaSaslMechanism)(0),                  // 1: services.rwc.KafkaSaslMechanism
	(PostgresSslMode)(0),                     // 2: services.rwc.PostgresSslMode
	(SchemaFormat)(0),                        // 3: services.rwc.SchemaFormat
	(SchemaLocation)(0),                      // 4: services.rwc.SchemaLocation
	(*MetaNodeBackupRequest)(nil),            // 5: services.rwc.MetaNodeBackupRequest
	(*MetaNodeBackupResponse)(nil),           // 6: services.rwc.MetaNodeBackupResponse
	(*ValidateSourceRequest)(nil),            // 7: services.rwc.ValidateSourceRequest
	(*ValidateSourceResponse)(nil),           // 8: services.rwc.ValidateSourceResponse
	(*GetClusterInfoRequest)(nil),            // 9: services.rwc.GetClusterInfoRequest
	(*GetClusterInfoResponse)(nil),           // 10: services.rwc.GetClusterInfoResponse
	(*CordonWorkersRequest)(nil),             // 11: services.rwc.CordonWorkersRequest
	(*CordonWorkersResponse)(nil),            // 12: services.rwc.CordonWorkersResponse
	(*ResizeWorkersRequest)(nil),             // 13: services.rwc.ResizeWorkersRequest
	(*ResizeWorkersResponse)(nil),            // 14: services.rwc.ResizeWorkersResponse
	(*DeleteWorkersRequest)(nil),             // 15: services.rwc.DeleteWorkersRequest
	(*DeleteWorkersResponse)(nil),            // 16: services.rwc.DeleteWorkersResponse
	(*DeleteSnapshotRequest)(nil),            // 17: services.rwc.DeleteSnapshotRequest
	(*DeleteSnapshotResponse)(nil),           // 18: services.rwc.DeleteSnapshotResponse
	(*RestoreMetaRequestEtcd)(nil),           // 19: services.rwc.RestoreMetaRequestEtcd
	(*RestoreMetaRequestSql)(nil),            // 20: services.rwc.RestoreMetaRequestSql
	(*RestoreMetaRequest)(nil),               // 21: services.rwc.RestoreMetaRequest
	(*RestoreMetaResponse)(nil),              // 22: services.rwc.RestoreMetaResponse
	(*VacuumEtcdMetaRequest)(nil),            // 23: services.rwc.VacuumEtcdMetaRequest
	(*VacuumEtcdMetaResponse)(nil),           // 24: services.rwc.VacuumEtcdMetaResponse
	(*GenDiagnosisReportRequest)(nil),        // 25: services.rwc.GenDiagnosisReportRequest
	(*GenDiagnosisReportResponse)(nil),       // 26: services.rwc.GenDiagnosisReportResponse
	(*GenDiagnosisReportStreamRequest)(nil),  // 27: services.rwc.GenDiagnosisReportStreamRequest
	(*GenDiagnosisReportStreamResponse)(nil), // 28: services.rwc.GenDiagnosisReportStreamResponse
	(*MetaMigrationRequest)(nil),             // 29: services.rwc.MetaMigrationRequest
	(*MetaMigrationResponse)(nil),            // 30: services.rwc.MetaMigrationResponse
	(*KafkaConfig)(nil),                      // 31: services.rwc.KafkaConfig
	(*FetchKafkaTopicRequest)(nil),           // 32: services.rwc.FetchKafkaTopicRequest
	(*FetchKafkaTopicResponse)(nil),          // 33: services.rwc.FetchKafkaTopicResponse
	(*FetchKafkaMessageRequest)(nil),         // 34: services.rwc.FetchKafkaMessageRequest
	(*FetchKafkaMessageResponse)(nil),        // 35: services.rwc.FetchKafkaMessageResponse
	(*PostgresConfig)(nil),                   // 36: services.rwc.PostgresConfig
	(*FetchPostgresTableRequest)(nil),        // 37: services.rwc.FetchPostgresTableRequest
	(*FetchPostgresTableResponse)(nil),       // 38: services.rwc.FetchPostgresTableResponse
	(*SchemaSchemaRegistryConfig)(nil),       // 39: services.rwc.SchemaSchemaRegistryConfig
	(*SchemaS3Config)(nil),                   // 40: services.rwc.SchemaS3Config
	(*FetchSourceSchemaRequest)(nil),         // 41: services.rwc.FetchSourceSchemaRequest
	(*FetchSourceSchemaResponse)(nil),        // 42: services.rwc.FetchSourceSchemaResponse
	(*RawSchemaFile)(nil),                    // 43: services.rwc.RawSchemaFile
	nil,                                      // 44: services.rwc.RestoreMetaRequest.EnvsEntry
	(*resource.Meta)(nil),                    // 45: common.resource.Meta
	(*creation.Status)(nil),                  // 46: common.resource.creation.Status
	(*k8s.ResourceRequirements)(nil),         // 47: common.k8s.ResourceRequirements
	(*k8s.Toleration)(nil),                   // 48: common.k8s.Toleration
	(*k8s.Affinity)(nil),                     // 49: common.k8s.Affinity
}
var file_services_rwc_proto_depIdxs = []int32{
	45, // 0: services.rwc.MetaNodeBackupRequest.resource_meta:type_name -> common.resource.Meta
	46, // 1: services.rwc.MetaNodeBackupResponse.status:type_name -> common.resource.creation.Status
	45, // 2: services.rwc.RestoreMetaRequest.resource_meta:type_name -> common.resource.Meta
	19, // 3: services.rwc.RestoreMetaRequest.etcd_config:type_name -> services.rwc.RestoreMetaRequestEtcd
	20, // 4: services.rwc.RestoreMetaRequest.sql_config:type_name -> services.rwc.RestoreMetaRequestSql
	44, // 5: services.rwc.RestoreMetaRequest.envs:type_name -> services.rwc.RestoreMetaRequest.EnvsEntry
	46, // 6: services.rwc.RestoreMetaResponse.status:type_name -> common.resource.creation.Status
	45, // 7: services.rwc.MetaMigrationRequest.resource_meta:type_name -> common.resource.Meta
	47, // 8: services.rwc.MetaMigrationRequest.task_resources:type_name -> common.k8s.ResourceRequirements
	48, // 9: services.rwc.MetaMigrationRequest.task_tolerations:type_name -> common.k8s.Toleration
	49, // 10: services.rwc.MetaMigrationRequest.task_affinity:type_name -> common.k8s.Affinity
	46, // 11: services.rwc.MetaMigrationResponse.status:type_name -> common.resource.creation.Status
	0,  // 12: services.rwc.KafkaConfig.security_protocol:type_name -> services.rwc.KafkaSecurityProtocol
	1,  // 13: services.rwc.KafkaConfig.sasl_mechanism:type_name -> services.rwc.KafkaSaslMechanism
	31, // 14: services.rwc.FetchKafkaTopicRequest.kafka:type_name -> services.rwc.KafkaConfig
	31, // 15: services.rwc.FetchKafkaMessageRequest.kafka:type_name -> services.rwc.KafkaConfig
	2,  // 16: services.rwc.PostgresConfig.ssl_mode:type_name -> services.rwc.PostgresSslMode
	36, // 17: services.rwc.FetchPostgresTableRequest.postgres:type_name -> services.rwc.PostgresConfig
	3,  // 18: services.rwc.FetchSourceSchemaRequest.format:type_name -> services.rwc.SchemaFormat
	4,  // 19: services.rwc.FetchSourceSchemaRequest.location:type_name -> services.rwc.SchemaLocation
	39, // 20: services.rwc.FetchSourceSchemaRequest.schema_registry:type_name -> services.rwc.SchemaSchemaRegistryConfig
	40, // 21: services.rwc.FetchSourceSchemaRequest.s3:type_name -> services.rwc.SchemaS3Config
	43, // 22: services.rwc.FetchSourceSchemaResponse.files:type_name -> services.rwc.RawSchemaFile
	5,  // 23: services.rwc.RisingwaveControl.MetaNodeBackup:input_type -> services.rwc.MetaNodeBackupRequest
	7,  // 24: services.rwc.RisingwaveControl.ValidateSource:input_type -> services.rwc.ValidateSourceRequest
	9,  // 25: services.rwc.RisingwaveControl.GetClusterInfo:input_type -> services.rwc.GetClusterInfoRequest
	11, // 26: services.rwc.RisingwaveControl.CordonWorkers:input_type -> services.rwc.CordonWorkersRequest
	13, // 27: services.rwc.RisingwaveControl.ResizeWorkers:input_type -> services.rwc.ResizeWorkersRequest
	15, // 28: services.rwc.RisingwaveControl.DeleteWorkers:input_type -> services.rwc.DeleteWorkersRequest
	17, // 29: services.rwc.RisingwaveControl.DeleteSnapshot:input_type -> services.rwc.DeleteSnapshotRequest
	21, // 30: services.rwc.RisingwaveControl.RestoreMeta:input_type -> services.rwc.RestoreMetaRequest
	23, // 31: services.rwc.RisingwaveControl.VacuumEtcdMeta:input_type -> services.rwc.VacuumEtcdMetaRequest
	25, // 32: services.rwc.RisingwaveControl.GenDiagnosisReport:input_type -> services.rwc.GenDiagnosisReportRequest
	27, // 33: services.rwc.RisingwaveControl.GenDiagnosisReportStream:input_type -> services.rwc.GenDiagnosisReportStreamRequest
	29, // 34: services.rwc.RisingwaveControl.MetaMigration:input_type -> services.rwc.MetaMigrationRequest
	32, // 35: services.rwc.RisingwaveControl.FetchKafkaTopic:input_type -> services.rwc.FetchKafkaTopicRequest
	34, // 36: services.rwc.RisingwaveControl.FetchKafkaMessage:input_type -> services.rwc.FetchKafkaMessageRequest
	37, // 37: services.rwc.RisingwaveControl.FetchPostgresTable:input_type -> services.rwc.FetchPostgresTableRequest
	41, // 38: services.rwc.RisingwaveControl.FetchSourceSchema:input_type -> services.rwc.FetchSourceSchemaRequest
	6,  // 39: services.rwc.RisingwaveControl.MetaNodeBackup:output_type -> services.rwc.MetaNodeBackupResponse
	8,  // 40: services.rwc.RisingwaveControl.ValidateSource:output_type -> services.rwc.ValidateSourceResponse
	10, // 41: services.rwc.RisingwaveControl.GetClusterInfo:output_type -> services.rwc.GetClusterInfoResponse
	12, // 42: services.rwc.RisingwaveControl.CordonWorkers:output_type -> services.rwc.CordonWorkersResponse
	14, // 43: services.rwc.RisingwaveControl.ResizeWorkers:output_type -> services.rwc.ResizeWorkersResponse
	16, // 44: services.rwc.RisingwaveControl.DeleteWorkers:output_type -> services.rwc.DeleteWorkersResponse
	18, // 45: services.rwc.RisingwaveControl.DeleteSnapshot:output_type -> services.rwc.DeleteSnapshotResponse
	22, // 46: services.rwc.RisingwaveControl.RestoreMeta:output_type -> services.rwc.RestoreMetaResponse
	24, // 47: services.rwc.RisingwaveControl.VacuumEtcdMeta:output_type -> services.rwc.VacuumEtcdMetaResponse
	26, // 48: services.rwc.RisingwaveControl.GenDiagnosisReport:output_type -> services.rwc.GenDiagnosisReportResponse
	28, // 49: services.rwc.RisingwaveControl.GenDiagnosisReportStream:output_type -> services.rwc.GenDiagnosisReportStreamResponse
	30, // 50: services.rwc.RisingwaveControl.MetaMigration:output_type -> services.rwc.MetaMigrationResponse
	33, // 51: services.rwc.RisingwaveControl.FetchKafkaTopic:output_type -> services.rwc.FetchKafkaTopicResponse
	35, // 52: services.rwc.RisingwaveControl.FetchKafkaMessage:output_type -> services.rwc.FetchKafkaMessageResponse
	38, // 53: services.rwc.RisingwaveControl.FetchPostgresTable:output_type -> services.rwc.FetchPostgresTableResponse
	42, // 54: services.rwc.RisingwaveControl.FetchSourceSchema:output_type -> services.rwc.FetchSourceSchemaResponse
	39, // [39:55] is the sub-list for method output_type
	23, // [23:39] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_services_rwc_proto_init() }
func file_services_rwc_proto_init() {
	if File_services_rwc_proto != nil {
		return
	}
	file_services_rwc_proto_msgTypes[16].OneofWrappers = []any{
		(*RestoreMetaRequest_EtcdConfig)(nil),
		(*RestoreMetaRequest_SqlConfig)(nil),
	}
	file_services_rwc_proto_msgTypes[26].OneofWrappers = []any{}
	file_services_rwc_proto_msgTypes[34].OneofWrappers = []any{}
	file_services_rwc_proto_msgTypes[35].OneofWrappers = []any{}
	file_services_rwc_proto_msgTypes[36].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_rwc_proto_rawDesc), len(file_services_rwc_proto_rawDesc)),
			NumEnums:      5,
			NumMessages:   40,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_rwc_proto_goTypes,
		DependencyIndexes: file_services_rwc_proto_depIdxs,
		EnumInfos:         file_services_rwc_proto_enumTypes,
		MessageInfos:      file_services_rwc_proto_msgTypes,
	}.Build()
	File_services_rwc_proto = out.File
	file_services_rwc_proto_goTypes = nil
	file_services_rwc_proto_depIdxs = nil
}
