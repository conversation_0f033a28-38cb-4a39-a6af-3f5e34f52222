// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/rwc (interfaces: RisingwaveControlClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/rwc -package=rwc -destination=pbgen/services/rwc/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/rwc RisingwaveControlClient
//

// Package rwc is a generated GoMock package.
package rwc

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockRisingwaveControlClient is a mock of RisingwaveControlClient interface.
type MockRisingwaveControlClient struct {
	ctrl     *gomock.Controller
	recorder *MockRisingwaveControlClientMockRecorder
	isgomock struct{}
}

// MockRisingwaveControlClientMockRecorder is the mock recorder for MockRisingwaveControlClient.
type MockRisingwaveControlClientMockRecorder struct {
	mock *MockRisingwaveControlClient
}

// NewMockRisingwaveControlClient creates a new mock instance.
func NewMockRisingwaveControlClient(ctrl *gomock.Controller) *MockRisingwaveControlClient {
	mock := &MockRisingwaveControlClient{ctrl: ctrl}
	mock.recorder = &MockRisingwaveControlClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRisingwaveControlClient) EXPECT() *MockRisingwaveControlClientMockRecorder {
	return m.recorder
}

// CordonWorkers mocks base method.
func (m *MockRisingwaveControlClient) CordonWorkers(ctx context.Context, in *CordonWorkersRequest, opts ...grpc.CallOption) (*CordonWorkersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CordonWorkers", varargs...)
	ret0, _ := ret[0].(*CordonWorkersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CordonWorkers indicates an expected call of CordonWorkers.
func (mr *MockRisingwaveControlClientMockRecorder) CordonWorkers(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CordonWorkers", reflect.TypeOf((*MockRisingwaveControlClient)(nil).CordonWorkers), varargs...)
}

// DeleteSnapshot mocks base method.
func (m *MockRisingwaveControlClient) DeleteSnapshot(ctx context.Context, in *DeleteSnapshotRequest, opts ...grpc.CallOption) (*DeleteSnapshotResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSnapshot", varargs...)
	ret0, _ := ret[0].(*DeleteSnapshotResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSnapshot indicates an expected call of DeleteSnapshot.
func (mr *MockRisingwaveControlClientMockRecorder) DeleteSnapshot(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSnapshot", reflect.TypeOf((*MockRisingwaveControlClient)(nil).DeleteSnapshot), varargs...)
}

// DeleteWorkers mocks base method.
func (m *MockRisingwaveControlClient) DeleteWorkers(ctx context.Context, in *DeleteWorkersRequest, opts ...grpc.CallOption) (*DeleteWorkersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteWorkers", varargs...)
	ret0, _ := ret[0].(*DeleteWorkersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteWorkers indicates an expected call of DeleteWorkers.
func (mr *MockRisingwaveControlClientMockRecorder) DeleteWorkers(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkers", reflect.TypeOf((*MockRisingwaveControlClient)(nil).DeleteWorkers), varargs...)
}

// FetchKafkaMessage mocks base method.
func (m *MockRisingwaveControlClient) FetchKafkaMessage(ctx context.Context, in *FetchKafkaMessageRequest, opts ...grpc.CallOption) (*FetchKafkaMessageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchKafkaMessage", varargs...)
	ret0, _ := ret[0].(*FetchKafkaMessageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchKafkaMessage indicates an expected call of FetchKafkaMessage.
func (mr *MockRisingwaveControlClientMockRecorder) FetchKafkaMessage(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchKafkaMessage", reflect.TypeOf((*MockRisingwaveControlClient)(nil).FetchKafkaMessage), varargs...)
}

// FetchKafkaTopic mocks base method.
func (m *MockRisingwaveControlClient) FetchKafkaTopic(ctx context.Context, in *FetchKafkaTopicRequest, opts ...grpc.CallOption) (*FetchKafkaTopicResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchKafkaTopic", varargs...)
	ret0, _ := ret[0].(*FetchKafkaTopicResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchKafkaTopic indicates an expected call of FetchKafkaTopic.
func (mr *MockRisingwaveControlClientMockRecorder) FetchKafkaTopic(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchKafkaTopic", reflect.TypeOf((*MockRisingwaveControlClient)(nil).FetchKafkaTopic), varargs...)
}

// FetchPostgresTable mocks base method.
func (m *MockRisingwaveControlClient) FetchPostgresTable(ctx context.Context, in *FetchPostgresTableRequest, opts ...grpc.CallOption) (*FetchPostgresTableResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchPostgresTable", varargs...)
	ret0, _ := ret[0].(*FetchPostgresTableResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPostgresTable indicates an expected call of FetchPostgresTable.
func (mr *MockRisingwaveControlClientMockRecorder) FetchPostgresTable(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPostgresTable", reflect.TypeOf((*MockRisingwaveControlClient)(nil).FetchPostgresTable), varargs...)
}

// FetchSourceSchema mocks base method.
func (m *MockRisingwaveControlClient) FetchSourceSchema(ctx context.Context, in *FetchSourceSchemaRequest, opts ...grpc.CallOption) (*FetchSourceSchemaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "FetchSourceSchema", varargs...)
	ret0, _ := ret[0].(*FetchSourceSchemaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSourceSchema indicates an expected call of FetchSourceSchema.
func (mr *MockRisingwaveControlClientMockRecorder) FetchSourceSchema(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSourceSchema", reflect.TypeOf((*MockRisingwaveControlClient)(nil).FetchSourceSchema), varargs...)
}

// GenDiagnosisReport mocks base method.
func (m *MockRisingwaveControlClient) GenDiagnosisReport(ctx context.Context, in *GenDiagnosisReportRequest, opts ...grpc.CallOption) (*GenDiagnosisReportResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenDiagnosisReport", varargs...)
	ret0, _ := ret[0].(*GenDiagnosisReportResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenDiagnosisReport indicates an expected call of GenDiagnosisReport.
func (mr *MockRisingwaveControlClientMockRecorder) GenDiagnosisReport(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenDiagnosisReport", reflect.TypeOf((*MockRisingwaveControlClient)(nil).GenDiagnosisReport), varargs...)
}

// GenDiagnosisReportStream mocks base method.
func (m *MockRisingwaveControlClient) GenDiagnosisReportStream(ctx context.Context, in *GenDiagnosisReportStreamRequest, opts ...grpc.CallOption) (grpc.ServerStreamingClient[GenDiagnosisReportStreamResponse], error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GenDiagnosisReportStream", varargs...)
	ret0, _ := ret[0].(grpc.ServerStreamingClient[GenDiagnosisReportStreamResponse])
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenDiagnosisReportStream indicates an expected call of GenDiagnosisReportStream.
func (mr *MockRisingwaveControlClientMockRecorder) GenDiagnosisReportStream(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenDiagnosisReportStream", reflect.TypeOf((*MockRisingwaveControlClient)(nil).GenDiagnosisReportStream), varargs...)
}

// GetClusterInfo mocks base method.
func (m *MockRisingwaveControlClient) GetClusterInfo(ctx context.Context, in *GetClusterInfoRequest, opts ...grpc.CallOption) (*GetClusterInfoResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClusterInfo", varargs...)
	ret0, _ := ret[0].(*GetClusterInfoResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterInfo indicates an expected call of GetClusterInfo.
func (mr *MockRisingwaveControlClientMockRecorder) GetClusterInfo(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterInfo", reflect.TypeOf((*MockRisingwaveControlClient)(nil).GetClusterInfo), varargs...)
}

// MetaMigration mocks base method.
func (m *MockRisingwaveControlClient) MetaMigration(ctx context.Context, in *MetaMigrationRequest, opts ...grpc.CallOption) (*MetaMigrationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MetaMigration", varargs...)
	ret0, _ := ret[0].(*MetaMigrationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MetaMigration indicates an expected call of MetaMigration.
func (mr *MockRisingwaveControlClientMockRecorder) MetaMigration(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MetaMigration", reflect.TypeOf((*MockRisingwaveControlClient)(nil).MetaMigration), varargs...)
}

// MetaNodeBackup mocks base method.
func (m *MockRisingwaveControlClient) MetaNodeBackup(ctx context.Context, in *MetaNodeBackupRequest, opts ...grpc.CallOption) (*MetaNodeBackupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "MetaNodeBackup", varargs...)
	ret0, _ := ret[0].(*MetaNodeBackupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// MetaNodeBackup indicates an expected call of MetaNodeBackup.
func (mr *MockRisingwaveControlClientMockRecorder) MetaNodeBackup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MetaNodeBackup", reflect.TypeOf((*MockRisingwaveControlClient)(nil).MetaNodeBackup), varargs...)
}

// ResizeWorkers mocks base method.
func (m *MockRisingwaveControlClient) ResizeWorkers(ctx context.Context, in *ResizeWorkersRequest, opts ...grpc.CallOption) (*ResizeWorkersResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ResizeWorkers", varargs...)
	ret0, _ := ret[0].(*ResizeWorkersResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ResizeWorkers indicates an expected call of ResizeWorkers.
func (mr *MockRisingwaveControlClientMockRecorder) ResizeWorkers(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeWorkers", reflect.TypeOf((*MockRisingwaveControlClient)(nil).ResizeWorkers), varargs...)
}

// RestoreMeta mocks base method.
func (m *MockRisingwaveControlClient) RestoreMeta(ctx context.Context, in *RestoreMetaRequest, opts ...grpc.CallOption) (*RestoreMetaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RestoreMeta", varargs...)
	ret0, _ := ret[0].(*RestoreMetaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RestoreMeta indicates an expected call of RestoreMeta.
func (mr *MockRisingwaveControlClientMockRecorder) RestoreMeta(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreMeta", reflect.TypeOf((*MockRisingwaveControlClient)(nil).RestoreMeta), varargs...)
}

// VacuumEtcdMeta mocks base method.
func (m *MockRisingwaveControlClient) VacuumEtcdMeta(ctx context.Context, in *VacuumEtcdMetaRequest, opts ...grpc.CallOption) (*VacuumEtcdMetaResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "VacuumEtcdMeta", varargs...)
	ret0, _ := ret[0].(*VacuumEtcdMetaResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// VacuumEtcdMeta indicates an expected call of VacuumEtcdMeta.
func (mr *MockRisingwaveControlClientMockRecorder) VacuumEtcdMeta(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "VacuumEtcdMeta", reflect.TypeOf((*MockRisingwaveControlClient)(nil).VacuumEtcdMeta), varargs...)
}

// ValidateSource mocks base method.
func (m *MockRisingwaveControlClient) ValidateSource(ctx context.Context, in *ValidateSourceRequest, opts ...grpc.CallOption) (*ValidateSourceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ValidateSource", varargs...)
	ret0, _ := ret[0].(*ValidateSourceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ValidateSource indicates an expected call of ValidateSource.
func (mr *MockRisingwaveControlClientMockRecorder) ValidateSource(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateSource", reflect.TypeOf((*MockRisingwaveControlClient)(nil).ValidateSource), varargs...)
}
