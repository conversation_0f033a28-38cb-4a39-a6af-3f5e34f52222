// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/aws.proto

package aws

import (
	aws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	deletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	update "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type VPCEndpointServiceReachabilityStatus int32

const (
	VPCEndpointServiceReachabilityStatus_REACHABILITY_UNSPECIFIED VPCEndpointServiceReachabilityStatus = 0
	VPCEndpointServiceReachabilityStatus_SUCCESS                  VPCEndpointServiceReachabilityStatus = 1
	VPCEndpointServiceReachabilityStatus_NOT_FOUND                VPCEndpointServiceReachabilityStatus = 2
)

// Enum value maps for VPCEndpointServiceReachabilityStatus.
var (
	VPCEndpointServiceReachabilityStatus_name = map[int32]string{
		0: "REACHABILITY_UNSPECIFIED",
		1: "SUCCESS",
		2: "NOT_FOUND",
	}
	VPCEndpointServiceReachabilityStatus_value = map[string]int32{
		"REACHABILITY_UNSPECIFIED": 0,
		"SUCCESS":                  1,
		"NOT_FOUND":                2,
	}
)

func (x VPCEndpointServiceReachabilityStatus) Enum() *VPCEndpointServiceReachabilityStatus {
	p := new(VPCEndpointServiceReachabilityStatus)
	*p = x
	return p
}

func (x VPCEndpointServiceReachabilityStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VPCEndpointServiceReachabilityStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_services_aws_proto_enumTypes[0].Descriptor()
}

func (VPCEndpointServiceReachabilityStatus) Type() protoreflect.EnumType {
	return &file_services_aws_proto_enumTypes[0]
}

func (x VPCEndpointServiceReachabilityStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VPCEndpointServiceReachabilityStatus.Descriptor instead.
func (VPCEndpointServiceReachabilityStatus) EnumDescriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{0}
}

// https://docs.aws.amazon.com/vpc/latest/privatelink/concepts.html#concepts-endpoint-states
type VPCEndpointStatus int32

const (
	VPCEndpointStatus_STATUS_UNSPECIFIED VPCEndpointStatus = 0
	VPCEndpointStatus_PENDING_ACCEPTANCE VPCEndpointStatus = 1
	VPCEndpointStatus_PENDING            VPCEndpointStatus = 2
	VPCEndpointStatus_AVAILABLE          VPCEndpointStatus = 3
	VPCEndpointStatus_REJECTED           VPCEndpointStatus = 4
	VPCEndpointStatus_EXPIRED            VPCEndpointStatus = 5
	VPCEndpointStatus_FAILED             VPCEndpointStatus = 6
	VPCEndpointStatus_DELETING           VPCEndpointStatus = 7
	VPCEndpointStatus_DELETED            VPCEndpointStatus = 8
)

// Enum value maps for VPCEndpointStatus.
var (
	VPCEndpointStatus_name = map[int32]string{
		0: "STATUS_UNSPECIFIED",
		1: "PENDING_ACCEPTANCE",
		2: "PENDING",
		3: "AVAILABLE",
		4: "REJECTED",
		5: "EXPIRED",
		6: "FAILED",
		7: "DELETING",
		8: "DELETED",
	}
	VPCEndpointStatus_value = map[string]int32{
		"STATUS_UNSPECIFIED": 0,
		"PENDING_ACCEPTANCE": 1,
		"PENDING":            2,
		"AVAILABLE":          3,
		"REJECTED":           4,
		"EXPIRED":            5,
		"FAILED":             6,
		"DELETING":           7,
		"DELETED":            8,
	}
)

func (x VPCEndpointStatus) Enum() *VPCEndpointStatus {
	p := new(VPCEndpointStatus)
	*p = x
	return p
}

func (x VPCEndpointStatus) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VPCEndpointStatus) Descriptor() protoreflect.EnumDescriptor {
	return file_services_aws_proto_enumTypes[1].Descriptor()
}

func (VPCEndpointStatus) Type() protoreflect.EnumType {
	return &file_services_aws_proto_enumTypes[1]
}

func (x VPCEndpointStatus) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VPCEndpointStatus.Descriptor instead.
func (VPCEndpointStatus) EnumDescriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{1}
}

type IPPermission struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// IP protocol. A value of -1 indicates all protocols
	Protocol string `protobuf:"bytes,1,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// The starting port number (included).
	FromPort int32 `protobuf:"varint,2,opt,name=from_port,json=fromPort,proto3" json:"from_port,omitempty"`
	// The ending port number (included).
	ToPort int32 `protobuf:"varint,3,opt,name=to_port,json=toPort,proto3" json:"to_port,omitempty"`
	// CIDRs of this rule.
	Cidrs []string `protobuf:"bytes,4,rep,name=cidrs,proto3" json:"cidrs,omitempty"`
	// target security groups of this rule.
	SourceSecurityGroupIds []string `protobuf:"bytes,5,rep,name=source_security_group_ids,json=sourceSecurityGroupIds,proto3" json:"source_security_group_ids,omitempty"`
	// description of this permission. shown in the AWS console.
	Description   string `protobuf:"bytes,6,opt,name=description,proto3" json:"description,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IPPermission) Reset() {
	*x = IPPermission{}
	mi := &file_services_aws_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IPPermission) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPPermission) ProtoMessage() {}

func (x *IPPermission) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPPermission.ProtoReflect.Descriptor instead.
func (*IPPermission) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{0}
}

func (x *IPPermission) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *IPPermission) GetFromPort() int32 {
	if x != nil {
		return x.FromPort
	}
	return 0
}

func (x *IPPermission) GetToPort() int32 {
	if x != nil {
		return x.ToPort
	}
	return 0
}

func (x *IPPermission) GetCidrs() []string {
	if x != nil {
		return x.Cidrs
	}
	return nil
}

func (x *IPPermission) GetSourceSecurityGroupIds() []string {
	if x != nil {
		return x.SourceSecurityGroupIds
	}
	return nil
}

func (x *IPPermission) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

type CreateSecurityGroupRequest struct {
	state                 protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta          *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	OutboundIpPermissions []*IPPermission        `protobuf:"bytes,2,rep,name=outbound_ip_permissions,json=outboundIpPermissions,proto3" json:"outbound_ip_permissions,omitempty"`
	InboundIpPermissions  []*IPPermission        `protobuf:"bytes,3,rep,name=inbound_ip_permissions,json=inboundIpPermissions,proto3" json:"inbound_ip_permissions,omitempty"`
	unknownFields         protoimpl.UnknownFields
	sizeCache             protoimpl.SizeCache
}

func (x *CreateSecurityGroupRequest) Reset() {
	*x = CreateSecurityGroupRequest{}
	mi := &file_services_aws_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSecurityGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecurityGroupRequest) ProtoMessage() {}

func (x *CreateSecurityGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecurityGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateSecurityGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{1}
}

func (x *CreateSecurityGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateSecurityGroupRequest) GetOutboundIpPermissions() []*IPPermission {
	if x != nil {
		return x.OutboundIpPermissions
	}
	return nil
}

func (x *CreateSecurityGroupRequest) GetInboundIpPermissions() []*IPPermission {
	if x != nil {
		return x.InboundIpPermissions
	}
	return nil
}

// Statuses - UNKNOWN, SCHEDULED, CREATED, ALREADY_EXISTS
type CreateSecurityGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSecurityGroupResponse) Reset() {
	*x = CreateSecurityGroupResponse{}
	mi := &file_services_aws_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSecurityGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecurityGroupResponse) ProtoMessage() {}

func (x *CreateSecurityGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecurityGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateSecurityGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{2}
}

func (x *CreateSecurityGroupResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteSecurityGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSecurityGroupRequest) Reset() {
	*x = DeleteSecurityGroupRequest{}
	mi := &file_services_aws_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSecurityGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecurityGroupRequest) ProtoMessage() {}

func (x *DeleteSecurityGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecurityGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteSecurityGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteSecurityGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - UNKNOWN, SCHEDULED, DELETED, NOT_FOUND
type DeleteSecurityGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSecurityGroupResponse) Reset() {
	*x = DeleteSecurityGroupResponse{}
	mi := &file_services_aws_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSecurityGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecurityGroupResponse) ProtoMessage() {}

func (x *DeleteSecurityGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecurityGroupResponse.ProtoReflect.Descriptor instead.
func (*DeleteSecurityGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteSecurityGroupResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSecurityGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSecurityGroupRequest) Reset() {
	*x = GetSecurityGroupRequest{}
	mi := &file_services_aws_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSecurityGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityGroupRequest) ProtoMessage() {}

func (x *GetSecurityGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityGroupRequest.ProtoReflect.Descriptor instead.
func (*GetSecurityGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{5}
}

func (x *GetSecurityGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - UNKNOWN, NOT_FOUND, NOT_READY, READY, ERROR
type GetSecurityGroupResponse struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Status          *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SecurityGroupId string                 `protobuf:"bytes,3,opt,name=security_group_id,json=securityGroupId,proto3" json:"security_group_id,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *GetSecurityGroupResponse) Reset() {
	*x = GetSecurityGroupResponse{}
	mi := &file_services_aws_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSecurityGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityGroupResponse) ProtoMessage() {}

func (x *GetSecurityGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityGroupResponse.ProtoReflect.Descriptor instead.
func (*GetSecurityGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{6}
}

func (x *GetSecurityGroupResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecurityGroupResponse) GetSecurityGroupId() string {
	if x != nil {
		return x.SecurityGroupId
	}
	return ""
}

// Request for creating an IAM policy. Empty policy is not accepted. So it's
// required to have at least one of the access options set.
type CreateIAMPolicyRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource meta must meet the following restrictions:
	// - Name:
	//  1. contains at most 128 characters.
	//  2. contain only lowercase alphanumeric characters, '-' or '.'
	//  3. start with an alphanumeric character
	//  4. end with an alphanumeric character
	//
	// - Namespace:
	//  1. Namespace is REQUIRED
	//  2. Must be valid RFC 1123 Label Names, i.e.:
	//     a. contain at most 63 characters
	//     b. contain only lowercase alphanumeric characters or '-'
	//     c. start with an alphanumeric character
	//     d. end with an alphanumeric character
	ResourceMeta  *resource.Meta     `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	AccessOptions []*IAMAccessOption `protobuf:"bytes,2,rep,name=access_options,json=accessOptions,proto3" json:"access_options,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMPolicyRequest) Reset() {
	*x = CreateIAMPolicyRequest{}
	mi := &file_services_aws_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMPolicyRequest) ProtoMessage() {}

func (x *CreateIAMPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMPolicyRequest.ProtoReflect.Descriptor instead.
func (*CreateIAMPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{7}
}

func (x *CreateIAMPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateIAMPolicyRequest) GetAccessOptions() []*IAMAccessOption {
	if x != nil {
		return x.AccessOptions
	}
	return nil
}

type IAMAccessOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to AccessOption:
	//
	//	*IAMAccessOption_S3AccessOption
	AccessOption  isIAMAccessOption_AccessOption `protobuf_oneof:"access_option"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IAMAccessOption) Reset() {
	*x = IAMAccessOption{}
	mi := &file_services_aws_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IAMAccessOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IAMAccessOption) ProtoMessage() {}

func (x *IAMAccessOption) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IAMAccessOption.ProtoReflect.Descriptor instead.
func (*IAMAccessOption) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{8}
}

func (x *IAMAccessOption) GetAccessOption() isIAMAccessOption_AccessOption {
	if x != nil {
		return x.AccessOption
	}
	return nil
}

func (x *IAMAccessOption) GetS3AccessOption() *IAMS3AccessOption {
	if x != nil {
		if x, ok := x.AccessOption.(*IAMAccessOption_S3AccessOption); ok {
			return x.S3AccessOption
		}
	}
	return nil
}

type isIAMAccessOption_AccessOption interface {
	isIAMAccessOption_AccessOption()
}

type IAMAccessOption_S3AccessOption struct {
	// If specified, the created IAM policy will grant access to the specified
	// S3 location.
	S3AccessOption *IAMS3AccessOption `protobuf:"bytes,1,opt,name=s3_access_option,json=s3AccessOption,proto3,oneof"`
}

func (*IAMAccessOption_S3AccessOption) isIAMAccessOption_AccessOption() {}

type CreateIAMPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMPolicyResponse) Reset() {
	*x = CreateIAMPolicyResponse{}
	mi := &file_services_aws_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMPolicyResponse) ProtoMessage() {}

func (x *CreateIAMPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMPolicyResponse.ProtoReflect.Descriptor instead.
func (*CreateIAMPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{9}
}

func (x *CreateIAMPolicyResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetIAMPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMPolicyRequest) Reset() {
	*x = GetIAMPolicyRequest{}
	mi := &file_services_aws_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMPolicyRequest) ProtoMessage() {}

func (x *GetIAMPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMPolicyRequest.ProtoReflect.Descriptor instead.
func (*GetIAMPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{10}
}

func (x *GetIAMPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetIAMPolicyResponse struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// ARN of the corresponding policy. Only populated when status is READY or
	// ALREAY_EXISTS
	PolicyArn     string `protobuf:"bytes,3,opt,name=policy_arn,json=policyArn,proto3" json:"policy_arn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMPolicyResponse) Reset() {
	*x = GetIAMPolicyResponse{}
	mi := &file_services_aws_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMPolicyResponse) ProtoMessage() {}

func (x *GetIAMPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMPolicyResponse.ProtoReflect.Descriptor instead.
func (*GetIAMPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{11}
}

func (x *GetIAMPolicyResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetIAMPolicyResponse) GetPolicyArn() string {
	if x != nil {
		return x.PolicyArn
	}
	return ""
}

type DeleteIAMPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMPolicyRequest) Reset() {
	*x = DeleteIAMPolicyRequest{}
	mi := &file_services_aws_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMPolicyRequest) ProtoMessage() {}

func (x *DeleteIAMPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMPolicyRequest.ProtoReflect.Descriptor instead.
func (*DeleteIAMPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteIAMPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteIAMPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMPolicyResponse) Reset() {
	*x = DeleteIAMPolicyResponse{}
	mi := &file_services_aws_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMPolicyResponse) ProtoMessage() {}

func (x *DeleteIAMPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMPolicyResponse.ProtoReflect.Descriptor instead.
func (*DeleteIAMPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteIAMPolicyResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateIAMRoleRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// The resource meta must meet the following restrictions:
	// - Name:
	//  1. contains at most 64 characters.
	//  2. contain only lowercase alphanumeric characters, '-' or '.'
	//  3. start with an alphanumeric character
	//  4. end with an alphanumeric character
	//
	// - Namespace:
	//  1. Namespace is REQUIRED
	//  2. Must be valid RFC 1123 Label Names, i.e.:
	//     a. contain at most 63 characters
	//     b. contain only lowercase alphanumeric characters or '-'
	//     c. start with an alphanumeric character
	//     d. end with an alphanumeric character
	ResourceMeta *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Metas of the attached AWS policies managed by agent.
	PolicyRefs []*resource.Meta `protobuf:"bytes,2,rep,name=policy_refs,json=policyRefs,proto3" json:"policy_refs,omitempty"`
	// K8s service account that is allowed to assume the created
	// role.
	ServiceAccount *k8s.ServiceAccount `protobuf:"bytes,3,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateIAMRoleRequest) Reset() {
	*x = CreateIAMRoleRequest{}
	mi := &file_services_aws_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMRoleRequest) ProtoMessage() {}

func (x *CreateIAMRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMRoleRequest.ProtoReflect.Descriptor instead.
func (*CreateIAMRoleRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{14}
}

func (x *CreateIAMRoleRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateIAMRoleRequest) GetPolicyRefs() []*resource.Meta {
	if x != nil {
		return x.PolicyRefs
	}
	return nil
}

func (x *CreateIAMRoleRequest) GetServiceAccount() *k8s.ServiceAccount {
	if x != nil {
		return x.ServiceAccount
	}
	return nil
}

type CreateIAMRoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateIAMRoleResponse) Reset() {
	*x = CreateIAMRoleResponse{}
	mi := &file_services_aws_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateIAMRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIAMRoleResponse) ProtoMessage() {}

func (x *CreateIAMRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIAMRoleResponse.ProtoReflect.Descriptor instead.
func (*CreateIAMRoleResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{15}
}

func (x *CreateIAMRoleResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetIAMRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMRoleRequest) Reset() {
	*x = GetIAMRoleRequest{}
	mi := &file_services_aws_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMRoleRequest) ProtoMessage() {}

func (x *GetIAMRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMRoleRequest.ProtoReflect.Descriptor instead.
func (*GetIAMRoleRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{16}
}

func (x *GetIAMRoleRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetIAMRoleResponse struct {
	state  protoimpl.MessageState `protogen:"open.v1"`
	Status *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	// ARN of the corresponding role. Only populated when status is READY or
	// ALREAY_EXISTS
	RoleArn       string `protobuf:"bytes,3,opt,name=role_arn,json=roleArn,proto3" json:"role_arn,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetIAMRoleResponse) Reset() {
	*x = GetIAMRoleResponse{}
	mi := &file_services_aws_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetIAMRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetIAMRoleResponse) ProtoMessage() {}

func (x *GetIAMRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetIAMRoleResponse.ProtoReflect.Descriptor instead.
func (*GetIAMRoleResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{17}
}

func (x *GetIAMRoleResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetIAMRoleResponse) GetRoleArn() string {
	if x != nil {
		return x.RoleArn
	}
	return ""
}

type DeleteIAMRoleRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMRoleRequest) Reset() {
	*x = DeleteIAMRoleRequest{}
	mi := &file_services_aws_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMRoleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMRoleRequest) ProtoMessage() {}

func (x *DeleteIAMRoleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMRoleRequest.ProtoReflect.Descriptor instead.
func (*DeleteIAMRoleRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{18}
}

func (x *DeleteIAMRoleRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteIAMRoleResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteIAMRoleResponse) Reset() {
	*x = DeleteIAMRoleResponse{}
	mi := &file_services_aws_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteIAMRoleResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteIAMRoleResponse) ProtoMessage() {}

func (x *DeleteIAMRoleResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteIAMRoleResponse.ProtoReflect.Descriptor instead.
func (*DeleteIAMRoleResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{19}
}

func (x *DeleteIAMRoleResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// IAMS3AccessOption specifies a S3 directory a AWS Policy should give access
// to.
// It will give access to all operations towards the directory.
type IAMS3AccessOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// S3 Bucket name.
	Bucket string `protobuf:"bytes,1,opt,name=bucket,proto3" json:"bucket,omitempty"`
	// The directory of the S3 bucket.
	//
	// Deprecated: Marked as deprecated in services/aws.proto.
	Dir string `protobuf:"bytes,2,opt,name=dir,proto3" json:"dir,omitempty"`
	// List of directories of the S3 bucket.
	Dirs          []string `protobuf:"bytes,3,rep,name=dirs,proto3" json:"dirs,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *IAMS3AccessOption) Reset() {
	*x = IAMS3AccessOption{}
	mi := &file_services_aws_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IAMS3AccessOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IAMS3AccessOption) ProtoMessage() {}

func (x *IAMS3AccessOption) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IAMS3AccessOption.ProtoReflect.Descriptor instead.
func (*IAMS3AccessOption) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{20}
}

func (x *IAMS3AccessOption) GetBucket() string {
	if x != nil {
		return x.Bucket
	}
	return ""
}

// Deprecated: Marked as deprecated in services/aws.proto.
func (x *IAMS3AccessOption) GetDir() string {
	if x != nil {
		return x.Dir
	}
	return ""
}

func (x *IAMS3AccessOption) GetDirs() []string {
	if x != nil {
		return x.Dirs
	}
	return nil
}

type IAMPrivateLinkAccessOption struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// ID of the SG the create PrivateLink can be associated with.
	// The SG must be in the same region as the agent and belongs to the same AWS
	// account.
	VpcEndpointsTagKey string `protobuf:"bytes,1,opt,name=vpc_endpoints_tag_key,json=vpcEndpointsTagKey,proto3" json:"vpc_endpoints_tag_key,omitempty"`
	VpcEndpointsTagVal string `protobuf:"bytes,2,opt,name=vpc_endpoints_tag_val,json=vpcEndpointsTagVal,proto3" json:"vpc_endpoints_tag_val,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *IAMPrivateLinkAccessOption) Reset() {
	*x = IAMPrivateLinkAccessOption{}
	mi := &file_services_aws_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *IAMPrivateLinkAccessOption) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IAMPrivateLinkAccessOption) ProtoMessage() {}

func (x *IAMPrivateLinkAccessOption) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IAMPrivateLinkAccessOption.ProtoReflect.Descriptor instead.
func (*IAMPrivateLinkAccessOption) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{21}
}

func (x *IAMPrivateLinkAccessOption) GetVpcEndpointsTagKey() string {
	if x != nil {
		return x.VpcEndpointsTagKey
	}
	return ""
}

func (x *IAMPrivateLinkAccessOption) GetVpcEndpointsTagVal() string {
	if x != nil {
		return x.VpcEndpointsTagVal
	}
	return ""
}

type DeleteVPCEndpointsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	TagKey        string                 `protobuf:"bytes,1,opt,name=tag_key,json=tagKey,proto3" json:"tag_key,omitempty"`
	TagValue      string                 `protobuf:"bytes,2,opt,name=tag_value,json=tagValue,proto3" json:"tag_value,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVPCEndpointsRequest) Reset() {
	*x = DeleteVPCEndpointsRequest{}
	mi := &file_services_aws_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVPCEndpointsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVPCEndpointsRequest) ProtoMessage() {}

func (x *DeleteVPCEndpointsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVPCEndpointsRequest.ProtoReflect.Descriptor instead.
func (*DeleteVPCEndpointsRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteVPCEndpointsRequest) GetTagKey() string {
	if x != nil {
		return x.TagKey
	}
	return ""
}

func (x *DeleteVPCEndpointsRequest) GetTagValue() string {
	if x != nil {
		return x.TagValue
	}
	return ""
}

type DeleteVPCEndpointsResponse struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	VpcEndpointIds []string               `protobuf:"bytes,1,rep,name=vpc_endpoint_ids,json=vpcEndpointIds,proto3" json:"vpc_endpoint_ids,omitempty"`
	ErrorMessage   string                 `protobuf:"bytes,2,opt,name=error_message,json=errorMessage,proto3" json:"error_message,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *DeleteVPCEndpointsResponse) Reset() {
	*x = DeleteVPCEndpointsResponse{}
	mi := &file_services_aws_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVPCEndpointsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVPCEndpointsResponse) ProtoMessage() {}

func (x *DeleteVPCEndpointsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVPCEndpointsResponse.ProtoReflect.Descriptor instead.
func (*DeleteVPCEndpointsResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteVPCEndpointsResponse) GetVpcEndpointIds() []string {
	if x != nil {
		return x.VpcEndpointIds
	}
	return nil
}

func (x *DeleteVPCEndpointsResponse) GetErrorMessage() string {
	if x != nil {
		return x.ErrorMessage
	}
	return ""
}

type CreateSecurityGroupPolicyRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta      *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SecurityGroupIds  []string               `protobuf:"bytes,2,rep,name=security_group_ids,json=securityGroupIds,proto3" json:"security_group_ids,omitempty"`
	PodLabelsSelector map[string]string      `protobuf:"bytes,3,rep,name=pod_labels_selector,json=podLabelsSelector,proto3" json:"pod_labels_selector,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateSecurityGroupPolicyRequest) Reset() {
	*x = CreateSecurityGroupPolicyRequest{}
	mi := &file_services_aws_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSecurityGroupPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecurityGroupPolicyRequest) ProtoMessage() {}

func (x *CreateSecurityGroupPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecurityGroupPolicyRequest.ProtoReflect.Descriptor instead.
func (*CreateSecurityGroupPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{24}
}

func (x *CreateSecurityGroupPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateSecurityGroupPolicyRequest) GetSecurityGroupIds() []string {
	if x != nil {
		return x.SecurityGroupIds
	}
	return nil
}

func (x *CreateSecurityGroupPolicyRequest) GetPodLabelsSelector() map[string]string {
	if x != nil {
		return x.PodLabelsSelector
	}
	return nil
}

type CreateSecurityGroupPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSecurityGroupPolicyResponse) Reset() {
	*x = CreateSecurityGroupPolicyResponse{}
	mi := &file_services_aws_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSecurityGroupPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecurityGroupPolicyResponse) ProtoMessage() {}

func (x *CreateSecurityGroupPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecurityGroupPolicyResponse.ProtoReflect.Descriptor instead.
func (*CreateSecurityGroupPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{25}
}

func (x *CreateSecurityGroupPolicyResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteSecurityGroupPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSecurityGroupPolicyRequest) Reset() {
	*x = DeleteSecurityGroupPolicyRequest{}
	mi := &file_services_aws_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSecurityGroupPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecurityGroupPolicyRequest) ProtoMessage() {}

func (x *DeleteSecurityGroupPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecurityGroupPolicyRequest.ProtoReflect.Descriptor instead.
func (*DeleteSecurityGroupPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{26}
}

func (x *DeleteSecurityGroupPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteSecurityGroupPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSecurityGroupPolicyResponse) Reset() {
	*x = DeleteSecurityGroupPolicyResponse{}
	mi := &file_services_aws_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSecurityGroupPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecurityGroupPolicyResponse) ProtoMessage() {}

func (x *DeleteSecurityGroupPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecurityGroupPolicyResponse.ProtoReflect.Descriptor instead.
func (*DeleteSecurityGroupPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{27}
}

func (x *DeleteSecurityGroupPolicyResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSecurityGroupPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSecurityGroupPolicyRequest) Reset() {
	*x = GetSecurityGroupPolicyRequest{}
	mi := &file_services_aws_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSecurityGroupPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityGroupPolicyRequest) ProtoMessage() {}

func (x *GetSecurityGroupPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityGroupPolicyRequest.ProtoReflect.Descriptor instead.
func (*GetSecurityGroupPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{28}
}

func (x *GetSecurityGroupPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetSecurityGroupPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSecurityGroupPolicyResponse) Reset() {
	*x = GetSecurityGroupPolicyResponse{}
	mi := &file_services_aws_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSecurityGroupPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecurityGroupPolicyResponse) ProtoMessage() {}

func (x *GetSecurityGroupPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecurityGroupPolicyResponse.ProtoReflect.Descriptor instead.
func (*GetSecurityGroupPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{29}
}

func (x *GetSecurityGroupPolicyResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CheckVPCEndpointServiceReachabilityRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ServiceName   string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVPCEndpointServiceReachabilityRequest) Reset() {
	*x = CheckVPCEndpointServiceReachabilityRequest{}
	mi := &file_services_aws_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVPCEndpointServiceReachabilityRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVPCEndpointServiceReachabilityRequest) ProtoMessage() {}

func (x *CheckVPCEndpointServiceReachabilityRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVPCEndpointServiceReachabilityRequest.ProtoReflect.Descriptor instead.
func (*CheckVPCEndpointServiceReachabilityRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{30}
}

func (x *CheckVPCEndpointServiceReachabilityRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CheckVPCEndpointServiceReachabilityRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

type CheckVPCEndpointServiceReachabilityResponse struct {
	state         protoimpl.MessageState               `protogen:"open.v1"`
	ResourceMeta  *resource.Meta                       `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Status        VPCEndpointServiceReachabilityStatus `protobuf:"varint,2,opt,name=status,proto3,enum=services.aws.VPCEndpointServiceReachabilityStatus" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CheckVPCEndpointServiceReachabilityResponse) Reset() {
	*x = CheckVPCEndpointServiceReachabilityResponse{}
	mi := &file_services_aws_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CheckVPCEndpointServiceReachabilityResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CheckVPCEndpointServiceReachabilityResponse) ProtoMessage() {}

func (x *CheckVPCEndpointServiceReachabilityResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CheckVPCEndpointServiceReachabilityResponse.ProtoReflect.Descriptor instead.
func (*CheckVPCEndpointServiceReachabilityResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{31}
}

func (x *CheckVPCEndpointServiceReachabilityResponse) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CheckVPCEndpointServiceReachabilityResponse) GetStatus() VPCEndpointServiceReachabilityStatus {
	if x != nil {
		return x.Status
	}
	return VPCEndpointServiceReachabilityStatus_REACHABILITY_UNSPECIFIED
}

type CreateVPCEndpointRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta      *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ServiceName       string                 `protobuf:"bytes,2,opt,name=service_name,json=serviceName,proto3" json:"service_name,omitempty"`
	SecurityGroupIds  []string               `protobuf:"bytes,3,rep,name=security_group_ids,json=securityGroupIds,proto3" json:"security_group_ids,omitempty"`
	SubnetIds         []string               `protobuf:"bytes,4,rep,name=subnet_ids,json=subnetIds,proto3" json:"subnet_ids,omitempty"`
	PrivateDnsEnabled bool                   `protobuf:"varint,5,opt,name=private_dns_enabled,json=privateDnsEnabled,proto3" json:"private_dns_enabled,omitempty"`
	ExtraTags         map[string]string      `protobuf:"bytes,6,rep,name=extra_tags,json=extraTags,proto3" json:"extra_tags,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreateVPCEndpointRequest) Reset() {
	*x = CreateVPCEndpointRequest{}
	mi := &file_services_aws_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVPCEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVPCEndpointRequest) ProtoMessage() {}

func (x *CreateVPCEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVPCEndpointRequest.ProtoReflect.Descriptor instead.
func (*CreateVPCEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{32}
}

func (x *CreateVPCEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateVPCEndpointRequest) GetServiceName() string {
	if x != nil {
		return x.ServiceName
	}
	return ""
}

func (x *CreateVPCEndpointRequest) GetSecurityGroupIds() []string {
	if x != nil {
		return x.SecurityGroupIds
	}
	return nil
}

func (x *CreateVPCEndpointRequest) GetSubnetIds() []string {
	if x != nil {
		return x.SubnetIds
	}
	return nil
}

func (x *CreateVPCEndpointRequest) GetPrivateDnsEnabled() bool {
	if x != nil {
		return x.PrivateDnsEnabled
	}
	return false
}

func (x *CreateVPCEndpointRequest) GetExtraTags() map[string]string {
	if x != nil {
		return x.ExtraTags
	}
	return nil
}

type CreateVPCEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateVPCEndpointResponse) Reset() {
	*x = CreateVPCEndpointResponse{}
	mi := &file_services_aws_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateVPCEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVPCEndpointResponse) ProtoMessage() {}

func (x *CreateVPCEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVPCEndpointResponse.ProtoReflect.Descriptor instead.
func (*CreateVPCEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{33}
}

func (x *CreateVPCEndpointResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteVPCEndpointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVPCEndpointRequest) Reset() {
	*x = DeleteVPCEndpointRequest{}
	mi := &file_services_aws_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVPCEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVPCEndpointRequest) ProtoMessage() {}

func (x *DeleteVPCEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVPCEndpointRequest.ProtoReflect.Descriptor instead.
func (*DeleteVPCEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{34}
}

func (x *DeleteVPCEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteVPCEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteVPCEndpointResponse) Reset() {
	*x = DeleteVPCEndpointResponse{}
	mi := &file_services_aws_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteVPCEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVPCEndpointResponse) ProtoMessage() {}

func (x *DeleteVPCEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVPCEndpointResponse.ProtoReflect.Descriptor instead.
func (*DeleteVPCEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{35}
}

func (x *DeleteVPCEndpointResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetVPCEndpointRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVPCEndpointRequest) Reset() {
	*x = GetVPCEndpointRequest{}
	mi := &file_services_aws_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVPCEndpointRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVPCEndpointRequest) ProtoMessage() {}

func (x *GetVPCEndpointRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVPCEndpointRequest.ProtoReflect.Descriptor instead.
func (*GetVPCEndpointRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{36}
}

func (x *GetVPCEndpointRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetVPCEndpointResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	EndpointId    string                 `protobuf:"bytes,2,opt,name=endpoint_id,json=endpointId,proto3" json:"endpoint_id,omitempty"`
	EndpointState VPCEndpointStatus      `protobuf:"varint,3,opt,name=endpoint_state,json=endpointState,proto3,enum=services.aws.VPCEndpointStatus" json:"endpoint_state,omitempty"`
	EndpointDns   string                 `protobuf:"bytes,4,opt,name=endpoint_dns,json=endpointDns,proto3" json:"endpoint_dns,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetVPCEndpointResponse) Reset() {
	*x = GetVPCEndpointResponse{}
	mi := &file_services_aws_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetVPCEndpointResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVPCEndpointResponse) ProtoMessage() {}

func (x *GetVPCEndpointResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVPCEndpointResponse.ProtoReflect.Descriptor instead.
func (*GetVPCEndpointResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{37}
}

func (x *GetVPCEndpointResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetVPCEndpointResponse) GetEndpointId() string {
	if x != nil {
		return x.EndpointId
	}
	return ""
}

func (x *GetVPCEndpointResponse) GetEndpointState() VPCEndpointStatus {
	if x != nil {
		return x.EndpointState
	}
	return VPCEndpointStatus_STATUS_UNSPECIFIED
}

func (x *GetVPCEndpointResponse) GetEndpointDns() string {
	if x != nil {
		return x.EndpointDns
	}
	return ""
}

type CreateDBInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Spec          *aws.DBInstanceSpec    `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDBInstanceRequest) Reset() {
	*x = CreateDBInstanceRequest{}
	mi := &file_services_aws_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDBInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDBInstanceRequest) ProtoMessage() {}

func (x *CreateDBInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDBInstanceRequest.ProtoReflect.Descriptor instead.
func (*CreateDBInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{38}
}

func (x *CreateDBInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateDBInstanceRequest) GetSpec() *aws.DBInstanceSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
type CreateDBInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateDBInstanceResponse) Reset() {
	*x = CreateDBInstanceResponse{}
	mi := &file_services_aws_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateDBInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDBInstanceResponse) ProtoMessage() {}

func (x *CreateDBInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDBInstanceResponse.ProtoReflect.Descriptor instead.
func (*CreateDBInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{39}
}

func (x *CreateDBInstanceResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteDBInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDBInstanceRequest) Reset() {
	*x = DeleteDBInstanceRequest{}
	mi := &file_services_aws_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDBInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDBInstanceRequest) ProtoMessage() {}

func (x *DeleteDBInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDBInstanceRequest.ProtoReflect.Descriptor instead.
func (*DeleteDBInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{40}
}

func (x *DeleteDBInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, NOT_FOUND.
type DeleteDBInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteDBInstanceResponse) Reset() {
	*x = DeleteDBInstanceResponse{}
	mi := &file_services_aws_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteDBInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDBInstanceResponse) ProtoMessage() {}

func (x *DeleteDBInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDBInstanceResponse.ProtoReflect.Descriptor instead.
func (*DeleteDBInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{41}
}

func (x *DeleteDBInstanceResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StartDBInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartDBInstanceRequest) Reset() {
	*x = StartDBInstanceRequest{}
	mi := &file_services_aws_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartDBInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDBInstanceRequest) ProtoMessage() {}

func (x *StartDBInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDBInstanceRequest.ProtoReflect.Descriptor instead.
func (*StartDBInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{42}
}

func (x *StartDBInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
type StartDBInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartDBInstanceResponse) Reset() {
	*x = StartDBInstanceResponse{}
	mi := &file_services_aws_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartDBInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartDBInstanceResponse) ProtoMessage() {}

func (x *StartDBInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartDBInstanceResponse.ProtoReflect.Descriptor instead.
func (*StartDBInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{43}
}

func (x *StartDBInstanceResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type StopDBInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopDBInstanceRequest) Reset() {
	*x = StopDBInstanceRequest{}
	mi := &file_services_aws_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopDBInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDBInstanceRequest) ProtoMessage() {}

func (x *StopDBInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDBInstanceRequest.ProtoReflect.Descriptor instead.
func (*StopDBInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{44}
}

func (x *StopDBInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
type StopDBInstanceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopDBInstanceResponse) Reset() {
	*x = StopDBInstanceResponse{}
	mi := &file_services_aws_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopDBInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopDBInstanceResponse) ProtoMessage() {}

func (x *StopDBInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopDBInstanceResponse.ProtoReflect.Descriptor instead.
func (*StopDBInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{45}
}

func (x *StopDBInstanceResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetDBInstanceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDBInstanceRequest) Reset() {
	*x = GetDBInstanceRequest{}
	mi := &file_services_aws_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDBInstanceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDBInstanceRequest) ProtoMessage() {}

func (x *GetDBInstanceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDBInstanceRequest.ProtoReflect.Descriptor instead.
func (*GetDBInstanceRequest) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{46}
}

func (x *GetDBInstanceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetDBInstanceResponse struct {
	state          protoimpl.MessageState  `protogen:"open.v1"`
	Status         *resource.Status        `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	Endpoint       *aws.DBInstanceEndpoint `protobuf:"bytes,2,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	InstanceStatus aws.DBInstanceStatus    `protobuf:"varint,3,opt,name=instance_status,json=instanceStatus,proto3,enum=common.aws.DBInstanceStatus" json:"instance_status,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetDBInstanceResponse) Reset() {
	*x = GetDBInstanceResponse{}
	mi := &file_services_aws_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDBInstanceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDBInstanceResponse) ProtoMessage() {}

func (x *GetDBInstanceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_aws_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDBInstanceResponse.ProtoReflect.Descriptor instead.
func (*GetDBInstanceResponse) Descriptor() ([]byte, []int) {
	return file_services_aws_proto_rawDescGZIP(), []int{47}
}

func (x *GetDBInstanceResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetDBInstanceResponse) GetEndpoint() *aws.DBInstanceEndpoint {
	if x != nil {
		return x.Endpoint
	}
	return nil
}

func (x *GetDBInstanceResponse) GetInstanceStatus() aws.DBInstanceStatus {
	if x != nil {
		return x.InstanceStatus
	}
	return aws.DBInstanceStatus(0)
}

var File_services_aws_proto protoreflect.FileDescriptor

var file_services_aws_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x61, 0x77, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61,
	0x77, 0x73, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x61, 0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1a, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd3, 0x01, 0x0a,
	0x0c, 0x49, 0x50, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x1a, 0x0a,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1b, 0x0a, 0x09, 0x66, 0x72, 0x6f,
	0x6d, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x66, 0x72,
	0x6f, 0x6d, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x6f, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74, 0x6f, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x14, 0x0a, 0x05, 0x63, 0x69, 0x64, 0x72, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05,
	0x63, 0x69, 0x64, 0x72, 0x73, 0x12, 0x39, 0x0a, 0x19, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69,
	0x64, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x16, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x22, 0xfe, 0x01, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x52, 0x0a,
	0x17, 0x6f, 0x75, 0x74, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x70, 0x5f, 0x70, 0x65, 0x72,
	0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x49, 0x50,
	0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x15, 0x6f, 0x75, 0x74, 0x62,
	0x6f, 0x75, 0x6e, 0x64, 0x49, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e,
	0x73, 0x12, 0x50, 0x0a, 0x16, 0x69, 0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x5f, 0x69, 0x70, 0x5f,
	0x70, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73,
	0x2e, 0x49, 0x50, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x69,
	0x6e, 0x62, 0x6f, 0x75, 0x6e, 0x64, 0x49, 0x70, 0x50, 0x65, 0x72, 0x6d, 0x69, 0x73, 0x73, 0x69,
	0x6f, 0x6e, 0x73, 0x22, 0x57, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x58, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x55, 0x0a, 0x17, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x77, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63,
	0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x22,
	0x9a, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x44, 0x0a, 0x0e, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73,
	0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x1d,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x49, 0x41,
	0x4d, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x0d, 0x61,
	0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x22, 0x75, 0x0a, 0x0f,
	0x49, 0x41, 0x4d, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x4b, 0x0a, 0x10, 0x73, 0x33, 0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x49, 0x41, 0x4d, 0x53, 0x33, 0x41, 0x63,
	0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x33,
	0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d,
	0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6f, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4a, 0x04, 0x08,
	0x02, 0x10, 0x03, 0x22, 0x53, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x51, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x49,
	0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x66, 0x0a, 0x14, 0x47,
	0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x61,
	0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x41, 0x72, 0x6e, 0x22, 0x54, 0x0a, 0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x17, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcf,
	0x01, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x72, 0x65,
	0x66, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x66, 0x73, 0x12, 0x43, 0x0a, 0x0f, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x0e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x22, 0x51, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x4f, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x52, 0x6f,
	0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x19, 0x0a, 0x08, 0x72,
	0x6f, 0x6c, 0x65, 0x5f, 0x61, 0x72, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x72,
	0x6f, 0x6c, 0x65, 0x41, 0x72, 0x6e, 0x22, 0x52, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x15, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a,
	0x11, 0x49, 0x41, 0x4d, 0x53, 0x33, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x16, 0x0a, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x62, 0x75, 0x63, 0x6b, 0x65, 0x74, 0x12, 0x14, 0x0a, 0x03, 0x64, 0x69,
	0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01, 0x52, 0x03, 0x64, 0x69, 0x72,
	0x12, 0x12, 0x0a, 0x04, 0x64, 0x69, 0x72, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04,
	0x64, 0x69, 0x72, 0x73, 0x22, 0x82, 0x01, 0x0a, 0x1a, 0x49, 0x41, 0x4d, 0x50, 0x72, 0x69, 0x76,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x6e, 0x6b, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4f, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x31, 0x0a, 0x15, 0x76, 0x70, 0x63, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x73, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x12, 0x76, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73,
	0x54, 0x61, 0x67, 0x4b, 0x65, 0x79, 0x12, 0x31, 0x0a, 0x15, 0x76, 0x70, 0x63, 0x5f, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x5f, 0x74, 0x61, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12, 0x76, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x73, 0x54, 0x61, 0x67, 0x56, 0x61, 0x6c, 0x22, 0x51, 0x0a, 0x19, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x67, 0x5f, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x67, 0x4b, 0x65, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x61, 0x67, 0x5f, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x67, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0x6b, 0x0a, 0x1a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x28, 0x0a, 0x10, 0x76, 0x70,
	0x63, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x76, 0x70, 0x63, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x49, 0x64, 0x73, 0x12, 0x23, 0x0a, 0x0d, 0x65, 0x72, 0x72, 0x6f, 0x72, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x72, 0x72,
	0x6f, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0xc9, 0x02, 0x0a, 0x20, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x64, 0x73, 0x12, 0x75, 0x0a, 0x13, 0x70, 0x6f, 0x64, 0x5f,
	0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x18,
	0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x45, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x50, 0x6f, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x53,
	0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x11, 0x70, 0x6f,
	0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x53, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x1a,
	0x44, 0x0a, 0x16, 0x50, 0x6f, 0x64, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x53, 0x65, 0x6c, 0x65,
	0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x5d, 0x0a, 0x21, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x5e, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x5d, 0x0a, 0x21, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x5b, 0x0a, 0x1d, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69,
	0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x22, 0x51, 0x0a, 0x1e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x8b, 0x01, 0x0a, 0x2a, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x50, 0x43,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x21,
	0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xb5, 0x01, 0x0a, 0x2b, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x50, 0x43, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x61,
	0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65,
	0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4a, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x56, 0x50, 0x43,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x8a, 0x03, 0x0a, 0x18, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x21, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2c, 0x0a, 0x12, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x69, 0x64, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x49, 0x64, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x69, 0x64,
	0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x49,
	0x64, 0x73, 0x12, 0x2e, 0x0a, 0x13, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x5f, 0x64, 0x6e,
	0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x11, 0x70, 0x72, 0x69, 0x76, 0x61, 0x74, 0x65, 0x44, 0x6e, 0x73, 0x45, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x12, 0x54, 0x0a, 0x0a, 0x65, 0x78, 0x74, 0x72, 0x61, 0x5f, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45,
	0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x45,
	0x78, 0x74, 0x72, 0x61, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x09, 0x65,
	0x78, 0x74, 0x72, 0x61, 0x54, 0x61, 0x67, 0x73, 0x1a, 0x3c, 0x0a, 0x0e, 0x45, 0x78, 0x74, 0x72,
	0x61, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x55, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x56, 0x0a,
	0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x55, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56,
	0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x53, 0x0a, 0x15,
	0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x22, 0xd5, 0x01, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70,
	0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1f, 0x0a,
	0x0b, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x46,
	0x0a, 0x0e, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0d, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69,
	0x6e, 0x74, 0x5f, 0x64, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x44, 0x6e, 0x73, 0x22, 0x85, 0x01, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x12, 0x2e, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x42, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65,
	0x63, 0x22, 0x54, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x42, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x54,
	0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x54, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x42, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x51, 0x0a, 0x17, 0x53, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x53, 0x0a,
	0x15, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x22, 0x50, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0xcb, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74,
	0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3a, 0x0a, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x61,
	0x77, 0x73, 0x2e, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x08, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12,
	0x45, 0x0a, 0x0f, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x1c, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2a, 0x60, 0x0a, 0x24, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x61, 0x63,
	0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x1c,
	0x0a, 0x18, 0x52, 0x45, 0x41, 0x43, 0x48, 0x41, 0x42, 0x49, 0x4c, 0x49, 0x54, 0x59, 0x5f, 0x55,
	0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07,
	0x53, 0x55, 0x43, 0x43, 0x45, 0x53, 0x53, 0x10, 0x01, 0x12, 0x0d, 0x0a, 0x09, 0x4e, 0x4f, 0x54,
	0x5f, 0x46, 0x4f, 0x55, 0x4e, 0x44, 0x10, 0x02, 0x2a, 0xa1, 0x01, 0x0a, 0x11, 0x56, 0x50, 0x43,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x12, 0x53, 0x54, 0x41, 0x54, 0x55, 0x53, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49,
	0x46, 0x49, 0x45, 0x44, 0x10, 0x00, 0x12, 0x16, 0x0a, 0x12, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x5f, 0x41, 0x43, 0x43, 0x45, 0x50, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x50, 0x45, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x41,
	0x56, 0x41, 0x49, 0x4c, 0x41, 0x42, 0x4c, 0x45, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45,
	0x4a, 0x45, 0x43, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10,
	0x06, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x07, 0x12,
	0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x08, 0x32, 0xb7, 0x15, 0x0a,
	0x12, 0x41, 0x77, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x72, 0x12, 0x92, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61,
	0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x79, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x89, 0x01, 0x0a, 0x1c, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79,
	0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x79, 0x43, 0x6c, 0x6f,
	0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x33, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x44, 0x61, 0x74, 0x61, 0x44, 0x69, 0x72, 0x65, 0x63, 0x74, 0x6f, 0x72,
	0x79, 0x43, 0x6c, 0x6f, 0x6e, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x28, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72,
	0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x6c, 0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61,
	0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74,
	0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x63, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x61, 0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65,
	0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77,
	0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77,
	0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77,
	0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77,
	0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x16, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x75,
	0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x53, 0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x75, 0x72, 0x69, 0x74, 0x79, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12,
	0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60,
	0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x57, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61,
	0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49,
	0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x41, 0x4d, 0x52,
	0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x51, 0x0a, 0x0a, 0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x12,
	0x1f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47,
	0x65, 0x74, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x20, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x49, 0x41, 0x4d, 0x52, 0x6f, 0x6c, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x00, 0x12, 0x9c, 0x01, 0x0a, 0x23, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x50,
	0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x12, 0x38, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x52, 0x65, 0x61, 0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x39, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x56, 0x50, 0x43, 0x45, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x61,
	0x63, 0x68, 0x61, 0x62, 0x69, 0x6c, 0x69, 0x74, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50,
	0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74,
	0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50,
	0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64,
	0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x45, 0x6e, 0x64, 0x70, 0x6f,
	0x69, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43,
	0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x92, 0x01, 0x0a, 0x1f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d,
	0x70, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x69, 0x6d, 0x70, 0x6c, 0x65, 0x44, 0x61, 0x74, 0x61, 0x52, 0x65,
	0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x25, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77,
	0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x42, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x60, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x61, 0x77, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x44,
	0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x42, 0x49, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x61, 0x77, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x44, 0x42,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x61,
	0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x42, 0x49, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x56,
	0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x12, 0x21, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x47, 0x65,
	0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x64, 0x61, 0x74, 0x61,
	0x2e, 0x47, 0x65, 0x74, 0x4d, 0x61, 0x6e, 0x69, 0x66, 0x65, 0x73, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62,
	0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c,
	0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70,
	0x62, 0x67, 0x65, 0x6e, 0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x61, 0x77,
	0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_aws_proto_rawDescOnce sync.Once
	file_services_aws_proto_rawDescData []byte
)

func file_services_aws_proto_rawDescGZIP() []byte {
	file_services_aws_proto_rawDescOnce.Do(func() {
		file_services_aws_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_aws_proto_rawDesc), len(file_services_aws_proto_rawDesc)))
	})
	return file_services_aws_proto_rawDescData
}

var file_services_aws_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_services_aws_proto_msgTypes = make([]protoimpl.MessageInfo, 50)
var file_services_aws_proto_goTypes = []any{
	(VPCEndpointServiceReachabilityStatus)(0),            // 0: services.aws.VPCEndpointServiceReachabilityStatus
	(VPCEndpointStatus)(0),                               // 1: services.aws.VPCEndpointStatus
	(*IPPermission)(nil),                                 // 2: services.aws.IPPermission
	(*CreateSecurityGroupRequest)(nil),                   // 3: services.aws.CreateSecurityGroupRequest
	(*CreateSecurityGroupResponse)(nil),                  // 4: services.aws.CreateSecurityGroupResponse
	(*DeleteSecurityGroupRequest)(nil),                   // 5: services.aws.DeleteSecurityGroupRequest
	(*DeleteSecurityGroupResponse)(nil),                  // 6: services.aws.DeleteSecurityGroupResponse
	(*GetSecurityGroupRequest)(nil),                      // 7: services.aws.GetSecurityGroupRequest
	(*GetSecurityGroupResponse)(nil),                     // 8: services.aws.GetSecurityGroupResponse
	(*CreateIAMPolicyRequest)(nil),                       // 9: services.aws.CreateIAMPolicyRequest
	(*IAMAccessOption)(nil),                              // 10: services.aws.IAMAccessOption
	(*CreateIAMPolicyResponse)(nil),                      // 11: services.aws.CreateIAMPolicyResponse
	(*GetIAMPolicyRequest)(nil),                          // 12: services.aws.GetIAMPolicyRequest
	(*GetIAMPolicyResponse)(nil),                         // 13: services.aws.GetIAMPolicyResponse
	(*DeleteIAMPolicyRequest)(nil),                       // 14: services.aws.DeleteIAMPolicyRequest
	(*DeleteIAMPolicyResponse)(nil),                      // 15: services.aws.DeleteIAMPolicyResponse
	(*CreateIAMRoleRequest)(nil),                         // 16: services.aws.CreateIAMRoleRequest
	(*CreateIAMRoleResponse)(nil),                        // 17: services.aws.CreateIAMRoleResponse
	(*GetIAMRoleRequest)(nil),                            // 18: services.aws.GetIAMRoleRequest
	(*GetIAMRoleResponse)(nil),                           // 19: services.aws.GetIAMRoleResponse
	(*DeleteIAMRoleRequest)(nil),                         // 20: services.aws.DeleteIAMRoleRequest
	(*DeleteIAMRoleResponse)(nil),                        // 21: services.aws.DeleteIAMRoleResponse
	(*IAMS3AccessOption)(nil),                            // 22: services.aws.IAMS3AccessOption
	(*IAMPrivateLinkAccessOption)(nil),                   // 23: services.aws.IAMPrivateLinkAccessOption
	(*DeleteVPCEndpointsRequest)(nil),                    // 24: services.aws.DeleteVPCEndpointsRequest
	(*DeleteVPCEndpointsResponse)(nil),                   // 25: services.aws.DeleteVPCEndpointsResponse
	(*CreateSecurityGroupPolicyRequest)(nil),             // 26: services.aws.CreateSecurityGroupPolicyRequest
	(*CreateSecurityGroupPolicyResponse)(nil),            // 27: services.aws.CreateSecurityGroupPolicyResponse
	(*DeleteSecurityGroupPolicyRequest)(nil),             // 28: services.aws.DeleteSecurityGroupPolicyRequest
	(*DeleteSecurityGroupPolicyResponse)(nil),            // 29: services.aws.DeleteSecurityGroupPolicyResponse
	(*GetSecurityGroupPolicyRequest)(nil),                // 30: services.aws.GetSecurityGroupPolicyRequest
	(*GetSecurityGroupPolicyResponse)(nil),               // 31: services.aws.GetSecurityGroupPolicyResponse
	(*CheckVPCEndpointServiceReachabilityRequest)(nil),   // 32: services.aws.CheckVPCEndpointServiceReachabilityRequest
	(*CheckVPCEndpointServiceReachabilityResponse)(nil),  // 33: services.aws.CheckVPCEndpointServiceReachabilityResponse
	(*CreateVPCEndpointRequest)(nil),                     // 34: services.aws.CreateVPCEndpointRequest
	(*CreateVPCEndpointResponse)(nil),                    // 35: services.aws.CreateVPCEndpointResponse
	(*DeleteVPCEndpointRequest)(nil),                     // 36: services.aws.DeleteVPCEndpointRequest
	(*DeleteVPCEndpointResponse)(nil),                    // 37: services.aws.DeleteVPCEndpointResponse
	(*GetVPCEndpointRequest)(nil),                        // 38: services.aws.GetVPCEndpointRequest
	(*GetVPCEndpointResponse)(nil),                       // 39: services.aws.GetVPCEndpointResponse
	(*CreateDBInstanceRequest)(nil),                      // 40: services.aws.CreateDBInstanceRequest
	(*CreateDBInstanceResponse)(nil),                     // 41: services.aws.CreateDBInstanceResponse
	(*DeleteDBInstanceRequest)(nil),                      // 42: services.aws.DeleteDBInstanceRequest
	(*DeleteDBInstanceResponse)(nil),                     // 43: services.aws.DeleteDBInstanceResponse
	(*StartDBInstanceRequest)(nil),                       // 44: services.aws.StartDBInstanceRequest
	(*StartDBInstanceResponse)(nil),                      // 45: services.aws.StartDBInstanceResponse
	(*StopDBInstanceRequest)(nil),                        // 46: services.aws.StopDBInstanceRequest
	(*StopDBInstanceResponse)(nil),                       // 47: services.aws.StopDBInstanceResponse
	(*GetDBInstanceRequest)(nil),                         // 48: services.aws.GetDBInstanceRequest
	(*GetDBInstanceResponse)(nil),                        // 49: services.aws.GetDBInstanceResponse
	nil,                                                  // 50: services.aws.CreateSecurityGroupPolicyRequest.PodLabelsSelectorEntry
	nil,                                                  // 51: services.aws.CreateVPCEndpointRequest.ExtraTagsEntry
	(*resource.Meta)(nil),                                // 52: common.resource.Meta
	(*creation.Status)(nil),                              // 53: common.resource.creation.Status
	(*deletion.Status)(nil),                              // 54: common.resource.deletion.Status
	(*resource.Status)(nil),                              // 55: common.resource.Status
	(*k8s.ServiceAccount)(nil),                           // 56: common.k8s.ServiceAccount
	(*aws.DBInstanceSpec)(nil),                           // 57: common.aws.DBInstanceSpec
	(*update.Status)(nil),                                // 58: common.resource.update.Status
	(*aws.DBInstanceEndpoint)(nil),                       // 59: common.aws.DBInstanceEndpoint
	(aws.DBInstanceStatus)(0),                            // 60: common.aws.DBInstanceStatus
	(*data.CreateDataDirectoryDeletionTaskRequest)(nil),  // 61: services.data.CreateDataDirectoryDeletionTaskRequest
	(*data.CreateDataDirectoryCloneTaskRequest)(nil),     // 62: services.data.CreateDataDirectoryCloneTaskRequest
	(*data.CreateSimpleDataReplicationTaskRequest)(nil),  // 63: services.data.CreateSimpleDataReplicationTaskRequest
	(*data.GetManifestRequest)(nil),                      // 64: services.data.GetManifestRequest
	(*data.CreateDataDirectoryDeletionTaskResponse)(nil), // 65: services.data.CreateDataDirectoryDeletionTaskResponse
	(*data.CreateDataDirectoryCloneTaskResponse)(nil),    // 66: services.data.CreateDataDirectoryCloneTaskResponse
	(*data.CreateSimpleDataReplicationTaskResponse)(nil), // 67: services.data.CreateSimpleDataReplicationTaskResponse
	(*data.GetManifestResponse)(nil),                     // 68: services.data.GetManifestResponse
}
var file_services_aws_proto_depIdxs = []int32{
	52, // 0: services.aws.CreateSecurityGroupRequest.resource_meta:type_name -> common.resource.Meta
	2,  // 1: services.aws.CreateSecurityGroupRequest.outbound_ip_permissions:type_name -> services.aws.IPPermission
	2,  // 2: services.aws.CreateSecurityGroupRequest.inbound_ip_permissions:type_name -> services.aws.IPPermission
	53, // 3: services.aws.CreateSecurityGroupResponse.status:type_name -> common.resource.creation.Status
	52, // 4: services.aws.DeleteSecurityGroupRequest.resource_meta:type_name -> common.resource.Meta
	54, // 5: services.aws.DeleteSecurityGroupResponse.status:type_name -> common.resource.deletion.Status
	52, // 6: services.aws.GetSecurityGroupRequest.resource_meta:type_name -> common.resource.Meta
	55, // 7: services.aws.GetSecurityGroupResponse.status:type_name -> common.resource.Status
	52, // 8: services.aws.CreateIAMPolicyRequest.resource_meta:type_name -> common.resource.Meta
	10, // 9: services.aws.CreateIAMPolicyRequest.access_options:type_name -> services.aws.IAMAccessOption
	22, // 10: services.aws.IAMAccessOption.s3_access_option:type_name -> services.aws.IAMS3AccessOption
	53, // 11: services.aws.CreateIAMPolicyResponse.status:type_name -> common.resource.creation.Status
	52, // 12: services.aws.GetIAMPolicyRequest.resource_meta:type_name -> common.resource.Meta
	55, // 13: services.aws.GetIAMPolicyResponse.status:type_name -> common.resource.Status
	52, // 14: services.aws.DeleteIAMPolicyRequest.resource_meta:type_name -> common.resource.Meta
	54, // 15: services.aws.DeleteIAMPolicyResponse.status:type_name -> common.resource.deletion.Status
	52, // 16: services.aws.CreateIAMRoleRequest.resource_meta:type_name -> common.resource.Meta
	52, // 17: services.aws.CreateIAMRoleRequest.policy_refs:type_name -> common.resource.Meta
	56, // 18: services.aws.CreateIAMRoleRequest.service_account:type_name -> common.k8s.ServiceAccount
	53, // 19: services.aws.CreateIAMRoleResponse.status:type_name -> common.resource.creation.Status
	52, // 20: services.aws.GetIAMRoleRequest.resource_meta:type_name -> common.resource.Meta
	55, // 21: services.aws.GetIAMRoleResponse.status:type_name -> common.resource.Status
	52, // 22: services.aws.DeleteIAMRoleRequest.resource_meta:type_name -> common.resource.Meta
	54, // 23: services.aws.DeleteIAMRoleResponse.status:type_name -> common.resource.deletion.Status
	52, // 24: services.aws.CreateSecurityGroupPolicyRequest.resource_meta:type_name -> common.resource.Meta
	50, // 25: services.aws.CreateSecurityGroupPolicyRequest.pod_labels_selector:type_name -> services.aws.CreateSecurityGroupPolicyRequest.PodLabelsSelectorEntry
	53, // 26: services.aws.CreateSecurityGroupPolicyResponse.status:type_name -> common.resource.creation.Status
	52, // 27: services.aws.DeleteSecurityGroupPolicyRequest.resource_meta:type_name -> common.resource.Meta
	54, // 28: services.aws.DeleteSecurityGroupPolicyResponse.status:type_name -> common.resource.deletion.Status
	52, // 29: services.aws.GetSecurityGroupPolicyRequest.resource_meta:type_name -> common.resource.Meta
	55, // 30: services.aws.GetSecurityGroupPolicyResponse.status:type_name -> common.resource.Status
	52, // 31: services.aws.CheckVPCEndpointServiceReachabilityRequest.resource_meta:type_name -> common.resource.Meta
	52, // 32: services.aws.CheckVPCEndpointServiceReachabilityResponse.resource_meta:type_name -> common.resource.Meta
	0,  // 33: services.aws.CheckVPCEndpointServiceReachabilityResponse.status:type_name -> services.aws.VPCEndpointServiceReachabilityStatus
	52, // 34: services.aws.CreateVPCEndpointRequest.resource_meta:type_name -> common.resource.Meta
	51, // 35: services.aws.CreateVPCEndpointRequest.extra_tags:type_name -> services.aws.CreateVPCEndpointRequest.ExtraTagsEntry
	53, // 36: services.aws.CreateVPCEndpointResponse.status:type_name -> common.resource.creation.Status
	52, // 37: services.aws.DeleteVPCEndpointRequest.resource_meta:type_name -> common.resource.Meta
	54, // 38: services.aws.DeleteVPCEndpointResponse.status:type_name -> common.resource.deletion.Status
	52, // 39: services.aws.GetVPCEndpointRequest.resource_meta:type_name -> common.resource.Meta
	55, // 40: services.aws.GetVPCEndpointResponse.status:type_name -> common.resource.Status
	1,  // 41: services.aws.GetVPCEndpointResponse.endpoint_state:type_name -> services.aws.VPCEndpointStatus
	52, // 42: services.aws.CreateDBInstanceRequest.resource_meta:type_name -> common.resource.Meta
	57, // 43: services.aws.CreateDBInstanceRequest.spec:type_name -> common.aws.DBInstanceSpec
	53, // 44: services.aws.CreateDBInstanceResponse.status:type_name -> common.resource.creation.Status
	52, // 45: services.aws.DeleteDBInstanceRequest.resource_meta:type_name -> common.resource.Meta
	54, // 46: services.aws.DeleteDBInstanceResponse.status:type_name -> common.resource.deletion.Status
	52, // 47: services.aws.StartDBInstanceRequest.resource_meta:type_name -> common.resource.Meta
	58, // 48: services.aws.StartDBInstanceResponse.status:type_name -> common.resource.update.Status
	52, // 49: services.aws.StopDBInstanceRequest.resource_meta:type_name -> common.resource.Meta
	58, // 50: services.aws.StopDBInstanceResponse.status:type_name -> common.resource.update.Status
	52, // 51: services.aws.GetDBInstanceRequest.resource_meta:type_name -> common.resource.Meta
	55, // 52: services.aws.GetDBInstanceResponse.status:type_name -> common.resource.Status
	59, // 53: services.aws.GetDBInstanceResponse.endpoint:type_name -> common.aws.DBInstanceEndpoint
	60, // 54: services.aws.GetDBInstanceResponse.instance_status:type_name -> common.aws.DBInstanceStatus
	61, // 55: services.aws.AwsResourceManager.CreateDataDirectoryDeletionTask:input_type -> services.data.CreateDataDirectoryDeletionTaskRequest
	62, // 56: services.aws.AwsResourceManager.CreateDataDirectoryCloneTask:input_type -> services.data.CreateDataDirectoryCloneTaskRequest
	3,  // 57: services.aws.AwsResourceManager.CreateSecurityGroup:input_type -> services.aws.CreateSecurityGroupRequest
	5,  // 58: services.aws.AwsResourceManager.DeleteSecurityGroup:input_type -> services.aws.DeleteSecurityGroupRequest
	7,  // 59: services.aws.AwsResourceManager.GetSecurityGroup:input_type -> services.aws.GetSecurityGroupRequest
	26, // 60: services.aws.AwsResourceManager.CreateSecurityGroupPolicy:input_type -> services.aws.CreateSecurityGroupPolicyRequest
	28, // 61: services.aws.AwsResourceManager.DeleteSecurityGroupPolicy:input_type -> services.aws.DeleteSecurityGroupPolicyRequest
	30, // 62: services.aws.AwsResourceManager.GetSecurityGroupPolicy:input_type -> services.aws.GetSecurityGroupPolicyRequest
	9,  // 63: services.aws.AwsResourceManager.CreateIAMPolicy:input_type -> services.aws.CreateIAMPolicyRequest
	14, // 64: services.aws.AwsResourceManager.DeleteIAMPolicy:input_type -> services.aws.DeleteIAMPolicyRequest
	12, // 65: services.aws.AwsResourceManager.GetIAMPolicy:input_type -> services.aws.GetIAMPolicyRequest
	16, // 66: services.aws.AwsResourceManager.CreateIAMRole:input_type -> services.aws.CreateIAMRoleRequest
	20, // 67: services.aws.AwsResourceManager.DeleteIAMRole:input_type -> services.aws.DeleteIAMRoleRequest
	18, // 68: services.aws.AwsResourceManager.GetIAMRole:input_type -> services.aws.GetIAMRoleRequest
	32, // 69: services.aws.AwsResourceManager.CheckVPCEndpointServiceReachability:input_type -> services.aws.CheckVPCEndpointServiceReachabilityRequest
	34, // 70: services.aws.AwsResourceManager.CreateVPCEndpoint:input_type -> services.aws.CreateVPCEndpointRequest
	36, // 71: services.aws.AwsResourceManager.DeleteVPCEndpoint:input_type -> services.aws.DeleteVPCEndpointRequest
	38, // 72: services.aws.AwsResourceManager.GetVPCEndpoint:input_type -> services.aws.GetVPCEndpointRequest
	63, // 73: services.aws.AwsResourceManager.CreateSimpleDataReplicationTask:input_type -> services.data.CreateSimpleDataReplicationTaskRequest
	40, // 74: services.aws.AwsResourceManager.CreateDBInstance:input_type -> services.aws.CreateDBInstanceRequest
	42, // 75: services.aws.AwsResourceManager.DeleteDBInstance:input_type -> services.aws.DeleteDBInstanceRequest
	44, // 76: services.aws.AwsResourceManager.StartDBInstance:input_type -> services.aws.StartDBInstanceRequest
	46, // 77: services.aws.AwsResourceManager.StopDBInstance:input_type -> services.aws.StopDBInstanceRequest
	48, // 78: services.aws.AwsResourceManager.GetDBInstance:input_type -> services.aws.GetDBInstanceRequest
	64, // 79: services.aws.AwsResourceManager.GetManifest:input_type -> services.data.GetManifestRequest
	65, // 80: services.aws.AwsResourceManager.CreateDataDirectoryDeletionTask:output_type -> services.data.CreateDataDirectoryDeletionTaskResponse
	66, // 81: services.aws.AwsResourceManager.CreateDataDirectoryCloneTask:output_type -> services.data.CreateDataDirectoryCloneTaskResponse
	4,  // 82: services.aws.AwsResourceManager.CreateSecurityGroup:output_type -> services.aws.CreateSecurityGroupResponse
	6,  // 83: services.aws.AwsResourceManager.DeleteSecurityGroup:output_type -> services.aws.DeleteSecurityGroupResponse
	8,  // 84: services.aws.AwsResourceManager.GetSecurityGroup:output_type -> services.aws.GetSecurityGroupResponse
	27, // 85: services.aws.AwsResourceManager.CreateSecurityGroupPolicy:output_type -> services.aws.CreateSecurityGroupPolicyResponse
	29, // 86: services.aws.AwsResourceManager.DeleteSecurityGroupPolicy:output_type -> services.aws.DeleteSecurityGroupPolicyResponse
	31, // 87: services.aws.AwsResourceManager.GetSecurityGroupPolicy:output_type -> services.aws.GetSecurityGroupPolicyResponse
	11, // 88: services.aws.AwsResourceManager.CreateIAMPolicy:output_type -> services.aws.CreateIAMPolicyResponse
	15, // 89: services.aws.AwsResourceManager.DeleteIAMPolicy:output_type -> services.aws.DeleteIAMPolicyResponse
	13, // 90: services.aws.AwsResourceManager.GetIAMPolicy:output_type -> services.aws.GetIAMPolicyResponse
	17, // 91: services.aws.AwsResourceManager.CreateIAMRole:output_type -> services.aws.CreateIAMRoleResponse
	21, // 92: services.aws.AwsResourceManager.DeleteIAMRole:output_type -> services.aws.DeleteIAMRoleResponse
	19, // 93: services.aws.AwsResourceManager.GetIAMRole:output_type -> services.aws.GetIAMRoleResponse
	33, // 94: services.aws.AwsResourceManager.CheckVPCEndpointServiceReachability:output_type -> services.aws.CheckVPCEndpointServiceReachabilityResponse
	35, // 95: services.aws.AwsResourceManager.CreateVPCEndpoint:output_type -> services.aws.CreateVPCEndpointResponse
	37, // 96: services.aws.AwsResourceManager.DeleteVPCEndpoint:output_type -> services.aws.DeleteVPCEndpointResponse
	39, // 97: services.aws.AwsResourceManager.GetVPCEndpoint:output_type -> services.aws.GetVPCEndpointResponse
	67, // 98: services.aws.AwsResourceManager.CreateSimpleDataReplicationTask:output_type -> services.data.CreateSimpleDataReplicationTaskResponse
	41, // 99: services.aws.AwsResourceManager.CreateDBInstance:output_type -> services.aws.CreateDBInstanceResponse
	43, // 100: services.aws.AwsResourceManager.DeleteDBInstance:output_type -> services.aws.DeleteDBInstanceResponse
	45, // 101: services.aws.AwsResourceManager.StartDBInstance:output_type -> services.aws.StartDBInstanceResponse
	47, // 102: services.aws.AwsResourceManager.StopDBInstance:output_type -> services.aws.StopDBInstanceResponse
	49, // 103: services.aws.AwsResourceManager.GetDBInstance:output_type -> services.aws.GetDBInstanceResponse
	68, // 104: services.aws.AwsResourceManager.GetManifest:output_type -> services.data.GetManifestResponse
	80, // [80:105] is the sub-list for method output_type
	55, // [55:80] is the sub-list for method input_type
	55, // [55:55] is the sub-list for extension type_name
	55, // [55:55] is the sub-list for extension extendee
	0,  // [0:55] is the sub-list for field type_name
}

func init() { file_services_aws_proto_init() }
func file_services_aws_proto_init() {
	if File_services_aws_proto != nil {
		return
	}
	file_services_aws_proto_msgTypes[8].OneofWrappers = []any{
		(*IAMAccessOption_S3AccessOption)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_aws_proto_rawDesc), len(file_services_aws_proto_rawDesc)),
			NumEnums:      2,
			NumMessages:   50,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_aws_proto_goTypes,
		DependencyIndexes: file_services_aws_proto_depIdxs,
		EnumInfos:         file_services_aws_proto_enumTypes,
		MessageInfos:      file_services_aws_proto_msgTypes,
	}.Build()
	File_services_aws_proto = out.File
	file_services_aws_proto_goTypes = nil
	file_services_aws_proto_depIdxs = nil
}
