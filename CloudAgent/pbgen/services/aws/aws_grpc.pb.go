// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/aws.proto

package aws

import (
	context "context"
	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	AwsResourceManager_CreateDataDirectoryDeletionTask_FullMethodName     = "/services.aws.AwsResourceManager/CreateDataDirectoryDeletionTask"
	AwsResourceManager_CreateDataDirectoryCloneTask_FullMethodName        = "/services.aws.AwsResourceManager/CreateDataDirectoryCloneTask"
	AwsResourceManager_CreateSecurityGroup_FullMethodName                 = "/services.aws.AwsResourceManager/CreateSecurityGroup"
	AwsResourceManager_DeleteSecurityGroup_FullMethodName                 = "/services.aws.AwsResourceManager/DeleteSecurityGroup"
	AwsResourceManager_GetSecurityGroup_FullMethodName                    = "/services.aws.AwsResourceManager/GetSecurityGroup"
	AwsResourceManager_CreateSecurityGroupPolicy_FullMethodName           = "/services.aws.AwsResourceManager/CreateSecurityGroupPolicy"
	AwsResourceManager_DeleteSecurityGroupPolicy_FullMethodName           = "/services.aws.AwsResourceManager/DeleteSecurityGroupPolicy"
	AwsResourceManager_GetSecurityGroupPolicy_FullMethodName              = "/services.aws.AwsResourceManager/GetSecurityGroupPolicy"
	AwsResourceManager_CreateIAMPolicy_FullMethodName                     = "/services.aws.AwsResourceManager/CreateIAMPolicy"
	AwsResourceManager_DeleteIAMPolicy_FullMethodName                     = "/services.aws.AwsResourceManager/DeleteIAMPolicy"
	AwsResourceManager_GetIAMPolicy_FullMethodName                        = "/services.aws.AwsResourceManager/GetIAMPolicy"
	AwsResourceManager_CreateIAMRole_FullMethodName                       = "/services.aws.AwsResourceManager/CreateIAMRole"
	AwsResourceManager_DeleteIAMRole_FullMethodName                       = "/services.aws.AwsResourceManager/DeleteIAMRole"
	AwsResourceManager_GetIAMRole_FullMethodName                          = "/services.aws.AwsResourceManager/GetIAMRole"
	AwsResourceManager_CheckVPCEndpointServiceReachability_FullMethodName = "/services.aws.AwsResourceManager/CheckVPCEndpointServiceReachability"
	AwsResourceManager_CreateVPCEndpoint_FullMethodName                   = "/services.aws.AwsResourceManager/CreateVPCEndpoint"
	AwsResourceManager_DeleteVPCEndpoint_FullMethodName                   = "/services.aws.AwsResourceManager/DeleteVPCEndpoint"
	AwsResourceManager_GetVPCEndpoint_FullMethodName                      = "/services.aws.AwsResourceManager/GetVPCEndpoint"
	AwsResourceManager_CreateSimpleDataReplicationTask_FullMethodName     = "/services.aws.AwsResourceManager/CreateSimpleDataReplicationTask"
	AwsResourceManager_CreateDBInstance_FullMethodName                    = "/services.aws.AwsResourceManager/CreateDBInstance"
	AwsResourceManager_DeleteDBInstance_FullMethodName                    = "/services.aws.AwsResourceManager/DeleteDBInstance"
	AwsResourceManager_StartDBInstance_FullMethodName                     = "/services.aws.AwsResourceManager/StartDBInstance"
	AwsResourceManager_StopDBInstance_FullMethodName                      = "/services.aws.AwsResourceManager/StopDBInstance"
	AwsResourceManager_GetDBInstance_FullMethodName                       = "/services.aws.AwsResourceManager/GetDBInstance"
	AwsResourceManager_GetManifest_FullMethodName                         = "/services.aws.AwsResourceManager/GetManifest"
)

// AwsResourceManagerClient is the client API for AwsResourceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type AwsResourceManagerClient interface {
	CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error)
	CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateSecurityGroup(ctx context.Context, in *CreateSecurityGroupRequest, opts ...grpc.CallOption) (*CreateSecurityGroupResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteSecurityGroup(ctx context.Context, in *DeleteSecurityGroupRequest, opts ...grpc.CallOption) (*DeleteSecurityGroupResponse, error)
	GetSecurityGroup(ctx context.Context, in *GetSecurityGroupRequest, opts ...grpc.CallOption) (*GetSecurityGroupResponse, error)
	CreateSecurityGroupPolicy(ctx context.Context, in *CreateSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*CreateSecurityGroupPolicyResponse, error)
	DeleteSecurityGroupPolicy(ctx context.Context, in *DeleteSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*DeleteSecurityGroupPolicyResponse, error)
	GetSecurityGroupPolicy(ctx context.Context, in *GetSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*GetSecurityGroupPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMPolicy(ctx context.Context, in *CreateIAMPolicyRequest, opts ...grpc.CallOption) (*CreateIAMPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMPolicy(ctx context.Context, in *DeleteIAMPolicyRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyResponse, error)
	GetIAMPolicy(ctx context.Context, in *GetIAMPolicyRequest, opts ...grpc.CallOption) (*GetIAMPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMRole(ctx context.Context, in *CreateIAMRoleRequest, opts ...grpc.CallOption) (*CreateIAMRoleResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMRole(ctx context.Context, in *DeleteIAMRoleRequest, opts ...grpc.CallOption) (*DeleteIAMRoleResponse, error)
	GetIAMRole(ctx context.Context, in *GetIAMRoleRequest, opts ...grpc.CallOption) (*GetIAMRoleResponse, error)
	// Expected a successful status
	CheckVPCEndpointServiceReachability(ctx context.Context, in *CheckVPCEndpointServiceReachabilityRequest, opts ...grpc.CallOption) (*CheckVPCEndpointServiceReachabilityResponse, error)
	// Expected a SCHEDULED status on success.
	CreateVPCEndpoint(ctx context.Context, in *CreateVPCEndpointRequest, opts ...grpc.CallOption) (*CreateVPCEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteVPCEndpoint(ctx context.Context, in *DeleteVPCEndpointRequest, opts ...grpc.CallOption) (*DeleteVPCEndpointResponse, error)
	GetVPCEndpoint(ctx context.Context, in *GetVPCEndpointRequest, opts ...grpc.CallOption) (*GetVPCEndpointResponse, error)
	CreateSimpleDataReplicationTask(ctx context.Context, in *data.CreateSimpleDataReplicationTaskRequest, opts ...grpc.CallOption) (*data.CreateSimpleDataReplicationTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateDBInstance(ctx context.Context, in *CreateDBInstanceRequest, opts ...grpc.CallOption) (*CreateDBInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteDBInstance(ctx context.Context, in *DeleteDBInstanceRequest, opts ...grpc.CallOption) (*DeleteDBInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StartDBInstance(ctx context.Context, in *StartDBInstanceRequest, opts ...grpc.CallOption) (*StartDBInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StopDBInstance(ctx context.Context, in *StopDBInstanceRequest, opts ...grpc.CallOption) (*StopDBInstanceResponse, error)
	GetDBInstance(ctx context.Context, in *GetDBInstanceRequest, opts ...grpc.CallOption) (*GetDBInstanceResponse, error)
	GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error)
}

type awsResourceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewAwsResourceManagerClient(cc grpc.ClientConnInterface) AwsResourceManagerClient {
	return &awsResourceManagerClient{cc}
}

func (c *awsResourceManagerClient) CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateDataDirectoryDeletionTaskResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateDataDirectoryDeletionTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateDataDirectoryCloneTaskResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateDataDirectoryCloneTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateSecurityGroup(ctx context.Context, in *CreateSecurityGroupRequest, opts ...grpc.CallOption) (*CreateSecurityGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSecurityGroupResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateSecurityGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) DeleteSecurityGroup(ctx context.Context, in *DeleteSecurityGroupRequest, opts ...grpc.CallOption) (*DeleteSecurityGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSecurityGroupResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_DeleteSecurityGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetSecurityGroup(ctx context.Context, in *GetSecurityGroupRequest, opts ...grpc.CallOption) (*GetSecurityGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSecurityGroupResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetSecurityGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateSecurityGroupPolicy(ctx context.Context, in *CreateSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*CreateSecurityGroupPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSecurityGroupPolicyResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateSecurityGroupPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) DeleteSecurityGroupPolicy(ctx context.Context, in *DeleteSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*DeleteSecurityGroupPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSecurityGroupPolicyResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_DeleteSecurityGroupPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetSecurityGroupPolicy(ctx context.Context, in *GetSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*GetSecurityGroupPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSecurityGroupPolicyResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetSecurityGroupPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateIAMPolicy(ctx context.Context, in *CreateIAMPolicyRequest, opts ...grpc.CallOption) (*CreateIAMPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIAMPolicyResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateIAMPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) DeleteIAMPolicy(ctx context.Context, in *DeleteIAMPolicyRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIAMPolicyResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_DeleteIAMPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetIAMPolicy(ctx context.Context, in *GetIAMPolicyRequest, opts ...grpc.CallOption) (*GetIAMPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIAMPolicyResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetIAMPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateIAMRole(ctx context.Context, in *CreateIAMRoleRequest, opts ...grpc.CallOption) (*CreateIAMRoleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateIAMRoleResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateIAMRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) DeleteIAMRole(ctx context.Context, in *DeleteIAMRoleRequest, opts ...grpc.CallOption) (*DeleteIAMRoleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteIAMRoleResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_DeleteIAMRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetIAMRole(ctx context.Context, in *GetIAMRoleRequest, opts ...grpc.CallOption) (*GetIAMRoleResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetIAMRoleResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetIAMRole_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CheckVPCEndpointServiceReachability(ctx context.Context, in *CheckVPCEndpointServiceReachabilityRequest, opts ...grpc.CallOption) (*CheckVPCEndpointServiceReachabilityResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CheckVPCEndpointServiceReachabilityResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CheckVPCEndpointServiceReachability_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateVPCEndpoint(ctx context.Context, in *CreateVPCEndpointRequest, opts ...grpc.CallOption) (*CreateVPCEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateVPCEndpointResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateVPCEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) DeleteVPCEndpoint(ctx context.Context, in *DeleteVPCEndpointRequest, opts ...grpc.CallOption) (*DeleteVPCEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteVPCEndpointResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_DeleteVPCEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetVPCEndpoint(ctx context.Context, in *GetVPCEndpointRequest, opts ...grpc.CallOption) (*GetVPCEndpointResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetVPCEndpointResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetVPCEndpoint_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateSimpleDataReplicationTask(ctx context.Context, in *data.CreateSimpleDataReplicationTaskRequest, opts ...grpc.CallOption) (*data.CreateSimpleDataReplicationTaskResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.CreateSimpleDataReplicationTaskResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateSimpleDataReplicationTask_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) CreateDBInstance(ctx context.Context, in *CreateDBInstanceRequest, opts ...grpc.CallOption) (*CreateDBInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateDBInstanceResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_CreateDBInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) DeleteDBInstance(ctx context.Context, in *DeleteDBInstanceRequest, opts ...grpc.CallOption) (*DeleteDBInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteDBInstanceResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_DeleteDBInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) StartDBInstance(ctx context.Context, in *StartDBInstanceRequest, opts ...grpc.CallOption) (*StartDBInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartDBInstanceResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_StartDBInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) StopDBInstance(ctx context.Context, in *StopDBInstanceRequest, opts ...grpc.CallOption) (*StopDBInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopDBInstanceResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_StopDBInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetDBInstance(ctx context.Context, in *GetDBInstanceRequest, opts ...grpc.CallOption) (*GetDBInstanceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDBInstanceResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetDBInstance_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *awsResourceManagerClient) GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(data.GetManifestResponse)
	err := c.cc.Invoke(ctx, AwsResourceManager_GetManifest_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// AwsResourceManagerServer is the server API for AwsResourceManager service.
// All implementations must embed UnimplementedAwsResourceManagerServer
// for forward compatibility.
type AwsResourceManagerServer interface {
	CreateDataDirectoryDeletionTask(context.Context, *data.CreateDataDirectoryDeletionTaskRequest) (*data.CreateDataDirectoryDeletionTaskResponse, error)
	CreateDataDirectoryCloneTask(context.Context, *data.CreateDataDirectoryCloneTaskRequest) (*data.CreateDataDirectoryCloneTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateSecurityGroup(context.Context, *CreateSecurityGroupRequest) (*CreateSecurityGroupResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteSecurityGroup(context.Context, *DeleteSecurityGroupRequest) (*DeleteSecurityGroupResponse, error)
	GetSecurityGroup(context.Context, *GetSecurityGroupRequest) (*GetSecurityGroupResponse, error)
	CreateSecurityGroupPolicy(context.Context, *CreateSecurityGroupPolicyRequest) (*CreateSecurityGroupPolicyResponse, error)
	DeleteSecurityGroupPolicy(context.Context, *DeleteSecurityGroupPolicyRequest) (*DeleteSecurityGroupPolicyResponse, error)
	GetSecurityGroupPolicy(context.Context, *GetSecurityGroupPolicyRequest) (*GetSecurityGroupPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMPolicy(context.Context, *CreateIAMPolicyRequest) (*CreateIAMPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMPolicy(context.Context, *DeleteIAMPolicyRequest) (*DeleteIAMPolicyResponse, error)
	GetIAMPolicy(context.Context, *GetIAMPolicyRequest) (*GetIAMPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	CreateIAMRole(context.Context, *CreateIAMRoleRequest) (*CreateIAMRoleResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteIAMRole(context.Context, *DeleteIAMRoleRequest) (*DeleteIAMRoleResponse, error)
	GetIAMRole(context.Context, *GetIAMRoleRequest) (*GetIAMRoleResponse, error)
	// Expected a successful status
	CheckVPCEndpointServiceReachability(context.Context, *CheckVPCEndpointServiceReachabilityRequest) (*CheckVPCEndpointServiceReachabilityResponse, error)
	// Expected a SCHEDULED status on success.
	CreateVPCEndpoint(context.Context, *CreateVPCEndpointRequest) (*CreateVPCEndpointResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteVPCEndpoint(context.Context, *DeleteVPCEndpointRequest) (*DeleteVPCEndpointResponse, error)
	GetVPCEndpoint(context.Context, *GetVPCEndpointRequest) (*GetVPCEndpointResponse, error)
	CreateSimpleDataReplicationTask(context.Context, *data.CreateSimpleDataReplicationTaskRequest) (*data.CreateSimpleDataReplicationTaskResponse, error)
	// Expected a SCHEDULED status on success.
	CreateDBInstance(context.Context, *CreateDBInstanceRequest) (*CreateDBInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteDBInstance(context.Context, *DeleteDBInstanceRequest) (*DeleteDBInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StartDBInstance(context.Context, *StartDBInstanceRequest) (*StartDBInstanceResponse, error)
	// Expected a SCHEDULED status on success.
	StopDBInstance(context.Context, *StopDBInstanceRequest) (*StopDBInstanceResponse, error)
	GetDBInstance(context.Context, *GetDBInstanceRequest) (*GetDBInstanceResponse, error)
	GetManifest(context.Context, *data.GetManifestRequest) (*data.GetManifestResponse, error)
	mustEmbedUnimplementedAwsResourceManagerServer()
}

// UnimplementedAwsResourceManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedAwsResourceManagerServer struct{}

func (UnimplementedAwsResourceManagerServer) CreateDataDirectoryDeletionTask(context.Context, *data.CreateDataDirectoryDeletionTaskRequest) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataDirectoryDeletionTask not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateDataDirectoryCloneTask(context.Context, *data.CreateDataDirectoryCloneTaskRequest) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDataDirectoryCloneTask not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateSecurityGroup(context.Context, *CreateSecurityGroupRequest) (*CreateSecurityGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSecurityGroup not implemented")
}
func (UnimplementedAwsResourceManagerServer) DeleteSecurityGroup(context.Context, *DeleteSecurityGroupRequest) (*DeleteSecurityGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSecurityGroup not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetSecurityGroup(context.Context, *GetSecurityGroupRequest) (*GetSecurityGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityGroup not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateSecurityGroupPolicy(context.Context, *CreateSecurityGroupPolicyRequest) (*CreateSecurityGroupPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSecurityGroupPolicy not implemented")
}
func (UnimplementedAwsResourceManagerServer) DeleteSecurityGroupPolicy(context.Context, *DeleteSecurityGroupPolicyRequest) (*DeleteSecurityGroupPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSecurityGroupPolicy not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetSecurityGroupPolicy(context.Context, *GetSecurityGroupPolicyRequest) (*GetSecurityGroupPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecurityGroupPolicy not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateIAMPolicy(context.Context, *CreateIAMPolicyRequest) (*CreateIAMPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIAMPolicy not implemented")
}
func (UnimplementedAwsResourceManagerServer) DeleteIAMPolicy(context.Context, *DeleteIAMPolicyRequest) (*DeleteIAMPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIAMPolicy not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetIAMPolicy(context.Context, *GetIAMPolicyRequest) (*GetIAMPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIAMPolicy not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateIAMRole(context.Context, *CreateIAMRoleRequest) (*CreateIAMRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIAMRole not implemented")
}
func (UnimplementedAwsResourceManagerServer) DeleteIAMRole(context.Context, *DeleteIAMRoleRequest) (*DeleteIAMRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIAMRole not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetIAMRole(context.Context, *GetIAMRoleRequest) (*GetIAMRoleResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIAMRole not implemented")
}
func (UnimplementedAwsResourceManagerServer) CheckVPCEndpointServiceReachability(context.Context, *CheckVPCEndpointServiceReachabilityRequest) (*CheckVPCEndpointServiceReachabilityResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CheckVPCEndpointServiceReachability not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateVPCEndpoint(context.Context, *CreateVPCEndpointRequest) (*CreateVPCEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVPCEndpoint not implemented")
}
func (UnimplementedAwsResourceManagerServer) DeleteVPCEndpoint(context.Context, *DeleteVPCEndpointRequest) (*DeleteVPCEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVPCEndpoint not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetVPCEndpoint(context.Context, *GetVPCEndpointRequest) (*GetVPCEndpointResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVPCEndpoint not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateSimpleDataReplicationTask(context.Context, *data.CreateSimpleDataReplicationTaskRequest) (*data.CreateSimpleDataReplicationTaskResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSimpleDataReplicationTask not implemented")
}
func (UnimplementedAwsResourceManagerServer) CreateDBInstance(context.Context, *CreateDBInstanceRequest) (*CreateDBInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDBInstance not implemented")
}
func (UnimplementedAwsResourceManagerServer) DeleteDBInstance(context.Context, *DeleteDBInstanceRequest) (*DeleteDBInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDBInstance not implemented")
}
func (UnimplementedAwsResourceManagerServer) StartDBInstance(context.Context, *StartDBInstanceRequest) (*StartDBInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartDBInstance not implemented")
}
func (UnimplementedAwsResourceManagerServer) StopDBInstance(context.Context, *StopDBInstanceRequest) (*StopDBInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopDBInstance not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetDBInstance(context.Context, *GetDBInstanceRequest) (*GetDBInstanceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDBInstance not implemented")
}
func (UnimplementedAwsResourceManagerServer) GetManifest(context.Context, *data.GetManifestRequest) (*data.GetManifestResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetManifest not implemented")
}
func (UnimplementedAwsResourceManagerServer) mustEmbedUnimplementedAwsResourceManagerServer() {}
func (UnimplementedAwsResourceManagerServer) testEmbeddedByValue()                            {}

// UnsafeAwsResourceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to AwsResourceManagerServer will
// result in compilation errors.
type UnsafeAwsResourceManagerServer interface {
	mustEmbedUnimplementedAwsResourceManagerServer()
}

func RegisterAwsResourceManagerServer(s grpc.ServiceRegistrar, srv AwsResourceManagerServer) {
	// If the following call pancis, it indicates UnimplementedAwsResourceManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&AwsResourceManager_ServiceDesc, srv)
}

func _AwsResourceManager_CreateDataDirectoryDeletionTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateDataDirectoryDeletionTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateDataDirectoryDeletionTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateDataDirectoryDeletionTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateDataDirectoryDeletionTask(ctx, req.(*data.CreateDataDirectoryDeletionTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateDataDirectoryCloneTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateDataDirectoryCloneTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateDataDirectoryCloneTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateDataDirectoryCloneTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateDataDirectoryCloneTask(ctx, req.(*data.CreateDataDirectoryCloneTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateSecurityGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSecurityGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateSecurityGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateSecurityGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateSecurityGroup(ctx, req.(*CreateSecurityGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_DeleteSecurityGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSecurityGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).DeleteSecurityGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_DeleteSecurityGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).DeleteSecurityGroup(ctx, req.(*DeleteSecurityGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetSecurityGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetSecurityGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetSecurityGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetSecurityGroup(ctx, req.(*GetSecurityGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateSecurityGroupPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSecurityGroupPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateSecurityGroupPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateSecurityGroupPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateSecurityGroupPolicy(ctx, req.(*CreateSecurityGroupPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_DeleteSecurityGroupPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSecurityGroupPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).DeleteSecurityGroupPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_DeleteSecurityGroupPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).DeleteSecurityGroupPolicy(ctx, req.(*DeleteSecurityGroupPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetSecurityGroupPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecurityGroupPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetSecurityGroupPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetSecurityGroupPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetSecurityGroupPolicy(ctx, req.(*GetSecurityGroupPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateIAMPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIAMPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateIAMPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateIAMPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateIAMPolicy(ctx, req.(*CreateIAMPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_DeleteIAMPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIAMPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).DeleteIAMPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_DeleteIAMPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).DeleteIAMPolicy(ctx, req.(*DeleteIAMPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetIAMPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIAMPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetIAMPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetIAMPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetIAMPolicy(ctx, req.(*GetIAMPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateIAMRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIAMRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateIAMRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateIAMRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateIAMRole(ctx, req.(*CreateIAMRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_DeleteIAMRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteIAMRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).DeleteIAMRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_DeleteIAMRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).DeleteIAMRole(ctx, req.(*DeleteIAMRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetIAMRole_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetIAMRoleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetIAMRole(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetIAMRole_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetIAMRole(ctx, req.(*GetIAMRoleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CheckVPCEndpointServiceReachability_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CheckVPCEndpointServiceReachabilityRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CheckVPCEndpointServiceReachability(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CheckVPCEndpointServiceReachability_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CheckVPCEndpointServiceReachability(ctx, req.(*CheckVPCEndpointServiceReachabilityRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateVPCEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVPCEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateVPCEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateVPCEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateVPCEndpoint(ctx, req.(*CreateVPCEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_DeleteVPCEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVPCEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).DeleteVPCEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_DeleteVPCEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).DeleteVPCEndpoint(ctx, req.(*DeleteVPCEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetVPCEndpoint_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVPCEndpointRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetVPCEndpoint(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetVPCEndpoint_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetVPCEndpoint(ctx, req.(*GetVPCEndpointRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateSimpleDataReplicationTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.CreateSimpleDataReplicationTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateSimpleDataReplicationTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateSimpleDataReplicationTask_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateSimpleDataReplicationTask(ctx, req.(*data.CreateSimpleDataReplicationTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_CreateDBInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDBInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).CreateDBInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_CreateDBInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).CreateDBInstance(ctx, req.(*CreateDBInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_DeleteDBInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDBInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).DeleteDBInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_DeleteDBInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).DeleteDBInstance(ctx, req.(*DeleteDBInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_StartDBInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartDBInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).StartDBInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_StartDBInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).StartDBInstance(ctx, req.(*StartDBInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_StopDBInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopDBInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).StopDBInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_StopDBInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).StopDBInstance(ctx, req.(*StopDBInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetDBInstance_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDBInstanceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetDBInstance(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetDBInstance_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetDBInstance(ctx, req.(*GetDBInstanceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _AwsResourceManager_GetManifest_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(data.GetManifestRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(AwsResourceManagerServer).GetManifest(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: AwsResourceManager_GetManifest_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(AwsResourceManagerServer).GetManifest(ctx, req.(*data.GetManifestRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// AwsResourceManager_ServiceDesc is the grpc.ServiceDesc for AwsResourceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var AwsResourceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.aws.AwsResourceManager",
	HandlerType: (*AwsResourceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDataDirectoryDeletionTask",
			Handler:    _AwsResourceManager_CreateDataDirectoryDeletionTask_Handler,
		},
		{
			MethodName: "CreateDataDirectoryCloneTask",
			Handler:    _AwsResourceManager_CreateDataDirectoryCloneTask_Handler,
		},
		{
			MethodName: "CreateSecurityGroup",
			Handler:    _AwsResourceManager_CreateSecurityGroup_Handler,
		},
		{
			MethodName: "DeleteSecurityGroup",
			Handler:    _AwsResourceManager_DeleteSecurityGroup_Handler,
		},
		{
			MethodName: "GetSecurityGroup",
			Handler:    _AwsResourceManager_GetSecurityGroup_Handler,
		},
		{
			MethodName: "CreateSecurityGroupPolicy",
			Handler:    _AwsResourceManager_CreateSecurityGroupPolicy_Handler,
		},
		{
			MethodName: "DeleteSecurityGroupPolicy",
			Handler:    _AwsResourceManager_DeleteSecurityGroupPolicy_Handler,
		},
		{
			MethodName: "GetSecurityGroupPolicy",
			Handler:    _AwsResourceManager_GetSecurityGroupPolicy_Handler,
		},
		{
			MethodName: "CreateIAMPolicy",
			Handler:    _AwsResourceManager_CreateIAMPolicy_Handler,
		},
		{
			MethodName: "DeleteIAMPolicy",
			Handler:    _AwsResourceManager_DeleteIAMPolicy_Handler,
		},
		{
			MethodName: "GetIAMPolicy",
			Handler:    _AwsResourceManager_GetIAMPolicy_Handler,
		},
		{
			MethodName: "CreateIAMRole",
			Handler:    _AwsResourceManager_CreateIAMRole_Handler,
		},
		{
			MethodName: "DeleteIAMRole",
			Handler:    _AwsResourceManager_DeleteIAMRole_Handler,
		},
		{
			MethodName: "GetIAMRole",
			Handler:    _AwsResourceManager_GetIAMRole_Handler,
		},
		{
			MethodName: "CheckVPCEndpointServiceReachability",
			Handler:    _AwsResourceManager_CheckVPCEndpointServiceReachability_Handler,
		},
		{
			MethodName: "CreateVPCEndpoint",
			Handler:    _AwsResourceManager_CreateVPCEndpoint_Handler,
		},
		{
			MethodName: "DeleteVPCEndpoint",
			Handler:    _AwsResourceManager_DeleteVPCEndpoint_Handler,
		},
		{
			MethodName: "GetVPCEndpoint",
			Handler:    _AwsResourceManager_GetVPCEndpoint_Handler,
		},
		{
			MethodName: "CreateSimpleDataReplicationTask",
			Handler:    _AwsResourceManager_CreateSimpleDataReplicationTask_Handler,
		},
		{
			MethodName: "CreateDBInstance",
			Handler:    _AwsResourceManager_CreateDBInstance_Handler,
		},
		{
			MethodName: "DeleteDBInstance",
			Handler:    _AwsResourceManager_DeleteDBInstance_Handler,
		},
		{
			MethodName: "StartDBInstance",
			Handler:    _AwsResourceManager_StartDBInstance_Handler,
		},
		{
			MethodName: "StopDBInstance",
			Handler:    _AwsResourceManager_StopDBInstance_Handler,
		},
		{
			MethodName: "GetDBInstance",
			Handler:    _AwsResourceManager_GetDBInstance_Handler,
		},
		{
			MethodName: "GetManifest",
			Handler:    _AwsResourceManager_GetManifest_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/aws.proto",
}
