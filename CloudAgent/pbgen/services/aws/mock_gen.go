// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/aws (interfaces: AwsResourceManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/aws -package=aws -destination=pbgen/services/aws/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/aws AwsResourceManagerClient
//

// Package aws is a generated GoMock package.
package aws

import (
	context "context"
	reflect "reflect"

	data "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockAwsResourceManagerClient is a mock of AwsResourceManagerClient interface.
type MockAwsResourceManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockAwsResourceManagerClientMockRecorder
	isgomock struct{}
}

// MockAwsResourceManagerClientMockRecorder is the mock recorder for MockAwsResourceManagerClient.
type MockAwsResourceManagerClientMockRecorder struct {
	mock *MockAwsResourceManagerClient
}

// NewMockAwsResourceManagerClient creates a new mock instance.
func NewMockAwsResourceManagerClient(ctrl *gomock.Controller) *MockAwsResourceManagerClient {
	mock := &MockAwsResourceManagerClient{ctrl: ctrl}
	mock.recorder = &MockAwsResourceManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAwsResourceManagerClient) EXPECT() *MockAwsResourceManagerClientMockRecorder {
	return m.recorder
}

// CheckVPCEndpointServiceReachability mocks base method.
func (m *MockAwsResourceManagerClient) CheckVPCEndpointServiceReachability(ctx context.Context, in *CheckVPCEndpointServiceReachabilityRequest, opts ...grpc.CallOption) (*CheckVPCEndpointServiceReachabilityResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CheckVPCEndpointServiceReachability", varargs...)
	ret0, _ := ret[0].(*CheckVPCEndpointServiceReachabilityResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CheckVPCEndpointServiceReachability indicates an expected call of CheckVPCEndpointServiceReachability.
func (mr *MockAwsResourceManagerClientMockRecorder) CheckVPCEndpointServiceReachability(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CheckVPCEndpointServiceReachability", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CheckVPCEndpointServiceReachability), varargs...)
}

// CreateDBInstance mocks base method.
func (m *MockAwsResourceManagerClient) CreateDBInstance(ctx context.Context, in *CreateDBInstanceRequest, opts ...grpc.CallOption) (*CreateDBInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDBInstance", varargs...)
	ret0, _ := ret[0].(*CreateDBInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDBInstance indicates an expected call of CreateDBInstance.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateDBInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDBInstance", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateDBInstance), varargs...)
}

// CreateDataDirectoryCloneTask mocks base method.
func (m *MockAwsResourceManagerClient) CreateDataDirectoryCloneTask(ctx context.Context, in *data.CreateDataDirectoryCloneTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryCloneTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDataDirectoryCloneTask", varargs...)
	ret0, _ := ret[0].(*data.CreateDataDirectoryCloneTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataDirectoryCloneTask indicates an expected call of CreateDataDirectoryCloneTask.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateDataDirectoryCloneTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataDirectoryCloneTask", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateDataDirectoryCloneTask), varargs...)
}

// CreateDataDirectoryDeletionTask mocks base method.
func (m *MockAwsResourceManagerClient) CreateDataDirectoryDeletionTask(ctx context.Context, in *data.CreateDataDirectoryDeletionTaskRequest, opts ...grpc.CallOption) (*data.CreateDataDirectoryDeletionTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateDataDirectoryDeletionTask", varargs...)
	ret0, _ := ret[0].(*data.CreateDataDirectoryDeletionTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateDataDirectoryDeletionTask indicates an expected call of CreateDataDirectoryDeletionTask.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateDataDirectoryDeletionTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDataDirectoryDeletionTask", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateDataDirectoryDeletionTask), varargs...)
}

// CreateIAMPolicy mocks base method.
func (m *MockAwsResourceManagerClient) CreateIAMPolicy(ctx context.Context, in *CreateIAMPolicyRequest, opts ...grpc.CallOption) (*CreateIAMPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIAMPolicy", varargs...)
	ret0, _ := ret[0].(*CreateIAMPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIAMPolicy indicates an expected call of CreateIAMPolicy.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateIAMPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIAMPolicy", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateIAMPolicy), varargs...)
}

// CreateIAMRole mocks base method.
func (m *MockAwsResourceManagerClient) CreateIAMRole(ctx context.Context, in *CreateIAMRoleRequest, opts ...grpc.CallOption) (*CreateIAMRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateIAMRole", varargs...)
	ret0, _ := ret[0].(*CreateIAMRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateIAMRole indicates an expected call of CreateIAMRole.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateIAMRole(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateIAMRole", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateIAMRole), varargs...)
}

// CreateSecurityGroup mocks base method.
func (m *MockAwsResourceManagerClient) CreateSecurityGroup(ctx context.Context, in *CreateSecurityGroupRequest, opts ...grpc.CallOption) (*CreateSecurityGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSecurityGroup", varargs...)
	ret0, _ := ret[0].(*CreateSecurityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSecurityGroup indicates an expected call of CreateSecurityGroup.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateSecurityGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecurityGroup", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateSecurityGroup), varargs...)
}

// CreateSecurityGroupPolicy mocks base method.
func (m *MockAwsResourceManagerClient) CreateSecurityGroupPolicy(ctx context.Context, in *CreateSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*CreateSecurityGroupPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSecurityGroupPolicy", varargs...)
	ret0, _ := ret[0].(*CreateSecurityGroupPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSecurityGroupPolicy indicates an expected call of CreateSecurityGroupPolicy.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateSecurityGroupPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecurityGroupPolicy", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateSecurityGroupPolicy), varargs...)
}

// CreateSimpleDataReplicationTask mocks base method.
func (m *MockAwsResourceManagerClient) CreateSimpleDataReplicationTask(ctx context.Context, in *data.CreateSimpleDataReplicationTaskRequest, opts ...grpc.CallOption) (*data.CreateSimpleDataReplicationTaskResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSimpleDataReplicationTask", varargs...)
	ret0, _ := ret[0].(*data.CreateSimpleDataReplicationTaskResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSimpleDataReplicationTask indicates an expected call of CreateSimpleDataReplicationTask.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateSimpleDataReplicationTask(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSimpleDataReplicationTask", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateSimpleDataReplicationTask), varargs...)
}

// CreateVPCEndpoint mocks base method.
func (m *MockAwsResourceManagerClient) CreateVPCEndpoint(ctx context.Context, in *CreateVPCEndpointRequest, opts ...grpc.CallOption) (*CreateVPCEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateVPCEndpoint", varargs...)
	ret0, _ := ret[0].(*CreateVPCEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateVPCEndpoint indicates an expected call of CreateVPCEndpoint.
func (mr *MockAwsResourceManagerClientMockRecorder) CreateVPCEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateVPCEndpoint", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).CreateVPCEndpoint), varargs...)
}

// DeleteDBInstance mocks base method.
func (m *MockAwsResourceManagerClient) DeleteDBInstance(ctx context.Context, in *DeleteDBInstanceRequest, opts ...grpc.CallOption) (*DeleteDBInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteDBInstance", varargs...)
	ret0, _ := ret[0].(*DeleteDBInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteDBInstance indicates an expected call of DeleteDBInstance.
func (mr *MockAwsResourceManagerClientMockRecorder) DeleteDBInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDBInstance", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).DeleteDBInstance), varargs...)
}

// DeleteIAMPolicy mocks base method.
func (m *MockAwsResourceManagerClient) DeleteIAMPolicy(ctx context.Context, in *DeleteIAMPolicyRequest, opts ...grpc.CallOption) (*DeleteIAMPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIAMPolicy", varargs...)
	ret0, _ := ret[0].(*DeleteIAMPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIAMPolicy indicates an expected call of DeleteIAMPolicy.
func (mr *MockAwsResourceManagerClientMockRecorder) DeleteIAMPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIAMPolicy", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).DeleteIAMPolicy), varargs...)
}

// DeleteIAMRole mocks base method.
func (m *MockAwsResourceManagerClient) DeleteIAMRole(ctx context.Context, in *DeleteIAMRoleRequest, opts ...grpc.CallOption) (*DeleteIAMRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteIAMRole", varargs...)
	ret0, _ := ret[0].(*DeleteIAMRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteIAMRole indicates an expected call of DeleteIAMRole.
func (mr *MockAwsResourceManagerClientMockRecorder) DeleteIAMRole(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteIAMRole", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).DeleteIAMRole), varargs...)
}

// DeleteSecurityGroup mocks base method.
func (m *MockAwsResourceManagerClient) DeleteSecurityGroup(ctx context.Context, in *DeleteSecurityGroupRequest, opts ...grpc.CallOption) (*DeleteSecurityGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSecurityGroup", varargs...)
	ret0, _ := ret[0].(*DeleteSecurityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSecurityGroup indicates an expected call of DeleteSecurityGroup.
func (mr *MockAwsResourceManagerClientMockRecorder) DeleteSecurityGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSecurityGroup", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).DeleteSecurityGroup), varargs...)
}

// DeleteSecurityGroupPolicy mocks base method.
func (m *MockAwsResourceManagerClient) DeleteSecurityGroupPolicy(ctx context.Context, in *DeleteSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*DeleteSecurityGroupPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSecurityGroupPolicy", varargs...)
	ret0, _ := ret[0].(*DeleteSecurityGroupPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSecurityGroupPolicy indicates an expected call of DeleteSecurityGroupPolicy.
func (mr *MockAwsResourceManagerClientMockRecorder) DeleteSecurityGroupPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSecurityGroupPolicy", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).DeleteSecurityGroupPolicy), varargs...)
}

// DeleteVPCEndpoint mocks base method.
func (m *MockAwsResourceManagerClient) DeleteVPCEndpoint(ctx context.Context, in *DeleteVPCEndpointRequest, opts ...grpc.CallOption) (*DeleteVPCEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteVPCEndpoint", varargs...)
	ret0, _ := ret[0].(*DeleteVPCEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteVPCEndpoint indicates an expected call of DeleteVPCEndpoint.
func (mr *MockAwsResourceManagerClientMockRecorder) DeleteVPCEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVPCEndpoint", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).DeleteVPCEndpoint), varargs...)
}

// GetDBInstance mocks base method.
func (m *MockAwsResourceManagerClient) GetDBInstance(ctx context.Context, in *GetDBInstanceRequest, opts ...grpc.CallOption) (*GetDBInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDBInstance", varargs...)
	ret0, _ := ret[0].(*GetDBInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDBInstance indicates an expected call of GetDBInstance.
func (mr *MockAwsResourceManagerClientMockRecorder) GetDBInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDBInstance", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetDBInstance), varargs...)
}

// GetIAMPolicy mocks base method.
func (m *MockAwsResourceManagerClient) GetIAMPolicy(ctx context.Context, in *GetIAMPolicyRequest, opts ...grpc.CallOption) (*GetIAMPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIAMPolicy", varargs...)
	ret0, _ := ret[0].(*GetIAMPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIAMPolicy indicates an expected call of GetIAMPolicy.
func (mr *MockAwsResourceManagerClientMockRecorder) GetIAMPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIAMPolicy", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetIAMPolicy), varargs...)
}

// GetIAMRole mocks base method.
func (m *MockAwsResourceManagerClient) GetIAMRole(ctx context.Context, in *GetIAMRoleRequest, opts ...grpc.CallOption) (*GetIAMRoleResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetIAMRole", varargs...)
	ret0, _ := ret[0].(*GetIAMRoleResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetIAMRole indicates an expected call of GetIAMRole.
func (mr *MockAwsResourceManagerClientMockRecorder) GetIAMRole(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetIAMRole", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetIAMRole), varargs...)
}

// GetManifest mocks base method.
func (m *MockAwsResourceManagerClient) GetManifest(ctx context.Context, in *data.GetManifestRequest, opts ...grpc.CallOption) (*data.GetManifestResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetManifest", varargs...)
	ret0, _ := ret[0].(*data.GetManifestResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetManifest indicates an expected call of GetManifest.
func (mr *MockAwsResourceManagerClientMockRecorder) GetManifest(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetManifest", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetManifest), varargs...)
}

// GetSecurityGroup mocks base method.
func (m *MockAwsResourceManagerClient) GetSecurityGroup(ctx context.Context, in *GetSecurityGroupRequest, opts ...grpc.CallOption) (*GetSecurityGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecurityGroup", varargs...)
	ret0, _ := ret[0].(*GetSecurityGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityGroup indicates an expected call of GetSecurityGroup.
func (mr *MockAwsResourceManagerClientMockRecorder) GetSecurityGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityGroup", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetSecurityGroup), varargs...)
}

// GetSecurityGroupPolicy mocks base method.
func (m *MockAwsResourceManagerClient) GetSecurityGroupPolicy(ctx context.Context, in *GetSecurityGroupPolicyRequest, opts ...grpc.CallOption) (*GetSecurityGroupPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecurityGroupPolicy", varargs...)
	ret0, _ := ret[0].(*GetSecurityGroupPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecurityGroupPolicy indicates an expected call of GetSecurityGroupPolicy.
func (mr *MockAwsResourceManagerClientMockRecorder) GetSecurityGroupPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecurityGroupPolicy", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetSecurityGroupPolicy), varargs...)
}

// GetVPCEndpoint mocks base method.
func (m *MockAwsResourceManagerClient) GetVPCEndpoint(ctx context.Context, in *GetVPCEndpointRequest, opts ...grpc.CallOption) (*GetVPCEndpointResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetVPCEndpoint", varargs...)
	ret0, _ := ret[0].(*GetVPCEndpointResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetVPCEndpoint indicates an expected call of GetVPCEndpoint.
func (mr *MockAwsResourceManagerClientMockRecorder) GetVPCEndpoint(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetVPCEndpoint", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).GetVPCEndpoint), varargs...)
}

// StartDBInstance mocks base method.
func (m *MockAwsResourceManagerClient) StartDBInstance(ctx context.Context, in *StartDBInstanceRequest, opts ...grpc.CallOption) (*StartDBInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartDBInstance", varargs...)
	ret0, _ := ret[0].(*StartDBInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartDBInstance indicates an expected call of StartDBInstance.
func (mr *MockAwsResourceManagerClientMockRecorder) StartDBInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartDBInstance", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).StartDBInstance), varargs...)
}

// StopDBInstance mocks base method.
func (m *MockAwsResourceManagerClient) StopDBInstance(ctx context.Context, in *StopDBInstanceRequest, opts ...grpc.CallOption) (*StopDBInstanceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopDBInstance", varargs...)
	ret0, _ := ret[0].(*StopDBInstanceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopDBInstance indicates an expected call of StopDBInstance.
func (mr *MockAwsResourceManagerClientMockRecorder) StopDBInstance(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopDBInstance", reflect.TypeOf((*MockAwsResourceManagerClient)(nil).StopDBInstance), varargs...)
}
