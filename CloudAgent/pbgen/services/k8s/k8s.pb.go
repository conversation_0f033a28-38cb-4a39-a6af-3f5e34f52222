// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: services/k8s.proto

package k8s

import (
	gmp "github.com/risingwavelabs/cloudagent/pbgen/common/gmp"
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	postgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	prometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	creation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	deletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	update "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	rw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	durationpb "google.golang.org/protobuf/types/known/durationpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type CreateNamespaceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNamespaceRequest) Reset() {
	*x = CreateNamespaceRequest{}
	mi := &file_services_k8s_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNamespaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNamespaceRequest) ProtoMessage() {}

func (x *CreateNamespaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNamespaceRequest.ProtoReflect.Descriptor instead.
func (*CreateNamespaceRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{0}
}

func (x *CreateNamespaceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateNamespaceRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateNamespaceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNamespaceResponse) Reset() {
	*x = CreateNamespaceResponse{}
	mi := &file_services_k8s_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNamespaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNamespaceResponse) ProtoMessage() {}

func (x *CreateNamespaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNamespaceResponse.ProtoReflect.Descriptor instead.
func (*CreateNamespaceResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{1}
}

func (x *CreateNamespaceResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type LabelNamespaceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelNamespaceRequest) Reset() {
	*x = LabelNamespaceRequest{}
	mi := &file_services_k8s_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelNamespaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelNamespaceRequest) ProtoMessage() {}

func (x *LabelNamespaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelNamespaceRequest.ProtoReflect.Descriptor instead.
func (*LabelNamespaceRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{2}
}

func (x *LabelNamespaceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *LabelNamespaceRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type LabelNamespaceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LabelNamespaceResponse) Reset() {
	*x = LabelNamespaceResponse{}
	mi := &file_services_k8s_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LabelNamespaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LabelNamespaceResponse) ProtoMessage() {}

func (x *LabelNamespaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LabelNamespaceResponse.ProtoReflect.Descriptor instead.
func (*LabelNamespaceResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{3}
}

type GetNamespaceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNamespaceRequest) Reset() {
	*x = GetNamespaceRequest{}
	mi := &file_services_k8s_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNamespaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNamespaceRequest) ProtoMessage() {}

func (x *GetNamespaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNamespaceRequest.ProtoReflect.Descriptor instead.
func (*GetNamespaceRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{4}
}

func (x *GetNamespaceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type GetNamespaceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetNamespaceResponse) Reset() {
	*x = GetNamespaceResponse{}
	mi := &file_services_k8s_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetNamespaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetNamespaceResponse) ProtoMessage() {}

func (x *GetNamespaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetNamespaceResponse.ProtoReflect.Descriptor instead.
func (*GetNamespaceResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{5}
}

func (x *GetNamespaceResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteNamespaceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNamespaceRequest) Reset() {
	*x = DeleteNamespaceRequest{}
	mi := &file_services_k8s_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNamespaceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNamespaceRequest) ProtoMessage() {}

func (x *DeleteNamespaceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNamespaceRequest.ProtoReflect.Descriptor instead.
func (*DeleteNamespaceRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{6}
}

func (x *DeleteNamespaceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes SCHEDULED and NOT_FOUND.
// Otherwise an RPC error will be returned.
type DeleteNamespaceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNamespaceResponse) Reset() {
	*x = DeleteNamespaceResponse{}
	mi := &file_services_k8s_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNamespaceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNamespaceResponse) ProtoMessage() {}

func (x *DeleteNamespaceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNamespaceResponse.ProtoReflect.Descriptor instead.
func (*DeleteNamespaceResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{7}
}

func (x *DeleteNamespaceResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateServiceAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceAccountRequest) Reset() {
	*x = CreateServiceAccountRequest{}
	mi := &file_services_k8s_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceAccountRequest) ProtoMessage() {}

func (x *CreateServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{8}
}

func (x *CreateServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateServiceAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceAccountResponse) Reset() {
	*x = CreateServiceAccountResponse{}
	mi := &file_services_k8s_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceAccountResponse) ProtoMessage() {}

func (x *CreateServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{9}
}

func (x *CreateServiceAccountResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetServiceAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceAccountRequest) Reset() {
	*x = GetServiceAccountRequest{}
	mi := &file_services_k8s_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAccountRequest) ProtoMessage() {}

func (x *GetServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*GetServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{10}
}

func (x *GetServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type GetServiceAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceAccountResponse) Reset() {
	*x = GetServiceAccountResponse{}
	mi := &file_services_k8s_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceAccountResponse) ProtoMessage() {}

func (x *GetServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*GetServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{11}
}

func (x *GetServiceAccountResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteServiceAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceAccountRequest) Reset() {
	*x = DeleteServiceAccountRequest{}
	mi := &file_services_k8s_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceAccountRequest) ProtoMessage() {}

func (x *DeleteServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{12}
}

func (x *DeleteServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes DELETED and NOT_FOUND.
// Otherwise an RPC error will be returned.
type DeleteServiceAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceAccountResponse) Reset() {
	*x = DeleteServiceAccountResponse{}
	mi := &file_services_k8s_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceAccountResponse) ProtoMessage() {}

func (x *DeleteServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{13}
}

func (x *DeleteServiceAccountResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type AnnotateServiceAccountRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,2,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnotateServiceAccountRequest) Reset() {
	*x = AnnotateServiceAccountRequest{}
	mi := &file_services_k8s_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnotateServiceAccountRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotateServiceAccountRequest) ProtoMessage() {}

func (x *AnnotateServiceAccountRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotateServiceAccountRequest.ProtoReflect.Descriptor instead.
func (*AnnotateServiceAccountRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{14}
}

func (x *AnnotateServiceAccountRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *AnnotateServiceAccountRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type AnnotateServiceAccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AnnotateServiceAccountResponse) Reset() {
	*x = AnnotateServiceAccountResponse{}
	mi := &file_services_k8s_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AnnotateServiceAccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AnnotateServiceAccountResponse) ProtoMessage() {}

func (x *AnnotateServiceAccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AnnotateServiceAccountResponse.ProtoReflect.Descriptor instead.
func (*AnnotateServiceAccountResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{15}
}

type CreateConfigMapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ConfigMapSpec *k8s.ConfigMap         `protobuf:"bytes,2,opt,name=config_map_spec,json=configMapSpec,proto3" json:"config_map_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateConfigMapRequest) Reset() {
	*x = CreateConfigMapRequest{}
	mi := &file_services_k8s_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateConfigMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConfigMapRequest) ProtoMessage() {}

func (x *CreateConfigMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConfigMapRequest.ProtoReflect.Descriptor instead.
func (*CreateConfigMapRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{16}
}

func (x *CreateConfigMapRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateConfigMapRequest) GetConfigMapSpec() *k8s.ConfigMap {
	if x != nil {
		return x.ConfigMapSpec
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateConfigMapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateConfigMapResponse) Reset() {
	*x = CreateConfigMapResponse{}
	mi := &file_services_k8s_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateConfigMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateConfigMapResponse) ProtoMessage() {}

func (x *CreateConfigMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateConfigMapResponse.ProtoReflect.Descriptor instead.
func (*CreateConfigMapResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{17}
}

func (x *CreateConfigMapResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetConfigMapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigMapRequest) Reset() {
	*x = GetConfigMapRequest{}
	mi := &file_services_k8s_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigMapRequest) ProtoMessage() {}

func (x *GetConfigMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigMapRequest.ProtoReflect.Descriptor instead.
func (*GetConfigMapRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{18}
}

func (x *GetConfigMapRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type GetConfigMapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ConfigMapSpec *k8s.ConfigMap         `protobuf:"bytes,2,opt,name=config_map_spec,json=configMapSpec,proto3" json:"config_map_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetConfigMapResponse) Reset() {
	*x = GetConfigMapResponse{}
	mi := &file_services_k8s_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetConfigMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetConfigMapResponse) ProtoMessage() {}

func (x *GetConfigMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetConfigMapResponse.ProtoReflect.Descriptor instead.
func (*GetConfigMapResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{19}
}

func (x *GetConfigMapResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetConfigMapResponse) GetConfigMapSpec() *k8s.ConfigMap {
	if x != nil {
		return x.ConfigMapSpec
	}
	return nil
}

type UpdateConfigMapRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Deprecated: Marked as deprecated in services/k8s.proto.
	FromConfigMapSpec *k8s.ConfigMap `protobuf:"bytes,2,opt,name=from_config_map_spec,json=fromConfigMapSpec,proto3" json:"from_config_map_spec,omitempty"`
	ToConfigMapSpec   *k8s.ConfigMap `protobuf:"bytes,3,opt,name=to_config_map_spec,json=toConfigMapSpec,proto3" json:"to_config_map_spec,omitempty"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *UpdateConfigMapRequest) Reset() {
	*x = UpdateConfigMapRequest{}
	mi := &file_services_k8s_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfigMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfigMapRequest) ProtoMessage() {}

func (x *UpdateConfigMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfigMapRequest.ProtoReflect.Descriptor instead.
func (*UpdateConfigMapRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{20}
}

func (x *UpdateConfigMapRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Deprecated: Marked as deprecated in services/k8s.proto.
func (x *UpdateConfigMapRequest) GetFromConfigMapSpec() *k8s.ConfigMap {
	if x != nil {
		return x.FromConfigMapSpec
	}
	return nil
}

func (x *UpdateConfigMapRequest) GetToConfigMapSpec() *k8s.ConfigMap {
	if x != nil {
		return x.ToConfigMapSpec
	}
	return nil
}

type UpdateConfigMapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateConfigMapResponse) Reset() {
	*x = UpdateConfigMapResponse{}
	mi := &file_services_k8s_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateConfigMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateConfigMapResponse) ProtoMessage() {}

func (x *UpdateConfigMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateConfigMapResponse.ProtoReflect.Descriptor instead.
func (*UpdateConfigMapResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{21}
}

type DeleteConfigMapRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteConfigMapRequest) Reset() {
	*x = DeleteConfigMapRequest{}
	mi := &file_services_k8s_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteConfigMapRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConfigMapRequest) ProtoMessage() {}

func (x *DeleteConfigMapRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConfigMapRequest.ProtoReflect.Descriptor instead.
func (*DeleteConfigMapRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{22}
}

func (x *DeleteConfigMapRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes DELETED and NOT_FOUND.
// Otherwise an RPC error will be returned.
type DeleteConfigMapResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteConfigMapResponse) Reset() {
	*x = DeleteConfigMapResponse{}
	mi := &file_services_k8s_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteConfigMapResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteConfigMapResponse) ProtoMessage() {}

func (x *DeleteConfigMapResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteConfigMapResponse.ProtoReflect.Descriptor instead.
func (*DeleteConfigMapResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{23}
}

func (x *DeleteConfigMapResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateSecretRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SecretSpec    *k8s.Secret            `protobuf:"bytes,2,opt,name=secret_spec,json=secretSpec,proto3" json:"secret_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSecretRequest) Reset() {
	*x = CreateSecretRequest{}
	mi := &file_services_k8s_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecretRequest) ProtoMessage() {}

func (x *CreateSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecretRequest.ProtoReflect.Descriptor instead.
func (*CreateSecretRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{24}
}

func (x *CreateSecretRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateSecretRequest) GetSecretSpec() *k8s.Secret {
	if x != nil {
		return x.SecretSpec
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateSecretResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateSecretResponse) Reset() {
	*x = CreateSecretResponse{}
	mi := &file_services_k8s_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateSecretResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSecretResponse) ProtoMessage() {}

func (x *CreateSecretResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSecretResponse.ProtoReflect.Descriptor instead.
func (*CreateSecretResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{25}
}

func (x *CreateSecretResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetSecretRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSecretRequest) Reset() {
	*x = GetSecretRequest{}
	mi := &file_services_k8s_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretRequest) ProtoMessage() {}

func (x *GetSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretRequest.ProtoReflect.Descriptor instead.
func (*GetSecretRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{26}
}

func (x *GetSecretRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetSecretResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	SecretSpec    *k8s.Secret            `protobuf:"bytes,2,opt,name=secret_spec,json=secretSpec,proto3" json:"secret_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetSecretResponse) Reset() {
	*x = GetSecretResponse{}
	mi := &file_services_k8s_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetSecretResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSecretResponse) ProtoMessage() {}

func (x *GetSecretResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSecretResponse.ProtoReflect.Descriptor instead.
func (*GetSecretResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{27}
}

func (x *GetSecretResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetSecretResponse) GetSecretSpec() *k8s.Secret {
	if x != nil {
		return x.SecretSpec
	}
	return nil
}

type DeleteSecretRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSecretRequest) Reset() {
	*x = DeleteSecretRequest{}
	mi := &file_services_k8s_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecretRequest) ProtoMessage() {}

func (x *DeleteSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecretRequest.ProtoReflect.Descriptor instead.
func (*DeleteSecretRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{28}
}

func (x *DeleteSecretRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type UpdateSecretRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SecretSpec    *k8s.Secret            `protobuf:"bytes,2,opt,name=secret_spec,json=secretSpec,proto3" json:"secret_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSecretRequest) Reset() {
	*x = UpdateSecretRequest{}
	mi := &file_services_k8s_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSecretRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecretRequest) ProtoMessage() {}

func (x *UpdateSecretRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecretRequest.ProtoReflect.Descriptor instead.
func (*UpdateSecretRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{29}
}

func (x *UpdateSecretRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateSecretRequest) GetSecretSpec() *k8s.Secret {
	if x != nil {
		return x.SecretSpec
	}
	return nil
}

type UpdateSecretResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateSecretResponse) Reset() {
	*x = UpdateSecretResponse{}
	mi := &file_services_k8s_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateSecretResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSecretResponse) ProtoMessage() {}

func (x *UpdateSecretResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSecretResponse.ProtoReflect.Descriptor instead.
func (*UpdateSecretResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{30}
}

// Valid statuses includes DELETED and NOT_FOUND.
// Otherwise an RPC error will be returned.
type DeleteSecretResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteSecretResponse) Reset() {
	*x = DeleteSecretResponse{}
	mi := &file_services_k8s_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteSecretResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSecretResponse) ProtoMessage() {}

func (x *DeleteSecretResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSecretResponse.ProtoReflect.Descriptor instead.
func (*DeleteSecretResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{31}
}

func (x *DeleteSecretResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateRisingWaveRequest struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta   *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	RisingwaveSpec *rw.RisingWaveSpec     `protobuf:"bytes,2,opt,name=risingwave_spec,json=risingwaveSpec,proto3" json:"risingwave_spec,omitempty"`
	Labels         map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Annotations    map[string]string      `protobuf:"bytes,4,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateRisingWaveRequest) Reset() {
	*x = CreateRisingWaveRequest{}
	mi := &file_services_k8s_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRisingWaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRisingWaveRequest) ProtoMessage() {}

func (x *CreateRisingWaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRisingWaveRequest.ProtoReflect.Descriptor instead.
func (*CreateRisingWaveRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{32}
}

func (x *CreateRisingWaveRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateRisingWaveRequest) GetRisingwaveSpec() *rw.RisingWaveSpec {
	if x != nil {
		return x.RisingwaveSpec
	}
	return nil
}

func (x *CreateRisingWaveRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreateRisingWaveRequest) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

type SpecificGroups struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Groups        []string               `protobuf:"bytes,1,rep,name=groups,proto3" json:"groups,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SpecificGroups) Reset() {
	*x = SpecificGroups{}
	mi := &file_services_k8s_proto_msgTypes[33]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SpecificGroups) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SpecificGroups) ProtoMessage() {}

func (x *SpecificGroups) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[33]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SpecificGroups.ProtoReflect.Descriptor instead.
func (*SpecificGroups) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{33}
}

func (x *SpecificGroups) GetGroups() []string {
	if x != nil {
		return x.Groups
	}
	return nil
}

type EnvVars struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Vars          []*k8s.EnvVar          `protobuf:"bytes,1,rep,name=vars,proto3" json:"vars,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnvVars) Reset() {
	*x = EnvVars{}
	mi := &file_services_k8s_proto_msgTypes[34]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnvVars) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvVars) ProtoMessage() {}

func (x *EnvVars) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[34]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvVars.ProtoReflect.Descriptor instead.
func (*EnvVars) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{34}
}

func (x *EnvVars) GetVars() []*k8s.EnvVar {
	if x != nil {
		return x.Vars
	}
	return nil
}

type EnvKeys struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Keys          []string               `protobuf:"bytes,1,rep,name=keys,proto3" json:"keys,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EnvKeys) Reset() {
	*x = EnvKeys{}
	mi := &file_services_k8s_proto_msgTypes[35]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EnvKeys) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EnvKeys) ProtoMessage() {}

func (x *EnvKeys) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[35]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EnvKeys.ProtoReflect.Descriptor instead.
func (*EnvKeys) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{35}
}

func (x *EnvKeys) GetKeys() []string {
	if x != nil {
		return x.Keys
	}
	return nil
}

type PutRisingWaveEnvRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Types that are valid to be assigned to NodeGroupSelection:
	//
	//	*PutRisingWaveEnvRequest_SpecificGroups
	//	*PutRisingWaveEnvRequest_AllGroups
	NodeGroupSelection isPutRisingWaveEnvRequest_NodeGroupSelection `protobuf_oneof:"node_group_selection"`
	// Types that are valid to be assigned to Component:
	//
	//	*PutRisingWaveEnvRequest_ComputeEnvChange
	//	*PutRisingWaveEnvRequest_CompactorEnvChange
	//	*PutRisingWaveEnvRequest_StandaloneEnvChange
	//	*PutRisingWaveEnvRequest_MetaEnvChange
	//	*PutRisingWaveEnvRequest_FrontendEnvChange
	Component     isPutRisingWaveEnvRequest_Component `protobuf_oneof:"component"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PutRisingWaveEnvRequest) Reset() {
	*x = PutRisingWaveEnvRequest{}
	mi := &file_services_k8s_proto_msgTypes[36]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PutRisingWaveEnvRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutRisingWaveEnvRequest) ProtoMessage() {}

func (x *PutRisingWaveEnvRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[36]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutRisingWaveEnvRequest.ProtoReflect.Descriptor instead.
func (*PutRisingWaveEnvRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{36}
}

func (x *PutRisingWaveEnvRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetNodeGroupSelection() isPutRisingWaveEnvRequest_NodeGroupSelection {
	if x != nil {
		return x.NodeGroupSelection
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetSpecificGroups() *SpecificGroups {
	if x != nil {
		if x, ok := x.NodeGroupSelection.(*PutRisingWaveEnvRequest_SpecificGroups); ok {
			return x.SpecificGroups
		}
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetAllGroups() bool {
	if x != nil {
		if x, ok := x.NodeGroupSelection.(*PutRisingWaveEnvRequest_AllGroups); ok {
			return x.AllGroups
		}
	}
	return false
}

func (x *PutRisingWaveEnvRequest) GetComponent() isPutRisingWaveEnvRequest_Component {
	if x != nil {
		return x.Component
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetComputeEnvChange() *EnvVars {
	if x != nil {
		if x, ok := x.Component.(*PutRisingWaveEnvRequest_ComputeEnvChange); ok {
			return x.ComputeEnvChange
		}
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetCompactorEnvChange() *EnvVars {
	if x != nil {
		if x, ok := x.Component.(*PutRisingWaveEnvRequest_CompactorEnvChange); ok {
			return x.CompactorEnvChange
		}
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetStandaloneEnvChange() *EnvVars {
	if x != nil {
		if x, ok := x.Component.(*PutRisingWaveEnvRequest_StandaloneEnvChange); ok {
			return x.StandaloneEnvChange
		}
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetMetaEnvChange() *EnvVars {
	if x != nil {
		if x, ok := x.Component.(*PutRisingWaveEnvRequest_MetaEnvChange); ok {
			return x.MetaEnvChange
		}
	}
	return nil
}

func (x *PutRisingWaveEnvRequest) GetFrontendEnvChange() *EnvVars {
	if x != nil {
		if x, ok := x.Component.(*PutRisingWaveEnvRequest_FrontendEnvChange); ok {
			return x.FrontendEnvChange
		}
	}
	return nil
}

type isPutRisingWaveEnvRequest_NodeGroupSelection interface {
	isPutRisingWaveEnvRequest_NodeGroupSelection()
}

type PutRisingWaveEnvRequest_SpecificGroups struct {
	SpecificGroups *SpecificGroups `protobuf:"bytes,2,opt,name=specific_groups,json=specificGroups,proto3,oneof"`
}

type PutRisingWaveEnvRequest_AllGroups struct {
	AllGroups bool `protobuf:"varint,3,opt,name=all_groups,json=allGroups,proto3,oneof"`
}

func (*PutRisingWaveEnvRequest_SpecificGroups) isPutRisingWaveEnvRequest_NodeGroupSelection() {}

func (*PutRisingWaveEnvRequest_AllGroups) isPutRisingWaveEnvRequest_NodeGroupSelection() {}

type isPutRisingWaveEnvRequest_Component interface {
	isPutRisingWaveEnvRequest_Component()
}

type PutRisingWaveEnvRequest_ComputeEnvChange struct {
	ComputeEnvChange *EnvVars `protobuf:"bytes,4,opt,name=compute_env_change,json=computeEnvChange,proto3,oneof"`
}

type PutRisingWaveEnvRequest_CompactorEnvChange struct {
	CompactorEnvChange *EnvVars `protobuf:"bytes,5,opt,name=compactor_env_change,json=compactorEnvChange,proto3,oneof"`
}

type PutRisingWaveEnvRequest_StandaloneEnvChange struct {
	StandaloneEnvChange *EnvVars `protobuf:"bytes,6,opt,name=standalone_env_change,json=standaloneEnvChange,proto3,oneof"`
}

type PutRisingWaveEnvRequest_MetaEnvChange struct {
	MetaEnvChange *EnvVars `protobuf:"bytes,7,opt,name=meta_env_change,json=metaEnvChange,proto3,oneof"`
}

type PutRisingWaveEnvRequest_FrontendEnvChange struct {
	FrontendEnvChange *EnvVars `protobuf:"bytes,8,opt,name=frontend_env_change,json=frontendEnvChange,proto3,oneof"`
}

func (*PutRisingWaveEnvRequest_ComputeEnvChange) isPutRisingWaveEnvRequest_Component() {}

func (*PutRisingWaveEnvRequest_CompactorEnvChange) isPutRisingWaveEnvRequest_Component() {}

func (*PutRisingWaveEnvRequest_StandaloneEnvChange) isPutRisingWaveEnvRequest_Component() {}

func (*PutRisingWaveEnvRequest_MetaEnvChange) isPutRisingWaveEnvRequest_Component() {}

func (*PutRisingWaveEnvRequest_FrontendEnvChange) isPutRisingWaveEnvRequest_Component() {}

type PutRisingWaveEnvResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *PutRisingWaveEnvResponse) Reset() {
	*x = PutRisingWaveEnvResponse{}
	mi := &file_services_k8s_proto_msgTypes[37]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *PutRisingWaveEnvResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*PutRisingWaveEnvResponse) ProtoMessage() {}

func (x *PutRisingWaveEnvResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[37]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use PutRisingWaveEnvResponse.ProtoReflect.Descriptor instead.
func (*PutRisingWaveEnvResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{37}
}

type DeleteRisingWaveEnvRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// Types that are valid to be assigned to NodeGroupSelection:
	//
	//	*DeleteRisingWaveEnvRequest_SpecificGroups
	//	*DeleteRisingWaveEnvRequest_AllGroups
	NodeGroupSelection isDeleteRisingWaveEnvRequest_NodeGroupSelection `protobuf_oneof:"node_group_selection"`
	// Types that are valid to be assigned to Component:
	//
	//	*DeleteRisingWaveEnvRequest_ComputeEnvChange
	//	*DeleteRisingWaveEnvRequest_CompactorEnvChange
	//	*DeleteRisingWaveEnvRequest_StandaloneEnvChange
	//	*DeleteRisingWaveEnvRequest_MetaEnvChange
	//	*DeleteRisingWaveEnvRequest_FrontendEnvChange
	Component     isDeleteRisingWaveEnvRequest_Component `protobuf_oneof:"component"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveEnvRequest) Reset() {
	*x = DeleteRisingWaveEnvRequest{}
	mi := &file_services_k8s_proto_msgTypes[38]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveEnvRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveEnvRequest) ProtoMessage() {}

func (x *DeleteRisingWaveEnvRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[38]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveEnvRequest.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveEnvRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{38}
}

func (x *DeleteRisingWaveEnvRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetNodeGroupSelection() isDeleteRisingWaveEnvRequest_NodeGroupSelection {
	if x != nil {
		return x.NodeGroupSelection
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetSpecificGroups() *SpecificGroups {
	if x != nil {
		if x, ok := x.NodeGroupSelection.(*DeleteRisingWaveEnvRequest_SpecificGroups); ok {
			return x.SpecificGroups
		}
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetAllGroups() bool {
	if x != nil {
		if x, ok := x.NodeGroupSelection.(*DeleteRisingWaveEnvRequest_AllGroups); ok {
			return x.AllGroups
		}
	}
	return false
}

func (x *DeleteRisingWaveEnvRequest) GetComponent() isDeleteRisingWaveEnvRequest_Component {
	if x != nil {
		return x.Component
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetComputeEnvChange() *EnvKeys {
	if x != nil {
		if x, ok := x.Component.(*DeleteRisingWaveEnvRequest_ComputeEnvChange); ok {
			return x.ComputeEnvChange
		}
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetCompactorEnvChange() *EnvKeys {
	if x != nil {
		if x, ok := x.Component.(*DeleteRisingWaveEnvRequest_CompactorEnvChange); ok {
			return x.CompactorEnvChange
		}
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetStandaloneEnvChange() *EnvKeys {
	if x != nil {
		if x, ok := x.Component.(*DeleteRisingWaveEnvRequest_StandaloneEnvChange); ok {
			return x.StandaloneEnvChange
		}
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetMetaEnvChange() *EnvKeys {
	if x != nil {
		if x, ok := x.Component.(*DeleteRisingWaveEnvRequest_MetaEnvChange); ok {
			return x.MetaEnvChange
		}
	}
	return nil
}

func (x *DeleteRisingWaveEnvRequest) GetFrontendEnvChange() *EnvKeys {
	if x != nil {
		if x, ok := x.Component.(*DeleteRisingWaveEnvRequest_FrontendEnvChange); ok {
			return x.FrontendEnvChange
		}
	}
	return nil
}

type isDeleteRisingWaveEnvRequest_NodeGroupSelection interface {
	isDeleteRisingWaveEnvRequest_NodeGroupSelection()
}

type DeleteRisingWaveEnvRequest_SpecificGroups struct {
	SpecificGroups *SpecificGroups `protobuf:"bytes,2,opt,name=specific_groups,json=specificGroups,proto3,oneof"`
}

type DeleteRisingWaveEnvRequest_AllGroups struct {
	AllGroups bool `protobuf:"varint,3,opt,name=all_groups,json=allGroups,proto3,oneof"`
}

func (*DeleteRisingWaveEnvRequest_SpecificGroups) isDeleteRisingWaveEnvRequest_NodeGroupSelection() {}

func (*DeleteRisingWaveEnvRequest_AllGroups) isDeleteRisingWaveEnvRequest_NodeGroupSelection() {}

type isDeleteRisingWaveEnvRequest_Component interface {
	isDeleteRisingWaveEnvRequest_Component()
}

type DeleteRisingWaveEnvRequest_ComputeEnvChange struct {
	ComputeEnvChange *EnvKeys `protobuf:"bytes,4,opt,name=compute_env_change,json=computeEnvChange,proto3,oneof"`
}

type DeleteRisingWaveEnvRequest_CompactorEnvChange struct {
	CompactorEnvChange *EnvKeys `protobuf:"bytes,5,opt,name=compactor_env_change,json=compactorEnvChange,proto3,oneof"`
}

type DeleteRisingWaveEnvRequest_StandaloneEnvChange struct {
	StandaloneEnvChange *EnvKeys `protobuf:"bytes,6,opt,name=standalone_env_change,json=standaloneEnvChange,proto3,oneof"`
}

type DeleteRisingWaveEnvRequest_MetaEnvChange struct {
	MetaEnvChange *EnvKeys `protobuf:"bytes,7,opt,name=meta_env_change,json=metaEnvChange,proto3,oneof"`
}

type DeleteRisingWaveEnvRequest_FrontendEnvChange struct {
	FrontendEnvChange *EnvKeys `protobuf:"bytes,8,opt,name=frontend_env_change,json=frontendEnvChange,proto3,oneof"`
}

func (*DeleteRisingWaveEnvRequest_ComputeEnvChange) isDeleteRisingWaveEnvRequest_Component() {}

func (*DeleteRisingWaveEnvRequest_CompactorEnvChange) isDeleteRisingWaveEnvRequest_Component() {}

func (*DeleteRisingWaveEnvRequest_StandaloneEnvChange) isDeleteRisingWaveEnvRequest_Component() {}

func (*DeleteRisingWaveEnvRequest_MetaEnvChange) isDeleteRisingWaveEnvRequest_Component() {}

func (*DeleteRisingWaveEnvRequest_FrontendEnvChange) isDeleteRisingWaveEnvRequest_Component() {}

type DeleteRisingWaveEnvResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveEnvResponse) Reset() {
	*x = DeleteRisingWaveEnvResponse{}
	mi := &file_services_k8s_proto_msgTypes[39]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveEnvResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveEnvResponse) ProtoMessage() {}

func (x *DeleteRisingWaveEnvResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[39]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveEnvResponse.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveEnvResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{39}
}

// Valid statuses includes SCHEDULED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateRisingWaveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRisingWaveResponse) Reset() {
	*x = CreateRisingWaveResponse{}
	mi := &file_services_k8s_proto_msgTypes[40]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRisingWaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRisingWaveResponse) ProtoMessage() {}

func (x *CreateRisingWaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[40]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRisingWaveResponse.ProtoReflect.Descriptor instead.
func (*CreateRisingWaveResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{40}
}

func (x *CreateRisingWaveResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetRisingWaveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetRisingWaveRequest) Reset() {
	*x = GetRisingWaveRequest{}
	mi := &file_services_k8s_proto_msgTypes[41]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRisingWaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRisingWaveRequest) ProtoMessage() {}

func (x *GetRisingWaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[41]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRisingWaveRequest.ProtoReflect.Descriptor instead.
func (*GetRisingWaveRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{41}
}

func (x *GetRisingWaveRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type GetRisingWaveResponse struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Status           *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	RisingwaveSpec   *rw.RisingWaveSpec     `protobuf:"bytes,2,opt,name=risingwave_spec,json=risingwaveSpec,proto3" json:"risingwave_spec,omitempty"`
	RisingwaveStatus *rw.RisingWaveStatus   `protobuf:"bytes,3,opt,name=risingwave_status,json=risingwaveStatus,proto3" json:"risingwave_status,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *GetRisingWaveResponse) Reset() {
	*x = GetRisingWaveResponse{}
	mi := &file_services_k8s_proto_msgTypes[42]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetRisingWaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetRisingWaveResponse) ProtoMessage() {}

func (x *GetRisingWaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[42]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetRisingWaveResponse.ProtoReflect.Descriptor instead.
func (*GetRisingWaveResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{42}
}

func (x *GetRisingWaveResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetRisingWaveResponse) GetRisingwaveSpec() *rw.RisingWaveSpec {
	if x != nil {
		return x.RisingwaveSpec
	}
	return nil
}

func (x *GetRisingWaveResponse) GetRisingwaveStatus() *rw.RisingWaveStatus {
	if x != nil {
		return x.RisingwaveStatus
	}
	return nil
}

type DeleteRisingWaveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveRequest) Reset() {
	*x = DeleteRisingWaveRequest{}
	mi := &file_services_k8s_proto_msgTypes[43]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveRequest) ProtoMessage() {}

func (x *DeleteRisingWaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[43]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveRequest.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{43}
}

func (x *DeleteRisingWaveRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes SCHEDULED and NOT_FOUND.
// Otherwise an RPC error will be returned.
type DeleteRisingWaveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveResponse) Reset() {
	*x = DeleteRisingWaveResponse{}
	mi := &file_services_k8s_proto_msgTypes[44]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveResponse) ProtoMessage() {}

func (x *DeleteRisingWaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[44]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveResponse.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{44}
}

func (x *DeleteRisingWaveResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateRisingWaveImageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ImageTag      string                 `protobuf:"bytes,2,opt,name=image_tag,json=imageTag,proto3" json:"image_tag,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveImageRequest) Reset() {
	*x = UpdateRisingWaveImageRequest{}
	mi := &file_services_k8s_proto_msgTypes[45]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveImageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveImageRequest) ProtoMessage() {}

func (x *UpdateRisingWaveImageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[45]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveImageRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveImageRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{45}
}

func (x *UpdateRisingWaveImageRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveImageRequest) GetImageTag() string {
	if x != nil {
		return x.ImageTag
	}
	return ""
}

type UpdateRisingWaveImageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveImageResponse) Reset() {
	*x = UpdateRisingWaveImageResponse{}
	mi := &file_services_k8s_proto_msgTypes[46]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveImageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveImageResponse) ProtoMessage() {}

func (x *UpdateRisingWaveImageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[46]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveImageResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveImageResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{46}
}

type UpdateRisingWaveLicenseKeyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SecretName    string                 `protobuf:"bytes,2,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveLicenseKeyRequest) Reset() {
	*x = UpdateRisingWaveLicenseKeyRequest{}
	mi := &file_services_k8s_proto_msgTypes[47]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveLicenseKeyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveLicenseKeyRequest) ProtoMessage() {}

func (x *UpdateRisingWaveLicenseKeyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[47]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveLicenseKeyRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveLicenseKeyRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{47}
}

func (x *UpdateRisingWaveLicenseKeyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveLicenseKeyRequest) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

type UpdateRisingWaveLicenseKeyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveLicenseKeyResponse) Reset() {
	*x = UpdateRisingWaveLicenseKeyResponse{}
	mi := &file_services_k8s_proto_msgTypes[48]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveLicenseKeyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveLicenseKeyResponse) ProtoMessage() {}

func (x *UpdateRisingWaveLicenseKeyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[48]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveLicenseKeyResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveLicenseKeyResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{48}
}

type UpdateRisingWaveSecretStoreRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	SecretName    string                 `protobuf:"bytes,2,opt,name=secret_name,json=secretName,proto3" json:"secret_name,omitempty"`
	SecretKey     string                 `protobuf:"bytes,3,opt,name=secret_key,json=secretKey,proto3" json:"secret_key,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveSecretStoreRequest) Reset() {
	*x = UpdateRisingWaveSecretStoreRequest{}
	mi := &file_services_k8s_proto_msgTypes[49]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveSecretStoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveSecretStoreRequest) ProtoMessage() {}

func (x *UpdateRisingWaveSecretStoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[49]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveSecretStoreRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveSecretStoreRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{49}
}

func (x *UpdateRisingWaveSecretStoreRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveSecretStoreRequest) GetSecretName() string {
	if x != nil {
		return x.SecretName
	}
	return ""
}

func (x *UpdateRisingWaveSecretStoreRequest) GetSecretKey() string {
	if x != nil {
		return x.SecretKey
	}
	return ""
}

type UpdateRisingWaveSecretStoreResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveSecretStoreResponse) Reset() {
	*x = UpdateRisingWaveSecretStoreResponse{}
	mi := &file_services_k8s_proto_msgTypes[50]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveSecretStoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveSecretStoreResponse) ProtoMessage() {}

func (x *UpdateRisingWaveSecretStoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[50]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveSecretStoreResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveSecretStoreResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{50}
}

// Deprecated. Use ScaleRisingWaveRequestOneOf instead
type ScaleRisingWaveRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// If present, set meta nodes with the specified replica and resources.
	// This will be a no-op if meta has no node group.
	MetaScaleSpec *rw.ScaleSpec `protobuf:"bytes,2,opt,name=meta_scale_spec,json=metaScaleSpec,proto3" json:"meta_scale_spec,omitempty"`
	// If present, set frontend nodes with the specified replica and resources.
	// This will be a no-op if frontend has no node group.
	FrontendScaleSpec *rw.ScaleSpec `protobuf:"bytes,3,opt,name=frontend_scale_spec,json=frontendScaleSpec,proto3" json:"frontend_scale_spec,omitempty"`
	// If present, set compute nodes with the specified replica and resources.
	// This will be a no-op if compute has no node group.
	ComputeScaleSpec *rw.ScaleSpec `protobuf:"bytes,4,opt,name=compute_scale_spec,json=computeScaleSpec,proto3" json:"compute_scale_spec,omitempty"`
	// If present, set compactor nodes with the specified replica and resources.
	// This will be a no-op if compactor has no node group.
	CompactorScaleSpec *rw.ScaleSpec `protobuf:"bytes,5,opt,name=compactor_scale_spec,json=compactorScaleSpec,proto3" json:"compactor_scale_spec,omitempty"`
	// If present, set compactor nodes with the specified replica and resources.
	// This will be a no-op if connector has no node group.
	//
	// Deprecated: Marked as deprecated in services/k8s.proto.
	ConnectorScaleSpec *rw.ScaleSpec `protobuf:"bytes,6,opt,name=connector_scale_spec,json=connectorScaleSpec,proto3" json:"connector_scale_spec,omitempty"`
	// If present, we will start the cluster in standalone mode.
	// This will be a no-op if standalone has no node group.
	StandaloneScaleSpec *rw.ScaleSpec `protobuf:"bytes,7,opt,name=standalone_scale_spec,json=standaloneScaleSpec,proto3" json:"standalone_scale_spec,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *ScaleRisingWaveRequest) Reset() {
	*x = ScaleRisingWaveRequest{}
	mi := &file_services_k8s_proto_msgTypes[51]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScaleRisingWaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScaleRisingWaveRequest) ProtoMessage() {}

func (x *ScaleRisingWaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[51]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScaleRisingWaveRequest.ProtoReflect.Descriptor instead.
func (*ScaleRisingWaveRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{51}
}

func (x *ScaleRisingWaveRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *ScaleRisingWaveRequest) GetMetaScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.MetaScaleSpec
	}
	return nil
}

func (x *ScaleRisingWaveRequest) GetFrontendScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.FrontendScaleSpec
	}
	return nil
}

func (x *ScaleRisingWaveRequest) GetComputeScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.ComputeScaleSpec
	}
	return nil
}

func (x *ScaleRisingWaveRequest) GetCompactorScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.CompactorScaleSpec
	}
	return nil
}

// Deprecated: Marked as deprecated in services/k8s.proto.
func (x *ScaleRisingWaveRequest) GetConnectorScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.ConnectorScaleSpec
	}
	return nil
}

func (x *ScaleRisingWaveRequest) GetStandaloneScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.StandaloneScaleSpec
	}
	return nil
}

type ScaleRisingWaveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScaleRisingWaveResponse) Reset() {
	*x = ScaleRisingWaveResponse{}
	mi := &file_services_k8s_proto_msgTypes[52]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScaleRisingWaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScaleRisingWaveResponse) ProtoMessage() {}

func (x *ScaleRisingWaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[52]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScaleRisingWaveResponse.ProtoReflect.Descriptor instead.
func (*ScaleRisingWaveResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{52}
}

type ScaleRisingWaveRequestOneOf struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to Mode:
	//
	//	*ScaleRisingWaveRequestOneOf_StandaloneSpec
	//	*ScaleRisingWaveRequestOneOf_ClusterSpec
	Mode          isScaleRisingWaveRequestOneOf_Mode `protobuf_oneof:"mode"`
	ResourceMeta  *resource.Meta                     `protobuf:"bytes,3,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ScaleRisingWaveRequestOneOf) Reset() {
	*x = ScaleRisingWaveRequestOneOf{}
	mi := &file_services_k8s_proto_msgTypes[53]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ScaleRisingWaveRequestOneOf) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ScaleRisingWaveRequestOneOf) ProtoMessage() {}

func (x *ScaleRisingWaveRequestOneOf) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[53]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ScaleRisingWaveRequestOneOf.ProtoReflect.Descriptor instead.
func (*ScaleRisingWaveRequestOneOf) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{53}
}

func (x *ScaleRisingWaveRequestOneOf) GetMode() isScaleRisingWaveRequestOneOf_Mode {
	if x != nil {
		return x.Mode
	}
	return nil
}

func (x *ScaleRisingWaveRequestOneOf) GetStandaloneSpec() *rw.ScaleSpec {
	if x != nil {
		if x, ok := x.Mode.(*ScaleRisingWaveRequestOneOf_StandaloneSpec); ok {
			return x.StandaloneSpec
		}
	}
	return nil
}

func (x *ScaleRisingWaveRequestOneOf) GetClusterSpec() *ClusterScaleSpec {
	if x != nil {
		if x, ok := x.Mode.(*ScaleRisingWaveRequestOneOf_ClusterSpec); ok {
			return x.ClusterSpec
		}
	}
	return nil
}

func (x *ScaleRisingWaveRequestOneOf) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type isScaleRisingWaveRequestOneOf_Mode interface {
	isScaleRisingWaveRequestOneOf_Mode()
}

type ScaleRisingWaveRequestOneOf_StandaloneSpec struct {
	StandaloneSpec *rw.ScaleSpec `protobuf:"bytes,1,opt,name=standalone_spec,json=standaloneSpec,proto3,oneof"`
}

type ScaleRisingWaveRequestOneOf_ClusterSpec struct {
	ClusterSpec *ClusterScaleSpec `protobuf:"bytes,2,opt,name=cluster_spec,json=clusterSpec,proto3,oneof"`
}

func (*ScaleRisingWaveRequestOneOf_StandaloneSpec) isScaleRisingWaveRequestOneOf_Mode() {}

func (*ScaleRisingWaveRequestOneOf_ClusterSpec) isScaleRisingWaveRequestOneOf_Mode() {}

type ClusterScaleSpec struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If present, set meta nodes with the specified replica and resources.
	// This will be a no-op if meta has no node group.
	MetaScaleSpec *rw.ScaleSpec `protobuf:"bytes,2,opt,name=meta_scale_spec,json=metaScaleSpec,proto3" json:"meta_scale_spec,omitempty"`
	// If present, set frontend nodes with the specified replica and resources.
	// This will be a no-op if frontend has no node group.
	FrontendScaleSpec *rw.ScaleSpec `protobuf:"bytes,3,opt,name=frontend_scale_spec,json=frontendScaleSpec,proto3" json:"frontend_scale_spec,omitempty"`
	// If present, set compute nodes with the specified replica and resources.
	// This will be a no-op if compute has no node group.
	ComputeScaleSpec *rw.ScaleSpec `protobuf:"bytes,4,opt,name=compute_scale_spec,json=computeScaleSpec,proto3" json:"compute_scale_spec,omitempty"`
	// If present, set compactor nodes with the specified replica and resources.
	// This will be a no-op if compactor has no node group.
	CompactorScaleSpec *rw.ScaleSpec `protobuf:"bytes,5,opt,name=compactor_scale_spec,json=compactorScaleSpec,proto3" json:"compactor_scale_spec,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *ClusterScaleSpec) Reset() {
	*x = ClusterScaleSpec{}
	mi := &file_services_k8s_proto_msgTypes[54]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ClusterScaleSpec) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterScaleSpec) ProtoMessage() {}

func (x *ClusterScaleSpec) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[54]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterScaleSpec.ProtoReflect.Descriptor instead.
func (*ClusterScaleSpec) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{54}
}

func (x *ClusterScaleSpec) GetMetaScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.MetaScaleSpec
	}
	return nil
}

func (x *ClusterScaleSpec) GetFrontendScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.FrontendScaleSpec
	}
	return nil
}

func (x *ClusterScaleSpec) GetComputeScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.ComputeScaleSpec
	}
	return nil
}

func (x *ClusterScaleSpec) GetCompactorScaleSpec() *rw.ScaleSpec {
	if x != nil {
		return x.CompactorScaleSpec
	}
	return nil
}

type RisingWaveReplicaOverride struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Replicas      uint32                 `protobuf:"varint,1,opt,name=replicas,proto3" json:"replicas,omitempty"`
	Component     string                 `protobuf:"bytes,2,opt,name=component,proto3" json:"component,omitempty"`
	NodeGroup     string                 `protobuf:"bytes,3,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RisingWaveReplicaOverride) Reset() {
	*x = RisingWaveReplicaOverride{}
	mi := &file_services_k8s_proto_msgTypes[55]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RisingWaveReplicaOverride) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RisingWaveReplicaOverride) ProtoMessage() {}

func (x *RisingWaveReplicaOverride) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[55]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RisingWaveReplicaOverride.ProtoReflect.Descriptor instead.
func (*RisingWaveReplicaOverride) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{55}
}

func (x *RisingWaveReplicaOverride) GetReplicas() uint32 {
	if x != nil {
		return x.Replicas
	}
	return 0
}

func (x *RisingWaveReplicaOverride) GetComponent() string {
	if x != nil {
		return x.Component
	}
	return ""
}

func (x *RisingWaveReplicaOverride) GetNodeGroup() string {
	if x != nil {
		return x.NodeGroup
	}
	return ""
}

type StartRisingWaveRequest struct {
	state         protoimpl.MessageState       `protogen:"open.v1"`
	ResourceMeta  *resource.Meta               `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Overrides     []*RisingWaveReplicaOverride `protobuf:"bytes,2,rep,name=overrides,proto3" json:"overrides,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartRisingWaveRequest) Reset() {
	*x = StartRisingWaveRequest{}
	mi := &file_services_k8s_proto_msgTypes[56]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartRisingWaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartRisingWaveRequest) ProtoMessage() {}

func (x *StartRisingWaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[56]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartRisingWaveRequest.ProtoReflect.Descriptor instead.
func (*StartRisingWaveRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{56}
}

func (x *StartRisingWaveRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *StartRisingWaveRequest) GetOverrides() []*RisingWaveReplicaOverride {
	if x != nil {
		return x.Overrides
	}
	return nil
}

type StartRisingWaveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StartRisingWaveResponse) Reset() {
	*x = StartRisingWaveResponse{}
	mi := &file_services_k8s_proto_msgTypes[57]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StartRisingWaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartRisingWaveResponse) ProtoMessage() {}

func (x *StartRisingWaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[57]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartRisingWaveResponse.ProtoReflect.Descriptor instead.
func (*StartRisingWaveResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{57}
}

type StopRisingWaveRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopRisingWaveRequest) Reset() {
	*x = StopRisingWaveRequest{}
	mi := &file_services_k8s_proto_msgTypes[58]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopRisingWaveRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopRisingWaveRequest) ProtoMessage() {}

func (x *StopRisingWaveRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[58]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopRisingWaveRequest.ProtoReflect.Descriptor instead.
func (*StopRisingWaveRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{58}
}

func (x *StopRisingWaveRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type StopRisingWaveResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StopRisingWaveResponse) Reset() {
	*x = StopRisingWaveResponse{}
	mi := &file_services_k8s_proto_msgTypes[59]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StopRisingWaveResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopRisingWaveResponse) ProtoMessage() {}

func (x *StopRisingWaveResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[59]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopRisingWaveResponse.ProtoReflect.Descriptor instead.
func (*StopRisingWaveResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{59}
}

type UpdateRisingWaveComponentsRequest struct {
	state                protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta         *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ComponentsSpec       *rw.ComponentsSpec     `protobuf:"bytes,2,opt,name=components_spec,json=componentsSpec,proto3" json:"components_spec,omitempty"`
	EnableStandaloneMode *bool                  `protobuf:"varint,3,opt,name=enable_standalone_mode,json=enableStandaloneMode,proto3,oneof" json:"enable_standalone_mode,omitempty"`
	unknownFields        protoimpl.UnknownFields
	sizeCache            protoimpl.SizeCache
}

func (x *UpdateRisingWaveComponentsRequest) Reset() {
	*x = UpdateRisingWaveComponentsRequest{}
	mi := &file_services_k8s_proto_msgTypes[60]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveComponentsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveComponentsRequest) ProtoMessage() {}

func (x *UpdateRisingWaveComponentsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[60]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveComponentsRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveComponentsRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{60}
}

func (x *UpdateRisingWaveComponentsRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveComponentsRequest) GetComponentsSpec() *rw.ComponentsSpec {
	if x != nil {
		return x.ComponentsSpec
	}
	return nil
}

func (x *UpdateRisingWaveComponentsRequest) GetEnableStandaloneMode() bool {
	if x != nil && x.EnableStandaloneMode != nil {
		return *x.EnableStandaloneMode
	}
	return false
}

type UpdateRisingWaveComponentsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveComponentsResponse) Reset() {
	*x = UpdateRisingWaveComponentsResponse{}
	mi := &file_services_k8s_proto_msgTypes[61]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveComponentsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveComponentsResponse) ProtoMessage() {}

func (x *UpdateRisingWaveComponentsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[61]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveComponentsResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveComponentsResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{61}
}

type UpdateRisingWaveMetaStoreRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	MetaStoreSpec *rw.MetaStoreSpec      `protobuf:"bytes,2,opt,name=meta_store_spec,json=metaStoreSpec,proto3" json:"meta_store_spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveMetaStoreRequest) Reset() {
	*x = UpdateRisingWaveMetaStoreRequest{}
	mi := &file_services_k8s_proto_msgTypes[62]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveMetaStoreRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveMetaStoreRequest) ProtoMessage() {}

func (x *UpdateRisingWaveMetaStoreRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[62]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveMetaStoreRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveMetaStoreRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{62}
}

func (x *UpdateRisingWaveMetaStoreRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveMetaStoreRequest) GetMetaStoreSpec() *rw.MetaStoreSpec {
	if x != nil {
		return x.MetaStoreSpec
	}
	return nil
}

type UpdateRisingWaveMetaStoreResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveMetaStoreResponse) Reset() {
	*x = UpdateRisingWaveMetaStoreResponse{}
	mi := &file_services_k8s_proto_msgTypes[63]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveMetaStoreResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveMetaStoreResponse) ProtoMessage() {}

func (x *UpdateRisingWaveMetaStoreResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[63]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveMetaStoreResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveMetaStoreResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{63}
}

type CreateRisingWaveComputeNodeGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	NodeGroup     *rw.NodeGroupSpec      `protobuf:"bytes,2,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRisingWaveComputeNodeGroupRequest) Reset() {
	*x = CreateRisingWaveComputeNodeGroupRequest{}
	mi := &file_services_k8s_proto_msgTypes[64]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRisingWaveComputeNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRisingWaveComputeNodeGroupRequest) ProtoMessage() {}

func (x *CreateRisingWaveComputeNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[64]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRisingWaveComputeNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateRisingWaveComputeNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{64}
}

func (x *CreateRisingWaveComputeNodeGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateRisingWaveComputeNodeGroupRequest) GetNodeGroup() *rw.NodeGroupSpec {
	if x != nil {
		return x.NodeGroup
	}
	return nil
}

type CreateRisingWaveComputeNodeGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRisingWaveComputeNodeGroupResponse) Reset() {
	*x = CreateRisingWaveComputeNodeGroupResponse{}
	mi := &file_services_k8s_proto_msgTypes[65]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRisingWaveComputeNodeGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRisingWaveComputeNodeGroupResponse) ProtoMessage() {}

func (x *CreateRisingWaveComputeNodeGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[65]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRisingWaveComputeNodeGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateRisingWaveComputeNodeGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{65}
}

func (x *CreateRisingWaveComputeNodeGroupResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateRisingWaveComputeNodeGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	NodeGroup     *rw.NodeGroupSpec      `protobuf:"bytes,2,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveComputeNodeGroupRequest) Reset() {
	*x = UpdateRisingWaveComputeNodeGroupRequest{}
	mi := &file_services_k8s_proto_msgTypes[66]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveComputeNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveComputeNodeGroupRequest) ProtoMessage() {}

func (x *UpdateRisingWaveComputeNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[66]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveComputeNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveComputeNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{66}
}

func (x *UpdateRisingWaveComputeNodeGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveComputeNodeGroupRequest) GetNodeGroup() *rw.NodeGroupSpec {
	if x != nil {
		return x.NodeGroup
	}
	return nil
}

type UpdateRisingWaveComputeNodeGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveComputeNodeGroupResponse) Reset() {
	*x = UpdateRisingWaveComputeNodeGroupResponse{}
	mi := &file_services_k8s_proto_msgTypes[67]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveComputeNodeGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveComputeNodeGroupResponse) ProtoMessage() {}

func (x *UpdateRisingWaveComputeNodeGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[67]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveComputeNodeGroupResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveComputeNodeGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{67}
}

func (x *UpdateRisingWaveComputeNodeGroupResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteRisingWaveComputeNodeGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	NodeGroupName string                 `protobuf:"bytes,2,opt,name=node_group_name,json=nodeGroupName,proto3" json:"node_group_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveComputeNodeGroupRequest) Reset() {
	*x = DeleteRisingWaveComputeNodeGroupRequest{}
	mi := &file_services_k8s_proto_msgTypes[68]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveComputeNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveComputeNodeGroupRequest) ProtoMessage() {}

func (x *DeleteRisingWaveComputeNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[68]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveComputeNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveComputeNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{68}
}

func (x *DeleteRisingWaveComputeNodeGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *DeleteRisingWaveComputeNodeGroupRequest) GetNodeGroupName() string {
	if x != nil {
		return x.NodeGroupName
	}
	return ""
}

type DeleteRisingWaveComputeNodeGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveComputeNodeGroupResponse) Reset() {
	*x = DeleteRisingWaveComputeNodeGroupResponse{}
	mi := &file_services_k8s_proto_msgTypes[69]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveComputeNodeGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveComputeNodeGroupResponse) ProtoMessage() {}

func (x *DeleteRisingWaveComputeNodeGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[69]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveComputeNodeGroupResponse.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveComputeNodeGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{69}
}

func (x *DeleteRisingWaveComputeNodeGroupResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateRisingWaveNodeGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Component     rw.ComponentType       `protobuf:"varint,2,opt,name=component,proto3,enum=common.rw.ComponentType" json:"component,omitempty"`
	NodeGroup     *rw.NodeGroupSpec      `protobuf:"bytes,3,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRisingWaveNodeGroupRequest) Reset() {
	*x = CreateRisingWaveNodeGroupRequest{}
	mi := &file_services_k8s_proto_msgTypes[70]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRisingWaveNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRisingWaveNodeGroupRequest) ProtoMessage() {}

func (x *CreateRisingWaveNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[70]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRisingWaveNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateRisingWaveNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{70}
}

func (x *CreateRisingWaveNodeGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateRisingWaveNodeGroupRequest) GetComponent() rw.ComponentType {
	if x != nil {
		return x.Component
	}
	return rw.ComponentType(0)
}

func (x *CreateRisingWaveNodeGroupRequest) GetNodeGroup() *rw.NodeGroupSpec {
	if x != nil {
		return x.NodeGroup
	}
	return nil
}

type CreateRisingWaveNodeGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateRisingWaveNodeGroupResponse) Reset() {
	*x = CreateRisingWaveNodeGroupResponse{}
	mi := &file_services_k8s_proto_msgTypes[71]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateRisingWaveNodeGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateRisingWaveNodeGroupResponse) ProtoMessage() {}

func (x *CreateRisingWaveNodeGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[71]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateRisingWaveNodeGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateRisingWaveNodeGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{71}
}

func (x *CreateRisingWaveNodeGroupResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateRisingWaveNodeGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Component     rw.ComponentType       `protobuf:"varint,2,opt,name=component,proto3,enum=common.rw.ComponentType" json:"component,omitempty"`
	NodeGroup     *rw.NodeGroupSpec      `protobuf:"bytes,3,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveNodeGroupRequest) Reset() {
	*x = UpdateRisingWaveNodeGroupRequest{}
	mi := &file_services_k8s_proto_msgTypes[72]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveNodeGroupRequest) ProtoMessage() {}

func (x *UpdateRisingWaveNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[72]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{72}
}

func (x *UpdateRisingWaveNodeGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveNodeGroupRequest) GetComponent() rw.ComponentType {
	if x != nil {
		return x.Component
	}
	return rw.ComponentType(0)
}

func (x *UpdateRisingWaveNodeGroupRequest) GetNodeGroup() *rw.NodeGroupSpec {
	if x != nil {
		return x.NodeGroup
	}
	return nil
}

type UpdateRisingWaveNodeGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveNodeGroupResponse) Reset() {
	*x = UpdateRisingWaveNodeGroupResponse{}
	mi := &file_services_k8s_proto_msgTypes[73]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveNodeGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveNodeGroupResponse) ProtoMessage() {}

func (x *UpdateRisingWaveNodeGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[73]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveNodeGroupResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveNodeGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{73}
}

func (x *UpdateRisingWaveNodeGroupResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteRisingWaveNodeGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Component     rw.ComponentType       `protobuf:"varint,2,opt,name=component,proto3,enum=common.rw.ComponentType" json:"component,omitempty"`
	NodeGroupName string                 `protobuf:"bytes,3,opt,name=node_group_name,json=nodeGroupName,proto3" json:"node_group_name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveNodeGroupRequest) Reset() {
	*x = DeleteRisingWaveNodeGroupRequest{}
	mi := &file_services_k8s_proto_msgTypes[74]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveNodeGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveNodeGroupRequest) ProtoMessage() {}

func (x *DeleteRisingWaveNodeGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[74]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveNodeGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveNodeGroupRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{74}
}

func (x *DeleteRisingWaveNodeGroupRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *DeleteRisingWaveNodeGroupRequest) GetComponent() rw.ComponentType {
	if x != nil {
		return x.Component
	}
	return rw.ComponentType(0)
}

func (x *DeleteRisingWaveNodeGroupRequest) GetNodeGroupName() string {
	if x != nil {
		return x.NodeGroupName
	}
	return ""
}

type DeleteRisingWaveNodeGroupResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteRisingWaveNodeGroupResponse) Reset() {
	*x = DeleteRisingWaveNodeGroupResponse{}
	mi := &file_services_k8s_proto_msgTypes[75]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteRisingWaveNodeGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteRisingWaveNodeGroupResponse) ProtoMessage() {}

func (x *DeleteRisingWaveNodeGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[75]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteRisingWaveNodeGroupResponse.ProtoReflect.Descriptor instead.
func (*DeleteRisingWaveNodeGroupResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{75}
}

func (x *DeleteRisingWaveNodeGroupResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateRisingWaveNodeGroupConfigurationRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Component     rw.ComponentType       `protobuf:"varint,2,opt,name=component,proto3,enum=common.rw.ComponentType" json:"component,omitempty"`
	NodeGroup     string                 `protobuf:"bytes,3,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	NodeConfig    *rw.NodeConfig         `protobuf:"bytes,4,opt,name=node_config,json=nodeConfig,proto3" json:"node_config,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) Reset() {
	*x = UpdateRisingWaveNodeGroupConfigurationRequest{}
	mi := &file_services_k8s_proto_msgTypes[76]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveNodeGroupConfigurationRequest) ProtoMessage() {}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[76]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveNodeGroupConfigurationRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveNodeGroupConfigurationRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{76}
}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) GetComponent() rw.ComponentType {
	if x != nil {
		return x.Component
	}
	return rw.ComponentType(0)
}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) GetNodeGroup() string {
	if x != nil {
		return x.NodeGroup
	}
	return ""
}

func (x *UpdateRisingWaveNodeGroupConfigurationRequest) GetNodeConfig() *rw.NodeConfig {
	if x != nil {
		return x.NodeConfig
	}
	return nil
}

type UpdateRisingWaveNodeGroupConfigurationResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveNodeGroupConfigurationResponse) Reset() {
	*x = UpdateRisingWaveNodeGroupConfigurationResponse{}
	mi := &file_services_k8s_proto_msgTypes[77]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveNodeGroupConfigurationResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveNodeGroupConfigurationResponse) ProtoMessage() {}

func (x *UpdateRisingWaveNodeGroupConfigurationResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[77]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveNodeGroupConfigurationResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveNodeGroupConfigurationResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{77}
}

func (x *UpdateRisingWaveNodeGroupConfigurationResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdateRisingWaveNodeGroupRestartAtRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Component     rw.ComponentType       `protobuf:"varint,2,opt,name=component,proto3,enum=common.rw.ComponentType" json:"component,omitempty"`
	NodeGroup     string                 `protobuf:"bytes,3,opt,name=node_group,json=nodeGroup,proto3" json:"node_group,omitempty"`
	RestartAt     *timestamppb.Timestamp `protobuf:"bytes,4,opt,name=restartAt,proto3" json:"restartAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) Reset() {
	*x = UpdateRisingWaveNodeGroupRestartAtRequest{}
	mi := &file_services_k8s_proto_msgTypes[78]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveNodeGroupRestartAtRequest) ProtoMessage() {}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[78]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveNodeGroupRestartAtRequest.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveNodeGroupRestartAtRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{78}
}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) GetComponent() rw.ComponentType {
	if x != nil {
		return x.Component
	}
	return rw.ComponentType(0)
}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) GetNodeGroup() string {
	if x != nil {
		return x.NodeGroup
	}
	return ""
}

func (x *UpdateRisingWaveNodeGroupRestartAtRequest) GetRestartAt() *timestamppb.Timestamp {
	if x != nil {
		return x.RestartAt
	}
	return nil
}

type UpdateRisingWaveNodeGroupRestartAtResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *update.Status         `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateRisingWaveNodeGroupRestartAtResponse) Reset() {
	*x = UpdateRisingWaveNodeGroupRestartAtResponse{}
	mi := &file_services_k8s_proto_msgTypes[79]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateRisingWaveNodeGroupRestartAtResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateRisingWaveNodeGroupRestartAtResponse) ProtoMessage() {}

func (x *UpdateRisingWaveNodeGroupRestartAtResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[79]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateRisingWaveNodeGroupRestartAtResponse.ProtoReflect.Descriptor instead.
func (*UpdateRisingWaveNodeGroupRestartAtResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{79}
}

func (x *UpdateRisingWaveNodeGroupRestartAtResponse) GetStatus() *update.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeletePersistentVolumeClaimsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePersistentVolumeClaimsRequest) Reset() {
	*x = DeletePersistentVolumeClaimsRequest{}
	mi := &file_services_k8s_proto_msgTypes[80]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePersistentVolumeClaimsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePersistentVolumeClaimsRequest) ProtoMessage() {}

func (x *DeletePersistentVolumeClaimsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[80]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePersistentVolumeClaimsRequest.ProtoReflect.Descriptor instead.
func (*DeletePersistentVolumeClaimsRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{80}
}

func (x *DeletePersistentVolumeClaimsRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeletePersistentVolumeClaimsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePersistentVolumeClaimsResponse) Reset() {
	*x = DeletePersistentVolumeClaimsResponse{}
	mi := &file_services_k8s_proto_msgTypes[81]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePersistentVolumeClaimsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePersistentVolumeClaimsResponse) ProtoMessage() {}

func (x *DeletePersistentVolumeClaimsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[81]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePersistentVolumeClaimsResponse.ProtoReflect.Descriptor instead.
func (*DeletePersistentVolumeClaimsResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{81}
}

func (x *DeletePersistentVolumeClaimsResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPersistentVolumeClaimsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPersistentVolumeClaimsRequest) Reset() {
	*x = GetPersistentVolumeClaimsRequest{}
	mi := &file_services_k8s_proto_msgTypes[82]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPersistentVolumeClaimsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersistentVolumeClaimsRequest) ProtoMessage() {}

func (x *GetPersistentVolumeClaimsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[82]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersistentVolumeClaimsRequest.ProtoReflect.Descriptor instead.
func (*GetPersistentVolumeClaimsRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{82}
}

func (x *GetPersistentVolumeClaimsRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetPersistentVolumeClaimsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPersistentVolumeClaimsResponse) Reset() {
	*x = GetPersistentVolumeClaimsResponse{}
	mi := &file_services_k8s_proto_msgTypes[83]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPersistentVolumeClaimsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPersistentVolumeClaimsResponse) ProtoMessage() {}

func (x *GetPersistentVolumeClaimsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[83]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPersistentVolumeClaimsResponse.ProtoReflect.Descriptor instead.
func (*GetPersistentVolumeClaimsResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{83}
}

func (x *GetPersistentVolumeClaimsResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreatePersistentVolumeClaimRequest struct {
	state         protoimpl.MessageState         `protogen:"open.v1"`
	ResourceMeta  *resource.Meta                 `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Spec          *k8s.PersistentVolumeClaimSpec `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePersistentVolumeClaimRequest) Reset() {
	*x = CreatePersistentVolumeClaimRequest{}
	mi := &file_services_k8s_proto_msgTypes[84]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePersistentVolumeClaimRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePersistentVolumeClaimRequest) ProtoMessage() {}

func (x *CreatePersistentVolumeClaimRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[84]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePersistentVolumeClaimRequest.ProtoReflect.Descriptor instead.
func (*CreatePersistentVolumeClaimRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{84}
}

func (x *CreatePersistentVolumeClaimRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreatePersistentVolumeClaimRequest) GetSpec() *k8s.PersistentVolumeClaimSpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

type CreatePersistentVolumeClaimResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePersistentVolumeClaimResponse) Reset() {
	*x = CreatePersistentVolumeClaimResponse{}
	mi := &file_services_k8s_proto_msgTypes[85]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePersistentVolumeClaimResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePersistentVolumeClaimResponse) ProtoMessage() {}

func (x *CreatePersistentVolumeClaimResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[85]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePersistentVolumeClaimResponse.ProtoReflect.Descriptor instead.
func (*CreatePersistentVolumeClaimResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{85}
}

func (x *CreatePersistentVolumeClaimResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

// managing helm release
type InstallHelmReleaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task resource meta
	ResourceMeta  *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ReleaseMeta   *resource.Meta `protobuf:"bytes,2,opt,name=release_meta,json=releaseMeta,proto3" json:"release_meta,omitempty"`
	ChartUrl      string         `protobuf:"bytes,3,opt,name=chart_url,json=chartUrl,proto3" json:"chart_url,omitempty"`
	ValuesJson    string         `protobuf:"bytes,4,opt,name=values_json,json=valuesJson,proto3" json:"values_json,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InstallHelmReleaseRequest) Reset() {
	*x = InstallHelmReleaseRequest{}
	mi := &file_services_k8s_proto_msgTypes[86]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallHelmReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallHelmReleaseRequest) ProtoMessage() {}

func (x *InstallHelmReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[86]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallHelmReleaseRequest.ProtoReflect.Descriptor instead.
func (*InstallHelmReleaseRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{86}
}

func (x *InstallHelmReleaseRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *InstallHelmReleaseRequest) GetReleaseMeta() *resource.Meta {
	if x != nil {
		return x.ReleaseMeta
	}
	return nil
}

func (x *InstallHelmReleaseRequest) GetChartUrl() string {
	if x != nil {
		return x.ChartUrl
	}
	return ""
}

func (x *InstallHelmReleaseRequest) GetValuesJson() string {
	if x != nil {
		return x.ValuesJson
	}
	return ""
}

type InstallHelmReleaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the creation status of the corresponding task
	Status        *creation.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InstallHelmReleaseResponse) Reset() {
	*x = InstallHelmReleaseResponse{}
	mi := &file_services_k8s_proto_msgTypes[87]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InstallHelmReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InstallHelmReleaseResponse) ProtoMessage() {}

func (x *InstallHelmReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[87]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InstallHelmReleaseResponse.ProtoReflect.Descriptor instead.
func (*InstallHelmReleaseResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{87}
}

func (x *InstallHelmReleaseResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpgradeHelmReleaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task resource meta
	ResourceMeta  *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ReleaseMeta   *resource.Meta `protobuf:"bytes,2,opt,name=release_meta,json=releaseMeta,proto3" json:"release_meta,omitempty"`
	ChartUrl      string         `protobuf:"bytes,3,opt,name=chart_url,json=chartUrl,proto3" json:"chart_url,omitempty"`
	ValuesJson    string         `protobuf:"bytes,4,opt,name=values_json,json=valuesJson,proto3" json:"values_json,omitempty"`
	Install       bool           `protobuf:"varint,5,opt,name=install,proto3" json:"install,omitempty"` // if true, install the release if it does not exist
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpgradeHelmReleaseRequest) Reset() {
	*x = UpgradeHelmReleaseRequest{}
	mi := &file_services_k8s_proto_msgTypes[88]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpgradeHelmReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeHelmReleaseRequest) ProtoMessage() {}

func (x *UpgradeHelmReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[88]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeHelmReleaseRequest.ProtoReflect.Descriptor instead.
func (*UpgradeHelmReleaseRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{88}
}

func (x *UpgradeHelmReleaseRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpgradeHelmReleaseRequest) GetReleaseMeta() *resource.Meta {
	if x != nil {
		return x.ReleaseMeta
	}
	return nil
}

func (x *UpgradeHelmReleaseRequest) GetChartUrl() string {
	if x != nil {
		return x.ChartUrl
	}
	return ""
}

func (x *UpgradeHelmReleaseRequest) GetValuesJson() string {
	if x != nil {
		return x.ValuesJson
	}
	return ""
}

func (x *UpgradeHelmReleaseRequest) GetInstall() bool {
	if x != nil {
		return x.Install
	}
	return false
}

type UpgradeHelmReleaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the creation status of the corresponding task
	Status        *creation.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpgradeHelmReleaseResponse) Reset() {
	*x = UpgradeHelmReleaseResponse{}
	mi := &file_services_k8s_proto_msgTypes[89]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpgradeHelmReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpgradeHelmReleaseResponse) ProtoMessage() {}

func (x *UpgradeHelmReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[89]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpgradeHelmReleaseResponse.ProtoReflect.Descriptor instead.
func (*UpgradeHelmReleaseResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{89}
}

func (x *UpgradeHelmReleaseResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UninstallHelmReleaseRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// task resource meta
	ResourceMeta  *resource.Meta `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ReleaseMeta   *resource.Meta `protobuf:"bytes,2,opt,name=release_meta,json=releaseMeta,proto3" json:"release_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UninstallHelmReleaseRequest) Reset() {
	*x = UninstallHelmReleaseRequest{}
	mi := &file_services_k8s_proto_msgTypes[90]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UninstallHelmReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UninstallHelmReleaseRequest) ProtoMessage() {}

func (x *UninstallHelmReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[90]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UninstallHelmReleaseRequest.ProtoReflect.Descriptor instead.
func (*UninstallHelmReleaseRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{90}
}

func (x *UninstallHelmReleaseRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UninstallHelmReleaseRequest) GetReleaseMeta() *resource.Meta {
	if x != nil {
		return x.ReleaseMeta
	}
	return nil
}

type UninstallHelmReleaseResponse struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// the creation status of the corresponding task
	Status        *creation.Status `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UninstallHelmReleaseResponse) Reset() {
	*x = UninstallHelmReleaseResponse{}
	mi := &file_services_k8s_proto_msgTypes[91]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UninstallHelmReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UninstallHelmReleaseResponse) ProtoMessage() {}

func (x *UninstallHelmReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[91]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UninstallHelmReleaseResponse.ProtoReflect.Descriptor instead.
func (*UninstallHelmReleaseResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{91}
}

func (x *UninstallHelmReleaseResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetHelmReleaseRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ReleaseMeta   *resource.Meta         `protobuf:"bytes,1,opt,name=release_meta,json=releaseMeta,proto3" json:"release_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHelmReleaseRequest) Reset() {
	*x = GetHelmReleaseRequest{}
	mi := &file_services_k8s_proto_msgTypes[92]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHelmReleaseRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHelmReleaseRequest) ProtoMessage() {}

func (x *GetHelmReleaseRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[92]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHelmReleaseRequest.ProtoReflect.Descriptor instead.
func (*GetHelmReleaseRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{92}
}

func (x *GetHelmReleaseRequest) GetReleaseMeta() *resource.Meta {
	if x != nil {
		return x.ReleaseMeta
	}
	return nil
}

type GetHelmReleaseResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	HelmRelease   *k8s.HelmRelease       `protobuf:"bytes,1,opt,name=helm_release,json=helmRelease,proto3" json:"helm_release,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetHelmReleaseResponse) Reset() {
	*x = GetHelmReleaseResponse{}
	mi := &file_services_k8s_proto_msgTypes[93]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetHelmReleaseResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetHelmReleaseResponse) ProtoMessage() {}

func (x *GetHelmReleaseResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[93]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetHelmReleaseResponse.ProtoReflect.Descriptor instead.
func (*GetHelmReleaseResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{93}
}

func (x *GetHelmReleaseResponse) GetHelmRelease() *k8s.HelmRelease {
	if x != nil {
		return x.HelmRelease
	}
	return nil
}

type GetPodPhasesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPodPhasesRequest) Reset() {
	*x = GetPodPhasesRequest{}
	mi := &file_services_k8s_proto_msgTypes[94]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodPhasesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodPhasesRequest) ProtoMessage() {}

func (x *GetPodPhasesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[94]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodPhasesRequest.ProtoReflect.Descriptor instead.
func (*GetPodPhasesRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{94}
}

func (x *GetPodPhasesRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetPodPhasesResponse struct {
	state         protoimpl.MessageState  `protogen:"open.v1"`
	PodToPhase    map[string]k8s.PodPhase `protobuf:"bytes,1,rep,name=pod_to_phase,json=podToPhase,proto3" json:"pod_to_phase,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"varint,2,opt,name=value,enum=common.k8s.PodPhase"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPodPhasesResponse) Reset() {
	*x = GetPodPhasesResponse{}
	mi := &file_services_k8s_proto_msgTypes[95]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodPhasesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodPhasesResponse) ProtoMessage() {}

func (x *GetPodPhasesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[95]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodPhasesResponse.ProtoReflect.Descriptor instead.
func (*GetPodPhasesResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{95}
}

func (x *GetPodPhasesResponse) GetPodToPhase() map[string]k8s.PodPhase {
	if x != nil {
		return x.PodToPhase
	}
	return nil
}

// Valid statuses includes READY and NOT_FOUND.
// Otherwise an RPC error will be returned.
type RestartStatefulSetRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartStatefulSetRequest) Reset() {
	*x = RestartStatefulSetRequest{}
	mi := &file_services_k8s_proto_msgTypes[96]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartStatefulSetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartStatefulSetRequest) ProtoMessage() {}

func (x *RestartStatefulSetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[96]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartStatefulSetRequest.ProtoReflect.Descriptor instead.
func (*RestartStatefulSetRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{96}
}

func (x *RestartStatefulSetRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type RestartStatefulSetResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartStatefulSetResponse) Reset() {
	*x = RestartStatefulSetResponse{}
	mi := &file_services_k8s_proto_msgTypes[97]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartStatefulSetResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartStatefulSetResponse) ProtoMessage() {}

func (x *RestartStatefulSetResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[97]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartStatefulSetResponse.ProtoReflect.Descriptor instead.
func (*RestartStatefulSetResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{97}
}

// Valid statuses includes READY and NOT_FOUND.
// Otherwise an RPC error will be returned.
type RestartDeploymentRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartDeploymentRequest) Reset() {
	*x = RestartDeploymentRequest{}
	mi := &file_services_k8s_proto_msgTypes[98]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartDeploymentRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartDeploymentRequest) ProtoMessage() {}

func (x *RestartDeploymentRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[98]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartDeploymentRequest.ProtoReflect.Descriptor instead.
func (*RestartDeploymentRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{98}
}

func (x *RestartDeploymentRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type RestartDeploymentResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RestartDeploymentResponse) Reset() {
	*x = RestartDeploymentResponse{}
	mi := &file_services_k8s_proto_msgTypes[99]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RestartDeploymentResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RestartDeploymentResponse) ProtoMessage() {}

func (x *RestartDeploymentResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[99]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RestartDeploymentResponse.ProtoReflect.Descriptor instead.
func (*RestartDeploymentResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{99}
}

// Valid statuses includes READY and NOT_FOUND.
// Otherwise an RPC error will be returned.
type GetStatefulSetReplicasStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStatefulSetReplicasStatusRequest) Reset() {
	*x = GetStatefulSetReplicasStatusRequest{}
	mi := &file_services_k8s_proto_msgTypes[100]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStatefulSetReplicasStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatefulSetReplicasStatusRequest) ProtoMessage() {}

func (x *GetStatefulSetReplicasStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[100]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatefulSetReplicasStatusRequest.ProtoReflect.Descriptor instead.
func (*GetStatefulSetReplicasStatusRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{100}
}

func (x *GetStatefulSetReplicasStatusRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetStatefulSetReplicasStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetStatefulSetReplicasStatusResponse) Reset() {
	*x = GetStatefulSetReplicasStatusResponse{}
	mi := &file_services_k8s_proto_msgTypes[101]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetStatefulSetReplicasStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetStatefulSetReplicasStatusResponse) ProtoMessage() {}

func (x *GetStatefulSetReplicasStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[101]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetStatefulSetReplicasStatusResponse.ProtoReflect.Descriptor instead.
func (*GetStatefulSetReplicasStatusResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{101}
}

func (x *GetStatefulSetReplicasStatusResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetDeploymentReplicasStatusRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeploymentReplicasStatusRequest) Reset() {
	*x = GetDeploymentReplicasStatusRequest{}
	mi := &file_services_k8s_proto_msgTypes[102]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeploymentReplicasStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeploymentReplicasStatusRequest) ProtoMessage() {}

func (x *GetDeploymentReplicasStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[102]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeploymentReplicasStatusRequest.ProtoReflect.Descriptor instead.
func (*GetDeploymentReplicasStatusRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{102}
}

func (x *GetDeploymentReplicasStatusRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetDeploymentReplicasStatusResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *resource.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetDeploymentReplicasStatusResponse) Reset() {
	*x = GetDeploymentReplicasStatusResponse{}
	mi := &file_services_k8s_proto_msgTypes[103]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetDeploymentReplicasStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDeploymentReplicasStatusResponse) ProtoMessage() {}

func (x *GetDeploymentReplicasStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[103]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDeploymentReplicasStatusResponse.ProtoReflect.Descriptor instead.
func (*GetDeploymentReplicasStatusResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{103}
}

func (x *GetDeploymentReplicasStatusResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateServiceMonitorRequest struct {
	state              protoimpl.MessageState         `protogen:"open.v1"`
	ResourceMeta       *resource.Meta                 `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ServiceMonitorSpec *prometheus.ServiceMonitorSpec `protobuf:"bytes,2,opt,name=service_monitor_spec,json=serviceMonitorSpec,proto3" json:"service_monitor_spec,omitempty"`
	Labels             map[string]string              `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Annotations        map[string]string              `protobuf:"bytes,4,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *CreateServiceMonitorRequest) Reset() {
	*x = CreateServiceMonitorRequest{}
	mi := &file_services_k8s_proto_msgTypes[104]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceMonitorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceMonitorRequest) ProtoMessage() {}

func (x *CreateServiceMonitorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[104]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceMonitorRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceMonitorRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{104}
}

func (x *CreateServiceMonitorRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateServiceMonitorRequest) GetServiceMonitorSpec() *prometheus.ServiceMonitorSpec {
	if x != nil {
		return x.ServiceMonitorSpec
	}
	return nil
}

func (x *CreateServiceMonitorRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreateServiceMonitorRequest) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateServiceMonitorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceMonitorResponse) Reset() {
	*x = CreateServiceMonitorResponse{}
	mi := &file_services_k8s_proto_msgTypes[105]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceMonitorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceMonitorResponse) ProtoMessage() {}

func (x *CreateServiceMonitorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[105]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceMonitorResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceMonitorResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{105}
}

func (x *CreateServiceMonitorResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreatePodMonitoringRequest struct {
	state             protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta      *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	PodMonitoringSpec *gmp.PodMonitoringSpec `protobuf:"bytes,2,opt,name=pod_monitoring_spec,json=podMonitoringSpec,proto3" json:"pod_monitoring_spec,omitempty"`
	Labels            map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	Annotations       map[string]string      `protobuf:"bytes,4,rep,name=annotations,proto3" json:"annotations,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields     protoimpl.UnknownFields
	sizeCache         protoimpl.SizeCache
}

func (x *CreatePodMonitoringRequest) Reset() {
	*x = CreatePodMonitoringRequest{}
	mi := &file_services_k8s_proto_msgTypes[106]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePodMonitoringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePodMonitoringRequest) ProtoMessage() {}

func (x *CreatePodMonitoringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[106]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePodMonitoringRequest.ProtoReflect.Descriptor instead.
func (*CreatePodMonitoringRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{106}
}

func (x *CreatePodMonitoringRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreatePodMonitoringRequest) GetPodMonitoringSpec() *gmp.PodMonitoringSpec {
	if x != nil {
		return x.PodMonitoringSpec
	}
	return nil
}

func (x *CreatePodMonitoringRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

func (x *CreatePodMonitoringRequest) GetAnnotations() map[string]string {
	if x != nil {
		return x.Annotations
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreatePodMonitoringResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePodMonitoringResponse) Reset() {
	*x = CreatePodMonitoringResponse{}
	mi := &file_services_k8s_proto_msgTypes[107]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePodMonitoringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePodMonitoringResponse) ProtoMessage() {}

func (x *CreatePodMonitoringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[107]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePodMonitoringResponse.ProtoReflect.Descriptor instead.
func (*CreatePodMonitoringResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{107}
}

func (x *CreatePodMonitoringResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	ServiceSpec   *k8s.ServiceSpec       `protobuf:"bytes,2,opt,name=service_spec,json=serviceSpec,proto3" json:"service_spec,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	mi := &file_services_k8s_proto_msgTypes[108]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[108]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{108}
}

func (x *CreateServiceRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateServiceRequest) GetServiceSpec() *k8s.ServiceSpec {
	if x != nil {
		return x.ServiceSpec
	}
	return nil
}

func (x *CreateServiceRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Valid statuses includes SCHEDULED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	mi := &file_services_k8s_proto_msgTypes[109]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[109]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{109}
}

func (x *CreateServiceResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateNetworkPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Spec          *k8s.NetworkPolicySpec `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNetworkPolicyRequest) Reset() {
	*x = CreateNetworkPolicyRequest{}
	mi := &file_services_k8s_proto_msgTypes[110]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNetworkPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNetworkPolicyRequest) ProtoMessage() {}

func (x *CreateNetworkPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[110]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNetworkPolicyRequest.ProtoReflect.Descriptor instead.
func (*CreateNetworkPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{110}
}

func (x *CreateNetworkPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateNetworkPolicyRequest) GetSpec() *k8s.NetworkPolicySpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *CreateNetworkPolicyRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
type CreateNetworkPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateNetworkPolicyResponse) Reset() {
	*x = CreateNetworkPolicyResponse{}
	mi := &file_services_k8s_proto_msgTypes[111]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateNetworkPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateNetworkPolicyResponse) ProtoMessage() {}

func (x *CreateNetworkPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[111]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateNetworkPolicyResponse.ProtoReflect.Descriptor instead.
func (*CreateNetworkPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{111}
}

func (x *CreateNetworkPolicyResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type CreateOrUpdateNetworkPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	Spec          *k8s.NetworkPolicySpec `protobuf:"bytes,2,opt,name=spec,proto3" json:"spec,omitempty"`
	Labels        map[string]string      `protobuf:"bytes,3,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrUpdateNetworkPolicyRequest) Reset() {
	*x = CreateOrUpdateNetworkPolicyRequest{}
	mi := &file_services_k8s_proto_msgTypes[112]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrUpdateNetworkPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateNetworkPolicyRequest) ProtoMessage() {}

func (x *CreateOrUpdateNetworkPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[112]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateNetworkPolicyRequest.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateNetworkPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{112}
}

func (x *CreateOrUpdateNetworkPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreateOrUpdateNetworkPolicyRequest) GetSpec() *k8s.NetworkPolicySpec {
	if x != nil {
		return x.Spec
	}
	return nil
}

func (x *CreateOrUpdateNetworkPolicyRequest) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

type CreateOrUpdateNetworkPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateOrUpdateNetworkPolicyResponse) Reset() {
	*x = CreateOrUpdateNetworkPolicyResponse{}
	mi := &file_services_k8s_proto_msgTypes[113]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateOrUpdateNetworkPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateOrUpdateNetworkPolicyResponse) ProtoMessage() {}

func (x *CreateOrUpdateNetworkPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[113]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateOrUpdateNetworkPolicyResponse.ProtoReflect.Descriptor instead.
func (*CreateOrUpdateNetworkPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{113}
}

type CreatePostgreSqlRequest struct {
	state          protoimpl.MessageState     `protogen:"open.v1"`
	ResourceMeta   *resource.Meta             `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	PostgresqlSpec *postgresql.PostgreSqlSpec `protobuf:"bytes,2,opt,name=postgresql_spec,json=postgresqlSpec,proto3" json:"postgresql_spec,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreatePostgreSqlRequest) Reset() {
	*x = CreatePostgreSqlRequest{}
	mi := &file_services_k8s_proto_msgTypes[114]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePostgreSqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePostgreSqlRequest) ProtoMessage() {}

func (x *CreatePostgreSqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[114]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePostgreSqlRequest.ProtoReflect.Descriptor instead.
func (*CreatePostgreSqlRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{114}
}

func (x *CreatePostgreSqlRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *CreatePostgreSqlRequest) GetPostgresqlSpec() *postgresql.PostgreSqlSpec {
	if x != nil {
		return x.PostgresqlSpec
	}
	return nil
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
type CreatePostgreSqlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *creation.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreatePostgreSqlResponse) Reset() {
	*x = CreatePostgreSqlResponse{}
	mi := &file_services_k8s_proto_msgTypes[115]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreatePostgreSqlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreatePostgreSqlResponse) ProtoMessage() {}

func (x *CreatePostgreSqlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[115]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreatePostgreSqlResponse.ProtoReflect.Descriptor instead.
func (*CreatePostgreSqlResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{115}
}

func (x *CreatePostgreSqlResponse) GetStatus() *creation.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeletePostgreSqlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostgreSqlRequest) Reset() {
	*x = DeletePostgreSqlRequest{}
	mi := &file_services_k8s_proto_msgTypes[116]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostgreSqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostgreSqlRequest) ProtoMessage() {}

func (x *DeletePostgreSqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[116]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostgreSqlRequest.ProtoReflect.Descriptor instead.
func (*DeletePostgreSqlRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{116}
}

func (x *DeletePostgreSqlRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Statuses - SCHEDULED, NOT_FOUND
type DeletePostgreSqlResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePostgreSqlResponse) Reset() {
	*x = DeletePostgreSqlResponse{}
	mi := &file_services_k8s_proto_msgTypes[117]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePostgreSqlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePostgreSqlResponse) ProtoMessage() {}

func (x *DeletePostgreSqlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[117]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePostgreSqlResponse.ProtoReflect.Descriptor instead.
func (*DeletePostgreSqlResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{117}
}

func (x *DeletePostgreSqlResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type UpdatePostgreSqlRequest struct {
	state        protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	// allow to update: number_of_instances, resources, volume
	PostgresqlSpec *postgresql.PostgreSqlSpec `protobuf:"bytes,2,opt,name=postgresql_spec,json=postgresqlSpec,proto3" json:"postgresql_spec,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *UpdatePostgreSqlRequest) Reset() {
	*x = UpdatePostgreSqlRequest{}
	mi := &file_services_k8s_proto_msgTypes[118]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePostgreSqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePostgreSqlRequest) ProtoMessage() {}

func (x *UpdatePostgreSqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[118]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePostgreSqlRequest.ProtoReflect.Descriptor instead.
func (*UpdatePostgreSqlRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{118}
}

func (x *UpdatePostgreSqlRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

func (x *UpdatePostgreSqlRequest) GetPostgresqlSpec() *postgresql.PostgreSqlSpec {
	if x != nil {
		return x.PostgresqlSpec
	}
	return nil
}

type UpdatePostgreSqlResponse struct {
	state         protoimpl.MessageState      `protogen:"open.v1"`
	Status        postgresql.UpdateStatusCode `protobuf:"varint,1,opt,name=status,proto3,enum=common.postgresql.UpdateStatusCode" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdatePostgreSqlResponse) Reset() {
	*x = UpdatePostgreSqlResponse{}
	mi := &file_services_k8s_proto_msgTypes[119]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdatePostgreSqlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdatePostgreSqlResponse) ProtoMessage() {}

func (x *UpdatePostgreSqlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[119]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdatePostgreSqlResponse.ProtoReflect.Descriptor instead.
func (*UpdatePostgreSqlResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{119}
}

func (x *UpdatePostgreSqlResponse) GetStatus() postgresql.UpdateStatusCode {
	if x != nil {
		return x.Status
	}
	return postgresql.UpdateStatusCode(0)
}

type GetPostgreSqlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPostgreSqlRequest) Reset() {
	*x = GetPostgreSqlRequest{}
	mi := &file_services_k8s_proto_msgTypes[120]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostgreSqlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostgreSqlRequest) ProtoMessage() {}

func (x *GetPostgreSqlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[120]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostgreSqlRequest.ProtoReflect.Descriptor instead.
func (*GetPostgreSqlRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{120}
}

func (x *GetPostgreSqlRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type GetPostgreSqlResponse struct {
	state          protoimpl.MessageState     `protogen:"open.v1"`
	Status         *resource.Status           `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	PostgresqlSpec *postgresql.PostgreSqlSpec `protobuf:"bytes,2,opt,name=postgresql_spec,json=postgresqlSpec,proto3" json:"postgresql_spec,omitempty"`
	SecretRef      *resource.Meta             `protobuf:"bytes,3,opt,name=secret_ref,json=secretRef,proto3" json:"secret_ref,omitempty"`
	Credentials    *postgresql.Credentials    `protobuf:"bytes,4,opt,name=credentials,proto3" json:"credentials,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *GetPostgreSqlResponse) Reset() {
	*x = GetPostgreSqlResponse{}
	mi := &file_services_k8s_proto_msgTypes[121]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPostgreSqlResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPostgreSqlResponse) ProtoMessage() {}

func (x *GetPostgreSqlResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[121]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPostgreSqlResponse.ProtoReflect.Descriptor instead.
func (*GetPostgreSqlResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{121}
}

func (x *GetPostgreSqlResponse) GetStatus() *resource.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

func (x *GetPostgreSqlResponse) GetPostgresqlSpec() *postgresql.PostgreSqlSpec {
	if x != nil {
		return x.PostgresqlSpec
	}
	return nil
}

func (x *GetPostgreSqlResponse) GetSecretRef() *resource.Meta {
	if x != nil {
		return x.SecretRef
	}
	return nil
}

func (x *GetPostgreSqlResponse) GetCredentials() *postgresql.Credentials {
	if x != nil {
		return x.Credentials
	}
	return nil
}

type DeleteNetworkPolicyRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNetworkPolicyRequest) Reset() {
	*x = DeleteNetworkPolicyRequest{}
	mi := &file_services_k8s_proto_msgTypes[122]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNetworkPolicyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNetworkPolicyRequest) ProtoMessage() {}

func (x *DeleteNetworkPolicyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[122]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNetworkPolicyRequest.ProtoReflect.Descriptor instead.
func (*DeleteNetworkPolicyRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{122}
}

func (x *DeleteNetworkPolicyRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

// Valid statuses includes SCHEDULED and NOT_FOUND.
// Otherwise an RPC error will be returned.
type DeleteNetworkPolicyResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteNetworkPolicyResponse) Reset() {
	*x = DeleteNetworkPolicyResponse{}
	mi := &file_services_k8s_proto_msgTypes[123]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteNetworkPolicyResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteNetworkPolicyResponse) ProtoMessage() {}

func (x *DeleteNetworkPolicyResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[123]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteNetworkPolicyResponse.ProtoReflect.Descriptor instead.
func (*DeleteNetworkPolicyResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{123}
}

func (x *DeleteNetworkPolicyResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeleteServiceMonitorRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceMonitorRequest) Reset() {
	*x = DeleteServiceMonitorRequest{}
	mi := &file_services_k8s_proto_msgTypes[124]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceMonitorRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceMonitorRequest) ProtoMessage() {}

func (x *DeleteServiceMonitorRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[124]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceMonitorRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceMonitorRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{124}
}

func (x *DeleteServiceMonitorRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeleteServiceMonitorResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceMonitorResponse) Reset() {
	*x = DeleteServiceMonitorResponse{}
	mi := &file_services_k8s_proto_msgTypes[125]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceMonitorResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceMonitorResponse) ProtoMessage() {}

func (x *DeleteServiceMonitorResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[125]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceMonitorResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceMonitorResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{125}
}

func (x *DeleteServiceMonitorResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type DeletePodMonitoringRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ResourceMeta  *resource.Meta         `protobuf:"bytes,1,opt,name=resource_meta,json=resourceMeta,proto3" json:"resource_meta,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePodMonitoringRequest) Reset() {
	*x = DeletePodMonitoringRequest{}
	mi := &file_services_k8s_proto_msgTypes[126]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePodMonitoringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePodMonitoringRequest) ProtoMessage() {}

func (x *DeletePodMonitoringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[126]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePodMonitoringRequest.ProtoReflect.Descriptor instead.
func (*DeletePodMonitoringRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{126}
}

func (x *DeletePodMonitoringRequest) GetResourceMeta() *resource.Meta {
	if x != nil {
		return x.ResourceMeta
	}
	return nil
}

type DeletePodMonitoringResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Status        *deletion.Status       `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeletePodMonitoringResponse) Reset() {
	*x = DeletePodMonitoringResponse{}
	mi := &file_services_k8s_proto_msgTypes[127]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeletePodMonitoringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeletePodMonitoringResponse) ProtoMessage() {}

func (x *DeletePodMonitoringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[127]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeletePodMonitoringResponse.ProtoReflect.Descriptor instead.
func (*DeletePodMonitoringResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{127}
}

func (x *DeletePodMonitoringResponse) GetStatus() *deletion.Status {
	if x != nil {
		return x.Status
	}
	return nil
}

type GetPodsRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Namespace     string                 `protobuf:"bytes,1,opt,name=namespace,proto3" json:"namespace,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPodsRequest) Reset() {
	*x = GetPodsRequest{}
	mi := &file_services_k8s_proto_msgTypes[128]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodsRequest) ProtoMessage() {}

func (x *GetPodsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[128]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodsRequest.ProtoReflect.Descriptor instead.
func (*GetPodsRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{128}
}

func (x *GetPodsRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

type GetPodsResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Pods          []*k8s.Pod             `protobuf:"bytes,1,rep,name=pods,proto3" json:"pods,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetPodsResponse) Reset() {
	*x = GetPodsResponse{}
	mi := &file_services_k8s_proto_msgTypes[129]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetPodsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetPodsResponse) ProtoMessage() {}

func (x *GetPodsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[129]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetPodsResponse.ProtoReflect.Descriptor instead.
func (*GetPodsResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{129}
}

func (x *GetPodsResponse) GetPods() []*k8s.Pod {
	if x != nil {
		return x.Pods
	}
	return nil
}

type GetClusterAccessRequest struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// If not specified, a default 30 mins timeout will be enforced.
	// Timeout cannot exceed 24 hours
	Timeout       *durationpb.Duration `protobuf:"bytes,1,opt,name=timeout,proto3" json:"timeout,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetClusterAccessRequest) Reset() {
	*x = GetClusterAccessRequest{}
	mi := &file_services_k8s_proto_msgTypes[130]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterAccessRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterAccessRequest) ProtoMessage() {}

func (x *GetClusterAccessRequest) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[130]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterAccessRequest.ProtoReflect.Descriptor instead.
func (*GetClusterAccessRequest) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{130}
}

func (x *GetClusterAccessRequest) GetTimeout() *durationpb.Duration {
	if x != nil {
		return x.Timeout
	}
	return nil
}

type GetClusterAccessResponse struct {
	state                      protoimpl.MessageState `protogen:"open.v1"`
	ClusterEndpoint            string                 `protobuf:"bytes,1,opt,name=cluster_endpoint,json=clusterEndpoint,proto3" json:"cluster_endpoint,omitempty"`
	ClusterCaCertificateBase64 string                 `protobuf:"bytes,2,opt,name=cluster_ca_certificate_base64,json=clusterCaCertificateBase64,proto3" json:"cluster_ca_certificate_base64,omitempty"`
	Token                      string                 `protobuf:"bytes,3,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields              protoimpl.UnknownFields
	sizeCache                  protoimpl.SizeCache
}

func (x *GetClusterAccessResponse) Reset() {
	*x = GetClusterAccessResponse{}
	mi := &file_services_k8s_proto_msgTypes[131]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetClusterAccessResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetClusterAccessResponse) ProtoMessage() {}

func (x *GetClusterAccessResponse) ProtoReflect() protoreflect.Message {
	mi := &file_services_k8s_proto_msgTypes[131]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetClusterAccessResponse.ProtoReflect.Descriptor instead.
func (*GetClusterAccessResponse) Descriptor() ([]byte, []int) {
	return file_services_k8s_proto_rawDescGZIP(), []int{131}
}

func (x *GetClusterAccessResponse) GetClusterEndpoint() string {
	if x != nil {
		return x.ClusterEndpoint
	}
	return ""
}

func (x *GetClusterAccessResponse) GetClusterCaCertificateBase64() string {
	if x != nil {
		return x.ClusterCaCertificateBase64
	}
	return ""
}

func (x *GetClusterAccessResponse) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

var File_services_k8s_proto protoreflect.FileDescriptor

var file_services_k8s_proto_rawDesc = string([]byte{
	0x0a, 0x12, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x12, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x13, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x15, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x10, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x67, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x17, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x70, 0x6f, 0x73, 0x74, 0x67,
	0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x64, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xd9, 0x01, 0x0a,
	0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x48, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61,
	0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x53, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xd7, 0x01,
	0x0a, 0x15, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x02, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x18, 0x0a, 0x16, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x51, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x47, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x54, 0x0a,
	0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x59, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x58, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x56, 0x0a,
	0x18, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75,
	0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x4c, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x59, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x58,
	0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xe7, 0x01, 0x0a, 0x1d, 0x41, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f,
	0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4f, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02,
	0x38, 0x01, 0x22, 0x20, 0x0a, 0x1e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x93, 0x01, 0x0a, 0x16, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3d, 0x0a, 0x0f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x0d, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x53, 0x70, 0x65, 0x63, 0x22, 0x53, 0x0a, 0x17, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x51, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x22, 0x86, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x0f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x0d, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x53, 0x70, 0x65, 0x63, 0x22, 0xe4, 0x01, 0x0a, 0x16,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x12, 0x4a, 0x0a, 0x14, 0x66, 0x72, 0x6f, 0x6d, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x42, 0x02, 0x18, 0x01, 0x52, 0x11, 0x66, 0x72, 0x6f,
	0x6d, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x53, 0x70, 0x65, 0x63, 0x12, 0x42,
	0x0a, 0x12, 0x74, 0x6f, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x5f, 0x6d, 0x61, 0x70, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x52, 0x0f, 0x74, 0x6f, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x53, 0x70,
	0x65, 0x63, 0x22, 0x19, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x54, 0x0a,
	0x16, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x53, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x86, 0x01, 0x0a, 0x13, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x0b,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x70, 0x65,
	0x63, 0x22, 0x50, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x4e, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x79, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x33, 0x0a, 0x0b, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x22, 0x51,
	0x0a, 0x13, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x22, 0x86, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x33, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f,
	0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x0a,
	0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x70, 0x65, 0x63, 0x22, 0x16, 0x0a, 0x14, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x50, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xb9, 0x03, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52,
	0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x42, 0x0a, 0x0f,
	0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x77, 0x2e, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x52, 0x0e, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x12, 0x49, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x58, 0x0a, 0x0b, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x36, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x28, 0x0a, 0x0e, 0x53, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x06, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x31, 0x0a, 0x07, 0x45, 0x6e,
	0x76, 0x56, 0x61, 0x72, 0x73, 0x12, 0x26, 0x0a, 0x04, 0x76, 0x61, 0x72, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x52, 0x04, 0x76, 0x61, 0x72, 0x73, 0x22, 0x1d, 0x0a,
	0x07, 0x45, 0x6e, 0x76, 0x4b, 0x65, 0x79, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6b, 0x65, 0x79, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x09, 0x52, 0x04, 0x6b, 0x65, 0x79, 0x73, 0x22, 0xcd, 0x04, 0x0a,
	0x17, 0x50, 0x75, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45, 0x6e,
	0x76, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x70, 0x65,
	0x63, 0x69, 0x66, 0x69, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x73,
	0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x1f, 0x0a,
	0x0a, 0x61, 0x6c, 0x6c, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x08, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x45,
	0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72,
	0x73, 0x48, 0x01, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x76, 0x43,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x49, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x63, 0x74,
	0x6f, 0x72, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x48, 0x01, 0x52, 0x12, 0x63, 0x6f,
	0x6d, 0x70, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x12, 0x4b, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x65,
	0x6e, 0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45,
	0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x48, 0x01, 0x52, 0x13, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61,
	0x6c, 0x6f, 0x6e, 0x65, 0x45, 0x6e, 0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3f, 0x0a,
	0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x73, 0x48, 0x01, 0x52,
	0x0d, 0x6d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x47,
	0x0a, 0x13, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63,
	0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x56, 0x61,
	0x72, 0x73, 0x48, 0x01, 0x52, 0x11, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e,
	0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42,
	0x0b, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x1a, 0x0a, 0x18,
	0x50, 0x75, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45, 0x6e, 0x76,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd0, 0x04, 0x0a, 0x1a, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45, 0x6e, 0x76,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x12, 0x47, 0x0a, 0x0f, 0x73, 0x70, 0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1c, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x70, 0x65, 0x63,
	0x69, 0x66, 0x69, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x48, 0x00, 0x52, 0x0e, 0x73, 0x70,
	0x65, 0x63, 0x69, 0x66, 0x69, 0x63, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x1f, 0x0a, 0x0a,
	0x61, 0x6c, 0x6c, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x08,
	0x48, 0x00, 0x52, 0x09, 0x61, 0x6c, 0x6c, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x45, 0x0a,
	0x12, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68, 0x61,
	0x6e, 0x67, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x4b, 0x65, 0x79, 0x73,
	0x48, 0x01, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x45, 0x6e, 0x76, 0x43, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x12, 0x49, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x63, 0x74, 0x6f,
	0x72, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x45, 0x6e, 0x76, 0x4b, 0x65, 0x79, 0x73, 0x48, 0x01, 0x52, 0x12, 0x63, 0x6f, 0x6d,
	0x70, 0x61, 0x63, 0x74, 0x6f, 0x72, 0x45, 0x6e, 0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12,
	0x4b, 0x0a, 0x15, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x65, 0x6e,
	0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e,
	0x76, 0x4b, 0x65, 0x79, 0x73, 0x48, 0x01, 0x52, 0x13, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c,
	0x6f, 0x6e, 0x65, 0x45, 0x6e, 0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x3f, 0x0a, 0x0f,
	0x6d, 0x65, 0x74, 0x61, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x4b, 0x65, 0x79, 0x73, 0x48, 0x01, 0x52, 0x0d,
	0x6d, 0x65, 0x74, 0x61, 0x45, 0x6e, 0x76, 0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x12, 0x47, 0x0a,
	0x13, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x65, 0x6e, 0x76, 0x5f, 0x63, 0x68,
	0x61, 0x6e, 0x67, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x45, 0x6e, 0x76, 0x4b, 0x65, 0x79,
	0x73, 0x48, 0x01, 0x52, 0x11, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x45, 0x6e, 0x76,
	0x43, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x42, 0x16, 0x0a, 0x14, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x73, 0x65, 0x6c, 0x65, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x42, 0x0b,
	0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x22, 0x1d, 0x0a, 0x1b, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45,
	0x6e, 0x76, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x54, 0x0a, 0x18, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f,
	0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0xd6, 0x01, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12,
	0x42, 0x0a, 0x0f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x0e, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x53,
	0x70, 0x65, 0x63, 0x12, 0x48, 0x0a, 0x11, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76,
	0x65, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x10, 0x72, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a,
	0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x77, 0x0a, 0x1c, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x49, 0x6d,
	0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x61, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x6d, 0x61, 0x67, 0x65,
	0x54, 0x61, 0x67, 0x22, 0x1f, 0x0a, 0x1d, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x80, 0x01, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65,
	0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x24, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4c, 0x69, 0x63, 0x65, 0x6e,
	0x73, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa0, 0x01,
	0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x6b, 0x65, 0x79, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x4b, 0x65, 0x79,
	0x22, 0x25, 0x0a, 0x23, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xfa, 0x03, 0x0a, 0x16, 0x53, 0x63, 0x61, 0x6c,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x3c,
	0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0d, 0x6d,
	0x65, 0x74, 0x61, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x44, 0x0a, 0x13,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52,
	0x11, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x12, 0x42, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x63,
	0x61, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x63, 0x61,
	0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x46, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77,
	0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x12, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x4a,
	0x0a, 0x14, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x63, 0x61, 0x6c,
	0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x42, 0x02, 0x18, 0x01, 0x52, 0x12, 0x63, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x6f,
	0x72, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x48, 0x0a, 0x15, 0x73, 0x74,
	0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52,
	0x13, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x53, 0x63, 0x61, 0x6c, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xe7, 0x01, 0x0a, 0x1b, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x12,
	0x3f, 0x0a, 0x0f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x48, 0x00,
	0x52, 0x0e, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x53, 0x70, 0x65, 0x63,
	0x12, 0x43, 0x0a, 0x0c, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x63, 0x61,
	0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x48, 0x00, 0x52, 0x0b, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x42, 0x06, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0xa2, 0x02, 0x0a, 0x10, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x3c,
	0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0d, 0x6d,
	0x65, 0x74, 0x61, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x44, 0x0a, 0x13,
	0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52,
	0x11, 0x66, 0x72, 0x6f, 0x6e, 0x74, 0x65, 0x6e, 0x64, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x12, 0x42, 0x0a, 0x12, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x5f, 0x73, 0x63,
	0x61, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65,
	0x53, 0x70, 0x65, 0x63, 0x52, 0x10, 0x63, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x53, 0x63, 0x61,
	0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x46, 0x0a, 0x14, 0x63, 0x6f, 0x6d, 0x70, 0x61, 0x63,
	0x74, 0x6f, 0x72, 0x5f, 0x73, 0x63, 0x61, 0x6c, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77,
	0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x12, 0x63, 0x6f, 0x6d, 0x70,
	0x61, 0x63, 0x74, 0x6f, 0x72, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x53, 0x70, 0x65, 0x63, 0x22, 0x74,
	0x0a, 0x19, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6c,
	0x69, 0x63, 0x61, 0x4f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x72,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0d, 0x52, 0x08, 0x72,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x12, 0x1c, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x22, 0x9b, 0x01, 0x0a, 0x16, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x45, 0x0a, 0x09, 0x6f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x4f,
	0x76, 0x65, 0x72, 0x72, 0x69, 0x64, 0x65, 0x52, 0x09, 0x6f, 0x76, 0x65, 0x72, 0x72, 0x69, 0x64,
	0x65, 0x73, 0x22, 0x19, 0x0a, 0x17, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x53, 0x0a,
	0x15, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x22, 0x18, 0x0a, 0x16, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xf9, 0x01, 0x0a,
	0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x42,
	0x0a, 0x0f, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x53, 0x70,
	0x65, 0x63, 0x12, 0x39, 0x0a, 0x16, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61,
	0x6e, 0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x08, 0x48, 0x00, 0x52, 0x14, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x53, 0x74, 0x61, 0x6e,
	0x64, 0x61, 0x6c, 0x6f, 0x6e, 0x65, 0x4d, 0x6f, 0x64, 0x65, 0x88, 0x01, 0x01, 0x42, 0x19, 0x0a,
	0x17, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x5f, 0x73, 0x74, 0x61, 0x6e, 0x64, 0x61, 0x6c,
	0x6f, 0x6e, 0x65, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0x24, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70,
	0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa0,
	0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x40, 0x0a, 0x0f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x6f, 0x72, 0x65, 0x5f, 0x73, 0x70,
	0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x70,
	0x65, 0x63, 0x52, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x53, 0x70, 0x65,
	0x63, 0x22, 0x23, 0x0a, 0x21, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x27, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x37,
	0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x70, 0x65, 0x63, 0x52, 0x09, 0x6e, 0x6f,
	0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x64, 0x0a, 0x28, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75,
	0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x9e, 0x01,
	0x0a, 0x27, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x37, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x62,
	0x0a, 0x28, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x8d, 0x01, 0x0a, 0x27, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x6f,
	0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x64, 0x0a, 0x28, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcf, 0x01, 0x0a, 0x20, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x37, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x70, 0x65, 0x63, 0x52,
	0x09, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x5d, 0x0a, 0x21, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcf, 0x01, 0x0a, 0x20, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x12, 0x37, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x72, 0x77, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x70, 0x65, 0x63,
	0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0x5b, 0x0a, 0x21, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xbe, 0x01, 0x0a, 0x20, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x6f, 0x64, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x21, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xfa, 0x01, 0x0a, 0x2d, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e,
	0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x12, 0x1d,
	0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x36, 0x0a,
	0x0b, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x4e,
	0x6f, 0x64, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x68, 0x0a, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52,
	0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0xf8, 0x01, 0x0a, 0x29, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x36, 0x0a, 0x09, 0x63, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x18, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x77, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x12, 0x38, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x09, 0x72, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x22, 0x64, 0x0a, 0x2a, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f,
	0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x61, 0x0a, 0x23, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d,
	0x65, 0x74, 0x61, 0x22, 0x60, 0x0a, 0x24, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x72,
	0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x5e, 0x0a, 0x20, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x21, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x9b, 0x01, 0x0a, 0x22,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x39,
	0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x22, 0x5f, 0x0a, 0x23, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c,
	0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xcf, 0x01, 0x0a, 0x19, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1b,
	0x0a, 0x09, 0x63, 0x68, 0x61, 0x72, 0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x5f, 0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x4a, 0x73, 0x6f, 0x6e, 0x22, 0x56, 0x0a, 0x1a,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0xe9, 0x01, 0x0a, 0x19, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65,
	0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x38,
	0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b, 0x72, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x72,
	0x74, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61,
	0x72, 0x74, 0x55, 0x72, 0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x5f,
	0x6a, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x73, 0x4a, 0x73, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c,
	0x22, 0x56, 0x0a, 0x1a, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x48, 0x65, 0x6c, 0x6d, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x93, 0x01, 0x0a, 0x1b, 0x55, 0x6e, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0b, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x58,
	0x0a, 0x1c, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38,
	0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20,
	0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x51, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x48,
	0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x38, 0x0a, 0x0c, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0b,
	0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x54, 0x0a, 0x16, 0x47,
	0x65, 0x74, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x0c, 0x68, 0x65, 0x6c, 0x6d, 0x5f, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c,
	0x65, 0x61, 0x73, 0x65, 0x52, 0x0b, 0x68, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x22, 0x51, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0xc1, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x50,
	0x68, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x54, 0x0a,
	0x0c, 0x70, 0x6f, 0x64, 0x5f, 0x74, 0x6f, 0x5f, 0x70, 0x68, 0x61, 0x73, 0x65, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x50, 0x6f, 0x64, 0x54, 0x6f, 0x50, 0x68, 0x61,
	0x73, 0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0a, 0x70, 0x6f, 0x64, 0x54, 0x6f, 0x50, 0x68,
	0x61, 0x73, 0x65, 0x1a, 0x53, 0x0a, 0x0f, 0x50, 0x6f, 0x64, 0x54, 0x6f, 0x50, 0x68, 0x61, 0x73,
	0x65, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x2a, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x57, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d,
	0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74,
	0x61, 0x22, 0x1c, 0x0a, 0x1a, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x56, 0x0a, 0x18, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79,
	0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x1b, 0x0a, 0x19, 0x52, 0x65, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x61, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65,
	0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x24, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x22, 0x60, 0x0a, 0x22, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e,
	0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e,
	0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65,
	0x74, 0x61, 0x22, 0x56, 0x0a, 0x23, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d,
	0x65, 0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xda, 0x03, 0x0a, 0x1b, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x57, 0x0a, 0x14, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x72,
	0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x52, 0x12, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x53, 0x70, 0x65, 0x63, 0x12,
	0x4d, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x5c,
	0x0a, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x3a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x0a, 0x0b,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x58, 0x0a, 0x1c, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xcd, 0x03, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4d, 0x0a, 0x13,
	0x70, 0x6f, 0x64, 0x5f, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x73,
	0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x67, 0x6d, 0x70, 0x2e, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x65, 0x63, 0x52, 0x11, 0x70, 0x6f, 0x64, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x53, 0x70, 0x65, 0x63, 0x12, 0x4c, 0x0a, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x12, 0x5b, 0x0a, 0x0b, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x0b, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x1a, 0x3e, 0x0a, 0x10, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x57, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f,
	0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x91, 0x02, 0x0a, 0x14, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74,
	0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12,
	0x3a, 0x0a, 0x0c, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0b,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x53, 0x70, 0x65, 0x63, 0x12, 0x46, 0x0a, 0x06, 0x6c,
	0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62,
	0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x51,
	0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x94, 0x02, 0x0a, 0x1a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74,
	0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x04,
	0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x70, 0x65, 0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x12,
	0x4c, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x34, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x57, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0xa4, 0x02, 0x0a, 0x22, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x12, 0x31, 0x0a, 0x04, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1d, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x53, 0x70, 0x65,
	0x63, 0x52, 0x04, 0x73, 0x70, 0x65, 0x63, 0x12, 0x54, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c,
	0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x25, 0x0a, 0x23, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0xa1, 0x01, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72,
	0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4a, 0x0a, 0x0f, 0x70, 0x6f, 0x73, 0x74, 0x67,
	0x72, 0x65, 0x73, 0x71, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72,
	0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x53,
	0x70, 0x65, 0x63, 0x52, 0x0e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x53,
	0x70, 0x65, 0x63, 0x22, 0x54, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73,
	0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x55, 0x0a, 0x17, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x22, 0x54, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72,
	0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa1, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x12, 0x4a,
	0x0a, 0x0f, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x5f, 0x73, 0x70, 0x65,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x50, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0e, 0x70, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x22, 0x57, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3b, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x23, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x43, 0x6f, 0x64, 0x65, 0x52, 0x06, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x22, 0x52, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72,
	0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x8c, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x2f, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x17, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x12, 0x4a, 0x0a, 0x0f, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c,
	0x5f, 0x73, 0x70, 0x65, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x21, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e,
	0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x52, 0x0e,
	0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x53, 0x70, 0x65, 0x63, 0x12, 0x34,
	0x0a, 0x0a, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x5f, 0x72, 0x65, 0x66, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x09, 0x73, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x52, 0x65, 0x66, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74, 0x69,
	0x61, 0x6c, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1e, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x70, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x73, 0x71, 0x6c, 0x2e, 0x43, 0x72,
	0x65, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x52, 0x0b, 0x63, 0x72, 0x65, 0x64, 0x65,
	0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x22, 0x58, 0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65,
	0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61,
	0x22, 0x57, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x59, 0x0a, 0x1b, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x22, 0x58, 0x0a, 0x1c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x58,
	0x0a, 0x1a, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x3a, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x15, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x4d, 0x65, 0x74, 0x61, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x22, 0x57, 0x0a, 0x1b, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x20, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x69,
	0x6f, 0x6e, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x22, 0x2e, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x22, 0x36, 0x0a, 0x0f, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x12, 0x23, 0x0a, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x0f, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x50, 0x6f, 0x64, 0x52, 0x04, 0x70, 0x6f, 0x64, 0x73, 0x22, 0x4e, 0x0a, 0x17, 0x47, 0x65, 0x74,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x33, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x44, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0x9e, 0x01, 0x0a, 0x18, 0x47, 0x65,
	0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x29, 0x0a, 0x10, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e,
	0x74, 0x12, 0x41, 0x0a, 0x1d, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x63, 0x61, 0x5f,
	0x63, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65,
	0x36, 0x34, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x43, 0x61, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x42, 0x61,
	0x73, 0x65, 0x36, 0x34, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x32, 0xe5, 0x39, 0x0a, 0x12, 0x4b,
	0x38, 0x73, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65,
	0x72, 0x12, 0x60, 0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65,
	0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d,
	0x0a, 0x0e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a,
	0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f,
	0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x66, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x75, 0x0a, 0x16, 0x41, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x12, 0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x41, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2c,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60,
	0x0a, 0x0f, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x60, 0x0a, 0x0f, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4d, 0x61, 0x70, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d,
	0x61, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d,
	0x61, 0x70, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60, 0x0a, 0x0f, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x12, 0x24,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x4d, 0x61, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x4d, 0x61, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a,
	0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x21, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72,
	0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x4e, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12, 0x1e, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1f, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x57, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x12,
	0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x12, 0x25, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a,
	0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x72,
	0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x49, 0x6d, 0x61, 0x67, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x81, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4b, 0x65,
	0x79, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x4c, 0x69, 0x63, 0x65, 0x6e, 0x73, 0x65, 0x4b, 0x65, 0x79, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x1b, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65,
	0x74, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x12, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74, 0x6f, 0x72,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x53, 0x65, 0x63, 0x72, 0x65, 0x74, 0x53, 0x74,
	0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a,
	0x0f, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88,
	0x02, 0x01, 0x12, 0x6a, 0x0a, 0x14, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52,
	0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x4f, 0x6e, 0x65, 0x4f, 0x66, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x63, 0x61, 0x6c, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x60,
	0x0a, 0x0f, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x12, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x52, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x5d, 0x0a, 0x0e, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x12, 0x23, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x53, 0x74, 0x6f, 0x70, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x81, 0x01, 0x0a, 0x1a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x12, 0x2f,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f,
	0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65,
	0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65,
	0x4d, 0x65, 0x74, 0x61, 0x53, 0x74, 0x6f, 0x72, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x13, 0x50, 0x75, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x45, 0x6e, 0x76, 0x56, 0x61, 0x72, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50, 0x75, 0x74, 0x52, 0x69, 0x73,
	0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45, 0x6e, 0x76, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x50, 0x75, 0x74, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45, 0x6e,
	0x76, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6f, 0x0a, 0x16, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45,
	0x6e, 0x76, 0x56, 0x61, 0x72, 0x12, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x57, 0x61, 0x76, 0x65, 0x45, 0x6e, 0x76, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x45,
	0x6e, 0x76, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x96, 0x01, 0x0a,
	0x20, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69,
	0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x96, 0x01, 0x0a, 0x20, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x36, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76,
	0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x96,
	0x01, 0x0a, 0x20, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x12, 0x35, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57,
	0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x36, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x43, 0x6f, 0x6d, 0x70, 0x75, 0x74,
	0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0x03, 0x88, 0x02, 0x01, 0x12, 0x7e, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0xa5, 0x01, 0x0a, 0x26, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x3b, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61,
	0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x75, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3c, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x75, 0x72,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x99, 0x01, 0x0a, 0x22, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67,
	0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73,
	0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x12, 0x37, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e, 0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x57, 0x61, 0x76, 0x65, 0x4e,
	0x6f, 0x64, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x41,
	0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x1c,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74,
	0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x12, 0x31, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75,
	0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x32, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x7e, 0x0a, 0x19, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69,
	0x6d, 0x73, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56,
	0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x50, 0x65, 0x72, 0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65,
	0x43, 0x6c, 0x61, 0x69, 0x6d, 0x12, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72, 0x73, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61, 0x69, 0x6d,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x65, 0x72,
	0x73, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x74, 0x56, 0x6f, 0x6c, 0x75, 0x6d, 0x65, 0x43, 0x6c, 0x61,
	0x69, 0x6d, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5d, 0x0a, 0x0e,
	0x47, 0x65, 0x74, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x23,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x49,
	0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x12, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65,
	0x61, 0x73, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6c,
	0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64,
	0x65, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x27, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x67, 0x72,
	0x61, 0x64, 0x65, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x67, 0x72, 0x61, 0x64, 0x65, 0x48, 0x65, 0x6c, 0x6d,
	0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x6f, 0x0a, 0x14, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65,
	0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6c, 0x6c, 0x48, 0x65, 0x6c, 0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x55, 0x6e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6c, 0x6c, 0x48, 0x65, 0x6c,
	0x6d, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x57, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73,
	0x65, 0x73, 0x12, 0x21, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x50, 0x68, 0x61, 0x73, 0x65,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x69, 0x0a, 0x12, 0x52,
	0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65,
	0x74, 0x12, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c,
	0x53, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x73, 0x70,
	0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x87, 0x01, 0x0a, 0x1c, 0x47, 0x65, 0x74, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61,
	0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x65, 0x66,
	0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x74, 0x61,
	0x74, 0x65, 0x66, 0x75, 0x6c, 0x53, 0x65, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x84, 0x01, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65,
	0x6e, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x65, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x66, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x74, 0x61,
	0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x26, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x74,
	0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x52, 0x65, 0x73, 0x74, 0x61, 0x72, 0x74, 0x44, 0x65, 0x70, 0x6c, 0x6f,
	0x79, 0x6d, 0x65, 0x6e, 0x74, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12,
	0x6f, 0x0a, 0x14, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d,
	0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00,
	0x12, 0x6f, 0x0a, 0x14, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22,
	0x00, 0x12, 0x74, 0x0a, 0x19, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x7a, 0x75, 0x72, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x12, 0x29,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76,
	0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x74, 0x0a, 0x19, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x41, 0x7a, 0x75, 0x72, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x12, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63,
	0x65, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2a, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x4d, 0x6f, 0x6e, 0x69,
	0x74, 0x6f, 0x72, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a,
	0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e,
	0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e,
	0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x13, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38,
	0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74,
	0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x50, 0x6f, 0x64, 0x4d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72,
	0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x28, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x84, 0x01, 0x0a, 0x1b, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x12, 0x30, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73,
	0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x4f, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x6c, 0x0a, 0x13, 0x44, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x28, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x12, 0x25, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65,
	0x53, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a,
	0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71,
	0x6c, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71,
	0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x50, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x12, 0x63, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74,
	0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x50, 0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x5a, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x50, 0x6f,
	0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x12, 0x22, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x73, 0x74, 0x67,
	0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x23, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x73, 0x74, 0x67, 0x72, 0x65, 0x53, 0x71, 0x6c, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0x00, 0x12, 0x48, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x50, 0x6f, 0x64, 0x73, 0x12, 0x1c,
	0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65,
	0x74, 0x50, 0x6f, 0x64, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x1d, 0x2e, 0x73,
	0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x50,
	0x6f, 0x64, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x00, 0x12, 0x63, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x12, 0x25, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73,
	0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x26, 0x2e, 0x73, 0x65, 0x72, 0x76, 0x69,
	0x63, 0x65, 0x73, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x47, 0x65, 0x74, 0x43, 0x6c, 0x75, 0x73, 0x74,
	0x65, 0x72, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x00, 0x42, 0x39, 0x5a, 0x37, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d,
	0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f,
	0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e,
	0x2f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x73, 0x2f, 0x6b, 0x38, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_services_k8s_proto_rawDescOnce sync.Once
	file_services_k8s_proto_rawDescData []byte
)

func file_services_k8s_proto_rawDescGZIP() []byte {
	file_services_k8s_proto_rawDescOnce.Do(func() {
		file_services_k8s_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_services_k8s_proto_rawDesc), len(file_services_k8s_proto_rawDesc)))
	})
	return file_services_k8s_proto_rawDescData
}

var file_services_k8s_proto_msgTypes = make([]protoimpl.MessageInfo, 145)
var file_services_k8s_proto_goTypes = []any{
	(*CreateNamespaceRequest)(nil),                         // 0: services.k8s.CreateNamespaceRequest
	(*CreateNamespaceResponse)(nil),                        // 1: services.k8s.CreateNamespaceResponse
	(*LabelNamespaceRequest)(nil),                          // 2: services.k8s.LabelNamespaceRequest
	(*LabelNamespaceResponse)(nil),                         // 3: services.k8s.LabelNamespaceResponse
	(*GetNamespaceRequest)(nil),                            // 4: services.k8s.GetNamespaceRequest
	(*GetNamespaceResponse)(nil),                           // 5: services.k8s.GetNamespaceResponse
	(*DeleteNamespaceRequest)(nil),                         // 6: services.k8s.DeleteNamespaceRequest
	(*DeleteNamespaceResponse)(nil),                        // 7: services.k8s.DeleteNamespaceResponse
	(*CreateServiceAccountRequest)(nil),                    // 8: services.k8s.CreateServiceAccountRequest
	(*CreateServiceAccountResponse)(nil),                   // 9: services.k8s.CreateServiceAccountResponse
	(*GetServiceAccountRequest)(nil),                       // 10: services.k8s.GetServiceAccountRequest
	(*GetServiceAccountResponse)(nil),                      // 11: services.k8s.GetServiceAccountResponse
	(*DeleteServiceAccountRequest)(nil),                    // 12: services.k8s.DeleteServiceAccountRequest
	(*DeleteServiceAccountResponse)(nil),                   // 13: services.k8s.DeleteServiceAccountResponse
	(*AnnotateServiceAccountRequest)(nil),                  // 14: services.k8s.AnnotateServiceAccountRequest
	(*AnnotateServiceAccountResponse)(nil),                 // 15: services.k8s.AnnotateServiceAccountResponse
	(*CreateConfigMapRequest)(nil),                         // 16: services.k8s.CreateConfigMapRequest
	(*CreateConfigMapResponse)(nil),                        // 17: services.k8s.CreateConfigMapResponse
	(*GetConfigMapRequest)(nil),                            // 18: services.k8s.GetConfigMapRequest
	(*GetConfigMapResponse)(nil),                           // 19: services.k8s.GetConfigMapResponse
	(*UpdateConfigMapRequest)(nil),                         // 20: services.k8s.UpdateConfigMapRequest
	(*UpdateConfigMapResponse)(nil),                        // 21: services.k8s.UpdateConfigMapResponse
	(*DeleteConfigMapRequest)(nil),                         // 22: services.k8s.DeleteConfigMapRequest
	(*DeleteConfigMapResponse)(nil),                        // 23: services.k8s.DeleteConfigMapResponse
	(*CreateSecretRequest)(nil),                            // 24: services.k8s.CreateSecretRequest
	(*CreateSecretResponse)(nil),                           // 25: services.k8s.CreateSecretResponse
	(*GetSecretRequest)(nil),                               // 26: services.k8s.GetSecretRequest
	(*GetSecretResponse)(nil),                              // 27: services.k8s.GetSecretResponse
	(*DeleteSecretRequest)(nil),                            // 28: services.k8s.DeleteSecretRequest
	(*UpdateSecretRequest)(nil),                            // 29: services.k8s.UpdateSecretRequest
	(*UpdateSecretResponse)(nil),                           // 30: services.k8s.UpdateSecretResponse
	(*DeleteSecretResponse)(nil),                           // 31: services.k8s.DeleteSecretResponse
	(*CreateRisingWaveRequest)(nil),                        // 32: services.k8s.CreateRisingWaveRequest
	(*SpecificGroups)(nil),                                 // 33: services.k8s.SpecificGroups
	(*EnvVars)(nil),                                        // 34: services.k8s.EnvVars
	(*EnvKeys)(nil),                                        // 35: services.k8s.EnvKeys
	(*PutRisingWaveEnvRequest)(nil),                        // 36: services.k8s.PutRisingWaveEnvRequest
	(*PutRisingWaveEnvResponse)(nil),                       // 37: services.k8s.PutRisingWaveEnvResponse
	(*DeleteRisingWaveEnvRequest)(nil),                     // 38: services.k8s.DeleteRisingWaveEnvRequest
	(*DeleteRisingWaveEnvResponse)(nil),                    // 39: services.k8s.DeleteRisingWaveEnvResponse
	(*CreateRisingWaveResponse)(nil),                       // 40: services.k8s.CreateRisingWaveResponse
	(*GetRisingWaveRequest)(nil),                           // 41: services.k8s.GetRisingWaveRequest
	(*GetRisingWaveResponse)(nil),                          // 42: services.k8s.GetRisingWaveResponse
	(*DeleteRisingWaveRequest)(nil),                        // 43: services.k8s.DeleteRisingWaveRequest
	(*DeleteRisingWaveResponse)(nil),                       // 44: services.k8s.DeleteRisingWaveResponse
	(*UpdateRisingWaveImageRequest)(nil),                   // 45: services.k8s.UpdateRisingWaveImageRequest
	(*UpdateRisingWaveImageResponse)(nil),                  // 46: services.k8s.UpdateRisingWaveImageResponse
	(*UpdateRisingWaveLicenseKeyRequest)(nil),              // 47: services.k8s.UpdateRisingWaveLicenseKeyRequest
	(*UpdateRisingWaveLicenseKeyResponse)(nil),             // 48: services.k8s.UpdateRisingWaveLicenseKeyResponse
	(*UpdateRisingWaveSecretStoreRequest)(nil),             // 49: services.k8s.UpdateRisingWaveSecretStoreRequest
	(*UpdateRisingWaveSecretStoreResponse)(nil),            // 50: services.k8s.UpdateRisingWaveSecretStoreResponse
	(*ScaleRisingWaveRequest)(nil),                         // 51: services.k8s.ScaleRisingWaveRequest
	(*ScaleRisingWaveResponse)(nil),                        // 52: services.k8s.ScaleRisingWaveResponse
	(*ScaleRisingWaveRequestOneOf)(nil),                    // 53: services.k8s.ScaleRisingWaveRequestOneOf
	(*ClusterScaleSpec)(nil),                               // 54: services.k8s.ClusterScaleSpec
	(*RisingWaveReplicaOverride)(nil),                      // 55: services.k8s.RisingWaveReplicaOverride
	(*StartRisingWaveRequest)(nil),                         // 56: services.k8s.StartRisingWaveRequest
	(*StartRisingWaveResponse)(nil),                        // 57: services.k8s.StartRisingWaveResponse
	(*StopRisingWaveRequest)(nil),                          // 58: services.k8s.StopRisingWaveRequest
	(*StopRisingWaveResponse)(nil),                         // 59: services.k8s.StopRisingWaveResponse
	(*UpdateRisingWaveComponentsRequest)(nil),              // 60: services.k8s.UpdateRisingWaveComponentsRequest
	(*UpdateRisingWaveComponentsResponse)(nil),             // 61: services.k8s.UpdateRisingWaveComponentsResponse
	(*UpdateRisingWaveMetaStoreRequest)(nil),               // 62: services.k8s.UpdateRisingWaveMetaStoreRequest
	(*UpdateRisingWaveMetaStoreResponse)(nil),              // 63: services.k8s.UpdateRisingWaveMetaStoreResponse
	(*CreateRisingWaveComputeNodeGroupRequest)(nil),        // 64: services.k8s.CreateRisingWaveComputeNodeGroupRequest
	(*CreateRisingWaveComputeNodeGroupResponse)(nil),       // 65: services.k8s.CreateRisingWaveComputeNodeGroupResponse
	(*UpdateRisingWaveComputeNodeGroupRequest)(nil),        // 66: services.k8s.UpdateRisingWaveComputeNodeGroupRequest
	(*UpdateRisingWaveComputeNodeGroupResponse)(nil),       // 67: services.k8s.UpdateRisingWaveComputeNodeGroupResponse
	(*DeleteRisingWaveComputeNodeGroupRequest)(nil),        // 68: services.k8s.DeleteRisingWaveComputeNodeGroupRequest
	(*DeleteRisingWaveComputeNodeGroupResponse)(nil),       // 69: services.k8s.DeleteRisingWaveComputeNodeGroupResponse
	(*CreateRisingWaveNodeGroupRequest)(nil),               // 70: services.k8s.CreateRisingWaveNodeGroupRequest
	(*CreateRisingWaveNodeGroupResponse)(nil),              // 71: services.k8s.CreateRisingWaveNodeGroupResponse
	(*UpdateRisingWaveNodeGroupRequest)(nil),               // 72: services.k8s.UpdateRisingWaveNodeGroupRequest
	(*UpdateRisingWaveNodeGroupResponse)(nil),              // 73: services.k8s.UpdateRisingWaveNodeGroupResponse
	(*DeleteRisingWaveNodeGroupRequest)(nil),               // 74: services.k8s.DeleteRisingWaveNodeGroupRequest
	(*DeleteRisingWaveNodeGroupResponse)(nil),              // 75: services.k8s.DeleteRisingWaveNodeGroupResponse
	(*UpdateRisingWaveNodeGroupConfigurationRequest)(nil),  // 76: services.k8s.UpdateRisingWaveNodeGroupConfigurationRequest
	(*UpdateRisingWaveNodeGroupConfigurationResponse)(nil), // 77: services.k8s.UpdateRisingWaveNodeGroupConfigurationResponse
	(*UpdateRisingWaveNodeGroupRestartAtRequest)(nil),      // 78: services.k8s.UpdateRisingWaveNodeGroupRestartAtRequest
	(*UpdateRisingWaveNodeGroupRestartAtResponse)(nil),     // 79: services.k8s.UpdateRisingWaveNodeGroupRestartAtResponse
	(*DeletePersistentVolumeClaimsRequest)(nil),            // 80: services.k8s.DeletePersistentVolumeClaimsRequest
	(*DeletePersistentVolumeClaimsResponse)(nil),           // 81: services.k8s.DeletePersistentVolumeClaimsResponse
	(*GetPersistentVolumeClaimsRequest)(nil),               // 82: services.k8s.GetPersistentVolumeClaimsRequest
	(*GetPersistentVolumeClaimsResponse)(nil),              // 83: services.k8s.GetPersistentVolumeClaimsResponse
	(*CreatePersistentVolumeClaimRequest)(nil),             // 84: services.k8s.CreatePersistentVolumeClaimRequest
	(*CreatePersistentVolumeClaimResponse)(nil),            // 85: services.k8s.CreatePersistentVolumeClaimResponse
	(*InstallHelmReleaseRequest)(nil),                      // 86: services.k8s.InstallHelmReleaseRequest
	(*InstallHelmReleaseResponse)(nil),                     // 87: services.k8s.InstallHelmReleaseResponse
	(*UpgradeHelmReleaseRequest)(nil),                      // 88: services.k8s.UpgradeHelmReleaseRequest
	(*UpgradeHelmReleaseResponse)(nil),                     // 89: services.k8s.UpgradeHelmReleaseResponse
	(*UninstallHelmReleaseRequest)(nil),                    // 90: services.k8s.UninstallHelmReleaseRequest
	(*UninstallHelmReleaseResponse)(nil),                   // 91: services.k8s.UninstallHelmReleaseResponse
	(*GetHelmReleaseRequest)(nil),                          // 92: services.k8s.GetHelmReleaseRequest
	(*GetHelmReleaseResponse)(nil),                         // 93: services.k8s.GetHelmReleaseResponse
	(*GetPodPhasesRequest)(nil),                            // 94: services.k8s.GetPodPhasesRequest
	(*GetPodPhasesResponse)(nil),                           // 95: services.k8s.GetPodPhasesResponse
	(*RestartStatefulSetRequest)(nil),                      // 96: services.k8s.RestartStatefulSetRequest
	(*RestartStatefulSetResponse)(nil),                     // 97: services.k8s.RestartStatefulSetResponse
	(*RestartDeploymentRequest)(nil),                       // 98: services.k8s.RestartDeploymentRequest
	(*RestartDeploymentResponse)(nil),                      // 99: services.k8s.RestartDeploymentResponse
	(*GetStatefulSetReplicasStatusRequest)(nil),            // 100: services.k8s.GetStatefulSetReplicasStatusRequest
	(*GetStatefulSetReplicasStatusResponse)(nil),           // 101: services.k8s.GetStatefulSetReplicasStatusResponse
	(*GetDeploymentReplicasStatusRequest)(nil),             // 102: services.k8s.GetDeploymentReplicasStatusRequest
	(*GetDeploymentReplicasStatusResponse)(nil),            // 103: services.k8s.GetDeploymentReplicasStatusResponse
	(*CreateServiceMonitorRequest)(nil),                    // 104: services.k8s.CreateServiceMonitorRequest
	(*CreateServiceMonitorResponse)(nil),                   // 105: services.k8s.CreateServiceMonitorResponse
	(*CreatePodMonitoringRequest)(nil),                     // 106: services.k8s.CreatePodMonitoringRequest
	(*CreatePodMonitoringResponse)(nil),                    // 107: services.k8s.CreatePodMonitoringResponse
	(*CreateServiceRequest)(nil),                           // 108: services.k8s.CreateServiceRequest
	(*CreateServiceResponse)(nil),                          // 109: services.k8s.CreateServiceResponse
	(*CreateNetworkPolicyRequest)(nil),                     // 110: services.k8s.CreateNetworkPolicyRequest
	(*CreateNetworkPolicyResponse)(nil),                    // 111: services.k8s.CreateNetworkPolicyResponse
	(*CreateOrUpdateNetworkPolicyRequest)(nil),             // 112: services.k8s.CreateOrUpdateNetworkPolicyRequest
	(*CreateOrUpdateNetworkPolicyResponse)(nil),            // 113: services.k8s.CreateOrUpdateNetworkPolicyResponse
	(*CreatePostgreSqlRequest)(nil),                        // 114: services.k8s.CreatePostgreSqlRequest
	(*CreatePostgreSqlResponse)(nil),                       // 115: services.k8s.CreatePostgreSqlResponse
	(*DeletePostgreSqlRequest)(nil),                        // 116: services.k8s.DeletePostgreSqlRequest
	(*DeletePostgreSqlResponse)(nil),                       // 117: services.k8s.DeletePostgreSqlResponse
	(*UpdatePostgreSqlRequest)(nil),                        // 118: services.k8s.UpdatePostgreSqlRequest
	(*UpdatePostgreSqlResponse)(nil),                       // 119: services.k8s.UpdatePostgreSqlResponse
	(*GetPostgreSqlRequest)(nil),                           // 120: services.k8s.GetPostgreSqlRequest
	(*GetPostgreSqlResponse)(nil),                          // 121: services.k8s.GetPostgreSqlResponse
	(*DeleteNetworkPolicyRequest)(nil),                     // 122: services.k8s.DeleteNetworkPolicyRequest
	(*DeleteNetworkPolicyResponse)(nil),                    // 123: services.k8s.DeleteNetworkPolicyResponse
	(*DeleteServiceMonitorRequest)(nil),                    // 124: services.k8s.DeleteServiceMonitorRequest
	(*DeleteServiceMonitorResponse)(nil),                   // 125: services.k8s.DeleteServiceMonitorResponse
	(*DeletePodMonitoringRequest)(nil),                     // 126: services.k8s.DeletePodMonitoringRequest
	(*DeletePodMonitoringResponse)(nil),                    // 127: services.k8s.DeletePodMonitoringResponse
	(*GetPodsRequest)(nil),                                 // 128: services.k8s.GetPodsRequest
	(*GetPodsResponse)(nil),                                // 129: services.k8s.GetPodsResponse
	(*GetClusterAccessRequest)(nil),                        // 130: services.k8s.GetClusterAccessRequest
	(*GetClusterAccessResponse)(nil),                       // 131: services.k8s.GetClusterAccessResponse
	nil,                                                    // 132: services.k8s.CreateNamespaceRequest.LabelsEntry
	nil,                                                    // 133: services.k8s.LabelNamespaceRequest.LabelsEntry
	nil,                                                    // 134: services.k8s.AnnotateServiceAccountRequest.LabelsEntry
	nil,                                                    // 135: services.k8s.CreateRisingWaveRequest.LabelsEntry
	nil,                                                    // 136: services.k8s.CreateRisingWaveRequest.AnnotationsEntry
	nil,                                                    // 137: services.k8s.GetPodPhasesResponse.PodToPhaseEntry
	nil,                                                    // 138: services.k8s.CreateServiceMonitorRequest.LabelsEntry
	nil,                                                    // 139: services.k8s.CreateServiceMonitorRequest.AnnotationsEntry
	nil,                                                    // 140: services.k8s.CreatePodMonitoringRequest.LabelsEntry
	nil,                                                    // 141: services.k8s.CreatePodMonitoringRequest.AnnotationsEntry
	nil,                                                    // 142: services.k8s.CreateServiceRequest.LabelsEntry
	nil,                                                    // 143: services.k8s.CreateNetworkPolicyRequest.LabelsEntry
	nil,                                                    // 144: services.k8s.CreateOrUpdateNetworkPolicyRequest.LabelsEntry
	(*resource.Meta)(nil),                                  // 145: common.resource.Meta
	(*creation.Status)(nil),                                // 146: common.resource.creation.Status
	(*resource.Status)(nil),                                // 147: common.resource.Status
	(*deletion.Status)(nil),                                // 148: common.resource.deletion.Status
	(*k8s.ConfigMap)(nil),                                  // 149: common.k8s.ConfigMap
	(*k8s.Secret)(nil),                                     // 150: common.k8s.Secret
	(*rw.RisingWaveSpec)(nil),                              // 151: common.rw.RisingWaveSpec
	(*k8s.EnvVar)(nil),                                     // 152: common.k8s.EnvVar
	(*rw.RisingWaveStatus)(nil),                            // 153: common.rw.RisingWaveStatus
	(*rw.ScaleSpec)(nil),                                   // 154: common.rw.ScaleSpec
	(*rw.ComponentsSpec)(nil),                              // 155: common.rw.ComponentsSpec
	(*rw.MetaStoreSpec)(nil),                               // 156: common.rw.MetaStoreSpec
	(*rw.NodeGroupSpec)(nil),                               // 157: common.rw.NodeGroupSpec
	(*update.Status)(nil),                                  // 158: common.resource.update.Status
	(rw.ComponentType)(0),                                  // 159: common.rw.ComponentType
	(*rw.NodeConfig)(nil),                                  // 160: common.rw.NodeConfig
	(*timestamppb.Timestamp)(nil),                          // 161: google.protobuf.Timestamp
	(*k8s.PersistentVolumeClaimSpec)(nil),                  // 162: common.k8s.PersistentVolumeClaimSpec
	(*k8s.HelmRelease)(nil),                                // 163: common.k8s.HelmRelease
	(*prometheus.ServiceMonitorSpec)(nil),                  // 164: common.prometheus.ServiceMonitorSpec
	(*gmp.PodMonitoringSpec)(nil),                          // 165: common.gmp.PodMonitoringSpec
	(*k8s.ServiceSpec)(nil),                                // 166: common.k8s.ServiceSpec
	(*k8s.NetworkPolicySpec)(nil),                          // 167: common.k8s.NetworkPolicySpec
	(*postgresql.PostgreSqlSpec)(nil),                      // 168: common.postgresql.PostgreSqlSpec
	(postgresql.UpdateStatusCode)(0),                       // 169: common.postgresql.UpdateStatusCode
	(*postgresql.Credentials)(nil),                         // 170: common.postgresql.Credentials
	(*k8s.Pod)(nil),                                        // 171: common.k8s.Pod
	(*durationpb.Duration)(nil),                            // 172: google.protobuf.Duration
	(k8s.PodPhase)(0),                                      // 173: common.k8s.PodPhase
}
var file_services_k8s_proto_depIdxs = []int32{
	145, // 0: services.k8s.CreateNamespaceRequest.resource_meta:type_name -> common.resource.Meta
	132, // 1: services.k8s.CreateNamespaceRequest.labels:type_name -> services.k8s.CreateNamespaceRequest.LabelsEntry
	146, // 2: services.k8s.CreateNamespaceResponse.status:type_name -> common.resource.creation.Status
	145, // 3: services.k8s.LabelNamespaceRequest.resource_meta:type_name -> common.resource.Meta
	133, // 4: services.k8s.LabelNamespaceRequest.labels:type_name -> services.k8s.LabelNamespaceRequest.LabelsEntry
	145, // 5: services.k8s.GetNamespaceRequest.resource_meta:type_name -> common.resource.Meta
	147, // 6: services.k8s.GetNamespaceResponse.status:type_name -> common.resource.Status
	145, // 7: services.k8s.DeleteNamespaceRequest.resource_meta:type_name -> common.resource.Meta
	148, // 8: services.k8s.DeleteNamespaceResponse.status:type_name -> common.resource.deletion.Status
	145, // 9: services.k8s.CreateServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	146, // 10: services.k8s.CreateServiceAccountResponse.status:type_name -> common.resource.creation.Status
	145, // 11: services.k8s.GetServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	147, // 12: services.k8s.GetServiceAccountResponse.status:type_name -> common.resource.Status
	145, // 13: services.k8s.DeleteServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	148, // 14: services.k8s.DeleteServiceAccountResponse.status:type_name -> common.resource.deletion.Status
	145, // 15: services.k8s.AnnotateServiceAccountRequest.resource_meta:type_name -> common.resource.Meta
	134, // 16: services.k8s.AnnotateServiceAccountRequest.labels:type_name -> services.k8s.AnnotateServiceAccountRequest.LabelsEntry
	145, // 17: services.k8s.CreateConfigMapRequest.resource_meta:type_name -> common.resource.Meta
	149, // 18: services.k8s.CreateConfigMapRequest.config_map_spec:type_name -> common.k8s.ConfigMap
	146, // 19: services.k8s.CreateConfigMapResponse.status:type_name -> common.resource.creation.Status
	145, // 20: services.k8s.GetConfigMapRequest.resource_meta:type_name -> common.resource.Meta
	147, // 21: services.k8s.GetConfigMapResponse.status:type_name -> common.resource.Status
	149, // 22: services.k8s.GetConfigMapResponse.config_map_spec:type_name -> common.k8s.ConfigMap
	145, // 23: services.k8s.UpdateConfigMapRequest.resource_meta:type_name -> common.resource.Meta
	149, // 24: services.k8s.UpdateConfigMapRequest.from_config_map_spec:type_name -> common.k8s.ConfigMap
	149, // 25: services.k8s.UpdateConfigMapRequest.to_config_map_spec:type_name -> common.k8s.ConfigMap
	145, // 26: services.k8s.DeleteConfigMapRequest.resource_meta:type_name -> common.resource.Meta
	148, // 27: services.k8s.DeleteConfigMapResponse.status:type_name -> common.resource.deletion.Status
	145, // 28: services.k8s.CreateSecretRequest.resource_meta:type_name -> common.resource.Meta
	150, // 29: services.k8s.CreateSecretRequest.secret_spec:type_name -> common.k8s.Secret
	146, // 30: services.k8s.CreateSecretResponse.status:type_name -> common.resource.creation.Status
	145, // 31: services.k8s.GetSecretRequest.resource_meta:type_name -> common.resource.Meta
	147, // 32: services.k8s.GetSecretResponse.status:type_name -> common.resource.Status
	150, // 33: services.k8s.GetSecretResponse.secret_spec:type_name -> common.k8s.Secret
	145, // 34: services.k8s.DeleteSecretRequest.resource_meta:type_name -> common.resource.Meta
	145, // 35: services.k8s.UpdateSecretRequest.resource_meta:type_name -> common.resource.Meta
	150, // 36: services.k8s.UpdateSecretRequest.secret_spec:type_name -> common.k8s.Secret
	148, // 37: services.k8s.DeleteSecretResponse.status:type_name -> common.resource.deletion.Status
	145, // 38: services.k8s.CreateRisingWaveRequest.resource_meta:type_name -> common.resource.Meta
	151, // 39: services.k8s.CreateRisingWaveRequest.risingwave_spec:type_name -> common.rw.RisingWaveSpec
	135, // 40: services.k8s.CreateRisingWaveRequest.labels:type_name -> services.k8s.CreateRisingWaveRequest.LabelsEntry
	136, // 41: services.k8s.CreateRisingWaveRequest.annotations:type_name -> services.k8s.CreateRisingWaveRequest.AnnotationsEntry
	152, // 42: services.k8s.EnvVars.vars:type_name -> common.k8s.EnvVar
	145, // 43: services.k8s.PutRisingWaveEnvRequest.resource_meta:type_name -> common.resource.Meta
	33,  // 44: services.k8s.PutRisingWaveEnvRequest.specific_groups:type_name -> services.k8s.SpecificGroups
	34,  // 45: services.k8s.PutRisingWaveEnvRequest.compute_env_change:type_name -> services.k8s.EnvVars
	34,  // 46: services.k8s.PutRisingWaveEnvRequest.compactor_env_change:type_name -> services.k8s.EnvVars
	34,  // 47: services.k8s.PutRisingWaveEnvRequest.standalone_env_change:type_name -> services.k8s.EnvVars
	34,  // 48: services.k8s.PutRisingWaveEnvRequest.meta_env_change:type_name -> services.k8s.EnvVars
	34,  // 49: services.k8s.PutRisingWaveEnvRequest.frontend_env_change:type_name -> services.k8s.EnvVars
	145, // 50: services.k8s.DeleteRisingWaveEnvRequest.resource_meta:type_name -> common.resource.Meta
	33,  // 51: services.k8s.DeleteRisingWaveEnvRequest.specific_groups:type_name -> services.k8s.SpecificGroups
	35,  // 52: services.k8s.DeleteRisingWaveEnvRequest.compute_env_change:type_name -> services.k8s.EnvKeys
	35,  // 53: services.k8s.DeleteRisingWaveEnvRequest.compactor_env_change:type_name -> services.k8s.EnvKeys
	35,  // 54: services.k8s.DeleteRisingWaveEnvRequest.standalone_env_change:type_name -> services.k8s.EnvKeys
	35,  // 55: services.k8s.DeleteRisingWaveEnvRequest.meta_env_change:type_name -> services.k8s.EnvKeys
	35,  // 56: services.k8s.DeleteRisingWaveEnvRequest.frontend_env_change:type_name -> services.k8s.EnvKeys
	146, // 57: services.k8s.CreateRisingWaveResponse.status:type_name -> common.resource.creation.Status
	145, // 58: services.k8s.GetRisingWaveRequest.resource_meta:type_name -> common.resource.Meta
	147, // 59: services.k8s.GetRisingWaveResponse.status:type_name -> common.resource.Status
	151, // 60: services.k8s.GetRisingWaveResponse.risingwave_spec:type_name -> common.rw.RisingWaveSpec
	153, // 61: services.k8s.GetRisingWaveResponse.risingwave_status:type_name -> common.rw.RisingWaveStatus
	145, // 62: services.k8s.DeleteRisingWaveRequest.resource_meta:type_name -> common.resource.Meta
	148, // 63: services.k8s.DeleteRisingWaveResponse.status:type_name -> common.resource.deletion.Status
	145, // 64: services.k8s.UpdateRisingWaveImageRequest.resource_meta:type_name -> common.resource.Meta
	145, // 65: services.k8s.UpdateRisingWaveLicenseKeyRequest.resource_meta:type_name -> common.resource.Meta
	145, // 66: services.k8s.UpdateRisingWaveSecretStoreRequest.resource_meta:type_name -> common.resource.Meta
	145, // 67: services.k8s.ScaleRisingWaveRequest.resource_meta:type_name -> common.resource.Meta
	154, // 68: services.k8s.ScaleRisingWaveRequest.meta_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 69: services.k8s.ScaleRisingWaveRequest.frontend_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 70: services.k8s.ScaleRisingWaveRequest.compute_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 71: services.k8s.ScaleRisingWaveRequest.compactor_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 72: services.k8s.ScaleRisingWaveRequest.connector_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 73: services.k8s.ScaleRisingWaveRequest.standalone_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 74: services.k8s.ScaleRisingWaveRequestOneOf.standalone_spec:type_name -> common.rw.ScaleSpec
	54,  // 75: services.k8s.ScaleRisingWaveRequestOneOf.cluster_spec:type_name -> services.k8s.ClusterScaleSpec
	145, // 76: services.k8s.ScaleRisingWaveRequestOneOf.resource_meta:type_name -> common.resource.Meta
	154, // 77: services.k8s.ClusterScaleSpec.meta_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 78: services.k8s.ClusterScaleSpec.frontend_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 79: services.k8s.ClusterScaleSpec.compute_scale_spec:type_name -> common.rw.ScaleSpec
	154, // 80: services.k8s.ClusterScaleSpec.compactor_scale_spec:type_name -> common.rw.ScaleSpec
	145, // 81: services.k8s.StartRisingWaveRequest.resource_meta:type_name -> common.resource.Meta
	55,  // 82: services.k8s.StartRisingWaveRequest.overrides:type_name -> services.k8s.RisingWaveReplicaOverride
	145, // 83: services.k8s.StopRisingWaveRequest.resource_meta:type_name -> common.resource.Meta
	145, // 84: services.k8s.UpdateRisingWaveComponentsRequest.resource_meta:type_name -> common.resource.Meta
	155, // 85: services.k8s.UpdateRisingWaveComponentsRequest.components_spec:type_name -> common.rw.ComponentsSpec
	145, // 86: services.k8s.UpdateRisingWaveMetaStoreRequest.resource_meta:type_name -> common.resource.Meta
	156, // 87: services.k8s.UpdateRisingWaveMetaStoreRequest.meta_store_spec:type_name -> common.rw.MetaStoreSpec
	145, // 88: services.k8s.CreateRisingWaveComputeNodeGroupRequest.resource_meta:type_name -> common.resource.Meta
	157, // 89: services.k8s.CreateRisingWaveComputeNodeGroupRequest.node_group:type_name -> common.rw.NodeGroupSpec
	146, // 90: services.k8s.CreateRisingWaveComputeNodeGroupResponse.status:type_name -> common.resource.creation.Status
	145, // 91: services.k8s.UpdateRisingWaveComputeNodeGroupRequest.resource_meta:type_name -> common.resource.Meta
	157, // 92: services.k8s.UpdateRisingWaveComputeNodeGroupRequest.node_group:type_name -> common.rw.NodeGroupSpec
	158, // 93: services.k8s.UpdateRisingWaveComputeNodeGroupResponse.status:type_name -> common.resource.update.Status
	145, // 94: services.k8s.DeleteRisingWaveComputeNodeGroupRequest.resource_meta:type_name -> common.resource.Meta
	148, // 95: services.k8s.DeleteRisingWaveComputeNodeGroupResponse.status:type_name -> common.resource.deletion.Status
	145, // 96: services.k8s.CreateRisingWaveNodeGroupRequest.resource_meta:type_name -> common.resource.Meta
	159, // 97: services.k8s.CreateRisingWaveNodeGroupRequest.component:type_name -> common.rw.ComponentType
	157, // 98: services.k8s.CreateRisingWaveNodeGroupRequest.node_group:type_name -> common.rw.NodeGroupSpec
	146, // 99: services.k8s.CreateRisingWaveNodeGroupResponse.status:type_name -> common.resource.creation.Status
	145, // 100: services.k8s.UpdateRisingWaveNodeGroupRequest.resource_meta:type_name -> common.resource.Meta
	159, // 101: services.k8s.UpdateRisingWaveNodeGroupRequest.component:type_name -> common.rw.ComponentType
	157, // 102: services.k8s.UpdateRisingWaveNodeGroupRequest.node_group:type_name -> common.rw.NodeGroupSpec
	158, // 103: services.k8s.UpdateRisingWaveNodeGroupResponse.status:type_name -> common.resource.update.Status
	145, // 104: services.k8s.DeleteRisingWaveNodeGroupRequest.resource_meta:type_name -> common.resource.Meta
	159, // 105: services.k8s.DeleteRisingWaveNodeGroupRequest.component:type_name -> common.rw.ComponentType
	148, // 106: services.k8s.DeleteRisingWaveNodeGroupResponse.status:type_name -> common.resource.deletion.Status
	145, // 107: services.k8s.UpdateRisingWaveNodeGroupConfigurationRequest.resource_meta:type_name -> common.resource.Meta
	159, // 108: services.k8s.UpdateRisingWaveNodeGroupConfigurationRequest.component:type_name -> common.rw.ComponentType
	160, // 109: services.k8s.UpdateRisingWaveNodeGroupConfigurationRequest.node_config:type_name -> common.rw.NodeConfig
	158, // 110: services.k8s.UpdateRisingWaveNodeGroupConfigurationResponse.status:type_name -> common.resource.update.Status
	145, // 111: services.k8s.UpdateRisingWaveNodeGroupRestartAtRequest.resource_meta:type_name -> common.resource.Meta
	159, // 112: services.k8s.UpdateRisingWaveNodeGroupRestartAtRequest.component:type_name -> common.rw.ComponentType
	161, // 113: services.k8s.UpdateRisingWaveNodeGroupRestartAtRequest.restartAt:type_name -> google.protobuf.Timestamp
	158, // 114: services.k8s.UpdateRisingWaveNodeGroupRestartAtResponse.status:type_name -> common.resource.update.Status
	145, // 115: services.k8s.DeletePersistentVolumeClaimsRequest.resource_meta:type_name -> common.resource.Meta
	148, // 116: services.k8s.DeletePersistentVolumeClaimsResponse.status:type_name -> common.resource.deletion.Status
	145, // 117: services.k8s.GetPersistentVolumeClaimsRequest.resource_meta:type_name -> common.resource.Meta
	147, // 118: services.k8s.GetPersistentVolumeClaimsResponse.status:type_name -> common.resource.Status
	145, // 119: services.k8s.CreatePersistentVolumeClaimRequest.resource_meta:type_name -> common.resource.Meta
	162, // 120: services.k8s.CreatePersistentVolumeClaimRequest.spec:type_name -> common.k8s.PersistentVolumeClaimSpec
	146, // 121: services.k8s.CreatePersistentVolumeClaimResponse.status:type_name -> common.resource.creation.Status
	145, // 122: services.k8s.InstallHelmReleaseRequest.resource_meta:type_name -> common.resource.Meta
	145, // 123: services.k8s.InstallHelmReleaseRequest.release_meta:type_name -> common.resource.Meta
	146, // 124: services.k8s.InstallHelmReleaseResponse.status:type_name -> common.resource.creation.Status
	145, // 125: services.k8s.UpgradeHelmReleaseRequest.resource_meta:type_name -> common.resource.Meta
	145, // 126: services.k8s.UpgradeHelmReleaseRequest.release_meta:type_name -> common.resource.Meta
	146, // 127: services.k8s.UpgradeHelmReleaseResponse.status:type_name -> common.resource.creation.Status
	145, // 128: services.k8s.UninstallHelmReleaseRequest.resource_meta:type_name -> common.resource.Meta
	145, // 129: services.k8s.UninstallHelmReleaseRequest.release_meta:type_name -> common.resource.Meta
	146, // 130: services.k8s.UninstallHelmReleaseResponse.status:type_name -> common.resource.creation.Status
	145, // 131: services.k8s.GetHelmReleaseRequest.release_meta:type_name -> common.resource.Meta
	163, // 132: services.k8s.GetHelmReleaseResponse.helm_release:type_name -> common.k8s.HelmRelease
	145, // 133: services.k8s.GetPodPhasesRequest.resource_meta:type_name -> common.resource.Meta
	137, // 134: services.k8s.GetPodPhasesResponse.pod_to_phase:type_name -> services.k8s.GetPodPhasesResponse.PodToPhaseEntry
	145, // 135: services.k8s.RestartStatefulSetRequest.resource_meta:type_name -> common.resource.Meta
	145, // 136: services.k8s.RestartDeploymentRequest.resource_meta:type_name -> common.resource.Meta
	145, // 137: services.k8s.GetStatefulSetReplicasStatusRequest.resource_meta:type_name -> common.resource.Meta
	147, // 138: services.k8s.GetStatefulSetReplicasStatusResponse.status:type_name -> common.resource.Status
	145, // 139: services.k8s.GetDeploymentReplicasStatusRequest.resource_meta:type_name -> common.resource.Meta
	147, // 140: services.k8s.GetDeploymentReplicasStatusResponse.status:type_name -> common.resource.Status
	145, // 141: services.k8s.CreateServiceMonitorRequest.resource_meta:type_name -> common.resource.Meta
	164, // 142: services.k8s.CreateServiceMonitorRequest.service_monitor_spec:type_name -> common.prometheus.ServiceMonitorSpec
	138, // 143: services.k8s.CreateServiceMonitorRequest.labels:type_name -> services.k8s.CreateServiceMonitorRequest.LabelsEntry
	139, // 144: services.k8s.CreateServiceMonitorRequest.annotations:type_name -> services.k8s.CreateServiceMonitorRequest.AnnotationsEntry
	146, // 145: services.k8s.CreateServiceMonitorResponse.status:type_name -> common.resource.creation.Status
	145, // 146: services.k8s.CreatePodMonitoringRequest.resource_meta:type_name -> common.resource.Meta
	165, // 147: services.k8s.CreatePodMonitoringRequest.pod_monitoring_spec:type_name -> common.gmp.PodMonitoringSpec
	140, // 148: services.k8s.CreatePodMonitoringRequest.labels:type_name -> services.k8s.CreatePodMonitoringRequest.LabelsEntry
	141, // 149: services.k8s.CreatePodMonitoringRequest.annotations:type_name -> services.k8s.CreatePodMonitoringRequest.AnnotationsEntry
	146, // 150: services.k8s.CreatePodMonitoringResponse.status:type_name -> common.resource.creation.Status
	145, // 151: services.k8s.CreateServiceRequest.resource_meta:type_name -> common.resource.Meta
	166, // 152: services.k8s.CreateServiceRequest.service_spec:type_name -> common.k8s.ServiceSpec
	142, // 153: services.k8s.CreateServiceRequest.labels:type_name -> services.k8s.CreateServiceRequest.LabelsEntry
	146, // 154: services.k8s.CreateServiceResponse.status:type_name -> common.resource.creation.Status
	145, // 155: services.k8s.CreateNetworkPolicyRequest.resource_meta:type_name -> common.resource.Meta
	167, // 156: services.k8s.CreateNetworkPolicyRequest.spec:type_name -> common.k8s.NetworkPolicySpec
	143, // 157: services.k8s.CreateNetworkPolicyRequest.labels:type_name -> services.k8s.CreateNetworkPolicyRequest.LabelsEntry
	146, // 158: services.k8s.CreateNetworkPolicyResponse.status:type_name -> common.resource.creation.Status
	145, // 159: services.k8s.CreateOrUpdateNetworkPolicyRequest.resource_meta:type_name -> common.resource.Meta
	167, // 160: services.k8s.CreateOrUpdateNetworkPolicyRequest.spec:type_name -> common.k8s.NetworkPolicySpec
	144, // 161: services.k8s.CreateOrUpdateNetworkPolicyRequest.labels:type_name -> services.k8s.CreateOrUpdateNetworkPolicyRequest.LabelsEntry
	145, // 162: services.k8s.CreatePostgreSqlRequest.resource_meta:type_name -> common.resource.Meta
	168, // 163: services.k8s.CreatePostgreSqlRequest.postgresql_spec:type_name -> common.postgresql.PostgreSqlSpec
	146, // 164: services.k8s.CreatePostgreSqlResponse.status:type_name -> common.resource.creation.Status
	145, // 165: services.k8s.DeletePostgreSqlRequest.resource_meta:type_name -> common.resource.Meta
	148, // 166: services.k8s.DeletePostgreSqlResponse.status:type_name -> common.resource.deletion.Status
	145, // 167: services.k8s.UpdatePostgreSqlRequest.resource_meta:type_name -> common.resource.Meta
	168, // 168: services.k8s.UpdatePostgreSqlRequest.postgresql_spec:type_name -> common.postgresql.PostgreSqlSpec
	169, // 169: services.k8s.UpdatePostgreSqlResponse.status:type_name -> common.postgresql.UpdateStatusCode
	145, // 170: services.k8s.GetPostgreSqlRequest.resource_meta:type_name -> common.resource.Meta
	147, // 171: services.k8s.GetPostgreSqlResponse.status:type_name -> common.resource.Status
	168, // 172: services.k8s.GetPostgreSqlResponse.postgresql_spec:type_name -> common.postgresql.PostgreSqlSpec
	145, // 173: services.k8s.GetPostgreSqlResponse.secret_ref:type_name -> common.resource.Meta
	170, // 174: services.k8s.GetPostgreSqlResponse.credentials:type_name -> common.postgresql.Credentials
	145, // 175: services.k8s.DeleteNetworkPolicyRequest.resource_meta:type_name -> common.resource.Meta
	148, // 176: services.k8s.DeleteNetworkPolicyResponse.status:type_name -> common.resource.deletion.Status
	145, // 177: services.k8s.DeleteServiceMonitorRequest.resource_meta:type_name -> common.resource.Meta
	148, // 178: services.k8s.DeleteServiceMonitorResponse.status:type_name -> common.resource.deletion.Status
	145, // 179: services.k8s.DeletePodMonitoringRequest.resource_meta:type_name -> common.resource.Meta
	148, // 180: services.k8s.DeletePodMonitoringResponse.status:type_name -> common.resource.deletion.Status
	171, // 181: services.k8s.GetPodsResponse.pods:type_name -> common.k8s.Pod
	172, // 182: services.k8s.GetClusterAccessRequest.timeout:type_name -> google.protobuf.Duration
	173, // 183: services.k8s.GetPodPhasesResponse.PodToPhaseEntry.value:type_name -> common.k8s.PodPhase
	0,   // 184: services.k8s.K8sResourceManager.CreateNamespace:input_type -> services.k8s.CreateNamespaceRequest
	6,   // 185: services.k8s.K8sResourceManager.DeleteNamespace:input_type -> services.k8s.DeleteNamespaceRequest
	4,   // 186: services.k8s.K8sResourceManager.GetNamespace:input_type -> services.k8s.GetNamespaceRequest
	2,   // 187: services.k8s.K8sResourceManager.LabelNamespace:input_type -> services.k8s.LabelNamespaceRequest
	8,   // 188: services.k8s.K8sResourceManager.CreateServiceAccount:input_type -> services.k8s.CreateServiceAccountRequest
	12,  // 189: services.k8s.K8sResourceManager.DeleteServiceAccount:input_type -> services.k8s.DeleteServiceAccountRequest
	10,  // 190: services.k8s.K8sResourceManager.GetServiceAccount:input_type -> services.k8s.GetServiceAccountRequest
	14,  // 191: services.k8s.K8sResourceManager.AnnotateServiceAccount:input_type -> services.k8s.AnnotateServiceAccountRequest
	16,  // 192: services.k8s.K8sResourceManager.CreateConfigMap:input_type -> services.k8s.CreateConfigMapRequest
	22,  // 193: services.k8s.K8sResourceManager.DeleteConfigMap:input_type -> services.k8s.DeleteConfigMapRequest
	18,  // 194: services.k8s.K8sResourceManager.GetConfigMap:input_type -> services.k8s.GetConfigMapRequest
	20,  // 195: services.k8s.K8sResourceManager.UpdateConfigMap:input_type -> services.k8s.UpdateConfigMapRequest
	24,  // 196: services.k8s.K8sResourceManager.CreateSecret:input_type -> services.k8s.CreateSecretRequest
	28,  // 197: services.k8s.K8sResourceManager.DeleteSecret:input_type -> services.k8s.DeleteSecretRequest
	26,  // 198: services.k8s.K8sResourceManager.GetSecret:input_type -> services.k8s.GetSecretRequest
	29,  // 199: services.k8s.K8sResourceManager.UpdateSecret:input_type -> services.k8s.UpdateSecretRequest
	32,  // 200: services.k8s.K8sResourceManager.CreateRisingWave:input_type -> services.k8s.CreateRisingWaveRequest
	43,  // 201: services.k8s.K8sResourceManager.DeleteRisingWave:input_type -> services.k8s.DeleteRisingWaveRequest
	41,  // 202: services.k8s.K8sResourceManager.GetRisingWave:input_type -> services.k8s.GetRisingWaveRequest
	45,  // 203: services.k8s.K8sResourceManager.UpdateRisingWaveImage:input_type -> services.k8s.UpdateRisingWaveImageRequest
	47,  // 204: services.k8s.K8sResourceManager.UpdateRisingWaveLicenseKey:input_type -> services.k8s.UpdateRisingWaveLicenseKeyRequest
	49,  // 205: services.k8s.K8sResourceManager.UpdateRisingWaveSecretStore:input_type -> services.k8s.UpdateRisingWaveSecretStoreRequest
	51,  // 206: services.k8s.K8sResourceManager.ScaleRisingWave:input_type -> services.k8s.ScaleRisingWaveRequest
	53,  // 207: services.k8s.K8sResourceManager.ScaleRisingWaveOneOf:input_type -> services.k8s.ScaleRisingWaveRequestOneOf
	56,  // 208: services.k8s.K8sResourceManager.StartRisingWave:input_type -> services.k8s.StartRisingWaveRequest
	58,  // 209: services.k8s.K8sResourceManager.StopRisingWave:input_type -> services.k8s.StopRisingWaveRequest
	60,  // 210: services.k8s.K8sResourceManager.UpdateRisingWaveComponents:input_type -> services.k8s.UpdateRisingWaveComponentsRequest
	62,  // 211: services.k8s.K8sResourceManager.UpdateRisingWaveMetaStore:input_type -> services.k8s.UpdateRisingWaveMetaStoreRequest
	36,  // 212: services.k8s.K8sResourceManager.PutRisingWaveEnvVar:input_type -> services.k8s.PutRisingWaveEnvRequest
	38,  // 213: services.k8s.K8sResourceManager.DeleteRisingWaveEnvVar:input_type -> services.k8s.DeleteRisingWaveEnvRequest
	64,  // 214: services.k8s.K8sResourceManager.CreateRisingWaveComputeNodeGroup:input_type -> services.k8s.CreateRisingWaveComputeNodeGroupRequest
	66,  // 215: services.k8s.K8sResourceManager.UpdateRisingWaveComputeNodeGroup:input_type -> services.k8s.UpdateRisingWaveComputeNodeGroupRequest
	68,  // 216: services.k8s.K8sResourceManager.DeleteRisingWaveComputeNodeGroup:input_type -> services.k8s.DeleteRisingWaveComputeNodeGroupRequest
	70,  // 217: services.k8s.K8sResourceManager.CreateRisingWaveNodeGroup:input_type -> services.k8s.CreateRisingWaveNodeGroupRequest
	72,  // 218: services.k8s.K8sResourceManager.UpdateRisingWaveNodeGroup:input_type -> services.k8s.UpdateRisingWaveNodeGroupRequest
	74,  // 219: services.k8s.K8sResourceManager.DeleteRisingWaveNodeGroup:input_type -> services.k8s.DeleteRisingWaveNodeGroupRequest
	76,  // 220: services.k8s.K8sResourceManager.UpdateRisingWaveNodeGroupConfiguration:input_type -> services.k8s.UpdateRisingWaveNodeGroupConfigurationRequest
	78,  // 221: services.k8s.K8sResourceManager.UpdateRisingWaveNodeGroupRestartAt:input_type -> services.k8s.UpdateRisingWaveNodeGroupRestartAtRequest
	80,  // 222: services.k8s.K8sResourceManager.DeletePersistentVolumeClaims:input_type -> services.k8s.DeletePersistentVolumeClaimsRequest
	82,  // 223: services.k8s.K8sResourceManager.GetPersistentVolumeClaims:input_type -> services.k8s.GetPersistentVolumeClaimsRequest
	84,  // 224: services.k8s.K8sResourceManager.CreatePersistentVolumeClaim:input_type -> services.k8s.CreatePersistentVolumeClaimRequest
	92,  // 225: services.k8s.K8sResourceManager.GetHelmRelease:input_type -> services.k8s.GetHelmReleaseRequest
	86,  // 226: services.k8s.K8sResourceManager.InstallHelmRelease:input_type -> services.k8s.InstallHelmReleaseRequest
	88,  // 227: services.k8s.K8sResourceManager.UpgradeHelmRelease:input_type -> services.k8s.UpgradeHelmReleaseRequest
	90,  // 228: services.k8s.K8sResourceManager.UninstallHelmRelease:input_type -> services.k8s.UninstallHelmReleaseRequest
	94,  // 229: services.k8s.K8sResourceManager.GetPodPhases:input_type -> services.k8s.GetPodPhasesRequest
	96,  // 230: services.k8s.K8sResourceManager.RestartStatefulSet:input_type -> services.k8s.RestartStatefulSetRequest
	100, // 231: services.k8s.K8sResourceManager.GetStatefulSetReplicasStatus:input_type -> services.k8s.GetStatefulSetReplicasStatusRequest
	102, // 232: services.k8s.K8sResourceManager.GetDeploymentReplicasStatus:input_type -> services.k8s.GetDeploymentReplicasStatusRequest
	98,  // 233: services.k8s.K8sResourceManager.RestartDeployment:input_type -> services.k8s.RestartDeploymentRequest
	104, // 234: services.k8s.K8sResourceManager.CreateServiceMonitor:input_type -> services.k8s.CreateServiceMonitorRequest
	124, // 235: services.k8s.K8sResourceManager.DeleteServiceMonitor:input_type -> services.k8s.DeleteServiceMonitorRequest
	104, // 236: services.k8s.K8sResourceManager.CreateAzureServiceMonitor:input_type -> services.k8s.CreateServiceMonitorRequest
	124, // 237: services.k8s.K8sResourceManager.DeleteAzureServiceMonitor:input_type -> services.k8s.DeleteServiceMonitorRequest
	106, // 238: services.k8s.K8sResourceManager.CreatePodMonitoring:input_type -> services.k8s.CreatePodMonitoringRequest
	126, // 239: services.k8s.K8sResourceManager.DeletePodMonitoring:input_type -> services.k8s.DeletePodMonitoringRequest
	108, // 240: services.k8s.K8sResourceManager.CreateService:input_type -> services.k8s.CreateServiceRequest
	110, // 241: services.k8s.K8sResourceManager.CreateNetworkPolicy:input_type -> services.k8s.CreateNetworkPolicyRequest
	112, // 242: services.k8s.K8sResourceManager.CreateOrUpdateNetworkPolicy:input_type -> services.k8s.CreateOrUpdateNetworkPolicyRequest
	122, // 243: services.k8s.K8sResourceManager.DeleteNetworkPolicy:input_type -> services.k8s.DeleteNetworkPolicyRequest
	114, // 244: services.k8s.K8sResourceManager.CreatePostgreSql:input_type -> services.k8s.CreatePostgreSqlRequest
	116, // 245: services.k8s.K8sResourceManager.DeletePostgreSql:input_type -> services.k8s.DeletePostgreSqlRequest
	118, // 246: services.k8s.K8sResourceManager.UpdatePostgreSql:input_type -> services.k8s.UpdatePostgreSqlRequest
	120, // 247: services.k8s.K8sResourceManager.GetPostgreSql:input_type -> services.k8s.GetPostgreSqlRequest
	128, // 248: services.k8s.K8sResourceManager.GetPods:input_type -> services.k8s.GetPodsRequest
	130, // 249: services.k8s.K8sResourceManager.GetClusterAccess:input_type -> services.k8s.GetClusterAccessRequest
	1,   // 250: services.k8s.K8sResourceManager.CreateNamespace:output_type -> services.k8s.CreateNamespaceResponse
	7,   // 251: services.k8s.K8sResourceManager.DeleteNamespace:output_type -> services.k8s.DeleteNamespaceResponse
	5,   // 252: services.k8s.K8sResourceManager.GetNamespace:output_type -> services.k8s.GetNamespaceResponse
	3,   // 253: services.k8s.K8sResourceManager.LabelNamespace:output_type -> services.k8s.LabelNamespaceResponse
	9,   // 254: services.k8s.K8sResourceManager.CreateServiceAccount:output_type -> services.k8s.CreateServiceAccountResponse
	13,  // 255: services.k8s.K8sResourceManager.DeleteServiceAccount:output_type -> services.k8s.DeleteServiceAccountResponse
	11,  // 256: services.k8s.K8sResourceManager.GetServiceAccount:output_type -> services.k8s.GetServiceAccountResponse
	15,  // 257: services.k8s.K8sResourceManager.AnnotateServiceAccount:output_type -> services.k8s.AnnotateServiceAccountResponse
	17,  // 258: services.k8s.K8sResourceManager.CreateConfigMap:output_type -> services.k8s.CreateConfigMapResponse
	23,  // 259: services.k8s.K8sResourceManager.DeleteConfigMap:output_type -> services.k8s.DeleteConfigMapResponse
	19,  // 260: services.k8s.K8sResourceManager.GetConfigMap:output_type -> services.k8s.GetConfigMapResponse
	21,  // 261: services.k8s.K8sResourceManager.UpdateConfigMap:output_type -> services.k8s.UpdateConfigMapResponse
	25,  // 262: services.k8s.K8sResourceManager.CreateSecret:output_type -> services.k8s.CreateSecretResponse
	31,  // 263: services.k8s.K8sResourceManager.DeleteSecret:output_type -> services.k8s.DeleteSecretResponse
	27,  // 264: services.k8s.K8sResourceManager.GetSecret:output_type -> services.k8s.GetSecretResponse
	30,  // 265: services.k8s.K8sResourceManager.UpdateSecret:output_type -> services.k8s.UpdateSecretResponse
	40,  // 266: services.k8s.K8sResourceManager.CreateRisingWave:output_type -> services.k8s.CreateRisingWaveResponse
	44,  // 267: services.k8s.K8sResourceManager.DeleteRisingWave:output_type -> services.k8s.DeleteRisingWaveResponse
	42,  // 268: services.k8s.K8sResourceManager.GetRisingWave:output_type -> services.k8s.GetRisingWaveResponse
	46,  // 269: services.k8s.K8sResourceManager.UpdateRisingWaveImage:output_type -> services.k8s.UpdateRisingWaveImageResponse
	48,  // 270: services.k8s.K8sResourceManager.UpdateRisingWaveLicenseKey:output_type -> services.k8s.UpdateRisingWaveLicenseKeyResponse
	50,  // 271: services.k8s.K8sResourceManager.UpdateRisingWaveSecretStore:output_type -> services.k8s.UpdateRisingWaveSecretStoreResponse
	52,  // 272: services.k8s.K8sResourceManager.ScaleRisingWave:output_type -> services.k8s.ScaleRisingWaveResponse
	52,  // 273: services.k8s.K8sResourceManager.ScaleRisingWaveOneOf:output_type -> services.k8s.ScaleRisingWaveResponse
	57,  // 274: services.k8s.K8sResourceManager.StartRisingWave:output_type -> services.k8s.StartRisingWaveResponse
	59,  // 275: services.k8s.K8sResourceManager.StopRisingWave:output_type -> services.k8s.StopRisingWaveResponse
	61,  // 276: services.k8s.K8sResourceManager.UpdateRisingWaveComponents:output_type -> services.k8s.UpdateRisingWaveComponentsResponse
	63,  // 277: services.k8s.K8sResourceManager.UpdateRisingWaveMetaStore:output_type -> services.k8s.UpdateRisingWaveMetaStoreResponse
	37,  // 278: services.k8s.K8sResourceManager.PutRisingWaveEnvVar:output_type -> services.k8s.PutRisingWaveEnvResponse
	39,  // 279: services.k8s.K8sResourceManager.DeleteRisingWaveEnvVar:output_type -> services.k8s.DeleteRisingWaveEnvResponse
	65,  // 280: services.k8s.K8sResourceManager.CreateRisingWaveComputeNodeGroup:output_type -> services.k8s.CreateRisingWaveComputeNodeGroupResponse
	67,  // 281: services.k8s.K8sResourceManager.UpdateRisingWaveComputeNodeGroup:output_type -> services.k8s.UpdateRisingWaveComputeNodeGroupResponse
	69,  // 282: services.k8s.K8sResourceManager.DeleteRisingWaveComputeNodeGroup:output_type -> services.k8s.DeleteRisingWaveComputeNodeGroupResponse
	71,  // 283: services.k8s.K8sResourceManager.CreateRisingWaveNodeGroup:output_type -> services.k8s.CreateRisingWaveNodeGroupResponse
	73,  // 284: services.k8s.K8sResourceManager.UpdateRisingWaveNodeGroup:output_type -> services.k8s.UpdateRisingWaveNodeGroupResponse
	75,  // 285: services.k8s.K8sResourceManager.DeleteRisingWaveNodeGroup:output_type -> services.k8s.DeleteRisingWaveNodeGroupResponse
	77,  // 286: services.k8s.K8sResourceManager.UpdateRisingWaveNodeGroupConfiguration:output_type -> services.k8s.UpdateRisingWaveNodeGroupConfigurationResponse
	79,  // 287: services.k8s.K8sResourceManager.UpdateRisingWaveNodeGroupRestartAt:output_type -> services.k8s.UpdateRisingWaveNodeGroupRestartAtResponse
	81,  // 288: services.k8s.K8sResourceManager.DeletePersistentVolumeClaims:output_type -> services.k8s.DeletePersistentVolumeClaimsResponse
	83,  // 289: services.k8s.K8sResourceManager.GetPersistentVolumeClaims:output_type -> services.k8s.GetPersistentVolumeClaimsResponse
	85,  // 290: services.k8s.K8sResourceManager.CreatePersistentVolumeClaim:output_type -> services.k8s.CreatePersistentVolumeClaimResponse
	93,  // 291: services.k8s.K8sResourceManager.GetHelmRelease:output_type -> services.k8s.GetHelmReleaseResponse
	87,  // 292: services.k8s.K8sResourceManager.InstallHelmRelease:output_type -> services.k8s.InstallHelmReleaseResponse
	89,  // 293: services.k8s.K8sResourceManager.UpgradeHelmRelease:output_type -> services.k8s.UpgradeHelmReleaseResponse
	91,  // 294: services.k8s.K8sResourceManager.UninstallHelmRelease:output_type -> services.k8s.UninstallHelmReleaseResponse
	95,  // 295: services.k8s.K8sResourceManager.GetPodPhases:output_type -> services.k8s.GetPodPhasesResponse
	97,  // 296: services.k8s.K8sResourceManager.RestartStatefulSet:output_type -> services.k8s.RestartStatefulSetResponse
	101, // 297: services.k8s.K8sResourceManager.GetStatefulSetReplicasStatus:output_type -> services.k8s.GetStatefulSetReplicasStatusResponse
	103, // 298: services.k8s.K8sResourceManager.GetDeploymentReplicasStatus:output_type -> services.k8s.GetDeploymentReplicasStatusResponse
	99,  // 299: services.k8s.K8sResourceManager.RestartDeployment:output_type -> services.k8s.RestartDeploymentResponse
	105, // 300: services.k8s.K8sResourceManager.CreateServiceMonitor:output_type -> services.k8s.CreateServiceMonitorResponse
	125, // 301: services.k8s.K8sResourceManager.DeleteServiceMonitor:output_type -> services.k8s.DeleteServiceMonitorResponse
	105, // 302: services.k8s.K8sResourceManager.CreateAzureServiceMonitor:output_type -> services.k8s.CreateServiceMonitorResponse
	125, // 303: services.k8s.K8sResourceManager.DeleteAzureServiceMonitor:output_type -> services.k8s.DeleteServiceMonitorResponse
	107, // 304: services.k8s.K8sResourceManager.CreatePodMonitoring:output_type -> services.k8s.CreatePodMonitoringResponse
	127, // 305: services.k8s.K8sResourceManager.DeletePodMonitoring:output_type -> services.k8s.DeletePodMonitoringResponse
	109, // 306: services.k8s.K8sResourceManager.CreateService:output_type -> services.k8s.CreateServiceResponse
	111, // 307: services.k8s.K8sResourceManager.CreateNetworkPolicy:output_type -> services.k8s.CreateNetworkPolicyResponse
	113, // 308: services.k8s.K8sResourceManager.CreateOrUpdateNetworkPolicy:output_type -> services.k8s.CreateOrUpdateNetworkPolicyResponse
	123, // 309: services.k8s.K8sResourceManager.DeleteNetworkPolicy:output_type -> services.k8s.DeleteNetworkPolicyResponse
	115, // 310: services.k8s.K8sResourceManager.CreatePostgreSql:output_type -> services.k8s.CreatePostgreSqlResponse
	117, // 311: services.k8s.K8sResourceManager.DeletePostgreSql:output_type -> services.k8s.DeletePostgreSqlResponse
	119, // 312: services.k8s.K8sResourceManager.UpdatePostgreSql:output_type -> services.k8s.UpdatePostgreSqlResponse
	121, // 313: services.k8s.K8sResourceManager.GetPostgreSql:output_type -> services.k8s.GetPostgreSqlResponse
	129, // 314: services.k8s.K8sResourceManager.GetPods:output_type -> services.k8s.GetPodsResponse
	131, // 315: services.k8s.K8sResourceManager.GetClusterAccess:output_type -> services.k8s.GetClusterAccessResponse
	250, // [250:316] is the sub-list for method output_type
	184, // [184:250] is the sub-list for method input_type
	184, // [184:184] is the sub-list for extension type_name
	184, // [184:184] is the sub-list for extension extendee
	0,   // [0:184] is the sub-list for field type_name
}

func init() { file_services_k8s_proto_init() }
func file_services_k8s_proto_init() {
	if File_services_k8s_proto != nil {
		return
	}
	file_services_k8s_proto_msgTypes[36].OneofWrappers = []any{
		(*PutRisingWaveEnvRequest_SpecificGroups)(nil),
		(*PutRisingWaveEnvRequest_AllGroups)(nil),
		(*PutRisingWaveEnvRequest_ComputeEnvChange)(nil),
		(*PutRisingWaveEnvRequest_CompactorEnvChange)(nil),
		(*PutRisingWaveEnvRequest_StandaloneEnvChange)(nil),
		(*PutRisingWaveEnvRequest_MetaEnvChange)(nil),
		(*PutRisingWaveEnvRequest_FrontendEnvChange)(nil),
	}
	file_services_k8s_proto_msgTypes[38].OneofWrappers = []any{
		(*DeleteRisingWaveEnvRequest_SpecificGroups)(nil),
		(*DeleteRisingWaveEnvRequest_AllGroups)(nil),
		(*DeleteRisingWaveEnvRequest_ComputeEnvChange)(nil),
		(*DeleteRisingWaveEnvRequest_CompactorEnvChange)(nil),
		(*DeleteRisingWaveEnvRequest_StandaloneEnvChange)(nil),
		(*DeleteRisingWaveEnvRequest_MetaEnvChange)(nil),
		(*DeleteRisingWaveEnvRequest_FrontendEnvChange)(nil),
	}
	file_services_k8s_proto_msgTypes[53].OneofWrappers = []any{
		(*ScaleRisingWaveRequestOneOf_StandaloneSpec)(nil),
		(*ScaleRisingWaveRequestOneOf_ClusterSpec)(nil),
	}
	file_services_k8s_proto_msgTypes[60].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_services_k8s_proto_rawDesc), len(file_services_k8s_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   145,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_services_k8s_proto_goTypes,
		DependencyIndexes: file_services_k8s_proto_depIdxs,
		MessageInfos:      file_services_k8s_proto_msgTypes,
	}.Build()
	File_services_k8s_proto = out.File
	file_services_k8s_proto_goTypes = nil
	file_services_k8s_proto_depIdxs = nil
}
