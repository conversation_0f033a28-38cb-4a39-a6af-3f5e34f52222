// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.5.1
// - protoc             v6.30.1
// source: services/k8s.proto

package k8s

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.64.0 or later.
const _ = grpc.SupportPackageIsVersion9

const (
	K8SResourceManager_CreateNamespace_FullMethodName                        = "/services.k8s.K8sResourceManager/CreateNamespace"
	K8SResourceManager_DeleteNamespace_FullMethodName                        = "/services.k8s.K8sResourceManager/DeleteNamespace"
	K8SResourceManager_GetNamespace_FullMethodName                           = "/services.k8s.K8sResourceManager/GetNamespace"
	K8SResourceManager_LabelNamespace_FullMethodName                         = "/services.k8s.K8sResourceManager/LabelNamespace"
	K8SResourceManager_CreateServiceAccount_FullMethodName                   = "/services.k8s.K8sResourceManager/CreateServiceAccount"
	K8SResourceManager_DeleteServiceAccount_FullMethodName                   = "/services.k8s.K8sResourceManager/DeleteServiceAccount"
	K8SResourceManager_GetServiceAccount_FullMethodName                      = "/services.k8s.K8sResourceManager/GetServiceAccount"
	K8SResourceManager_AnnotateServiceAccount_FullMethodName                 = "/services.k8s.K8sResourceManager/AnnotateServiceAccount"
	K8SResourceManager_CreateConfigMap_FullMethodName                        = "/services.k8s.K8sResourceManager/CreateConfigMap"
	K8SResourceManager_DeleteConfigMap_FullMethodName                        = "/services.k8s.K8sResourceManager/DeleteConfigMap"
	K8SResourceManager_GetConfigMap_FullMethodName                           = "/services.k8s.K8sResourceManager/GetConfigMap"
	K8SResourceManager_UpdateConfigMap_FullMethodName                        = "/services.k8s.K8sResourceManager/UpdateConfigMap"
	K8SResourceManager_CreateSecret_FullMethodName                           = "/services.k8s.K8sResourceManager/CreateSecret"
	K8SResourceManager_DeleteSecret_FullMethodName                           = "/services.k8s.K8sResourceManager/DeleteSecret"
	K8SResourceManager_GetSecret_FullMethodName                              = "/services.k8s.K8sResourceManager/GetSecret"
	K8SResourceManager_UpdateSecret_FullMethodName                           = "/services.k8s.K8sResourceManager/UpdateSecret"
	K8SResourceManager_CreateRisingWave_FullMethodName                       = "/services.k8s.K8sResourceManager/CreateRisingWave"
	K8SResourceManager_DeleteRisingWave_FullMethodName                       = "/services.k8s.K8sResourceManager/DeleteRisingWave"
	K8SResourceManager_GetRisingWave_FullMethodName                          = "/services.k8s.K8sResourceManager/GetRisingWave"
	K8SResourceManager_UpdateRisingWaveImage_FullMethodName                  = "/services.k8s.K8sResourceManager/UpdateRisingWaveImage"
	K8SResourceManager_UpdateRisingWaveLicenseKey_FullMethodName             = "/services.k8s.K8sResourceManager/UpdateRisingWaveLicenseKey"
	K8SResourceManager_UpdateRisingWaveSecretStore_FullMethodName            = "/services.k8s.K8sResourceManager/UpdateRisingWaveSecretStore"
	K8SResourceManager_ScaleRisingWave_FullMethodName                        = "/services.k8s.K8sResourceManager/ScaleRisingWave"
	K8SResourceManager_ScaleRisingWaveOneOf_FullMethodName                   = "/services.k8s.K8sResourceManager/ScaleRisingWaveOneOf"
	K8SResourceManager_StartRisingWave_FullMethodName                        = "/services.k8s.K8sResourceManager/StartRisingWave"
	K8SResourceManager_StopRisingWave_FullMethodName                         = "/services.k8s.K8sResourceManager/StopRisingWave"
	K8SResourceManager_UpdateRisingWaveComponents_FullMethodName             = "/services.k8s.K8sResourceManager/UpdateRisingWaveComponents"
	K8SResourceManager_UpdateRisingWaveMetaStore_FullMethodName              = "/services.k8s.K8sResourceManager/UpdateRisingWaveMetaStore"
	K8SResourceManager_PutRisingWaveEnvVar_FullMethodName                    = "/services.k8s.K8sResourceManager/PutRisingWaveEnvVar"
	K8SResourceManager_DeleteRisingWaveEnvVar_FullMethodName                 = "/services.k8s.K8sResourceManager/DeleteRisingWaveEnvVar"
	K8SResourceManager_CreateRisingWaveComputeNodeGroup_FullMethodName       = "/services.k8s.K8sResourceManager/CreateRisingWaveComputeNodeGroup"
	K8SResourceManager_UpdateRisingWaveComputeNodeGroup_FullMethodName       = "/services.k8s.K8sResourceManager/UpdateRisingWaveComputeNodeGroup"
	K8SResourceManager_DeleteRisingWaveComputeNodeGroup_FullMethodName       = "/services.k8s.K8sResourceManager/DeleteRisingWaveComputeNodeGroup"
	K8SResourceManager_CreateRisingWaveNodeGroup_FullMethodName              = "/services.k8s.K8sResourceManager/CreateRisingWaveNodeGroup"
	K8SResourceManager_UpdateRisingWaveNodeGroup_FullMethodName              = "/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroup"
	K8SResourceManager_DeleteRisingWaveNodeGroup_FullMethodName              = "/services.k8s.K8sResourceManager/DeleteRisingWaveNodeGroup"
	K8SResourceManager_UpdateRisingWaveNodeGroupConfiguration_FullMethodName = "/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroupConfiguration"
	K8SResourceManager_UpdateRisingWaveNodeGroupRestartAt_FullMethodName     = "/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroupRestartAt"
	K8SResourceManager_DeletePersistentVolumeClaims_FullMethodName           = "/services.k8s.K8sResourceManager/DeletePersistentVolumeClaims"
	K8SResourceManager_GetPersistentVolumeClaims_FullMethodName              = "/services.k8s.K8sResourceManager/GetPersistentVolumeClaims"
	K8SResourceManager_CreatePersistentVolumeClaim_FullMethodName            = "/services.k8s.K8sResourceManager/CreatePersistentVolumeClaim"
	K8SResourceManager_GetHelmRelease_FullMethodName                         = "/services.k8s.K8sResourceManager/GetHelmRelease"
	K8SResourceManager_InstallHelmRelease_FullMethodName                     = "/services.k8s.K8sResourceManager/InstallHelmRelease"
	K8SResourceManager_UpgradeHelmRelease_FullMethodName                     = "/services.k8s.K8sResourceManager/UpgradeHelmRelease"
	K8SResourceManager_UninstallHelmRelease_FullMethodName                   = "/services.k8s.K8sResourceManager/UninstallHelmRelease"
	K8SResourceManager_GetPodPhases_FullMethodName                           = "/services.k8s.K8sResourceManager/GetPodPhases"
	K8SResourceManager_RestartStatefulSet_FullMethodName                     = "/services.k8s.K8sResourceManager/RestartStatefulSet"
	K8SResourceManager_GetStatefulSetReplicasStatus_FullMethodName           = "/services.k8s.K8sResourceManager/GetStatefulSetReplicasStatus"
	K8SResourceManager_GetDeploymentReplicasStatus_FullMethodName            = "/services.k8s.K8sResourceManager/GetDeploymentReplicasStatus"
	K8SResourceManager_RestartDeployment_FullMethodName                      = "/services.k8s.K8sResourceManager/RestartDeployment"
	K8SResourceManager_CreateServiceMonitor_FullMethodName                   = "/services.k8s.K8sResourceManager/CreateServiceMonitor"
	K8SResourceManager_DeleteServiceMonitor_FullMethodName                   = "/services.k8s.K8sResourceManager/DeleteServiceMonitor"
	K8SResourceManager_CreateAzureServiceMonitor_FullMethodName              = "/services.k8s.K8sResourceManager/CreateAzureServiceMonitor"
	K8SResourceManager_DeleteAzureServiceMonitor_FullMethodName              = "/services.k8s.K8sResourceManager/DeleteAzureServiceMonitor"
	K8SResourceManager_CreatePodMonitoring_FullMethodName                    = "/services.k8s.K8sResourceManager/CreatePodMonitoring"
	K8SResourceManager_DeletePodMonitoring_FullMethodName                    = "/services.k8s.K8sResourceManager/DeletePodMonitoring"
	K8SResourceManager_CreateService_FullMethodName                          = "/services.k8s.K8sResourceManager/CreateService"
	K8SResourceManager_CreateNetworkPolicy_FullMethodName                    = "/services.k8s.K8sResourceManager/CreateNetworkPolicy"
	K8SResourceManager_CreateOrUpdateNetworkPolicy_FullMethodName            = "/services.k8s.K8sResourceManager/CreateOrUpdateNetworkPolicy"
	K8SResourceManager_DeleteNetworkPolicy_FullMethodName                    = "/services.k8s.K8sResourceManager/DeleteNetworkPolicy"
	K8SResourceManager_CreatePostgreSql_FullMethodName                       = "/services.k8s.K8sResourceManager/CreatePostgreSql"
	K8SResourceManager_DeletePostgreSql_FullMethodName                       = "/services.k8s.K8sResourceManager/DeletePostgreSql"
	K8SResourceManager_UpdatePostgreSql_FullMethodName                       = "/services.k8s.K8sResourceManager/UpdatePostgreSql"
	K8SResourceManager_GetPostgreSql_FullMethodName                          = "/services.k8s.K8sResourceManager/GetPostgreSql"
	K8SResourceManager_GetPods_FullMethodName                                = "/services.k8s.K8sResourceManager/GetPods"
	K8SResourceManager_GetClusterAccess_FullMethodName                       = "/services.k8s.K8sResourceManager/GetClusterAccess"
)

// K8SResourceManagerClient is the client API for K8SResourceManager service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
//
// K8sResourceManager offers APIs for native K8s resoruces management.
// The ID and namespace of all resources, if not specified, needs to follow the
// K8s naming restriction.
type K8SResourceManagerClient interface {
	// Expected a CREATED status on success.
	CreateNamespace(ctx context.Context, in *CreateNamespaceRequest, opts ...grpc.CallOption) (*CreateNamespaceResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteNamespace(ctx context.Context, in *DeleteNamespaceRequest, opts ...grpc.CallOption) (*DeleteNamespaceResponse, error)
	GetNamespace(ctx context.Context, in *GetNamespaceRequest, opts ...grpc.CallOption) (*GetNamespaceResponse, error)
	// LabelNamespace add labels to a specified namespace. If the key already
	// exists, new value will override the existing one.
	LabelNamespace(ctx context.Context, in *LabelNamespaceRequest, opts ...grpc.CallOption) (*LabelNamespaceResponse, error)
	// Expected a CREATED status on success.
	CreateServiceAccount(ctx context.Context, in *CreateServiceAccountRequest, opts ...grpc.CallOption) (*CreateServiceAccountResponse, error)
	// Expected a DELETED status on success.
	DeleteServiceAccount(ctx context.Context, in *DeleteServiceAccountRequest, opts ...grpc.CallOption) (*DeleteServiceAccountResponse, error)
	GetServiceAccount(ctx context.Context, in *GetServiceAccountRequest, opts ...grpc.CallOption) (*GetServiceAccountResponse, error)
	AnnotateServiceAccount(ctx context.Context, in *AnnotateServiceAccountRequest, opts ...grpc.CallOption) (*AnnotateServiceAccountResponse, error)
	// Expected a CREATED status on success.
	CreateConfigMap(ctx context.Context, in *CreateConfigMapRequest, opts ...grpc.CallOption) (*CreateConfigMapResponse, error)
	// Expected a DELETED status on success.
	DeleteConfigMap(ctx context.Context, in *DeleteConfigMapRequest, opts ...grpc.CallOption) (*DeleteConfigMapResponse, error)
	GetConfigMap(ctx context.Context, in *GetConfigMapRequest, opts ...grpc.CallOption) (*GetConfigMapResponse, error)
	// UpdateConfigMap generats a JSON merge patch that converts `from` to `to`
	// and apply the generated patch to the runtime object.
	UpdateConfigMap(ctx context.Context, in *UpdateConfigMapRequest, opts ...grpc.CallOption) (*UpdateConfigMapResponse, error)
	// Expected a CREATED status on success.
	CreateSecret(ctx context.Context, in *CreateSecretRequest, opts ...grpc.CallOption) (*CreateSecretResponse, error)
	// Expected a DELETED status on success.
	DeleteSecret(ctx context.Context, in *DeleteSecretRequest, opts ...grpc.CallOption) (*DeleteSecretResponse, error)
	GetSecret(ctx context.Context, in *GetSecretRequest, opts ...grpc.CallOption) (*GetSecretResponse, error)
	UpdateSecret(ctx context.Context, in *UpdateSecretRequest, opts ...grpc.CallOption) (*UpdateSecretResponse, error)
	// Expected a SCHEDULED status on success.
	CreateRisingWave(ctx context.Context, in *CreateRisingWaveRequest, opts ...grpc.CallOption) (*CreateRisingWaveResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteRisingWave(ctx context.Context, in *DeleteRisingWaveRequest, opts ...grpc.CallOption) (*DeleteRisingWaveResponse, error)
	GetRisingWave(ctx context.Context, in *GetRisingWaveRequest, opts ...grpc.CallOption) (*GetRisingWaveResponse, error)
	// UpdateRisingWave generats a JSON merge patch that converts `from` to `to`
	// and apply the generated patch to the runtime RW object.
	UpdateRisingWaveImage(ctx context.Context, in *UpdateRisingWaveImageRequest, opts ...grpc.CallOption) (*UpdateRisingWaveImageResponse, error)
	UpdateRisingWaveLicenseKey(ctx context.Context, in *UpdateRisingWaveLicenseKeyRequest, opts ...grpc.CallOption) (*UpdateRisingWaveLicenseKeyResponse, error)
	UpdateRisingWaveSecretStore(ctx context.Context, in *UpdateRisingWaveSecretStoreRequest, opts ...grpc.CallOption) (*UpdateRisingWaveSecretStoreResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use ScaleRisingWaveOneOf
	ScaleRisingWave(ctx context.Context, in *ScaleRisingWaveRequest, opts ...grpc.CallOption) (*ScaleRisingWaveResponse, error)
	// Scale the replica of `default` node group
	ScaleRisingWaveOneOf(ctx context.Context, in *ScaleRisingWaveRequestOneOf, opts ...grpc.CallOption) (*ScaleRisingWaveResponse, error)
	// StartRisingWave scale all node groups to previous replica
	StartRisingWave(ctx context.Context, in *StartRisingWaveRequest, opts ...grpc.CallOption) (*StartRisingWaveResponse, error)
	// StopRisingWave scale all node groups to 0, and record the current replica
	StopRisingWave(ctx context.Context, in *StopRisingWaveRequest, opts ...grpc.CallOption) (*StopRisingWaveResponse, error)
	// Expected a SCHEDULED status on success.
	UpdateRisingWaveComponents(ctx context.Context, in *UpdateRisingWaveComponentsRequest, opts ...grpc.CallOption) (*UpdateRisingWaveComponentsResponse, error)
	// Expected a SCHEDULED status on success.
	UpdateRisingWaveMetaStore(ctx context.Context, in *UpdateRisingWaveMetaStoreRequest, opts ...grpc.CallOption) (*UpdateRisingWaveMetaStoreResponse, error)
	// Add one or more environment variable to zero or more components.
	PutRisingWaveEnvVar(ctx context.Context, in *PutRisingWaveEnvRequest, opts ...grpc.CallOption) (*PutRisingWaveEnvResponse, error)
	// Removes one or more environment variable from zero or more components.
	DeleteRisingWaveEnvVar(ctx context.Context, in *DeleteRisingWaveEnvRequest, opts ...grpc.CallOption) (*DeleteRisingWaveEnvResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use CreateRisingWaveNodeGroup.
	CreateRisingWaveComputeNodeGroup(ctx context.Context, in *CreateRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*CreateRisingWaveComputeNodeGroupResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use UpdateRisingWaveNodeGroup.
	UpdateRisingWaveComputeNodeGroup(ctx context.Context, in *UpdateRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*UpdateRisingWaveComputeNodeGroupResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use DeleteRisingWaveNodeGroup.
	DeleteRisingWaveComputeNodeGroup(ctx context.Context, in *DeleteRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*DeleteRisingWaveComputeNodeGroupResponse, error)
	// Expected a CREATED status on success.
	CreateRisingWaveNodeGroup(ctx context.Context, in *CreateRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*CreateRisingWaveNodeGroupResponse, error)
	// Expected a UPDATED status on success.
	UpdateRisingWaveNodeGroup(ctx context.Context, in *UpdateRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupResponse, error)
	// Expected a DELETED status on success.
	DeleteRisingWaveNodeGroup(ctx context.Context, in *DeleteRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*DeleteRisingWaveNodeGroupResponse, error)
	// Expected a UPDATED status on success.
	UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, in *UpdateRisingWaveNodeGroupConfigurationRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupConfigurationResponse, error)
	// Expected a UPDATED status on success. Setting any value for this field will immediately trigger nodes restart.
	UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, in *UpdateRisingWaveNodeGroupRestartAtRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupRestartAtResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePersistentVolumeClaims(ctx context.Context, in *DeletePersistentVolumeClaimsRequest, opts ...grpc.CallOption) (*DeletePersistentVolumeClaimsResponse, error)
	GetPersistentVolumeClaims(ctx context.Context, in *GetPersistentVolumeClaimsRequest, opts ...grpc.CallOption) (*GetPersistentVolumeClaimsResponse, error)
	CreatePersistentVolumeClaim(ctx context.Context, in *CreatePersistentVolumeClaimRequest, opts ...grpc.CallOption) (*CreatePersistentVolumeClaimResponse, error)
	GetHelmRelease(ctx context.Context, in *GetHelmReleaseRequest, opts ...grpc.CallOption) (*GetHelmReleaseResponse, error)
	InstallHelmRelease(ctx context.Context, in *InstallHelmReleaseRequest, opts ...grpc.CallOption) (*InstallHelmReleaseResponse, error)
	UpgradeHelmRelease(ctx context.Context, in *UpgradeHelmReleaseRequest, opts ...grpc.CallOption) (*UpgradeHelmReleaseResponse, error)
	UninstallHelmRelease(ctx context.Context, in *UninstallHelmReleaseRequest, opts ...grpc.CallOption) (*UninstallHelmReleaseResponse, error)
	// GetPodPhases retrieves all pods' phases in a namespace. The resource id
	// will be a k8s namesapce and the resource namespace is omitted.
	// Returns a map from pod name to pod phase.
	GetPodPhases(ctx context.Context, in *GetPodPhasesRequest, opts ...grpc.CallOption) (*GetPodPhasesResponse, error)
	// Expected a READY status on success.
	RestartStatefulSet(ctx context.Context, in *RestartStatefulSetRequest, opts ...grpc.CallOption) (*RestartStatefulSetResponse, error)
	GetStatefulSetReplicasStatus(ctx context.Context, in *GetStatefulSetReplicasStatusRequest, opts ...grpc.CallOption) (*GetStatefulSetReplicasStatusResponse, error)
	GetDeploymentReplicasStatus(ctx context.Context, in *GetDeploymentReplicasStatusRequest, opts ...grpc.CallOption) (*GetDeploymentReplicasStatusResponse, error)
	RestartDeployment(ctx context.Context, in *RestartDeploymentRequest, opts ...grpc.CallOption) (*RestartDeploymentResponse, error)
	// Expected a CREATED status on success.
	CreateServiceMonitor(ctx context.Context, in *CreateServiceMonitorRequest, opts ...grpc.CallOption) (*CreateServiceMonitorResponse, error)
	// Expect a DELETED status on success
	DeleteServiceMonitor(ctx context.Context, in *DeleteServiceMonitorRequest, opts ...grpc.CallOption) (*DeleteServiceMonitorResponse, error)
	// Expected a CREATED status on success.
	CreateAzureServiceMonitor(ctx context.Context, in *CreateServiceMonitorRequest, opts ...grpc.CallOption) (*CreateServiceMonitorResponse, error)
	// Expect a DELETED status on success
	DeleteAzureServiceMonitor(ctx context.Context, in *DeleteServiceMonitorRequest, opts ...grpc.CallOption) (*DeleteServiceMonitorResponse, error)
	// Expected a CREATED status on success.
	CreatePodMonitoring(ctx context.Context, in *CreatePodMonitoringRequest, opts ...grpc.CallOption) (*CreatePodMonitoringResponse, error)
	// Expect a DELETED status on success
	DeletePodMonitoring(ctx context.Context, in *DeletePodMonitoringRequest, opts ...grpc.CallOption) (*DeletePodMonitoringResponse, error)
	// Expected a SCHEDULED status on success.
	CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error)
	// Expect a CREATED status on success
	CreateNetworkPolicy(ctx context.Context, in *CreateNetworkPolicyRequest, opts ...grpc.CallOption) (*CreateNetworkPolicyResponse, error)
	CreateOrUpdateNetworkPolicy(ctx context.Context, in *CreateOrUpdateNetworkPolicyRequest, opts ...grpc.CallOption) (*CreateOrUpdateNetworkPolicyResponse, error)
	// Expect a DELETED status on success
	DeleteNetworkPolicy(ctx context.Context, in *DeleteNetworkPolicyRequest, opts ...grpc.CallOption) (*DeleteNetworkPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePostgreSql(ctx context.Context, in *CreatePostgreSqlRequest, opts ...grpc.CallOption) (*CreatePostgreSqlResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePostgreSql(ctx context.Context, in *DeletePostgreSqlRequest, opts ...grpc.CallOption) (*DeletePostgreSqlResponse, error)
	// Expected a SCHEDULED status on success.
	UpdatePostgreSql(ctx context.Context, in *UpdatePostgreSqlRequest, opts ...grpc.CallOption) (*UpdatePostgreSqlResponse, error)
	GetPostgreSql(ctx context.Context, in *GetPostgreSqlRequest, opts ...grpc.CallOption) (*GetPostgreSqlResponse, error)
	// GetPods retrieves all pods in a namespace. The resource id
	// will be a k8s namesapce and the resource namespace is omitted.
	GetPods(ctx context.Context, in *GetPodsRequest, opts ...grpc.CallOption) (*GetPodsResponse, error)
	GetClusterAccess(ctx context.Context, in *GetClusterAccessRequest, opts ...grpc.CallOption) (*GetClusterAccessResponse, error)
}

type k8SResourceManagerClient struct {
	cc grpc.ClientConnInterface
}

func NewK8SResourceManagerClient(cc grpc.ClientConnInterface) K8SResourceManagerClient {
	return &k8SResourceManagerClient{cc}
}

func (c *k8SResourceManagerClient) CreateNamespace(ctx context.Context, in *CreateNamespaceRequest, opts ...grpc.CallOption) (*CreateNamespaceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateNamespaceResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateNamespace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteNamespace(ctx context.Context, in *DeleteNamespaceRequest, opts ...grpc.CallOption) (*DeleteNamespaceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteNamespaceResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteNamespace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetNamespace(ctx context.Context, in *GetNamespaceRequest, opts ...grpc.CallOption) (*GetNamespaceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetNamespaceResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetNamespace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) LabelNamespace(ctx context.Context, in *LabelNamespaceRequest, opts ...grpc.CallOption) (*LabelNamespaceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(LabelNamespaceResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_LabelNamespace_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateServiceAccount(ctx context.Context, in *CreateServiceAccountRequest, opts ...grpc.CallOption) (*CreateServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceAccountResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteServiceAccount(ctx context.Context, in *DeleteServiceAccountRequest, opts ...grpc.CallOption) (*DeleteServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteServiceAccountResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetServiceAccount(ctx context.Context, in *GetServiceAccountRequest, opts ...grpc.CallOption) (*GetServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetServiceAccountResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) AnnotateServiceAccount(ctx context.Context, in *AnnotateServiceAccountRequest, opts ...grpc.CallOption) (*AnnotateServiceAccountResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(AnnotateServiceAccountResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_AnnotateServiceAccount_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateConfigMap(ctx context.Context, in *CreateConfigMapRequest, opts ...grpc.CallOption) (*CreateConfigMapResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateConfigMapResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateConfigMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteConfigMap(ctx context.Context, in *DeleteConfigMapRequest, opts ...grpc.CallOption) (*DeleteConfigMapResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteConfigMapResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteConfigMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetConfigMap(ctx context.Context, in *GetConfigMapRequest, opts ...grpc.CallOption) (*GetConfigMapResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetConfigMapResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetConfigMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateConfigMap(ctx context.Context, in *UpdateConfigMapRequest, opts ...grpc.CallOption) (*UpdateConfigMapResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateConfigMapResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateConfigMap_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateSecret(ctx context.Context, in *CreateSecretRequest, opts ...grpc.CallOption) (*CreateSecretResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateSecretResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateSecret_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteSecret(ctx context.Context, in *DeleteSecretRequest, opts ...grpc.CallOption) (*DeleteSecretResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteSecretResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteSecret_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetSecret(ctx context.Context, in *GetSecretRequest, opts ...grpc.CallOption) (*GetSecretResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetSecretResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetSecret_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateSecret(ctx context.Context, in *UpdateSecretRequest, opts ...grpc.CallOption) (*UpdateSecretResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateSecretResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateSecret_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateRisingWave(ctx context.Context, in *CreateRisingWaveRequest, opts ...grpc.CallOption) (*CreateRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateRisingWave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteRisingWave(ctx context.Context, in *DeleteRisingWaveRequest, opts ...grpc.CallOption) (*DeleteRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteRisingWave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetRisingWave(ctx context.Context, in *GetRisingWaveRequest, opts ...grpc.CallOption) (*GetRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetRisingWave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveImage(ctx context.Context, in *UpdateRisingWaveImageRequest, opts ...grpc.CallOption) (*UpdateRisingWaveImageResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveImageResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveImage_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveLicenseKey(ctx context.Context, in *UpdateRisingWaveLicenseKeyRequest, opts ...grpc.CallOption) (*UpdateRisingWaveLicenseKeyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveLicenseKeyResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveLicenseKey_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveSecretStore(ctx context.Context, in *UpdateRisingWaveSecretStoreRequest, opts ...grpc.CallOption) (*UpdateRisingWaveSecretStoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveSecretStoreResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveSecretStore_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *k8SResourceManagerClient) ScaleRisingWave(ctx context.Context, in *ScaleRisingWaveRequest, opts ...grpc.CallOption) (*ScaleRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScaleRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_ScaleRisingWave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) ScaleRisingWaveOneOf(ctx context.Context, in *ScaleRisingWaveRequestOneOf, opts ...grpc.CallOption) (*ScaleRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(ScaleRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_ScaleRisingWaveOneOf_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) StartRisingWave(ctx context.Context, in *StartRisingWaveRequest, opts ...grpc.CallOption) (*StartRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StartRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_StartRisingWave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) StopRisingWave(ctx context.Context, in *StopRisingWaveRequest, opts ...grpc.CallOption) (*StopRisingWaveResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(StopRisingWaveResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_StopRisingWave_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveComponents(ctx context.Context, in *UpdateRisingWaveComponentsRequest, opts ...grpc.CallOption) (*UpdateRisingWaveComponentsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveComponentsResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveComponents_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveMetaStore(ctx context.Context, in *UpdateRisingWaveMetaStoreRequest, opts ...grpc.CallOption) (*UpdateRisingWaveMetaStoreResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveMetaStoreResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveMetaStore_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) PutRisingWaveEnvVar(ctx context.Context, in *PutRisingWaveEnvRequest, opts ...grpc.CallOption) (*PutRisingWaveEnvResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(PutRisingWaveEnvResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_PutRisingWaveEnvVar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteRisingWaveEnvVar(ctx context.Context, in *DeleteRisingWaveEnvRequest, opts ...grpc.CallOption) (*DeleteRisingWaveEnvResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteRisingWaveEnvResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteRisingWaveEnvVar_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *k8SResourceManagerClient) CreateRisingWaveComputeNodeGroup(ctx context.Context, in *CreateRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*CreateRisingWaveComputeNodeGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRisingWaveComputeNodeGroupResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateRisingWaveComputeNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *k8SResourceManagerClient) UpdateRisingWaveComputeNodeGroup(ctx context.Context, in *UpdateRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*UpdateRisingWaveComputeNodeGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveComputeNodeGroupResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveComputeNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// Deprecated: Do not use.
func (c *k8SResourceManagerClient) DeleteRisingWaveComputeNodeGroup(ctx context.Context, in *DeleteRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*DeleteRisingWaveComputeNodeGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteRisingWaveComputeNodeGroupResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteRisingWaveComputeNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateRisingWaveNodeGroup(ctx context.Context, in *CreateRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*CreateRisingWaveNodeGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateRisingWaveNodeGroupResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateRisingWaveNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveNodeGroup(ctx context.Context, in *UpdateRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveNodeGroupResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteRisingWaveNodeGroup(ctx context.Context, in *DeleteRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*DeleteRisingWaveNodeGroupResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteRisingWaveNodeGroupResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteRisingWaveNodeGroup_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, in *UpdateRisingWaveNodeGroupConfigurationRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupConfigurationResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveNodeGroupConfigurationResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveNodeGroupConfiguration_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, in *UpdateRisingWaveNodeGroupRestartAtRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupRestartAtResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdateRisingWaveNodeGroupRestartAtResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdateRisingWaveNodeGroupRestartAt_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeletePersistentVolumeClaims(ctx context.Context, in *DeletePersistentVolumeClaimsRequest, opts ...grpc.CallOption) (*DeletePersistentVolumeClaimsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePersistentVolumeClaimsResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeletePersistentVolumeClaims_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetPersistentVolumeClaims(ctx context.Context, in *GetPersistentVolumeClaimsRequest, opts ...grpc.CallOption) (*GetPersistentVolumeClaimsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPersistentVolumeClaimsResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetPersistentVolumeClaims_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreatePersistentVolumeClaim(ctx context.Context, in *CreatePersistentVolumeClaimRequest, opts ...grpc.CallOption) (*CreatePersistentVolumeClaimResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePersistentVolumeClaimResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreatePersistentVolumeClaim_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetHelmRelease(ctx context.Context, in *GetHelmReleaseRequest, opts ...grpc.CallOption) (*GetHelmReleaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetHelmReleaseResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetHelmRelease_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) InstallHelmRelease(ctx context.Context, in *InstallHelmReleaseRequest, opts ...grpc.CallOption) (*InstallHelmReleaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(InstallHelmReleaseResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_InstallHelmRelease_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpgradeHelmRelease(ctx context.Context, in *UpgradeHelmReleaseRequest, opts ...grpc.CallOption) (*UpgradeHelmReleaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpgradeHelmReleaseResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpgradeHelmRelease_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UninstallHelmRelease(ctx context.Context, in *UninstallHelmReleaseRequest, opts ...grpc.CallOption) (*UninstallHelmReleaseResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UninstallHelmReleaseResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UninstallHelmRelease_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetPodPhases(ctx context.Context, in *GetPodPhasesRequest, opts ...grpc.CallOption) (*GetPodPhasesResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPodPhasesResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetPodPhases_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) RestartStatefulSet(ctx context.Context, in *RestartStatefulSetRequest, opts ...grpc.CallOption) (*RestartStatefulSetResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RestartStatefulSetResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_RestartStatefulSet_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetStatefulSetReplicasStatus(ctx context.Context, in *GetStatefulSetReplicasStatusRequest, opts ...grpc.CallOption) (*GetStatefulSetReplicasStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetStatefulSetReplicasStatusResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetStatefulSetReplicasStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetDeploymentReplicasStatus(ctx context.Context, in *GetDeploymentReplicasStatusRequest, opts ...grpc.CallOption) (*GetDeploymentReplicasStatusResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetDeploymentReplicasStatusResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetDeploymentReplicasStatus_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) RestartDeployment(ctx context.Context, in *RestartDeploymentRequest, opts ...grpc.CallOption) (*RestartDeploymentResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(RestartDeploymentResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_RestartDeployment_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateServiceMonitor(ctx context.Context, in *CreateServiceMonitorRequest, opts ...grpc.CallOption) (*CreateServiceMonitorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceMonitorResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateServiceMonitor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteServiceMonitor(ctx context.Context, in *DeleteServiceMonitorRequest, opts ...grpc.CallOption) (*DeleteServiceMonitorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteServiceMonitorResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteServiceMonitor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateAzureServiceMonitor(ctx context.Context, in *CreateServiceMonitorRequest, opts ...grpc.CallOption) (*CreateServiceMonitorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceMonitorResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateAzureServiceMonitor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteAzureServiceMonitor(ctx context.Context, in *DeleteServiceMonitorRequest, opts ...grpc.CallOption) (*DeleteServiceMonitorResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteServiceMonitorResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteAzureServiceMonitor_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreatePodMonitoring(ctx context.Context, in *CreatePodMonitoringRequest, opts ...grpc.CallOption) (*CreatePodMonitoringResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePodMonitoringResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreatePodMonitoring_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeletePodMonitoring(ctx context.Context, in *DeletePodMonitoringRequest, opts ...grpc.CallOption) (*DeletePodMonitoringResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePodMonitoringResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeletePodMonitoring_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateServiceResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateService_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateNetworkPolicy(ctx context.Context, in *CreateNetworkPolicyRequest, opts ...grpc.CallOption) (*CreateNetworkPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateNetworkPolicyResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateNetworkPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreateOrUpdateNetworkPolicy(ctx context.Context, in *CreateOrUpdateNetworkPolicyRequest, opts ...grpc.CallOption) (*CreateOrUpdateNetworkPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreateOrUpdateNetworkPolicyResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreateOrUpdateNetworkPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeleteNetworkPolicy(ctx context.Context, in *DeleteNetworkPolicyRequest, opts ...grpc.CallOption) (*DeleteNetworkPolicyResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeleteNetworkPolicyResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeleteNetworkPolicy_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) CreatePostgreSql(ctx context.Context, in *CreatePostgreSqlRequest, opts ...grpc.CallOption) (*CreatePostgreSqlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(CreatePostgreSqlResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_CreatePostgreSql_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) DeletePostgreSql(ctx context.Context, in *DeletePostgreSqlRequest, opts ...grpc.CallOption) (*DeletePostgreSqlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(DeletePostgreSqlResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_DeletePostgreSql_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) UpdatePostgreSql(ctx context.Context, in *UpdatePostgreSqlRequest, opts ...grpc.CallOption) (*UpdatePostgreSqlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(UpdatePostgreSqlResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_UpdatePostgreSql_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetPostgreSql(ctx context.Context, in *GetPostgreSqlRequest, opts ...grpc.CallOption) (*GetPostgreSqlResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPostgreSqlResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetPostgreSql_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetPods(ctx context.Context, in *GetPodsRequest, opts ...grpc.CallOption) (*GetPodsResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetPodsResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetPods_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *k8SResourceManagerClient) GetClusterAccess(ctx context.Context, in *GetClusterAccessRequest, opts ...grpc.CallOption) (*GetClusterAccessResponse, error) {
	cOpts := append([]grpc.CallOption{grpc.StaticMethod()}, opts...)
	out := new(GetClusterAccessResponse)
	err := c.cc.Invoke(ctx, K8SResourceManager_GetClusterAccess_FullMethodName, in, out, cOpts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// K8SResourceManagerServer is the server API for K8SResourceManager service.
// All implementations must embed UnimplementedK8SResourceManagerServer
// for forward compatibility.
//
// K8sResourceManager offers APIs for native K8s resoruces management.
// The ID and namespace of all resources, if not specified, needs to follow the
// K8s naming restriction.
type K8SResourceManagerServer interface {
	// Expected a CREATED status on success.
	CreateNamespace(context.Context, *CreateNamespaceRequest) (*CreateNamespaceResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteNamespace(context.Context, *DeleteNamespaceRequest) (*DeleteNamespaceResponse, error)
	GetNamespace(context.Context, *GetNamespaceRequest) (*GetNamespaceResponse, error)
	// LabelNamespace add labels to a specified namespace. If the key already
	// exists, new value will override the existing one.
	LabelNamespace(context.Context, *LabelNamespaceRequest) (*LabelNamespaceResponse, error)
	// Expected a CREATED status on success.
	CreateServiceAccount(context.Context, *CreateServiceAccountRequest) (*CreateServiceAccountResponse, error)
	// Expected a DELETED status on success.
	DeleteServiceAccount(context.Context, *DeleteServiceAccountRequest) (*DeleteServiceAccountResponse, error)
	GetServiceAccount(context.Context, *GetServiceAccountRequest) (*GetServiceAccountResponse, error)
	AnnotateServiceAccount(context.Context, *AnnotateServiceAccountRequest) (*AnnotateServiceAccountResponse, error)
	// Expected a CREATED status on success.
	CreateConfigMap(context.Context, *CreateConfigMapRequest) (*CreateConfigMapResponse, error)
	// Expected a DELETED status on success.
	DeleteConfigMap(context.Context, *DeleteConfigMapRequest) (*DeleteConfigMapResponse, error)
	GetConfigMap(context.Context, *GetConfigMapRequest) (*GetConfigMapResponse, error)
	// UpdateConfigMap generats a JSON merge patch that converts `from` to `to`
	// and apply the generated patch to the runtime object.
	UpdateConfigMap(context.Context, *UpdateConfigMapRequest) (*UpdateConfigMapResponse, error)
	// Expected a CREATED status on success.
	CreateSecret(context.Context, *CreateSecretRequest) (*CreateSecretResponse, error)
	// Expected a DELETED status on success.
	DeleteSecret(context.Context, *DeleteSecretRequest) (*DeleteSecretResponse, error)
	GetSecret(context.Context, *GetSecretRequest) (*GetSecretResponse, error)
	UpdateSecret(context.Context, *UpdateSecretRequest) (*UpdateSecretResponse, error)
	// Expected a SCHEDULED status on success.
	CreateRisingWave(context.Context, *CreateRisingWaveRequest) (*CreateRisingWaveResponse, error)
	// Expected a SCHEDULED status on success.
	DeleteRisingWave(context.Context, *DeleteRisingWaveRequest) (*DeleteRisingWaveResponse, error)
	GetRisingWave(context.Context, *GetRisingWaveRequest) (*GetRisingWaveResponse, error)
	// UpdateRisingWave generats a JSON merge patch that converts `from` to `to`
	// and apply the generated patch to the runtime RW object.
	UpdateRisingWaveImage(context.Context, *UpdateRisingWaveImageRequest) (*UpdateRisingWaveImageResponse, error)
	UpdateRisingWaveLicenseKey(context.Context, *UpdateRisingWaveLicenseKeyRequest) (*UpdateRisingWaveLicenseKeyResponse, error)
	UpdateRisingWaveSecretStore(context.Context, *UpdateRisingWaveSecretStoreRequest) (*UpdateRisingWaveSecretStoreResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use ScaleRisingWaveOneOf
	ScaleRisingWave(context.Context, *ScaleRisingWaveRequest) (*ScaleRisingWaveResponse, error)
	// Scale the replica of `default` node group
	ScaleRisingWaveOneOf(context.Context, *ScaleRisingWaveRequestOneOf) (*ScaleRisingWaveResponse, error)
	// StartRisingWave scale all node groups to previous replica
	StartRisingWave(context.Context, *StartRisingWaveRequest) (*StartRisingWaveResponse, error)
	// StopRisingWave scale all node groups to 0, and record the current replica
	StopRisingWave(context.Context, *StopRisingWaveRequest) (*StopRisingWaveResponse, error)
	// Expected a SCHEDULED status on success.
	UpdateRisingWaveComponents(context.Context, *UpdateRisingWaveComponentsRequest) (*UpdateRisingWaveComponentsResponse, error)
	// Expected a SCHEDULED status on success.
	UpdateRisingWaveMetaStore(context.Context, *UpdateRisingWaveMetaStoreRequest) (*UpdateRisingWaveMetaStoreResponse, error)
	// Add one or more environment variable to zero or more components.
	PutRisingWaveEnvVar(context.Context, *PutRisingWaveEnvRequest) (*PutRisingWaveEnvResponse, error)
	// Removes one or more environment variable from zero or more components.
	DeleteRisingWaveEnvVar(context.Context, *DeleteRisingWaveEnvRequest) (*DeleteRisingWaveEnvResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use CreateRisingWaveNodeGroup.
	CreateRisingWaveComputeNodeGroup(context.Context, *CreateRisingWaveComputeNodeGroupRequest) (*CreateRisingWaveComputeNodeGroupResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use UpdateRisingWaveNodeGroup.
	UpdateRisingWaveComputeNodeGroup(context.Context, *UpdateRisingWaveComputeNodeGroupRequest) (*UpdateRisingWaveComputeNodeGroupResponse, error)
	// Deprecated: Do not use.
	// Deprecated. Please use DeleteRisingWaveNodeGroup.
	DeleteRisingWaveComputeNodeGroup(context.Context, *DeleteRisingWaveComputeNodeGroupRequest) (*DeleteRisingWaveComputeNodeGroupResponse, error)
	// Expected a CREATED status on success.
	CreateRisingWaveNodeGroup(context.Context, *CreateRisingWaveNodeGroupRequest) (*CreateRisingWaveNodeGroupResponse, error)
	// Expected a UPDATED status on success.
	UpdateRisingWaveNodeGroup(context.Context, *UpdateRisingWaveNodeGroupRequest) (*UpdateRisingWaveNodeGroupResponse, error)
	// Expected a DELETED status on success.
	DeleteRisingWaveNodeGroup(context.Context, *DeleteRisingWaveNodeGroupRequest) (*DeleteRisingWaveNodeGroupResponse, error)
	// Expected a UPDATED status on success.
	UpdateRisingWaveNodeGroupConfiguration(context.Context, *UpdateRisingWaveNodeGroupConfigurationRequest) (*UpdateRisingWaveNodeGroupConfigurationResponse, error)
	// Expected a UPDATED status on success. Setting any value for this field will immediately trigger nodes restart.
	UpdateRisingWaveNodeGroupRestartAt(context.Context, *UpdateRisingWaveNodeGroupRestartAtRequest) (*UpdateRisingWaveNodeGroupRestartAtResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePersistentVolumeClaims(context.Context, *DeletePersistentVolumeClaimsRequest) (*DeletePersistentVolumeClaimsResponse, error)
	GetPersistentVolumeClaims(context.Context, *GetPersistentVolumeClaimsRequest) (*GetPersistentVolumeClaimsResponse, error)
	CreatePersistentVolumeClaim(context.Context, *CreatePersistentVolumeClaimRequest) (*CreatePersistentVolumeClaimResponse, error)
	GetHelmRelease(context.Context, *GetHelmReleaseRequest) (*GetHelmReleaseResponse, error)
	InstallHelmRelease(context.Context, *InstallHelmReleaseRequest) (*InstallHelmReleaseResponse, error)
	UpgradeHelmRelease(context.Context, *UpgradeHelmReleaseRequest) (*UpgradeHelmReleaseResponse, error)
	UninstallHelmRelease(context.Context, *UninstallHelmReleaseRequest) (*UninstallHelmReleaseResponse, error)
	// GetPodPhases retrieves all pods' phases in a namespace. The resource id
	// will be a k8s namesapce and the resource namespace is omitted.
	// Returns a map from pod name to pod phase.
	GetPodPhases(context.Context, *GetPodPhasesRequest) (*GetPodPhasesResponse, error)
	// Expected a READY status on success.
	RestartStatefulSet(context.Context, *RestartStatefulSetRequest) (*RestartStatefulSetResponse, error)
	GetStatefulSetReplicasStatus(context.Context, *GetStatefulSetReplicasStatusRequest) (*GetStatefulSetReplicasStatusResponse, error)
	GetDeploymentReplicasStatus(context.Context, *GetDeploymentReplicasStatusRequest) (*GetDeploymentReplicasStatusResponse, error)
	RestartDeployment(context.Context, *RestartDeploymentRequest) (*RestartDeploymentResponse, error)
	// Expected a CREATED status on success.
	CreateServiceMonitor(context.Context, *CreateServiceMonitorRequest) (*CreateServiceMonitorResponse, error)
	// Expect a DELETED status on success
	DeleteServiceMonitor(context.Context, *DeleteServiceMonitorRequest) (*DeleteServiceMonitorResponse, error)
	// Expected a CREATED status on success.
	CreateAzureServiceMonitor(context.Context, *CreateServiceMonitorRequest) (*CreateServiceMonitorResponse, error)
	// Expect a DELETED status on success
	DeleteAzureServiceMonitor(context.Context, *DeleteServiceMonitorRequest) (*DeleteServiceMonitorResponse, error)
	// Expected a CREATED status on success.
	CreatePodMonitoring(context.Context, *CreatePodMonitoringRequest) (*CreatePodMonitoringResponse, error)
	// Expect a DELETED status on success
	DeletePodMonitoring(context.Context, *DeletePodMonitoringRequest) (*DeletePodMonitoringResponse, error)
	// Expected a SCHEDULED status on success.
	CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error)
	// Expect a CREATED status on success
	CreateNetworkPolicy(context.Context, *CreateNetworkPolicyRequest) (*CreateNetworkPolicyResponse, error)
	CreateOrUpdateNetworkPolicy(context.Context, *CreateOrUpdateNetworkPolicyRequest) (*CreateOrUpdateNetworkPolicyResponse, error)
	// Expect a DELETED status on success
	DeleteNetworkPolicy(context.Context, *DeleteNetworkPolicyRequest) (*DeleteNetworkPolicyResponse, error)
	// Expected a SCHEDULED status on success.
	CreatePostgreSql(context.Context, *CreatePostgreSqlRequest) (*CreatePostgreSqlResponse, error)
	// Expected a SCHEDULED status on success.
	DeletePostgreSql(context.Context, *DeletePostgreSqlRequest) (*DeletePostgreSqlResponse, error)
	// Expected a SCHEDULED status on success.
	UpdatePostgreSql(context.Context, *UpdatePostgreSqlRequest) (*UpdatePostgreSqlResponse, error)
	GetPostgreSql(context.Context, *GetPostgreSqlRequest) (*GetPostgreSqlResponse, error)
	// GetPods retrieves all pods in a namespace. The resource id
	// will be a k8s namesapce and the resource namespace is omitted.
	GetPods(context.Context, *GetPodsRequest) (*GetPodsResponse, error)
	GetClusterAccess(context.Context, *GetClusterAccessRequest) (*GetClusterAccessResponse, error)
	mustEmbedUnimplementedK8SResourceManagerServer()
}

// UnimplementedK8SResourceManagerServer must be embedded to have
// forward compatible implementations.
//
// NOTE: this should be embedded by value instead of pointer to avoid a nil
// pointer dereference when methods are called.
type UnimplementedK8SResourceManagerServer struct{}

func (UnimplementedK8SResourceManagerServer) CreateNamespace(context.Context, *CreateNamespaceRequest) (*CreateNamespaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNamespace not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteNamespace(context.Context, *DeleteNamespaceRequest) (*DeleteNamespaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNamespace not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetNamespace(context.Context, *GetNamespaceRequest) (*GetNamespaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNamespace not implemented")
}
func (UnimplementedK8SResourceManagerServer) LabelNamespace(context.Context, *LabelNamespaceRequest) (*LabelNamespaceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method LabelNamespace not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateServiceAccount(context.Context, *CreateServiceAccountRequest) (*CreateServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceAccount not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteServiceAccount(context.Context, *DeleteServiceAccountRequest) (*DeleteServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceAccount not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetServiceAccount(context.Context, *GetServiceAccountRequest) (*GetServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetServiceAccount not implemented")
}
func (UnimplementedK8SResourceManagerServer) AnnotateServiceAccount(context.Context, *AnnotateServiceAccountRequest) (*AnnotateServiceAccountResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method AnnotateServiceAccount not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateConfigMap(context.Context, *CreateConfigMapRequest) (*CreateConfigMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateConfigMap not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteConfigMap(context.Context, *DeleteConfigMapRequest) (*DeleteConfigMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteConfigMap not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetConfigMap(context.Context, *GetConfigMapRequest) (*GetConfigMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetConfigMap not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateConfigMap(context.Context, *UpdateConfigMapRequest) (*UpdateConfigMapResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateConfigMap not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateSecret(context.Context, *CreateSecretRequest) (*CreateSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSecret not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteSecret(context.Context, *DeleteSecretRequest) (*DeleteSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSecret not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetSecret(context.Context, *GetSecretRequest) (*GetSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSecret not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateSecret(context.Context, *UpdateSecretRequest) (*UpdateSecretResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSecret not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateRisingWave(context.Context, *CreateRisingWaveRequest) (*CreateRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRisingWave not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteRisingWave(context.Context, *DeleteRisingWaveRequest) (*DeleteRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRisingWave not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetRisingWave(context.Context, *GetRisingWaveRequest) (*GetRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetRisingWave not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveImage(context.Context, *UpdateRisingWaveImageRequest) (*UpdateRisingWaveImageResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveImage not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveLicenseKey(context.Context, *UpdateRisingWaveLicenseKeyRequest) (*UpdateRisingWaveLicenseKeyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveLicenseKey not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveSecretStore(context.Context, *UpdateRisingWaveSecretStoreRequest) (*UpdateRisingWaveSecretStoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveSecretStore not implemented")
}
func (UnimplementedK8SResourceManagerServer) ScaleRisingWave(context.Context, *ScaleRisingWaveRequest) (*ScaleRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScaleRisingWave not implemented")
}
func (UnimplementedK8SResourceManagerServer) ScaleRisingWaveOneOf(context.Context, *ScaleRisingWaveRequestOneOf) (*ScaleRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ScaleRisingWaveOneOf not implemented")
}
func (UnimplementedK8SResourceManagerServer) StartRisingWave(context.Context, *StartRisingWaveRequest) (*StartRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartRisingWave not implemented")
}
func (UnimplementedK8SResourceManagerServer) StopRisingWave(context.Context, *StopRisingWaveRequest) (*StopRisingWaveResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopRisingWave not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveComponents(context.Context, *UpdateRisingWaveComponentsRequest) (*UpdateRisingWaveComponentsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveComponents not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveMetaStore(context.Context, *UpdateRisingWaveMetaStoreRequest) (*UpdateRisingWaveMetaStoreResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveMetaStore not implemented")
}
func (UnimplementedK8SResourceManagerServer) PutRisingWaveEnvVar(context.Context, *PutRisingWaveEnvRequest) (*PutRisingWaveEnvResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method PutRisingWaveEnvVar not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteRisingWaveEnvVar(context.Context, *DeleteRisingWaveEnvRequest) (*DeleteRisingWaveEnvResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRisingWaveEnvVar not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateRisingWaveComputeNodeGroup(context.Context, *CreateRisingWaveComputeNodeGroupRequest) (*CreateRisingWaveComputeNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRisingWaveComputeNodeGroup not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveComputeNodeGroup(context.Context, *UpdateRisingWaveComputeNodeGroupRequest) (*UpdateRisingWaveComputeNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveComputeNodeGroup not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteRisingWaveComputeNodeGroup(context.Context, *DeleteRisingWaveComputeNodeGroupRequest) (*DeleteRisingWaveComputeNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRisingWaveComputeNodeGroup not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateRisingWaveNodeGroup(context.Context, *CreateRisingWaveNodeGroupRequest) (*CreateRisingWaveNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateRisingWaveNodeGroup not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveNodeGroup(context.Context, *UpdateRisingWaveNodeGroupRequest) (*UpdateRisingWaveNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveNodeGroup not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteRisingWaveNodeGroup(context.Context, *DeleteRisingWaveNodeGroupRequest) (*DeleteRisingWaveNodeGroupResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteRisingWaveNodeGroup not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveNodeGroupConfiguration(context.Context, *UpdateRisingWaveNodeGroupConfigurationRequest) (*UpdateRisingWaveNodeGroupConfigurationResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveNodeGroupConfiguration not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdateRisingWaveNodeGroupRestartAt(context.Context, *UpdateRisingWaveNodeGroupRestartAtRequest) (*UpdateRisingWaveNodeGroupRestartAtResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateRisingWaveNodeGroupRestartAt not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeletePersistentVolumeClaims(context.Context, *DeletePersistentVolumeClaimsRequest) (*DeletePersistentVolumeClaimsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePersistentVolumeClaims not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetPersistentVolumeClaims(context.Context, *GetPersistentVolumeClaimsRequest) (*GetPersistentVolumeClaimsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPersistentVolumeClaims not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreatePersistentVolumeClaim(context.Context, *CreatePersistentVolumeClaimRequest) (*CreatePersistentVolumeClaimResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePersistentVolumeClaim not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetHelmRelease(context.Context, *GetHelmReleaseRequest) (*GetHelmReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetHelmRelease not implemented")
}
func (UnimplementedK8SResourceManagerServer) InstallHelmRelease(context.Context, *InstallHelmReleaseRequest) (*InstallHelmReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method InstallHelmRelease not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpgradeHelmRelease(context.Context, *UpgradeHelmReleaseRequest) (*UpgradeHelmReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpgradeHelmRelease not implemented")
}
func (UnimplementedK8SResourceManagerServer) UninstallHelmRelease(context.Context, *UninstallHelmReleaseRequest) (*UninstallHelmReleaseResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UninstallHelmRelease not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetPodPhases(context.Context, *GetPodPhasesRequest) (*GetPodPhasesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPodPhases not implemented")
}
func (UnimplementedK8SResourceManagerServer) RestartStatefulSet(context.Context, *RestartStatefulSetRequest) (*RestartStatefulSetResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestartStatefulSet not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetStatefulSetReplicasStatus(context.Context, *GetStatefulSetReplicasStatusRequest) (*GetStatefulSetReplicasStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetStatefulSetReplicasStatus not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetDeploymentReplicasStatus(context.Context, *GetDeploymentReplicasStatusRequest) (*GetDeploymentReplicasStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDeploymentReplicasStatus not implemented")
}
func (UnimplementedK8SResourceManagerServer) RestartDeployment(context.Context, *RestartDeploymentRequest) (*RestartDeploymentResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RestartDeployment not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateServiceMonitor(context.Context, *CreateServiceMonitorRequest) (*CreateServiceMonitorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateServiceMonitor not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteServiceMonitor(context.Context, *DeleteServiceMonitorRequest) (*DeleteServiceMonitorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteServiceMonitor not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateAzureServiceMonitor(context.Context, *CreateServiceMonitorRequest) (*CreateServiceMonitorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateAzureServiceMonitor not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteAzureServiceMonitor(context.Context, *DeleteServiceMonitorRequest) (*DeleteServiceMonitorResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteAzureServiceMonitor not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreatePodMonitoring(context.Context, *CreatePodMonitoringRequest) (*CreatePodMonitoringResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePodMonitoring not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeletePodMonitoring(context.Context, *DeletePodMonitoringRequest) (*DeletePodMonitoringResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePodMonitoring not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateService(context.Context, *CreateServiceRequest) (*CreateServiceResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateService not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateNetworkPolicy(context.Context, *CreateNetworkPolicyRequest) (*CreateNetworkPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNetworkPolicy not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreateOrUpdateNetworkPolicy(context.Context, *CreateOrUpdateNetworkPolicyRequest) (*CreateOrUpdateNetworkPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateOrUpdateNetworkPolicy not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeleteNetworkPolicy(context.Context, *DeleteNetworkPolicyRequest) (*DeleteNetworkPolicyResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNetworkPolicy not implemented")
}
func (UnimplementedK8SResourceManagerServer) CreatePostgreSql(context.Context, *CreatePostgreSqlRequest) (*CreatePostgreSqlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreatePostgreSql not implemented")
}
func (UnimplementedK8SResourceManagerServer) DeletePostgreSql(context.Context, *DeletePostgreSqlRequest) (*DeletePostgreSqlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeletePostgreSql not implemented")
}
func (UnimplementedK8SResourceManagerServer) UpdatePostgreSql(context.Context, *UpdatePostgreSqlRequest) (*UpdatePostgreSqlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdatePostgreSql not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetPostgreSql(context.Context, *GetPostgreSqlRequest) (*GetPostgreSqlResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPostgreSql not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetPods(context.Context, *GetPodsRequest) (*GetPodsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetPods not implemented")
}
func (UnimplementedK8SResourceManagerServer) GetClusterAccess(context.Context, *GetClusterAccessRequest) (*GetClusterAccessResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetClusterAccess not implemented")
}
func (UnimplementedK8SResourceManagerServer) mustEmbedUnimplementedK8SResourceManagerServer() {}
func (UnimplementedK8SResourceManagerServer) testEmbeddedByValue()                            {}

// UnsafeK8SResourceManagerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to K8SResourceManagerServer will
// result in compilation errors.
type UnsafeK8SResourceManagerServer interface {
	mustEmbedUnimplementedK8SResourceManagerServer()
}

func RegisterK8SResourceManagerServer(s grpc.ServiceRegistrar, srv K8SResourceManagerServer) {
	// If the following call pancis, it indicates UnimplementedK8SResourceManagerServer was
	// embedded by pointer and is nil.  This will cause panics if an
	// unimplemented method is ever invoked, so we test this at initialization
	// time to prevent it from happening at runtime later due to I/O.
	if t, ok := srv.(interface{ testEmbeddedByValue() }); ok {
		t.testEmbeddedByValue()
	}
	s.RegisterService(&K8SResourceManager_ServiceDesc, srv)
}

func _K8SResourceManager_CreateNamespace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNamespaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateNamespace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateNamespace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateNamespace(ctx, req.(*CreateNamespaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteNamespace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNamespaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteNamespace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteNamespace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteNamespace(ctx, req.(*DeleteNamespaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetNamespace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNamespaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetNamespace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetNamespace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetNamespace(ctx, req.(*GetNamespaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_LabelNamespace_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(LabelNamespaceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).LabelNamespace(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_LabelNamespace_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).LabelNamespace(ctx, req.(*LabelNamespaceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateServiceAccount(ctx, req.(*CreateServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteServiceAccount(ctx, req.(*DeleteServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetServiceAccount(ctx, req.(*GetServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_AnnotateServiceAccount_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(AnnotateServiceAccountRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).AnnotateServiceAccount(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_AnnotateServiceAccount_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).AnnotateServiceAccount(ctx, req.(*AnnotateServiceAccountRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateConfigMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateConfigMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateConfigMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateConfigMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateConfigMap(ctx, req.(*CreateConfigMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteConfigMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteConfigMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteConfigMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteConfigMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteConfigMap(ctx, req.(*DeleteConfigMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetConfigMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetConfigMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetConfigMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetConfigMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetConfigMap(ctx, req.(*GetConfigMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateConfigMap_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateConfigMapRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateConfigMap(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateConfigMap_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateConfigMap(ctx, req.(*UpdateConfigMapRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateSecret(ctx, req.(*CreateSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteSecret(ctx, req.(*DeleteSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetSecret(ctx, req.(*GetSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateSecret_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSecretRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateSecret(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateSecret_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateSecret(ctx, req.(*UpdateSecretRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateRisingWave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRisingWaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateRisingWave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateRisingWave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateRisingWave(ctx, req.(*CreateRisingWaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteRisingWave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRisingWaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteRisingWave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteRisingWave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteRisingWave(ctx, req.(*DeleteRisingWaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetRisingWave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetRisingWaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetRisingWave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetRisingWave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetRisingWave(ctx, req.(*GetRisingWaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveImage_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveImageRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveImage(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveImage_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveImage(ctx, req.(*UpdateRisingWaveImageRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveLicenseKey_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveLicenseKeyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveLicenseKey(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveLicenseKey_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveLicenseKey(ctx, req.(*UpdateRisingWaveLicenseKeyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveSecretStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveSecretStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveSecretStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveSecretStore_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveSecretStore(ctx, req.(*UpdateRisingWaveSecretStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_ScaleRisingWave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScaleRisingWaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).ScaleRisingWave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_ScaleRisingWave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).ScaleRisingWave(ctx, req.(*ScaleRisingWaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_ScaleRisingWaveOneOf_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ScaleRisingWaveRequestOneOf)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).ScaleRisingWaveOneOf(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_ScaleRisingWaveOneOf_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).ScaleRisingWaveOneOf(ctx, req.(*ScaleRisingWaveRequestOneOf))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_StartRisingWave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartRisingWaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).StartRisingWave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_StartRisingWave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).StartRisingWave(ctx, req.(*StartRisingWaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_StopRisingWave_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopRisingWaveRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).StopRisingWave(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_StopRisingWave_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).StopRisingWave(ctx, req.(*StopRisingWaveRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveComponents_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveComponentsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveComponents(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveComponents_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveComponents(ctx, req.(*UpdateRisingWaveComponentsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveMetaStore_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveMetaStoreRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveMetaStore(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveMetaStore_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveMetaStore(ctx, req.(*UpdateRisingWaveMetaStoreRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_PutRisingWaveEnvVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(PutRisingWaveEnvRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).PutRisingWaveEnvVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_PutRisingWaveEnvVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).PutRisingWaveEnvVar(ctx, req.(*PutRisingWaveEnvRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteRisingWaveEnvVar_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRisingWaveEnvRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteRisingWaveEnvVar(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteRisingWaveEnvVar_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteRisingWaveEnvVar(ctx, req.(*DeleteRisingWaveEnvRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateRisingWaveComputeNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRisingWaveComputeNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateRisingWaveComputeNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateRisingWaveComputeNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateRisingWaveComputeNodeGroup(ctx, req.(*CreateRisingWaveComputeNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveComputeNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveComputeNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveComputeNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveComputeNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveComputeNodeGroup(ctx, req.(*UpdateRisingWaveComputeNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteRisingWaveComputeNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRisingWaveComputeNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteRisingWaveComputeNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteRisingWaveComputeNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteRisingWaveComputeNodeGroup(ctx, req.(*DeleteRisingWaveComputeNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateRisingWaveNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateRisingWaveNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateRisingWaveNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateRisingWaveNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateRisingWaveNodeGroup(ctx, req.(*CreateRisingWaveNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveNodeGroup(ctx, req.(*UpdateRisingWaveNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteRisingWaveNodeGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteRisingWaveNodeGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteRisingWaveNodeGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteRisingWaveNodeGroup_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteRisingWaveNodeGroup(ctx, req.(*DeleteRisingWaveNodeGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveNodeGroupConfiguration_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveNodeGroupConfigurationRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveNodeGroupConfiguration(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveNodeGroupConfiguration_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveNodeGroupConfiguration(ctx, req.(*UpdateRisingWaveNodeGroupConfigurationRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdateRisingWaveNodeGroupRestartAt_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateRisingWaveNodeGroupRestartAtRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveNodeGroupRestartAt(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdateRisingWaveNodeGroupRestartAt_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdateRisingWaveNodeGroupRestartAt(ctx, req.(*UpdateRisingWaveNodeGroupRestartAtRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeletePersistentVolumeClaims_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePersistentVolumeClaimsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeletePersistentVolumeClaims(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeletePersistentVolumeClaims_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeletePersistentVolumeClaims(ctx, req.(*DeletePersistentVolumeClaimsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetPersistentVolumeClaims_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPersistentVolumeClaimsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetPersistentVolumeClaims(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetPersistentVolumeClaims_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetPersistentVolumeClaims(ctx, req.(*GetPersistentVolumeClaimsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreatePersistentVolumeClaim_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePersistentVolumeClaimRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreatePersistentVolumeClaim(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreatePersistentVolumeClaim_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreatePersistentVolumeClaim(ctx, req.(*CreatePersistentVolumeClaimRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetHelmRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetHelmReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetHelmRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetHelmRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetHelmRelease(ctx, req.(*GetHelmReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_InstallHelmRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(InstallHelmReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).InstallHelmRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_InstallHelmRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).InstallHelmRelease(ctx, req.(*InstallHelmReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpgradeHelmRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpgradeHelmReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpgradeHelmRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpgradeHelmRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpgradeHelmRelease(ctx, req.(*UpgradeHelmReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UninstallHelmRelease_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UninstallHelmReleaseRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UninstallHelmRelease(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UninstallHelmRelease_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UninstallHelmRelease(ctx, req.(*UninstallHelmReleaseRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetPodPhases_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPodPhasesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetPodPhases(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetPodPhases_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetPodPhases(ctx, req.(*GetPodPhasesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_RestartStatefulSet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestartStatefulSetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).RestartStatefulSet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_RestartStatefulSet_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).RestartStatefulSet(ctx, req.(*RestartStatefulSetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetStatefulSetReplicasStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetStatefulSetReplicasStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetStatefulSetReplicasStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetStatefulSetReplicasStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetStatefulSetReplicasStatus(ctx, req.(*GetStatefulSetReplicasStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetDeploymentReplicasStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDeploymentReplicasStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetDeploymentReplicasStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetDeploymentReplicasStatus_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetDeploymentReplicasStatus(ctx, req.(*GetDeploymentReplicasStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_RestartDeployment_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RestartDeploymentRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).RestartDeployment(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_RestartDeployment_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).RestartDeployment(ctx, req.(*RestartDeploymentRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateServiceMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceMonitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateServiceMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateServiceMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateServiceMonitor(ctx, req.(*CreateServiceMonitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteServiceMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceMonitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteServiceMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteServiceMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteServiceMonitor(ctx, req.(*DeleteServiceMonitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateAzureServiceMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceMonitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateAzureServiceMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateAzureServiceMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateAzureServiceMonitor(ctx, req.(*CreateServiceMonitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteAzureServiceMonitor_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteServiceMonitorRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteAzureServiceMonitor(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteAzureServiceMonitor_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteAzureServiceMonitor(ctx, req.(*DeleteServiceMonitorRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreatePodMonitoring_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePodMonitoringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreatePodMonitoring(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreatePodMonitoring_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreatePodMonitoring(ctx, req.(*CreatePodMonitoringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeletePodMonitoring_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePodMonitoringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeletePodMonitoring(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeletePodMonitoring_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeletePodMonitoring(ctx, req.(*DeletePodMonitoringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateService_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateServiceRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateService(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateService_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateService(ctx, req.(*CreateServiceRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateNetworkPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNetworkPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateNetworkPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateNetworkPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateNetworkPolicy(ctx, req.(*CreateNetworkPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreateOrUpdateNetworkPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateOrUpdateNetworkPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreateOrUpdateNetworkPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreateOrUpdateNetworkPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreateOrUpdateNetworkPolicy(ctx, req.(*CreateOrUpdateNetworkPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeleteNetworkPolicy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNetworkPolicyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeleteNetworkPolicy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeleteNetworkPolicy_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeleteNetworkPolicy(ctx, req.(*DeleteNetworkPolicyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_CreatePostgreSql_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreatePostgreSqlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).CreatePostgreSql(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_CreatePostgreSql_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).CreatePostgreSql(ctx, req.(*CreatePostgreSqlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_DeletePostgreSql_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeletePostgreSqlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).DeletePostgreSql(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_DeletePostgreSql_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).DeletePostgreSql(ctx, req.(*DeletePostgreSqlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_UpdatePostgreSql_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdatePostgreSqlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).UpdatePostgreSql(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_UpdatePostgreSql_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).UpdatePostgreSql(ctx, req.(*UpdatePostgreSqlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetPostgreSql_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPostgreSqlRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetPostgreSql(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetPostgreSql_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetPostgreSql(ctx, req.(*GetPostgreSqlRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetPods_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetPodsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetPods(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetPods_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetPods(ctx, req.(*GetPodsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _K8SResourceManager_GetClusterAccess_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetClusterAccessRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(K8SResourceManagerServer).GetClusterAccess(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: K8SResourceManager_GetClusterAccess_FullMethodName,
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(K8SResourceManagerServer).GetClusterAccess(ctx, req.(*GetClusterAccessRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// K8SResourceManager_ServiceDesc is the grpc.ServiceDesc for K8SResourceManager service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var K8SResourceManager_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "services.k8s.K8sResourceManager",
	HandlerType: (*K8SResourceManagerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateNamespace",
			Handler:    _K8SResourceManager_CreateNamespace_Handler,
		},
		{
			MethodName: "DeleteNamespace",
			Handler:    _K8SResourceManager_DeleteNamespace_Handler,
		},
		{
			MethodName: "GetNamespace",
			Handler:    _K8SResourceManager_GetNamespace_Handler,
		},
		{
			MethodName: "LabelNamespace",
			Handler:    _K8SResourceManager_LabelNamespace_Handler,
		},
		{
			MethodName: "CreateServiceAccount",
			Handler:    _K8SResourceManager_CreateServiceAccount_Handler,
		},
		{
			MethodName: "DeleteServiceAccount",
			Handler:    _K8SResourceManager_DeleteServiceAccount_Handler,
		},
		{
			MethodName: "GetServiceAccount",
			Handler:    _K8SResourceManager_GetServiceAccount_Handler,
		},
		{
			MethodName: "AnnotateServiceAccount",
			Handler:    _K8SResourceManager_AnnotateServiceAccount_Handler,
		},
		{
			MethodName: "CreateConfigMap",
			Handler:    _K8SResourceManager_CreateConfigMap_Handler,
		},
		{
			MethodName: "DeleteConfigMap",
			Handler:    _K8SResourceManager_DeleteConfigMap_Handler,
		},
		{
			MethodName: "GetConfigMap",
			Handler:    _K8SResourceManager_GetConfigMap_Handler,
		},
		{
			MethodName: "UpdateConfigMap",
			Handler:    _K8SResourceManager_UpdateConfigMap_Handler,
		},
		{
			MethodName: "CreateSecret",
			Handler:    _K8SResourceManager_CreateSecret_Handler,
		},
		{
			MethodName: "DeleteSecret",
			Handler:    _K8SResourceManager_DeleteSecret_Handler,
		},
		{
			MethodName: "GetSecret",
			Handler:    _K8SResourceManager_GetSecret_Handler,
		},
		{
			MethodName: "UpdateSecret",
			Handler:    _K8SResourceManager_UpdateSecret_Handler,
		},
		{
			MethodName: "CreateRisingWave",
			Handler:    _K8SResourceManager_CreateRisingWave_Handler,
		},
		{
			MethodName: "DeleteRisingWave",
			Handler:    _K8SResourceManager_DeleteRisingWave_Handler,
		},
		{
			MethodName: "GetRisingWave",
			Handler:    _K8SResourceManager_GetRisingWave_Handler,
		},
		{
			MethodName: "UpdateRisingWaveImage",
			Handler:    _K8SResourceManager_UpdateRisingWaveImage_Handler,
		},
		{
			MethodName: "UpdateRisingWaveLicenseKey",
			Handler:    _K8SResourceManager_UpdateRisingWaveLicenseKey_Handler,
		},
		{
			MethodName: "UpdateRisingWaveSecretStore",
			Handler:    _K8SResourceManager_UpdateRisingWaveSecretStore_Handler,
		},
		{
			MethodName: "ScaleRisingWave",
			Handler:    _K8SResourceManager_ScaleRisingWave_Handler,
		},
		{
			MethodName: "ScaleRisingWaveOneOf",
			Handler:    _K8SResourceManager_ScaleRisingWaveOneOf_Handler,
		},
		{
			MethodName: "StartRisingWave",
			Handler:    _K8SResourceManager_StartRisingWave_Handler,
		},
		{
			MethodName: "StopRisingWave",
			Handler:    _K8SResourceManager_StopRisingWave_Handler,
		},
		{
			MethodName: "UpdateRisingWaveComponents",
			Handler:    _K8SResourceManager_UpdateRisingWaveComponents_Handler,
		},
		{
			MethodName: "UpdateRisingWaveMetaStore",
			Handler:    _K8SResourceManager_UpdateRisingWaveMetaStore_Handler,
		},
		{
			MethodName: "PutRisingWaveEnvVar",
			Handler:    _K8SResourceManager_PutRisingWaveEnvVar_Handler,
		},
		{
			MethodName: "DeleteRisingWaveEnvVar",
			Handler:    _K8SResourceManager_DeleteRisingWaveEnvVar_Handler,
		},
		{
			MethodName: "CreateRisingWaveComputeNodeGroup",
			Handler:    _K8SResourceManager_CreateRisingWaveComputeNodeGroup_Handler,
		},
		{
			MethodName: "UpdateRisingWaveComputeNodeGroup",
			Handler:    _K8SResourceManager_UpdateRisingWaveComputeNodeGroup_Handler,
		},
		{
			MethodName: "DeleteRisingWaveComputeNodeGroup",
			Handler:    _K8SResourceManager_DeleteRisingWaveComputeNodeGroup_Handler,
		},
		{
			MethodName: "CreateRisingWaveNodeGroup",
			Handler:    _K8SResourceManager_CreateRisingWaveNodeGroup_Handler,
		},
		{
			MethodName: "UpdateRisingWaveNodeGroup",
			Handler:    _K8SResourceManager_UpdateRisingWaveNodeGroup_Handler,
		},
		{
			MethodName: "DeleteRisingWaveNodeGroup",
			Handler:    _K8SResourceManager_DeleteRisingWaveNodeGroup_Handler,
		},
		{
			MethodName: "UpdateRisingWaveNodeGroupConfiguration",
			Handler:    _K8SResourceManager_UpdateRisingWaveNodeGroupConfiguration_Handler,
		},
		{
			MethodName: "UpdateRisingWaveNodeGroupRestartAt",
			Handler:    _K8SResourceManager_UpdateRisingWaveNodeGroupRestartAt_Handler,
		},
		{
			MethodName: "DeletePersistentVolumeClaims",
			Handler:    _K8SResourceManager_DeletePersistentVolumeClaims_Handler,
		},
		{
			MethodName: "GetPersistentVolumeClaims",
			Handler:    _K8SResourceManager_GetPersistentVolumeClaims_Handler,
		},
		{
			MethodName: "CreatePersistentVolumeClaim",
			Handler:    _K8SResourceManager_CreatePersistentVolumeClaim_Handler,
		},
		{
			MethodName: "GetHelmRelease",
			Handler:    _K8SResourceManager_GetHelmRelease_Handler,
		},
		{
			MethodName: "InstallHelmRelease",
			Handler:    _K8SResourceManager_InstallHelmRelease_Handler,
		},
		{
			MethodName: "UpgradeHelmRelease",
			Handler:    _K8SResourceManager_UpgradeHelmRelease_Handler,
		},
		{
			MethodName: "UninstallHelmRelease",
			Handler:    _K8SResourceManager_UninstallHelmRelease_Handler,
		},
		{
			MethodName: "GetPodPhases",
			Handler:    _K8SResourceManager_GetPodPhases_Handler,
		},
		{
			MethodName: "RestartStatefulSet",
			Handler:    _K8SResourceManager_RestartStatefulSet_Handler,
		},
		{
			MethodName: "GetStatefulSetReplicasStatus",
			Handler:    _K8SResourceManager_GetStatefulSetReplicasStatus_Handler,
		},
		{
			MethodName: "GetDeploymentReplicasStatus",
			Handler:    _K8SResourceManager_GetDeploymentReplicasStatus_Handler,
		},
		{
			MethodName: "RestartDeployment",
			Handler:    _K8SResourceManager_RestartDeployment_Handler,
		},
		{
			MethodName: "CreateServiceMonitor",
			Handler:    _K8SResourceManager_CreateServiceMonitor_Handler,
		},
		{
			MethodName: "DeleteServiceMonitor",
			Handler:    _K8SResourceManager_DeleteServiceMonitor_Handler,
		},
		{
			MethodName: "CreateAzureServiceMonitor",
			Handler:    _K8SResourceManager_CreateAzureServiceMonitor_Handler,
		},
		{
			MethodName: "DeleteAzureServiceMonitor",
			Handler:    _K8SResourceManager_DeleteAzureServiceMonitor_Handler,
		},
		{
			MethodName: "CreatePodMonitoring",
			Handler:    _K8SResourceManager_CreatePodMonitoring_Handler,
		},
		{
			MethodName: "DeletePodMonitoring",
			Handler:    _K8SResourceManager_DeletePodMonitoring_Handler,
		},
		{
			MethodName: "CreateService",
			Handler:    _K8SResourceManager_CreateService_Handler,
		},
		{
			MethodName: "CreateNetworkPolicy",
			Handler:    _K8SResourceManager_CreateNetworkPolicy_Handler,
		},
		{
			MethodName: "CreateOrUpdateNetworkPolicy",
			Handler:    _K8SResourceManager_CreateOrUpdateNetworkPolicy_Handler,
		},
		{
			MethodName: "DeleteNetworkPolicy",
			Handler:    _K8SResourceManager_DeleteNetworkPolicy_Handler,
		},
		{
			MethodName: "CreatePostgreSql",
			Handler:    _K8SResourceManager_CreatePostgreSql_Handler,
		},
		{
			MethodName: "DeletePostgreSql",
			Handler:    _K8SResourceManager_DeletePostgreSql_Handler,
		},
		{
			MethodName: "UpdatePostgreSql",
			Handler:    _K8SResourceManager_UpdatePostgreSql_Handler,
		},
		{
			MethodName: "GetPostgreSql",
			Handler:    _K8SResourceManager_GetPostgreSql_Handler,
		},
		{
			MethodName: "GetPods",
			Handler:    _K8SResourceManager_GetPods_Handler,
		},
		{
			MethodName: "GetClusterAccess",
			Handler:    _K8SResourceManager_GetClusterAccess_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "services/k8s.proto",
}
