// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pbgen/services/k8s (interfaces: K8SResourceManagerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/k8s -package=k8s -destination=pbgen/services/k8s/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/k8s K8SResourceManagerClient
//

// Package k8s is a generated GoMock package.
package k8s

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	grpc "google.golang.org/grpc"
)

// MockK8SResourceManagerClient is a mock of K8SResourceManagerClient interface.
type MockK8SResourceManagerClient struct {
	ctrl     *gomock.Controller
	recorder *MockK8SResourceManagerClientMockRecorder
	isgomock struct{}
}

// MockK8SResourceManagerClientMockRecorder is the mock recorder for MockK8SResourceManagerClient.
type MockK8SResourceManagerClientMockRecorder struct {
	mock *MockK8SResourceManagerClient
}

// NewMockK8SResourceManagerClient creates a new mock instance.
func NewMockK8SResourceManagerClient(ctrl *gomock.Controller) *MockK8SResourceManagerClient {
	mock := &MockK8SResourceManagerClient{ctrl: ctrl}
	mock.recorder = &MockK8SResourceManagerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockK8SResourceManagerClient) EXPECT() *MockK8SResourceManagerClientMockRecorder {
	return m.recorder
}

// AnnotateServiceAccount mocks base method.
func (m *MockK8SResourceManagerClient) AnnotateServiceAccount(ctx context.Context, in *AnnotateServiceAccountRequest, opts ...grpc.CallOption) (*AnnotateServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "AnnotateServiceAccount", varargs...)
	ret0, _ := ret[0].(*AnnotateServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// AnnotateServiceAccount indicates an expected call of AnnotateServiceAccount.
func (mr *MockK8SResourceManagerClientMockRecorder) AnnotateServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnnotateServiceAccount", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).AnnotateServiceAccount), varargs...)
}

// CreateAzureServiceMonitor mocks base method.
func (m *MockK8SResourceManagerClient) CreateAzureServiceMonitor(ctx context.Context, in *CreateServiceMonitorRequest, opts ...grpc.CallOption) (*CreateServiceMonitorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateAzureServiceMonitor", varargs...)
	ret0, _ := ret[0].(*CreateServiceMonitorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateAzureServiceMonitor indicates an expected call of CreateAzureServiceMonitor.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateAzureServiceMonitor(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAzureServiceMonitor", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateAzureServiceMonitor), varargs...)
}

// CreateConfigMap mocks base method.
func (m *MockK8SResourceManagerClient) CreateConfigMap(ctx context.Context, in *CreateConfigMapRequest, opts ...grpc.CallOption) (*CreateConfigMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateConfigMap", varargs...)
	ret0, _ := ret[0].(*CreateConfigMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateConfigMap indicates an expected call of CreateConfigMap.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateConfigMap(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConfigMap", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateConfigMap), varargs...)
}

// CreateNamespace mocks base method.
func (m *MockK8SResourceManagerClient) CreateNamespace(ctx context.Context, in *CreateNamespaceRequest, opts ...grpc.CallOption) (*CreateNamespaceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateNamespace", varargs...)
	ret0, _ := ret[0].(*CreateNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNamespace indicates an expected call of CreateNamespace.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateNamespace(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNamespace", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateNamespace), varargs...)
}

// CreateNetworkPolicy mocks base method.
func (m *MockK8SResourceManagerClient) CreateNetworkPolicy(ctx context.Context, in *CreateNetworkPolicyRequest, opts ...grpc.CallOption) (*CreateNetworkPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateNetworkPolicy", varargs...)
	ret0, _ := ret[0].(*CreateNetworkPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateNetworkPolicy indicates an expected call of CreateNetworkPolicy.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateNetworkPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNetworkPolicy", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateNetworkPolicy), varargs...)
}

// CreateOrUpdateNetworkPolicy mocks base method.
func (m *MockK8SResourceManagerClient) CreateOrUpdateNetworkPolicy(ctx context.Context, in *CreateOrUpdateNetworkPolicyRequest, opts ...grpc.CallOption) (*CreateOrUpdateNetworkPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateOrUpdateNetworkPolicy", varargs...)
	ret0, _ := ret[0].(*CreateOrUpdateNetworkPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateOrUpdateNetworkPolicy indicates an expected call of CreateOrUpdateNetworkPolicy.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateOrUpdateNetworkPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateNetworkPolicy", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateOrUpdateNetworkPolicy), varargs...)
}

// CreatePersistentVolumeClaim mocks base method.
func (m *MockK8SResourceManagerClient) CreatePersistentVolumeClaim(ctx context.Context, in *CreatePersistentVolumeClaimRequest, opts ...grpc.CallOption) (*CreatePersistentVolumeClaimResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePersistentVolumeClaim", varargs...)
	ret0, _ := ret[0].(*CreatePersistentVolumeClaimResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePersistentVolumeClaim indicates an expected call of CreatePersistentVolumeClaim.
func (mr *MockK8SResourceManagerClientMockRecorder) CreatePersistentVolumeClaim(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePersistentVolumeClaim", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreatePersistentVolumeClaim), varargs...)
}

// CreatePodMonitoring mocks base method.
func (m *MockK8SResourceManagerClient) CreatePodMonitoring(ctx context.Context, in *CreatePodMonitoringRequest, opts ...grpc.CallOption) (*CreatePodMonitoringResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePodMonitoring", varargs...)
	ret0, _ := ret[0].(*CreatePodMonitoringResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePodMonitoring indicates an expected call of CreatePodMonitoring.
func (mr *MockK8SResourceManagerClientMockRecorder) CreatePodMonitoring(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePodMonitoring", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreatePodMonitoring), varargs...)
}

// CreatePostgreSql mocks base method.
func (m *MockK8SResourceManagerClient) CreatePostgreSql(ctx context.Context, in *CreatePostgreSqlRequest, opts ...grpc.CallOption) (*CreatePostgreSqlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreatePostgreSql", varargs...)
	ret0, _ := ret[0].(*CreatePostgreSqlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreatePostgreSql indicates an expected call of CreatePostgreSql.
func (mr *MockK8SResourceManagerClientMockRecorder) CreatePostgreSql(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePostgreSql", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreatePostgreSql), varargs...)
}

// CreateRisingWave mocks base method.
func (m *MockK8SResourceManagerClient) CreateRisingWave(ctx context.Context, in *CreateRisingWaveRequest, opts ...grpc.CallOption) (*CreateRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRisingWave", varargs...)
	ret0, _ := ret[0].(*CreateRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRisingWave indicates an expected call of CreateRisingWave.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateRisingWave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRisingWave", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateRisingWave), varargs...)
}

// CreateRisingWaveComputeNodeGroup mocks base method.
func (m *MockK8SResourceManagerClient) CreateRisingWaveComputeNodeGroup(ctx context.Context, in *CreateRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*CreateRisingWaveComputeNodeGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRisingWaveComputeNodeGroup", varargs...)
	ret0, _ := ret[0].(*CreateRisingWaveComputeNodeGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRisingWaveComputeNodeGroup indicates an expected call of CreateRisingWaveComputeNodeGroup.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateRisingWaveComputeNodeGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRisingWaveComputeNodeGroup", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateRisingWaveComputeNodeGroup), varargs...)
}

// CreateRisingWaveNodeGroup mocks base method.
func (m *MockK8SResourceManagerClient) CreateRisingWaveNodeGroup(ctx context.Context, in *CreateRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*CreateRisingWaveNodeGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateRisingWaveNodeGroup", varargs...)
	ret0, _ := ret[0].(*CreateRisingWaveNodeGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateRisingWaveNodeGroup indicates an expected call of CreateRisingWaveNodeGroup.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateRisingWaveNodeGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRisingWaveNodeGroup", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateRisingWaveNodeGroup), varargs...)
}

// CreateSecret mocks base method.
func (m *MockK8SResourceManagerClient) CreateSecret(ctx context.Context, in *CreateSecretRequest, opts ...grpc.CallOption) (*CreateSecretResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateSecret", varargs...)
	ret0, _ := ret[0].(*CreateSecretResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateSecret indicates an expected call of CreateSecret.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateSecret(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecret", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateSecret), varargs...)
}

// CreateService mocks base method.
func (m *MockK8SResourceManagerClient) CreateService(ctx context.Context, in *CreateServiceRequest, opts ...grpc.CallOption) (*CreateServiceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateService", varargs...)
	ret0, _ := ret[0].(*CreateServiceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateService indicates an expected call of CreateService.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateService(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateService", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateService), varargs...)
}

// CreateServiceAccount mocks base method.
func (m *MockK8SResourceManagerClient) CreateServiceAccount(ctx context.Context, in *CreateServiceAccountRequest, opts ...grpc.CallOption) (*CreateServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateServiceAccount", varargs...)
	ret0, _ := ret[0].(*CreateServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServiceAccount indicates an expected call of CreateServiceAccount.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceAccount", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateServiceAccount), varargs...)
}

// CreateServiceMonitor mocks base method.
func (m *MockK8SResourceManagerClient) CreateServiceMonitor(ctx context.Context, in *CreateServiceMonitorRequest, opts ...grpc.CallOption) (*CreateServiceMonitorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "CreateServiceMonitor", varargs...)
	ret0, _ := ret[0].(*CreateServiceMonitorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// CreateServiceMonitor indicates an expected call of CreateServiceMonitor.
func (mr *MockK8SResourceManagerClientMockRecorder) CreateServiceMonitor(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceMonitor", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).CreateServiceMonitor), varargs...)
}

// DeleteAzureServiceMonitor mocks base method.
func (m *MockK8SResourceManagerClient) DeleteAzureServiceMonitor(ctx context.Context, in *DeleteServiceMonitorRequest, opts ...grpc.CallOption) (*DeleteServiceMonitorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAzureServiceMonitor", varargs...)
	ret0, _ := ret[0].(*DeleteServiceMonitorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteAzureServiceMonitor indicates an expected call of DeleteAzureServiceMonitor.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteAzureServiceMonitor(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAzureServiceMonitor", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteAzureServiceMonitor), varargs...)
}

// DeleteConfigMap mocks base method.
func (m *MockK8SResourceManagerClient) DeleteConfigMap(ctx context.Context, in *DeleteConfigMapRequest, opts ...grpc.CallOption) (*DeleteConfigMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteConfigMap", varargs...)
	ret0, _ := ret[0].(*DeleteConfigMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteConfigMap indicates an expected call of DeleteConfigMap.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteConfigMap(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConfigMap", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteConfigMap), varargs...)
}

// DeleteNamespace mocks base method.
func (m *MockK8SResourceManagerClient) DeleteNamespace(ctx context.Context, in *DeleteNamespaceRequest, opts ...grpc.CallOption) (*DeleteNamespaceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteNamespace", varargs...)
	ret0, _ := ret[0].(*DeleteNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNamespace indicates an expected call of DeleteNamespace.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteNamespace(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNamespace", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteNamespace), varargs...)
}

// DeleteNetworkPolicy mocks base method.
func (m *MockK8SResourceManagerClient) DeleteNetworkPolicy(ctx context.Context, in *DeleteNetworkPolicyRequest, opts ...grpc.CallOption) (*DeleteNetworkPolicyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteNetworkPolicy", varargs...)
	ret0, _ := ret[0].(*DeleteNetworkPolicyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteNetworkPolicy indicates an expected call of DeleteNetworkPolicy.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteNetworkPolicy(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNetworkPolicy", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteNetworkPolicy), varargs...)
}

// DeletePersistentVolumeClaims mocks base method.
func (m *MockK8SResourceManagerClient) DeletePersistentVolumeClaims(ctx context.Context, in *DeletePersistentVolumeClaimsRequest, opts ...grpc.CallOption) (*DeletePersistentVolumeClaimsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePersistentVolumeClaims", varargs...)
	ret0, _ := ret[0].(*DeletePersistentVolumeClaimsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePersistentVolumeClaims indicates an expected call of DeletePersistentVolumeClaims.
func (mr *MockK8SResourceManagerClientMockRecorder) DeletePersistentVolumeClaims(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePersistentVolumeClaims", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeletePersistentVolumeClaims), varargs...)
}

// DeletePodMonitoring mocks base method.
func (m *MockK8SResourceManagerClient) DeletePodMonitoring(ctx context.Context, in *DeletePodMonitoringRequest, opts ...grpc.CallOption) (*DeletePodMonitoringResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePodMonitoring", varargs...)
	ret0, _ := ret[0].(*DeletePodMonitoringResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePodMonitoring indicates an expected call of DeletePodMonitoring.
func (mr *MockK8SResourceManagerClientMockRecorder) DeletePodMonitoring(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePodMonitoring", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeletePodMonitoring), varargs...)
}

// DeletePostgreSql mocks base method.
func (m *MockK8SResourceManagerClient) DeletePostgreSql(ctx context.Context, in *DeletePostgreSqlRequest, opts ...grpc.CallOption) (*DeletePostgreSqlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeletePostgreSql", varargs...)
	ret0, _ := ret[0].(*DeletePostgreSqlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeletePostgreSql indicates an expected call of DeletePostgreSql.
func (mr *MockK8SResourceManagerClientMockRecorder) DeletePostgreSql(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePostgreSql", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeletePostgreSql), varargs...)
}

// DeleteRisingWave mocks base method.
func (m *MockK8SResourceManagerClient) DeleteRisingWave(ctx context.Context, in *DeleteRisingWaveRequest, opts ...grpc.CallOption) (*DeleteRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRisingWave", varargs...)
	ret0, _ := ret[0].(*DeleteRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRisingWave indicates an expected call of DeleteRisingWave.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteRisingWave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWave", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteRisingWave), varargs...)
}

// DeleteRisingWaveComputeNodeGroup mocks base method.
func (m *MockK8SResourceManagerClient) DeleteRisingWaveComputeNodeGroup(ctx context.Context, in *DeleteRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*DeleteRisingWaveComputeNodeGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRisingWaveComputeNodeGroup", varargs...)
	ret0, _ := ret[0].(*DeleteRisingWaveComputeNodeGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRisingWaveComputeNodeGroup indicates an expected call of DeleteRisingWaveComputeNodeGroup.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteRisingWaveComputeNodeGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWaveComputeNodeGroup", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteRisingWaveComputeNodeGroup), varargs...)
}

// DeleteRisingWaveEnvVar mocks base method.
func (m *MockK8SResourceManagerClient) DeleteRisingWaveEnvVar(ctx context.Context, in *DeleteRisingWaveEnvRequest, opts ...grpc.CallOption) (*DeleteRisingWaveEnvResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRisingWaveEnvVar", varargs...)
	ret0, _ := ret[0].(*DeleteRisingWaveEnvResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRisingWaveEnvVar indicates an expected call of DeleteRisingWaveEnvVar.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteRisingWaveEnvVar(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWaveEnvVar", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteRisingWaveEnvVar), varargs...)
}

// DeleteRisingWaveNodeGroup mocks base method.
func (m *MockK8SResourceManagerClient) DeleteRisingWaveNodeGroup(ctx context.Context, in *DeleteRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*DeleteRisingWaveNodeGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteRisingWaveNodeGroup", varargs...)
	ret0, _ := ret[0].(*DeleteRisingWaveNodeGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRisingWaveNodeGroup indicates an expected call of DeleteRisingWaveNodeGroup.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteRisingWaveNodeGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWaveNodeGroup", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteRisingWaveNodeGroup), varargs...)
}

// DeleteSecret mocks base method.
func (m *MockK8SResourceManagerClient) DeleteSecret(ctx context.Context, in *DeleteSecretRequest, opts ...grpc.CallOption) (*DeleteSecretResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteSecret", varargs...)
	ret0, _ := ret[0].(*DeleteSecretResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteSecret indicates an expected call of DeleteSecret.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteSecret(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSecret", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteSecret), varargs...)
}

// DeleteServiceAccount mocks base method.
func (m *MockK8SResourceManagerClient) DeleteServiceAccount(ctx context.Context, in *DeleteServiceAccountRequest, opts ...grpc.CallOption) (*DeleteServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteServiceAccount", varargs...)
	ret0, _ := ret[0].(*DeleteServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteServiceAccount indicates an expected call of DeleteServiceAccount.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceAccount", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteServiceAccount), varargs...)
}

// DeleteServiceMonitor mocks base method.
func (m *MockK8SResourceManagerClient) DeleteServiceMonitor(ctx context.Context, in *DeleteServiceMonitorRequest, opts ...grpc.CallOption) (*DeleteServiceMonitorResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteServiceMonitor", varargs...)
	ret0, _ := ret[0].(*DeleteServiceMonitorResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteServiceMonitor indicates an expected call of DeleteServiceMonitor.
func (mr *MockK8SResourceManagerClientMockRecorder) DeleteServiceMonitor(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceMonitor", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).DeleteServiceMonitor), varargs...)
}

// GetClusterAccess mocks base method.
func (m *MockK8SResourceManagerClient) GetClusterAccess(ctx context.Context, in *GetClusterAccessRequest, opts ...grpc.CallOption) (*GetClusterAccessResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetClusterAccess", varargs...)
	ret0, _ := ret[0].(*GetClusterAccessResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterAccess indicates an expected call of GetClusterAccess.
func (mr *MockK8SResourceManagerClientMockRecorder) GetClusterAccess(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterAccess", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetClusterAccess), varargs...)
}

// GetConfigMap mocks base method.
func (m *MockK8SResourceManagerClient) GetConfigMap(ctx context.Context, in *GetConfigMapRequest, opts ...grpc.CallOption) (*GetConfigMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetConfigMap", varargs...)
	ret0, _ := ret[0].(*GetConfigMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigMap indicates an expected call of GetConfigMap.
func (mr *MockK8SResourceManagerClientMockRecorder) GetConfigMap(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigMap", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetConfigMap), varargs...)
}

// GetDeploymentReplicasStatus mocks base method.
func (m *MockK8SResourceManagerClient) GetDeploymentReplicasStatus(ctx context.Context, in *GetDeploymentReplicasStatusRequest, opts ...grpc.CallOption) (*GetDeploymentReplicasStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetDeploymentReplicasStatus", varargs...)
	ret0, _ := ret[0].(*GetDeploymentReplicasStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeploymentReplicasStatus indicates an expected call of GetDeploymentReplicasStatus.
func (mr *MockK8SResourceManagerClientMockRecorder) GetDeploymentReplicasStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeploymentReplicasStatus", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetDeploymentReplicasStatus), varargs...)
}

// GetHelmRelease mocks base method.
func (m *MockK8SResourceManagerClient) GetHelmRelease(ctx context.Context, in *GetHelmReleaseRequest, opts ...grpc.CallOption) (*GetHelmReleaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetHelmRelease", varargs...)
	ret0, _ := ret[0].(*GetHelmReleaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHelmRelease indicates an expected call of GetHelmRelease.
func (mr *MockK8SResourceManagerClientMockRecorder) GetHelmRelease(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHelmRelease", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetHelmRelease), varargs...)
}

// GetNamespace mocks base method.
func (m *MockK8SResourceManagerClient) GetNamespace(ctx context.Context, in *GetNamespaceRequest, opts ...grpc.CallOption) (*GetNamespaceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetNamespace", varargs...)
	ret0, _ := ret[0].(*GetNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamespace indicates an expected call of GetNamespace.
func (mr *MockK8SResourceManagerClientMockRecorder) GetNamespace(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamespace", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetNamespace), varargs...)
}

// GetPersistentVolumeClaims mocks base method.
func (m *MockK8SResourceManagerClient) GetPersistentVolumeClaims(ctx context.Context, in *GetPersistentVolumeClaimsRequest, opts ...grpc.CallOption) (*GetPersistentVolumeClaimsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPersistentVolumeClaims", varargs...)
	ret0, _ := ret[0].(*GetPersistentVolumeClaimsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersistentVolumeClaims indicates an expected call of GetPersistentVolumeClaims.
func (mr *MockK8SResourceManagerClientMockRecorder) GetPersistentVolumeClaims(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersistentVolumeClaims", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetPersistentVolumeClaims), varargs...)
}

// GetPodPhases mocks base method.
func (m *MockK8SResourceManagerClient) GetPodPhases(ctx context.Context, in *GetPodPhasesRequest, opts ...grpc.CallOption) (*GetPodPhasesResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPodPhases", varargs...)
	ret0, _ := ret[0].(*GetPodPhasesResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodPhases indicates an expected call of GetPodPhases.
func (mr *MockK8SResourceManagerClientMockRecorder) GetPodPhases(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodPhases", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetPodPhases), varargs...)
}

// GetPods mocks base method.
func (m *MockK8SResourceManagerClient) GetPods(ctx context.Context, in *GetPodsRequest, opts ...grpc.CallOption) (*GetPodsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPods", varargs...)
	ret0, _ := ret[0].(*GetPodsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPods indicates an expected call of GetPods.
func (mr *MockK8SResourceManagerClientMockRecorder) GetPods(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPods", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetPods), varargs...)
}

// GetPostgreSql mocks base method.
func (m *MockK8SResourceManagerClient) GetPostgreSql(ctx context.Context, in *GetPostgreSqlRequest, opts ...grpc.CallOption) (*GetPostgreSqlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetPostgreSql", varargs...)
	ret0, _ := ret[0].(*GetPostgreSqlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostgreSql indicates an expected call of GetPostgreSql.
func (mr *MockK8SResourceManagerClientMockRecorder) GetPostgreSql(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostgreSql", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetPostgreSql), varargs...)
}

// GetRisingWave mocks base method.
func (m *MockK8SResourceManagerClient) GetRisingWave(ctx context.Context, in *GetRisingWaveRequest, opts ...grpc.CallOption) (*GetRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetRisingWave", varargs...)
	ret0, _ := ret[0].(*GetRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRisingWave indicates an expected call of GetRisingWave.
func (mr *MockK8SResourceManagerClientMockRecorder) GetRisingWave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRisingWave", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetRisingWave), varargs...)
}

// GetSecret mocks base method.
func (m *MockK8SResourceManagerClient) GetSecret(ctx context.Context, in *GetSecretRequest, opts ...grpc.CallOption) (*GetSecretResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetSecret", varargs...)
	ret0, _ := ret[0].(*GetSecretResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecret indicates an expected call of GetSecret.
func (mr *MockK8SResourceManagerClientMockRecorder) GetSecret(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecret", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetSecret), varargs...)
}

// GetServiceAccount mocks base method.
func (m *MockK8SResourceManagerClient) GetServiceAccount(ctx context.Context, in *GetServiceAccountRequest, opts ...grpc.CallOption) (*GetServiceAccountResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetServiceAccount", varargs...)
	ret0, _ := ret[0].(*GetServiceAccountResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceAccount indicates an expected call of GetServiceAccount.
func (mr *MockK8SResourceManagerClientMockRecorder) GetServiceAccount(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceAccount", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetServiceAccount), varargs...)
}

// GetStatefulSetReplicasStatus mocks base method.
func (m *MockK8SResourceManagerClient) GetStatefulSetReplicasStatus(ctx context.Context, in *GetStatefulSetReplicasStatusRequest, opts ...grpc.CallOption) (*GetStatefulSetReplicasStatusResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "GetStatefulSetReplicasStatus", varargs...)
	ret0, _ := ret[0].(*GetStatefulSetReplicasStatusResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatefulSetReplicasStatus indicates an expected call of GetStatefulSetReplicasStatus.
func (mr *MockK8SResourceManagerClientMockRecorder) GetStatefulSetReplicasStatus(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatefulSetReplicasStatus", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).GetStatefulSetReplicasStatus), varargs...)
}

// InstallHelmRelease mocks base method.
func (m *MockK8SResourceManagerClient) InstallHelmRelease(ctx context.Context, in *InstallHelmReleaseRequest, opts ...grpc.CallOption) (*InstallHelmReleaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "InstallHelmRelease", varargs...)
	ret0, _ := ret[0].(*InstallHelmReleaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// InstallHelmRelease indicates an expected call of InstallHelmRelease.
func (mr *MockK8SResourceManagerClientMockRecorder) InstallHelmRelease(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallHelmRelease", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).InstallHelmRelease), varargs...)
}

// LabelNamespace mocks base method.
func (m *MockK8SResourceManagerClient) LabelNamespace(ctx context.Context, in *LabelNamespaceRequest, opts ...grpc.CallOption) (*LabelNamespaceResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "LabelNamespace", varargs...)
	ret0, _ := ret[0].(*LabelNamespaceResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// LabelNamespace indicates an expected call of LabelNamespace.
func (mr *MockK8SResourceManagerClientMockRecorder) LabelNamespace(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelNamespace", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).LabelNamespace), varargs...)
}

// PutRisingWaveEnvVar mocks base method.
func (m *MockK8SResourceManagerClient) PutRisingWaveEnvVar(ctx context.Context, in *PutRisingWaveEnvRequest, opts ...grpc.CallOption) (*PutRisingWaveEnvResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "PutRisingWaveEnvVar", varargs...)
	ret0, _ := ret[0].(*PutRisingWaveEnvResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutRisingWaveEnvVar indicates an expected call of PutRisingWaveEnvVar.
func (mr *MockK8SResourceManagerClientMockRecorder) PutRisingWaveEnvVar(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutRisingWaveEnvVar", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).PutRisingWaveEnvVar), varargs...)
}

// RestartDeployment mocks base method.
func (m *MockK8SResourceManagerClient) RestartDeployment(ctx context.Context, in *RestartDeploymentRequest, opts ...grpc.CallOption) (*RestartDeploymentResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RestartDeployment", varargs...)
	ret0, _ := ret[0].(*RestartDeploymentResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RestartDeployment indicates an expected call of RestartDeployment.
func (mr *MockK8SResourceManagerClientMockRecorder) RestartDeployment(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartDeployment", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).RestartDeployment), varargs...)
}

// RestartStatefulSet mocks base method.
func (m *MockK8SResourceManagerClient) RestartStatefulSet(ctx context.Context, in *RestartStatefulSetRequest, opts ...grpc.CallOption) (*RestartStatefulSetResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "RestartStatefulSet", varargs...)
	ret0, _ := ret[0].(*RestartStatefulSetResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RestartStatefulSet indicates an expected call of RestartStatefulSet.
func (mr *MockK8SResourceManagerClientMockRecorder) RestartStatefulSet(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartStatefulSet", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).RestartStatefulSet), varargs...)
}

// ScaleRisingWave mocks base method.
func (m *MockK8SResourceManagerClient) ScaleRisingWave(ctx context.Context, in *ScaleRisingWaveRequest, opts ...grpc.CallOption) (*ScaleRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScaleRisingWave", varargs...)
	ret0, _ := ret[0].(*ScaleRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScaleRisingWave indicates an expected call of ScaleRisingWave.
func (mr *MockK8SResourceManagerClientMockRecorder) ScaleRisingWave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScaleRisingWave", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).ScaleRisingWave), varargs...)
}

// ScaleRisingWaveOneOf mocks base method.
func (m *MockK8SResourceManagerClient) ScaleRisingWaveOneOf(ctx context.Context, in *ScaleRisingWaveRequestOneOf, opts ...grpc.CallOption) (*ScaleRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "ScaleRisingWaveOneOf", varargs...)
	ret0, _ := ret[0].(*ScaleRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ScaleRisingWaveOneOf indicates an expected call of ScaleRisingWaveOneOf.
func (mr *MockK8SResourceManagerClientMockRecorder) ScaleRisingWaveOneOf(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScaleRisingWaveOneOf", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).ScaleRisingWaveOneOf), varargs...)
}

// StartRisingWave mocks base method.
func (m *MockK8SResourceManagerClient) StartRisingWave(ctx context.Context, in *StartRisingWaveRequest, opts ...grpc.CallOption) (*StartRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StartRisingWave", varargs...)
	ret0, _ := ret[0].(*StartRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StartRisingWave indicates an expected call of StartRisingWave.
func (mr *MockK8SResourceManagerClientMockRecorder) StartRisingWave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartRisingWave", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).StartRisingWave), varargs...)
}

// StopRisingWave mocks base method.
func (m *MockK8SResourceManagerClient) StopRisingWave(ctx context.Context, in *StopRisingWaveRequest, opts ...grpc.CallOption) (*StopRisingWaveResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "StopRisingWave", varargs...)
	ret0, _ := ret[0].(*StopRisingWaveResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// StopRisingWave indicates an expected call of StopRisingWave.
func (mr *MockK8SResourceManagerClientMockRecorder) StopRisingWave(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopRisingWave", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).StopRisingWave), varargs...)
}

// UninstallHelmRelease mocks base method.
func (m *MockK8SResourceManagerClient) UninstallHelmRelease(ctx context.Context, in *UninstallHelmReleaseRequest, opts ...grpc.CallOption) (*UninstallHelmReleaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UninstallHelmRelease", varargs...)
	ret0, _ := ret[0].(*UninstallHelmReleaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UninstallHelmRelease indicates an expected call of UninstallHelmRelease.
func (mr *MockK8SResourceManagerClientMockRecorder) UninstallHelmRelease(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UninstallHelmRelease", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UninstallHelmRelease), varargs...)
}

// UpdateConfigMap mocks base method.
func (m *MockK8SResourceManagerClient) UpdateConfigMap(ctx context.Context, in *UpdateConfigMapRequest, opts ...grpc.CallOption) (*UpdateConfigMapResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateConfigMap", varargs...)
	ret0, _ := ret[0].(*UpdateConfigMapResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateConfigMap indicates an expected call of UpdateConfigMap.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateConfigMap(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfigMap", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateConfigMap), varargs...)
}

// UpdatePostgreSql mocks base method.
func (m *MockK8SResourceManagerClient) UpdatePostgreSql(ctx context.Context, in *UpdatePostgreSqlRequest, opts ...grpc.CallOption) (*UpdatePostgreSqlResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdatePostgreSql", varargs...)
	ret0, _ := ret[0].(*UpdatePostgreSqlResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdatePostgreSql indicates an expected call of UpdatePostgreSql.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdatePostgreSql(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePostgreSql", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdatePostgreSql), varargs...)
}

// UpdateRisingWaveComponents mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveComponents(ctx context.Context, in *UpdateRisingWaveComponentsRequest, opts ...grpc.CallOption) (*UpdateRisingWaveComponentsResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveComponents", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveComponentsResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveComponents indicates an expected call of UpdateRisingWaveComponents.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveComponents(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveComponents", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveComponents), varargs...)
}

// UpdateRisingWaveComputeNodeGroup mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveComputeNodeGroup(ctx context.Context, in *UpdateRisingWaveComputeNodeGroupRequest, opts ...grpc.CallOption) (*UpdateRisingWaveComputeNodeGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveComputeNodeGroup", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveComputeNodeGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveComputeNodeGroup indicates an expected call of UpdateRisingWaveComputeNodeGroup.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveComputeNodeGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveComputeNodeGroup", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveComputeNodeGroup), varargs...)
}

// UpdateRisingWaveImage mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveImage(ctx context.Context, in *UpdateRisingWaveImageRequest, opts ...grpc.CallOption) (*UpdateRisingWaveImageResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveImage", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveImageResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveImage indicates an expected call of UpdateRisingWaveImage.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveImage(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveImage", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveImage), varargs...)
}

// UpdateRisingWaveLicenseKey mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveLicenseKey(ctx context.Context, in *UpdateRisingWaveLicenseKeyRequest, opts ...grpc.CallOption) (*UpdateRisingWaveLicenseKeyResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveLicenseKey", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveLicenseKeyResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveLicenseKey indicates an expected call of UpdateRisingWaveLicenseKey.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveLicenseKey(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveLicenseKey", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveLicenseKey), varargs...)
}

// UpdateRisingWaveMetaStore mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveMetaStore(ctx context.Context, in *UpdateRisingWaveMetaStoreRequest, opts ...grpc.CallOption) (*UpdateRisingWaveMetaStoreResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveMetaStore", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveMetaStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveMetaStore indicates an expected call of UpdateRisingWaveMetaStore.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveMetaStore(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveMetaStore", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveMetaStore), varargs...)
}

// UpdateRisingWaveNodeGroup mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveNodeGroup(ctx context.Context, in *UpdateRisingWaveNodeGroupRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveNodeGroup", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveNodeGroupResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveNodeGroup indicates an expected call of UpdateRisingWaveNodeGroup.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveNodeGroup(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveNodeGroup", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveNodeGroup), varargs...)
}

// UpdateRisingWaveNodeGroupConfiguration mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, in *UpdateRisingWaveNodeGroupConfigurationRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupConfigurationResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveNodeGroupConfiguration", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveNodeGroupConfigurationResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveNodeGroupConfiguration indicates an expected call of UpdateRisingWaveNodeGroupConfiguration.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveNodeGroupConfiguration(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveNodeGroupConfiguration", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveNodeGroupConfiguration), varargs...)
}

// UpdateRisingWaveNodeGroupRestartAt mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, in *UpdateRisingWaveNodeGroupRestartAtRequest, opts ...grpc.CallOption) (*UpdateRisingWaveNodeGroupRestartAtResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveNodeGroupRestartAt", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveNodeGroupRestartAtResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveNodeGroupRestartAt indicates an expected call of UpdateRisingWaveNodeGroupRestartAt.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveNodeGroupRestartAt(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveNodeGroupRestartAt", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveNodeGroupRestartAt), varargs...)
}

// UpdateRisingWaveSecretStore mocks base method.
func (m *MockK8SResourceManagerClient) UpdateRisingWaveSecretStore(ctx context.Context, in *UpdateRisingWaveSecretStoreRequest, opts ...grpc.CallOption) (*UpdateRisingWaveSecretStoreResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateRisingWaveSecretStore", varargs...)
	ret0, _ := ret[0].(*UpdateRisingWaveSecretStoreResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateRisingWaveSecretStore indicates an expected call of UpdateRisingWaveSecretStore.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateRisingWaveSecretStore(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveSecretStore", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateRisingWaveSecretStore), varargs...)
}

// UpdateSecret mocks base method.
func (m *MockK8SResourceManagerClient) UpdateSecret(ctx context.Context, in *UpdateSecretRequest, opts ...grpc.CallOption) (*UpdateSecretResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpdateSecret", varargs...)
	ret0, _ := ret[0].(*UpdateSecretResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpdateSecret indicates an expected call of UpdateSecret.
func (mr *MockK8SResourceManagerClientMockRecorder) UpdateSecret(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSecret", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpdateSecret), varargs...)
}

// UpgradeHelmRelease mocks base method.
func (m *MockK8SResourceManagerClient) UpgradeHelmRelease(ctx context.Context, in *UpgradeHelmReleaseRequest, opts ...grpc.CallOption) (*UpgradeHelmReleaseResponse, error) {
	m.ctrl.T.Helper()
	varargs := []any{ctx, in}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "UpgradeHelmRelease", varargs...)
	ret0, _ := ret[0].(*UpgradeHelmReleaseResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// UpgradeHelmRelease indicates an expected call of UpgradeHelmRelease.
func (mr *MockK8SResourceManagerClientMockRecorder) UpgradeHelmRelease(ctx, in any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, in}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpgradeHelmRelease", reflect.TypeOf((*MockK8SResourceManagerClient)(nil).UpgradeHelmRelease), varargs...)
}
