// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: config/k8s.proto

package k8s

import (
	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type PullPolicy int32

const (
	PullPolicy_UNKNOWN PullPolicy = 0
	// The image registry of the task image will always be queried.
	PullPolicy_PULL_ALWAYS PullPolicy = 1
	// The task image is pulled only if it is not already present locally.
	PullPolicy_PULL_IF_NOT_PRESENT PullPolicy = 2
)

// Enum value maps for PullPolicy.
var (
	PullPolicy_name = map[int32]string{
		0: "UNKNOWN",
		1: "PULL_ALWAYS",
		2: "PULL_IF_NOT_PRESENT",
	}
	PullPolicy_value = map[string]int32{
		"UNKNOWN":             0,
		"PULL_ALWAYS":         1,
		"PULL_IF_NOT_PRESENT": 2,
	}
)

func (x PullPolicy) Enum() *PullPolicy {
	p := new(PullPolicy)
	*p = x
	return p
}

func (x PullPolicy) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (PullPolicy) Descriptor() protoreflect.EnumDescriptor {
	return file_config_k8s_proto_enumTypes[0].Descriptor()
}

func (PullPolicy) Type() protoreflect.EnumType {
	return &file_config_k8s_proto_enumTypes[0]
}

func (x PullPolicy) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use PullPolicy.Descriptor instead.
func (PullPolicy) EnumDescriptor() ([]byte, []int) {
	return file_config_k8s_proto_rawDescGZIP(), []int{0}
}

type Config struct {
	state     protoimpl.MessageState `protogen:"open.v1"`
	ClusterId string                 `protobuf:"bytes,1,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Endpoint  string                 `protobuf:"bytes,6,opt,name=endpoint,proto3" json:"endpoint,omitempty"`
	// Base64 encoded CA cert for K8S API server.
	CaCertificateBase64 string `protobuf:"bytes,7,opt,name=ca_certificate_base64,json=caCertificateBase64,proto3" json:"ca_certificate_base64,omitempty"`
	// Types that are valid to be assigned to Auth:
	//
	//	*Config_StaticTokenAuth
	//	*Config_InClusterAuth
	Auth            isConfig_Auth `protobuf_oneof:"auth"`
	TaskConfig      *TaskConfig   `protobuf:"bytes,4,opt,name=task_config,json=taskConfig,proto3" json:"task_config,omitempty"`
	AllowHelmCharts []string      `protobuf:"bytes,5,rep,name=allow_helm_charts,json=allowHelmCharts,proto3" json:"allow_helm_charts,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_config_k8s_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_k8s_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_k8s_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *Config) GetEndpoint() string {
	if x != nil {
		return x.Endpoint
	}
	return ""
}

func (x *Config) GetCaCertificateBase64() string {
	if x != nil {
		return x.CaCertificateBase64
	}
	return ""
}

func (x *Config) GetAuth() isConfig_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Config) GetStaticTokenAuth() *StaticTokenAuth {
	if x != nil {
		if x, ok := x.Auth.(*Config_StaticTokenAuth); ok {
			return x.StaticTokenAuth
		}
	}
	return nil
}

func (x *Config) GetInClusterAuth() *InClusterAuth {
	if x != nil {
		if x, ok := x.Auth.(*Config_InClusterAuth); ok {
			return x.InClusterAuth
		}
	}
	return nil
}

func (x *Config) GetTaskConfig() *TaskConfig {
	if x != nil {
		return x.TaskConfig
	}
	return nil
}

func (x *Config) GetAllowHelmCharts() []string {
	if x != nil {
		return x.AllowHelmCharts
	}
	return nil
}

type isConfig_Auth interface {
	isConfig_Auth()
}

type Config_StaticTokenAuth struct {
	// Use static token of a service account to auth.
	StaticTokenAuth *StaticTokenAuth `protobuf:"bytes,2,opt,name=static_token_auth,json=staticTokenAuth,proto3,oneof"`
}

type Config_InClusterAuth struct {
	// In cluster auth, require the service account running agent to be
	// binded with required clusterrole
	InClusterAuth *InClusterAuth `protobuf:"bytes,3,opt,name=in_cluster_auth,json=inClusterAuth,proto3,oneof"`
}

func (*Config_StaticTokenAuth) isConfig_Auth() {}

func (*Config_InClusterAuth) isConfig_Auth() {}

type StaticTokenAuth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	MasterUrl     string                 `protobuf:"bytes,1,opt,name=master_url,json=masterUrl,proto3" json:"master_url,omitempty"`
	Token         string                 `protobuf:"bytes,2,opt,name=token,proto3" json:"token,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StaticTokenAuth) Reset() {
	*x = StaticTokenAuth{}
	mi := &file_config_k8s_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaticTokenAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticTokenAuth) ProtoMessage() {}

func (x *StaticTokenAuth) ProtoReflect() protoreflect.Message {
	mi := &file_config_k8s_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticTokenAuth.ProtoReflect.Descriptor instead.
func (*StaticTokenAuth) Descriptor() ([]byte, []int) {
	return file_config_k8s_proto_rawDescGZIP(), []int{1}
}

func (x *StaticTokenAuth) GetMasterUrl() string {
	if x != nil {
		return x.MasterUrl
	}
	return ""
}

func (x *StaticTokenAuth) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

type InClusterAuth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *InClusterAuth) Reset() {
	*x = InClusterAuth{}
	mi := &file_config_k8s_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *InClusterAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*InClusterAuth) ProtoMessage() {}

func (x *InClusterAuth) ProtoReflect() protoreflect.Message {
	mi := &file_config_k8s_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use InClusterAuth.ProtoReflect.Descriptor instead.
func (*InClusterAuth) Descriptor() ([]byte, []int) {
	return file_config_k8s_proto_rawDescGZIP(), []int{2}
}

type TaskConfig struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Image          string                 `protobuf:"bytes,1,opt,name=image,proto3" json:"image,omitempty"`
	ServiceAccount string                 `protobuf:"bytes,2,opt,name=service_account,json=serviceAccount,proto3" json:"service_account,omitempty"`
	Namespace      string                 `protobuf:"bytes,3,opt,name=namespace,proto3" json:"namespace,omitempty"`
	PullPolicy     PullPolicy             `protobuf:"varint,4,opt,name=pull_policy,json=pullPolicy,proto3,enum=config.k8s.PullPolicy" json:"pull_policy,omitempty"`
	// Toleration settings for the Task pod.
	Tolerations []*k8s.Toleration `protobuf:"bytes,5,rep,name=tolerations,proto3" json:"tolerations,omitempty"`
	// Affinity settings for the Task pod.
	Affinity      *k8s.Affinity     `protobuf:"bytes,6,opt,name=affinity,proto3" json:"affinity,omitempty"`
	Labels        map[string]string `protobuf:"bytes,7,rep,name=labels,proto3" json:"labels,omitempty" protobuf_key:"bytes,1,opt,name=key" protobuf_val:"bytes,2,opt,name=value"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *TaskConfig) Reset() {
	*x = TaskConfig{}
	mi := &file_config_k8s_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *TaskConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TaskConfig) ProtoMessage() {}

func (x *TaskConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_k8s_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TaskConfig.ProtoReflect.Descriptor instead.
func (*TaskConfig) Descriptor() ([]byte, []int) {
	return file_config_k8s_proto_rawDescGZIP(), []int{3}
}

func (x *TaskConfig) GetImage() string {
	if x != nil {
		return x.Image
	}
	return ""
}

func (x *TaskConfig) GetServiceAccount() string {
	if x != nil {
		return x.ServiceAccount
	}
	return ""
}

func (x *TaskConfig) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *TaskConfig) GetPullPolicy() PullPolicy {
	if x != nil {
		return x.PullPolicy
	}
	return PullPolicy_UNKNOWN
}

func (x *TaskConfig) GetTolerations() []*k8s.Toleration {
	if x != nil {
		return x.Tolerations
	}
	return nil
}

func (x *TaskConfig) GetAffinity() *k8s.Affinity {
	if x != nil {
		return x.Affinity
	}
	return nil
}

func (x *TaskConfig) GetLabels() map[string]string {
	if x != nil {
		return x.Labels
	}
	return nil
}

var File_config_k8s_proto protoreflect.FileDescriptor

var file_config_k8s_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b, 0x38, 0x73, 0x1a, 0x10,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x6b, 0x38, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x22, 0xf4, 0x02, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x63,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x65, 0x6e,
	0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x63, 0x61, 0x5f, 0x63, 0x65, 0x72,
	0x74, 0x69, 0x66, 0x69, 0x63, 0x61, 0x74, 0x65, 0x5f, 0x62, 0x61, 0x73, 0x65, 0x36, 0x34, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x13, 0x63, 0x61, 0x43, 0x65, 0x72, 0x74, 0x69, 0x66, 0x69,
	0x63, 0x61, 0x74, 0x65, 0x42, 0x61, 0x73, 0x65, 0x36, 0x34, 0x12, 0x49, 0x0a, 0x11, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1b, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b,
	0x38, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x75,
	0x74, 0x68, 0x48, 0x00, 0x52, 0x0f, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x41, 0x75, 0x74, 0x68, 0x12, 0x43, 0x0a, 0x0f, 0x69, 0x6e, 0x5f, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x61, 0x75, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x19,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x49, 0x6e, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x48, 0x00, 0x52, 0x0d, 0x69, 0x6e, 0x43,
	0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68, 0x12, 0x37, 0x0a, 0x0b, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x61, 0x73,
	0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x0a, 0x74, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x2a, 0x0a, 0x11, 0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x5f, 0x68, 0x65, 0x6c,
	0x6d, 0x5f, 0x63, 0x68, 0x61, 0x72, 0x74, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0f,
	0x61, 0x6c, 0x6c, 0x6f, 0x77, 0x48, 0x65, 0x6c, 0x6d, 0x43, 0x68, 0x61, 0x72, 0x74, 0x73, 0x42,
	0x06, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x22, 0x46, 0x0a, 0x0f, 0x53, 0x74, 0x61, 0x74, 0x69,
	0x63, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x41, 0x75, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6d, 0x61,
	0x73, 0x74, 0x65, 0x72, 0x5f, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x6d, 0x61, 0x73, 0x74, 0x65, 0x72, 0x55, 0x72, 0x6c, 0x12, 0x14, 0x0a, 0x05, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x22,
	0x0f, 0x0a, 0x0d, 0x49, 0x6e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x41, 0x75, 0x74, 0x68,
	0x22, 0x85, 0x03, 0x0a, 0x0a, 0x54, 0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12,
	0x14, 0x0a, 0x05, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65,
	0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0e,
	0x73, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c,
	0x0a, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x37, 0x0a, 0x0b,
	0x70, 0x75, 0x6c, 0x6c, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x50,
	0x75, 0x6c, 0x6c, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x52, 0x0a, 0x70, 0x75, 0x6c, 0x6c, 0x50,
	0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x38, 0x0a, 0x0b, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x18, 0x05, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x16, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69,
	0x6f, 0x6e, 0x52, 0x0b, 0x74, 0x6f, 0x6c, 0x65, 0x72, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12,
	0x30, 0x0a, 0x08, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x41,
	0x66, 0x66, 0x69, 0x6e, 0x69, 0x74, 0x79, 0x52, 0x08, 0x61, 0x66, 0x66, 0x69, 0x6e, 0x69, 0x74,
	0x79, 0x12, 0x3a, 0x0a, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x22, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b, 0x38, 0x73, 0x2e, 0x54,
	0x61, 0x73, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x6c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x1a, 0x39, 0x0a,
	0x0b, 0x4c, 0x61, 0x62, 0x65, 0x6c, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x2a, 0x43, 0x0a, 0x0a, 0x50, 0x75, 0x6c, 0x6c,
	0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57,
	0x4e, 0x10, 0x00, 0x12, 0x0f, 0x0a, 0x0b, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x41, 0x4c, 0x57, 0x41,
	0x59, 0x53, 0x10, 0x01, 0x12, 0x17, 0x0a, 0x13, 0x50, 0x55, 0x4c, 0x4c, 0x5f, 0x49, 0x46, 0x5f,
	0x4e, 0x4f, 0x54, 0x5f, 0x50, 0x52, 0x45, 0x53, 0x45, 0x4e, 0x54, 0x10, 0x02, 0x42, 0x37, 0x5a,
	0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69,
	0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64,
	0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2f, 0x6b, 0x38, 0x73, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_config_k8s_proto_rawDescOnce sync.Once
	file_config_k8s_proto_rawDescData []byte
)

func file_config_k8s_proto_rawDescGZIP() []byte {
	file_config_k8s_proto_rawDescOnce.Do(func() {
		file_config_k8s_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_config_k8s_proto_rawDesc), len(file_config_k8s_proto_rawDesc)))
	})
	return file_config_k8s_proto_rawDescData
}

var file_config_k8s_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_config_k8s_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_config_k8s_proto_goTypes = []any{
	(PullPolicy)(0),         // 0: config.k8s.PullPolicy
	(*Config)(nil),          // 1: config.k8s.Config
	(*StaticTokenAuth)(nil), // 2: config.k8s.StaticTokenAuth
	(*InClusterAuth)(nil),   // 3: config.k8s.InClusterAuth
	(*TaskConfig)(nil),      // 4: config.k8s.TaskConfig
	nil,                     // 5: config.k8s.TaskConfig.LabelsEntry
	(*k8s.Toleration)(nil),  // 6: common.k8s.Toleration
	(*k8s.Affinity)(nil),    // 7: common.k8s.Affinity
}
var file_config_k8s_proto_depIdxs = []int32{
	2, // 0: config.k8s.Config.static_token_auth:type_name -> config.k8s.StaticTokenAuth
	3, // 1: config.k8s.Config.in_cluster_auth:type_name -> config.k8s.InClusterAuth
	4, // 2: config.k8s.Config.task_config:type_name -> config.k8s.TaskConfig
	0, // 3: config.k8s.TaskConfig.pull_policy:type_name -> config.k8s.PullPolicy
	6, // 4: config.k8s.TaskConfig.tolerations:type_name -> common.k8s.Toleration
	7, // 5: config.k8s.TaskConfig.affinity:type_name -> common.k8s.Affinity
	5, // 6: config.k8s.TaskConfig.labels:type_name -> config.k8s.TaskConfig.LabelsEntry
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_config_k8s_proto_init() }
func file_config_k8s_proto_init() {
	if File_config_k8s_proto != nil {
		return
	}
	file_config_k8s_proto_msgTypes[0].OneofWrappers = []any{
		(*Config_StaticTokenAuth)(nil),
		(*Config_InClusterAuth)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_config_k8s_proto_rawDesc), len(file_config_k8s_proto_rawDesc)),
			NumEnums:      1,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_k8s_proto_goTypes,
		DependencyIndexes: file_config_k8s_proto_depIdxs,
		EnumInfos:         file_config_k8s_proto_enumTypes,
		MessageInfos:      file_config_k8s_proto_msgTypes,
	}.Build()
	File_config_k8s_proto = out.File
	file_config_k8s_proto_goTypes = nil
	file_config_k8s_proto_depIdxs = nil
}
