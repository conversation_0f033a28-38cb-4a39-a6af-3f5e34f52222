// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: config/azr.proto

package azr

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Config contains Azure configuration data for CloudAgent to initialize
// authetication to Azure services
type Config struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	SubscriptionId string                 `protobuf:"bytes,1,opt,name=subscription_id,json=subscriptionId,proto3" json:"subscription_id,omitempty"`
	Location       string                 `protobuf:"bytes,2,opt,name=location,proto3" json:"location,omitempty"`
	// Name of the resource group holding all the provisioned resources
	ResourceGroup string `protobuf:"bytes,3,opt,name=resource_group,json=resourceGroup,proto3" json:"resource_group,omitempty"`
	OidcIssuer    string `protobuf:"bytes,6,opt,name=oidc_issuer,json=oidcIssuer,proto3" json:"oidc_issuer,omitempty"`
	// Types that are valid to be assigned to Auth:
	//
	//	*Config_AksWorkloadIdentity
	//	*Config_StaticCreds
	Auth isConfig_Auth `protobuf_oneof:"auth"`
	// Name of the storage account used to read/write to Azure blob
	//
	// Deprecated: Marked as deprecated in config/azr.proto.
	StorageAccountName string `protobuf:"bytes,7,opt,name=storage_account_name,json=storageAccountName,proto3" json:"storage_account_name,omitempty"`
	unknownFields      protoimpl.UnknownFields
	sizeCache          protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_config_azr_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_azr_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_azr_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetSubscriptionId() string {
	if x != nil {
		return x.SubscriptionId
	}
	return ""
}

func (x *Config) GetLocation() string {
	if x != nil {
		return x.Location
	}
	return ""
}

func (x *Config) GetResourceGroup() string {
	if x != nil {
		return x.ResourceGroup
	}
	return ""
}

func (x *Config) GetOidcIssuer() string {
	if x != nil {
		return x.OidcIssuer
	}
	return ""
}

func (x *Config) GetAuth() isConfig_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Config) GetAksWorkloadIdentity() *AKSWorkloadIdentity {
	if x != nil {
		if x, ok := x.Auth.(*Config_AksWorkloadIdentity); ok {
			return x.AksWorkloadIdentity
		}
	}
	return nil
}

func (x *Config) GetStaticCreds() *StaticCredAuth {
	if x != nil {
		if x, ok := x.Auth.(*Config_StaticCreds); ok {
			return x.StaticCreds
		}
	}
	return nil
}

// Deprecated: Marked as deprecated in config/azr.proto.
func (x *Config) GetStorageAccountName() string {
	if x != nil {
		return x.StorageAccountName
	}
	return ""
}

type isConfig_Auth interface {
	isConfig_Auth()
}

type Config_AksWorkloadIdentity struct {
	AksWorkloadIdentity *AKSWorkloadIdentity `protobuf:"bytes,4,opt,name=aks_workload_identity,json=aksWorkloadIdentity,proto3,oneof"`
}

type Config_StaticCreds struct {
	StaticCreds *StaticCredAuth `protobuf:"bytes,5,opt,name=static_creds,json=staticCreds,proto3,oneof"`
}

func (*Config_AksWorkloadIdentity) isConfig_Auth() {}

func (*Config_StaticCreds) isConfig_Auth() {}

// Auth option to use static credential to access Azure APIs.
// This is intended for test usage only. Do NOT use it in production
type StaticCredAuth struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ClientSecret  string                 `protobuf:"bytes,1,opt,name=client_secret,json=clientSecret,proto3" json:"client_secret,omitempty"`
	ClientId      string                 `protobuf:"bytes,2,opt,name=client_id,json=clientId,proto3" json:"client_id,omitempty"`
	TenantId      string                 `protobuf:"bytes,3,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *StaticCredAuth) Reset() {
	*x = StaticCredAuth{}
	mi := &file_config_azr_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaticCredAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticCredAuth) ProtoMessage() {}

func (x *StaticCredAuth) ProtoReflect() protoreflect.Message {
	mi := &file_config_azr_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticCredAuth.ProtoReflect.Descriptor instead.
func (*StaticCredAuth) Descriptor() ([]byte, []int) {
	return file_config_azr_proto_rawDescGZIP(), []int{1}
}

func (x *StaticCredAuth) GetClientSecret() string {
	if x != nil {
		return x.ClientSecret
	}
	return ""
}

func (x *StaticCredAuth) GetClientId() string {
	if x != nil {
		return x.ClientId
	}
	return ""
}

func (x *StaticCredAuth) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

// Auth option to use AKS workload identity to access Azure APIs
// https://learn.microsoft.com/en-us/azure/aks/workload-identity-overview?tabs=dotnet
type AKSWorkloadIdentity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AKSWorkloadIdentity) Reset() {
	*x = AKSWorkloadIdentity{}
	mi := &file_config_azr_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AKSWorkloadIdentity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AKSWorkloadIdentity) ProtoMessage() {}

func (x *AKSWorkloadIdentity) ProtoReflect() protoreflect.Message {
	mi := &file_config_azr_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AKSWorkloadIdentity.ProtoReflect.Descriptor instead.
func (*AKSWorkloadIdentity) Descriptor() ([]byte, []int) {
	return file_config_azr_proto_rawDescGZIP(), []int{2}
}

var File_config_azr_proto protoreflect.FileDescriptor

var file_config_azr_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x7a, 0x72, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61, 0x7a, 0x72, 0x22, 0xeb,
	0x02, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x27, 0x0a, 0x0f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0e, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x6c, 0x6f, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x25,
	0x0a, 0x0e, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x69, 0x64, 0x63, 0x5f, 0x69, 0x73,
	0x73, 0x75, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x69, 0x64, 0x63,
	0x49, 0x73, 0x73, 0x75, 0x65, 0x72, 0x12, 0x55, 0x0a, 0x15, 0x61, 0x6b, 0x73, 0x5f, 0x77, 0x6f,
	0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61,
	0x7a, 0x72, 0x2e, 0x41, 0x4b, 0x53, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64,
	0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x13, 0x61, 0x6b, 0x73, 0x57, 0x6f, 0x72,
	0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x3f, 0x0a,
	0x0c, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x73, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61, 0x7a, 0x72,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x48,
	0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x73, 0x12, 0x34,
	0x0a, 0x14, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x5f, 0x61, 0x63, 0x63, 0x6f, 0x75, 0x6e,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x02, 0x18, 0x01,
	0x52, 0x12, 0x73, 0x74, 0x6f, 0x72, 0x61, 0x67, 0x65, 0x41, 0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74,
	0x4e, 0x61, 0x6d, 0x65, 0x42, 0x06, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68, 0x22, 0x6f, 0x0a, 0x0e,
	0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x12, 0x23,
	0x0a, 0x0d, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x53, 0x65, 0x63,
	0x72, 0x65, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x15, 0x0a,
	0x13, 0x41, 0x4b, 0x53, 0x57, 0x6f, 0x72, 0x6b, 0x6c, 0x6f, 0x61, 0x64, 0x49, 0x64, 0x65, 0x6e,
	0x74, 0x69, 0x74, 0x79, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62,
	0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67,
	0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x7a, 0x72, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_config_azr_proto_rawDescOnce sync.Once
	file_config_azr_proto_rawDescData []byte
)

func file_config_azr_proto_rawDescGZIP() []byte {
	file_config_azr_proto_rawDescOnce.Do(func() {
		file_config_azr_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_config_azr_proto_rawDesc), len(file_config_azr_proto_rawDesc)))
	})
	return file_config_azr_proto_rawDescData
}

var file_config_azr_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_config_azr_proto_goTypes = []any{
	(*Config)(nil),              // 0: config.azr.Config
	(*StaticCredAuth)(nil),      // 1: config.azr.StaticCredAuth
	(*AKSWorkloadIdentity)(nil), // 2: config.azr.AKSWorkloadIdentity
}
var file_config_azr_proto_depIdxs = []int32{
	2, // 0: config.azr.Config.aks_workload_identity:type_name -> config.azr.AKSWorkloadIdentity
	1, // 1: config.azr.Config.static_creds:type_name -> config.azr.StaticCredAuth
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_config_azr_proto_init() }
func file_config_azr_proto_init() {
	if File_config_azr_proto != nil {
		return
	}
	file_config_azr_proto_msgTypes[0].OneofWrappers = []any{
		(*Config_AksWorkloadIdentity)(nil),
		(*Config_StaticCreds)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_config_azr_proto_rawDesc), len(file_config_azr_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_azr_proto_goTypes,
		DependencyIndexes: file_config_azr_proto_depIdxs,
		MessageInfos:      file_config_azr_proto_msgTypes,
	}.Build()
	File_config_azr_proto = out.File
	file_config_azr_proto_goTypes = nil
	file_config_azr_proto_depIdxs = nil
}
