// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: config/aws.proto

package aws

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Config contains AWS configuration data for CloudAgent to initialize
// authetication to AWS services
type Config struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// AWS account id
	AccountId string `protobuf:"bytes,1,opt,name=account_id,json=accountId,proto3" json:"account_id,omitempty"`
	// The region of the AWS services agent uses for regional services
	Region string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	// OIDC provider of the EKS cluster agent is deployed to. Only required for
	// IAM related resources provisioning.
	OidcProvider string `protobuf:"bytes,3,opt,name=oidc_provider,json=oidcProvider,proto3" json:"oidc_provider,omitempty"`
	// Types that are valid to be assigned to Auth:
	//
	//	*Config_StaticCreds
	//	*Config_EksWebIdentity
	Auth  isConfig_Auth `protobuf_oneof:"auth"`
	VpcId string        `protobuf:"bytes,6,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	// default tags for AWS resources, the key and the value of a tag is divided
	// by the first colon.
	DefaultTags   []string `protobuf:"bytes,7,rep,name=default_tags,json=defaultTags,proto3" json:"default_tags,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_config_aws_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_aws_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_aws_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetAccountId() string {
	if x != nil {
		return x.AccountId
	}
	return ""
}

func (x *Config) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Config) GetOidcProvider() string {
	if x != nil {
		return x.OidcProvider
	}
	return ""
}

func (x *Config) GetAuth() isConfig_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Config) GetStaticCreds() *StaticCredAuth {
	if x != nil {
		if x, ok := x.Auth.(*Config_StaticCreds); ok {
			return x.StaticCreds
		}
	}
	return nil
}

func (x *Config) GetEksWebIdentity() *EKSWebIdendity {
	if x != nil {
		if x, ok := x.Auth.(*Config_EksWebIdentity); ok {
			return x.EksWebIdentity
		}
	}
	return nil
}

func (x *Config) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *Config) GetDefaultTags() []string {
	if x != nil {
		return x.DefaultTags
	}
	return nil
}

type isConfig_Auth interface {
	isConfig_Auth()
}

type Config_StaticCreds struct {
	// AWS access key + secret key auth
	StaticCreds *StaticCredAuth `protobuf:"bytes,4,opt,name=static_creds,json=staticCreds,proto3,oneof"`
}

type Config_EksWebIdentity struct {
	// EKS web identity auth -- requires the agent to be running by a K8s
	// service account configured to assume an IAM role.
	EksWebIdentity *EKSWebIdendity `protobuf:"bytes,5,opt,name=eks_web_identity,json=eksWebIdentity,proto3,oneof"`
}

func (*Config_StaticCreds) isConfig_Auth() {}

func (*Config_EksWebIdentity) isConfig_Auth() {}

// Auth option to use static access key to access AWS APIs.
// This is intended for test usage only. Do NOT use it in production
type StaticCredAuth struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	AccessKeyId     string                 `protobuf:"bytes,1,opt,name=access_key_id,json=accessKeyId,proto3" json:"access_key_id,omitempty"`
	SecretAccessKey string                 `protobuf:"bytes,2,opt,name=secret_access_key,json=secretAccessKey,proto3" json:"secret_access_key,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *StaticCredAuth) Reset() {
	*x = StaticCredAuth{}
	mi := &file_config_aws_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaticCredAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticCredAuth) ProtoMessage() {}

func (x *StaticCredAuth) ProtoReflect() protoreflect.Message {
	mi := &file_config_aws_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticCredAuth.ProtoReflect.Descriptor instead.
func (*StaticCredAuth) Descriptor() ([]byte, []int) {
	return file_config_aws_proto_rawDescGZIP(), []int{1}
}

func (x *StaticCredAuth) GetAccessKeyId() string {
	if x != nil {
		return x.AccessKeyId
	}
	return ""
}

func (x *StaticCredAuth) GetSecretAccessKey() string {
	if x != nil {
		return x.SecretAccessKey
	}
	return ""
}

// AWS auth option to use EKS OIDC provided identity to access AWS APIs
// The identity token should be retrieved through enviroment variables and this
// process should be handled automatically by AWS SDK.
type EKSWebIdendity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *EKSWebIdendity) Reset() {
	*x = EKSWebIdendity{}
	mi := &file_config_aws_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *EKSWebIdendity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EKSWebIdendity) ProtoMessage() {}

func (x *EKSWebIdendity) ProtoReflect() protoreflect.Message {
	mi := &file_config_aws_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EKSWebIdendity.ProtoReflect.Descriptor instead.
func (*EKSWebIdendity) Descriptor() ([]byte, []int) {
	return file_config_aws_proto_rawDescGZIP(), []int{2}
}

var File_config_aws_proto protoreflect.FileDescriptor

var file_config_aws_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61, 0x77, 0x73, 0x22, 0xaf,
	0x02, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x61, 0x63, 0x63,
	0x6f, 0x75, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x61,
	0x63, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e,
	0x12, 0x23, 0x0a, 0x0d, 0x6f, 0x69, 0x64, 0x63, 0x5f, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6f, 0x69, 0x64, 0x63, 0x50, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x12, 0x3f, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74, 0x69, 0x63, 0x5f,
	0x63, 0x72, 0x65, 0x64, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43,
	0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74, 0x61, 0x74, 0x69,
	0x63, 0x43, 0x72, 0x65, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x65, 0x6b, 0x73, 0x5f, 0x77, 0x65,
	0x62, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61, 0x77, 0x73, 0x2e, 0x45, 0x4b,
	0x53, 0x57, 0x65, 0x62, 0x49, 0x64, 0x65, 0x6e, 0x64, 0x69, 0x74, 0x79, 0x48, 0x00, 0x52, 0x0e,
	0x65, 0x6b, 0x73, 0x57, 0x65, 0x62, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x12, 0x15,
	0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x5f, 0x74, 0x61, 0x67, 0x73, 0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x54, 0x61, 0x67, 0x73, 0x42, 0x06, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68,
	0x22, 0x60, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x41, 0x75,
	0x74, 0x68, 0x12, 0x22, 0x0a, 0x0d, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x61, 0x63, 0x63, 0x65, 0x73,
	0x73, 0x4b, 0x65, 0x79, 0x49, 0x64, 0x12, 0x2a, 0x0a, 0x11, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74,
	0x5f, 0x61, 0x63, 0x63, 0x65, 0x73, 0x73, 0x5f, 0x6b, 0x65, 0x79, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0f, 0x73, 0x65, 0x63, 0x72, 0x65, 0x74, 0x41, 0x63, 0x63, 0x65, 0x73, 0x73, 0x4b,
	0x65, 0x79, 0x22, 0x10, 0x0a, 0x0e, 0x45, 0x4b, 0x53, 0x57, 0x65, 0x62, 0x49, 0x64, 0x65, 0x6e,
	0x64, 0x69, 0x74, 0x79, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62,
	0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67,
	0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x77, 0x73, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_config_aws_proto_rawDescOnce sync.Once
	file_config_aws_proto_rawDescData []byte
)

func file_config_aws_proto_rawDescGZIP() []byte {
	file_config_aws_proto_rawDescOnce.Do(func() {
		file_config_aws_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_config_aws_proto_rawDesc), len(file_config_aws_proto_rawDesc)))
	})
	return file_config_aws_proto_rawDescData
}

var file_config_aws_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_config_aws_proto_goTypes = []any{
	(*Config)(nil),         // 0: config.aws.Config
	(*StaticCredAuth)(nil), // 1: config.aws.StaticCredAuth
	(*EKSWebIdendity)(nil), // 2: config.aws.EKSWebIdendity
}
var file_config_aws_proto_depIdxs = []int32{
	1, // 0: config.aws.Config.static_creds:type_name -> config.aws.StaticCredAuth
	2, // 1: config.aws.Config.eks_web_identity:type_name -> config.aws.EKSWebIdendity
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_config_aws_proto_init() }
func file_config_aws_proto_init() {
	if File_config_aws_proto != nil {
		return
	}
	file_config_aws_proto_msgTypes[0].OneofWrappers = []any{
		(*Config_StaticCreds)(nil),
		(*Config_EksWebIdentity)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_config_aws_proto_rawDesc), len(file_config_aws_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_aws_proto_goTypes,
		DependencyIndexes: file_config_aws_proto_depIdxs,
		MessageInfos:      file_config_aws_proto_msgTypes,
	}.Build()
	File_config_aws_proto = out.File
	file_config_aws_proto_goTypes = nil
	file_config_aws_proto_depIdxs = nil
}
