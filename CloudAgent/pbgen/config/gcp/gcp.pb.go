// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: config/gcp.proto

package gcp

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Config contains GCP configuration data for CloudAgent to initialize
// authetication to GCP services
type Config struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// GCP projec id
	ProjectId string `protobuf:"bytes,1,opt,name=project_id,json=projectId,proto3" json:"project_id,omitempty"`
	// Types that are valid to be assigned to Auth:
	//
	//	*Config_StaticCreds
	//	*Config_GkeWebIdentity
	Auth isConfig_Auth `protobuf_oneof:"auth"`
	// The region of the GCP services agent uses for regional services
	Region string `protobuf:"bytes,4,opt,name=region,proto3" json:"region,omitempty"`
	// The self-link to the tenant vpc
	TenantVpc     string `protobuf:"bytes,5,opt,name=tenant_vpc,json=tenantVpc,proto3" json:"tenant_vpc,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_config_gcp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_gcp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_gcp_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetProjectId() string {
	if x != nil {
		return x.ProjectId
	}
	return ""
}

func (x *Config) GetAuth() isConfig_Auth {
	if x != nil {
		return x.Auth
	}
	return nil
}

func (x *Config) GetStaticCreds() *StaticCredAuth {
	if x != nil {
		if x, ok := x.Auth.(*Config_StaticCreds); ok {
			return x.StaticCreds
		}
	}
	return nil
}

func (x *Config) GetGkeWebIdentity() *GKEWebIdendity {
	if x != nil {
		if x, ok := x.Auth.(*Config_GkeWebIdentity); ok {
			return x.GkeWebIdentity
		}
	}
	return nil
}

func (x *Config) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *Config) GetTenantVpc() string {
	if x != nil {
		return x.TenantVpc
	}
	return ""
}

type isConfig_Auth interface {
	isConfig_Auth()
}

type Config_StaticCreds struct {
	// Auth with static Google service account key.
	StaticCreds *StaticCredAuth `protobuf:"bytes,2,opt,name=static_creds,json=staticCreds,proto3,oneof"`
}

type Config_GkeWebIdentity struct {
	// GKE web identity auth -- requires the agent to be running by a K8s
	// service account configured to assume Google service account.
	GkeWebIdentity *GKEWebIdendity `protobuf:"bytes,3,opt,name=gke_web_identity,json=gkeWebIdentity,proto3,oneof"`
}

func (*Config_StaticCreds) isConfig_Auth() {}

func (*Config_GkeWebIdentity) isConfig_Auth() {}

// Auth option to use static credential to access Google APIs.
// This is intended for test usage only. Do NOT use it in production
type StaticCredAuth struct {
	state                        protoimpl.MessageState `protogen:"open.v1"`
	GoogleApplicationCredentials string                 `protobuf:"bytes,1,opt,name=google_application_credentials,json=googleApplicationCredentials,proto3" json:"google_application_credentials,omitempty"`
	unknownFields                protoimpl.UnknownFields
	sizeCache                    protoimpl.SizeCache
}

func (x *StaticCredAuth) Reset() {
	*x = StaticCredAuth{}
	mi := &file_config_gcp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *StaticCredAuth) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StaticCredAuth) ProtoMessage() {}

func (x *StaticCredAuth) ProtoReflect() protoreflect.Message {
	mi := &file_config_gcp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StaticCredAuth.ProtoReflect.Descriptor instead.
func (*StaticCredAuth) Descriptor() ([]byte, []int) {
	return file_config_gcp_proto_rawDescGZIP(), []int{1}
}

func (x *StaticCredAuth) GetGoogleApplicationCredentials() string {
	if x != nil {
		return x.GoogleApplicationCredentials
	}
	return ""
}

// Auth option to use GKE workload identity to access Google APIs
// The identity token should be retrieved through enviroment variables and this
// process should be handled automatically by Google APIs SDK.
type GKEWebIdendity struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GKEWebIdendity) Reset() {
	*x = GKEWebIdendity{}
	mi := &file_config_gcp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GKEWebIdendity) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GKEWebIdendity) ProtoMessage() {}

func (x *GKEWebIdendity) ProtoReflect() protoreflect.Message {
	mi := &file_config_gcp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GKEWebIdendity.ProtoReflect.Descriptor instead.
func (*GKEWebIdendity) Descriptor() ([]byte, []int) {
	return file_config_gcp_proto_rawDescGZIP(), []int{2}
}

var File_config_gcp_proto protoreflect.FileDescriptor

var file_config_gcp_proto_rawDesc = string([]byte{
	0x0a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x67, 0x63, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x0a, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x67, 0x63, 0x70, 0x22, 0xef,
	0x01, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x6a, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x72, 0x6f, 0x6a, 0x65, 0x63, 0x74, 0x49, 0x64, 0x12, 0x3f, 0x0a, 0x0c, 0x73, 0x74, 0x61, 0x74,
	0x69, 0x63, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x41, 0x75, 0x74, 0x68, 0x48, 0x00, 0x52, 0x0b, 0x73, 0x74,
	0x61, 0x74, 0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x73, 0x12, 0x46, 0x0a, 0x10, 0x67, 0x6b, 0x65,
	0x5f, 0x77, 0x65, 0x62, 0x5f, 0x69, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74, 0x79, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x67, 0x63, 0x70,
	0x2e, 0x47, 0x4b, 0x45, 0x57, 0x65, 0x62, 0x49, 0x64, 0x65, 0x6e, 0x64, 0x69, 0x74, 0x79, 0x48,
	0x00, 0x52, 0x0e, 0x67, 0x6b, 0x65, 0x57, 0x65, 0x62, 0x49, 0x64, 0x65, 0x6e, 0x74, 0x69, 0x74,
	0x79, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x6e,
	0x61, 0x6e, 0x74, 0x5f, 0x76, 0x70, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x56, 0x70, 0x63, 0x42, 0x06, 0x0a, 0x04, 0x61, 0x75, 0x74, 0x68,
	0x22, 0x56, 0x0a, 0x0e, 0x53, 0x74, 0x61, 0x74, 0x69, 0x63, 0x43, 0x72, 0x65, 0x64, 0x41, 0x75,
	0x74, 0x68, 0x12, 0x44, 0x0a, 0x1e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x5f, 0x61, 0x70, 0x70,
	0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x63, 0x72, 0x65, 0x64, 0x65, 0x6e, 0x74,
	0x69, 0x61, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x1c, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x41, 0x70, 0x70, 0x6c, 0x69, 0x63, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x43, 0x72, 0x65,
	0x64, 0x65, 0x6e, 0x74, 0x69, 0x61, 0x6c, 0x73, 0x22, 0x10, 0x0a, 0x0e, 0x47, 0x4b, 0x45, 0x57,
	0x65, 0x62, 0x49, 0x64, 0x65, 0x6e, 0x64, 0x69, 0x74, 0x79, 0x42, 0x37, 0x5a, 0x35, 0x67, 0x69,
	0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e, 0x67, 0x77,
	0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61, 0x67, 0x65,
	0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f,
	0x67, 0x63, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_config_gcp_proto_rawDescOnce sync.Once
	file_config_gcp_proto_rawDescData []byte
)

func file_config_gcp_proto_rawDescGZIP() []byte {
	file_config_gcp_proto_rawDescOnce.Do(func() {
		file_config_gcp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_config_gcp_proto_rawDesc), len(file_config_gcp_proto_rawDesc)))
	})
	return file_config_gcp_proto_rawDescData
}

var file_config_gcp_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_config_gcp_proto_goTypes = []any{
	(*Config)(nil),         // 0: config.gcp.Config
	(*StaticCredAuth)(nil), // 1: config.gcp.StaticCredAuth
	(*GKEWebIdendity)(nil), // 2: config.gcp.GKEWebIdendity
}
var file_config_gcp_proto_depIdxs = []int32{
	1, // 0: config.gcp.Config.static_creds:type_name -> config.gcp.StaticCredAuth
	2, // 1: config.gcp.Config.gke_web_identity:type_name -> config.gcp.GKEWebIdendity
	2, // [2:2] is the sub-list for method output_type
	2, // [2:2] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_config_gcp_proto_init() }
func file_config_gcp_proto_init() {
	if File_config_gcp_proto != nil {
		return
	}
	file_config_gcp_proto_msgTypes[0].OneofWrappers = []any{
		(*Config_StaticCreds)(nil),
		(*Config_GkeWebIdentity)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_config_gcp_proto_rawDesc), len(file_config_gcp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_gcp_proto_goTypes,
		DependencyIndexes: file_config_gcp_proto_depIdxs,
		MessageInfos:      file_config_gcp_proto_msgTypes,
	}.Build()
	File_config_gcp_proto = out.File
	file_config_gcp_proto_goTypes = nil
	file_config_gcp_proto_depIdxs = nil
}
