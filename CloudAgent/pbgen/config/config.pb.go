// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: config/config.proto

package config

import (
	aws "github.com/risingwavelabs/cloudagent/pbgen/config/aws"
	azr "github.com/risingwavelabs/cloudagent/pbgen/config/azr"
	gcp "github.com/risingwavelabs/cloudagent/pbgen/config/gcp"
	k8s "github.com/risingwavelabs/cloudagent/pbgen/config/k8s"
	local "github.com/risingwavelabs/cloudagent/pbgen/config/local"
	telemetry "github.com/risingwavelabs/cloudagent/pbgen/config/telemetry"
	tls "github.com/risingwavelabs/cloudagent/pbgen/config/tls"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Config contains configuration data for CloudAgent to initialize the service.
type Config struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Configuration for K8s cluster authentication.
	K8SConfig *k8s.Config `protobuf:"bytes,1,opt,name=k8s_config,json=k8sConfig,proto3" json:"k8s_config,omitempty"`
	// Configuration for cloud platform authentication.
	//
	// Types that are valid to be assigned to CloudProviderConfig:
	//
	//	*Config_AwsConfig
	//	*Config_GcpConfig
	//	*Config_AzrConfig
	//	*Config_LocalConfig
	CloudProviderConfig isConfig_CloudProviderConfig `protobuf_oneof:"cloud_provider_config"`
	TlsConfig           *tls.Config                  `protobuf:"bytes,4,opt,name=tls_config,json=tlsConfig,proto3" json:"tls_config,omitempty"`
	TelemetryConfig     *telemetry.Config            `protobuf:"bytes,6,opt,name=telemetry_config,json=telemetryConfig,proto3" json:"telemetry_config,omitempty"`
	unknownFields       protoimpl.UnknownFields
	sizeCache           protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_config_config_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_config_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_config_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetK8SConfig() *k8s.Config {
	if x != nil {
		return x.K8SConfig
	}
	return nil
}

func (x *Config) GetCloudProviderConfig() isConfig_CloudProviderConfig {
	if x != nil {
		return x.CloudProviderConfig
	}
	return nil
}

func (x *Config) GetAwsConfig() *aws.Config {
	if x != nil {
		if x, ok := x.CloudProviderConfig.(*Config_AwsConfig); ok {
			return x.AwsConfig
		}
	}
	return nil
}

func (x *Config) GetGcpConfig() *gcp.Config {
	if x != nil {
		if x, ok := x.CloudProviderConfig.(*Config_GcpConfig); ok {
			return x.GcpConfig
		}
	}
	return nil
}

func (x *Config) GetAzrConfig() *azr.Config {
	if x != nil {
		if x, ok := x.CloudProviderConfig.(*Config_AzrConfig); ok {
			return x.AzrConfig
		}
	}
	return nil
}

func (x *Config) GetLocalConfig() *local.Config {
	if x != nil {
		if x, ok := x.CloudProviderConfig.(*Config_LocalConfig); ok {
			return x.LocalConfig
		}
	}
	return nil
}

func (x *Config) GetTlsConfig() *tls.Config {
	if x != nil {
		return x.TlsConfig
	}
	return nil
}

func (x *Config) GetTelemetryConfig() *telemetry.Config {
	if x != nil {
		return x.TelemetryConfig
	}
	return nil
}

type isConfig_CloudProviderConfig interface {
	isConfig_CloudProviderConfig()
}

type Config_AwsConfig struct {
	AwsConfig *aws.Config `protobuf:"bytes,2,opt,name=aws_config,json=awsConfig,proto3,oneof"`
}

type Config_GcpConfig struct {
	GcpConfig *gcp.Config `protobuf:"bytes,3,opt,name=gcp_config,json=gcpConfig,proto3,oneof"`
}

type Config_AzrConfig struct {
	AzrConfig *azr.Config `protobuf:"bytes,7,opt,name=azr_config,json=azrConfig,proto3,oneof"`
}

type Config_LocalConfig struct {
	LocalConfig *local.Config `protobuf:"bytes,5,opt,name=local_config,json=localConfig,proto3,oneof"`
}

func (*Config_AwsConfig) isConfig_CloudProviderConfig() {}

func (*Config_GcpConfig) isConfig_CloudProviderConfig() {}

func (*Config_AzrConfig) isConfig_CloudProviderConfig() {}

func (*Config_LocalConfig) isConfig_CloudProviderConfig() {}

var File_config_config_proto protoreflect.FileDescriptor

var file_config_config_proto_rawDesc = string([]byte{
	0x0a, 0x13, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x06, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x1a, 0x10, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x77, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x67, 0x63, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x61, 0x7a, 0x72, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x6b, 0x38, 0x73, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x74, 0x6c,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x12, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x16, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x22, 0xa6, 0x03, 0x0a, 0x06, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x31,
	0x0a, 0x0a, 0x6b, 0x38, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x6b, 0x38, 0x73, 0x2e,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x09, 0x6b, 0x38, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x12, 0x33, 0x0a, 0x0a, 0x61, 0x77, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61,
	0x77, 0x73, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x61, 0x77, 0x73,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x0a, 0x67, 0x63, 0x70, 0x5f, 0x63, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x12, 0x2e, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x2e, 0x67, 0x63, 0x70, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00,
	0x52, 0x09, 0x67, 0x63, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x33, 0x0a, 0x0a, 0x61,
	0x7a, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x61, 0x7a, 0x72, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x61, 0x7a, 0x72, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x39, 0x0a, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x14, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x2e, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0b,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x31, 0x0a, 0x0a, 0x74,
	0x6c, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x12, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x74, 0x6c, 0x73, 0x2e, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x52, 0x09, 0x74, 0x6c, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x43,
	0x0a, 0x10, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x18, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x0f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x42, 0x17, 0x0a, 0x15, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x5f, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x42, 0x33, 0x5a, 0x31,
	0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72, 0x69, 0x73, 0x69, 0x6e,
	0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c, 0x6f, 0x75, 0x64, 0x61,
	0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_config_config_proto_rawDescOnce sync.Once
	file_config_config_proto_rawDescData []byte
)

func file_config_config_proto_rawDescGZIP() []byte {
	file_config_config_proto_rawDescOnce.Do(func() {
		file_config_config_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_config_config_proto_rawDesc), len(file_config_config_proto_rawDesc)))
	})
	return file_config_config_proto_rawDescData
}

var file_config_config_proto_msgTypes = make([]protoimpl.MessageInfo, 1)
var file_config_config_proto_goTypes = []any{
	(*Config)(nil),           // 0: config.Config
	(*k8s.Config)(nil),       // 1: config.k8s.Config
	(*aws.Config)(nil),       // 2: config.aws.Config
	(*gcp.Config)(nil),       // 3: config.gcp.Config
	(*azr.Config)(nil),       // 4: config.azr.Config
	(*local.Config)(nil),     // 5: config.local.Config
	(*tls.Config)(nil),       // 6: config.tls.Config
	(*telemetry.Config)(nil), // 7: config.telemetry.Config
}
var file_config_config_proto_depIdxs = []int32{
	1, // 0: config.Config.k8s_config:type_name -> config.k8s.Config
	2, // 1: config.Config.aws_config:type_name -> config.aws.Config
	3, // 2: config.Config.gcp_config:type_name -> config.gcp.Config
	4, // 3: config.Config.azr_config:type_name -> config.azr.Config
	5, // 4: config.Config.local_config:type_name -> config.local.Config
	6, // 5: config.Config.tls_config:type_name -> config.tls.Config
	7, // 6: config.Config.telemetry_config:type_name -> config.telemetry.Config
	7, // [7:7] is the sub-list for method output_type
	7, // [7:7] is the sub-list for method input_type
	7, // [7:7] is the sub-list for extension type_name
	7, // [7:7] is the sub-list for extension extendee
	0, // [0:7] is the sub-list for field type_name
}

func init() { file_config_config_proto_init() }
func file_config_config_proto_init() {
	if File_config_config_proto != nil {
		return
	}
	file_config_config_proto_msgTypes[0].OneofWrappers = []any{
		(*Config_AwsConfig)(nil),
		(*Config_GcpConfig)(nil),
		(*Config_AzrConfig)(nil),
		(*Config_LocalConfig)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_config_config_proto_rawDesc), len(file_config_config_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   1,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_config_proto_goTypes,
		DependencyIndexes: file_config_config_proto_depIdxs,
		MessageInfos:      file_config_config_proto_msgTypes,
	}.Build()
	File_config_config_proto = out.File
	file_config_config_proto_goTypes = nil
	file_config_config_proto_depIdxs = nil
}
