// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.5
// 	protoc        v6.30.1
// source: config/telemetry.proto

package telemetry

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Config contains AWS configuration data for CloudAgent to initialize
// authetication to AWS services
type Config struct {
	state protoimpl.MessageState `protogen:"open.v1"`
	// Types that are valid to be assigned to MetricsConfig:
	//
	//	*Config_AmpConfig
	//	*Config_GmpConfig
	//	*Config_LocalPrometheusConfig
	//	*Config_AzmpConfig
	MetricsConfig isConfig_MetricsConfig `protobuf_oneof:"metrics_config"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Config) Reset() {
	*x = Config{}
	mi := &file_config_telemetry_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Config) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Config) ProtoMessage() {}

func (x *Config) ProtoReflect() protoreflect.Message {
	mi := &file_config_telemetry_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Config.ProtoReflect.Descriptor instead.
func (*Config) Descriptor() ([]byte, []int) {
	return file_config_telemetry_proto_rawDescGZIP(), []int{0}
}

func (x *Config) GetMetricsConfig() isConfig_MetricsConfig {
	if x != nil {
		return x.MetricsConfig
	}
	return nil
}

func (x *Config) GetAmpConfig() *AWSManagedPrometheusConfig {
	if x != nil {
		if x, ok := x.MetricsConfig.(*Config_AmpConfig); ok {
			return x.AmpConfig
		}
	}
	return nil
}

func (x *Config) GetGmpConfig() *GoogleManagedPrometheusConfig {
	if x != nil {
		if x, ok := x.MetricsConfig.(*Config_GmpConfig); ok {
			return x.GmpConfig
		}
	}
	return nil
}

func (x *Config) GetLocalPrometheusConfig() *LocalManagedPrometheusConfig {
	if x != nil {
		if x, ok := x.MetricsConfig.(*Config_LocalPrometheusConfig); ok {
			return x.LocalPrometheusConfig
		}
	}
	return nil
}

func (x *Config) GetAzmpConfig() *AzureManagedPrometheusConfig {
	if x != nil {
		if x, ok := x.MetricsConfig.(*Config_AzmpConfig); ok {
			return x.AzmpConfig
		}
	}
	return nil
}

type isConfig_MetricsConfig interface {
	isConfig_MetricsConfig()
}

type Config_AmpConfig struct {
	AmpConfig *AWSManagedPrometheusConfig `protobuf:"bytes,1,opt,name=amp_config,json=ampConfig,proto3,oneof"`
}

type Config_GmpConfig struct {
	GmpConfig *GoogleManagedPrometheusConfig `protobuf:"bytes,2,opt,name=gmp_config,json=gmpConfig,proto3,oneof"`
}

type Config_LocalPrometheusConfig struct {
	LocalPrometheusConfig *LocalManagedPrometheusConfig `protobuf:"bytes,3,opt,name=local_prometheus_config,json=localPrometheusConfig,proto3,oneof"`
}

type Config_AzmpConfig struct {
	AzmpConfig *AzureManagedPrometheusConfig `protobuf:"bytes,4,opt,name=azmp_config,json=azmpConfig,proto3,oneof"`
}

func (*Config_AmpConfig) isConfig_MetricsConfig() {}

func (*Config_GmpConfig) isConfig_MetricsConfig() {}

func (*Config_LocalPrometheusConfig) isConfig_MetricsConfig() {}

func (*Config_AzmpConfig) isConfig_MetricsConfig() {}

// We assume the AMP is provisioned in the same AWS account/region of the
// cluster.
type AWSManagedPrometheusConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WorkspaceId   string                 `protobuf:"bytes,1,opt,name=workspace_id,json=workspaceId,proto3" json:"workspace_id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AWSManagedPrometheusConfig) Reset() {
	*x = AWSManagedPrometheusConfig{}
	mi := &file_config_telemetry_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AWSManagedPrometheusConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AWSManagedPrometheusConfig) ProtoMessage() {}

func (x *AWSManagedPrometheusConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_telemetry_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AWSManagedPrometheusConfig.ProtoReflect.Descriptor instead.
func (*AWSManagedPrometheusConfig) Descriptor() ([]byte, []int) {
	return file_config_telemetry_proto_rawDescGZIP(), []int{1}
}

func (x *AWSManagedPrometheusConfig) GetWorkspaceId() string {
	if x != nil {
		return x.WorkspaceId
	}
	return ""
}

// We assume the GMP is provisioned in the same GCP project of the cluster.
type GoogleManagedPrometheusConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GoogleManagedPrometheusConfig) Reset() {
	*x = GoogleManagedPrometheusConfig{}
	mi := &file_config_telemetry_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GoogleManagedPrometheusConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GoogleManagedPrometheusConfig) ProtoMessage() {}

func (x *GoogleManagedPrometheusConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_telemetry_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GoogleManagedPrometheusConfig.ProtoReflect.Descriptor instead.
func (*GoogleManagedPrometheusConfig) Descriptor() ([]byte, []int) {
	return file_config_telemetry_proto_rawDescGZIP(), []int{2}
}

// We assume the AZP is provisioned in the same GCP project of the cluster.
type AzureManagedPrometheusConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	QueryEndpoint string                 `protobuf:"bytes,1,opt,name=query_endpoint,json=queryEndpoint,proto3" json:"query_endpoint,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AzureManagedPrometheusConfig) Reset() {
	*x = AzureManagedPrometheusConfig{}
	mi := &file_config_telemetry_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AzureManagedPrometheusConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AzureManagedPrometheusConfig) ProtoMessage() {}

func (x *AzureManagedPrometheusConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_telemetry_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AzureManagedPrometheusConfig.ProtoReflect.Descriptor instead.
func (*AzureManagedPrometheusConfig) Descriptor() ([]byte, []int) {
	return file_config_telemetry_proto_rawDescGZIP(), []int{3}
}

func (x *AzureManagedPrometheusConfig) GetQueryEndpoint() string {
	if x != nil {
		return x.QueryEndpoint
	}
	return ""
}

// Will request the local Prometheus service in the cluster.
type LocalManagedPrometheusConfig struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LocalManagedPrometheusConfig) Reset() {
	*x = LocalManagedPrometheusConfig{}
	mi := &file_config_telemetry_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LocalManagedPrometheusConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LocalManagedPrometheusConfig) ProtoMessage() {}

func (x *LocalManagedPrometheusConfig) ProtoReflect() protoreflect.Message {
	mi := &file_config_telemetry_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LocalManagedPrometheusConfig.ProtoReflect.Descriptor instead.
func (*LocalManagedPrometheusConfig) Descriptor() ([]byte, []int) {
	return file_config_telemetry_proto_rawDescGZIP(), []int{4}
}

func (x *LocalManagedPrometheusConfig) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

var File_config_telemetry_proto protoreflect.FileDescriptor

var file_config_telemetry_proto_rawDesc = string([]byte{
	0x0a, 0x16, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74,
	0x72, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x10, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x22, 0xf8, 0x02, 0x0a, 0x06, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x4d, 0x0a, 0x0a, 0x61, 0x6d, 0x70, 0x5f, 0x63, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x41, 0x57, 0x53,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x61, 0x6d, 0x70, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x12, 0x50, 0x0a, 0x0a, 0x67, 0x6d, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x47, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68,
	0x65, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x09, 0x67, 0x6d, 0x70,
	0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x68, 0x0a, 0x17, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f,
	0x70, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69,
	0x67, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x2e, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x4c, 0x6f, 0x63, 0x61, 0x6c,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x15, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67,
	0x12, 0x51, 0x0a, 0x0b, 0x61, 0x7a, 0x6d, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x74,
	0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x2e, 0x41, 0x7a, 0x75, 0x72, 0x65, 0x4d, 0x61,
	0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x48, 0x00, 0x52, 0x0a, 0x61, 0x7a, 0x6d, 0x70, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x42, 0x10, 0x0a, 0x0e, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x5f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x3f, 0x0a, 0x1a, 0x41, 0x57, 0x53, 0x4d, 0x61, 0x6e, 0x61,
	0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x43, 0x6f, 0x6e,
	0x66, 0x69, 0x67, 0x12, 0x21, 0x0a, 0x0c, 0x77, 0x6f, 0x72, 0x6b, 0x73, 0x70, 0x61, 0x63, 0x65,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x77, 0x6f, 0x72, 0x6b, 0x73,
	0x70, 0x61, 0x63, 0x65, 0x49, 0x64, 0x22, 0x1f, 0x0a, 0x1d, 0x47, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x45, 0x0a, 0x1c, 0x41, 0x7a, 0x75, 0x72, 0x65,
	0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72, 0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75,
	0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x25, 0x0a, 0x0e, 0x71, 0x75, 0x65, 0x72, 0x79,
	0x5f, 0x65, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x71, 0x75, 0x65, 0x72, 0x79, 0x45, 0x6e, 0x64, 0x70, 0x6f, 0x69, 0x6e, 0x74, 0x22, 0x30,
	0x0a, 0x1c, 0x4c, 0x6f, 0x63, 0x61, 0x6c, 0x4d, 0x61, 0x6e, 0x61, 0x67, 0x65, 0x64, 0x50, 0x72,
	0x6f, 0x6d, 0x65, 0x74, 0x68, 0x65, 0x75, 0x73, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x72, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x72, 0x6c,
	0x42, 0x3d, 0x5a, 0x3b, 0x67, 0x69, 0x74, 0x68, 0x75, 0x62, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x72,
	0x69, 0x73, 0x69, 0x6e, 0x67, 0x77, 0x61, 0x76, 0x65, 0x6c, 0x61, 0x62, 0x73, 0x2f, 0x63, 0x6c,
	0x6f, 0x75, 0x64, 0x61, 0x67, 0x65, 0x6e, 0x74, 0x2f, 0x70, 0x62, 0x67, 0x65, 0x6e, 0x2f, 0x63,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2f, 0x74, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x74, 0x72, 0x79, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
})

var (
	file_config_telemetry_proto_rawDescOnce sync.Once
	file_config_telemetry_proto_rawDescData []byte
)

func file_config_telemetry_proto_rawDescGZIP() []byte {
	file_config_telemetry_proto_rawDescOnce.Do(func() {
		file_config_telemetry_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_config_telemetry_proto_rawDesc), len(file_config_telemetry_proto_rawDesc)))
	})
	return file_config_telemetry_proto_rawDescData
}

var file_config_telemetry_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_config_telemetry_proto_goTypes = []any{
	(*Config)(nil),                        // 0: config.telemetry.Config
	(*AWSManagedPrometheusConfig)(nil),    // 1: config.telemetry.AWSManagedPrometheusConfig
	(*GoogleManagedPrometheusConfig)(nil), // 2: config.telemetry.GoogleManagedPrometheusConfig
	(*AzureManagedPrometheusConfig)(nil),  // 3: config.telemetry.AzureManagedPrometheusConfig
	(*LocalManagedPrometheusConfig)(nil),  // 4: config.telemetry.LocalManagedPrometheusConfig
}
var file_config_telemetry_proto_depIdxs = []int32{
	1, // 0: config.telemetry.Config.amp_config:type_name -> config.telemetry.AWSManagedPrometheusConfig
	2, // 1: config.telemetry.Config.gmp_config:type_name -> config.telemetry.GoogleManagedPrometheusConfig
	4, // 2: config.telemetry.Config.local_prometheus_config:type_name -> config.telemetry.LocalManagedPrometheusConfig
	3, // 3: config.telemetry.Config.azmp_config:type_name -> config.telemetry.AzureManagedPrometheusConfig
	4, // [4:4] is the sub-list for method output_type
	4, // [4:4] is the sub-list for method input_type
	4, // [4:4] is the sub-list for extension type_name
	4, // [4:4] is the sub-list for extension extendee
	0, // [0:4] is the sub-list for field type_name
}

func init() { file_config_telemetry_proto_init() }
func file_config_telemetry_proto_init() {
	if File_config_telemetry_proto != nil {
		return
	}
	file_config_telemetry_proto_msgTypes[0].OneofWrappers = []any{
		(*Config_AmpConfig)(nil),
		(*Config_GmpConfig)(nil),
		(*Config_LocalPrometheusConfig)(nil),
		(*Config_AzmpConfig)(nil),
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_config_telemetry_proto_rawDesc), len(file_config_telemetry_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_config_telemetry_proto_goTypes,
		DependencyIndexes: file_config_telemetry_proto_depIdxs,
		MessageInfos:      file_config_telemetry_proto_msgTypes,
	}.Build()
	File_config_telemetry_proto = out.File
	file_config_telemetry_proto_goTypes = nil
	file_config_telemetry_proto_depIdxs = nil
}
