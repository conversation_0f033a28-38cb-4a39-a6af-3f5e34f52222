import boto3
import logging

from .base import (
    CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY,
    KIND_KUBECTL_CONTEXT,
    CLOUDAGENT_TESTING_NAMESPACE,
    CLOUDAGENT_NAMESPACE,
    AWS_REGION,
    get_aws_arn,
    get_aws_credentials,
    get_testing_s3_bucket_name,
    get_ec2_client,
    get_s3_client,
)
from utils.command import run_command
from utils.k8s import delete_namespace as k8s_delete_namespace
from utils.k8s import delete_all_jobs
from utils.resource import wait
import utils.resource


def get_iam_client() -> boto3.client:
    """
    API References: https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/iam.html
    """
    access_key_id, secret_access_key = get_aws_credentials()
    client = boto3.client(
        "iam",
        aws_access_key_id=access_key_id,
        aws_secret_access_key=secret_access_key,
        region_name=AWS_REGION,
    )
    return client


def clean_vpc_endpoints():
    c = get_ec2_client()

    res = c.describe_vpc_endpoints(
        Filters=[
            {
                "Name": "tag:{}".format(CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY),
                "Values": [get_aws_arn()],
            },
        ]
    )

    ids = []
    for item in res["VpcEndpoints"]:
        ids.append(item["VpcEndpointId"])

    logging.info(
        "VPC endpoints to delete: {}. (Note that VPC endpoint deletion is asynchronous, these VPC endpoints could be in deleting status already)".format(
            ids
        )
    )

    if len(ids) == 0:
        c.close()
        return

    res = c.delete_vpc_endpoints(VpcEndpointIds=ids)
    if res["ResponseMetadata"]["HTTPStatusCode"] != 200:
        logging.warn(
            "failed to delete VPC endpoints: {}, response payload: {}".format(ids, res)
        )
    else:
        logging.info("VPC endpoints {} are deleted".format(ids))

    c.close()


def delete_s3_bucket():
    name = get_testing_s3_bucket_name()
    access_key_id, secret_access_key = get_aws_credentials()
    c = get_s3_client()  # get exception type, since exceptions are loaded in runtime
    try:
        bucket = boto3.resource(
            "s3",
            aws_access_key_id=access_key_id,
            aws_secret_access_key=secret_access_key,
            region_name=AWS_REGION,
        ).Bucket(name)
        bucket.objects.all().delete()

        def delete_bucket():
            try:
                bucket.delete()
            except c.exceptions.NoSuchBucket:
                return True
            except Exception as e:
                logging.warning("failed to delete s3 bucket: {}, retrying...".format(e))
                return False

        wait(f=delete_bucket, timeout_secs=30)
        logging.info("s3 bucket {} is deleted.".format(name))
    except c.exceptions.NoSuchBucket as e:
        logging.info("s3 bucket {} does not exist".format(name))


def clean_security_group():
    c = get_ec2_client()

    result: dict = c.describe_security_groups(
        Filters=[
            {
                "Name": "tag:{}".format(CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY),
                "Values": [get_aws_arn()],
            }
        ]
    )

    if result["ResponseMetadata"]["HTTPStatusCode"] != 200:
        raise RuntimeError(
            "failed to describe security groups, response: " + str(result)
        )

    sgList = []
    for item in result["SecurityGroups"]:
        sgList.append((item["GroupId"], item["GroupName"]))

    logging.info("found {} security groups".format(len(sgList)))

    not_deleted_list = []
    for sg in sgList:
        logging.info(
            "deleting security group, id: {id}, name: {name}".format(
                id=sg[0], name=sg[1]
            )
        )
        res = c.delete_security_group(GroupId=sg[0])
        if (
            result["ResponseMetadata"] != None
            and result["ResponseMetadata"]["HTTPStatusCode"] == 200
        ):
            logging.info("{} is deleted".format(sg[0]))
        else:
            not_deleted_list.append((sg[0], result["ResponseMetadata"]))

    if len(not_deleted_list) > 0:
        logging.warn("failed to delete security groups:")
        for item in not_deleted_list:
            logging.warn("id: {id}, response: {res}".format(id=item[0], res=item[1]))

    c.close()


def clean_iam_policy():
    c = get_iam_client()

    paginator = c.get_paginator("list_policies")
    count = 0
    for response in paginator.paginate():
        for policy in response["Policies"]:
            if (
                "Tags" in policy
                and (CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY, get_aws_arn())
                in policy["Tags"]
            ):
                c.delete_policy(PolicyArn=policy["Arn"])
                logging.info("Policy {} is deleted".format(policy["PolicyName"]))
                count += 1
    logging.info("deleted {} IAM policies".format(count))


def clean_iam_role():
    c = get_iam_client()

    paginator = c.get_paginator("list_roles")
    count = 0
    for response in paginator.paginate():
        for role in response["Roles"]:
            if (
                "Tags" in role
                and (CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY, get_aws_arn())
                in role["Tags"]
            ):
                c.delete_role(RoleName=role["RoleName"])
                logging.info("Role {} is deleted".format(role["RoleName"]))
                count += 1
    logging.info("deleted {} IAM roles".format(count))


def delete_namespace():
    k8s_delete_namespace(CLOUDAGENT_TESTING_NAMESPACE, ignore_not_found=True)


def delete_tasks():
    delete_all_jobs(CLOUDAGENT_NAMESPACE)


def cleanup():
    logging.info("deleting namespace {}".format(CLOUDAGENT_TESTING_NAMESPACE))
    try:
        delete_namespace()
    except Exception as e:
        logging.warn(
            "skipping safe cleanup procedure, this occurs when the kind cluster is not reachable: failed to clean up resources by deleting Kubernetes resources: {}".format(
                str(e)
            )
        )

    logging.info("deleting all jobs in cloudagent namespace")
    try:
        delete_tasks()
    except Exception as e:
        logging.warn(
            "skipping safe cleanup procedure, this occurs when the kind cluster is not reachable: failed to clean up resources by deleting Kubernetes resources: {}".format(
                str(e)
            )
        )

    logging.info(
        "starting to clean up all AWS resources created by {}".format(get_aws_arn())
    )

    clean_security_group()

    clean_vpc_endpoints()

    logging.info("deleting s3 bucket")

    try:
        delete_s3_bucket()
    except Exception as e:
        print(e)
        logging.error("failed to delete s3 bucket")

    try:
        clean_iam_role()
    except Exception as e:
        print(e)
        logging.error("failed to delete iam role")

    try:
        clean_iam_policy()
    except Exception as e:
        print(e)
        logging.error("failed to delete iam policy")
