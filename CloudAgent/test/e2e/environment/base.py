import json
import logging
import os
import subprocess
import sys
import tempfile
import threading
import boto3
import platform

from functools import lru_cache

import google.auth
from google.auth import impersonated_credentials
from google.cloud import secretmanager
from kind.cluster import create_cluster, delete_cluster
from utils.command import run_command
from utils.threads import run_parallel

logger = logging.getLogger(__name__)

PROJECT_ROOT_PATH = os.path.abspath(
    os.path.join(os.path.dirname(__file__), "..", "..", "..")
)
BIN_PATH = PROJECT_ROOT_PATH + "/bin"
E2E_TEST_FOLDER_PATH = PROJECT_ROOT_PATH + "/test/e2e"
TEMPLATE_PATH = E2E_TEST_FOLDER_PATH + "/templates"

CLOUDAGENT_SHELL_CACHE_PATH = E2E_TEST_FOLDER_PATH + "/.cache"
RESOURCES_PATH = E2E_TEST_FOLDER_PATH + "/resources"
CLOUDAGENT_SHELL_CONTAINER_TAG = "local.registry/cloudagent:latest"
CLOUDAGENT_SHELL_DOCKERFILE_PATH = E2E_TEST_FOLDER_PATH + "/shell.Dockerfile"
CLOUDAGENT_SHELL_DEPLOYMENT_FILE_PATH = E2E_TEST_FOLDER_PATH + "/templates/deploy.yml"

TASK_RUNNER_IMAHE_TAG = "local.registry/taskrunner:latest"
TASK_RUNNER_SHELL_DOCKERFILE_PATH = E2E_TEST_FOLDER_PATH + "/shelltask.Dockerfile"

CLOUDAGENT_CONFIG_TEMPLATE_PATH = TEMPLATE_PATH + "/config.e2e.textproto.template"
CLOUDAGENT_CONFIG_OUTPUT_PATH = E2E_TEST_FOLDER_PATH + "/tmp/config.e2e.textproto"
CLOUDAGENT_NODE_PORT = 30120
METRICS_NODE_PORT = 30121
CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY = "cloudagent.risingwave.cloud/test-owner-arn"

CLOUDAGENT_CLIENT_TLS_CERT_PATH = PROJECT_ROOT_PATH + "/certs/tls2.crt"
CLOUDAGENT_CLIENT_TLS_PRIVATE_KEY_PATH = PROJECT_ROOT_PATH + "/certs/tls2.key"
CLOUDAGENT_CLIENT_TLS_ROOT_CERT_PATH = PROJECT_ROOT_PATH + "/certs/ca.crt"

CLOUDAGENT_TESTING_NAMESPACE = "test"

KIND_CONFIG_TEMPLATE_PATH = E2E_TEST_FOLDER_PATH + "/templates/kind.yml"
KIND_NAME = "cloudagent-test"
KIND_KUBECTL_CONTEXT = "kind-" + KIND_NAME

AWS_REGION = "us-west-2"
AWS_S3_BUCKET_DEV = "cloud-local-dev"

ACK_SYSTEM_NAMESPACE = "ack-system"
CROSSPLANE_NAMESPACE = "crossplane-system"

CNRM_NAMESPACE = "cnrm-system"
CONFIG_CONNECTOR_MANIFEST = E2E_TEST_FOLDER_PATH + "/templates/configconnector.yaml"
CONFIG_CONNECTOR_RESOURCE_URL = (
    "gs://configconnector-operator/latest/release-bundle.tar.gz"
)
GCP_CLOUD_AGENT_ACC = "cloud-agent-local-dev"
GCP_CLOUD_AGENT_SECRET_ID = "cloud-agent-local-dev-key"
GCP_DEV_PROJECT = "rwcdev"
GCP_OWNER_EMAIL = "<EMAIL>"
GCP_SECRET_NAME = "gcpsecret"

ACK_IAM_VERSION = "1.2.1"
ACK_EC2_VERSION = "1.0.3"
CROSSPLANE_SYSTEM_VERSION = "v1.12.1"
CROSSPLANE_PROVIDER_AWS_VERSION = "v0.40.0"

CERT_MANAGER_VERSION = "v1.8.0"

RISINGWAVE_VERSION = "v2.0.1"
RISINGWAVE_OPERATOR_VERSION = "v0.8.2"

PSQL_CLIENT_IMAGE_TAG = "local/psql-client:latest"

CLOUDAGENT_NAMESPACE = "cloudagent"
CLOUDAGENT_CLUSTER_ROLE_NAME = "cloudagent"
CLOUDAGENT_SERVICE_ACCOUNT_NAME = "cloudagent"

DATAGEN_IMAGE = "ghcr.io/risingwavelabs/demo-datagen:v1.0.9"


def get_s3_client() -> boto3.client:
    """
    API References: https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/s3.html
    """
    access_key_id, secret_access_key = get_aws_credentials()
    client = boto3.client(
        "s3",
        aws_access_key_id=access_key_id,
        aws_secret_access_key=secret_access_key,
        region_name=AWS_REGION,
    )
    return client


def get_ec2_client() -> boto3.client:
    """
    API References: https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/ec2.html
    """
    access_key_id, secret_access_key = get_aws_credentials()
    client = boto3.client(
        "ec2",
        aws_access_key_id=access_key_id,
        aws_secret_access_key=secret_access_key,
        region_name=AWS_REGION,
    )
    return client


@lru_cache()
def get_default_vpc():
    c = get_ec2_client()
    paginator = c.get_paginator("describe_vpcs")
    res_iter = paginator.paginate(
        PaginationConfig={
            "MaxItems": 100,
            "PageSize": 10,
        },
    )
    for res in res_iter:
        for vpc in res["Vpcs"]:
            if vpc["IsDefault"]:
                c.close()
                return vpc["VpcId"]
    c.close()
    raise Exception("default VPC is not found")


def deploy_ack():
    """
    Follow the instructions here: https://aws-controllers-k8s.github.io/community/docs/user-docs/install/
    """
    ecr_pwd_proc = subprocess.Popen(
        ["aws", "ecr-public", "get-login-password", "--region", "us-east-1"],
        stdout=subprocess.PIPE,
    )

    helm_login_proc = subprocess.run(
        [
            "helm",
            "registry",
            "login",
            "--username",
            "AWS",
            "--password-stdin",
            "public.ecr.aws",
        ],
        stdin=ecr_pwd_proc.stdout,
        stdout=sys.stdout,
    )

    if helm_login_proc.returncode != 0:
        raise RuntimeError("failed to login aws ecr helm registry")

    access_key, secret_key = get_aws_credentials()

    def deploy_svc(svc: str, version: str):
        run_command(
            [
                "helm",
                "install",
                "--create-namespace",
                "-n",
                ACK_SYSTEM_NAMESPACE,
                "ack-{}-controller".format(svc),
                "oci://public.ecr.aws/aws-controllers-k8s/{}-chart".format(svc),
                "--version",
                version,
                "--set=aws.region={}".format(AWS_REGION),
            ]
        )
        run_command(
            [
                "kubectl",
                "--context",
                KIND_KUBECTL_CONTEXT,
                "patch",
                "deployment",
                "ack-{svc}-controller-{svc}-chart".format(svc=svc),
                "-n",
                ACK_SYSTEM_NAMESPACE,
                "-p",
                json.dumps(
                    {
                        "spec": {
                            "template": {
                                "spec": {
                                    "containers": [
                                        {
                                            "name": "controller",
                                            "env": [
                                                {
                                                    "name": "AWS_ACCESS_KEY_ID",
                                                    "value": access_key,
                                                },
                                                {
                                                    "name": "AWS_SECRET_ACCESS_KEY",
                                                    "value": secret_key,
                                                },
                                                {
                                                    "name": "AWS_REGION",
                                                    "value": AWS_REGION,
                                                },
                                            ],
                                        }
                                    ]
                                }
                            }
                        }
                    }
                ),
            ]
        )
        run_command(
            [
                "kubectl",
                "--context",
                KIND_KUBECTL_CONTEXT,
                "wait",
                "--for=condition=Available",
                "--timeout=5m",
                "-n",
                ACK_SYSTEM_NAMESPACE,
                "deployment/ack-{svc}-controller-{svc}-chart".format(svc=svc),
            ]
        )

    run_parallel(
        [
            threading.Thread(
                target=deploy_svc,
                args=(
                    "ec2",
                    ACK_EC2_VERSION,
                ),
            ),
            threading.Thread(
                target=deploy_svc,
                args=(
                    "iam",
                    ACK_IAM_VERSION,
                ),
            ),
        ]
    )


@lru_cache()
def get_aws_credentials():
    """
    get aws credentials using AWS CLI
    @returns access key id and access secret key
    """
    get_access_key_proc = subprocess.run(
        ["aws", "configure", "get", "aws_access_key_id"],
        stdout=subprocess.PIPE,
        stderr=sys.stderr,
    )
    get_secret_key_proc = subprocess.run(
        ["aws", "configure", "get", "aws_secret_access_key"],
        stdout=subprocess.PIPE,
        stderr=sys.stderr,
    )

    get_access_key_proc_output = get_access_key_proc.stdout.decode().strip()
    get_secret_key_proc_output = get_secret_key_proc.stdout.decode().strip()

    if get_access_key_proc.returncode != 0:
        raise RuntimeError(
            "failed to get access key: {}".format(get_access_key_proc_output)
        )

    if get_secret_key_proc.returncode != 0:
        raise RuntimeError(
            "failed to get secret key: {}".format(get_secret_key_proc_output)
        )

    return (
        get_access_key_proc_output,
        get_secret_key_proc_output,
    )


def deploy_config_connector():
    """
    deploy config connector following the instructions here:
    https://cloud.google.com/config-connector/docs/how-to/install-other-kubernetes#installing_operator
    """
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "create",
            "clusterrolebinding",
            "cluster-admin-binding",
            "--clusterrole",
            "cluster-admin",
            "--user",
            GCP_OWNER_EMAIL,
        ]
    )
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "create",
            "namespace",
            CNRM_NAMESPACE,
        ]
    )

    create_gcp_secret(CNRM_NAMESPACE)
    create_gcp_secret("cloudagent")
    download_config_connector_operator()

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "-f",
            f"{RESOURCES_PATH}/operator-system/configconnector-operator.yaml",
        ]
    )

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "-f",
            CONFIG_CONNECTOR_MANIFEST,
        ]
    )

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "annotate",
            "namespace",
            "default",
            f"cnrm.cloud.google.com/project-id={GCP_DEV_PROJECT}",
        ]
    )

def install_prometheus_crds():
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "--server-side=true",
            "-f",
            "https://raw.githubusercontent.com/GoogleCloudPlatform/prometheus-engine/main/manifests/setup.yaml"
        ]
    )

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "--server-side=true",
            "-f",
            "https://raw.githubusercontent.com/prometheus-operator/prometheus-operator/main/bundle.yaml"
        ]
    )


def install_aws_eks_crd():
    # install VPC CNI resource CRD
    # - SecurityGroupPolicy
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "-f",
            "https://raw.githubusercontent.com/aws/amazon-vpc-resource-controller-k8s/master/config/crd/bases/vpcresources.k8s.aws_securitygrouppolicies.yaml"
        ]
    )


def create_gcp_secret(namespace):
    """
    create gcp service account secret for use with config connector
    """
    token = ""
    try:
        token = get_service_account_token()
    except:
        logger.warn("failed to get gcp service account token")
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "create",
            "secret",
            "generic",
            GCP_SECRET_NAME,
            f"--from-literal=key.json={token}",
            f"--namespace={namespace}",
        ]
    )


def get_service_account_token():
    source_credentials, project = google.auth.default()

    target_credentials = impersonated_credentials.Credentials(
        source_credentials=source_credentials,
        target_principal=f"{GCP_CLOUD_AGENT_ACC}@{GCP_DEV_PROJECT}.iam.gserviceaccount.com",
        target_scopes=None,
        lifetime=60,
    )

    secret_client = secretmanager.SecretManagerServiceClient(
        credentials=target_credentials
    )
    response = secret_client.access_secret_version(
        request={
            "name": f"projects/{GCP_DEV_PROJECT}/secrets/{GCP_CLOUD_AGENT_SECRET_ID}/versions/latest"
        }
    )

    return response.payload.data.decode("UTF-8")


def download_config_connector_operator():
    """
    download and install kcc
    """
    config_connector_tar_file = f"{RESOURCES_PATH}/release-bundle.tar.gz"

    if os.path.exists(config_connector_tar_file):
        return

    try:
        os.makedirs(RESOURCES_PATH)
    except FileExistsError:
        pass
    except Exception as e:
        logger.exception(f"error creating resource folder {str(e)}")
        return

    run_command(
        [
            "gsutil",
            "cp",
            CONFIG_CONNECTOR_RESOURCE_URL,
            config_connector_tar_file,
        ]
    )

    run_command(["tar", "zxvf", config_connector_tar_file, "-C", RESOURCES_PATH])


def deploy_crossplane():
    """
    deploy crossplane following instructions here:
    https://docs.crossplane.io/latest/getting-started/provider-aws/
    """
    run_command(
        [
            "helm",
            "repo",
            "add",
            "crossplane-stable",
            "https://charts.crossplane.io/stable",
        ]
    )
    run_command(["helm", "repo", "update"])
    run_command(
        [
            "helm",
            "install",
            "crossplane",
            "crossplane-stable/crossplane",
            "--namespace",
            CROSSPLANE_NAMESPACE,
            "--create-namespace",
        ]
    )

    access_key, secret_key = get_aws_credentials()

    tmp = tempfile.NamedTemporaryFile()
    tmp.write(
        "[default]\naws_access_key_id = {access_key}\naws_secret_access_key = {secret_key}".format(
            access_key=access_key, secret_key=secret_key
        ).encode()
    )
    tmp.flush()

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "create",
            "secret",
            "generic",
            "aws-secret",
            "-n",
            CROSSPLANE_NAMESPACE,
            "--from-file=creds={}".format(tmp.name),
        ]
    )

    # this will destroy the temp file immediately
    tmp.close()


def deploy_risingwave_operator():
    """
    deploy cert manager and risingwave operator
    """
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "-f",
            "https://github.com/cert-manager/cert-manager/releases/download/{}/cert-manager.yaml".format(
                CERT_MANAGER_VERSION
            ),
        ]
    )
    run_command(["cmctl", "check", "api", "--wait=5m"])

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "apply",
            "--server-side",
            "-f",
            "https://github.com/risingwavelabs/risingwave-operator/releases/download/{}/risingwave-operator.yaml".format(
                RISINGWAVE_OPERATOR_VERSION
            ),
        ]
    )

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            "--for=condition=Ready",
            "Pods",
            "--all",
            "--namespace",
            "risingwave-operator-system",
            "--timeout=5m",
        ]
    )


def get_testing_s3_bucket_name() -> str:
    return "cloudagent-e2e-test-{}".format(get_name()).lower().replace("_", "-")


@lru_cache()
def get_aws_arn() -> str:
    cmd = subprocess.run(
        ["aws", "sts", "get-caller-identity", "--query", "Arn", "--output", "text"],
        stdout=subprocess.PIPE,
    )
    if cmd.returncode != 0:
        raise RuntimeError("failed to run `aws sts get-caller-identity`")
    return cmd.stdout.decode().strip()


def get_name() -> str:
    arn = get_aws_arn()
    return arn.split("/")[-1]


def extra_docker_build_param() -> str: 
    system = platform.system()
    is_macos = system == "Darwin"
    if is_macos: 
        return ["--load"]
    return []

def build_cloud_agent():
    ak, sk = get_aws_credentials()
    with open(CLOUDAGENT_CONFIG_OUTPUT_PATH, "w") as output_f:
        with open(CLOUDAGENT_CONFIG_TEMPLATE_PATH, "r") as template_f:
            output_f.write(
                template_f.read().format(
                    aws_default_tags='["{k1}:{v1}"]'.format(
                        k1=CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY, v1=get_aws_arn()
                    ),
                    aws_vpc_id=get_default_vpc(),
                    aws_region=AWS_REGION,
                    aws_access_key_id=ak,
                    aws_secret_access_key=sk,
                    task_runner_image_tag=TASK_RUNNER_IMAHE_TAG,
                    service_account_name=CLOUDAGENT_SERVICE_ACCOUNT_NAME,
                )
            )
    run_command(
        [
            "docker",
            "build",
            PROJECT_ROOT_PATH,
            "-f",
            CLOUDAGENT_SHELL_DOCKERFILE_PATH,
            "-t",
            CLOUDAGENT_SHELL_CONTAINER_TAG,
        ] + extra_docker_build_param()
    )


def build_task_runner():
    run_command(
        [
            "go",
            "build",
            "-C",
            PROJECT_ROOT_PATH,
            "-o",
            "{}/taskrunner".format(BIN_PATH),
            "{}/cmd/taskrunner/main.go".format(PROJECT_ROOT_PATH),
        ]
    )
    run_command(
        [
            "docker",
            "build",
            PROJECT_ROOT_PATH,
            "-f",
            TASK_RUNNER_SHELL_DOCKERFILE_PATH,
            "-t",
            TASK_RUNNER_IMAHE_TAG,
        ] + extra_docker_build_param()
    )


def build_psql_client():
    run_command(
        [
            "docker",
            "build",
            TEMPLATE_PATH,
            "-f",
            TEMPLATE_PATH + "/Dockerfile.psql",
            "-t",
            PSQL_CLIENT_IMAGE_TAG,
        ] + extra_docker_build_param()
    )


def deploy_cloud_agent():
    with open(CLOUDAGENT_SHELL_DEPLOYMENT_FILE_PATH) as file:
        content = file.read()

        with tempfile.NamedTemporaryFile() as tmp:
            cfg = content.format(
                agent_port=CLOUDAGENT_NODE_PORT,
                metrics_port=METRICS_NODE_PORT,
                cloud_agent_image=CLOUDAGENT_SHELL_CONTAINER_TAG,
                cloud_agent_cluster_role_name=CLOUDAGENT_CLUSTER_ROLE_NAME,
                cloud_agent_service_account_name=CLOUDAGENT_SERVICE_ACCOUNT_NAME,
                gcp_secret=GCP_SECRET_NAME,
            )

            logger.info("creating deployment config file in {}".format(tmp.name))
            logger.info(cfg)

            tmp.write(cfg.encode())
            tmp.flush()
            run_command(
                ["kubectl", "--context", KIND_KUBECTL_CONTEXT, "apply", "-f", tmp.name]
            )


def pull_and_load_image(image: str):
    try:
        run_command(["docker", "inspect", "--type=image", image], silent=True)
    except:
        run_command(["docker", "pull", image], silent=True)
    run_command(["kind", "load", "docker-image", image, "--name", KIND_NAME])


def load_images_to_kind(images: "list[str]"):
    threads: list[threading.Thread] = []

    for image in images:
        threads.append(threading.Thread(target=pull_and_load_image, args=(image,)))

    run_parallel(threads)


def load_task_runner():
    """
    this method is used in `make step`
    """
    load_images_to_kind([TASK_RUNNER_IMAHE_TAG])


def reload_task_runner():
    build_task_runner()
    load_task_runner()


def setup():
    logger.info("starting cloud agent end-to-end tests")

    create_cluster(
        name=KIND_NAME,
        kind_config_template_path=KIND_CONFIG_TEMPLATE_PATH,
        agent_port=CLOUDAGENT_NODE_PORT,
        metrics_port=METRICS_NODE_PORT,
        project_dir=PROJECT_ROOT_PATH,
        cache_dir=CLOUDAGENT_SHELL_CACHE_PATH,
    )

    install_prometheus_crds()

    install_aws_eks_crd()

    build_cloud_agent()

    build_task_runner()

    build_psql_client()

    run_command(
        [
            "kind",
            "load",
            "docker-image",
            CLOUDAGENT_SHELL_CONTAINER_TAG,
            "--name",
            KIND_NAME,
        ]
    )

    load_images_to_kind(
        [
            DATAGEN_IMAGE,
            PSQL_CLIENT_IMAGE_TAG,
            TASK_RUNNER_IMAHE_TAG,
            CLOUDAGENT_SHELL_CONTAINER_TAG,
            "public.ecr.aws/aws-controllers-k8s/ec2-controller:{}".format(
                ACK_EC2_VERSION
            ),
            "public.ecr.aws/aws-controllers-k8s/iam-controller:{}".format(
                ACK_IAM_VERSION
            ),
            "xpkg.upbound.io/crossplane-contrib/provider-aws:{}".format(
                CROSSPLANE_PROVIDER_AWS_VERSION
            ),
            "crossplane/crossplane:{}".format(CROSSPLANE_SYSTEM_VERSION),
            "risingwavelabs/risingwave:{}".format(RISINGWAVE_VERSION),
            "risingwavelabs/risingwave-operator:{}".format(
                RISINGWAVE_OPERATOR_VERSION
            ),
            "quay.io/jetstack/cert-manager-cainjector:{}".format(CERT_MANAGER_VERSION),
            "quay.io/jetstack/cert-manager-controller:{}".format(CERT_MANAGER_VERSION),
            "quay.io/jetstack/cert-manager-webhook:{}".format(CERT_MANAGER_VERSION),
            "quay.io/brancz/kube-rbac-proxy:v0.14.1",
            "docker.io/bitnami/etcd:3.5.6-debian-11-r0",
            "quay.io/coreos/etcd:latest",
            "bitnami/kafka:3.2.3",
        ]
    )

    run_parallel(
        [
            threading.Thread(target=deploy_cloud_agent),
            threading.Thread(target=deploy_crossplane),
            threading.Thread(target=deploy_ack),
            threading.Thread(target=deploy_risingwave_operator),
            threading.Thread(target=deploy_config_connector),
        ]
    )


def shutdown():
    logger.info("shutdown kind cluster")

    delete_cluster(name=KIND_NAME)
