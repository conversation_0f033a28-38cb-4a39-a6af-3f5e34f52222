# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/gmp.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/gmp.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63ommon/gmp.proto\x12\ncommon.gmp\x1a\x10\x63ommon/k8s.proto\"\x9a\x01\n\x11PodMonitoringSpec\x12\'\n\tendpoints\x18\x01 \x03(\x0b\x32\x14.common.gmp.Endpoint\x12+\n\x08selector\x18\x02 \x01(\x0b\x32\x19.common.k8s.LabelSelector\x12/\n\rtarget_labels\x18\x03 \x01(\x0b\x32\x18.common.gmp.TargetLabels\"r\n\x08\x45ndpoint\x12\x0c\n\x04port\x18\x01 \x01(\t\x12\x10\n\x08interval\x18\x02 \x01(\t\x12\x0f\n\x07timeout\x18\x03 \x01(\t\x12\x35\n\x11metric_relabeling\x18\x04 \x03(\x0b\x32\x1a.common.gmp.RelabelingRule\"F\n\x0eRelabelingRule\x12\x15\n\rsource_labels\x18\x01 \x03(\t\x12\r\n\x05regex\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\t\"9\n\x0cTargetLabels\x12)\n\x07\x66romPod\x18\x01 \x03(\x0b\x32\x18.common.gmp.LabelMapping\"(\n\x0cLabelMapping\x12\x0c\n\x04\x66rom\x18\x01 \x01(\t\x12\n\n\x02to\x18\x02 \x01(\tB7Z5github.com/risingwavelabs/cloudagent/pbgen/common/gmpb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.gmp_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/common/gmp'
  _globals['_PODMONITORINGSPEC']._serialized_start=51
  _globals['_PODMONITORINGSPEC']._serialized_end=205
  _globals['_ENDPOINT']._serialized_start=207
  _globals['_ENDPOINT']._serialized_end=321
  _globals['_RELABELINGRULE']._serialized_start=323
  _globals['_RELABELINGRULE']._serialized_end=393
  _globals['_TARGETLABELS']._serialized_start=395
  _globals['_TARGETLABELS']._serialized_end=452
  _globals['_LABELMAPPING']._serialized_start=454
  _globals['_LABELMAPPING']._serialized_end=494
# @@protoc_insertion_point(module_scope)
