from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class StatusCode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[StatusCode]
    SCHEDULED: _ClassVar[StatusCode]
    NOT_FOUND: _ClassVar[StatusCode]
    ALREADY_EXISTS: _ClassVar[StatusCode]
    UPDATED: _ClassVar[StatusCode]
UNKNOWN: StatusCode
SCHEDULED: StatusCode
NOT_FOUND: StatusCode
ALREADY_EXISTS: StatusCode
UPDATED: StatusCode

class Status(_message.Message):
    __slots__ = ("code",)
    CODE_FIELD_NUMBER: _ClassVar[int]
    code: StatusCode
    def __init__(self, code: _Optional[_Union[StatusCode, str]] = ...) -> None: ...
