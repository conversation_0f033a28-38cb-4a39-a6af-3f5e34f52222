# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/task.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/task.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x63ommon/task.proto\x12\x14\x63ommon.resource.task\"I\n\x06Status\x12.\n\x04\x63ode\x18\x01 \x01(\x0e\x32 .common.resource.task.StatusCode\x12\x0f\n\x07message\x18\x02 \x01(\t*N\n\nStatusCode\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0b\n\x07RUNNING\x10\x01\x12\n\n\x06\x46\x41ILED\x10\x02\x12\x0b\n\x07SUCCESS\x10\x03\x12\r\n\tNOT_FOUND\x10\x04\x42\x41Z?github.com/risingwavelabs/cloudagent/pbgen/common/resource/taskb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.task_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z?github.com/risingwavelabs/cloudagent/pbgen/common/resource/task'
  _globals['_STATUSCODE']._serialized_start=118
  _globals['_STATUSCODE']._serialized_end=196
  _globals['_STATUS']._serialized_start=43
  _globals['_STATUS']._serialized_end=116
# @@protoc_insertion_point(module_scope)
