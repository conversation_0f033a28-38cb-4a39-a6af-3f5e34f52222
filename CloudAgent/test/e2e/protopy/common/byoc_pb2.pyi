from google.protobuf import duration_pb2 as _duration_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ModuleOptions(_message.Message):
    __slots__ = ("module_path", "backend_config_file_name", "backend_config", "sensitive_variables", "variable_file_name", "variable_payload", "backend_config_env_var_overrides", "taskrunner_env_overrides")
    class SensitiveVariablesEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class BackendConfigEnvVarOverridesEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class TaskrunnerEnvOverridesEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    MODULE_PATH_FIELD_NUMBER: _ClassVar[int]
    BACKEND_CONFIG_FILE_NAME_FIELD_NUMBER: _ClassVar[int]
    BACKEND_CONFIG_FIELD_NUMBER: _ClassVar[int]
    SENSITIVE_VARIABLES_FIELD_NUMBER: _ClassVar[int]
    VARIABLE_FILE_NAME_FIELD_NUMBER: _ClassVar[int]
    VARIABLE_PAYLOAD_FIELD_NUMBER: _ClassVar[int]
    BACKEND_CONFIG_ENV_VAR_OVERRIDES_FIELD_NUMBER: _ClassVar[int]
    TASKRUNNER_ENV_OVERRIDES_FIELD_NUMBER: _ClassVar[int]
    module_path: str
    backend_config_file_name: str
    backend_config: bytes
    sensitive_variables: _containers.ScalarMap[str, str]
    variable_file_name: str
    variable_payload: bytes
    backend_config_env_var_overrides: _containers.ScalarMap[str, str]
    taskrunner_env_overrides: _containers.ScalarMap[str, str]
    def __init__(self, module_path: _Optional[str] = ..., backend_config_file_name: _Optional[str] = ..., backend_config: _Optional[bytes] = ..., sensitive_variables: _Optional[_Mapping[str, str]] = ..., variable_file_name: _Optional[str] = ..., variable_payload: _Optional[bytes] = ..., backend_config_env_var_overrides: _Optional[_Mapping[str, str]] = ..., taskrunner_env_overrides: _Optional[_Mapping[str, str]] = ...) -> None: ...

class TFInitOptions(_message.Message):
    __slots__ = ("retry", "retry_interval")
    RETRY_FIELD_NUMBER: _ClassVar[int]
    RETRY_INTERVAL_FIELD_NUMBER: _ClassVar[int]
    retry: int
    retry_interval: _duration_pb2.Duration
    def __init__(self, retry: _Optional[int] = ..., retry_interval: _Optional[_Union[_duration_pb2.Duration, _Mapping]] = ...) -> None: ...

class ApplyOptions(_message.Message):
    __slots__ = ("retry", "retry_interval", "graceful_shutdown_period", "lock_expiration_duration", "init_options")
    RETRY_FIELD_NUMBER: _ClassVar[int]
    RETRY_INTERVAL_FIELD_NUMBER: _ClassVar[int]
    GRACEFUL_SHUTDOWN_PERIOD_FIELD_NUMBER: _ClassVar[int]
    LOCK_EXPIRATION_DURATION_FIELD_NUMBER: _ClassVar[int]
    INIT_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    retry: int
    retry_interval: _duration_pb2.Duration
    graceful_shutdown_period: _duration_pb2.Duration
    lock_expiration_duration: _duration_pb2.Duration
    init_options: TFInitOptions
    def __init__(self, retry: _Optional[int] = ..., retry_interval: _Optional[_Union[_duration_pb2.Duration, _Mapping]] = ..., graceful_shutdown_period: _Optional[_Union[_duration_pb2.Duration, _Mapping]] = ..., lock_expiration_duration: _Optional[_Union[_duration_pb2.Duration, _Mapping]] = ..., init_options: _Optional[_Union[TFInitOptions, _Mapping]] = ...) -> None: ...

class PackageOptions(_message.Message):
    __slots__ = ("root_path", "tf_version_file_path", "package_url", "package_dest_name")
    ROOT_PATH_FIELD_NUMBER: _ClassVar[int]
    TF_VERSION_FILE_PATH_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_URL_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_DEST_NAME_FIELD_NUMBER: _ClassVar[int]
    root_path: str
    tf_version_file_path: str
    package_url: str
    package_dest_name: str
    def __init__(self, root_path: _Optional[str] = ..., tf_version_file_path: _Optional[str] = ..., package_url: _Optional[str] = ..., package_dest_name: _Optional[str] = ...) -> None: ...

class OutputOptions(_message.Message):
    __slots__ = ("retry", "retry_interval", "init_options")
    RETRY_FIELD_NUMBER: _ClassVar[int]
    RETRY_INTERVAL_FIELD_NUMBER: _ClassVar[int]
    INIT_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    retry: int
    retry_interval: _duration_pb2.Duration
    init_options: TFInitOptions
    def __init__(self, retry: _Optional[int] = ..., retry_interval: _Optional[_Union[_duration_pb2.Duration, _Mapping]] = ..., init_options: _Optional[_Union[TFInitOptions, _Mapping]] = ...) -> None: ...
