from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PGServerState(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[PGServerState]
    UPDATING: _ClassVar[PGServerState]
    READY: _ClassVar[PGServerState]
    STOPPED: _ClassVar[PGServerState]
UNKNOWN: PGServerState
UPDATING: PGServerState
READY: PGServerState
STOPPED: PGServerState

class PGServerSpec(_message.Message):
    __slots__ = ("azure_name", "owner", "version", "location", "sku", "storage", "administrator_login", "administrator_login_password", "network", "tags")
    class TagsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    AZURE_NAME_FIELD_NUMBER: _ClassVar[int]
    OWNER_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    LOCATION_FIELD_NUMBER: _ClassVar[int]
    SKU_FIELD_NUMBER: _ClassVar[int]
    STORAGE_FIELD_NUMBER: _ClassVar[int]
    ADMINISTRATOR_LOGIN_FIELD_NUMBER: _ClassVar[int]
    ADMINISTRATOR_LOGIN_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    NETWORK_FIELD_NUMBER: _ClassVar[int]
    TAGS_FIELD_NUMBER: _ClassVar[int]
    azure_name: str
    owner: ResourceReference
    version: str
    location: str
    sku: PGServerSku
    storage: PGServerStorage
    administrator_login: str
    administrator_login_password: SecretKeyRef
    network: PGServerNetwork
    tags: _containers.ScalarMap[str, str]
    def __init__(self, azure_name: _Optional[str] = ..., owner: _Optional[_Union[ResourceReference, _Mapping]] = ..., version: _Optional[str] = ..., location: _Optional[str] = ..., sku: _Optional[_Union[PGServerSku, _Mapping]] = ..., storage: _Optional[_Union[PGServerStorage, _Mapping]] = ..., administrator_login: _Optional[str] = ..., administrator_login_password: _Optional[_Union[SecretKeyRef, _Mapping]] = ..., network: _Optional[_Union[PGServerNetwork, _Mapping]] = ..., tags: _Optional[_Mapping[str, str]] = ...) -> None: ...

class ResourceReference(_message.Message):
    __slots__ = ("arm_id",)
    ARM_ID_FIELD_NUMBER: _ClassVar[int]
    arm_id: str
    def __init__(self, arm_id: _Optional[str] = ...) -> None: ...

class PGServerSku(_message.Message):
    __slots__ = ("name", "tier")
    NAME_FIELD_NUMBER: _ClassVar[int]
    TIER_FIELD_NUMBER: _ClassVar[int]
    name: str
    tier: str
    def __init__(self, name: _Optional[str] = ..., tier: _Optional[str] = ...) -> None: ...

class PGServerStorage(_message.Message):
    __slots__ = ("storage_size_gb",)
    STORAGE_SIZE_GB_FIELD_NUMBER: _ClassVar[int]
    storage_size_gb: int
    def __init__(self, storage_size_gb: _Optional[int] = ...) -> None: ...

class SecretKeyRef(_message.Message):
    __slots__ = ("key", "name")
    KEY_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    key: str
    name: str
    def __init__(self, key: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...

class PGServerNetwork(_message.Message):
    __slots__ = ("delegated_subnet", "private_dns_zone")
    DELEGATED_SUBNET_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_DNS_ZONE_FIELD_NUMBER: _ClassVar[int]
    delegated_subnet: ResourceReference
    private_dns_zone: ResourceReference
    def __init__(self, delegated_subnet: _Optional[_Union[ResourceReference, _Mapping]] = ..., private_dns_zone: _Optional[_Union[ResourceReference, _Mapping]] = ...) -> None: ...
