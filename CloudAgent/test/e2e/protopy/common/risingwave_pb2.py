# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/risingwave.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/risingwave.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x63ommon/risingwave.proto\x12\tcommon.rw\x1a\x10\x63ommon/k8s.proto\"\xa4\x04\n\x0eRisingWaveSpec\x12\r\n\x05image\x18\x01 \x01(\t\x12&\n\x1e\x65nable_default_service_monitor\x18\x02 \x01(\x08\x12.\n\x0bstate_store\x18\x03 \x01(\x0b\x32\x19.common.rw.StateStoreSpec\x12\x31\n\x0fmeta_store_spec\x18\x04 \x01(\x0b\x32\x18.common.rw.MetaStoreSpec\x12!\n\x06\x63onfig\x18\x05 \x01(\x0b\x32\x11.common.rw.Config\x12\x36\n\x15\x66rontend_service_type\x18\x06 \x01(\x0e\x32\x17.common.k8s.ServiceType\x12-\n\ncomponents\x18\x07 \x01(\x0b\x32\x19.common.rw.ComponentsSpec\x12#\n\x1b\x65nable_full_kubernetes_addr\x18\x08 \x01(\x08\x12\x1e\n\x16\x65nable_standalone_mode\x18\t \x01(\x08\x12$\n\x1c\x65nable_embedded_serving_mode\x18\n \x01(\x08\x12)\n\x0e\x63ompute_config\x18\x0b \x01(\x0b\x32\x11.common.rw.Config\x12*\n\x0blicense_key\x18\x0c \x01(\x0b\x32\x15.common.rw.LicenseKey\x12,\n\x0csecret_store\x18\r \x01(\x0b\x32\x16.common.rw.SecretStore\"\xf6\x02\n\x0eStateStoreSpec\x12\x16\n\x0e\x64\x61ta_directory\x18\x01 \x01(\t\x12\x38\n\x0es3_state_store\x18\x02 \x01(\x0b\x32\x1e.common.rw.StateStoreBackendS3H\x00\x12:\n\x0fgcs_state_store\x18\x03 \x01(\x0b\x32\x1f.common.rw.StateStoreBackendGCSH\x00\x12@\n\x12\x61zblob_state_store\x18\x06 \x01(\x0b\x32\".common.rw.StateStoreBackendAzblobH\x00\x12@\n\x12memory_state_store\x18\x04 \x01(\x0b\x32\".common.rw.StateStoreBackendMemoryH\x00\x12G\n\x16local_disk_state_store\x18\x05 \x01(\x0b\x32%.common.rw.StateStoreBackendLocalDiskH\x00\x42\t\n\x07\x62\x61\x63kend\"5\n\x13StateStoreBackendS3\x12\x0e\n\x06\x62ucket\x18\x01 \x01(\t\x12\x0e\n\x06region\x18\x02 \x01(\t\"&\n\x14StateStoreBackendGCS\x12\x0e\n\x06\x62ucket\x18\x01 \x01(\t\">\n\x17StateStoreBackendAzblob\x12\x11\n\tcontainer\x18\x01 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x02 \x01(\t\"\x19\n\x17StateStoreBackendMemory\"*\n\x1aStateStoreBackendLocalDisk\x12\x0c\n\x04root\x18\x01 \x01(\t\"\x89\x01\n\rMetaStoreSpec\x12\x35\n\x0c\x65tcd_backend\x18\x01 \x01(\x0b\x32\x1f.common.rw.MetaStoreBackendEtcd\x12\x41\n\x12postgresql_backend\x18\x02 \x01(\x0b\x32%.common.rw.MetaStoreBackendPostgreSql\"(\n\x14MetaStoreBackendEtcd\x12\x10\n\x08\x65ndpoint\x18\x01 \x01(\t\"\xf6\x01\n\x1aMetaStoreBackendPostgreSql\x12\x10\n\x08\x64\x61tabase\x18\x03 \x01(\t\x12\x0c\n\x04host\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\r\x12\x35\n\x0b\x63redentials\x18\x04 \x01(\x0b\x32 .common.rw.PostgreSqlCredentials\x12\x43\n\x07options\x18\x05 \x03(\x0b\x32\x32.common.rw.MetaStoreBackendPostgreSql.OptionsEntry\x1a.\n\x0cOptionsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"`\n\x15PostgreSqlCredentials\x12\x13\n\x0bsecret_name\x18\x01 \x01(\t\x12\x18\n\x10username_key_ref\x18\x02 \x01(\t\x12\x18\n\x10password_key_ref\x18\x03 \x01(\t\"4\n\x06\x43onfig\x12*\n\x0bnode_config\x18\x01 \x01(\x0b\x32\x15.common.rw.NodeConfig\"Z\n\nNodeConfig\x12L\n\x1dnode_configuration_config_map\x18\x01 \x01(\x0b\x32%.common.rw.NodeConfigurationConfigMap\"7\n\x1aNodeConfigurationConfigMap\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t\"Q\n\rComponentSpec\x12\x11\n\tlog_level\x18\x01 \x01(\t\x12-\n\x0bnode_groups\x18\x02 \x03(\x0b\x32\x18.common.rw.NodeGroupSpec\"6\n&PersistentVolumeClaimPartialObjectMeta\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xbb\x01\n$PersistentVolumeClaimRetentionPolicy\x12I\n\x0cwhen_deleted\x18\x01 \x01(\x0e\x32\x33.common.rw.PersistentVolumeClaimRetentionPolicyType\x12H\n\x0bwhen_scaled\x18\x02 \x01(\x0e\x32\x33.common.rw.PersistentVolumeClaimRetentionPolicyType\"\x91\x01\n\x15PersistentVolumeClaim\x12\x43\n\x08metadata\x18\x01 \x01(\x0b\x32\x31.common.rw.PersistentVolumeClaimPartialObjectMeta\x12\x33\n\x04spec\x18\x02 \x01(\x0b\x32%.common.k8s.PersistentVolumeClaimSpec\"\xee\x02\n\rNodeGroupSpec\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08replicas\x18\x02 \x01(\r\x12=\n\x10upgrade_strategy\x18\x03 \x01(\x0b\x32#.common.rw.NodeGroupUpgradeStrategy\x12-\n\rnode_pod_spec\x18\x04 \x01(\x0b\x32\x16.common.rw.NodePodSpec\x12@\n\x16volume_claim_templates\x18\x05 \x03(\x0b\x32 .common.rw.PersistentVolumeClaim\x12\x61\n(persistent_volume_claim_retention_policy\x18\x06 \x01(\x0b\x32/.common.rw.PersistentVolumeClaimRetentionPolicy\x12*\n\x0bnode_config\x18\x07 \x01(\x0b\x32\x15.common.rw.NodeConfig\"\xae\x03\n\x0bNodePodSpec\x12+\n\x0btolerations\x18\x01 \x03(\x0b\x32\x16.common.k8s.Toleration\x12&\n\x08\x61\x66\x66inity\x18\x02 \x01(\x0b\x32\x14.common.k8s.Affinity\x12\x37\n\x0e\x63ontainer_spec\x18\x03 \x01(\x0b\x32\x1f.common.rw.NodePodContainerSpec\x12\x17\n\x0fservice_account\x18\x04 \x01(\t\x12\x32\n\x06labels\x18\x05 \x03(\x0b\x32\".common.rw.NodePodSpec.LabelsEntry\x12<\n\x0b\x61nnotations\x18\x06 \x03(\x0b\x32\'.common.rw.NodePodSpec.AnnotationsEntry\x12#\n\x07volumes\x18\x07 \x03(\x0b\x32\x12.common.k8s.Volume\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x32\n\x10\x41nnotationsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xe1\x01\n\x14NodePodContainerSpec\x12\x33\n\tresources\x18\x01 \x01(\x0b\x32 .common.k8s.ResourceRequirements\x12\x37\n\x04\x65nvs\x18\x02 \x03(\x0b\x32).common.rw.NodePodContainerSpec.EnvsEntry\x12.\n\rvolume_mounts\x18\x03 \x03(\x0b\x32\x17.common.k8s.VolumeMount\x1a+\n\tEnvsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"H\n\x18NodeGroupUpgradeStrategy\x12,\n\x04type\x18\x01 \x01(\x0e\x32\x1e.common.rw.UpgradeStrategyType\"\xbf\x02\n\x0e\x43omponentsSpec\x12+\n\tmeta_spec\x18\x01 \x01(\x0b\x32\x18.common.rw.ComponentSpec\x12/\n\rfrontend_spec\x18\x02 \x01(\x0b\x32\x18.common.rw.ComponentSpec\x12.\n\x0c\x63ompute_spec\x18\x03 \x01(\x0b\x32\x18.common.rw.ComponentSpec\x12\x30\n\x0e\x63ompactor_spec\x18\x04 \x01(\x0b\x32\x18.common.rw.ComponentSpec\x12\x34\n\x0e\x63onnector_spec\x18\x05 \x01(\x0b\x32\x18.common.rw.ComponentSpecB\x02\x18\x01\x12\x37\n\x14standalone_component\x18\x06 \x01(\x0b\x32\x19.common.rw.StandaloneSpec\"\xa3\x01\n\x0eStandaloneSpec\x12\x11\n\tlog_level\x18\x01 \x01(\t\x12\x10\n\x08replicas\x18\x02 \x01(\r\x12=\n\x10upgrade_strategy\x18\x03 \x01(\x0b\x32#.common.rw.NodeGroupUpgradeStrategy\x12-\n\rnode_pod_spec\x18\x04 \x01(\x0b\x32\x16.common.rw.NodePodSpec\"g\n\x10RisingWaveStatus\x12\x34\n\x0bstatus_code\x18\x01 \x01(\x0e\x32\x1f.common.rw.RisingWaveStatusCode\x12\x1d\n\x15state_store_root_path\x18\x02 \x01(\t\"z\n\tScaleSpec\x12\x10\n\x08replicas\x18\x01 \x01(\r\x12\x33\n\tresources\x18\x02 \x01(\x0b\x32 .common.k8s.ResourceRequirements\x12&\n\x08\x61\x66\x66inity\x18\x03 \x01(\x0b\x32\x14.common.k8s.Affinity\"!\n\nLicenseKey\x12\x13\n\x0bsecret_name\x18\x01 \x01(\t\"D\n\x0bSecretStore\x12\x35\n\x0bprivate_key\x18\x01 \x01(\x0b\x32 .common.rw.SecretStorePrivateKey\"\x8e\x01\n\x15SecretStorePrivateKey\x12\x12\n\x05value\x18\x01 \x01(\tH\x00\x88\x01\x01\x12H\n\nsecret_ref\x18\x02 \x01(\x0b\x32/.common.rw.SecretStorePrivateKeySecretReferenceH\x01\x88\x01\x01\x42\x08\n\x06_valueB\r\n\x0b_secret_ref\"A\n$SecretStorePrivateKeySecretReference\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0b\n\x03key\x18\x02 \x01(\t*O\n(PersistentVolumeClaimRetentionPolicyType\x12\x0b\n\x07UNKNOWN\x10\x00\x12\n\n\x06\x44\x45LETE\x10\x01\x12\n\n\x06RETAIN\x10\x02*z\n\x13UpgradeStrategyType\x12\x14\n\x10UNKNOWN_STRATEGY\x10\x00\x12\x0c\n\x08RECREATE\x10\x01\x12\x12\n\x0eROLLING_UPDATE\x10\x02\x12\x18\n\x14IN_PLACE_IF_POSSIBLE\x10\x03\x12\x11\n\rIN_PLACE_ONLY\x10\x04*|\n\x14RisingWaveStatusCode\x12\x15\n\x11UNKNOWN_RW_STATUS\x10\x00\x12\x0c\n\x08RW_READY\x10\x01\x12\x10\n\x0cRW_NOT_READY\x10\x02\x12\x10\n\x0cRW_UPGRADING\x10\x03\x12\x1b\n\x17RW_WAIT_FOR_OBSERVATION\x10\x04*j\n\rComponentType\x12\x15\n\x11UNKNOWN_COMPONENT\x10\x00\x12\x08\n\x04META\x10\x01\x12\x0c\n\x08\x46RONTEND\x10\x02\x12\x0b\n\x07\x43OMPUTE\x10\x03\x12\r\n\tCOMPACTOR\x10\x04\x12\x0e\n\nSTANDALONE\x10\x05\x42\x36Z4github.com/risingwavelabs/cloudagent/pbgen/common/rwb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.risingwave_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z4github.com/risingwavelabs/cloudagent/pbgen/common/rw'
  _globals['_METASTOREBACKENDPOSTGRESQL_OPTIONSENTRY']._loaded_options = None
  _globals['_METASTOREBACKENDPOSTGRESQL_OPTIONSENTRY']._serialized_options = b'8\001'
  _globals['_NODEPODSPEC_LABELSENTRY']._loaded_options = None
  _globals['_NODEPODSPEC_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_NODEPODSPEC_ANNOTATIONSENTRY']._loaded_options = None
  _globals['_NODEPODSPEC_ANNOTATIONSENTRY']._serialized_options = b'8\001'
  _globals['_NODEPODCONTAINERSPEC_ENVSENTRY']._loaded_options = None
  _globals['_NODEPODCONTAINERSPEC_ENVSENTRY']._serialized_options = b'8\001'
  _globals['_COMPONENTSSPEC'].fields_by_name['connector_spec']._loaded_options = None
  _globals['_COMPONENTSSPEC'].fields_by_name['connector_spec']._serialized_options = b'\030\001'
  _globals['_PERSISTENTVOLUMECLAIMRETENTIONPOLICYTYPE']._serialized_start=4561
  _globals['_PERSISTENTVOLUMECLAIMRETENTIONPOLICYTYPE']._serialized_end=4640
  _globals['_UPGRADESTRATEGYTYPE']._serialized_start=4642
  _globals['_UPGRADESTRATEGYTYPE']._serialized_end=4764
  _globals['_RISINGWAVESTATUSCODE']._serialized_start=4766
  _globals['_RISINGWAVESTATUSCODE']._serialized_end=4890
  _globals['_COMPONENTTYPE']._serialized_start=4892
  _globals['_COMPONENTTYPE']._serialized_end=4998
  _globals['_RISINGWAVESPEC']._serialized_start=57
  _globals['_RISINGWAVESPEC']._serialized_end=605
  _globals['_STATESTORESPEC']._serialized_start=608
  _globals['_STATESTORESPEC']._serialized_end=982
  _globals['_STATESTOREBACKENDS3']._serialized_start=984
  _globals['_STATESTOREBACKENDS3']._serialized_end=1037
  _globals['_STATESTOREBACKENDGCS']._serialized_start=1039
  _globals['_STATESTOREBACKENDGCS']._serialized_end=1077
  _globals['_STATESTOREBACKENDAZBLOB']._serialized_start=1079
  _globals['_STATESTOREBACKENDAZBLOB']._serialized_end=1141
  _globals['_STATESTOREBACKENDMEMORY']._serialized_start=1143
  _globals['_STATESTOREBACKENDMEMORY']._serialized_end=1168
  _globals['_STATESTOREBACKENDLOCALDISK']._serialized_start=1170
  _globals['_STATESTOREBACKENDLOCALDISK']._serialized_end=1212
  _globals['_METASTORESPEC']._serialized_start=1215
  _globals['_METASTORESPEC']._serialized_end=1352
  _globals['_METASTOREBACKENDETCD']._serialized_start=1354
  _globals['_METASTOREBACKENDETCD']._serialized_end=1394
  _globals['_METASTOREBACKENDPOSTGRESQL']._serialized_start=1397
  _globals['_METASTOREBACKENDPOSTGRESQL']._serialized_end=1643
  _globals['_METASTOREBACKENDPOSTGRESQL_OPTIONSENTRY']._serialized_start=1597
  _globals['_METASTOREBACKENDPOSTGRESQL_OPTIONSENTRY']._serialized_end=1643
  _globals['_POSTGRESQLCREDENTIALS']._serialized_start=1645
  _globals['_POSTGRESQLCREDENTIALS']._serialized_end=1741
  _globals['_CONFIG']._serialized_start=1743
  _globals['_CONFIG']._serialized_end=1795
  _globals['_NODECONFIG']._serialized_start=1797
  _globals['_NODECONFIG']._serialized_end=1887
  _globals['_NODECONFIGURATIONCONFIGMAP']._serialized_start=1889
  _globals['_NODECONFIGURATIONCONFIGMAP']._serialized_end=1944
  _globals['_COMPONENTSPEC']._serialized_start=1946
  _globals['_COMPONENTSPEC']._serialized_end=2027
  _globals['_PERSISTENTVOLUMECLAIMPARTIALOBJECTMETA']._serialized_start=2029
  _globals['_PERSISTENTVOLUMECLAIMPARTIALOBJECTMETA']._serialized_end=2083
  _globals['_PERSISTENTVOLUMECLAIMRETENTIONPOLICY']._serialized_start=2086
  _globals['_PERSISTENTVOLUMECLAIMRETENTIONPOLICY']._serialized_end=2273
  _globals['_PERSISTENTVOLUMECLAIM']._serialized_start=2276
  _globals['_PERSISTENTVOLUMECLAIM']._serialized_end=2421
  _globals['_NODEGROUPSPEC']._serialized_start=2424
  _globals['_NODEGROUPSPEC']._serialized_end=2790
  _globals['_NODEPODSPEC']._serialized_start=2793
  _globals['_NODEPODSPEC']._serialized_end=3223
  _globals['_NODEPODSPEC_LABELSENTRY']._serialized_start=3126
  _globals['_NODEPODSPEC_LABELSENTRY']._serialized_end=3171
  _globals['_NODEPODSPEC_ANNOTATIONSENTRY']._serialized_start=3173
  _globals['_NODEPODSPEC_ANNOTATIONSENTRY']._serialized_end=3223
  _globals['_NODEPODCONTAINERSPEC']._serialized_start=3226
  _globals['_NODEPODCONTAINERSPEC']._serialized_end=3451
  _globals['_NODEPODCONTAINERSPEC_ENVSENTRY']._serialized_start=3408
  _globals['_NODEPODCONTAINERSPEC_ENVSENTRY']._serialized_end=3451
  _globals['_NODEGROUPUPGRADESTRATEGY']._serialized_start=3453
  _globals['_NODEGROUPUPGRADESTRATEGY']._serialized_end=3525
  _globals['_COMPONENTSSPEC']._serialized_start=3528
  _globals['_COMPONENTSSPEC']._serialized_end=3847
  _globals['_STANDALONESPEC']._serialized_start=3850
  _globals['_STANDALONESPEC']._serialized_end=4013
  _globals['_RISINGWAVESTATUS']._serialized_start=4015
  _globals['_RISINGWAVESTATUS']._serialized_end=4118
  _globals['_SCALESPEC']._serialized_start=4120
  _globals['_SCALESPEC']._serialized_end=4242
  _globals['_LICENSEKEY']._serialized_start=4244
  _globals['_LICENSEKEY']._serialized_end=4277
  _globals['_SECRETSTORE']._serialized_start=4279
  _globals['_SECRETSTORE']._serialized_end=4347
  _globals['_SECRETSTOREPRIVATEKEY']._serialized_start=4350
  _globals['_SECRETSTOREPRIVATEKEY']._serialized_end=4492
  _globals['_SECRETSTOREPRIVATEKEYSECRETREFERENCE']._serialized_start=4494
  _globals['_SECRETSTOREPRIVATEKEYSECRETREFERENCE']._serialized_end=4559
# @@protoc_insertion_point(module_scope)
