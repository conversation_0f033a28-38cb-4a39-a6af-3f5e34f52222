# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/gcp.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/gcp.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63ommon/gcp.proto\x12\ncommon.gcp\"\xcb\x01\n\x0fSQLInstanceSpec\x12\x13\n\x0bresource_id\x18\x01 \x01(\t\x12\x15\n\rinstance_type\x18\x02 \x01(\t\x12\x18\n\x10\x64\x61tabase_version\x18\x03 \x01(\t\x12\x0e\n\x06region\x18\x04 \x01(\t\x12/\n\rroot_password\x18\x05 \x01(\x0b\x32\x18.common.gcp.RootPassword\x12\x31\n\x08settings\x18\x06 \x01(\x0b\x32\x1f.common.gcp.SQLInstanceSettings\"\xc4\x01\n\x13SQLInstanceSettings\x12\x0c\n\x04tier\x18\x01 \x01(\t\x12\x11\n\tdisk_size\x18\x02 \x01(\x05\x12\x35\n\x10ip_configuration\x18\x03 \x01(\x0b\x32\x1b.common.gcp.IPConfiguration\x12#\n\x1b\x64\x65letion_protection_enabled\x18\x04 \x01(\x08\x12\x30\n\x0e\x64\x61tabase_flags\x18\x05 \x03(\x0b\x32\x18.common.gcp.DatabaseFlag\"+\n\x0c\x44\x61tabaseFlag\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"w\n\x0cRootPassword\x12\x12\n\x05value\x18\x01 \x01(\tH\x00\x88\x01\x01\x12:\n\nvalue_from\x18\x02 \x01(\x0b\x32!.common.gcp.RootPasswordValueFromH\x01\x88\x01\x01\x42\x08\n\x06_valueB\r\n\x0b_value_from\"G\n\x15RootPasswordValueFrom\x12.\n\x0csecretKeyRef\x18\x01 \x01(\x0b\x32\x18.common.gcp.SecretKeyRef\")\n\x0cSecretKeyRef\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xae\x01\n\x0fIPConfiguration\x12\x14\n\x0cipv4_enabled\x18\x01 \x01(\x08\x12:\n\x13private_network_ref\x18\x02 \x01(\x0b\x32\x1d.common.gcp.PrivateNetworkRef\x12\x1a\n\x12\x61llocated_ip_range\x18\x03 \x01(\t\x12-\n\x08ssl_mode\x18\x04 \x01(\x0e\x32\x1b.common.gcp.CloudSQLSSLMode\"%\n\x11PrivateNetworkRef\x12\x10\n\x08\x65xternal\x18\x01 \x01(\t*\xc0\x01\n\x0f\x43loudSQLSSLMode\x12\x1b\n\x17\x43loudSQLSSLMode_UNKNOWN\x10\x00\x12\x33\n/CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED\x10\x01\x12\"\n\x1e\x43loudSQLSSLMode_ENCRYPTED_ONLY\x10\x02\x12\x37\n3CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED\x10\x03*K\n\x11SQLInstanceStatus\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0c\n\x08UPDATING\x10\x01\x12\x0e\n\nUP_TO_DATE\x10\x02\x12\x0b\n\x07STOPPED\x10\x03\x42\x37Z5github.com/risingwavelabs/cloudagent/pbgen/common/gcpb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.gcp_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/common/gcp'
  _globals['_CLOUDSQLSSLMODE']._serialized_start=936
  _globals['_CLOUDSQLSSLMODE']._serialized_end=1128
  _globals['_SQLINSTANCESTATUS']._serialized_start=1130
  _globals['_SQLINSTANCESTATUS']._serialized_end=1205
  _globals['_SQLINSTANCESPEC']._serialized_start=33
  _globals['_SQLINSTANCESPEC']._serialized_end=236
  _globals['_SQLINSTANCESETTINGS']._serialized_start=239
  _globals['_SQLINSTANCESETTINGS']._serialized_end=435
  _globals['_DATABASEFLAG']._serialized_start=437
  _globals['_DATABASEFLAG']._serialized_end=480
  _globals['_ROOTPASSWORD']._serialized_start=482
  _globals['_ROOTPASSWORD']._serialized_end=601
  _globals['_ROOTPASSWORDVALUEFROM']._serialized_start=603
  _globals['_ROOTPASSWORDVALUEFROM']._serialized_end=674
  _globals['_SECRETKEYREF']._serialized_start=676
  _globals['_SECRETKEYREF']._serialized_end=717
  _globals['_IPCONFIGURATION']._serialized_start=720
  _globals['_IPCONFIGURATION']._serialized_end=894
  _globals['_PRIVATENETWORKREF']._serialized_start=896
  _globals['_PRIVATENETWORKREF']._serialized_end=933
# @@protoc_insertion_point(module_scope)
