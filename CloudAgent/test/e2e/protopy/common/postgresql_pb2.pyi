from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class UpdateStatusCode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UPDATE_UNKNOWN: _ClassVar[UpdateStatusCode]
    UPDATE_SCHEDULED: _ClassVar[UpdateStatusCode]
    UPDATE_NOT_FOUND: _ClassVar[UpdateStatusCode]
    UPDATE_ALREADY_EXISTS: _ClassVar[UpdateStatusCode]
UPDATE_UNKNOWN: UpdateStatusCode
UPDATE_SCHEDULED: UpdateStatusCode
UPDATE_NOT_FOUND: UpdateStatusCode
UPDATE_ALREADY_EXISTS: UpdateStatusCode

class PostgreSqlSpec(_message.Message):
    __slots__ = ("team_id", "docker_image", "resources", "number_of_instances", "volume", "users", "databases", "postgresql", "tolerations", "node_affinity", "pod_annotations", "service_annotations")
    class UsersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: StringArray
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[StringArray, _Mapping]] = ...) -> None: ...
    class DatabasesEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class PodAnnotationsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class ServiceAnnotationsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    TEAM_ID_FIELD_NUMBER: _ClassVar[int]
    DOCKER_IMAGE_FIELD_NUMBER: _ClassVar[int]
    RESOURCES_FIELD_NUMBER: _ClassVar[int]
    NUMBER_OF_INSTANCES_FIELD_NUMBER: _ClassVar[int]
    VOLUME_FIELD_NUMBER: _ClassVar[int]
    USERS_FIELD_NUMBER: _ClassVar[int]
    DATABASES_FIELD_NUMBER: _ClassVar[int]
    POSTGRESQL_FIELD_NUMBER: _ClassVar[int]
    TOLERATIONS_FIELD_NUMBER: _ClassVar[int]
    NODE_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    POD_ANNOTATIONS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ANNOTATIONS_FIELD_NUMBER: _ClassVar[int]
    team_id: str
    docker_image: str
    resources: _k8s_pb2.ResourceRequirements
    number_of_instances: int
    volume: PostgreSqlVolume
    users: _containers.MessageMap[str, StringArray]
    databases: _containers.ScalarMap[str, str]
    postgresql: PostgreSqlParam
    tolerations: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Toleration]
    node_affinity: _k8s_pb2.NodeAffinity
    pod_annotations: _containers.ScalarMap[str, str]
    service_annotations: _containers.ScalarMap[str, str]
    def __init__(self, team_id: _Optional[str] = ..., docker_image: _Optional[str] = ..., resources: _Optional[_Union[_k8s_pb2.ResourceRequirements, _Mapping]] = ..., number_of_instances: _Optional[int] = ..., volume: _Optional[_Union[PostgreSqlVolume, _Mapping]] = ..., users: _Optional[_Mapping[str, StringArray]] = ..., databases: _Optional[_Mapping[str, str]] = ..., postgresql: _Optional[_Union[PostgreSqlParam, _Mapping]] = ..., tolerations: _Optional[_Iterable[_Union[_k8s_pb2.Toleration, _Mapping]]] = ..., node_affinity: _Optional[_Union[_k8s_pb2.NodeAffinity, _Mapping]] = ..., pod_annotations: _Optional[_Mapping[str, str]] = ..., service_annotations: _Optional[_Mapping[str, str]] = ...) -> None: ...

class StringArray(_message.Message):
    __slots__ = ("value",)
    VALUE_FIELD_NUMBER: _ClassVar[int]
    value: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, value: _Optional[_Iterable[str]] = ...) -> None: ...

class PostgreSqlVolume(_message.Message):
    __slots__ = ("size", "storage_class")
    SIZE_FIELD_NUMBER: _ClassVar[int]
    STORAGE_CLASS_FIELD_NUMBER: _ClassVar[int]
    size: str
    storage_class: str
    def __init__(self, size: _Optional[str] = ..., storage_class: _Optional[str] = ...) -> None: ...

class PostgreSqlParam(_message.Message):
    __slots__ = ("version", "parameters")
    class ParametersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    VERSION_FIELD_NUMBER: _ClassVar[int]
    PARAMETERS_FIELD_NUMBER: _ClassVar[int]
    version: str
    parameters: _containers.ScalarMap[str, str]
    def __init__(self, version: _Optional[str] = ..., parameters: _Optional[_Mapping[str, str]] = ...) -> None: ...

class Credentials(_message.Message):
    __slots__ = ("username", "password")
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    PASSWORD_FIELD_NUMBER: _ClassVar[int]
    username: str
    password: str
    def __init__(self, username: _Optional[str] = ..., password: _Optional[str] = ...) -> None: ...
