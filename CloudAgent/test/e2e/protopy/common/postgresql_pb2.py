# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/postgresql.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/postgresql.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x63ommon/postgresql.proto\x12\x11\x63ommon.postgresql\x1a\x10\x63ommon/k8s.proto\"\xf0\x06\n\x0ePostgreSqlSpec\x12\x0f\n\x07team_id\x18\x01 \x01(\t\x12\x14\n\x0c\x64ocker_image\x18\x02 \x01(\t\x12\x33\n\tresources\x18\x03 \x01(\x0b\x32 .common.k8s.ResourceRequirements\x12\x1b\n\x13number_of_instances\x18\x04 \x01(\r\x12\x33\n\x06volume\x18\x05 \x01(\x0b\x32#.common.postgresql.PostgreSqlVolume\x12;\n\x05users\x18\x06 \x03(\x0b\x32,.common.postgresql.PostgreSqlSpec.UsersEntry\x12\x43\n\tdatabases\x18\x07 \x03(\x0b\x32\x30.common.postgresql.PostgreSqlSpec.DatabasesEntry\x12\x36\n\npostgresql\x18\x08 \x01(\x0b\x32\".common.postgresql.PostgreSqlParam\x12+\n\x0btolerations\x18\t \x03(\x0b\x32\x16.common.k8s.Toleration\x12/\n\rnode_affinity\x18\n \x01(\x0b\x32\x18.common.k8s.NodeAffinity\x12N\n\x0fpod_annotations\x18\x0b \x03(\x0b\x32\x35.common.postgresql.PostgreSqlSpec.PodAnnotationsEntry\x12V\n\x13service_annotations\x18\x0c \x03(\x0b\x32\x39.common.postgresql.PostgreSqlSpec.ServiceAnnotationsEntry\x1aL\n\nUsersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x05value\x18\x02 \x01(\x0b\x32\x1e.common.postgresql.StringArray:\x02\x38\x01\x1a\x30\n\x0e\x44\x61tabasesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x35\n\x13PodAnnotationsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x39\n\x17ServiceAnnotationsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x1c\n\x0bStringArray\x12\r\n\x05value\x18\x01 \x03(\t\"7\n\x10PostgreSqlVolume\x12\x0c\n\x04size\x18\x01 \x01(\t\x12\x15\n\rstorage_class\x18\x02 \x01(\t\"\x9d\x01\n\x0fPostgreSqlParam\x12\x0f\n\x07version\x18\x01 \x01(\t\x12\x46\n\nparameters\x18\x02 \x03(\x0b\x32\x32.common.postgresql.PostgreSqlParam.ParametersEntry\x1a\x31\n\x0fParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"1\n\x0b\x43redentials\x12\x10\n\x08username\x18\x01 \x01(\t\x12\x10\n\x08password\x18\x02 \x01(\t*m\n\x10UpdateStatusCode\x12\x12\n\x0eUPDATE_UNKNOWN\x10\x00\x12\x14\n\x10UPDATE_SCHEDULED\x10\x01\x12\x14\n\x10UPDATE_NOT_FOUND\x10\x02\x12\x19\n\x15UPDATE_ALREADY_EXISTS\x10\x03\x42>Z<github.com/risingwavelabs/cloudagent/pbgen/common/postgresqlb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.postgresql_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z<github.com/risingwavelabs/cloudagent/pbgen/common/postgresql'
  _globals['_POSTGRESQLSPEC_USERSENTRY']._loaded_options = None
  _globals['_POSTGRESQLSPEC_USERSENTRY']._serialized_options = b'8\001'
  _globals['_POSTGRESQLSPEC_DATABASESENTRY']._loaded_options = None
  _globals['_POSTGRESQLSPEC_DATABASESENTRY']._serialized_options = b'8\001'
  _globals['_POSTGRESQLSPEC_PODANNOTATIONSENTRY']._loaded_options = None
  _globals['_POSTGRESQLSPEC_PODANNOTATIONSENTRY']._serialized_options = b'8\001'
  _globals['_POSTGRESQLSPEC_SERVICEANNOTATIONSENTRY']._loaded_options = None
  _globals['_POSTGRESQLSPEC_SERVICEANNOTATIONSENTRY']._serialized_options = b'8\001'
  _globals['_POSTGRESQLPARAM_PARAMETERSENTRY']._loaded_options = None
  _globals['_POSTGRESQLPARAM_PARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_UPDATESTATUSCODE']._serialized_start=1245
  _globals['_UPDATESTATUSCODE']._serialized_end=1354
  _globals['_POSTGRESQLSPEC']._serialized_start=65
  _globals['_POSTGRESQLSPEC']._serialized_end=945
  _globals['_POSTGRESQLSPEC_USERSENTRY']._serialized_start=705
  _globals['_POSTGRESQLSPEC_USERSENTRY']._serialized_end=781
  _globals['_POSTGRESQLSPEC_DATABASESENTRY']._serialized_start=783
  _globals['_POSTGRESQLSPEC_DATABASESENTRY']._serialized_end=831
  _globals['_POSTGRESQLSPEC_PODANNOTATIONSENTRY']._serialized_start=833
  _globals['_POSTGRESQLSPEC_PODANNOTATIONSENTRY']._serialized_end=886
  _globals['_POSTGRESQLSPEC_SERVICEANNOTATIONSENTRY']._serialized_start=888
  _globals['_POSTGRESQLSPEC_SERVICEANNOTATIONSENTRY']._serialized_end=945
  _globals['_STRINGARRAY']._serialized_start=947
  _globals['_STRINGARRAY']._serialized_end=975
  _globals['_POSTGRESQLVOLUME']._serialized_start=977
  _globals['_POSTGRESQLVOLUME']._serialized_end=1032
  _globals['_POSTGRESQLPARAM']._serialized_start=1035
  _globals['_POSTGRESQLPARAM']._serialized_end=1192
  _globals['_POSTGRESQLPARAM_PARAMETERSENTRY']._serialized_start=1143
  _globals['_POSTGRESQLPARAM_PARAMETERSENTRY']._serialized_end=1192
  _globals['_CREDENTIALS']._serialized_start=1194
  _globals['_CREDENTIALS']._serialized_end=1243
# @@protoc_insertion_point(module_scope)
