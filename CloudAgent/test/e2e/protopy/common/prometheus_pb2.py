# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/prometheus.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/prometheus.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x17\x63ommon/prometheus.proto\x12\x11\x63ommon.prometheus\x1a\x10\x63ommon/k8s.proto\"\x80\x01\n\x08\x45ndpoint\x12\x0c\n\x04port\x18\x01 \x01(\t\x12\x10\n\x08interval\x18\x02 \x01(\t\x12\x16\n\x0escrape_timeout\x18\x03 \x01(\t\x12<\n\x12metric_relabelings\x18\x04 \x03(\x0b\x32 .common.prometheus.RelabelConfig\"\x9b\x01\n\x12ServiceMonitorSpec\x12\x11\n\tjob_label\x18\x01 \x01(\t\x12\x15\n\rtarget_labels\x18\x02 \x03(\t\x12.\n\tendpoints\x18\x03 \x03(\x0b\x32\x1b.common.prometheus.Endpoint\x12+\n\x08selector\x18\x04 \x01(\x0b\x32\x19.common.k8s.LabelSelector\"E\n\rRelabelConfig\x12\x15\n\rsource_labels\x18\x01 \x03(\t\x12\r\n\x05regex\x18\x02 \x01(\t\x12\x0e\n\x06\x61\x63tion\x18\x03 \x01(\tB>Z<github.com/risingwavelabs/cloudagent/pbgen/common/prometheusb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.prometheus_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z<github.com/risingwavelabs/cloudagent/pbgen/common/prometheus'
  _globals['_ENDPOINT']._serialized_start=65
  _globals['_ENDPOINT']._serialized_end=193
  _globals['_SERVICEMONITORSPEC']._serialized_start=196
  _globals['_SERVICEMONITORSPEC']._serialized_end=351
  _globals['_RELABELCONFIG']._serialized_start=353
  _globals['_RELABELCONFIG']._serialized_end=422
# @@protoc_insertion_point(module_scope)
