# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/azr.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/azr.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63ommon/azr.proto\x12\ncommon.azr\"\xb1\x03\n\x0cPGServerSpec\x12\x12\n\nazure_name\x18\x01 \x01(\t\x12,\n\x05owner\x18\x02 \x01(\x0b\x32\x1d.common.azr.ResourceReference\x12\x0f\n\x07version\x18\x03 \x01(\t\x12\x10\n\x08location\x18\x04 \x01(\t\x12$\n\x03sku\x18\x05 \x01(\x0b\x32\x17.common.azr.PGServerSku\x12,\n\x07storage\x18\x06 \x01(\x0b\x32\x1b.common.azr.PGServerStorage\x12\x1b\n\x13\x61\x64ministrator_login\x18\x07 \x01(\t\x12>\n\x1c\x61\x64ministrator_login_password\x18\x08 \x01(\x0b\x32\x18.common.azr.SecretKeyRef\x12,\n\x07network\x18\t \x01(\x0b\x32\x1b.common.azr.PGServerNetwork\x12\x30\n\x04tags\x18\n \x03(\x0b\x32\".common.azr.PGServerSpec.TagsEntry\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"#\n\x11ResourceReference\x12\x0e\n\x06\x61rm_id\x18\x01 \x01(\t\")\n\x0bPGServerSku\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04tier\x18\x02 \x01(\t\"*\n\x0fPGServerStorage\x12\x17\n\x0fstorage_size_gb\x18\x01 \x01(\x05\")\n\x0cSecretKeyRef\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x83\x01\n\x0fPGServerNetwork\x12\x37\n\x10\x64\x65legated_subnet\x18\x01 \x01(\x0b\x32\x1d.common.azr.ResourceReference\x12\x37\n\x10private_dns_zone\x18\x02 \x01(\x0b\x32\x1d.common.azr.ResourceReference*B\n\rPGServerState\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0c\n\x08UPDATING\x10\x01\x12\t\n\x05READY\x10\x02\x12\x0b\n\x07STOPPED\x10\x03\x42\x37Z5github.com/risingwavelabs/cloudagent/pbgen/common/azrb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.azr_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/common/azr'
  _globals['_PGSERVERSPEC_TAGSENTRY']._loaded_options = None
  _globals['_PGSERVERSPEC_TAGSENTRY']._serialized_options = b'8\001'
  _globals['_PGSERVERSTATE']._serialized_start=769
  _globals['_PGSERVERSTATE']._serialized_end=835
  _globals['_PGSERVERSPEC']._serialized_start=33
  _globals['_PGSERVERSPEC']._serialized_end=466
  _globals['_PGSERVERSPEC_TAGSENTRY']._serialized_start=423
  _globals['_PGSERVERSPEC_TAGSENTRY']._serialized_end=466
  _globals['_RESOURCEREFERENCE']._serialized_start=468
  _globals['_RESOURCEREFERENCE']._serialized_end=503
  _globals['_PGSERVERSKU']._serialized_start=505
  _globals['_PGSERVERSKU']._serialized_end=546
  _globals['_PGSERVERSTORAGE']._serialized_start=548
  _globals['_PGSERVERSTORAGE']._serialized_end=590
  _globals['_SECRETKEYREF']._serialized_start=592
  _globals['_SECRETKEYREF']._serialized_end=633
  _globals['_PGSERVERNETWORK']._serialized_start=636
  _globals['_PGSERVERNETWORK']._serialized_end=767
# @@protoc_insertion_point(module_scope)
