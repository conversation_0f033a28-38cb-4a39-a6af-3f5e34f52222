from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PersistentVolumeClaimRetentionPolicyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[PersistentVolumeClaimRetentionPolicyType]
    DELETE: _ClassVar[PersistentVolumeClaimRetentionPolicyType]
    RETAIN: _ClassVar[PersistentVolumeClaimRetentionPolicyType]

class UpgradeStrategyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN_STRATEGY: _ClassVar[UpgradeStrategyType]
    RECREATE: _ClassVar[UpgradeStrategyType]
    ROLLING_UPDATE: _ClassVar[UpgradeStrategyType]
    IN_PLACE_IF_POSSIBLE: _ClassVar[UpgradeStrategyType]
    IN_PLACE_ONLY: _ClassVar[UpgradeStrategyType]

class RisingWaveStatusCode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN_RW_STATUS: _ClassVar[RisingWaveStatusCode]
    RW_READY: _ClassVar[RisingWaveStatusCode]
    RW_NOT_READY: _ClassVar[RisingWaveStatusCode]
    RW_UPGRADING: _ClassVar[RisingWaveStatusCode]
    RW_WAIT_FOR_OBSERVATION: _ClassVar[RisingWaveStatusCode]

class ComponentType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN_COMPONENT: _ClassVar[ComponentType]
    META: _ClassVar[ComponentType]
    FRONTEND: _ClassVar[ComponentType]
    COMPUTE: _ClassVar[ComponentType]
    COMPACTOR: _ClassVar[ComponentType]
    STANDALONE: _ClassVar[ComponentType]
UNKNOWN: PersistentVolumeClaimRetentionPolicyType
DELETE: PersistentVolumeClaimRetentionPolicyType
RETAIN: PersistentVolumeClaimRetentionPolicyType
UNKNOWN_STRATEGY: UpgradeStrategyType
RECREATE: UpgradeStrategyType
ROLLING_UPDATE: UpgradeStrategyType
IN_PLACE_IF_POSSIBLE: UpgradeStrategyType
IN_PLACE_ONLY: UpgradeStrategyType
UNKNOWN_RW_STATUS: RisingWaveStatusCode
RW_READY: RisingWaveStatusCode
RW_NOT_READY: RisingWaveStatusCode
RW_UPGRADING: RisingWaveStatusCode
RW_WAIT_FOR_OBSERVATION: RisingWaveStatusCode
UNKNOWN_COMPONENT: ComponentType
META: ComponentType
FRONTEND: ComponentType
COMPUTE: ComponentType
COMPACTOR: ComponentType
STANDALONE: ComponentType

class RisingWaveSpec(_message.Message):
    __slots__ = ("image", "enable_default_service_monitor", "state_store", "meta_store_spec", "config", "frontend_service_type", "components", "enable_full_kubernetes_addr", "enable_standalone_mode", "enable_embedded_serving_mode", "compute_config", "license_key", "secret_store")
    IMAGE_FIELD_NUMBER: _ClassVar[int]
    ENABLE_DEFAULT_SERVICE_MONITOR_FIELD_NUMBER: _ClassVar[int]
    STATE_STORE_FIELD_NUMBER: _ClassVar[int]
    META_STORE_SPEC_FIELD_NUMBER: _ClassVar[int]
    CONFIG_FIELD_NUMBER: _ClassVar[int]
    FRONTEND_SERVICE_TYPE_FIELD_NUMBER: _ClassVar[int]
    COMPONENTS_FIELD_NUMBER: _ClassVar[int]
    ENABLE_FULL_KUBERNETES_ADDR_FIELD_NUMBER: _ClassVar[int]
    ENABLE_STANDALONE_MODE_FIELD_NUMBER: _ClassVar[int]
    ENABLE_EMBEDDED_SERVING_MODE_FIELD_NUMBER: _ClassVar[int]
    COMPUTE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    LICENSE_KEY_FIELD_NUMBER: _ClassVar[int]
    SECRET_STORE_FIELD_NUMBER: _ClassVar[int]
    image: str
    enable_default_service_monitor: bool
    state_store: StateStoreSpec
    meta_store_spec: MetaStoreSpec
    config: Config
    frontend_service_type: _k8s_pb2.ServiceType
    components: ComponentsSpec
    enable_full_kubernetes_addr: bool
    enable_standalone_mode: bool
    enable_embedded_serving_mode: bool
    compute_config: Config
    license_key: LicenseKey
    secret_store: SecretStore
    def __init__(self, image: _Optional[str] = ..., enable_default_service_monitor: bool = ..., state_store: _Optional[_Union[StateStoreSpec, _Mapping]] = ..., meta_store_spec: _Optional[_Union[MetaStoreSpec, _Mapping]] = ..., config: _Optional[_Union[Config, _Mapping]] = ..., frontend_service_type: _Optional[_Union[_k8s_pb2.ServiceType, str]] = ..., components: _Optional[_Union[ComponentsSpec, _Mapping]] = ..., enable_full_kubernetes_addr: bool = ..., enable_standalone_mode: bool = ..., enable_embedded_serving_mode: bool = ..., compute_config: _Optional[_Union[Config, _Mapping]] = ..., license_key: _Optional[_Union[LicenseKey, _Mapping]] = ..., secret_store: _Optional[_Union[SecretStore, _Mapping]] = ...) -> None: ...

class StateStoreSpec(_message.Message):
    __slots__ = ("data_directory", "s3_state_store", "gcs_state_store", "azblob_state_store", "memory_state_store", "local_disk_state_store")
    DATA_DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    S3_STATE_STORE_FIELD_NUMBER: _ClassVar[int]
    GCS_STATE_STORE_FIELD_NUMBER: _ClassVar[int]
    AZBLOB_STATE_STORE_FIELD_NUMBER: _ClassVar[int]
    MEMORY_STATE_STORE_FIELD_NUMBER: _ClassVar[int]
    LOCAL_DISK_STATE_STORE_FIELD_NUMBER: _ClassVar[int]
    data_directory: str
    s3_state_store: StateStoreBackendS3
    gcs_state_store: StateStoreBackendGCS
    azblob_state_store: StateStoreBackendAzblob
    memory_state_store: StateStoreBackendMemory
    local_disk_state_store: StateStoreBackendLocalDisk
    def __init__(self, data_directory: _Optional[str] = ..., s3_state_store: _Optional[_Union[StateStoreBackendS3, _Mapping]] = ..., gcs_state_store: _Optional[_Union[StateStoreBackendGCS, _Mapping]] = ..., azblob_state_store: _Optional[_Union[StateStoreBackendAzblob, _Mapping]] = ..., memory_state_store: _Optional[_Union[StateStoreBackendMemory, _Mapping]] = ..., local_disk_state_store: _Optional[_Union[StateStoreBackendLocalDisk, _Mapping]] = ...) -> None: ...

class StateStoreBackendS3(_message.Message):
    __slots__ = ("bucket", "region")
    BUCKET_FIELD_NUMBER: _ClassVar[int]
    REGION_FIELD_NUMBER: _ClassVar[int]
    bucket: str
    region: str
    def __init__(self, bucket: _Optional[str] = ..., region: _Optional[str] = ...) -> None: ...

class StateStoreBackendGCS(_message.Message):
    __slots__ = ("bucket",)
    BUCKET_FIELD_NUMBER: _ClassVar[int]
    bucket: str
    def __init__(self, bucket: _Optional[str] = ...) -> None: ...

class StateStoreBackendAzblob(_message.Message):
    __slots__ = ("container", "endpoint")
    CONTAINER_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    container: str
    endpoint: str
    def __init__(self, container: _Optional[str] = ..., endpoint: _Optional[str] = ...) -> None: ...

class StateStoreBackendMemory(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class StateStoreBackendLocalDisk(_message.Message):
    __slots__ = ("root",)
    ROOT_FIELD_NUMBER: _ClassVar[int]
    root: str
    def __init__(self, root: _Optional[str] = ...) -> None: ...

class MetaStoreSpec(_message.Message):
    __slots__ = ("etcd_backend", "postgresql_backend")
    ETCD_BACKEND_FIELD_NUMBER: _ClassVar[int]
    POSTGRESQL_BACKEND_FIELD_NUMBER: _ClassVar[int]
    etcd_backend: MetaStoreBackendEtcd
    postgresql_backend: MetaStoreBackendPostgreSql
    def __init__(self, etcd_backend: _Optional[_Union[MetaStoreBackendEtcd, _Mapping]] = ..., postgresql_backend: _Optional[_Union[MetaStoreBackendPostgreSql, _Mapping]] = ...) -> None: ...

class MetaStoreBackendEtcd(_message.Message):
    __slots__ = ("endpoint",)
    ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    endpoint: str
    def __init__(self, endpoint: _Optional[str] = ...) -> None: ...

class MetaStoreBackendPostgreSql(_message.Message):
    __slots__ = ("database", "host", "port", "credentials", "options")
    class OptionsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    DATABASE_FIELD_NUMBER: _ClassVar[int]
    HOST_FIELD_NUMBER: _ClassVar[int]
    PORT_FIELD_NUMBER: _ClassVar[int]
    CREDENTIALS_FIELD_NUMBER: _ClassVar[int]
    OPTIONS_FIELD_NUMBER: _ClassVar[int]
    database: str
    host: str
    port: int
    credentials: PostgreSqlCredentials
    options: _containers.ScalarMap[str, str]
    def __init__(self, database: _Optional[str] = ..., host: _Optional[str] = ..., port: _Optional[int] = ..., credentials: _Optional[_Union[PostgreSqlCredentials, _Mapping]] = ..., options: _Optional[_Mapping[str, str]] = ...) -> None: ...

class PostgreSqlCredentials(_message.Message):
    __slots__ = ("secret_name", "username_key_ref", "password_key_ref")
    SECRET_NAME_FIELD_NUMBER: _ClassVar[int]
    USERNAME_KEY_REF_FIELD_NUMBER: _ClassVar[int]
    PASSWORD_KEY_REF_FIELD_NUMBER: _ClassVar[int]
    secret_name: str
    username_key_ref: str
    password_key_ref: str
    def __init__(self, secret_name: _Optional[str] = ..., username_key_ref: _Optional[str] = ..., password_key_ref: _Optional[str] = ...) -> None: ...

class Config(_message.Message):
    __slots__ = ("node_config",)
    NODE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    node_config: NodeConfig
    def __init__(self, node_config: _Optional[_Union[NodeConfig, _Mapping]] = ...) -> None: ...

class NodeConfig(_message.Message):
    __slots__ = ("node_configuration_config_map",)
    NODE_CONFIGURATION_CONFIG_MAP_FIELD_NUMBER: _ClassVar[int]
    node_configuration_config_map: NodeConfigurationConfigMap
    def __init__(self, node_configuration_config_map: _Optional[_Union[NodeConfigurationConfigMap, _Mapping]] = ...) -> None: ...

class NodeConfigurationConfigMap(_message.Message):
    __slots__ = ("name", "key")
    NAME_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    name: str
    key: str
    def __init__(self, name: _Optional[str] = ..., key: _Optional[str] = ...) -> None: ...

class ComponentSpec(_message.Message):
    __slots__ = ("log_level", "node_groups")
    LOG_LEVEL_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUPS_FIELD_NUMBER: _ClassVar[int]
    log_level: str
    node_groups: _containers.RepeatedCompositeFieldContainer[NodeGroupSpec]
    def __init__(self, log_level: _Optional[str] = ..., node_groups: _Optional[_Iterable[_Union[NodeGroupSpec, _Mapping]]] = ...) -> None: ...

class PersistentVolumeClaimPartialObjectMeta(_message.Message):
    __slots__ = ("name",)
    NAME_FIELD_NUMBER: _ClassVar[int]
    name: str
    def __init__(self, name: _Optional[str] = ...) -> None: ...

class PersistentVolumeClaimRetentionPolicy(_message.Message):
    __slots__ = ("when_deleted", "when_scaled")
    WHEN_DELETED_FIELD_NUMBER: _ClassVar[int]
    WHEN_SCALED_FIELD_NUMBER: _ClassVar[int]
    when_deleted: PersistentVolumeClaimRetentionPolicyType
    when_scaled: PersistentVolumeClaimRetentionPolicyType
    def __init__(self, when_deleted: _Optional[_Union[PersistentVolumeClaimRetentionPolicyType, str]] = ..., when_scaled: _Optional[_Union[PersistentVolumeClaimRetentionPolicyType, str]] = ...) -> None: ...

class PersistentVolumeClaim(_message.Message):
    __slots__ = ("metadata", "spec")
    METADATA_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    metadata: PersistentVolumeClaimPartialObjectMeta
    spec: _k8s_pb2.PersistentVolumeClaimSpec
    def __init__(self, metadata: _Optional[_Union[PersistentVolumeClaimPartialObjectMeta, _Mapping]] = ..., spec: _Optional[_Union[_k8s_pb2.PersistentVolumeClaimSpec, _Mapping]] = ...) -> None: ...

class NodeGroupSpec(_message.Message):
    __slots__ = ("name", "replicas", "upgrade_strategy", "node_pod_spec", "volume_claim_templates", "persistent_volume_claim_retention_policy", "node_config")
    NAME_FIELD_NUMBER: _ClassVar[int]
    REPLICAS_FIELD_NUMBER: _ClassVar[int]
    UPGRADE_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    NODE_POD_SPEC_FIELD_NUMBER: _ClassVar[int]
    VOLUME_CLAIM_TEMPLATES_FIELD_NUMBER: _ClassVar[int]
    PERSISTENT_VOLUME_CLAIM_RETENTION_POLICY_FIELD_NUMBER: _ClassVar[int]
    NODE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    name: str
    replicas: int
    upgrade_strategy: NodeGroupUpgradeStrategy
    node_pod_spec: NodePodSpec
    volume_claim_templates: _containers.RepeatedCompositeFieldContainer[PersistentVolumeClaim]
    persistent_volume_claim_retention_policy: PersistentVolumeClaimRetentionPolicy
    node_config: NodeConfig
    def __init__(self, name: _Optional[str] = ..., replicas: _Optional[int] = ..., upgrade_strategy: _Optional[_Union[NodeGroupUpgradeStrategy, _Mapping]] = ..., node_pod_spec: _Optional[_Union[NodePodSpec, _Mapping]] = ..., volume_claim_templates: _Optional[_Iterable[_Union[PersistentVolumeClaim, _Mapping]]] = ..., persistent_volume_claim_retention_policy: _Optional[_Union[PersistentVolumeClaimRetentionPolicy, _Mapping]] = ..., node_config: _Optional[_Union[NodeConfig, _Mapping]] = ...) -> None: ...

class NodePodSpec(_message.Message):
    __slots__ = ("tolerations", "affinity", "container_spec", "service_account", "labels", "annotations", "volumes")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class AnnotationsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    TOLERATIONS_FIELD_NUMBER: _ClassVar[int]
    AFFINITY_FIELD_NUMBER: _ClassVar[int]
    CONTAINER_SPEC_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    ANNOTATIONS_FIELD_NUMBER: _ClassVar[int]
    VOLUMES_FIELD_NUMBER: _ClassVar[int]
    tolerations: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Toleration]
    affinity: _k8s_pb2.Affinity
    container_spec: NodePodContainerSpec
    service_account: str
    labels: _containers.ScalarMap[str, str]
    annotations: _containers.ScalarMap[str, str]
    volumes: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Volume]
    def __init__(self, tolerations: _Optional[_Iterable[_Union[_k8s_pb2.Toleration, _Mapping]]] = ..., affinity: _Optional[_Union[_k8s_pb2.Affinity, _Mapping]] = ..., container_spec: _Optional[_Union[NodePodContainerSpec, _Mapping]] = ..., service_account: _Optional[str] = ..., labels: _Optional[_Mapping[str, str]] = ..., annotations: _Optional[_Mapping[str, str]] = ..., volumes: _Optional[_Iterable[_Union[_k8s_pb2.Volume, _Mapping]]] = ...) -> None: ...

class NodePodContainerSpec(_message.Message):
    __slots__ = ("resources", "envs", "volume_mounts")
    class EnvsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCES_FIELD_NUMBER: _ClassVar[int]
    ENVS_FIELD_NUMBER: _ClassVar[int]
    VOLUME_MOUNTS_FIELD_NUMBER: _ClassVar[int]
    resources: _k8s_pb2.ResourceRequirements
    envs: _containers.ScalarMap[str, str]
    volume_mounts: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.VolumeMount]
    def __init__(self, resources: _Optional[_Union[_k8s_pb2.ResourceRequirements, _Mapping]] = ..., envs: _Optional[_Mapping[str, str]] = ..., volume_mounts: _Optional[_Iterable[_Union[_k8s_pb2.VolumeMount, _Mapping]]] = ...) -> None: ...

class NodeGroupUpgradeStrategy(_message.Message):
    __slots__ = ("type",)
    TYPE_FIELD_NUMBER: _ClassVar[int]
    type: UpgradeStrategyType
    def __init__(self, type: _Optional[_Union[UpgradeStrategyType, str]] = ...) -> None: ...

class ComponentsSpec(_message.Message):
    __slots__ = ("meta_spec", "frontend_spec", "compute_spec", "compactor_spec", "connector_spec", "standalone_component")
    META_SPEC_FIELD_NUMBER: _ClassVar[int]
    FRONTEND_SPEC_FIELD_NUMBER: _ClassVar[int]
    COMPUTE_SPEC_FIELD_NUMBER: _ClassVar[int]
    COMPACTOR_SPEC_FIELD_NUMBER: _ClassVar[int]
    CONNECTOR_SPEC_FIELD_NUMBER: _ClassVar[int]
    STANDALONE_COMPONENT_FIELD_NUMBER: _ClassVar[int]
    meta_spec: ComponentSpec
    frontend_spec: ComponentSpec
    compute_spec: ComponentSpec
    compactor_spec: ComponentSpec
    connector_spec: ComponentSpec
    standalone_component: StandaloneSpec
    def __init__(self, meta_spec: _Optional[_Union[ComponentSpec, _Mapping]] = ..., frontend_spec: _Optional[_Union[ComponentSpec, _Mapping]] = ..., compute_spec: _Optional[_Union[ComponentSpec, _Mapping]] = ..., compactor_spec: _Optional[_Union[ComponentSpec, _Mapping]] = ..., connector_spec: _Optional[_Union[ComponentSpec, _Mapping]] = ..., standalone_component: _Optional[_Union[StandaloneSpec, _Mapping]] = ...) -> None: ...

class StandaloneSpec(_message.Message):
    __slots__ = ("log_level", "replicas", "upgrade_strategy", "node_pod_spec")
    LOG_LEVEL_FIELD_NUMBER: _ClassVar[int]
    REPLICAS_FIELD_NUMBER: _ClassVar[int]
    UPGRADE_STRATEGY_FIELD_NUMBER: _ClassVar[int]
    NODE_POD_SPEC_FIELD_NUMBER: _ClassVar[int]
    log_level: str
    replicas: int
    upgrade_strategy: NodeGroupUpgradeStrategy
    node_pod_spec: NodePodSpec
    def __init__(self, log_level: _Optional[str] = ..., replicas: _Optional[int] = ..., upgrade_strategy: _Optional[_Union[NodeGroupUpgradeStrategy, _Mapping]] = ..., node_pod_spec: _Optional[_Union[NodePodSpec, _Mapping]] = ...) -> None: ...

class RisingWaveStatus(_message.Message):
    __slots__ = ("status_code", "state_store_root_path")
    STATUS_CODE_FIELD_NUMBER: _ClassVar[int]
    STATE_STORE_ROOT_PATH_FIELD_NUMBER: _ClassVar[int]
    status_code: RisingWaveStatusCode
    state_store_root_path: str
    def __init__(self, status_code: _Optional[_Union[RisingWaveStatusCode, str]] = ..., state_store_root_path: _Optional[str] = ...) -> None: ...

class ScaleSpec(_message.Message):
    __slots__ = ("replicas", "resources", "affinity")
    REPLICAS_FIELD_NUMBER: _ClassVar[int]
    RESOURCES_FIELD_NUMBER: _ClassVar[int]
    AFFINITY_FIELD_NUMBER: _ClassVar[int]
    replicas: int
    resources: _k8s_pb2.ResourceRequirements
    affinity: _k8s_pb2.Affinity
    def __init__(self, replicas: _Optional[int] = ..., resources: _Optional[_Union[_k8s_pb2.ResourceRequirements, _Mapping]] = ..., affinity: _Optional[_Union[_k8s_pb2.Affinity, _Mapping]] = ...) -> None: ...

class LicenseKey(_message.Message):
    __slots__ = ("secret_name",)
    SECRET_NAME_FIELD_NUMBER: _ClassVar[int]
    secret_name: str
    def __init__(self, secret_name: _Optional[str] = ...) -> None: ...

class SecretStore(_message.Message):
    __slots__ = ("private_key",)
    PRIVATE_KEY_FIELD_NUMBER: _ClassVar[int]
    private_key: SecretStorePrivateKey
    def __init__(self, private_key: _Optional[_Union[SecretStorePrivateKey, _Mapping]] = ...) -> None: ...

class SecretStorePrivateKey(_message.Message):
    __slots__ = ("value", "secret_ref")
    VALUE_FIELD_NUMBER: _ClassVar[int]
    SECRET_REF_FIELD_NUMBER: _ClassVar[int]
    value: str
    secret_ref: SecretStorePrivateKeySecretReference
    def __init__(self, value: _Optional[str] = ..., secret_ref: _Optional[_Union[SecretStorePrivateKeySecretReference, _Mapping]] = ...) -> None: ...

class SecretStorePrivateKeySecretReference(_message.Message):
    __slots__ = ("name", "key")
    NAME_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    name: str
    key: str
    def __init__(self, name: _Optional[str] = ..., key: _Optional[str] = ...) -> None: ...
