# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/byoc.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/byoc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x11\x63ommon/byoc.proto\x12\x0b\x63ommon.byoc\x1a\x1egoogle/protobuf/duration.proto\"\xe6\x04\n\rModuleOptions\x12\x13\n\x0bmodule_path\x18\x01 \x01(\t\x12 \n\x18\x62\x61\x63kend_config_file_name\x18\x02 \x01(\t\x12\x16\n\x0e\x62\x61\x63kend_config\x18\x03 \x01(\x0c\x12O\n\x13sensitive_variables\x18\x08 \x03(\x0b\x32\x32.common.byoc.ModuleOptions.SensitiveVariablesEntry\x12\x1a\n\x12variable_file_name\x18\x04 \x01(\t\x12\x18\n\x10variable_payload\x18\x05 \x01(\x0c\x12\x66\n backend_config_env_var_overrides\x18\x06 \x03(\x0b\x32<.common.byoc.ModuleOptions.BackendConfigEnvVarOverridesEntry\x12X\n\x18taskrunner_env_overrides\x18\x07 \x03(\x0b\x32\x36.common.byoc.ModuleOptions.TaskrunnerEnvOverridesEntry\x1a\x39\n\x17SensitiveVariablesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x43\n!BackendConfigEnvVarOverridesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a=\n\x1bTaskrunnerEnvOverridesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"Q\n\rTFInitOptions\x12\r\n\x05retry\x18\x01 \x01(\x05\x12\x31\n\x0eretry_interval\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration\"\xfc\x01\n\x0c\x41pplyOptions\x12\r\n\x05retry\x18\x01 \x01(\x05\x12\x31\n\x0eretry_interval\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration\x12;\n\x18graceful_shutdown_period\x18\x03 \x01(\x0b\x32\x19.google.protobuf.Duration\x12;\n\x18lock_expiration_duration\x18\x04 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x30\n\x0cinit_options\x18\x05 \x01(\x0b\x32\x1a.common.byoc.TFInitOptions\"q\n\x0ePackageOptions\x12\x11\n\troot_path\x18\x01 \x01(\t\x12\x1c\n\x14tf_version_file_path\x18\x02 \x01(\t\x12\x13\n\x0bpackage_url\x18\x03 \x01(\t\x12\x19\n\x11package_dest_name\x18\x04 \x01(\t\"\x83\x01\n\rOutputOptions\x12\r\n\x05retry\x18\x01 \x01(\x05\x12\x31\n\x0eretry_interval\x18\x02 \x01(\x0b\x32\x19.google.protobuf.Duration\x12\x30\n\x0cinit_options\x18\x03 \x01(\x0b\x32\x1a.common.byoc.TFInitOptionsB8Z6github.com/risingwavelabs/cloudagent/pbgen/common/byocb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.byoc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z6github.com/risingwavelabs/cloudagent/pbgen/common/byoc'
  _globals['_MODULEOPTIONS_SENSITIVEVARIABLESENTRY']._loaded_options = None
  _globals['_MODULEOPTIONS_SENSITIVEVARIABLESENTRY']._serialized_options = b'8\001'
  _globals['_MODULEOPTIONS_BACKENDCONFIGENVVAROVERRIDESENTRY']._loaded_options = None
  _globals['_MODULEOPTIONS_BACKENDCONFIGENVVAROVERRIDESENTRY']._serialized_options = b'8\001'
  _globals['_MODULEOPTIONS_TASKRUNNERENVOVERRIDESENTRY']._loaded_options = None
  _globals['_MODULEOPTIONS_TASKRUNNERENVOVERRIDESENTRY']._serialized_options = b'8\001'
  _globals['_MODULEOPTIONS']._serialized_start=67
  _globals['_MODULEOPTIONS']._serialized_end=681
  _globals['_MODULEOPTIONS_SENSITIVEVARIABLESENTRY']._serialized_start=492
  _globals['_MODULEOPTIONS_SENSITIVEVARIABLESENTRY']._serialized_end=549
  _globals['_MODULEOPTIONS_BACKENDCONFIGENVVAROVERRIDESENTRY']._serialized_start=551
  _globals['_MODULEOPTIONS_BACKENDCONFIGENVVAROVERRIDESENTRY']._serialized_end=618
  _globals['_MODULEOPTIONS_TASKRUNNERENVOVERRIDESENTRY']._serialized_start=620
  _globals['_MODULEOPTIONS_TASKRUNNERENVOVERRIDESENTRY']._serialized_end=681
  _globals['_TFINITOPTIONS']._serialized_start=683
  _globals['_TFINITOPTIONS']._serialized_end=764
  _globals['_APPLYOPTIONS']._serialized_start=767
  _globals['_APPLYOPTIONS']._serialized_end=1019
  _globals['_PACKAGEOPTIONS']._serialized_start=1021
  _globals['_PACKAGEOPTIONS']._serialized_end=1134
  _globals['_OUTPUTOPTIONS']._serialized_start=1137
  _globals['_OUTPUTOPTIONS']._serialized_end=1268
# @@protoc_insertion_point(module_scope)
