from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PodMonitoringSpec(_message.Message):
    __slots__ = ("endpoints", "selector", "target_labels")
    ENDPOINTS_FIELD_NUMBER: _ClassVar[int]
    SELECTOR_FIELD_NUMBER: _ClassVar[int]
    TARGET_LABELS_FIELD_NUMBER: _ClassVar[int]
    endpoints: _containers.RepeatedCompositeFieldContainer[Endpoint]
    selector: _k8s_pb2.LabelSelector
    target_labels: TargetLabels
    def __init__(self, endpoints: _Optional[_Iterable[_Union[Endpoint, _Mapping]]] = ..., selector: _Optional[_Union[_k8s_pb2.LabelSelector, _Mapping]] = ..., target_labels: _Optional[_Union[TargetLabels, _Mapping]] = ...) -> None: ...

class Endpoint(_message.Message):
    __slots__ = ("port", "interval", "timeout", "metric_relabeling")
    PORT_FIELD_NUMBER: _ClassVar[int]
    INTERVAL_FIELD_NUMBER: _ClassVar[int]
    TIMEOUT_FIELD_NUMBER: _ClassVar[int]
    METRIC_RELABELING_FIELD_NUMBER: _ClassVar[int]
    port: str
    interval: str
    timeout: str
    metric_relabeling: _containers.RepeatedCompositeFieldContainer[RelabelingRule]
    def __init__(self, port: _Optional[str] = ..., interval: _Optional[str] = ..., timeout: _Optional[str] = ..., metric_relabeling: _Optional[_Iterable[_Union[RelabelingRule, _Mapping]]] = ...) -> None: ...

class RelabelingRule(_message.Message):
    __slots__ = ("source_labels", "regex", "action")
    SOURCE_LABELS_FIELD_NUMBER: _ClassVar[int]
    REGEX_FIELD_NUMBER: _ClassVar[int]
    ACTION_FIELD_NUMBER: _ClassVar[int]
    source_labels: _containers.RepeatedScalarFieldContainer[str]
    regex: str
    action: str
    def __init__(self, source_labels: _Optional[_Iterable[str]] = ..., regex: _Optional[str] = ..., action: _Optional[str] = ...) -> None: ...

class TargetLabels(_message.Message):
    __slots__ = ("fromPod",)
    FROMPOD_FIELD_NUMBER: _ClassVar[int]
    fromPod: _containers.RepeatedCompositeFieldContainer[LabelMapping]
    def __init__(self, fromPod: _Optional[_Iterable[_Union[LabelMapping, _Mapping]]] = ...) -> None: ...

class LabelMapping(_message.Message):
    __slots__ = ("to",)
    FROM_FIELD_NUMBER: _ClassVar[int]
    TO_FIELD_NUMBER: _ClassVar[int]
    to: str
    def __init__(self, to: _Optional[str] = ..., **kwargs) -> None: ...
