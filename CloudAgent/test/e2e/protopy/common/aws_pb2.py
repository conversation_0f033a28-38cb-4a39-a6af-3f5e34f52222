# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/aws.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/aws.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63ommon/aws.proto\x12\ncommon.aws\"\xae\x03\n\x0e\x44\x42InstanceSpec\x12\x1e\n\x16\x64\x62_instance_identifier\x18\x01 \x01(\t\x12\x19\n\x11\x64\x62_instance_class\x18\x02 \x01(\t\x12\x19\n\x11\x61llocated_storage\x18\x03 \x01(\r\x12\x0e\n\x06\x65ngine\x18\x04 \x01(\t\x12\x16\n\x0e\x65ngine_version\x18\x05 \x01(\t\x12\x0f\n\x07\x64\x62_name\x18\x06 \x01(\t\x12\x17\n\x0fmaster_username\x18\x07 \x01(\t\x12;\n\x14master_user_password\x18\x08 \x01(\x0b\x32\x1d.common.aws.PasswordSecretRef\x12\x1c\n\x14\x64\x62_subnet_group_name\x18\t \x01(\t\x12\x1e\n\x16vpc_security_group_ids\x18\n \x03(\t\x12\x32\n\x04tags\x18\x0b \x03(\x0b\x32$.common.aws.DBInstanceSpec.TagsEntry\x12\x18\n\x10storageEncrypted\x18\x0c \x01(\x08\x1a+\n\tTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"A\n\x11PasswordSecretRef\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x0b\n\x03key\x18\x03 \x01(\t\"%\n\x12\x44\x42InstanceEndpoint\x12\x0f\n\x07\x61\x64\x64ress\x18\x01 \x01(\t*J\n\x10\x44\x42InstanceStatus\x12\x0b\n\x07UNKNOWN\x10\x00\x12\r\n\tNOT_READY\x10\x01\x12\r\n\tAVAILABLE\x10\x02\x12\x0b\n\x07STOPPED\x10\x03\x42\x37Z5github.com/risingwavelabs/cloudagent/pbgen/common/awsb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.aws_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/common/aws'
  _globals['_DBINSTANCESPEC_TAGSENTRY']._loaded_options = None
  _globals['_DBINSTANCESPEC_TAGSENTRY']._serialized_options = b'8\001'
  _globals['_DBINSTANCESTATUS']._serialized_start=571
  _globals['_DBINSTANCESTATUS']._serialized_end=645
  _globals['_DBINSTANCESPEC']._serialized_start=33
  _globals['_DBINSTANCESPEC']._serialized_end=463
  _globals['_DBINSTANCESPEC_TAGSENTRY']._serialized_start=420
  _globals['_DBINSTANCESPEC_TAGSENTRY']._serialized_end=463
  _globals['_PASSWORDSECRETREF']._serialized_start=465
  _globals['_PASSWORDSECRETREF']._serialized_end=530
  _globals['_DBINSTANCEENDPOINT']._serialized_start=532
  _globals['_DBINSTANCEENDPOINT']._serialized_end=569
# @@protoc_insertion_point(module_scope)
