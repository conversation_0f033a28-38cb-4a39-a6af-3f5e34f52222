from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class DBInstanceStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[DBInstanceStatus]
    NOT_READY: _ClassVar[DBInstanceStatus]
    AVAILABLE: _ClassVar[DBInstanceStatus]
    STOPPED: _ClassVar[DBInstanceStatus]
UNKNOWN: DBInstanceStatus
NOT_READY: DBInstanceStatus
AVAILABLE: DBInstanceStatus
STOPPED: DBInstanceStatus

class DBInstanceSpec(_message.Message):
    __slots__ = ("db_instance_identifier", "db_instance_class", "allocated_storage", "engine", "engine_version", "db_name", "master_username", "master_user_password", "db_subnet_group_name", "vpc_security_group_ids", "tags", "storageEncrypted")
    class TagsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    DB_INSTANCE_IDENTIFIER_FIELD_NUMBER: _ClassVar[int]
    DB_INSTANCE_CLASS_FIELD_NUMBER: _ClassVar[int]
    ALLOCATED_STORAGE_FIELD_NUMBER: _ClassVar[int]
    ENGINE_FIELD_NUMBER: _ClassVar[int]
    ENGINE_VERSION_FIELD_NUMBER: _ClassVar[int]
    DB_NAME_FIELD_NUMBER: _ClassVar[int]
    MASTER_USERNAME_FIELD_NUMBER: _ClassVar[int]
    MASTER_USER_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    DB_SUBNET_GROUP_NAME_FIELD_NUMBER: _ClassVar[int]
    VPC_SECURITY_GROUP_IDS_FIELD_NUMBER: _ClassVar[int]
    TAGS_FIELD_NUMBER: _ClassVar[int]
    STORAGEENCRYPTED_FIELD_NUMBER: _ClassVar[int]
    db_instance_identifier: str
    db_instance_class: str
    allocated_storage: int
    engine: str
    engine_version: str
    db_name: str
    master_username: str
    master_user_password: PasswordSecretRef
    db_subnet_group_name: str
    vpc_security_group_ids: _containers.RepeatedScalarFieldContainer[str]
    tags: _containers.ScalarMap[str, str]
    storageEncrypted: bool
    def __init__(self, db_instance_identifier: _Optional[str] = ..., db_instance_class: _Optional[str] = ..., allocated_storage: _Optional[int] = ..., engine: _Optional[str] = ..., engine_version: _Optional[str] = ..., db_name: _Optional[str] = ..., master_username: _Optional[str] = ..., master_user_password: _Optional[_Union[PasswordSecretRef, _Mapping]] = ..., db_subnet_group_name: _Optional[str] = ..., vpc_security_group_ids: _Optional[_Iterable[str]] = ..., tags: _Optional[_Mapping[str, str]] = ..., storageEncrypted: bool = ...) -> None: ...

class PasswordSecretRef(_message.Message):
    __slots__ = ("namespace", "name", "key")
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    KEY_FIELD_NUMBER: _ClassVar[int]
    namespace: str
    name: str
    key: str
    def __init__(self, namespace: _Optional[str] = ..., name: _Optional[str] = ..., key: _Optional[str] = ...) -> None: ...

class DBInstanceEndpoint(_message.Message):
    __slots__ = ("address",)
    ADDRESS_FIELD_NUMBER: _ClassVar[int]
    address: str
    def __init__(self, address: _Optional[str] = ...) -> None: ...
