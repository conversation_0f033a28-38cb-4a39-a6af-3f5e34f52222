from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Endpoint(_message.Message):
    __slots__ = ("port", "interval", "scrape_timeout", "metric_relabelings")
    PORT_FIELD_NUMBER: _ClassVar[int]
    INTERVAL_FIELD_NUMBER: _ClassVar[int]
    SCRAPE_TIMEOUT_FIELD_NUMBER: _ClassVar[int]
    METRIC_RELABELINGS_FIELD_NUMBER: _ClassVar[int]
    port: str
    interval: str
    scrape_timeout: str
    metric_relabelings: _containers.RepeatedCompositeFieldContainer[RelabelConfig]
    def __init__(self, port: _Optional[str] = ..., interval: _Optional[str] = ..., scrape_timeout: _Optional[str] = ..., metric_relabelings: _Optional[_Iterable[_Union[RelabelConfig, _Mapping]]] = ...) -> None: ...

class ServiceMonitorSpec(_message.Message):
    __slots__ = ("job_label", "target_labels", "endpoints", "selector")
    JOB_LABEL_FIELD_NUMBER: _ClassVar[int]
    TARGET_LABELS_FIELD_NUMBER: _ClassVar[int]
    ENDPOINTS_FIELD_NUMBER: _ClassVar[int]
    SELECTOR_FIELD_NUMBER: _ClassVar[int]
    job_label: str
    target_labels: _containers.RepeatedScalarFieldContainer[str]
    endpoints: _containers.RepeatedCompositeFieldContainer[Endpoint]
    selector: _k8s_pb2.LabelSelector
    def __init__(self, job_label: _Optional[str] = ..., target_labels: _Optional[_Iterable[str]] = ..., endpoints: _Optional[_Iterable[_Union[Endpoint, _Mapping]]] = ..., selector: _Optional[_Union[_k8s_pb2.LabelSelector, _Mapping]] = ...) -> None: ...

class RelabelConfig(_message.Message):
    __slots__ = ("source_labels", "regex", "action")
    SOURCE_LABELS_FIELD_NUMBER: _ClassVar[int]
    REGEX_FIELD_NUMBER: _ClassVar[int]
    ACTION_FIELD_NUMBER: _ClassVar[int]
    source_labels: _containers.RepeatedScalarFieldContainer[str]
    regex: str
    action: str
    def __init__(self, source_labels: _Optional[_Iterable[str]] = ..., regex: _Optional[str] = ..., action: _Optional[str] = ...) -> None: ...
