from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class StatusCode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[StatusCode]
    RUNNING: _ClassVar[StatusCode]
    FAILED: _ClassVar[StatusCode]
    SUCCESS: _ClassVar[StatusCode]
    NOT_FOUND: _ClassVar[StatusCode]
UNKNOWN: StatusCode
RUNNING: StatusCode
FAILED: StatusCode
SUCCESS: StatusCode
NOT_FOUND: StatusCode

class Status(_message.Message):
    __slots__ = ("code", "message")
    CODE_FIELD_NUMBER: _ClassVar[int]
    MESSAGE_FIELD_NUMBER: _ClassVar[int]
    code: StatusCode
    message: str
    def __init__(self, code: _Optional[_Union[StatusCode, str]] = ..., message: _Optional[str] = ...) -> None: ...
