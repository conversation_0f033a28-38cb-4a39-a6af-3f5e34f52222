# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: common/k8s.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'common/k8s.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63ommon/k8s.proto\x12\ncommon.k8s\"\xe9\x01\n\tConfigMap\x12\x11\n\timmutable\x18\x01 \x01(\x08\x12-\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x1f.common.k8s.ConfigMap.DataEntry\x12:\n\x0b\x62inary_data\x18\x03 \x03(\x0b\x32%.common.k8s.ConfigMap.BinaryDataEntry\x1a+\n\tDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x31\n\x0f\x42inaryDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\"\xee\x01\n\x06Secret\x12\x11\n\timmutable\x18\x01 \x01(\x08\x12*\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32\x1c.common.k8s.Secret.DataEntry\x12\x37\n\x0bstring_data\x18\x03 \x03(\x0b\x32\".common.k8s.Secret.StringDataEntry\x12\x0c\n\x04type\x18\x04 \x01(\t\x1a+\n\tDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x0c:\x02\x38\x01\x1a\x31\n\x0fStringDataEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"1\n\x0eServiceAccount\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tnamespace\x18\x02 \x01(\t\"v\n\x0bHelmRelease\x12\x14\n\x0crelease_name\x18\x01 \x01(\t\x12\x11\n\tnamespace\x18\x02 \x01(\t\x12-\n\x06status\x18\x03 \x01(\x0e\x32\x1d.common.k8s.HelmReleaseStatus\x12\x0f\n\x07version\x18\x04 \x01(\t\"\xb5\x01\n\nToleration\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\x12\'\n\x06\x65\x66\x66\x65\x63t\x18\x03 \x01(\x0e\x32\x17.common.k8s.TaintEffect\x12\x30\n\x08operator\x18\x04 \x01(\x0e\x32\x1e.common.k8s.TolerationOperator\x12\x1c\n\x0ftoleration_secs\x18\x05 \x01(\x03H\x00\x88\x01\x01\x42\x12\n\x10_toleration_secs\"\xa2\x01\n\x08\x41\x66\x66inity\x12/\n\rnode_affinity\x18\x01 \x01(\x0b\x32\x18.common.k8s.NodeAffinity\x12-\n\x0cpod_affinity\x18\x02 \x01(\x0b\x32\x17.common.k8s.PodAffinity\x12\x36\n\x11pod_anti_affinity\x18\x03 \x01(\x0b\x32\x1b.common.k8s.PodAntiAffinity\"\xc8\x01\n\x0cNodeAffinity\x12U\n3required_during_scheduling_ignored_during_execution\x18\x01 \x01(\x0b\x32\x18.common.k8s.NodeSelector\x12\x61\n4preferred_during_scheduling_ignored_during_execution\x18\x02 \x03(\x0b\x32#.common.k8s.PreferredSchedulingTerm\"I\n\x0cNodeSelector\x12\x39\n\x13node_selector_terms\x18\x01 \x03(\x0b\x32\x1c.common.k8s.NodeSelectorTerm\"[\n\x17PreferredSchedulingTerm\x12\x0e\n\x06weight\x18\x01 \x01(\x05\x12\x30\n\npreference\x18\x02 \x01(\x0b\x32\x1c.common.k8s.NodeSelectorTerm\"\x8d\x01\n\x10NodeSelectorTerm\x12>\n\x11match_expressions\x18\x01 \x03(\x0b\x32#.common.k8s.NodeSelectorRequirement\x12\x39\n\x0cmatch_fields\x18\x02 \x03(\x0b\x32#.common.k8s.NodeSelectorRequirement\"j\n\x17NodeSelectorRequirement\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x32\n\x08operator\x18\x02 \x01(\x0e\x32 .common.k8s.NodeSelectorOperator\x12\x0e\n\x06values\x18\x03 \x03(\t\"\xca\x01\n\x0bPodAffinity\x12X\n3required_during_scheduling_ignored_during_execution\x18\x01 \x03(\x0b\x32\x1b.common.k8s.PodAffinityTerm\x12\x61\n4preferred_during_scheduling_ignored_during_execution\x18\x02 \x03(\x0b\x32#.common.k8s.WeightedPodAffinityTerm\"\xce\x01\n\x0fPodAntiAffinity\x12X\n3required_during_scheduling_ignored_during_execution\x18\x01 \x03(\x0b\x32\x1b.common.k8s.PodAffinityTerm\x12\x61\n4preferred_during_scheduling_ignored_during_execution\x18\x02 \x03(\x0b\x32#.common.k8s.WeightedPodAffinityTerm\"\xa5\x01\n\x0fPodAffinityTerm\x12\x31\n\x0elabel_selector\x18\x01 \x01(\x0b\x32\x19.common.k8s.LabelSelector\x12\x12\n\nnamespaces\x18\x02 \x03(\t\x12\x14\n\x0ctopology_key\x18\x03 \x01(\t\x12\x35\n\x12namespace_selector\x18\x04 \x01(\x0b\x32\x19.common.k8s.LabelSelector\"a\n\x17WeightedPodAffinityTerm\x12\x0e\n\x06weight\x18\x01 \x01(\x05\x12\x36\n\x11pod_affinity_term\x18\x02 \x01(\x0b\x32\x1b.common.k8s.PodAffinityTerm\"\xc6\x01\n\rLabelSelector\x12@\n\x0cmatch_labels\x18\x01 \x03(\x0b\x32*.common.k8s.LabelSelector.MatchLabelsEntry\x12?\n\x11match_expressions\x18\x02 \x03(\x0b\x32$.common.k8s.LabelSelectorRequirement\x1a\x32\n\x10MatchLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"l\n\x18LabelSelectorRequirement\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x33\n\x08operator\x18\x02 \x01(\x0e\x32!.common.k8s.LabelSelectorOperator\x12\x0e\n\x06values\x18\x03 \x03(\t\"l\n\x14ResourceRequirements\x12\x13\n\x0b\x63pu_request\x18\x01 \x01(\t\x12\x11\n\tcpu_limit\x18\x02 \x01(\t\x12\x16\n\x0ememory_request\x18\x03 \x01(\t\x12\x14\n\x0cmemory_limit\x18\x04 \x01(\t\"\x9b\x01\n\x03Pod\x12\x0c\n\x04name\x18\x01 \x01(\t\x12+\n\x06labels\x18\x02 \x03(\x0b\x32\x1b.common.k8s.Pod.LabelsEntry\x12*\n\x0cstatus_phase\x18\x03 \x01(\x0e\x32\x14.common.k8s.PodPhase\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x9f\x01\n\x0bServiceSpec\x12&\n\x05ports\x18\x01 \x03(\x0b\x32\x17.common.k8s.ServicePort\x12\x37\n\x08selector\x18\x02 \x03(\x0b\x32%.common.k8s.ServiceSpec.SelectorEntry\x1a/\n\rSelectorEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\")\n\x0bServicePort\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\x05\"\xe5\x01\n\x11NetworkPolicySpec\x12/\n\x0cpod_selector\x18\x01 \x01(\x0b\x32\x19.common.k8s.LabelSelector\x12\x35\n\x07ingress\x18\x02 \x03(\x0b\x32$.common.k8s.NetworkPolicyIngressRule\x12\x33\n\x06\x65gress\x18\x03 \x03(\x0b\x32#.common.k8s.NetworkPolicyEgressRule\x12\x33\n\x0cpolicy_types\x18\x04 \x03(\x0e\x32\x1d.common.k8s.NetworkPolicyType\"r\n\x17NetworkPolicyEgressRule\x12,\n\x05ports\x18\x01 \x03(\x0b\x32\x1d.common.k8s.NetworkPolicyPort\x12)\n\x02to\x18\x02 \x03(\x0b\x32\x1d.common.k8s.NetworkPolicyPeer\"u\n\x18NetworkPolicyIngressRule\x12,\n\x05ports\x18\x01 \x03(\x0b\x32\x1d.common.k8s.NetworkPolicyPort\x12+\n\x04\x66rom\x18\x02 \x03(\x0b\x32\x1d.common.k8s.NetworkPolicyPeer\"z\n\x11NetworkPolicyPort\x12-\n\x08protocol\x18\x01 \x01(\x0e\x32\x1b.common.k8s.NetworkProtocol\x12%\n\x04port\x18\x02 \x01(\x0b\x32\x17.common.k8s.IntOrString\x12\x0f\n\x07\x65ndPort\x18\x03 \x01(\x05\"\x9f\x01\n\x11NetworkPolicyPeer\x12.\n\x0bpodSelector\x18\x01 \x01(\x0b\x32\x19.common.k8s.LabelSelector\x12\x34\n\x11namespaceSelector\x18\x02 \x01(\x0b\x32\x19.common.k8s.LabelSelector\x12$\n\x07ipBlock\x18\x03 \x01(\x0b\x32\x13.common.k8s.IPBlock\"\'\n\x07IPBlock\x12\x0c\n\x04\x63idr\x18\x01 \x01(\t\x12\x0e\n\x06\x65xcept\x18\x02 \x03(\t\"Q\n\x0bIntOrString\x12\x14\n\x07int_val\x18\x01 \x01(\x05H\x00\x88\x01\x01\x12\x14\n\x07str_val\x18\x02 \x01(\tH\x01\x88\x01\x01\x42\n\n\x08_int_valB\n\n\x08_str_val\"\xe5\x01\n\x19PersistentVolumeClaimSpec\x12/\n\x0c\x61\x63\x63\x65ss_modes\x18\x01 \x03(\x0e\x32\x19.common.k8s.PVCAccessMode\x12+\n\x08selector\x18\x02 \x01(\x0b\x32\x19.common.k8s.LabelSelector\x12\x39\n\tresources\x18\x03 \x01(\x0b\x32&.common.k8s.VolumeResourceRequirements\x12\x13\n\x0bvolume_name\x18\x04 \x01(\t\x12\x1a\n\x12storage_class_name\x18\x05 \x01(\t\"5\n\x1aVolumeResourceRequirements\x12\x17\n\x0fstorage_request\x18\x01 \x01(\t\"\x86\x01\n\x0bVolumeMount\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x11\n\tread_only\x18\x02 \x01(\x08\x12\x12\n\nmount_path\x18\x03 \x01(\t\x12\x10\n\x08sub_path\x18\x04 \x01(\t\x12\x19\n\x11mount_propagation\x18\x05 \x01(\t\x12\x15\n\rsub_path_expr\x18\x06 \x01(\t\"\xe0\x01\n\x06Volume\x12\x0c\n\x04name\x18\x01 \x01(\t\x12N\n\x15persistentVolumeClaim\x18\x02 \x01(\x0b\x32-.common.k8s.PersistentVolumeClaimVolumeSourceH\x00\x12\x34\n\x08\x65mptyDir\x18\x03 \x01(\x0b\x32 .common.k8s.EmptyDirVolumeSourceH\x00\x12\x30\n\x06secret\x18\x04 \x01(\x0b\x32\x1e.common.k8s.SecretVolumeSourceH\x00\x42\x10\n\x0evolumne_source\"J\n!PersistentVolumeClaimVolumeSource\x12\x12\n\nclaim_name\x18\x01 \x01(\t\x12\x11\n\tread_only\x18\x02 \x01(\x08\"N\n\x14\x45mptyDirVolumeSource\x12\x0e\n\x06medium\x18\x01 \x01(\t\x12\x17\n\nsize_limit\x18\x02 \x01(\tH\x00\x88\x01\x01\x42\r\n\x0b_size_limit\"\xa5\x01\n\x12SecretVolumeSource\x12\x13\n\x0bsecret_name\x18\x01 \x01(\t\x12$\n\x05items\x18\x02 \x03(\x0b\x32\x15.common.k8s.KeyToPath\x12\x19\n\x0c\x64\x65\x66\x61ult_mode\x18\x03 \x01(\x05H\x00\x88\x01\x01\x12\x18\n\x0bis_optional\x18\x04 \x01(\x08H\x01\x88\x01\x01\x42\x0f\n\r_default_modeB\x0e\n\x0c_is_optional\"B\n\tKeyToPath\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04path\x18\x02 \x01(\t\x12\x11\n\x04mode\x18\x03 \x01(\x05H\x00\x88\x01\x01\x42\x07\n\x05_mode\"%\n\x06\x45nvVar\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t*|\n\x11HelmReleaseStatus\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0c\n\x08\x44\x45PLOYED\x10\x01\x12\x10\n\x0cUNINSTALLING\x10\x02\x12\x0e\n\nINSTALLING\x10\x03\x12\r\n\tUPGRADING\x10\x04\x12\n\n\x06\x46\x41ILED\x10\x05\x12\x0f\n\x0bUNINSTALLED\x10\x06*\xa0\x01\n\x0bServiceType\x12\x18\n\x14SERVICE_TYPE_UNKNOWN\x10\x00\x12\x1b\n\x17SERVICE_TYPE_CLUSTER_IP\x10\x01\x12\x1a\n\x16SERVICE_TYPE_NODE_PORT\x10\x02\x12\x1e\n\x1aSERVICE_TYPE_LOAD_BALANCER\x10\x03\x12\x1e\n\x1aSERVICE_TYPE_EXTERNAL_NAME\x10\x04*\x87\x01\n\x0bTaintEffect\x12\x18\n\x14TAINT_EFFECT_UNKNOWN\x10\x00\x12\x1c\n\x18TAINT_EFFECT_NO_SCHEDULE\x10\x01\x12#\n\x1fTAINT_EFFECT_PREFER_NO_SCHEDULE\x10\x02\x12\x1b\n\x17TAINT_EFFECT_NO_EXECUTE\x10\x03*t\n\x12TolerationOperator\x12\x1f\n\x1bTOLERATION_OPERATOR_UNKNOWN\x10\x00\x12\x1e\n\x1aTOLERATION_OPERATOR_EXISTS\x10\x01\x12\x1d\n\x19TOLERATION_OPERATOR_EQUAL\x10\x02*\x88\x02\n\x14NodeSelectorOperator\x12\"\n\x1eNODE_SELECTOR_OPERATOR_UNKNOWN\x10\x00\x12\x1d\n\x19NODE_SELECTOR_OPERATOR_IN\x10\x01\x12!\n\x1dNODE_SELECTOR_OPERATOR_NOT_IN\x10\x02\x12!\n\x1dNODE_SELECTOR_OPERATOR_EXISTS\x10\x03\x12)\n%NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST\x10\x04\x12\x1d\n\x19NODE_SELECTOR_OPERATOR_GT\x10\x05\x12\x1d\n\x19NODE_SELECTOR_OPERATOR_LT\x10\x06*\xd0\x01\n\x15LabelSelectorOperator\x12#\n\x1fLABEL_SELECTOR_OPERATOR_UNKNOWN\x10\x00\x12\x1e\n\x1aLABEL_SELECTOR_OPERATOR_IN\x10\x01\x12\"\n\x1eLABEL_SELECTOR_OPERATOR_NOT_IN\x10\x02\x12\"\n\x1eLABEL_SELECTOR_OPERATOR_EXISTS\x10\x03\x12*\n&LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST\x10\x04*~\n\x08PodPhase\x12\x15\n\x11POD_PHASE_UNKNOWN\x10\x00\x12\x15\n\x11POD_PHASE_PENDING\x10\x01\x12\x15\n\x11POD_PHASE_RUNNING\x10\x02\x12\x17\n\x13POD_PHASE_SUCCEEDED\x10\x03\x12\x14\n\x10POD_PHASE_FAILED\x10\x04*~\n\x0fNetworkProtocol\x12\x1c\n\x18NETWORK_PROTOCOL_UNKNOWN\x10\x00\x12\x18\n\x14NETWORK_PROTOCOL_TCP\x10\x01\x12\x18\n\x14NETWORK_PROTOCOL_UDP\x10\x02\x12\x19\n\x15NETWORK_PROTOCOL_SCTP\x10\x03*f\n\x11NetworkPolicyType\x12\x1a\n\x16NETWORK_POLICY_UNKNOWN\x10\x00\x12\x1a\n\x16NETWORK_POLICY_INGRESS\x10\x01\x12\x19\n\x15NETWORK_POLICY_EGRESS\x10\x02*\xc7\x01\n\rPVCAccessMode\x12\x1f\n\x1bPVC_ACCESS_MODE_UNSPECIFIED\x10\x00\x12#\n\x1fPVC_ACCESS_MODE_READ_WRITE_ONCE\x10\x01\x12#\n\x1fPVC_ACCESS_MODE_READ_WRITE_MANY\x10\x02\x12\"\n\x1ePVC_ACCESS_MODE_READ_ONLY_MANY\x10\x03\x12\'\n#PVC_ACCESS_MODE_READ_WRITE_ONCE_POD\x10\x04\x42\x37Z5github.com/risingwavelabs/cloudagent/pbgen/common/k8sb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'common.k8s_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/common/k8s'
  _globals['_CONFIGMAP_DATAENTRY']._loaded_options = None
  _globals['_CONFIGMAP_DATAENTRY']._serialized_options = b'8\001'
  _globals['_CONFIGMAP_BINARYDATAENTRY']._loaded_options = None
  _globals['_CONFIGMAP_BINARYDATAENTRY']._serialized_options = b'8\001'
  _globals['_SECRET_DATAENTRY']._loaded_options = None
  _globals['_SECRET_DATAENTRY']._serialized_options = b'8\001'
  _globals['_SECRET_STRINGDATAENTRY']._loaded_options = None
  _globals['_SECRET_STRINGDATAENTRY']._serialized_options = b'8\001'
  _globals['_LABELSELECTOR_MATCHLABELSENTRY']._loaded_options = None
  _globals['_LABELSELECTOR_MATCHLABELSENTRY']._serialized_options = b'8\001'
  _globals['_POD_LABELSENTRY']._loaded_options = None
  _globals['_POD_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_SERVICESPEC_SELECTORENTRY']._loaded_options = None
  _globals['_SERVICESPEC_SELECTORENTRY']._serialized_options = b'8\001'
  _globals['_HELMRELEASESTATUS']._serialized_start=5076
  _globals['_HELMRELEASESTATUS']._serialized_end=5200
  _globals['_SERVICETYPE']._serialized_start=5203
  _globals['_SERVICETYPE']._serialized_end=5363
  _globals['_TAINTEFFECT']._serialized_start=5366
  _globals['_TAINTEFFECT']._serialized_end=5501
  _globals['_TOLERATIONOPERATOR']._serialized_start=5503
  _globals['_TOLERATIONOPERATOR']._serialized_end=5619
  _globals['_NODESELECTOROPERATOR']._serialized_start=5622
  _globals['_NODESELECTOROPERATOR']._serialized_end=5886
  _globals['_LABELSELECTOROPERATOR']._serialized_start=5889
  _globals['_LABELSELECTOROPERATOR']._serialized_end=6097
  _globals['_PODPHASE']._serialized_start=6099
  _globals['_PODPHASE']._serialized_end=6225
  _globals['_NETWORKPROTOCOL']._serialized_start=6227
  _globals['_NETWORKPROTOCOL']._serialized_end=6353
  _globals['_NETWORKPOLICYTYPE']._serialized_start=6355
  _globals['_NETWORKPOLICYTYPE']._serialized_end=6457
  _globals['_PVCACCESSMODE']._serialized_start=6460
  _globals['_PVCACCESSMODE']._serialized_end=6659
  _globals['_CONFIGMAP']._serialized_start=33
  _globals['_CONFIGMAP']._serialized_end=266
  _globals['_CONFIGMAP_DATAENTRY']._serialized_start=172
  _globals['_CONFIGMAP_DATAENTRY']._serialized_end=215
  _globals['_CONFIGMAP_BINARYDATAENTRY']._serialized_start=217
  _globals['_CONFIGMAP_BINARYDATAENTRY']._serialized_end=266
  _globals['_SECRET']._serialized_start=269
  _globals['_SECRET']._serialized_end=507
  _globals['_SECRET_DATAENTRY']._serialized_start=413
  _globals['_SECRET_DATAENTRY']._serialized_end=456
  _globals['_SECRET_STRINGDATAENTRY']._serialized_start=458
  _globals['_SECRET_STRINGDATAENTRY']._serialized_end=507
  _globals['_SERVICEACCOUNT']._serialized_start=509
  _globals['_SERVICEACCOUNT']._serialized_end=558
  _globals['_HELMRELEASE']._serialized_start=560
  _globals['_HELMRELEASE']._serialized_end=678
  _globals['_TOLERATION']._serialized_start=681
  _globals['_TOLERATION']._serialized_end=862
  _globals['_AFFINITY']._serialized_start=865
  _globals['_AFFINITY']._serialized_end=1027
  _globals['_NODEAFFINITY']._serialized_start=1030
  _globals['_NODEAFFINITY']._serialized_end=1230
  _globals['_NODESELECTOR']._serialized_start=1232
  _globals['_NODESELECTOR']._serialized_end=1305
  _globals['_PREFERREDSCHEDULINGTERM']._serialized_start=1307
  _globals['_PREFERREDSCHEDULINGTERM']._serialized_end=1398
  _globals['_NODESELECTORTERM']._serialized_start=1401
  _globals['_NODESELECTORTERM']._serialized_end=1542
  _globals['_NODESELECTORREQUIREMENT']._serialized_start=1544
  _globals['_NODESELECTORREQUIREMENT']._serialized_end=1650
  _globals['_PODAFFINITY']._serialized_start=1653
  _globals['_PODAFFINITY']._serialized_end=1855
  _globals['_PODANTIAFFINITY']._serialized_start=1858
  _globals['_PODANTIAFFINITY']._serialized_end=2064
  _globals['_PODAFFINITYTERM']._serialized_start=2067
  _globals['_PODAFFINITYTERM']._serialized_end=2232
  _globals['_WEIGHTEDPODAFFINITYTERM']._serialized_start=2234
  _globals['_WEIGHTEDPODAFFINITYTERM']._serialized_end=2331
  _globals['_LABELSELECTOR']._serialized_start=2334
  _globals['_LABELSELECTOR']._serialized_end=2532
  _globals['_LABELSELECTOR_MATCHLABELSENTRY']._serialized_start=2482
  _globals['_LABELSELECTOR_MATCHLABELSENTRY']._serialized_end=2532
  _globals['_LABELSELECTORREQUIREMENT']._serialized_start=2534
  _globals['_LABELSELECTORREQUIREMENT']._serialized_end=2642
  _globals['_RESOURCEREQUIREMENTS']._serialized_start=2644
  _globals['_RESOURCEREQUIREMENTS']._serialized_end=2752
  _globals['_POD']._serialized_start=2755
  _globals['_POD']._serialized_end=2910
  _globals['_POD_LABELSENTRY']._serialized_start=2865
  _globals['_POD_LABELSENTRY']._serialized_end=2910
  _globals['_SERVICESPEC']._serialized_start=2913
  _globals['_SERVICESPEC']._serialized_end=3072
  _globals['_SERVICESPEC_SELECTORENTRY']._serialized_start=3025
  _globals['_SERVICESPEC_SELECTORENTRY']._serialized_end=3072
  _globals['_SERVICEPORT']._serialized_start=3074
  _globals['_SERVICEPORT']._serialized_end=3115
  _globals['_NETWORKPOLICYSPEC']._serialized_start=3118
  _globals['_NETWORKPOLICYSPEC']._serialized_end=3347
  _globals['_NETWORKPOLICYEGRESSRULE']._serialized_start=3349
  _globals['_NETWORKPOLICYEGRESSRULE']._serialized_end=3463
  _globals['_NETWORKPOLICYINGRESSRULE']._serialized_start=3465
  _globals['_NETWORKPOLICYINGRESSRULE']._serialized_end=3582
  _globals['_NETWORKPOLICYPORT']._serialized_start=3584
  _globals['_NETWORKPOLICYPORT']._serialized_end=3706
  _globals['_NETWORKPOLICYPEER']._serialized_start=3709
  _globals['_NETWORKPOLICYPEER']._serialized_end=3868
  _globals['_IPBLOCK']._serialized_start=3870
  _globals['_IPBLOCK']._serialized_end=3909
  _globals['_INTORSTRING']._serialized_start=3911
  _globals['_INTORSTRING']._serialized_end=3992
  _globals['_PERSISTENTVOLUMECLAIMSPEC']._serialized_start=3995
  _globals['_PERSISTENTVOLUMECLAIMSPEC']._serialized_end=4224
  _globals['_VOLUMERESOURCEREQUIREMENTS']._serialized_start=4226
  _globals['_VOLUMERESOURCEREQUIREMENTS']._serialized_end=4279
  _globals['_VOLUMEMOUNT']._serialized_start=4282
  _globals['_VOLUMEMOUNT']._serialized_end=4416
  _globals['_VOLUME']._serialized_start=4419
  _globals['_VOLUME']._serialized_end=4643
  _globals['_PERSISTENTVOLUMECLAIMVOLUMESOURCE']._serialized_start=4645
  _globals['_PERSISTENTVOLUMECLAIMVOLUMESOURCE']._serialized_end=4719
  _globals['_EMPTYDIRVOLUMESOURCE']._serialized_start=4721
  _globals['_EMPTYDIRVOLUMESOURCE']._serialized_end=4799
  _globals['_SECRETVOLUMESOURCE']._serialized_start=4802
  _globals['_SECRETVOLUMESOURCE']._serialized_end=4967
  _globals['_KEYTOPATH']._serialized_start=4969
  _globals['_KEYTOPATH']._serialized_end=5035
  _globals['_ENVVAR']._serialized_start=5037
  _globals['_ENVVAR']._serialized_end=5074
# @@protoc_insertion_point(module_scope)
