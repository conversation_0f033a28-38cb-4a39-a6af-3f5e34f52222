from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CloudSQLSSLMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    CloudSQLSSLMode_UNKNOWN: _ClassVar[CloudSQLSSLMode]
    CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED: _ClassVar[CloudSQLSSLMode]
    CloudSQLSSLMode_ENCRYPTED_ONLY: _ClassVar[CloudSQLSSLMode]
    CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED: _ClassVar[CloudSQLSSLMode]

class SQLInstanceStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[SQLInstanceStatus]
    UPDATING: _ClassVar[SQLInstanceStatus]
    UP_TO_DATE: _ClassVar[SQLInstanceStatus]
    STOPPED: _ClassVar[SQLInstanceStatus]
CloudSQLSSLMode_UNKNOWN: CloudSQLSSLMode
CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED: CloudSQLSSLMode
CloudSQLSSLMode_ENCRYPTED_ONLY: CloudSQLSSLMode
CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED: CloudSQLSSLMode
UNKNOWN: SQLInstanceStatus
UPDATING: SQLInstanceStatus
UP_TO_DATE: SQLInstanceStatus
STOPPED: SQLInstanceStatus

class SQLInstanceSpec(_message.Message):
    __slots__ = ("resource_id", "instance_type", "database_version", "region", "root_password", "settings")
    RESOURCE_ID_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_TYPE_FIELD_NUMBER: _ClassVar[int]
    DATABASE_VERSION_FIELD_NUMBER: _ClassVar[int]
    REGION_FIELD_NUMBER: _ClassVar[int]
    ROOT_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    SETTINGS_FIELD_NUMBER: _ClassVar[int]
    resource_id: str
    instance_type: str
    database_version: str
    region: str
    root_password: RootPassword
    settings: SQLInstanceSettings
    def __init__(self, resource_id: _Optional[str] = ..., instance_type: _Optional[str] = ..., database_version: _Optional[str] = ..., region: _Optional[str] = ..., root_password: _Optional[_Union[RootPassword, _Mapping]] = ..., settings: _Optional[_Union[SQLInstanceSettings, _Mapping]] = ...) -> None: ...

class SQLInstanceSettings(_message.Message):
    __slots__ = ("tier", "disk_size", "ip_configuration", "deletion_protection_enabled", "database_flags")
    TIER_FIELD_NUMBER: _ClassVar[int]
    DISK_SIZE_FIELD_NUMBER: _ClassVar[int]
    IP_CONFIGURATION_FIELD_NUMBER: _ClassVar[int]
    DELETION_PROTECTION_ENABLED_FIELD_NUMBER: _ClassVar[int]
    DATABASE_FLAGS_FIELD_NUMBER: _ClassVar[int]
    tier: str
    disk_size: int
    ip_configuration: IPConfiguration
    deletion_protection_enabled: bool
    database_flags: _containers.RepeatedCompositeFieldContainer[DatabaseFlag]
    def __init__(self, tier: _Optional[str] = ..., disk_size: _Optional[int] = ..., ip_configuration: _Optional[_Union[IPConfiguration, _Mapping]] = ..., deletion_protection_enabled: bool = ..., database_flags: _Optional[_Iterable[_Union[DatabaseFlag, _Mapping]]] = ...) -> None: ...

class DatabaseFlag(_message.Message):
    __slots__ = ("name", "value")
    NAME_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    name: str
    value: str
    def __init__(self, name: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...

class RootPassword(_message.Message):
    __slots__ = ("value", "value_from")
    VALUE_FIELD_NUMBER: _ClassVar[int]
    VALUE_FROM_FIELD_NUMBER: _ClassVar[int]
    value: str
    value_from: RootPasswordValueFrom
    def __init__(self, value: _Optional[str] = ..., value_from: _Optional[_Union[RootPasswordValueFrom, _Mapping]] = ...) -> None: ...

class RootPasswordValueFrom(_message.Message):
    __slots__ = ("secretKeyRef",)
    SECRETKEYREF_FIELD_NUMBER: _ClassVar[int]
    secretKeyRef: SecretKeyRef
    def __init__(self, secretKeyRef: _Optional[_Union[SecretKeyRef, _Mapping]] = ...) -> None: ...

class SecretKeyRef(_message.Message):
    __slots__ = ("key", "name")
    KEY_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    key: str
    name: str
    def __init__(self, key: _Optional[str] = ..., name: _Optional[str] = ...) -> None: ...

class IPConfiguration(_message.Message):
    __slots__ = ("ipv4_enabled", "private_network_ref", "allocated_ip_range", "ssl_mode")
    IPV4_ENABLED_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_NETWORK_REF_FIELD_NUMBER: _ClassVar[int]
    ALLOCATED_IP_RANGE_FIELD_NUMBER: _ClassVar[int]
    SSL_MODE_FIELD_NUMBER: _ClassVar[int]
    ipv4_enabled: bool
    private_network_ref: PrivateNetworkRef
    allocated_ip_range: str
    ssl_mode: CloudSQLSSLMode
    def __init__(self, ipv4_enabled: bool = ..., private_network_ref: _Optional[_Union[PrivateNetworkRef, _Mapping]] = ..., allocated_ip_range: _Optional[str] = ..., ssl_mode: _Optional[_Union[CloudSQLSSLMode, str]] = ...) -> None: ...

class PrivateNetworkRef(_message.Message):
    __slots__ = ("external",)
    EXTERNAL_FIELD_NUMBER: _ClassVar[int]
    external: str
    def __init__(self, external: _Optional[str] = ...) -> None: ...
