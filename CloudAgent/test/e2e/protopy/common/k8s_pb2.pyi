from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class HelmReleaseStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[HelmReleaseStatus]
    DEPLOYED: _ClassVar[HelmReleaseStatus]
    UNINSTALLING: _ClassVar[HelmReleaseStatus]
    INSTALLING: _ClassVar[HelmReleaseStatus]
    UPGRADING: _ClassVar[HelmReleaseStatus]
    FAILED: _ClassVar[HelmReleaseStatus]
    UNINSTALLED: _ClassVar[HelmReleaseStatus]

class ServiceType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SERVICE_TYPE_UNKNOWN: _ClassVar[ServiceType]
    SERVICE_TYPE_CLUSTER_IP: _ClassVar[ServiceType]
    SERVICE_TYPE_NODE_PORT: _ClassVar[ServiceType]
    SERVICE_TYPE_LOAD_BALANCER: _ClassVar[ServiceType]
    SERVICE_TYPE_EXTERNAL_NAME: _ClassVar[ServiceType]

class TaintEffect(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TAINT_EFFECT_UNKNOWN: _ClassVar[TaintEffect]
    TAINT_EFFECT_NO_SCHEDULE: _ClassVar[TaintEffect]
    TAINT_EFFECT_PREFER_NO_SCHEDULE: _ClassVar[TaintEffect]
    TAINT_EFFECT_NO_EXECUTE: _ClassVar[TaintEffect]

class TolerationOperator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    TOLERATION_OPERATOR_UNKNOWN: _ClassVar[TolerationOperator]
    TOLERATION_OPERATOR_EXISTS: _ClassVar[TolerationOperator]
    TOLERATION_OPERATOR_EQUAL: _ClassVar[TolerationOperator]

class NodeSelectorOperator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    NODE_SELECTOR_OPERATOR_UNKNOWN: _ClassVar[NodeSelectorOperator]
    NODE_SELECTOR_OPERATOR_IN: _ClassVar[NodeSelectorOperator]
    NODE_SELECTOR_OPERATOR_NOT_IN: _ClassVar[NodeSelectorOperator]
    NODE_SELECTOR_OPERATOR_EXISTS: _ClassVar[NodeSelectorOperator]
    NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST: _ClassVar[NodeSelectorOperator]
    NODE_SELECTOR_OPERATOR_GT: _ClassVar[NodeSelectorOperator]
    NODE_SELECTOR_OPERATOR_LT: _ClassVar[NodeSelectorOperator]

class LabelSelectorOperator(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    LABEL_SELECTOR_OPERATOR_UNKNOWN: _ClassVar[LabelSelectorOperator]
    LABEL_SELECTOR_OPERATOR_IN: _ClassVar[LabelSelectorOperator]
    LABEL_SELECTOR_OPERATOR_NOT_IN: _ClassVar[LabelSelectorOperator]
    LABEL_SELECTOR_OPERATOR_EXISTS: _ClassVar[LabelSelectorOperator]
    LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST: _ClassVar[LabelSelectorOperator]

class PodPhase(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    POD_PHASE_UNKNOWN: _ClassVar[PodPhase]
    POD_PHASE_PENDING: _ClassVar[PodPhase]
    POD_PHASE_RUNNING: _ClassVar[PodPhase]
    POD_PHASE_SUCCEEDED: _ClassVar[PodPhase]
    POD_PHASE_FAILED: _ClassVar[PodPhase]

class NetworkProtocol(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    NETWORK_PROTOCOL_UNKNOWN: _ClassVar[NetworkProtocol]
    NETWORK_PROTOCOL_TCP: _ClassVar[NetworkProtocol]
    NETWORK_PROTOCOL_UDP: _ClassVar[NetworkProtocol]
    NETWORK_PROTOCOL_SCTP: _ClassVar[NetworkProtocol]

class NetworkPolicyType(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    NETWORK_POLICY_UNKNOWN: _ClassVar[NetworkPolicyType]
    NETWORK_POLICY_INGRESS: _ClassVar[NetworkPolicyType]
    NETWORK_POLICY_EGRESS: _ClassVar[NetworkPolicyType]

class PVCAccessMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PVC_ACCESS_MODE_UNSPECIFIED: _ClassVar[PVCAccessMode]
    PVC_ACCESS_MODE_READ_WRITE_ONCE: _ClassVar[PVCAccessMode]
    PVC_ACCESS_MODE_READ_WRITE_MANY: _ClassVar[PVCAccessMode]
    PVC_ACCESS_MODE_READ_ONLY_MANY: _ClassVar[PVCAccessMode]
    PVC_ACCESS_MODE_READ_WRITE_ONCE_POD: _ClassVar[PVCAccessMode]
UNKNOWN: HelmReleaseStatus
DEPLOYED: HelmReleaseStatus
UNINSTALLING: HelmReleaseStatus
INSTALLING: HelmReleaseStatus
UPGRADING: HelmReleaseStatus
FAILED: HelmReleaseStatus
UNINSTALLED: HelmReleaseStatus
SERVICE_TYPE_UNKNOWN: ServiceType
SERVICE_TYPE_CLUSTER_IP: ServiceType
SERVICE_TYPE_NODE_PORT: ServiceType
SERVICE_TYPE_LOAD_BALANCER: ServiceType
SERVICE_TYPE_EXTERNAL_NAME: ServiceType
TAINT_EFFECT_UNKNOWN: TaintEffect
TAINT_EFFECT_NO_SCHEDULE: TaintEffect
TAINT_EFFECT_PREFER_NO_SCHEDULE: TaintEffect
TAINT_EFFECT_NO_EXECUTE: TaintEffect
TOLERATION_OPERATOR_UNKNOWN: TolerationOperator
TOLERATION_OPERATOR_EXISTS: TolerationOperator
TOLERATION_OPERATOR_EQUAL: TolerationOperator
NODE_SELECTOR_OPERATOR_UNKNOWN: NodeSelectorOperator
NODE_SELECTOR_OPERATOR_IN: NodeSelectorOperator
NODE_SELECTOR_OPERATOR_NOT_IN: NodeSelectorOperator
NODE_SELECTOR_OPERATOR_EXISTS: NodeSelectorOperator
NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST: NodeSelectorOperator
NODE_SELECTOR_OPERATOR_GT: NodeSelectorOperator
NODE_SELECTOR_OPERATOR_LT: NodeSelectorOperator
LABEL_SELECTOR_OPERATOR_UNKNOWN: LabelSelectorOperator
LABEL_SELECTOR_OPERATOR_IN: LabelSelectorOperator
LABEL_SELECTOR_OPERATOR_NOT_IN: LabelSelectorOperator
LABEL_SELECTOR_OPERATOR_EXISTS: LabelSelectorOperator
LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST: LabelSelectorOperator
POD_PHASE_UNKNOWN: PodPhase
POD_PHASE_PENDING: PodPhase
POD_PHASE_RUNNING: PodPhase
POD_PHASE_SUCCEEDED: PodPhase
POD_PHASE_FAILED: PodPhase
NETWORK_PROTOCOL_UNKNOWN: NetworkProtocol
NETWORK_PROTOCOL_TCP: NetworkProtocol
NETWORK_PROTOCOL_UDP: NetworkProtocol
NETWORK_PROTOCOL_SCTP: NetworkProtocol
NETWORK_POLICY_UNKNOWN: NetworkPolicyType
NETWORK_POLICY_INGRESS: NetworkPolicyType
NETWORK_POLICY_EGRESS: NetworkPolicyType
PVC_ACCESS_MODE_UNSPECIFIED: PVCAccessMode
PVC_ACCESS_MODE_READ_WRITE_ONCE: PVCAccessMode
PVC_ACCESS_MODE_READ_WRITE_MANY: PVCAccessMode
PVC_ACCESS_MODE_READ_ONLY_MANY: PVCAccessMode
PVC_ACCESS_MODE_READ_WRITE_ONCE_POD: PVCAccessMode

class ConfigMap(_message.Message):
    __slots__ = ("immutable", "data", "binary_data")
    class DataEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class BinaryDataEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: bytes
        def __init__(self, key: _Optional[str] = ..., value: _Optional[bytes] = ...) -> None: ...
    IMMUTABLE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    BINARY_DATA_FIELD_NUMBER: _ClassVar[int]
    immutable: bool
    data: _containers.ScalarMap[str, str]
    binary_data: _containers.ScalarMap[str, bytes]
    def __init__(self, immutable: bool = ..., data: _Optional[_Mapping[str, str]] = ..., binary_data: _Optional[_Mapping[str, bytes]] = ...) -> None: ...

class Secret(_message.Message):
    __slots__ = ("immutable", "data", "string_data", "type")
    class DataEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: bytes
        def __init__(self, key: _Optional[str] = ..., value: _Optional[bytes] = ...) -> None: ...
    class StringDataEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    IMMUTABLE_FIELD_NUMBER: _ClassVar[int]
    DATA_FIELD_NUMBER: _ClassVar[int]
    STRING_DATA_FIELD_NUMBER: _ClassVar[int]
    TYPE_FIELD_NUMBER: _ClassVar[int]
    immutable: bool
    data: _containers.ScalarMap[str, bytes]
    string_data: _containers.ScalarMap[str, str]
    type: str
    def __init__(self, immutable: bool = ..., data: _Optional[_Mapping[str, bytes]] = ..., string_data: _Optional[_Mapping[str, str]] = ..., type: _Optional[str] = ...) -> None: ...

class ServiceAccount(_message.Message):
    __slots__ = ("name", "namespace")
    NAME_FIELD_NUMBER: _ClassVar[int]
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    name: str
    namespace: str
    def __init__(self, name: _Optional[str] = ..., namespace: _Optional[str] = ...) -> None: ...

class HelmRelease(_message.Message):
    __slots__ = ("release_name", "namespace", "status", "version")
    RELEASE_NAME_FIELD_NUMBER: _ClassVar[int]
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    VERSION_FIELD_NUMBER: _ClassVar[int]
    release_name: str
    namespace: str
    status: HelmReleaseStatus
    version: str
    def __init__(self, release_name: _Optional[str] = ..., namespace: _Optional[str] = ..., status: _Optional[_Union[HelmReleaseStatus, str]] = ..., version: _Optional[str] = ...) -> None: ...

class Toleration(_message.Message):
    __slots__ = ("key", "value", "effect", "operator", "toleration_secs")
    KEY_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    EFFECT_FIELD_NUMBER: _ClassVar[int]
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    TOLERATION_SECS_FIELD_NUMBER: _ClassVar[int]
    key: str
    value: str
    effect: TaintEffect
    operator: TolerationOperator
    toleration_secs: int
    def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ..., effect: _Optional[_Union[TaintEffect, str]] = ..., operator: _Optional[_Union[TolerationOperator, str]] = ..., toleration_secs: _Optional[int] = ...) -> None: ...

class Affinity(_message.Message):
    __slots__ = ("node_affinity", "pod_affinity", "pod_anti_affinity")
    NODE_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    POD_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    POD_ANTI_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    node_affinity: NodeAffinity
    pod_affinity: PodAffinity
    pod_anti_affinity: PodAntiAffinity
    def __init__(self, node_affinity: _Optional[_Union[NodeAffinity, _Mapping]] = ..., pod_affinity: _Optional[_Union[PodAffinity, _Mapping]] = ..., pod_anti_affinity: _Optional[_Union[PodAntiAffinity, _Mapping]] = ...) -> None: ...

class NodeAffinity(_message.Message):
    __slots__ = ("required_during_scheduling_ignored_during_execution", "preferred_during_scheduling_ignored_during_execution")
    REQUIRED_DURING_SCHEDULING_IGNORED_DURING_EXECUTION_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DURING_SCHEDULING_IGNORED_DURING_EXECUTION_FIELD_NUMBER: _ClassVar[int]
    required_during_scheduling_ignored_during_execution: NodeSelector
    preferred_during_scheduling_ignored_during_execution: _containers.RepeatedCompositeFieldContainer[PreferredSchedulingTerm]
    def __init__(self, required_during_scheduling_ignored_during_execution: _Optional[_Union[NodeSelector, _Mapping]] = ..., preferred_during_scheduling_ignored_during_execution: _Optional[_Iterable[_Union[PreferredSchedulingTerm, _Mapping]]] = ...) -> None: ...

class NodeSelector(_message.Message):
    __slots__ = ("node_selector_terms",)
    NODE_SELECTOR_TERMS_FIELD_NUMBER: _ClassVar[int]
    node_selector_terms: _containers.RepeatedCompositeFieldContainer[NodeSelectorTerm]
    def __init__(self, node_selector_terms: _Optional[_Iterable[_Union[NodeSelectorTerm, _Mapping]]] = ...) -> None: ...

class PreferredSchedulingTerm(_message.Message):
    __slots__ = ("weight", "preference")
    WEIGHT_FIELD_NUMBER: _ClassVar[int]
    PREFERENCE_FIELD_NUMBER: _ClassVar[int]
    weight: int
    preference: NodeSelectorTerm
    def __init__(self, weight: _Optional[int] = ..., preference: _Optional[_Union[NodeSelectorTerm, _Mapping]] = ...) -> None: ...

class NodeSelectorTerm(_message.Message):
    __slots__ = ("match_expressions", "match_fields")
    MATCH_EXPRESSIONS_FIELD_NUMBER: _ClassVar[int]
    MATCH_FIELDS_FIELD_NUMBER: _ClassVar[int]
    match_expressions: _containers.RepeatedCompositeFieldContainer[NodeSelectorRequirement]
    match_fields: _containers.RepeatedCompositeFieldContainer[NodeSelectorRequirement]
    def __init__(self, match_expressions: _Optional[_Iterable[_Union[NodeSelectorRequirement, _Mapping]]] = ..., match_fields: _Optional[_Iterable[_Union[NodeSelectorRequirement, _Mapping]]] = ...) -> None: ...

class NodeSelectorRequirement(_message.Message):
    __slots__ = ("key", "operator", "values")
    KEY_FIELD_NUMBER: _ClassVar[int]
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    VALUES_FIELD_NUMBER: _ClassVar[int]
    key: str
    operator: NodeSelectorOperator
    values: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, key: _Optional[str] = ..., operator: _Optional[_Union[NodeSelectorOperator, str]] = ..., values: _Optional[_Iterable[str]] = ...) -> None: ...

class PodAffinity(_message.Message):
    __slots__ = ("required_during_scheduling_ignored_during_execution", "preferred_during_scheduling_ignored_during_execution")
    REQUIRED_DURING_SCHEDULING_IGNORED_DURING_EXECUTION_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DURING_SCHEDULING_IGNORED_DURING_EXECUTION_FIELD_NUMBER: _ClassVar[int]
    required_during_scheduling_ignored_during_execution: _containers.RepeatedCompositeFieldContainer[PodAffinityTerm]
    preferred_during_scheduling_ignored_during_execution: _containers.RepeatedCompositeFieldContainer[WeightedPodAffinityTerm]
    def __init__(self, required_during_scheduling_ignored_during_execution: _Optional[_Iterable[_Union[PodAffinityTerm, _Mapping]]] = ..., preferred_during_scheduling_ignored_during_execution: _Optional[_Iterable[_Union[WeightedPodAffinityTerm, _Mapping]]] = ...) -> None: ...

class PodAntiAffinity(_message.Message):
    __slots__ = ("required_during_scheduling_ignored_during_execution", "preferred_during_scheduling_ignored_during_execution")
    REQUIRED_DURING_SCHEDULING_IGNORED_DURING_EXECUTION_FIELD_NUMBER: _ClassVar[int]
    PREFERRED_DURING_SCHEDULING_IGNORED_DURING_EXECUTION_FIELD_NUMBER: _ClassVar[int]
    required_during_scheduling_ignored_during_execution: _containers.RepeatedCompositeFieldContainer[PodAffinityTerm]
    preferred_during_scheduling_ignored_during_execution: _containers.RepeatedCompositeFieldContainer[WeightedPodAffinityTerm]
    def __init__(self, required_during_scheduling_ignored_during_execution: _Optional[_Iterable[_Union[PodAffinityTerm, _Mapping]]] = ..., preferred_during_scheduling_ignored_during_execution: _Optional[_Iterable[_Union[WeightedPodAffinityTerm, _Mapping]]] = ...) -> None: ...

class PodAffinityTerm(_message.Message):
    __slots__ = ("label_selector", "namespaces", "topology_key", "namespace_selector")
    LABEL_SELECTOR_FIELD_NUMBER: _ClassVar[int]
    NAMESPACES_FIELD_NUMBER: _ClassVar[int]
    TOPOLOGY_KEY_FIELD_NUMBER: _ClassVar[int]
    NAMESPACE_SELECTOR_FIELD_NUMBER: _ClassVar[int]
    label_selector: LabelSelector
    namespaces: _containers.RepeatedScalarFieldContainer[str]
    topology_key: str
    namespace_selector: LabelSelector
    def __init__(self, label_selector: _Optional[_Union[LabelSelector, _Mapping]] = ..., namespaces: _Optional[_Iterable[str]] = ..., topology_key: _Optional[str] = ..., namespace_selector: _Optional[_Union[LabelSelector, _Mapping]] = ...) -> None: ...

class WeightedPodAffinityTerm(_message.Message):
    __slots__ = ("weight", "pod_affinity_term")
    WEIGHT_FIELD_NUMBER: _ClassVar[int]
    POD_AFFINITY_TERM_FIELD_NUMBER: _ClassVar[int]
    weight: int
    pod_affinity_term: PodAffinityTerm
    def __init__(self, weight: _Optional[int] = ..., pod_affinity_term: _Optional[_Union[PodAffinityTerm, _Mapping]] = ...) -> None: ...

class LabelSelector(_message.Message):
    __slots__ = ("match_labels", "match_expressions")
    class MatchLabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    MATCH_LABELS_FIELD_NUMBER: _ClassVar[int]
    MATCH_EXPRESSIONS_FIELD_NUMBER: _ClassVar[int]
    match_labels: _containers.ScalarMap[str, str]
    match_expressions: _containers.RepeatedCompositeFieldContainer[LabelSelectorRequirement]
    def __init__(self, match_labels: _Optional[_Mapping[str, str]] = ..., match_expressions: _Optional[_Iterable[_Union[LabelSelectorRequirement, _Mapping]]] = ...) -> None: ...

class LabelSelectorRequirement(_message.Message):
    __slots__ = ("key", "operator", "values")
    KEY_FIELD_NUMBER: _ClassVar[int]
    OPERATOR_FIELD_NUMBER: _ClassVar[int]
    VALUES_FIELD_NUMBER: _ClassVar[int]
    key: str
    operator: LabelSelectorOperator
    values: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, key: _Optional[str] = ..., operator: _Optional[_Union[LabelSelectorOperator, str]] = ..., values: _Optional[_Iterable[str]] = ...) -> None: ...

class ResourceRequirements(_message.Message):
    __slots__ = ("cpu_request", "cpu_limit", "memory_request", "memory_limit")
    CPU_REQUEST_FIELD_NUMBER: _ClassVar[int]
    CPU_LIMIT_FIELD_NUMBER: _ClassVar[int]
    MEMORY_REQUEST_FIELD_NUMBER: _ClassVar[int]
    MEMORY_LIMIT_FIELD_NUMBER: _ClassVar[int]
    cpu_request: str
    cpu_limit: str
    memory_request: str
    memory_limit: str
    def __init__(self, cpu_request: _Optional[str] = ..., cpu_limit: _Optional[str] = ..., memory_request: _Optional[str] = ..., memory_limit: _Optional[str] = ...) -> None: ...

class Pod(_message.Message):
    __slots__ = ("name", "labels", "status_phase")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    NAME_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    STATUS_PHASE_FIELD_NUMBER: _ClassVar[int]
    name: str
    labels: _containers.ScalarMap[str, str]
    status_phase: PodPhase
    def __init__(self, name: _Optional[str] = ..., labels: _Optional[_Mapping[str, str]] = ..., status_phase: _Optional[_Union[PodPhase, str]] = ...) -> None: ...

class ServiceSpec(_message.Message):
    __slots__ = ("ports", "selector")
    class SelectorEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    PORTS_FIELD_NUMBER: _ClassVar[int]
    SELECTOR_FIELD_NUMBER: _ClassVar[int]
    ports: _containers.RepeatedCompositeFieldContainer[ServicePort]
    selector: _containers.ScalarMap[str, str]
    def __init__(self, ports: _Optional[_Iterable[_Union[ServicePort, _Mapping]]] = ..., selector: _Optional[_Mapping[str, str]] = ...) -> None: ...

class ServicePort(_message.Message):
    __slots__ = ("name", "port")
    NAME_FIELD_NUMBER: _ClassVar[int]
    PORT_FIELD_NUMBER: _ClassVar[int]
    name: str
    port: int
    def __init__(self, name: _Optional[str] = ..., port: _Optional[int] = ...) -> None: ...

class NetworkPolicySpec(_message.Message):
    __slots__ = ("pod_selector", "ingress", "egress", "policy_types")
    POD_SELECTOR_FIELD_NUMBER: _ClassVar[int]
    INGRESS_FIELD_NUMBER: _ClassVar[int]
    EGRESS_FIELD_NUMBER: _ClassVar[int]
    POLICY_TYPES_FIELD_NUMBER: _ClassVar[int]
    pod_selector: LabelSelector
    ingress: _containers.RepeatedCompositeFieldContainer[NetworkPolicyIngressRule]
    egress: _containers.RepeatedCompositeFieldContainer[NetworkPolicyEgressRule]
    policy_types: _containers.RepeatedScalarFieldContainer[NetworkPolicyType]
    def __init__(self, pod_selector: _Optional[_Union[LabelSelector, _Mapping]] = ..., ingress: _Optional[_Iterable[_Union[NetworkPolicyIngressRule, _Mapping]]] = ..., egress: _Optional[_Iterable[_Union[NetworkPolicyEgressRule, _Mapping]]] = ..., policy_types: _Optional[_Iterable[_Union[NetworkPolicyType, str]]] = ...) -> None: ...

class NetworkPolicyEgressRule(_message.Message):
    __slots__ = ("ports", "to")
    PORTS_FIELD_NUMBER: _ClassVar[int]
    TO_FIELD_NUMBER: _ClassVar[int]
    ports: _containers.RepeatedCompositeFieldContainer[NetworkPolicyPort]
    to: _containers.RepeatedCompositeFieldContainer[NetworkPolicyPeer]
    def __init__(self, ports: _Optional[_Iterable[_Union[NetworkPolicyPort, _Mapping]]] = ..., to: _Optional[_Iterable[_Union[NetworkPolicyPeer, _Mapping]]] = ...) -> None: ...

class NetworkPolicyIngressRule(_message.Message):
    __slots__ = ("ports",)
    PORTS_FIELD_NUMBER: _ClassVar[int]
    FROM_FIELD_NUMBER: _ClassVar[int]
    ports: _containers.RepeatedCompositeFieldContainer[NetworkPolicyPort]
    def __init__(self, ports: _Optional[_Iterable[_Union[NetworkPolicyPort, _Mapping]]] = ..., **kwargs) -> None: ...

class NetworkPolicyPort(_message.Message):
    __slots__ = ("protocol", "port", "endPort")
    PROTOCOL_FIELD_NUMBER: _ClassVar[int]
    PORT_FIELD_NUMBER: _ClassVar[int]
    ENDPORT_FIELD_NUMBER: _ClassVar[int]
    protocol: NetworkProtocol
    port: IntOrString
    endPort: int
    def __init__(self, protocol: _Optional[_Union[NetworkProtocol, str]] = ..., port: _Optional[_Union[IntOrString, _Mapping]] = ..., endPort: _Optional[int] = ...) -> None: ...

class NetworkPolicyPeer(_message.Message):
    __slots__ = ("podSelector", "namespaceSelector", "ipBlock")
    PODSELECTOR_FIELD_NUMBER: _ClassVar[int]
    NAMESPACESELECTOR_FIELD_NUMBER: _ClassVar[int]
    IPBLOCK_FIELD_NUMBER: _ClassVar[int]
    podSelector: LabelSelector
    namespaceSelector: LabelSelector
    ipBlock: IPBlock
    def __init__(self, podSelector: _Optional[_Union[LabelSelector, _Mapping]] = ..., namespaceSelector: _Optional[_Union[LabelSelector, _Mapping]] = ..., ipBlock: _Optional[_Union[IPBlock, _Mapping]] = ...) -> None: ...

class IPBlock(_message.Message):
    __slots__ = ("cidr",)
    CIDR_FIELD_NUMBER: _ClassVar[int]
    EXCEPT_FIELD_NUMBER: _ClassVar[int]
    cidr: str
    def __init__(self, cidr: _Optional[str] = ..., **kwargs) -> None: ...

class IntOrString(_message.Message):
    __slots__ = ("int_val", "str_val")
    INT_VAL_FIELD_NUMBER: _ClassVar[int]
    STR_VAL_FIELD_NUMBER: _ClassVar[int]
    int_val: int
    str_val: str
    def __init__(self, int_val: _Optional[int] = ..., str_val: _Optional[str] = ...) -> None: ...

class PersistentVolumeClaimSpec(_message.Message):
    __slots__ = ("access_modes", "selector", "resources", "volume_name", "storage_class_name")
    ACCESS_MODES_FIELD_NUMBER: _ClassVar[int]
    SELECTOR_FIELD_NUMBER: _ClassVar[int]
    RESOURCES_FIELD_NUMBER: _ClassVar[int]
    VOLUME_NAME_FIELD_NUMBER: _ClassVar[int]
    STORAGE_CLASS_NAME_FIELD_NUMBER: _ClassVar[int]
    access_modes: _containers.RepeatedScalarFieldContainer[PVCAccessMode]
    selector: LabelSelector
    resources: VolumeResourceRequirements
    volume_name: str
    storage_class_name: str
    def __init__(self, access_modes: _Optional[_Iterable[_Union[PVCAccessMode, str]]] = ..., selector: _Optional[_Union[LabelSelector, _Mapping]] = ..., resources: _Optional[_Union[VolumeResourceRequirements, _Mapping]] = ..., volume_name: _Optional[str] = ..., storage_class_name: _Optional[str] = ...) -> None: ...

class VolumeResourceRequirements(_message.Message):
    __slots__ = ("storage_request",)
    STORAGE_REQUEST_FIELD_NUMBER: _ClassVar[int]
    storage_request: str
    def __init__(self, storage_request: _Optional[str] = ...) -> None: ...

class VolumeMount(_message.Message):
    __slots__ = ("name", "read_only", "mount_path", "sub_path", "mount_propagation", "sub_path_expr")
    NAME_FIELD_NUMBER: _ClassVar[int]
    READ_ONLY_FIELD_NUMBER: _ClassVar[int]
    MOUNT_PATH_FIELD_NUMBER: _ClassVar[int]
    SUB_PATH_FIELD_NUMBER: _ClassVar[int]
    MOUNT_PROPAGATION_FIELD_NUMBER: _ClassVar[int]
    SUB_PATH_EXPR_FIELD_NUMBER: _ClassVar[int]
    name: str
    read_only: bool
    mount_path: str
    sub_path: str
    mount_propagation: str
    sub_path_expr: str
    def __init__(self, name: _Optional[str] = ..., read_only: bool = ..., mount_path: _Optional[str] = ..., sub_path: _Optional[str] = ..., mount_propagation: _Optional[str] = ..., sub_path_expr: _Optional[str] = ...) -> None: ...

class Volume(_message.Message):
    __slots__ = ("name", "persistentVolumeClaim", "emptyDir", "secret")
    NAME_FIELD_NUMBER: _ClassVar[int]
    PERSISTENTVOLUMECLAIM_FIELD_NUMBER: _ClassVar[int]
    EMPTYDIR_FIELD_NUMBER: _ClassVar[int]
    SECRET_FIELD_NUMBER: _ClassVar[int]
    name: str
    persistentVolumeClaim: PersistentVolumeClaimVolumeSource
    emptyDir: EmptyDirVolumeSource
    secret: SecretVolumeSource
    def __init__(self, name: _Optional[str] = ..., persistentVolumeClaim: _Optional[_Union[PersistentVolumeClaimVolumeSource, _Mapping]] = ..., emptyDir: _Optional[_Union[EmptyDirVolumeSource, _Mapping]] = ..., secret: _Optional[_Union[SecretVolumeSource, _Mapping]] = ...) -> None: ...

class PersistentVolumeClaimVolumeSource(_message.Message):
    __slots__ = ("claim_name", "read_only")
    CLAIM_NAME_FIELD_NUMBER: _ClassVar[int]
    READ_ONLY_FIELD_NUMBER: _ClassVar[int]
    claim_name: str
    read_only: bool
    def __init__(self, claim_name: _Optional[str] = ..., read_only: bool = ...) -> None: ...

class EmptyDirVolumeSource(_message.Message):
    __slots__ = ("medium", "size_limit")
    MEDIUM_FIELD_NUMBER: _ClassVar[int]
    SIZE_LIMIT_FIELD_NUMBER: _ClassVar[int]
    medium: str
    size_limit: str
    def __init__(self, medium: _Optional[str] = ..., size_limit: _Optional[str] = ...) -> None: ...

class SecretVolumeSource(_message.Message):
    __slots__ = ("secret_name", "items", "default_mode", "is_optional")
    SECRET_NAME_FIELD_NUMBER: _ClassVar[int]
    ITEMS_FIELD_NUMBER: _ClassVar[int]
    DEFAULT_MODE_FIELD_NUMBER: _ClassVar[int]
    IS_OPTIONAL_FIELD_NUMBER: _ClassVar[int]
    secret_name: str
    items: _containers.RepeatedCompositeFieldContainer[KeyToPath]
    default_mode: int
    is_optional: bool
    def __init__(self, secret_name: _Optional[str] = ..., items: _Optional[_Iterable[_Union[KeyToPath, _Mapping]]] = ..., default_mode: _Optional[int] = ..., is_optional: bool = ...) -> None: ...

class KeyToPath(_message.Message):
    __slots__ = ("key", "path", "mode")
    KEY_FIELD_NUMBER: _ClassVar[int]
    PATH_FIELD_NUMBER: _ClassVar[int]
    MODE_FIELD_NUMBER: _ClassVar[int]
    key: str
    path: str
    mode: int
    def __init__(self, key: _Optional[str] = ..., path: _Optional[str] = ..., mode: _Optional[int] = ...) -> None: ...

class EnvVar(_message.Message):
    __slots__ = ("name", "value")
    NAME_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    name: str
    value: str
    def __init__(self, name: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
