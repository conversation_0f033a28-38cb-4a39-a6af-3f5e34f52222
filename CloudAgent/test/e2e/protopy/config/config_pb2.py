# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/config.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'config/config.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from config import aws_pb2 as config_dot_aws__pb2
from config import gcp_pb2 as config_dot_gcp__pb2
from config import azr_pb2 as config_dot_azr__pb2
from config import k8s_pb2 as config_dot_k8s__pb2
from config import tls_pb2 as config_dot_tls__pb2
from config import local_pb2 as config_dot_local__pb2
from config import telemetry_pb2 as config_dot_telemetry__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13\x63onfig/config.proto\x12\x06\x63onfig\x1a\x10\x63onfig/aws.proto\x1a\x10\x63onfig/gcp.proto\x1a\x10\x63onfig/azr.proto\x1a\x10\x63onfig/k8s.proto\x1a\x10\x63onfig/tls.proto\x1a\x12\x63onfig/local.proto\x1a\x16\x63onfig/telemetry.proto\"\xd1\x02\n\x06\x43onfig\x12&\n\nk8s_config\x18\x01 \x01(\x0b\x32\x12.config.k8s.Config\x12(\n\naws_config\x18\x02 \x01(\x0b\x32\x12.config.aws.ConfigH\x00\x12(\n\ngcp_config\x18\x03 \x01(\x0b\x32\x12.config.gcp.ConfigH\x00\x12(\n\nazr_config\x18\x07 \x01(\x0b\x32\x12.config.azr.ConfigH\x00\x12,\n\x0clocal_config\x18\x05 \x01(\x0b\x32\x14.config.local.ConfigH\x00\x12&\n\ntls_config\x18\x04 \x01(\x0b\x32\x12.config.tls.Config\x12\x32\n\x10telemetry_config\x18\x06 \x01(\x0b\x32\x18.config.telemetry.ConfigB\x17\n\x15\x63loud_provider_configB3Z1github.com/risingwavelabs/cloudagent/pbgen/configb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.config_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z1github.com/risingwavelabs/cloudagent/pbgen/config'
  _globals['_CONFIG']._serialized_start=166
  _globals['_CONFIG']._serialized_end=503
# @@protoc_insertion_point(module_scope)
