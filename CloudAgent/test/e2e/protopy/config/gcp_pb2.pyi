from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Config(_message.Message):
    __slots__ = ("project_id", "static_creds", "gke_web_identity", "region", "tenant_vpc")
    PROJECT_ID_FIELD_NUMBER: _ClassVar[int]
    STATIC_CREDS_FIELD_NUMBER: _ClassVar[int]
    GKE_WEB_IDENTITY_FIELD_NUMBER: _ClassVar[int]
    REGION_FIELD_NUMBER: _ClassVar[int]
    TENANT_VPC_FIELD_NUMBER: _ClassVar[int]
    project_id: str
    static_creds: StaticCredAuth
    gke_web_identity: GKEWebIdendity
    region: str
    tenant_vpc: str
    def __init__(self, project_id: _Optional[str] = ..., static_creds: _Optional[_Union[StaticCredAuth, _Mapping]] = ..., gke_web_identity: _Optional[_Union[GKEWebIdendity, _Mapping]] = ..., region: _Optional[str] = ..., tenant_vpc: _Optional[str] = ...) -> None: ...

class StaticCredAuth(_message.Message):
    __slots__ = ("google_application_credentials",)
    GOOGLE_APPLICATION_CREDENTIALS_FIELD_NUMBER: _ClassVar[int]
    google_application_credentials: str
    def __init__(self, google_application_credentials: _Optional[str] = ...) -> None: ...

class GKEWebIdendity(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
