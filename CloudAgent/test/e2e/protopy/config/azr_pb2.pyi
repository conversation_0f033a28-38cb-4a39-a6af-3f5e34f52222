from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Config(_message.Message):
    __slots__ = ("subscription_id", "location", "resource_group", "oidc_issuer", "aks_workload_identity", "static_creds", "storage_account_name")
    SUBSCRIPTION_ID_FIELD_NUMBER: _ClassVar[int]
    LOCATION_FIELD_NUMBER: _ClassVar[int]
    RESOURCE_GROUP_FIELD_NUMBER: _ClassVar[int]
    OIDC_ISSUER_FIELD_NUMBER: _ClassVar[int]
    AKS_WORKLOAD_IDENTITY_FIELD_NUMBER: _ClassVar[int]
    STATIC_CREDS_FIELD_NUMBER: _ClassVar[int]
    STORAGE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    subscription_id: str
    location: str
    resource_group: str
    oidc_issuer: str
    aks_workload_identity: AKSWorkloadIdentity
    static_creds: StaticCredAuth
    storage_account_name: str
    def __init__(self, subscription_id: _Optional[str] = ..., location: _Optional[str] = ..., resource_group: _Optional[str] = ..., oidc_issuer: _Optional[str] = ..., aks_workload_identity: _Optional[_Union[AKSWorkloadIdentity, _Mapping]] = ..., static_creds: _Optional[_Union[StaticCredAuth, _Mapping]] = ..., storage_account_name: _Optional[str] = ...) -> None: ...

class StaticCredAuth(_message.Message):
    __slots__ = ("client_secret", "client_id", "tenant_id")
    CLIENT_SECRET_FIELD_NUMBER: _ClassVar[int]
    CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    TENANT_ID_FIELD_NUMBER: _ClassVar[int]
    client_secret: str
    client_id: str
    tenant_id: str
    def __init__(self, client_secret: _Optional[str] = ..., client_id: _Optional[str] = ..., tenant_id: _Optional[str] = ...) -> None: ...

class AKSWorkloadIdentity(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
