from config import aws_pb2 as _aws_pb2
from config import gcp_pb2 as _gcp_pb2
from config import azr_pb2 as _azr_pb2
from config import k8s_pb2 as _k8s_pb2
from config import tls_pb2 as _tls_pb2
from config import local_pb2 as _local_pb2
from config import telemetry_pb2 as _telemetry_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Config(_message.Message):
    __slots__ = ("k8s_config", "aws_config", "gcp_config", "azr_config", "local_config", "tls_config", "telemetry_config")
    K8S_CONFIG_FIELD_NUMBER: _ClassVar[int]
    AWS_CONFIG_FIELD_NUMBER: _ClassVar[int]
    GCP_CONFIG_FIELD_NUMBER: _ClassVar[int]
    AZR_CONFIG_FIELD_NUMBER: _ClassVar[int]
    LOCAL_CONFIG_FIELD_NUMBER: _ClassVar[int]
    TLS_CONFIG_FIELD_NUMBER: _ClassVar[int]
    TELEMETRY_CONFIG_FIELD_NUMBER: _ClassVar[int]
    k8s_config: _k8s_pb2.Config
    aws_config: _aws_pb2.Config
    gcp_config: _gcp_pb2.Config
    azr_config: _azr_pb2.Config
    local_config: _local_pb2.Config
    tls_config: _tls_pb2.Config
    telemetry_config: _telemetry_pb2.Config
    def __init__(self, k8s_config: _Optional[_Union[_k8s_pb2.Config, _Mapping]] = ..., aws_config: _Optional[_Union[_aws_pb2.Config, _Mapping]] = ..., gcp_config: _Optional[_Union[_gcp_pb2.Config, _Mapping]] = ..., azr_config: _Optional[_Union[_azr_pb2.Config, _Mapping]] = ..., local_config: _Optional[_Union[_local_pb2.Config, _Mapping]] = ..., tls_config: _Optional[_Union[_tls_pb2.Config, _Mapping]] = ..., telemetry_config: _Optional[_Union[_telemetry_pb2.Config, _Mapping]] = ...) -> None: ...
