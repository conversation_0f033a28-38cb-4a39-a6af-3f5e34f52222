from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PullPolicy(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[PullPolicy]
    PULL_ALWAYS: _ClassVar[PullPolicy]
    PULL_IF_NOT_PRESENT: _ClassVar[PullPolicy]
UNKNOWN: PullPolicy
PULL_ALWAYS: PullPolicy
PULL_IF_NOT_PRESENT: PullPolicy

class Config(_message.Message):
    __slots__ = ("cluster_id", "endpoint", "ca_certificate_base64", "static_token_auth", "in_cluster_auth", "task_config", "allow_helm_charts")
    CLUSTER_ID_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    CA_CERTIFICATE_BASE64_FIELD_NUMBER: _ClassVar[int]
    STATIC_TOKEN_AUTH_FIELD_NUMBER: _ClassVar[int]
    IN_CLUSTER_AUTH_FIELD_NUMBER: _ClassVar[int]
    TASK_CONFIG_FIELD_NUMBER: _ClassVar[int]
    ALLOW_HELM_CHARTS_FIELD_NUMBER: _ClassVar[int]
    cluster_id: str
    endpoint: str
    ca_certificate_base64: str
    static_token_auth: StaticTokenAuth
    in_cluster_auth: InClusterAuth
    task_config: TaskConfig
    allow_helm_charts: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, cluster_id: _Optional[str] = ..., endpoint: _Optional[str] = ..., ca_certificate_base64: _Optional[str] = ..., static_token_auth: _Optional[_Union[StaticTokenAuth, _Mapping]] = ..., in_cluster_auth: _Optional[_Union[InClusterAuth, _Mapping]] = ..., task_config: _Optional[_Union[TaskConfig, _Mapping]] = ..., allow_helm_charts: _Optional[_Iterable[str]] = ...) -> None: ...

class StaticTokenAuth(_message.Message):
    __slots__ = ("master_url", "token")
    MASTER_URL_FIELD_NUMBER: _ClassVar[int]
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    master_url: str
    token: str
    def __init__(self, master_url: _Optional[str] = ..., token: _Optional[str] = ...) -> None: ...

class InClusterAuth(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class TaskConfig(_message.Message):
    __slots__ = ("image", "service_account", "namespace", "pull_policy", "tolerations", "affinity", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    IMAGE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    PULL_POLICY_FIELD_NUMBER: _ClassVar[int]
    TOLERATIONS_FIELD_NUMBER: _ClassVar[int]
    AFFINITY_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    image: str
    service_account: str
    namespace: str
    pull_policy: PullPolicy
    tolerations: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Toleration]
    affinity: _k8s_pb2.Affinity
    labels: _containers.ScalarMap[str, str]
    def __init__(self, image: _Optional[str] = ..., service_account: _Optional[str] = ..., namespace: _Optional[str] = ..., pull_policy: _Optional[_Union[PullPolicy, str]] = ..., tolerations: _Optional[_Iterable[_Union[_k8s_pb2.Toleration, _Mapping]]] = ..., affinity: _Optional[_Union[_k8s_pb2.Affinity, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...
