from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Config(_message.Message):
    __slots__ = ("amp_config", "gmp_config", "local_prometheus_config", "azmp_config")
    AMP_CONFIG_FIELD_NUMBER: _ClassVar[int]
    GMP_CONFIG_FIELD_NUMBER: _ClassVar[int]
    LOCAL_PROMETHEUS_CONFIG_FIELD_NUMBER: _ClassVar[int]
    AZMP_CONFIG_FIELD_NUMBER: _ClassVar[int]
    amp_config: AWSManagedPrometheusConfig
    gmp_config: GoogleManagedPrometheusConfig
    local_prometheus_config: LocalManagedPrometheusConfig
    azmp_config: AzureManagedPrometheusConfig
    def __init__(self, amp_config: _Optional[_Union[AWSManagedPrometheusConfig, _Mapping]] = ..., gmp_config: _Optional[_Union[GoogleManagedPrometheusConfig, _Mapping]] = ..., local_prometheus_config: _Optional[_Union[LocalManagedPrometheusConfig, _Mapping]] = ..., azmp_config: _Optional[_Union[AzureManagedPrometheusConfig, _Mapping]] = ...) -> None: ...

class AWSManagedPrometheusConfig(_message.Message):
    __slots__ = ("workspace_id",)
    WORKSPACE_ID_FIELD_NUMBER: _ClassVar[int]
    workspace_id: str
    def __init__(self, workspace_id: _Optional[str] = ...) -> None: ...

class GoogleManagedPrometheusConfig(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class AzureManagedPrometheusConfig(_message.Message):
    __slots__ = ("query_endpoint",)
    QUERY_ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    query_endpoint: str
    def __init__(self, query_endpoint: _Optional[str] = ...) -> None: ...

class LocalManagedPrometheusConfig(_message.Message):
    __slots__ = ("url",)
    URL_FIELD_NUMBER: _ClassVar[int]
    url: str
    def __init__(self, url: _Optional[str] = ...) -> None: ...
