# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/azr.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'config/azr.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63onfig/azr.proto\x12\nconfig.azr\"\x80\x02\n\x06\x43onfig\x12\x17\n\x0fsubscription_id\x18\x01 \x01(\t\x12\x10\n\x08location\x18\x02 \x01(\t\x12\x16\n\x0eresource_group\x18\x03 \x01(\t\x12\x13\n\x0boidc_issuer\x18\x06 \x01(\t\x12@\n\x15\x61ks_workload_identity\x18\x04 \x01(\x0b\x32\x1f.config.azr.AKSWorkloadIdentityH\x00\x12\x32\n\x0cstatic_creds\x18\x05 \x01(\x0b\x32\x1a.config.azr.StaticCredAuthH\x00\x12 \n\x14storage_account_name\x18\x07 \x01(\tB\x02\x18\x01\x42\x06\n\x04\x61uth\"M\n\x0eStaticCredAuth\x12\x15\n\rclient_secret\x18\x01 \x01(\t\x12\x11\n\tclient_id\x18\x02 \x01(\t\x12\x11\n\ttenant_id\x18\x03 \x01(\t\"\x15\n\x13\x41KSWorkloadIdentityB7Z5github.com/risingwavelabs/cloudagent/pbgen/config/azrb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.azr_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/config/azr'
  _globals['_CONFIG'].fields_by_name['storage_account_name']._loaded_options = None
  _globals['_CONFIG'].fields_by_name['storage_account_name']._serialized_options = b'\030\001'
  _globals['_CONFIG']._serialized_start=33
  _globals['_CONFIG']._serialized_end=289
  _globals['_STATICCREDAUTH']._serialized_start=291
  _globals['_STATICCREDAUTH']._serialized_end=368
  _globals['_AKSWORKLOADIDENTITY']._serialized_start=370
  _globals['_AKSWORKLOADIDENTITY']._serialized_end=391
# @@protoc_insertion_point(module_scope)
