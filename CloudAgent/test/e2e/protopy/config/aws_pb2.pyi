from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Config(_message.Message):
    __slots__ = ("account_id", "region", "oidc_provider", "static_creds", "eks_web_identity", "vpc_id", "default_tags")
    ACCOUNT_ID_FIELD_NUMBER: _ClassVar[int]
    REGION_FIELD_NUMBER: _ClassVar[int]
    OIDC_PROVIDER_FIELD_NUMBER: _ClassVar[int]
    STATIC_CREDS_FIELD_NUMBER: _ClassVar[int]
    EKS_WEB_IDENTITY_FIELD_NUMBER: _ClassVar[int]
    VPC_ID_FIELD_NUMBER: _ClassVar[int]
    DEFAULT_TAGS_FIELD_NUMBER: _ClassVar[int]
    account_id: str
    region: str
    oidc_provider: str
    static_creds: StaticCredAuth
    eks_web_identity: EKSWebIdendity
    vpc_id: str
    default_tags: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, account_id: _Optional[str] = ..., region: _Optional[str] = ..., oidc_provider: _Optional[str] = ..., static_creds: _Optional[_Union[StaticCredAuth, _Mapping]] = ..., eks_web_identity: _Optional[_Union[EKSWebIdendity, _Mapping]] = ..., vpc_id: _Optional[str] = ..., default_tags: _Optional[_Iterable[str]] = ...) -> None: ...

class StaticCredAuth(_message.Message):
    __slots__ = ("access_key_id", "secret_access_key")
    ACCESS_KEY_ID_FIELD_NUMBER: _ClassVar[int]
    SECRET_ACCESS_KEY_FIELD_NUMBER: _ClassVar[int]
    access_key_id: str
    secret_access_key: str
    def __init__(self, access_key_id: _Optional[str] = ..., secret_access_key: _Optional[str] = ...) -> None: ...

class EKSWebIdendity(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...
