# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/telemetry.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'config/telemetry.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x16\x63onfig/telemetry.proto\x12\x10\x63onfig.telemetry\"\xbf\x02\n\x06\x43onfig\x12\x42\n\namp_config\x18\x01 \x01(\x0b\x32,.config.telemetry.AWSManagedPrometheusConfigH\x00\x12\x45\n\ngmp_config\x18\x02 \x01(\x0b\x32/.config.telemetry.GoogleManagedPrometheusConfigH\x00\x12Q\n\x17local_prometheus_config\x18\x03 \x01(\x0b\x32..config.telemetry.LocalManagedPrometheusConfigH\x00\x12\x45\n\x0b\x61zmp_config\x18\x04 \x01(\x0b\x32..config.telemetry.AzureManagedPrometheusConfigH\x00\x42\x10\n\x0emetrics_config\"2\n\x1a\x41WSManagedPrometheusConfig\x12\x14\n\x0cworkspace_id\x18\x01 \x01(\t\"\x1f\n\x1dGoogleManagedPrometheusConfig\"6\n\x1c\x41zureManagedPrometheusConfig\x12\x16\n\x0equery_endpoint\x18\x01 \x01(\t\"+\n\x1cLocalManagedPrometheusConfig\x12\x0b\n\x03url\x18\x01 \x01(\tB=Z;github.com/risingwavelabs/cloudagent/pbgen/config/telemetryb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.telemetry_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z;github.com/risingwavelabs/cloudagent/pbgen/config/telemetry'
  _globals['_CONFIG']._serialized_start=45
  _globals['_CONFIG']._serialized_end=364
  _globals['_AWSMANAGEDPROMETHEUSCONFIG']._serialized_start=366
  _globals['_AWSMANAGEDPROMETHEUSCONFIG']._serialized_end=416
  _globals['_GOOGLEMANAGEDPROMETHEUSCONFIG']._serialized_start=418
  _globals['_GOOGLEMANAGEDPROMETHEUSCONFIG']._serialized_end=449
  _globals['_AZUREMANAGEDPROMETHEUSCONFIG']._serialized_start=451
  _globals['_AZUREMANAGEDPROMETHEUSCONFIG']._serialized_end=505
  _globals['_LOCALMANAGEDPROMETHEUSCONFIG']._serialized_start=507
  _globals['_LOCALMANAGEDPROMETHEUSCONFIG']._serialized_end=550
# @@protoc_insertion_point(module_scope)
