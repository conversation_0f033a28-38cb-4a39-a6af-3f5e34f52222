# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/k8s.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'config/k8s.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63onfig/k8s.proto\x12\nconfig.k8s\x1a\x10\x63ommon/k8s.proto\"\x8d\x02\n\x06\x43onfig\x12\x12\n\ncluster_id\x18\x01 \x01(\t\x12\x10\n\x08\x65ndpoint\x18\x06 \x01(\t\x12\x1d\n\x15\x63\x61_certificate_base64\x18\x07 \x01(\t\x12\x38\n\x11static_token_auth\x18\x02 \x01(\x0b\x32\x1b.config.k8s.StaticTokenAuthH\x00\x12\x34\n\x0fin_cluster_auth\x18\x03 \x01(\x0b\x32\x19.config.k8s.InClusterAuthH\x00\x12+\n\x0btask_config\x18\x04 \x01(\x0b\x32\x16.config.k8s.TaskConfig\x12\x19\n\x11\x61llow_helm_charts\x18\x05 \x03(\tB\x06\n\x04\x61uth\"4\n\x0fStaticTokenAuth\x12\x12\n\nmaster_url\x18\x01 \x01(\t\x12\r\n\x05token\x18\x02 \x01(\t\"\x0f\n\rInClusterAuth\"\xac\x02\n\nTaskConfig\x12\r\n\x05image\x18\x01 \x01(\t\x12\x17\n\x0fservice_account\x18\x02 \x01(\t\x12\x11\n\tnamespace\x18\x03 \x01(\t\x12+\n\x0bpull_policy\x18\x04 \x01(\x0e\x32\x16.config.k8s.PullPolicy\x12+\n\x0btolerations\x18\x05 \x03(\x0b\x32\x16.common.k8s.Toleration\x12&\n\x08\x61\x66\x66inity\x18\x06 \x01(\x0b\x32\x14.common.k8s.Affinity\x12\x32\n\x06labels\x18\x07 \x03(\x0b\x32\".config.k8s.TaskConfig.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01*C\n\nPullPolicy\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x0f\n\x0bPULL_ALWAYS\x10\x01\x12\x17\n\x13PULL_IF_NOT_PRESENT\x10\x02\x42\x37Z5github.com/risingwavelabs/cloudagent/pbgen/config/k8sb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.k8s_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/config/k8s'
  _globals['_TASKCONFIG_LABELSENTRY']._loaded_options = None
  _globals['_TASKCONFIG_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_PULLPOLICY']._serialized_start=696
  _globals['_PULLPOLICY']._serialized_end=763
  _globals['_CONFIG']._serialized_start=51
  _globals['_CONFIG']._serialized_end=320
  _globals['_STATICTOKENAUTH']._serialized_start=322
  _globals['_STATICTOKENAUTH']._serialized_end=374
  _globals['_INCLUSTERAUTH']._serialized_start=376
  _globals['_INCLUSTERAUTH']._serialized_end=391
  _globals['_TASKCONFIG']._serialized_start=394
  _globals['_TASKCONFIG']._serialized_end=694
  _globals['_TASKCONFIG_LABELSENTRY']._serialized_start=649
  _globals['_TASKCONFIG_LABELSENTRY']._serialized_end=694
# @@protoc_insertion_point(module_scope)
