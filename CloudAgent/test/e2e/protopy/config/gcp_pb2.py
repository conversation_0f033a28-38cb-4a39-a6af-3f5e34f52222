# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/gcp.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'config/gcp.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63onfig/gcp.proto\x12\nconfig.gcp\"\xb4\x01\n\x06\x43onfig\x12\x12\n\nproject_id\x18\x01 \x01(\t\x12\x32\n\x0cstatic_creds\x18\x02 \x01(\x0b\x32\x1a.config.gcp.StaticCredAuthH\x00\x12\x36\n\x10gke_web_identity\x18\x03 \x01(\x0b\x32\x1a.config.gcp.GKEWebIdendityH\x00\x12\x0e\n\x06region\x18\x04 \x01(\t\x12\x12\n\ntenant_vpc\x18\x05 \x01(\tB\x06\n\x04\x61uth\"8\n\x0eStaticCredAuth\x12&\n\x1egoogle_application_credentials\x18\x01 \x01(\t\"\x10\n\x0eGKEWebIdendityB7Z5github.com/risingwavelabs/cloudagent/pbgen/config/gcpb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.gcp_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/config/gcp'
  _globals['_CONFIG']._serialized_start=33
  _globals['_CONFIG']._serialized_end=213
  _globals['_STATICCREDAUTH']._serialized_start=215
  _globals['_STATICCREDAUTH']._serialized_end=271
  _globals['_GKEWEBIDENDITY']._serialized_start=273
  _globals['_GKEWEBIDENDITY']._serialized_end=289
# @@protoc_insertion_point(module_scope)
