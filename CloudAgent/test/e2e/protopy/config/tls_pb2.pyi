from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class Config(_message.Message):
    __slots__ = ("key_path", "cert_path", "client_ca_path")
    KEY_PATH_FIELD_NUMBER: _ClassVar[int]
    CERT_PATH_FIELD_NUMBER: _ClassVar[int]
    CLIENT_CA_PATH_FIELD_NUMBER: _ClassVar[int]
    key_path: str
    cert_path: str
    client_ca_path: str
    def __init__(self, key_path: _Optional[str] = ..., cert_path: _Optional[str] = ..., client_ca_path: _Optional[str] = ...) -> None: ...
