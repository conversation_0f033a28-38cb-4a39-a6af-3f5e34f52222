# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: config/aws.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'config/aws.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x10\x63onfig/aws.proto\x12\nconfig.aws\"\xdd\x01\n\x06\x43onfig\x12\x12\n\naccount_id\x18\x01 \x01(\t\x12\x0e\n\x06region\x18\x02 \x01(\t\x12\x15\n\roidc_provider\x18\x03 \x01(\t\x12\x32\n\x0cstatic_creds\x18\x04 \x01(\x0b\x32\x1a.config.aws.StaticCredAuthH\x00\x12\x36\n\x10\x65ks_web_identity\x18\x05 \x01(\x0b\x32\x1a.config.aws.EKSWebIdendityH\x00\x12\x0e\n\x06vpc_id\x18\x06 \x01(\t\x12\x14\n\x0c\x64\x65\x66\x61ult_tags\x18\x07 \x03(\tB\x06\n\x04\x61uth\"B\n\x0eStaticCredAuth\x12\x15\n\raccess_key_id\x18\x01 \x01(\t\x12\x19\n\x11secret_access_key\x18\x02 \x01(\t\"\x10\n\x0e\x45KSWebIdendityB7Z5github.com/risingwavelabs/cloudagent/pbgen/config/awsb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'config.aws_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z5github.com/risingwavelabs/cloudagent/pbgen/config/aws'
  _globals['_CONFIG']._serialized_start=33
  _globals['_CONFIG']._serialized_end=254
  _globals['_STATICCREDAUTH']._serialized_start=256
  _globals['_STATICCREDAUTH']._serialized_end=322
  _globals['_EKSWEBIDENDITY']._serialized_start=324
  _globals['_EKSWEBIDENDITY']._serialized_end=340
# @@protoc_insertion_point(module_scope)
