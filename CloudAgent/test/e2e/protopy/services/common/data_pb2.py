# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/common/data.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/common/data.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import resource_pb2 as common_dot_resource__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1aservices/common/data.proto\x12\rservices.data\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/resource.proto\"\xb5\x01\n&CreateDataDirectoryDeletionTaskRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x16\n\x0e\x64irectory_name\x18\x02 \x01(\t\x12\x13\n\x0b\x62ucket_name\x18\x03 \x01(\t\x12\x12\n\x06region\x18\x04 \x01(\tB\x02\x18\x01\x12\x1c\n\x14storage_account_name\x18\x05 \x01(\t\"[\n\'CreateDataDirectoryDeletionTaskResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xc6\x02\n#CreateDataDirectoryCloneTaskRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x1d\n\x15source_directory_name\x18\x02 \x01(\t\x12\x1a\n\x12source_bucket_name\x18\x03 \x01(\t\x12\"\n\x1a\x64\x65stination_directory_name\x18\x04 \x01(\t\x12\x1f\n\x17\x64\x65stination_bucket_name\x18\x05 \x01(\t\x12#\n\x1bsource_storage_account_name\x18\x06 \x01(\t\x12(\n destination_storage_account_name\x18\x07 \x01(\t\x12\x0e\n\x06\x63ursor\x18\x08 \x01(\t\x12\x12\n\nclone_size\x18\t \x01(\x05\"X\n$CreateDataDirectoryCloneTaskResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xb4\x01\n&CreateSimpleDataReplicationTaskRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x15\n\rsource_bucket\x18\x02 \x01(\t\x12\x18\n\x10source_directory\x18\x03 \x01(\t\x12\x13\n\x0bsink_bucket\x18\x04 \x01(\t\x12\x16\n\x0esink_directory\x18\x05 \x01(\t\"[\n\'CreateSimpleDataReplicationTaskResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"V\n\x12GetManifestRequest\x12\x0e\n\x06\x62ucket\x18\x01 \x01(\t\x12\x12\n\nbackup_dir\x18\x02 \x01(\t\x12\x1c\n\x14storage_account_name\x18\x03 \x01(\t\",\n\x13GetManifestResponse\x12\x15\n\rmanifest_json\x18\x01 \x01(\tB:Z8github.com/risingwavelabs/cloudagent/pbgen/services/datab\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.common.data_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z8github.com/risingwavelabs/cloudagent/pbgen/services/data'
  _globals['_CREATEDATADIRECTORYDELETIONTASKREQUEST'].fields_by_name['region']._loaded_options = None
  _globals['_CREATEDATADIRECTORYDELETIONTASKREQUEST'].fields_by_name['region']._serialized_options = b'\030\001'
  _globals['_CREATEDATADIRECTORYDELETIONTASKREQUEST']._serialized_start=92
  _globals['_CREATEDATADIRECTORYDELETIONTASKREQUEST']._serialized_end=273
  _globals['_CREATEDATADIRECTORYDELETIONTASKRESPONSE']._serialized_start=275
  _globals['_CREATEDATADIRECTORYDELETIONTASKRESPONSE']._serialized_end=366
  _globals['_CREATEDATADIRECTORYCLONETASKREQUEST']._serialized_start=369
  _globals['_CREATEDATADIRECTORYCLONETASKREQUEST']._serialized_end=695
  _globals['_CREATEDATADIRECTORYCLONETASKRESPONSE']._serialized_start=697
  _globals['_CREATEDATADIRECTORYCLONETASKRESPONSE']._serialized_end=785
  _globals['_CREATESIMPLEDATAREPLICATIONTASKREQUEST']._serialized_start=788
  _globals['_CREATESIMPLEDATAREPLICATIONTASKREQUEST']._serialized_end=968
  _globals['_CREATESIMPLEDATAREPLICATIONTASKRESPONSE']._serialized_start=970
  _globals['_CREATESIMPLEDATAREPLICATIONTASKRESPONSE']._serialized_end=1061
  _globals['_GETMANIFESTREQUEST']._serialized_start=1063
  _globals['_GETMANIFESTREQUEST']._serialized_end=1149
  _globals['_GETMANIFESTRESPONSE']._serialized_start=1151
  _globals['_GETMANIFESTRESPONSE']._serialized_end=1195
# @@protoc_insertion_point(module_scope)
