from common import creation_pb2 as _creation_pb2
from common import resource_pb2 as _resource_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateDataDirectoryDeletionTaskRequest(_message.Message):
    __slots__ = ("resource_meta", "directory_name", "bucket_name", "region", "storage_account_name")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    BUCKET_NAME_FIELD_NUMBER: _ClassVar[int]
    REGION_FIELD_NUMBER: _ClassVar[int]
    STORAGE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    directory_name: str
    bucket_name: str
    region: str
    storage_account_name: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., directory_name: _Optional[str] = ..., bucket_name: _Optional[str] = ..., region: _Optional[str] = ..., storage_account_name: _Optional[str] = ...) -> None: ...

class CreateDataDirectoryDeletionTaskResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateDataDirectoryCloneTaskRequest(_message.Message):
    __slots__ = ("resource_meta", "source_directory_name", "source_bucket_name", "destination_directory_name", "destination_bucket_name", "source_storage_account_name", "destination_storage_account_name", "cursor", "clone_size")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SOURCE_DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    SOURCE_BUCKET_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_BUCKET_NAME_FIELD_NUMBER: _ClassVar[int]
    SOURCE_STORAGE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_STORAGE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    CURSOR_FIELD_NUMBER: _ClassVar[int]
    CLONE_SIZE_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    source_directory_name: str
    source_bucket_name: str
    destination_directory_name: str
    destination_bucket_name: str
    source_storage_account_name: str
    destination_storage_account_name: str
    cursor: str
    clone_size: int
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., source_directory_name: _Optional[str] = ..., source_bucket_name: _Optional[str] = ..., destination_directory_name: _Optional[str] = ..., destination_bucket_name: _Optional[str] = ..., source_storage_account_name: _Optional[str] = ..., destination_storage_account_name: _Optional[str] = ..., cursor: _Optional[str] = ..., clone_size: _Optional[int] = ...) -> None: ...

class CreateDataDirectoryCloneTaskResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateSimpleDataReplicationTaskRequest(_message.Message):
    __slots__ = ("resource_meta", "source_bucket", "source_directory", "sink_bucket", "sink_directory")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SOURCE_BUCKET_FIELD_NUMBER: _ClassVar[int]
    SOURCE_DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    SINK_BUCKET_FIELD_NUMBER: _ClassVar[int]
    SINK_DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    source_bucket: str
    source_directory: str
    sink_bucket: str
    sink_directory: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., source_bucket: _Optional[str] = ..., source_directory: _Optional[str] = ..., sink_bucket: _Optional[str] = ..., sink_directory: _Optional[str] = ...) -> None: ...

class CreateSimpleDataReplicationTaskResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetManifestRequest(_message.Message):
    __slots__ = ("bucket", "backup_dir", "storage_account_name")
    BUCKET_FIELD_NUMBER: _ClassVar[int]
    BACKUP_DIR_FIELD_NUMBER: _ClassVar[int]
    STORAGE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    bucket: str
    backup_dir: str
    storage_account_name: str
    def __init__(self, bucket: _Optional[str] = ..., backup_dir: _Optional[str] = ..., storage_account_name: _Optional[str] = ...) -> None: ...

class GetManifestResponse(_message.Message):
    __slots__ = ("manifest_json",)
    MANIFEST_JSON_FIELD_NUMBER: _ClassVar[int]
    manifest_json: str
    def __init__(self, manifest_json: _Optional[str] = ...) -> None: ...
