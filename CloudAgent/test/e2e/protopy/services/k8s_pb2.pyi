from common import creation_pb2 as _creation_pb2
from common import deletion_pb2 as _deletion_pb2
from common import update_pb2 as _update_pb2
from common import k8s_pb2 as _k8s_pb2
from common import resource_pb2 as _resource_pb2
from common import risingwave_pb2 as _risingwave_pb2
from common import prometheus_pb2 as _prometheus_pb2
from common import gmp_pb2 as _gmp_pb2
from common import postgresql_pb2 as _postgresql_pb2
from google.protobuf import duration_pb2 as _duration_pb2
from google.protobuf import timestamp_pb2 as _timestamp_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateNamespaceRequest(_message.Message):
    __slots__ = ("resource_meta", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateNamespaceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class LabelNamespaceRequest(_message.Message):
    __slots__ = ("resource_meta", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class LabelNamespaceResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetNamespaceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetNamespaceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteNamespaceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteNamespaceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class CreateServiceAccountResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetServiceAccountResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteServiceAccountResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class AnnotateServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class AnnotateServiceAccountResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CreateConfigMapRequest(_message.Message):
    __slots__ = ("resource_meta", "config_map_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    CONFIG_MAP_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    config_map_spec: _k8s_pb2.ConfigMap
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., config_map_spec: _Optional[_Union[_k8s_pb2.ConfigMap, _Mapping]] = ...) -> None: ...

class CreateConfigMapResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetConfigMapRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetConfigMapResponse(_message.Message):
    __slots__ = ("status", "config_map_spec")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    CONFIG_MAP_SPEC_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    config_map_spec: _k8s_pb2.ConfigMap
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., config_map_spec: _Optional[_Union[_k8s_pb2.ConfigMap, _Mapping]] = ...) -> None: ...

class UpdateConfigMapRequest(_message.Message):
    __slots__ = ("resource_meta", "from_config_map_spec", "to_config_map_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    FROM_CONFIG_MAP_SPEC_FIELD_NUMBER: _ClassVar[int]
    TO_CONFIG_MAP_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    from_config_map_spec: _k8s_pb2.ConfigMap
    to_config_map_spec: _k8s_pb2.ConfigMap
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., from_config_map_spec: _Optional[_Union[_k8s_pb2.ConfigMap, _Mapping]] = ..., to_config_map_spec: _Optional[_Union[_k8s_pb2.ConfigMap, _Mapping]] = ...) -> None: ...

class UpdateConfigMapResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteConfigMapRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteConfigMapResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateSecretRequest(_message.Message):
    __slots__ = ("resource_meta", "secret_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SECRET_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    secret_spec: _k8s_pb2.Secret
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., secret_spec: _Optional[_Union[_k8s_pb2.Secret, _Mapping]] = ...) -> None: ...

class CreateSecretResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetSecretRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetSecretResponse(_message.Message):
    __slots__ = ("status", "secret_spec")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SECRET_SPEC_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    secret_spec: _k8s_pb2.Secret
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., secret_spec: _Optional[_Union[_k8s_pb2.Secret, _Mapping]] = ...) -> None: ...

class DeleteSecretRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class UpdateSecretRequest(_message.Message):
    __slots__ = ("resource_meta", "secret_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SECRET_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    secret_spec: _k8s_pb2.Secret
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., secret_spec: _Optional[_Union[_k8s_pb2.Secret, _Mapping]] = ...) -> None: ...

class UpdateSecretResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteSecretResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateRisingWaveRequest(_message.Message):
    __slots__ = ("resource_meta", "risingwave_spec", "labels", "annotations")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class AnnotationsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    RISINGWAVE_SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    ANNOTATIONS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    risingwave_spec: _risingwave_pb2.RisingWaveSpec
    labels: _containers.ScalarMap[str, str]
    annotations: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., risingwave_spec: _Optional[_Union[_risingwave_pb2.RisingWaveSpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ..., annotations: _Optional[_Mapping[str, str]] = ...) -> None: ...

class SpecificGroups(_message.Message):
    __slots__ = ("groups",)
    GROUPS_FIELD_NUMBER: _ClassVar[int]
    groups: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, groups: _Optional[_Iterable[str]] = ...) -> None: ...

class EnvVars(_message.Message):
    __slots__ = ("vars",)
    VARS_FIELD_NUMBER: _ClassVar[int]
    vars: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.EnvVar]
    def __init__(self, vars: _Optional[_Iterable[_Union[_k8s_pb2.EnvVar, _Mapping]]] = ...) -> None: ...

class EnvKeys(_message.Message):
    __slots__ = ("keys",)
    KEYS_FIELD_NUMBER: _ClassVar[int]
    keys: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, keys: _Optional[_Iterable[str]] = ...) -> None: ...

class PutRisingWaveEnvRequest(_message.Message):
    __slots__ = ("resource_meta", "specific_groups", "all_groups", "compute_env_change", "compactor_env_change", "standalone_env_change", "meta_env_change", "frontend_env_change")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_GROUPS_FIELD_NUMBER: _ClassVar[int]
    ALL_GROUPS_FIELD_NUMBER: _ClassVar[int]
    COMPUTE_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    COMPACTOR_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    STANDALONE_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    META_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    FRONTEND_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    specific_groups: SpecificGroups
    all_groups: bool
    compute_env_change: EnvVars
    compactor_env_change: EnvVars
    standalone_env_change: EnvVars
    meta_env_change: EnvVars
    frontend_env_change: EnvVars
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., specific_groups: _Optional[_Union[SpecificGroups, _Mapping]] = ..., all_groups: bool = ..., compute_env_change: _Optional[_Union[EnvVars, _Mapping]] = ..., compactor_env_change: _Optional[_Union[EnvVars, _Mapping]] = ..., standalone_env_change: _Optional[_Union[EnvVars, _Mapping]] = ..., meta_env_change: _Optional[_Union[EnvVars, _Mapping]] = ..., frontend_env_change: _Optional[_Union[EnvVars, _Mapping]] = ...) -> None: ...

class PutRisingWaveEnvResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteRisingWaveEnvRequest(_message.Message):
    __slots__ = ("resource_meta", "specific_groups", "all_groups", "compute_env_change", "compactor_env_change", "standalone_env_change", "meta_env_change", "frontend_env_change")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPECIFIC_GROUPS_FIELD_NUMBER: _ClassVar[int]
    ALL_GROUPS_FIELD_NUMBER: _ClassVar[int]
    COMPUTE_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    COMPACTOR_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    STANDALONE_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    META_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    FRONTEND_ENV_CHANGE_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    specific_groups: SpecificGroups
    all_groups: bool
    compute_env_change: EnvKeys
    compactor_env_change: EnvKeys
    standalone_env_change: EnvKeys
    meta_env_change: EnvKeys
    frontend_env_change: EnvKeys
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., specific_groups: _Optional[_Union[SpecificGroups, _Mapping]] = ..., all_groups: bool = ..., compute_env_change: _Optional[_Union[EnvKeys, _Mapping]] = ..., compactor_env_change: _Optional[_Union[EnvKeys, _Mapping]] = ..., standalone_env_change: _Optional[_Union[EnvKeys, _Mapping]] = ..., meta_env_change: _Optional[_Union[EnvKeys, _Mapping]] = ..., frontend_env_change: _Optional[_Union[EnvKeys, _Mapping]] = ...) -> None: ...

class DeleteRisingWaveEnvResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CreateRisingWaveResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetRisingWaveRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetRisingWaveResponse(_message.Message):
    __slots__ = ("status", "risingwave_spec", "risingwave_status")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    RISINGWAVE_SPEC_FIELD_NUMBER: _ClassVar[int]
    RISINGWAVE_STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    risingwave_spec: _risingwave_pb2.RisingWaveSpec
    risingwave_status: _risingwave_pb2.RisingWaveStatus
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., risingwave_spec: _Optional[_Union[_risingwave_pb2.RisingWaveSpec, _Mapping]] = ..., risingwave_status: _Optional[_Union[_risingwave_pb2.RisingWaveStatus, _Mapping]] = ...) -> None: ...

class DeleteRisingWaveRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteRisingWaveResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveImageRequest(_message.Message):
    __slots__ = ("resource_meta", "image_tag")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    IMAGE_TAG_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    image_tag: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., image_tag: _Optional[str] = ...) -> None: ...

class UpdateRisingWaveImageResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateRisingWaveLicenseKeyRequest(_message.Message):
    __slots__ = ("resource_meta", "secret_name")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SECRET_NAME_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    secret_name: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., secret_name: _Optional[str] = ...) -> None: ...

class UpdateRisingWaveLicenseKeyResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateRisingWaveSecretStoreRequest(_message.Message):
    __slots__ = ("resource_meta", "secret_name", "secret_key")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SECRET_NAME_FIELD_NUMBER: _ClassVar[int]
    SECRET_KEY_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    secret_name: str
    secret_key: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., secret_name: _Optional[str] = ..., secret_key: _Optional[str] = ...) -> None: ...

class UpdateRisingWaveSecretStoreResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ScaleRisingWaveRequest(_message.Message):
    __slots__ = ("resource_meta", "meta_scale_spec", "frontend_scale_spec", "compute_scale_spec", "compactor_scale_spec", "connector_scale_spec", "standalone_scale_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    META_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    FRONTEND_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    COMPUTE_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    COMPACTOR_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    CONNECTOR_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    STANDALONE_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    meta_scale_spec: _risingwave_pb2.ScaleSpec
    frontend_scale_spec: _risingwave_pb2.ScaleSpec
    compute_scale_spec: _risingwave_pb2.ScaleSpec
    compactor_scale_spec: _risingwave_pb2.ScaleSpec
    connector_scale_spec: _risingwave_pb2.ScaleSpec
    standalone_scale_spec: _risingwave_pb2.ScaleSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., meta_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., frontend_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., compute_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., compactor_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., connector_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., standalone_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ...) -> None: ...

class ScaleRisingWaveResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ScaleRisingWaveRequestOneOf(_message.Message):
    __slots__ = ("standalone_spec", "cluster_spec", "resource_meta")
    STANDALONE_SPEC_FIELD_NUMBER: _ClassVar[int]
    CLUSTER_SPEC_FIELD_NUMBER: _ClassVar[int]
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    standalone_spec: _risingwave_pb2.ScaleSpec
    cluster_spec: ClusterScaleSpec
    resource_meta: _resource_pb2.Meta
    def __init__(self, standalone_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., cluster_spec: _Optional[_Union[ClusterScaleSpec, _Mapping]] = ..., resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class ClusterScaleSpec(_message.Message):
    __slots__ = ("meta_scale_spec", "frontend_scale_spec", "compute_scale_spec", "compactor_scale_spec")
    META_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    FRONTEND_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    COMPUTE_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    COMPACTOR_SCALE_SPEC_FIELD_NUMBER: _ClassVar[int]
    meta_scale_spec: _risingwave_pb2.ScaleSpec
    frontend_scale_spec: _risingwave_pb2.ScaleSpec
    compute_scale_spec: _risingwave_pb2.ScaleSpec
    compactor_scale_spec: _risingwave_pb2.ScaleSpec
    def __init__(self, meta_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., frontend_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., compute_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ..., compactor_scale_spec: _Optional[_Union[_risingwave_pb2.ScaleSpec, _Mapping]] = ...) -> None: ...

class RisingWaveReplicaOverride(_message.Message):
    __slots__ = ("replicas", "component", "node_group")
    REPLICAS_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    replicas: int
    component: str
    node_group: str
    def __init__(self, replicas: _Optional[int] = ..., component: _Optional[str] = ..., node_group: _Optional[str] = ...) -> None: ...

class StartRisingWaveRequest(_message.Message):
    __slots__ = ("resource_meta", "overrides")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    OVERRIDES_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    overrides: _containers.RepeatedCompositeFieldContainer[RisingWaveReplicaOverride]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., overrides: _Optional[_Iterable[_Union[RisingWaveReplicaOverride, _Mapping]]] = ...) -> None: ...

class StartRisingWaveResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class StopRisingWaveRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StopRisingWaveResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateRisingWaveComponentsRequest(_message.Message):
    __slots__ = ("resource_meta", "components_spec", "enable_standalone_mode")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    COMPONENTS_SPEC_FIELD_NUMBER: _ClassVar[int]
    ENABLE_STANDALONE_MODE_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    components_spec: _risingwave_pb2.ComponentsSpec
    enable_standalone_mode: bool
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., components_spec: _Optional[_Union[_risingwave_pb2.ComponentsSpec, _Mapping]] = ..., enable_standalone_mode: bool = ...) -> None: ...

class UpdateRisingWaveComponentsResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateRisingWaveMetaStoreRequest(_message.Message):
    __slots__ = ("resource_meta", "meta_store_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    META_STORE_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    meta_store_spec: _risingwave_pb2.MetaStoreSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., meta_store_spec: _Optional[_Union[_risingwave_pb2.MetaStoreSpec, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveMetaStoreResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CreateRisingWaveComputeNodeGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "node_group")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    node_group: _risingwave_pb2.NodeGroupSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., node_group: _Optional[_Union[_risingwave_pb2.NodeGroupSpec, _Mapping]] = ...) -> None: ...

class CreateRisingWaveComputeNodeGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveComputeNodeGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "node_group")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    node_group: _risingwave_pb2.NodeGroupSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., node_group: _Optional[_Union[_risingwave_pb2.NodeGroupSpec, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveComputeNodeGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteRisingWaveComputeNodeGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "node_group_name")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_NAME_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    node_group_name: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., node_group_name: _Optional[str] = ...) -> None: ...

class DeleteRisingWaveComputeNodeGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateRisingWaveNodeGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "component", "node_group")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    component: _risingwave_pb2.ComponentType
    node_group: _risingwave_pb2.NodeGroupSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., component: _Optional[_Union[_risingwave_pb2.ComponentType, str]] = ..., node_group: _Optional[_Union[_risingwave_pb2.NodeGroupSpec, _Mapping]] = ...) -> None: ...

class CreateRisingWaveNodeGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveNodeGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "component", "node_group")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    component: _risingwave_pb2.ComponentType
    node_group: _risingwave_pb2.NodeGroupSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., component: _Optional[_Union[_risingwave_pb2.ComponentType, str]] = ..., node_group: _Optional[_Union[_risingwave_pb2.NodeGroupSpec, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveNodeGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteRisingWaveNodeGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "component", "node_group_name")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_NAME_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    component: _risingwave_pb2.ComponentType
    node_group_name: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., component: _Optional[_Union[_risingwave_pb2.ComponentType, str]] = ..., node_group_name: _Optional[str] = ...) -> None: ...

class DeleteRisingWaveNodeGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveNodeGroupConfigurationRequest(_message.Message):
    __slots__ = ("resource_meta", "component", "node_group", "node_config")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    NODE_CONFIG_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    component: _risingwave_pb2.ComponentType
    node_group: str
    node_config: _risingwave_pb2.NodeConfig
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., component: _Optional[_Union[_risingwave_pb2.ComponentType, str]] = ..., node_group: _Optional[str] = ..., node_config: _Optional[_Union[_risingwave_pb2.NodeConfig, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveNodeGroupConfigurationResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveNodeGroupRestartAtRequest(_message.Message):
    __slots__ = ("resource_meta", "component", "node_group", "restartAt")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    COMPONENT_FIELD_NUMBER: _ClassVar[int]
    NODE_GROUP_FIELD_NUMBER: _ClassVar[int]
    RESTARTAT_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    component: _risingwave_pb2.ComponentType
    node_group: str
    restartAt: _timestamp_pb2.Timestamp
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., component: _Optional[_Union[_risingwave_pb2.ComponentType, str]] = ..., node_group: _Optional[str] = ..., restartAt: _Optional[_Union[_timestamp_pb2.Timestamp, _Mapping]] = ...) -> None: ...

class UpdateRisingWaveNodeGroupRestartAtResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class DeletePersistentVolumeClaimsRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeletePersistentVolumeClaimsResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class GetPersistentVolumeClaimsRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetPersistentVolumeClaimsResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class CreatePersistentVolumeClaimRequest(_message.Message):
    __slots__ = ("resource_meta", "spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    spec: _k8s_pb2.PersistentVolumeClaimSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., spec: _Optional[_Union[_k8s_pb2.PersistentVolumeClaimSpec, _Mapping]] = ...) -> None: ...

class CreatePersistentVolumeClaimResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class InstallHelmReleaseRequest(_message.Message):
    __slots__ = ("resource_meta", "release_meta", "chart_url", "values_json")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    RELEASE_META_FIELD_NUMBER: _ClassVar[int]
    CHART_URL_FIELD_NUMBER: _ClassVar[int]
    VALUES_JSON_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    release_meta: _resource_pb2.Meta
    chart_url: str
    values_json: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., release_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., chart_url: _Optional[str] = ..., values_json: _Optional[str] = ...) -> None: ...

class InstallHelmReleaseResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class UpgradeHelmReleaseRequest(_message.Message):
    __slots__ = ("resource_meta", "release_meta", "chart_url", "values_json", "install")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    RELEASE_META_FIELD_NUMBER: _ClassVar[int]
    CHART_URL_FIELD_NUMBER: _ClassVar[int]
    VALUES_JSON_FIELD_NUMBER: _ClassVar[int]
    INSTALL_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    release_meta: _resource_pb2.Meta
    chart_url: str
    values_json: str
    install: bool
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., release_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., chart_url: _Optional[str] = ..., values_json: _Optional[str] = ..., install: bool = ...) -> None: ...

class UpgradeHelmReleaseResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class UninstallHelmReleaseRequest(_message.Message):
    __slots__ = ("resource_meta", "release_meta")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    RELEASE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    release_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., release_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class UninstallHelmReleaseResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetHelmReleaseRequest(_message.Message):
    __slots__ = ("release_meta",)
    RELEASE_META_FIELD_NUMBER: _ClassVar[int]
    release_meta: _resource_pb2.Meta
    def __init__(self, release_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetHelmReleaseResponse(_message.Message):
    __slots__ = ("helm_release",)
    HELM_RELEASE_FIELD_NUMBER: _ClassVar[int]
    helm_release: _k8s_pb2.HelmRelease
    def __init__(self, helm_release: _Optional[_Union[_k8s_pb2.HelmRelease, _Mapping]] = ...) -> None: ...

class GetPodPhasesRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetPodPhasesResponse(_message.Message):
    __slots__ = ("pod_to_phase",)
    class PodToPhaseEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: _k8s_pb2.PodPhase
        def __init__(self, key: _Optional[str] = ..., value: _Optional[_Union[_k8s_pb2.PodPhase, str]] = ...) -> None: ...
    POD_TO_PHASE_FIELD_NUMBER: _ClassVar[int]
    pod_to_phase: _containers.ScalarMap[str, _k8s_pb2.PodPhase]
    def __init__(self, pod_to_phase: _Optional[_Mapping[str, _k8s_pb2.PodPhase]] = ...) -> None: ...

class RestartStatefulSetRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class RestartStatefulSetResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class RestartDeploymentRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class RestartDeploymentResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GetStatefulSetReplicasStatusRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetStatefulSetReplicasStatusResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class GetDeploymentReplicasStatusRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetDeploymentReplicasStatusResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateServiceMonitorRequest(_message.Message):
    __slots__ = ("resource_meta", "service_monitor_spec", "labels", "annotations")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class AnnotationsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SERVICE_MONITOR_SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    ANNOTATIONS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    service_monitor_spec: _prometheus_pb2.ServiceMonitorSpec
    labels: _containers.ScalarMap[str, str]
    annotations: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., service_monitor_spec: _Optional[_Union[_prometheus_pb2.ServiceMonitorSpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ..., annotations: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateServiceMonitorResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreatePodMonitoringRequest(_message.Message):
    __slots__ = ("resource_meta", "pod_monitoring_spec", "labels", "annotations")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    class AnnotationsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    POD_MONITORING_SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    ANNOTATIONS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    pod_monitoring_spec: _gmp_pb2.PodMonitoringSpec
    labels: _containers.ScalarMap[str, str]
    annotations: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., pod_monitoring_spec: _Optional[_Union[_gmp_pb2.PodMonitoringSpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ..., annotations: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreatePodMonitoringResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateServiceRequest(_message.Message):
    __slots__ = ("resource_meta", "service_spec", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SERVICE_SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    service_spec: _k8s_pb2.ServiceSpec
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., service_spec: _Optional[_Union[_k8s_pb2.ServiceSpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateServiceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateNetworkPolicyRequest(_message.Message):
    __slots__ = ("resource_meta", "spec", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    spec: _k8s_pb2.NetworkPolicySpec
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., spec: _Optional[_Union[_k8s_pb2.NetworkPolicySpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateNetworkPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateOrUpdateNetworkPolicyRequest(_message.Message):
    __slots__ = ("resource_meta", "spec", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    spec: _k8s_pb2.NetworkPolicySpec
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., spec: _Optional[_Union[_k8s_pb2.NetworkPolicySpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateOrUpdateNetworkPolicyResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CreatePostgreSqlRequest(_message.Message):
    __slots__ = ("resource_meta", "postgresql_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    POSTGRESQL_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    postgresql_spec: _postgresql_pb2.PostgreSqlSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., postgresql_spec: _Optional[_Union[_postgresql_pb2.PostgreSqlSpec, _Mapping]] = ...) -> None: ...

class CreatePostgreSqlResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeletePostgreSqlRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeletePostgreSqlResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class UpdatePostgreSqlRequest(_message.Message):
    __slots__ = ("resource_meta", "postgresql_spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    POSTGRESQL_SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    postgresql_spec: _postgresql_pb2.PostgreSqlSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., postgresql_spec: _Optional[_Union[_postgresql_pb2.PostgreSqlSpec, _Mapping]] = ...) -> None: ...

class UpdatePostgreSqlResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _postgresql_pb2.UpdateStatusCode
    def __init__(self, status: _Optional[_Union[_postgresql_pb2.UpdateStatusCode, str]] = ...) -> None: ...

class GetPostgreSqlRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetPostgreSqlResponse(_message.Message):
    __slots__ = ("status", "postgresql_spec", "secret_ref", "credentials")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    POSTGRESQL_SPEC_FIELD_NUMBER: _ClassVar[int]
    SECRET_REF_FIELD_NUMBER: _ClassVar[int]
    CREDENTIALS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    postgresql_spec: _postgresql_pb2.PostgreSqlSpec
    secret_ref: _resource_pb2.Meta
    credentials: _postgresql_pb2.Credentials
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., postgresql_spec: _Optional[_Union[_postgresql_pb2.PostgreSqlSpec, _Mapping]] = ..., secret_ref: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., credentials: _Optional[_Union[_postgresql_pb2.Credentials, _Mapping]] = ...) -> None: ...

class DeleteNetworkPolicyRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteNetworkPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteServiceMonitorRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteServiceMonitorResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class DeletePodMonitoringRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeletePodMonitoringResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class GetPodsRequest(_message.Message):
    __slots__ = ("namespace",)
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    namespace: str
    def __init__(self, namespace: _Optional[str] = ...) -> None: ...

class GetPodsResponse(_message.Message):
    __slots__ = ("pods",)
    PODS_FIELD_NUMBER: _ClassVar[int]
    pods: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Pod]
    def __init__(self, pods: _Optional[_Iterable[_Union[_k8s_pb2.Pod, _Mapping]]] = ...) -> None: ...

class GetClusterAccessRequest(_message.Message):
    __slots__ = ("timeout",)
    TIMEOUT_FIELD_NUMBER: _ClassVar[int]
    timeout: _duration_pb2.Duration
    def __init__(self, timeout: _Optional[_Union[_duration_pb2.Duration, _Mapping]] = ...) -> None: ...

class GetClusterAccessResponse(_message.Message):
    __slots__ = ("cluster_endpoint", "cluster_ca_certificate_base64", "token")
    CLUSTER_ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    CLUSTER_CA_CERTIFICATE_BASE64_FIELD_NUMBER: _ClassVar[int]
    TOKEN_FIELD_NUMBER: _ClassVar[int]
    cluster_endpoint: str
    cluster_ca_certificate_base64: str
    token: str
    def __init__(self, cluster_endpoint: _Optional[str] = ..., cluster_ca_certificate_base64: _Optional[str] = ..., token: _Optional[str] = ...) -> None: ...
