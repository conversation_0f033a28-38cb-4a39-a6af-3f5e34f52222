# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/agent.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/agent.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x14services/agent.proto\x12\x0eservices.agent\"\x13\n\x11GetVersionRequest\"%\n\x12GetVersionResponse\x12\x0f\n\x07version\x18\x01 \x01(\t2^\n\x05\x41gent\x12U\n\nGetVersion\x12!.services.agent.GetVersionRequest\x1a\".services.agent.GetVersionResponse\"\x00\x42;Z9github.com/risingwavelabs/cloudagent/pbgen/services/agentb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.agent_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z9github.com/risingwavelabs/cloudagent/pbgen/services/agent'
  _globals['_GETVERSIONREQUEST']._serialized_start=40
  _globals['_GETVERSIONREQUEST']._serialized_end=59
  _globals['_GETVERSIONRESPONSE']._serialized_start=61
  _globals['_GETVERSIONRESPONSE']._serialized_end=98
  _globals['_AGENT']._serialized_start=100
  _globals['_AGENT']._serialized_end=194
# @@protoc_insertion_point(module_scope)
