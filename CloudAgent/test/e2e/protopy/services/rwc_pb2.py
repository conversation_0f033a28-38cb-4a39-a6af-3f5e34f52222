# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/rwc.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/rwc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12services/rwc.proto\x12\x0cservices.rwc\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/resource.proto\x1a\x10\x63ommon/k8s.proto\"l\n\x15MetaNodeBackupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x0f\n\x07rw_name\x18\x02 \x01(\t\x12\x14\n\x0crw_namespace\x18\x03 \x01(\t\"J\n\x16MetaNodeBackupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"M\n\x15ValidateSourceRequest\x12\x0f\n\x07rw_name\x18\x01 \x01(\t\x12\x14\n\x0crw_namespace\x18\x02 \x01(\t\x12\r\n\x05props\x18\x03 \x01(\t\"/\n\x16ValidateSourceResponse\x12\x15\n\rerror_message\x18\x01 \x01(\t\">\n\x15GetClusterInfoRequest\x12\x0f\n\x07rw_name\x18\x01 \x01(\t\x12\x14\n\x0crw_namespace\x18\x02 \x01(\t\"(\n\x16GetClusterInfoResponse\x12\x0e\n\x06output\x18\x01 \x01(\t\"O\n\x14\x43ordonWorkersRequest\x12\x0f\n\x07rw_name\x18\x01 \x01(\t\x12\x14\n\x0crw_namespace\x18\x02 \x01(\t\x12\x10\n\x08work_ids\x18\x03 \x03(\t\"\x17\n\x15\x43ordonWorkersResponse\"p\n\x14ResizeWorkersRequest\x12\x0f\n\x07rw_name\x18\x01 \x01(\t\x12\x14\n\x0crw_namespace\x18\x02 \x01(\t\x12\x16\n\x0e\x61\x64\x64\x65\x64_work_ids\x18\x03 \x03(\t\x12\x19\n\x11\x64\x65leting_work_ids\x18\x04 \x03(\t\"\x17\n\x15ResizeWorkersResponse\"O\n\x14\x44\x65leteWorkersRequest\x12\x0f\n\x07rw_name\x18\x01 \x01(\t\x12\x14\n\x0crw_namespace\x18\x02 \x01(\t\x12\x10\n\x08work_ids\x18\x03 \x03(\t\"\x17\n\x15\x44\x65leteWorkersResponse\"g\n\x15\x44\x65leteSnapshotRequest\x12\x0f\n\x07rw_name\x18\x01 \x01(\t\x12\x14\n\x0crw_namespace\x18\x02 \x01(\t\x12\x13\n\x0bsnapshot_id\x18\x03 \x01(\x03\x12\x12\n\nrw_version\x18\x04 \x01(\t\"\x18\n\x16\x44\x65leteSnapshotResponse\"q\n\x16RestoreMetaRequestEtcd\x12\x16\n\x0e\x65tcd_endpoints\x18\x01 \x01(\t\x12\x11\n\tetcd_auth\x18\x02 \x01(\x08\x12\x15\n\retcd_username\x18\x03 \x01(\t\x12\x15\n\retcd_password\x18\x04 \x01(\t\"-\n\x15RestoreMetaRequestSql\x12\x14\n\x0csql_endpoint\x18\x01 \x01(\t\"\x89\x04\n\x12RestoreMetaRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x17\n\x0fservice_account\x18\x02 \x01(\t\x12\x14\n\x0crw_image_tag\x18\x03 \x01(\t\x12\x18\n\x10meta_snapshot_id\x18\x04 \x01(\x03\x12\x17\n\x0fmeta_store_type\x18\x05 \x01(\t\x12\x1a\n\x12\x62\x61\x63kup_storage_url\x18\x06 \x01(\t\x12\x1a\n\x12\x62\x61\x63kup_storage_dir\x18\x07 \x01(\t\x12\x1b\n\x13hummock_storage_url\x18\x08 \x01(\t\x12\x1b\n\x13hummock_storage_dir\x18\t \x01(\t\x12;\n\x0b\x65tcd_config\x18\n \x01(\x0b\x32$.services.rwc.RestoreMetaRequestEtcdH\x00\x12\x39\n\nsql_config\x18\x0b \x01(\x0b\x32#.services.rwc.RestoreMetaRequestSqlH\x00\x12\x38\n\x04\x65nvs\x18\x0e \x03(\x0b\x32*.services.rwc.RestoreMetaRequest.EnvsEntry\x1a+\n\tEnvsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x42\x12\n\x10metastore_config\"G\n\x13RestoreMetaResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"n\n\x15VacuumEtcdMetaRequest\x12\x10\n\x08pod_name\x18\x01 \x01(\t\x12\x15\n\rpod_namespace\x18\x02 \x01(\t\x12\x15\n\retcd_username\x18\x03 \x01(\t\x12\x15\n\retcd_password\x18\x04 \x01(\t\"\x18\n\x16VacuumEtcdMetaResponse\"D\n\x19GenDiagnosisReportRequest\x12\x14\n\x0cservice_name\x18\x02 \x01(\t\x12\x11\n\tnamespace\x18\x01 \x01(\t\",\n\x1aGenDiagnosisReportResponse\x12\x0e\n\x06report\x18\x01 \x01(\t\"c\n\x1fGenDiagnosisReportStreamRequest\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x14\n\x0cservice_name\x18\x02 \x01(\t\x12\x17\n\x0fgzip_compressed\x18\x03 \x01(\x08\"8\n GenDiagnosisReportStreamResponse\x12\x14\n\x0creport_chunk\x18\x01 \x01(\x0c\"\xc6\x02\n\x14MetaMigrationRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x0f\n\x07rw_name\x18\x02 \x01(\t\x12\x14\n\x0crw_namespace\x18\x03 \x01(\t\x12\x16\n\x0e\x65tcd_endpoints\x18\x04 \x01(\t\x12\x14\n\x0csql_endpoint\x18\x05 \x01(\t\x12\x12\n\ntask_image\x18\x06 \x01(\t\x12\x38\n\x0etask_resources\x18\x07 \x01(\x0b\x32 .common.k8s.ResourceRequirements\x12\x30\n\x10task_tolerations\x18\x08 \x03(\x0b\x32\x16.common.k8s.Toleration\x12+\n\rtask_affinity\x18\t \x01(\x0b\x32\x14.common.k8s.Affinity\"I\n\x15MetaMigrationResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xd6\x02\n\x0bKafkaConfig\x12\x0e\n\x06server\x18\x01 \x01(\t\x12\x43\n\x11security_protocol\x18\x02 \x01(\x0e\x32#.services.rwc.KafkaSecurityProtocolH\x00\x88\x01\x01\x12=\n\x0esasl_mechanism\x18\x03 \x01(\x0e\x32 .services.rwc.KafkaSaslMechanismH\x01\x88\x01\x01\x12\x1a\n\rsasl_username\x18\x04 \x01(\tH\x02\x88\x01\x01\x12\x1a\n\rsasl_password\x18\x05 \x01(\tH\x03\x88\x01\x01\x12\x1b\n\x0e\x63\x61_certificate\x18\x06 \x01(\tH\x04\x88\x01\x01\x42\x14\n\x12_security_protocolB\x11\n\x0f_sasl_mechanismB\x10\n\x0e_sasl_usernameB\x10\n\x0e_sasl_passwordB\x11\n\x0f_ca_certificate\"B\n\x16\x46\x65tchKafkaTopicRequest\x12(\n\x05kafka\x18\x01 \x01(\x0b\x32\x19.services.rwc.KafkaConfig\")\n\x17\x46\x65tchKafkaTopicResponse\x12\x0e\n\x06topics\x18\x01 \x03(\t\"S\n\x18\x46\x65tchKafkaMessageRequest\x12(\n\x05kafka\x18\x01 \x01(\x0b\x32\x19.services.rwc.KafkaConfig\x12\r\n\x05topic\x18\x02 \x01(\t\"7\n\x19\x46\x65tchKafkaMessageResponse\x12\x0b\n\x03key\x18\x01 \x01(\x0c\x12\r\n\x05value\x18\x02 \x01(\x0c\"\x97\x01\n\x0ePostgresConfig\x12\x10\n\x08hostname\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\x05\x12\x10\n\x08username\x18\x03 \x01(\t\x12\x10\n\x08password\x18\x04 \x01(\t\x12\x10\n\x08\x64\x61tabase\x18\x05 \x01(\t\x12/\n\x08ssl_mode\x18\x06 \x01(\x0e\x32\x1d.services.rwc.PostgresSslMode\"K\n\x19\x46\x65tchPostgresTableRequest\x12.\n\x08postgres\x18\x01 \x01(\x0b\x32\x1c.services.rwc.PostgresConfig\",\n\x1a\x46\x65tchPostgresTableResponse\x12\x0e\n\x06tables\x18\x01 \x03(\t\"s\n\x1aSchemaSchemaRegistryConfig\x12\r\n\x05topic\x18\x01 \x01(\t\x12\x15\n\x08username\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x15\n\x08password\x18\x03 \x01(\tH\x01\x88\x01\x01\x42\x0b\n\t_usernameB\x0b\n\t_password\"|\n\x0eSchemaS3Config\x12\x0e\n\x06region\x18\x01 \x01(\t\x12\x18\n\x0b\x61\x63\x63\x65ssKeyId\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x1c\n\x0fsecretAccessKey\x18\x03 \x01(\tH\x01\x88\x01\x01\x42\x0e\n\x0c_accessKeyIdB\x12\n\x10_secretAccessKey\"\x95\x02\n\x18\x46\x65tchSourceSchemaRequest\x12*\n\x06\x66ormat\x18\x01 \x01(\x0e\x32\x1a.services.rwc.SchemaFormat\x12.\n\x08location\x18\x02 \x01(\x0e\x32\x1c.services.rwc.SchemaLocation\x12\x0b\n\x03url\x18\x03 \x01(\t\x12\x46\n\x0fschema_registry\x18\x04 \x01(\x0b\x32(.services.rwc.SchemaSchemaRegistryConfigH\x00\x88\x01\x01\x12-\n\x02s3\x18\x05 \x01(\x0b\x32\x1c.services.rwc.SchemaS3ConfigH\x01\x88\x01\x01\x42\x12\n\x10_schema_registryB\x05\n\x03_s3\"G\n\x19\x46\x65tchSourceSchemaResponse\x12*\n\x05\x66iles\x18\x01 \x03(\x0b\x32\x1b.services.rwc.RawSchemaFile\".\n\rRawSchemaFile\x12\x0f\n\x07\x63ontent\x18\x01 \x01(\x0c\x12\x0c\n\x04name\x18\x02 \x01(\t*\\\n\x15KafkaSecurityProtocol\x12\x18\n\x14PROTOCOL_UNSPECIFIED\x10\x00\x12\x12\n\x0eSASL_PLAINTEXT\x10\x01\x12\x0c\n\x08SASL_SSL\x10\x02\x12\x07\n\x03SSL\x10\x03*`\n\x12KafkaSaslMechanism\x12\x19\n\x15MECHANISM_UNSPECIFIED\x10\x00\x12\t\n\x05PLAIN\x10\x01\x12\x11\n\rSCRAM_SHA_256\x10\x02\x12\x11\n\rSCRAM_SHA_512\x10\x03*v\n\x0fPostgresSslMode\x12\x18\n\x14SSL_MODE_UNSPECIFIED\x10\x00\x12\x0c\n\x08\x44ISABLED\x10\x01\x12\r\n\tPREFERRED\x10\x02\x12\x0c\n\x08REQUIRED\x10\x03\x12\r\n\tVERIFY_CA\x10\x04\x12\x0f\n\x0bVERIFY_FULL\x10\x05*H\n\x0cSchemaFormat\x12\x16\n\x12\x46ORMAT_UNSPECIFIED\x10\x00\x12\x08\n\x04\x41VRO\x10\x01\x12\x0c\n\x08PROTOBUF\x10\x02\x12\x08\n\x04JSON\x10\x03*Y\n\x0eSchemaLocation\x12\x18\n\x14LOCATION_UNSPECIFIED\x10\x00\x12\x10\n\x0cWEB_LOCATION\x10\x01\x12\x13\n\x0fSCHEMA_REGISTRY\x10\x02\x12\x06\n\x02S3\x10\x03\x32\xbb\x0c\n\x11RisingwaveControl\x12]\n\x0eMetaNodeBackup\x12#.services.rwc.MetaNodeBackupRequest\x1a$.services.rwc.MetaNodeBackupResponse\"\x00\x12]\n\x0eValidateSource\x12#.services.rwc.ValidateSourceRequest\x1a$.services.rwc.ValidateSourceResponse\"\x00\x12]\n\x0eGetClusterInfo\x12#.services.rwc.GetClusterInfoRequest\x1a$.services.rwc.GetClusterInfoResponse\"\x00\x12Z\n\rCordonWorkers\x12\".services.rwc.CordonWorkersRequest\x1a#.services.rwc.CordonWorkersResponse\"\x00\x12Z\n\rResizeWorkers\x12\".services.rwc.ResizeWorkersRequest\x1a#.services.rwc.ResizeWorkersResponse\"\x00\x12Z\n\rDeleteWorkers\x12\".services.rwc.DeleteWorkersRequest\x1a#.services.rwc.DeleteWorkersResponse\"\x00\x12]\n\x0e\x44\x65leteSnapshot\x12#.services.rwc.DeleteSnapshotRequest\x1a$.services.rwc.DeleteSnapshotResponse\"\x00\x12T\n\x0bRestoreMeta\x12 .services.rwc.RestoreMetaRequest\x1a!.services.rwc.RestoreMetaResponse\"\x00\x12]\n\x0eVacuumEtcdMeta\x12#.services.rwc.VacuumEtcdMetaRequest\x1a$.services.rwc.VacuumEtcdMetaResponse\"\x00\x12i\n\x12GenDiagnosisReport\x12\'.services.rwc.GenDiagnosisReportRequest\x1a(.services.rwc.GenDiagnosisReportResponse\"\x00\x12}\n\x18GenDiagnosisReportStream\x12-.services.rwc.GenDiagnosisReportStreamRequest\x1a..services.rwc.GenDiagnosisReportStreamResponse\"\x00\x30\x01\x12Z\n\rMetaMigration\x12\".services.rwc.MetaMigrationRequest\x1a#.services.rwc.MetaMigrationResponse\"\x00\x12`\n\x0f\x46\x65tchKafkaTopic\x12$.services.rwc.FetchKafkaTopicRequest\x1a%.services.rwc.FetchKafkaTopicResponse\"\x00\x12\x66\n\x11\x46\x65tchKafkaMessage\x12&.services.rwc.FetchKafkaMessageRequest\x1a\'.services.rwc.FetchKafkaMessageResponse\"\x00\x12i\n\x12\x46\x65tchPostgresTable\x12\'.services.rwc.FetchPostgresTableRequest\x1a(.services.rwc.FetchPostgresTableResponse\"\x00\x12\x66\n\x11\x46\x65tchSourceSchema\x12&.services.rwc.FetchSourceSchemaRequest\x1a\'.services.rwc.FetchSourceSchemaResponse\"\x00\x42\x39Z7github.com/risingwavelabs/cloudagent/pbgen/services/rwcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.rwc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z7github.com/risingwavelabs/cloudagent/pbgen/services/rwc'
  _globals['_RESTOREMETAREQUEST_ENVSENTRY']._loaded_options = None
  _globals['_RESTOREMETAREQUEST_ENVSENTRY']._serialized_options = b'8\001'
  _globals['_KAFKASECURITYPROTOCOL']._serialized_start=4097
  _globals['_KAFKASECURITYPROTOCOL']._serialized_end=4189
  _globals['_KAFKASASLMECHANISM']._serialized_start=4191
  _globals['_KAFKASASLMECHANISM']._serialized_end=4287
  _globals['_POSTGRESSSLMODE']._serialized_start=4289
  _globals['_POSTGRESSSLMODE']._serialized_end=4407
  _globals['_SCHEMAFORMAT']._serialized_start=4409
  _globals['_SCHEMAFORMAT']._serialized_end=4481
  _globals['_SCHEMALOCATION']._serialized_start=4483
  _globals['_SCHEMALOCATION']._serialized_end=4572
  _globals['_METANODEBACKUPREQUEST']._serialized_start=100
  _globals['_METANODEBACKUPREQUEST']._serialized_end=208
  _globals['_METANODEBACKUPRESPONSE']._serialized_start=210
  _globals['_METANODEBACKUPRESPONSE']._serialized_end=284
  _globals['_VALIDATESOURCEREQUEST']._serialized_start=286
  _globals['_VALIDATESOURCEREQUEST']._serialized_end=363
  _globals['_VALIDATESOURCERESPONSE']._serialized_start=365
  _globals['_VALIDATESOURCERESPONSE']._serialized_end=412
  _globals['_GETCLUSTERINFOREQUEST']._serialized_start=414
  _globals['_GETCLUSTERINFOREQUEST']._serialized_end=476
  _globals['_GETCLUSTERINFORESPONSE']._serialized_start=478
  _globals['_GETCLUSTERINFORESPONSE']._serialized_end=518
  _globals['_CORDONWORKERSREQUEST']._serialized_start=520
  _globals['_CORDONWORKERSREQUEST']._serialized_end=599
  _globals['_CORDONWORKERSRESPONSE']._serialized_start=601
  _globals['_CORDONWORKERSRESPONSE']._serialized_end=624
  _globals['_RESIZEWORKERSREQUEST']._serialized_start=626
  _globals['_RESIZEWORKERSREQUEST']._serialized_end=738
  _globals['_RESIZEWORKERSRESPONSE']._serialized_start=740
  _globals['_RESIZEWORKERSRESPONSE']._serialized_end=763
  _globals['_DELETEWORKERSREQUEST']._serialized_start=765
  _globals['_DELETEWORKERSREQUEST']._serialized_end=844
  _globals['_DELETEWORKERSRESPONSE']._serialized_start=846
  _globals['_DELETEWORKERSRESPONSE']._serialized_end=869
  _globals['_DELETESNAPSHOTREQUEST']._serialized_start=871
  _globals['_DELETESNAPSHOTREQUEST']._serialized_end=974
  _globals['_DELETESNAPSHOTRESPONSE']._serialized_start=976
  _globals['_DELETESNAPSHOTRESPONSE']._serialized_end=1000
  _globals['_RESTOREMETAREQUESTETCD']._serialized_start=1002
  _globals['_RESTOREMETAREQUESTETCD']._serialized_end=1115
  _globals['_RESTOREMETAREQUESTSQL']._serialized_start=1117
  _globals['_RESTOREMETAREQUESTSQL']._serialized_end=1162
  _globals['_RESTOREMETAREQUEST']._serialized_start=1165
  _globals['_RESTOREMETAREQUEST']._serialized_end=1686
  _globals['_RESTOREMETAREQUEST_ENVSENTRY']._serialized_start=1623
  _globals['_RESTOREMETAREQUEST_ENVSENTRY']._serialized_end=1666
  _globals['_RESTOREMETARESPONSE']._serialized_start=1688
  _globals['_RESTOREMETARESPONSE']._serialized_end=1759
  _globals['_VACUUMETCDMETAREQUEST']._serialized_start=1761
  _globals['_VACUUMETCDMETAREQUEST']._serialized_end=1871
  _globals['_VACUUMETCDMETARESPONSE']._serialized_start=1873
  _globals['_VACUUMETCDMETARESPONSE']._serialized_end=1897
  _globals['_GENDIAGNOSISREPORTREQUEST']._serialized_start=1899
  _globals['_GENDIAGNOSISREPORTREQUEST']._serialized_end=1967
  _globals['_GENDIAGNOSISREPORTRESPONSE']._serialized_start=1969
  _globals['_GENDIAGNOSISREPORTRESPONSE']._serialized_end=2013
  _globals['_GENDIAGNOSISREPORTSTREAMREQUEST']._serialized_start=2015
  _globals['_GENDIAGNOSISREPORTSTREAMREQUEST']._serialized_end=2114
  _globals['_GENDIAGNOSISREPORTSTREAMRESPONSE']._serialized_start=2116
  _globals['_GENDIAGNOSISREPORTSTREAMRESPONSE']._serialized_end=2172
  _globals['_METAMIGRATIONREQUEST']._serialized_start=2175
  _globals['_METAMIGRATIONREQUEST']._serialized_end=2501
  _globals['_METAMIGRATIONRESPONSE']._serialized_start=2503
  _globals['_METAMIGRATIONRESPONSE']._serialized_end=2576
  _globals['_KAFKACONFIG']._serialized_start=2579
  _globals['_KAFKACONFIG']._serialized_end=2921
  _globals['_FETCHKAFKATOPICREQUEST']._serialized_start=2923
  _globals['_FETCHKAFKATOPICREQUEST']._serialized_end=2989
  _globals['_FETCHKAFKATOPICRESPONSE']._serialized_start=2991
  _globals['_FETCHKAFKATOPICRESPONSE']._serialized_end=3032
  _globals['_FETCHKAFKAMESSAGEREQUEST']._serialized_start=3034
  _globals['_FETCHKAFKAMESSAGEREQUEST']._serialized_end=3117
  _globals['_FETCHKAFKAMESSAGERESPONSE']._serialized_start=3119
  _globals['_FETCHKAFKAMESSAGERESPONSE']._serialized_end=3174
  _globals['_POSTGRESCONFIG']._serialized_start=3177
  _globals['_POSTGRESCONFIG']._serialized_end=3328
  _globals['_FETCHPOSTGRESTABLEREQUEST']._serialized_start=3330
  _globals['_FETCHPOSTGRESTABLEREQUEST']._serialized_end=3405
  _globals['_FETCHPOSTGRESTABLERESPONSE']._serialized_start=3407
  _globals['_FETCHPOSTGRESTABLERESPONSE']._serialized_end=3451
  _globals['_SCHEMASCHEMAREGISTRYCONFIG']._serialized_start=3453
  _globals['_SCHEMASCHEMAREGISTRYCONFIG']._serialized_end=3568
  _globals['_SCHEMAS3CONFIG']._serialized_start=3570
  _globals['_SCHEMAS3CONFIG']._serialized_end=3694
  _globals['_FETCHSOURCESCHEMAREQUEST']._serialized_start=3697
  _globals['_FETCHSOURCESCHEMAREQUEST']._serialized_end=3974
  _globals['_FETCHSOURCESCHEMARESPONSE']._serialized_start=3976
  _globals['_FETCHSOURCESCHEMARESPONSE']._serialized_end=4047
  _globals['_RAWSCHEMAFILE']._serialized_start=4049
  _globals['_RAWSCHEMAFILE']._serialized_end=4095
  _globals['_RISINGWAVECONTROL']._serialized_start=4575
  _globals['_RISINGWAVECONTROL']._serialized_end=6170
# @@protoc_insertion_point(module_scope)
