# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import azr_pb2 as services_dot_azr__pb2
from services.common import data_pb2 as services_dot_common_dot_data__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/azr_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AzrResourceManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateUserAssignedIdentity = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreateUserAssignedIdentity',
                request_serializer=services_dot_azr__pb2.CreateUserAssignedIdentityRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.CreateUserAssignedIdentityResponse.FromString,
                _registered_method=True)
        self.DeleteUserAssignedIdentity = channel.unary_unary(
                '/services.azr.AzrResourceManager/DeleteUserAssignedIdentity',
                request_serializer=services_dot_azr__pb2.DeleteUserAssignedIdentityRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.DeleteUserAssignedIdentityResponse.FromString,
                _registered_method=True)
        self.GetUserAssignedIdentity = channel.unary_unary(
                '/services.azr.AzrResourceManager/GetUserAssignedIdentity',
                request_serializer=services_dot_azr__pb2.GetUserAssignedIdentityRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.GetUserAssignedIdentityResponse.FromString,
                _registered_method=True)
        self.CreateDataDirectoryDeletionTask = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreateDataDirectoryDeletionTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.FromString,
                _registered_method=True)
        self.CreateDataDirectoryCloneTask = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreateDataDirectoryCloneTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.FromString,
                _registered_method=True)
        self.CreateFederatedIdentityCredential = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreateFederatedIdentityCredential',
                request_serializer=services_dot_azr__pb2.CreateFederatedIdentityCredentialRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.CreateFederatedIdentityCredentialResponse.FromString,
                _registered_method=True)
        self.DeleteFederatedIdentityCredential = channel.unary_unary(
                '/services.azr.AzrResourceManager/DeleteFederatedIdentityCredential',
                request_serializer=services_dot_azr__pb2.DeleteFederatedIdentityCredentialRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.DeleteFederatedIdentityCredentialResponse.FromString,
                _registered_method=True)
        self.GetFederatedIdentityCredential = channel.unary_unary(
                '/services.azr.AzrResourceManager/GetFederatedIdentityCredential',
                request_serializer=services_dot_azr__pb2.GetFederatedIdentityCredentialRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.GetFederatedIdentityCredentialResponse.FromString,
                _registered_method=True)
        self.CreateRoleAssignment = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreateRoleAssignment',
                request_serializer=services_dot_azr__pb2.CreateRoleAssignmentRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.CreateRoleAssignmentResponse.FromString,
                _registered_method=True)
        self.DeleteRoleAssignment = channel.unary_unary(
                '/services.azr.AzrResourceManager/DeleteRoleAssignment',
                request_serializer=services_dot_azr__pb2.DeleteRoleAssignmentRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.DeleteRoleAssignmentResponse.FromString,
                _registered_method=True)
        self.GetRoleAssignment = channel.unary_unary(
                '/services.azr.AzrResourceManager/GetRoleAssignment',
                request_serializer=services_dot_azr__pb2.GetRoleAssignmentRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.GetRoleAssignmentResponse.FromString,
                _registered_method=True)
        self.CreatePrivateEndpoint = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreatePrivateEndpoint',
                request_serializer=services_dot_azr__pb2.CreatePrivateEndpointRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.CreatePrivateEndpointResponse.FromString,
                _registered_method=True)
        self.DeletePrivateEndpoint = channel.unary_unary(
                '/services.azr.AzrResourceManager/DeletePrivateEndpoint',
                request_serializer=services_dot_azr__pb2.DeletePrivateEndpointRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.DeletePrivateEndpointResponse.FromString,
                _registered_method=True)
        self.GetPrivateEndpoint = channel.unary_unary(
                '/services.azr.AzrResourceManager/GetPrivateEndpoint',
                request_serializer=services_dot_azr__pb2.GetPrivateEndpointRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.GetPrivateEndpointResponse.FromString,
                _registered_method=True)
        self.CreatePGServer = channel.unary_unary(
                '/services.azr.AzrResourceManager/CreatePGServer',
                request_serializer=services_dot_azr__pb2.CreatePGServerRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.CreatePGServerResponse.FromString,
                _registered_method=True)
        self.DeletePGServer = channel.unary_unary(
                '/services.azr.AzrResourceManager/DeletePGServer',
                request_serializer=services_dot_azr__pb2.DeletePGServerRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.DeletePGServerResponse.FromString,
                _registered_method=True)
        self.StartPGServer = channel.unary_unary(
                '/services.azr.AzrResourceManager/StartPGServer',
                request_serializer=services_dot_azr__pb2.StartPGServerRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.StartPGServerResponse.FromString,
                _registered_method=True)
        self.StopPGServer = channel.unary_unary(
                '/services.azr.AzrResourceManager/StopPGServer',
                request_serializer=services_dot_azr__pb2.StopPGServerRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.StopPGServerResponse.FromString,
                _registered_method=True)
        self.GetPGServer = channel.unary_unary(
                '/services.azr.AzrResourceManager/GetPGServer',
                request_serializer=services_dot_azr__pb2.GetPGServerRequest.SerializeToString,
                response_deserializer=services_dot_azr__pb2.GetPGServerResponse.FromString,
                _registered_method=True)
        self.GetManifest = channel.unary_unary(
                '/services.azr.AzrResourceManager/GetManifest',
                request_serializer=services_dot_common_dot_data__pb2.GetManifestRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.GetManifestResponse.FromString,
                _registered_method=True)


class AzrResourceManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateUserAssignedIdentity(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteUserAssignedIdentity(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetUserAssignedIdentity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDataDirectoryDeletionTask(self, request, context):
        """CreateDataDirectoryDeletionTask creates a task for AZR folder deletion.
        Caller should call the task manager service to manage the created task.
        Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDataDirectoryCloneTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateFederatedIdentityCredential(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteFederatedIdentityCredential(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetFederatedIdentityCredential(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRoleAssignment(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRoleAssignment(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRoleAssignment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePrivateEndpoint(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePrivateEndpoint(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPrivateEndpoint(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePGServer(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePGServer(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartPGServer(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopPGServer(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPGServer(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetManifest(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AzrResourceManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateUserAssignedIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateUserAssignedIdentity,
                    request_deserializer=services_dot_azr__pb2.CreateUserAssignedIdentityRequest.FromString,
                    response_serializer=services_dot_azr__pb2.CreateUserAssignedIdentityResponse.SerializeToString,
            ),
            'DeleteUserAssignedIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteUserAssignedIdentity,
                    request_deserializer=services_dot_azr__pb2.DeleteUserAssignedIdentityRequest.FromString,
                    response_serializer=services_dot_azr__pb2.DeleteUserAssignedIdentityResponse.SerializeToString,
            ),
            'GetUserAssignedIdentity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetUserAssignedIdentity,
                    request_deserializer=services_dot_azr__pb2.GetUserAssignedIdentityRequest.FromString,
                    response_serializer=services_dot_azr__pb2.GetUserAssignedIdentityResponse.SerializeToString,
            ),
            'CreateDataDirectoryDeletionTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataDirectoryDeletionTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.SerializeToString,
            ),
            'CreateDataDirectoryCloneTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataDirectoryCloneTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.SerializeToString,
            ),
            'CreateFederatedIdentityCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateFederatedIdentityCredential,
                    request_deserializer=services_dot_azr__pb2.CreateFederatedIdentityCredentialRequest.FromString,
                    response_serializer=services_dot_azr__pb2.CreateFederatedIdentityCredentialResponse.SerializeToString,
            ),
            'DeleteFederatedIdentityCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteFederatedIdentityCredential,
                    request_deserializer=services_dot_azr__pb2.DeleteFederatedIdentityCredentialRequest.FromString,
                    response_serializer=services_dot_azr__pb2.DeleteFederatedIdentityCredentialResponse.SerializeToString,
            ),
            'GetFederatedIdentityCredential': grpc.unary_unary_rpc_method_handler(
                    servicer.GetFederatedIdentityCredential,
                    request_deserializer=services_dot_azr__pb2.GetFederatedIdentityCredentialRequest.FromString,
                    response_serializer=services_dot_azr__pb2.GetFederatedIdentityCredentialResponse.SerializeToString,
            ),
            'CreateRoleAssignment': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRoleAssignment,
                    request_deserializer=services_dot_azr__pb2.CreateRoleAssignmentRequest.FromString,
                    response_serializer=services_dot_azr__pb2.CreateRoleAssignmentResponse.SerializeToString,
            ),
            'DeleteRoleAssignment': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRoleAssignment,
                    request_deserializer=services_dot_azr__pb2.DeleteRoleAssignmentRequest.FromString,
                    response_serializer=services_dot_azr__pb2.DeleteRoleAssignmentResponse.SerializeToString,
            ),
            'GetRoleAssignment': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRoleAssignment,
                    request_deserializer=services_dot_azr__pb2.GetRoleAssignmentRequest.FromString,
                    response_serializer=services_dot_azr__pb2.GetRoleAssignmentResponse.SerializeToString,
            ),
            'CreatePrivateEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePrivateEndpoint,
                    request_deserializer=services_dot_azr__pb2.CreatePrivateEndpointRequest.FromString,
                    response_serializer=services_dot_azr__pb2.CreatePrivateEndpointResponse.SerializeToString,
            ),
            'DeletePrivateEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePrivateEndpoint,
                    request_deserializer=services_dot_azr__pb2.DeletePrivateEndpointRequest.FromString,
                    response_serializer=services_dot_azr__pb2.DeletePrivateEndpointResponse.SerializeToString,
            ),
            'GetPrivateEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPrivateEndpoint,
                    request_deserializer=services_dot_azr__pb2.GetPrivateEndpointRequest.FromString,
                    response_serializer=services_dot_azr__pb2.GetPrivateEndpointResponse.SerializeToString,
            ),
            'CreatePGServer': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePGServer,
                    request_deserializer=services_dot_azr__pb2.CreatePGServerRequest.FromString,
                    response_serializer=services_dot_azr__pb2.CreatePGServerResponse.SerializeToString,
            ),
            'DeletePGServer': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePGServer,
                    request_deserializer=services_dot_azr__pb2.DeletePGServerRequest.FromString,
                    response_serializer=services_dot_azr__pb2.DeletePGServerResponse.SerializeToString,
            ),
            'StartPGServer': grpc.unary_unary_rpc_method_handler(
                    servicer.StartPGServer,
                    request_deserializer=services_dot_azr__pb2.StartPGServerRequest.FromString,
                    response_serializer=services_dot_azr__pb2.StartPGServerResponse.SerializeToString,
            ),
            'StopPGServer': grpc.unary_unary_rpc_method_handler(
                    servicer.StopPGServer,
                    request_deserializer=services_dot_azr__pb2.StopPGServerRequest.FromString,
                    response_serializer=services_dot_azr__pb2.StopPGServerResponse.SerializeToString,
            ),
            'GetPGServer': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPGServer,
                    request_deserializer=services_dot_azr__pb2.GetPGServerRequest.FromString,
                    response_serializer=services_dot_azr__pb2.GetPGServerResponse.SerializeToString,
            ),
            'GetManifest': grpc.unary_unary_rpc_method_handler(
                    servicer.GetManifest,
                    request_deserializer=services_dot_common_dot_data__pb2.GetManifestRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.GetManifestResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.azr.AzrResourceManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.azr.AzrResourceManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AzrResourceManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateUserAssignedIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreateUserAssignedIdentity',
            services_dot_azr__pb2.CreateUserAssignedIdentityRequest.SerializeToString,
            services_dot_azr__pb2.CreateUserAssignedIdentityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteUserAssignedIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/DeleteUserAssignedIdentity',
            services_dot_azr__pb2.DeleteUserAssignedIdentityRequest.SerializeToString,
            services_dot_azr__pb2.DeleteUserAssignedIdentityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetUserAssignedIdentity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/GetUserAssignedIdentity',
            services_dot_azr__pb2.GetUserAssignedIdentityRequest.SerializeToString,
            services_dot_azr__pb2.GetUserAssignedIdentityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDataDirectoryDeletionTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreateDataDirectoryDeletionTask',
            services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDataDirectoryCloneTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreateDataDirectoryCloneTask',
            services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateFederatedIdentityCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreateFederatedIdentityCredential',
            services_dot_azr__pb2.CreateFederatedIdentityCredentialRequest.SerializeToString,
            services_dot_azr__pb2.CreateFederatedIdentityCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteFederatedIdentityCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/DeleteFederatedIdentityCredential',
            services_dot_azr__pb2.DeleteFederatedIdentityCredentialRequest.SerializeToString,
            services_dot_azr__pb2.DeleteFederatedIdentityCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetFederatedIdentityCredential(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/GetFederatedIdentityCredential',
            services_dot_azr__pb2.GetFederatedIdentityCredentialRequest.SerializeToString,
            services_dot_azr__pb2.GetFederatedIdentityCredentialResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRoleAssignment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreateRoleAssignment',
            services_dot_azr__pb2.CreateRoleAssignmentRequest.SerializeToString,
            services_dot_azr__pb2.CreateRoleAssignmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRoleAssignment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/DeleteRoleAssignment',
            services_dot_azr__pb2.DeleteRoleAssignmentRequest.SerializeToString,
            services_dot_azr__pb2.DeleteRoleAssignmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRoleAssignment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/GetRoleAssignment',
            services_dot_azr__pb2.GetRoleAssignmentRequest.SerializeToString,
            services_dot_azr__pb2.GetRoleAssignmentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePrivateEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreatePrivateEndpoint',
            services_dot_azr__pb2.CreatePrivateEndpointRequest.SerializeToString,
            services_dot_azr__pb2.CreatePrivateEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePrivateEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/DeletePrivateEndpoint',
            services_dot_azr__pb2.DeletePrivateEndpointRequest.SerializeToString,
            services_dot_azr__pb2.DeletePrivateEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPrivateEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/GetPrivateEndpoint',
            services_dot_azr__pb2.GetPrivateEndpointRequest.SerializeToString,
            services_dot_azr__pb2.GetPrivateEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePGServer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/CreatePGServer',
            services_dot_azr__pb2.CreatePGServerRequest.SerializeToString,
            services_dot_azr__pb2.CreatePGServerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePGServer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/DeletePGServer',
            services_dot_azr__pb2.DeletePGServerRequest.SerializeToString,
            services_dot_azr__pb2.DeletePGServerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartPGServer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/StartPGServer',
            services_dot_azr__pb2.StartPGServerRequest.SerializeToString,
            services_dot_azr__pb2.StartPGServerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopPGServer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/StopPGServer',
            services_dot_azr__pb2.StopPGServerRequest.SerializeToString,
            services_dot_azr__pb2.StopPGServerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPGServer(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/GetPGServer',
            services_dot_azr__pb2.GetPGServerRequest.SerializeToString,
            services_dot_azr__pb2.GetPGServerResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetManifest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.azr.AzrResourceManager/GetManifest',
            services_dot_common_dot_data__pb2.GetManifestRequest.SerializeToString,
            services_dot_common_dot_data__pb2.GetManifestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
