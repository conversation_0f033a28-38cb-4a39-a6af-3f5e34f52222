# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import k8s_pb2 as services_dot_k8s__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/k8s_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class K8sResourceManagerStub(object):
    """K8sResourceManager offers APIs for native K8s resoruces management.
    The ID and namespace of all resources, if not specified, needs to follow the
    K8s naming restriction.
    """

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateNamespace = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateNamespace',
                request_serializer=services_dot_k8s__pb2.CreateNamespaceRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateNamespaceResponse.FromString,
                _registered_method=True)
        self.DeleteNamespace = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteNamespace',
                request_serializer=services_dot_k8s__pb2.DeleteNamespaceRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteNamespaceResponse.FromString,
                _registered_method=True)
        self.GetNamespace = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetNamespace',
                request_serializer=services_dot_k8s__pb2.GetNamespaceRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetNamespaceResponse.FromString,
                _registered_method=True)
        self.LabelNamespace = channel.unary_unary(
                '/services.k8s.K8sResourceManager/LabelNamespace',
                request_serializer=services_dot_k8s__pb2.LabelNamespaceRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.LabelNamespaceResponse.FromString,
                _registered_method=True)
        self.CreateServiceAccount = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateServiceAccount',
                request_serializer=services_dot_k8s__pb2.CreateServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateServiceAccountResponse.FromString,
                _registered_method=True)
        self.DeleteServiceAccount = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteServiceAccount',
                request_serializer=services_dot_k8s__pb2.DeleteServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteServiceAccountResponse.FromString,
                _registered_method=True)
        self.GetServiceAccount = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetServiceAccount',
                request_serializer=services_dot_k8s__pb2.GetServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetServiceAccountResponse.FromString,
                _registered_method=True)
        self.AnnotateServiceAccount = channel.unary_unary(
                '/services.k8s.K8sResourceManager/AnnotateServiceAccount',
                request_serializer=services_dot_k8s__pb2.AnnotateServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.AnnotateServiceAccountResponse.FromString,
                _registered_method=True)
        self.CreateConfigMap = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateConfigMap',
                request_serializer=services_dot_k8s__pb2.CreateConfigMapRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateConfigMapResponse.FromString,
                _registered_method=True)
        self.DeleteConfigMap = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteConfigMap',
                request_serializer=services_dot_k8s__pb2.DeleteConfigMapRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteConfigMapResponse.FromString,
                _registered_method=True)
        self.GetConfigMap = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetConfigMap',
                request_serializer=services_dot_k8s__pb2.GetConfigMapRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetConfigMapResponse.FromString,
                _registered_method=True)
        self.UpdateConfigMap = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateConfigMap',
                request_serializer=services_dot_k8s__pb2.UpdateConfigMapRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateConfigMapResponse.FromString,
                _registered_method=True)
        self.CreateSecret = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateSecret',
                request_serializer=services_dot_k8s__pb2.CreateSecretRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateSecretResponse.FromString,
                _registered_method=True)
        self.DeleteSecret = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteSecret',
                request_serializer=services_dot_k8s__pb2.DeleteSecretRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteSecretResponse.FromString,
                _registered_method=True)
        self.GetSecret = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetSecret',
                request_serializer=services_dot_k8s__pb2.GetSecretRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetSecretResponse.FromString,
                _registered_method=True)
        self.UpdateSecret = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateSecret',
                request_serializer=services_dot_k8s__pb2.UpdateSecretRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateSecretResponse.FromString,
                _registered_method=True)
        self.CreateRisingWave = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateRisingWave',
                request_serializer=services_dot_k8s__pb2.CreateRisingWaveRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateRisingWaveResponse.FromString,
                _registered_method=True)
        self.DeleteRisingWave = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteRisingWave',
                request_serializer=services_dot_k8s__pb2.DeleteRisingWaveRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteRisingWaveResponse.FromString,
                _registered_method=True)
        self.GetRisingWave = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetRisingWave',
                request_serializer=services_dot_k8s__pb2.GetRisingWaveRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetRisingWaveResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveImage = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveImage',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveImageRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveImageResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveLicenseKey = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveLicenseKey',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveLicenseKeyRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveLicenseKeyResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveSecretStore = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveSecretStore',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveSecretStoreRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveSecretStoreResponse.FromString,
                _registered_method=True)
        self.ScaleRisingWave = channel.unary_unary(
                '/services.k8s.K8sResourceManager/ScaleRisingWave',
                request_serializer=services_dot_k8s__pb2.ScaleRisingWaveRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.ScaleRisingWaveResponse.FromString,
                _registered_method=True)
        self.ScaleRisingWaveOneOf = channel.unary_unary(
                '/services.k8s.K8sResourceManager/ScaleRisingWaveOneOf',
                request_serializer=services_dot_k8s__pb2.ScaleRisingWaveRequestOneOf.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.ScaleRisingWaveResponse.FromString,
                _registered_method=True)
        self.StartRisingWave = channel.unary_unary(
                '/services.k8s.K8sResourceManager/StartRisingWave',
                request_serializer=services_dot_k8s__pb2.StartRisingWaveRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.StartRisingWaveResponse.FromString,
                _registered_method=True)
        self.StopRisingWave = channel.unary_unary(
                '/services.k8s.K8sResourceManager/StopRisingWave',
                request_serializer=services_dot_k8s__pb2.StopRisingWaveRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.StopRisingWaveResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveComponents = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveComponents',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveComponentsRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveComponentsResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveMetaStore = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveMetaStore',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveMetaStoreRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveMetaStoreResponse.FromString,
                _registered_method=True)
        self.PutRisingWaveEnvVar = channel.unary_unary(
                '/services.k8s.K8sResourceManager/PutRisingWaveEnvVar',
                request_serializer=services_dot_k8s__pb2.PutRisingWaveEnvRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.PutRisingWaveEnvResponse.FromString,
                _registered_method=True)
        self.DeleteRisingWaveEnvVar = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteRisingWaveEnvVar',
                request_serializer=services_dot_k8s__pb2.DeleteRisingWaveEnvRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteRisingWaveEnvResponse.FromString,
                _registered_method=True)
        self.CreateRisingWaveComputeNodeGroup = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateRisingWaveComputeNodeGroup',
                request_serializer=services_dot_k8s__pb2.CreateRisingWaveComputeNodeGroupRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateRisingWaveComputeNodeGroupResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveComputeNodeGroup = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveComputeNodeGroup',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveComputeNodeGroupRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveComputeNodeGroupResponse.FromString,
                _registered_method=True)
        self.DeleteRisingWaveComputeNodeGroup = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteRisingWaveComputeNodeGroup',
                request_serializer=services_dot_k8s__pb2.DeleteRisingWaveComputeNodeGroupRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteRisingWaveComputeNodeGroupResponse.FromString,
                _registered_method=True)
        self.CreateRisingWaveNodeGroup = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateRisingWaveNodeGroup',
                request_serializer=services_dot_k8s__pb2.CreateRisingWaveNodeGroupRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateRisingWaveNodeGroupResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveNodeGroup = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroup',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupResponse.FromString,
                _registered_method=True)
        self.DeleteRisingWaveNodeGroup = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteRisingWaveNodeGroup',
                request_serializer=services_dot_k8s__pb2.DeleteRisingWaveNodeGroupRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteRisingWaveNodeGroupResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveNodeGroupConfiguration = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroupConfiguration',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupConfigurationRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupConfigurationResponse.FromString,
                _registered_method=True)
        self.UpdateRisingWaveNodeGroupRestartAt = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroupRestartAt',
                request_serializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRestartAtRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRestartAtResponse.FromString,
                _registered_method=True)
        self.DeletePersistentVolumeClaims = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeletePersistentVolumeClaims',
                request_serializer=services_dot_k8s__pb2.DeletePersistentVolumeClaimsRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeletePersistentVolumeClaimsResponse.FromString,
                _registered_method=True)
        self.GetPersistentVolumeClaims = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetPersistentVolumeClaims',
                request_serializer=services_dot_k8s__pb2.GetPersistentVolumeClaimsRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetPersistentVolumeClaimsResponse.FromString,
                _registered_method=True)
        self.CreatePersistentVolumeClaim = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreatePersistentVolumeClaim',
                request_serializer=services_dot_k8s__pb2.CreatePersistentVolumeClaimRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreatePersistentVolumeClaimResponse.FromString,
                _registered_method=True)
        self.GetHelmRelease = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetHelmRelease',
                request_serializer=services_dot_k8s__pb2.GetHelmReleaseRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetHelmReleaseResponse.FromString,
                _registered_method=True)
        self.InstallHelmRelease = channel.unary_unary(
                '/services.k8s.K8sResourceManager/InstallHelmRelease',
                request_serializer=services_dot_k8s__pb2.InstallHelmReleaseRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.InstallHelmReleaseResponse.FromString,
                _registered_method=True)
        self.UpgradeHelmRelease = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpgradeHelmRelease',
                request_serializer=services_dot_k8s__pb2.UpgradeHelmReleaseRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpgradeHelmReleaseResponse.FromString,
                _registered_method=True)
        self.UninstallHelmRelease = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UninstallHelmRelease',
                request_serializer=services_dot_k8s__pb2.UninstallHelmReleaseRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UninstallHelmReleaseResponse.FromString,
                _registered_method=True)
        self.GetPodPhases = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetPodPhases',
                request_serializer=services_dot_k8s__pb2.GetPodPhasesRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetPodPhasesResponse.FromString,
                _registered_method=True)
        self.RestartStatefulSet = channel.unary_unary(
                '/services.k8s.K8sResourceManager/RestartStatefulSet',
                request_serializer=services_dot_k8s__pb2.RestartStatefulSetRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.RestartStatefulSetResponse.FromString,
                _registered_method=True)
        self.GetStatefulSetReplicasStatus = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetStatefulSetReplicasStatus',
                request_serializer=services_dot_k8s__pb2.GetStatefulSetReplicasStatusRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetStatefulSetReplicasStatusResponse.FromString,
                _registered_method=True)
        self.GetDeploymentReplicasStatus = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetDeploymentReplicasStatus',
                request_serializer=services_dot_k8s__pb2.GetDeploymentReplicasStatusRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetDeploymentReplicasStatusResponse.FromString,
                _registered_method=True)
        self.RestartDeployment = channel.unary_unary(
                '/services.k8s.K8sResourceManager/RestartDeployment',
                request_serializer=services_dot_k8s__pb2.RestartDeploymentRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.RestartDeploymentResponse.FromString,
                _registered_method=True)
        self.CreateServiceMonitor = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateServiceMonitor',
                request_serializer=services_dot_k8s__pb2.CreateServiceMonitorRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateServiceMonitorResponse.FromString,
                _registered_method=True)
        self.DeleteServiceMonitor = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteServiceMonitor',
                request_serializer=services_dot_k8s__pb2.DeleteServiceMonitorRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteServiceMonitorResponse.FromString,
                _registered_method=True)
        self.CreateAzureServiceMonitor = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateAzureServiceMonitor',
                request_serializer=services_dot_k8s__pb2.CreateServiceMonitorRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateServiceMonitorResponse.FromString,
                _registered_method=True)
        self.DeleteAzureServiceMonitor = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteAzureServiceMonitor',
                request_serializer=services_dot_k8s__pb2.DeleteServiceMonitorRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteServiceMonitorResponse.FromString,
                _registered_method=True)
        self.CreatePodMonitoring = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreatePodMonitoring',
                request_serializer=services_dot_k8s__pb2.CreatePodMonitoringRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreatePodMonitoringResponse.FromString,
                _registered_method=True)
        self.DeletePodMonitoring = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeletePodMonitoring',
                request_serializer=services_dot_k8s__pb2.DeletePodMonitoringRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeletePodMonitoringResponse.FromString,
                _registered_method=True)
        self.CreateService = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateService',
                request_serializer=services_dot_k8s__pb2.CreateServiceRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateServiceResponse.FromString,
                _registered_method=True)
        self.CreateNetworkPolicy = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateNetworkPolicy',
                request_serializer=services_dot_k8s__pb2.CreateNetworkPolicyRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateNetworkPolicyResponse.FromString,
                _registered_method=True)
        self.CreateOrUpdateNetworkPolicy = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreateOrUpdateNetworkPolicy',
                request_serializer=services_dot_k8s__pb2.CreateOrUpdateNetworkPolicyRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreateOrUpdateNetworkPolicyResponse.FromString,
                _registered_method=True)
        self.DeleteNetworkPolicy = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeleteNetworkPolicy',
                request_serializer=services_dot_k8s__pb2.DeleteNetworkPolicyRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeleteNetworkPolicyResponse.FromString,
                _registered_method=True)
        self.CreatePostgreSql = channel.unary_unary(
                '/services.k8s.K8sResourceManager/CreatePostgreSql',
                request_serializer=services_dot_k8s__pb2.CreatePostgreSqlRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.CreatePostgreSqlResponse.FromString,
                _registered_method=True)
        self.DeletePostgreSql = channel.unary_unary(
                '/services.k8s.K8sResourceManager/DeletePostgreSql',
                request_serializer=services_dot_k8s__pb2.DeletePostgreSqlRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.DeletePostgreSqlResponse.FromString,
                _registered_method=True)
        self.UpdatePostgreSql = channel.unary_unary(
                '/services.k8s.K8sResourceManager/UpdatePostgreSql',
                request_serializer=services_dot_k8s__pb2.UpdatePostgreSqlRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.UpdatePostgreSqlResponse.FromString,
                _registered_method=True)
        self.GetPostgreSql = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetPostgreSql',
                request_serializer=services_dot_k8s__pb2.GetPostgreSqlRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetPostgreSqlResponse.FromString,
                _registered_method=True)
        self.GetPods = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetPods',
                request_serializer=services_dot_k8s__pb2.GetPodsRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetPodsResponse.FromString,
                _registered_method=True)
        self.GetClusterAccess = channel.unary_unary(
                '/services.k8s.K8sResourceManager/GetClusterAccess',
                request_serializer=services_dot_k8s__pb2.GetClusterAccessRequest.SerializeToString,
                response_deserializer=services_dot_k8s__pb2.GetClusterAccessResponse.FromString,
                _registered_method=True)


class K8sResourceManagerServicer(object):
    """K8sResourceManager offers APIs for native K8s resoruces management.
    The ID and namespace of all resources, if not specified, needs to follow the
    K8s naming restriction.
    """

    def CreateNamespace(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteNamespace(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNamespace(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LabelNamespace(self, request, context):
        """LabelNamespace add labels to a specified namespace. If the key already
        exists, new value will override the existing one.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateServiceAccount(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteServiceAccount(self, request, context):
        """Expected a DELETED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetServiceAccount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AnnotateServiceAccount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateConfigMap(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteConfigMap(self, request, context):
        """Expected a DELETED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetConfigMap(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateConfigMap(self, request, context):
        """UpdateConfigMap generats a JSON merge patch that converts `from` to `to`
        and apply the generated patch to the runtime object.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateSecret(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSecret(self, request, context):
        """Expected a DELETED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSecret(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateSecret(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRisingWave(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRisingWave(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRisingWave(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveImage(self, request, context):
        """UpdateRisingWave generats a JSON merge patch that converts `from` to `to`
        and apply the generated patch to the runtime RW object.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveLicenseKey(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveSecretStore(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ScaleRisingWave(self, request, context):
        """Deprecated. Please use ScaleRisingWaveOneOf
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ScaleRisingWaveOneOf(self, request, context):
        """Scale the replica of `default` node group
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartRisingWave(self, request, context):
        """StartRisingWave scale all node groups to previous replica
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopRisingWave(self, request, context):
        """StopRisingWave scale all node groups to 0, and record the current replica
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveComponents(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveMetaStore(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PutRisingWaveEnvVar(self, request, context):
        """Add one or more environment variable to zero or more components.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRisingWaveEnvVar(self, request, context):
        """Removes one or more environment variable from zero or more components.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRisingWaveComputeNodeGroup(self, request, context):
        """Deprecated. Please use CreateRisingWaveNodeGroup.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveComputeNodeGroup(self, request, context):
        """Deprecated. Please use UpdateRisingWaveNodeGroup.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRisingWaveComputeNodeGroup(self, request, context):
        """Deprecated. Please use DeleteRisingWaveNodeGroup.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRisingWaveNodeGroup(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveNodeGroup(self, request, context):
        """Expected a UPDATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteRisingWaveNodeGroup(self, request, context):
        """Expected a DELETED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveNodeGroupConfiguration(self, request, context):
        """Expected a UPDATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateRisingWaveNodeGroupRestartAt(self, request, context):
        """Expected a UPDATED status on success. Setting any value for this field will immediately trigger nodes restart.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePersistentVolumeClaims(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPersistentVolumeClaims(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePersistentVolumeClaim(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetHelmRelease(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def InstallHelmRelease(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpgradeHelmRelease(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UninstallHelmRelease(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPodPhases(self, request, context):
        """GetPodPhases retrieves all pods' phases in a namespace. The resource id
        will be a k8s namesapce and the resource namespace is omitted.
        Returns a map from pod name to pod phase.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestartStatefulSet(self, request, context):
        """Expected a READY status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetStatefulSetReplicasStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDeploymentReplicasStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestartDeployment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateServiceMonitor(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteServiceMonitor(self, request, context):
        """Expect a DELETED status on success
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateAzureServiceMonitor(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteAzureServiceMonitor(self, request, context):
        """Expect a DELETED status on success
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePodMonitoring(self, request, context):
        """Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePodMonitoring(self, request, context):
        """Expect a DELETED status on success
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateService(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateNetworkPolicy(self, request, context):
        """Expect a CREATED status on success
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateOrUpdateNetworkPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteNetworkPolicy(self, request, context):
        """Expect a DELETED status on success
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePostgreSql(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePostgreSql(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdatePostgreSql(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPostgreSql(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPods(self, request, context):
        """GetPods retrieves all pods in a namespace. The resource id
        will be a k8s namesapce and the resource namespace is omitted.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetClusterAccess(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_K8sResourceManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateNamespace': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateNamespace,
                    request_deserializer=services_dot_k8s__pb2.CreateNamespaceRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateNamespaceResponse.SerializeToString,
            ),
            'DeleteNamespace': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteNamespace,
                    request_deserializer=services_dot_k8s__pb2.DeleteNamespaceRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteNamespaceResponse.SerializeToString,
            ),
            'GetNamespace': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNamespace,
                    request_deserializer=services_dot_k8s__pb2.GetNamespaceRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetNamespaceResponse.SerializeToString,
            ),
            'LabelNamespace': grpc.unary_unary_rpc_method_handler(
                    servicer.LabelNamespace,
                    request_deserializer=services_dot_k8s__pb2.LabelNamespaceRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.LabelNamespaceResponse.SerializeToString,
            ),
            'CreateServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateServiceAccount,
                    request_deserializer=services_dot_k8s__pb2.CreateServiceAccountRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateServiceAccountResponse.SerializeToString,
            ),
            'DeleteServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteServiceAccount,
                    request_deserializer=services_dot_k8s__pb2.DeleteServiceAccountRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteServiceAccountResponse.SerializeToString,
            ),
            'GetServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetServiceAccount,
                    request_deserializer=services_dot_k8s__pb2.GetServiceAccountRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetServiceAccountResponse.SerializeToString,
            ),
            'AnnotateServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.AnnotateServiceAccount,
                    request_deserializer=services_dot_k8s__pb2.AnnotateServiceAccountRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.AnnotateServiceAccountResponse.SerializeToString,
            ),
            'CreateConfigMap': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateConfigMap,
                    request_deserializer=services_dot_k8s__pb2.CreateConfigMapRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateConfigMapResponse.SerializeToString,
            ),
            'DeleteConfigMap': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteConfigMap,
                    request_deserializer=services_dot_k8s__pb2.DeleteConfigMapRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteConfigMapResponse.SerializeToString,
            ),
            'GetConfigMap': grpc.unary_unary_rpc_method_handler(
                    servicer.GetConfigMap,
                    request_deserializer=services_dot_k8s__pb2.GetConfigMapRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetConfigMapResponse.SerializeToString,
            ),
            'UpdateConfigMap': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateConfigMap,
                    request_deserializer=services_dot_k8s__pb2.UpdateConfigMapRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateConfigMapResponse.SerializeToString,
            ),
            'CreateSecret': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateSecret,
                    request_deserializer=services_dot_k8s__pb2.CreateSecretRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateSecretResponse.SerializeToString,
            ),
            'DeleteSecret': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSecret,
                    request_deserializer=services_dot_k8s__pb2.DeleteSecretRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteSecretResponse.SerializeToString,
            ),
            'GetSecret': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSecret,
                    request_deserializer=services_dot_k8s__pb2.GetSecretRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetSecretResponse.SerializeToString,
            ),
            'UpdateSecret': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateSecret,
                    request_deserializer=services_dot_k8s__pb2.UpdateSecretRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateSecretResponse.SerializeToString,
            ),
            'CreateRisingWave': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRisingWave,
                    request_deserializer=services_dot_k8s__pb2.CreateRisingWaveRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateRisingWaveResponse.SerializeToString,
            ),
            'DeleteRisingWave': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRisingWave,
                    request_deserializer=services_dot_k8s__pb2.DeleteRisingWaveRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteRisingWaveResponse.SerializeToString,
            ),
            'GetRisingWave': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRisingWave,
                    request_deserializer=services_dot_k8s__pb2.GetRisingWaveRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetRisingWaveResponse.SerializeToString,
            ),
            'UpdateRisingWaveImage': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveImage,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveImageRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveImageResponse.SerializeToString,
            ),
            'UpdateRisingWaveLicenseKey': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveLicenseKey,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveLicenseKeyRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveLicenseKeyResponse.SerializeToString,
            ),
            'UpdateRisingWaveSecretStore': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveSecretStore,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveSecretStoreRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveSecretStoreResponse.SerializeToString,
            ),
            'ScaleRisingWave': grpc.unary_unary_rpc_method_handler(
                    servicer.ScaleRisingWave,
                    request_deserializer=services_dot_k8s__pb2.ScaleRisingWaveRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.ScaleRisingWaveResponse.SerializeToString,
            ),
            'ScaleRisingWaveOneOf': grpc.unary_unary_rpc_method_handler(
                    servicer.ScaleRisingWaveOneOf,
                    request_deserializer=services_dot_k8s__pb2.ScaleRisingWaveRequestOneOf.FromString,
                    response_serializer=services_dot_k8s__pb2.ScaleRisingWaveResponse.SerializeToString,
            ),
            'StartRisingWave': grpc.unary_unary_rpc_method_handler(
                    servicer.StartRisingWave,
                    request_deserializer=services_dot_k8s__pb2.StartRisingWaveRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.StartRisingWaveResponse.SerializeToString,
            ),
            'StopRisingWave': grpc.unary_unary_rpc_method_handler(
                    servicer.StopRisingWave,
                    request_deserializer=services_dot_k8s__pb2.StopRisingWaveRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.StopRisingWaveResponse.SerializeToString,
            ),
            'UpdateRisingWaveComponents': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveComponents,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveComponentsRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveComponentsResponse.SerializeToString,
            ),
            'UpdateRisingWaveMetaStore': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveMetaStore,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveMetaStoreRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveMetaStoreResponse.SerializeToString,
            ),
            'PutRisingWaveEnvVar': grpc.unary_unary_rpc_method_handler(
                    servicer.PutRisingWaveEnvVar,
                    request_deserializer=services_dot_k8s__pb2.PutRisingWaveEnvRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.PutRisingWaveEnvResponse.SerializeToString,
            ),
            'DeleteRisingWaveEnvVar': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRisingWaveEnvVar,
                    request_deserializer=services_dot_k8s__pb2.DeleteRisingWaveEnvRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteRisingWaveEnvResponse.SerializeToString,
            ),
            'CreateRisingWaveComputeNodeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRisingWaveComputeNodeGroup,
                    request_deserializer=services_dot_k8s__pb2.CreateRisingWaveComputeNodeGroupRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateRisingWaveComputeNodeGroupResponse.SerializeToString,
            ),
            'UpdateRisingWaveComputeNodeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveComputeNodeGroup,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveComputeNodeGroupRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveComputeNodeGroupResponse.SerializeToString,
            ),
            'DeleteRisingWaveComputeNodeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRisingWaveComputeNodeGroup,
                    request_deserializer=services_dot_k8s__pb2.DeleteRisingWaveComputeNodeGroupRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteRisingWaveComputeNodeGroupResponse.SerializeToString,
            ),
            'CreateRisingWaveNodeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRisingWaveNodeGroup,
                    request_deserializer=services_dot_k8s__pb2.CreateRisingWaveNodeGroupRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateRisingWaveNodeGroupResponse.SerializeToString,
            ),
            'UpdateRisingWaveNodeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveNodeGroup,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupResponse.SerializeToString,
            ),
            'DeleteRisingWaveNodeGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteRisingWaveNodeGroup,
                    request_deserializer=services_dot_k8s__pb2.DeleteRisingWaveNodeGroupRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteRisingWaveNodeGroupResponse.SerializeToString,
            ),
            'UpdateRisingWaveNodeGroupConfiguration': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveNodeGroupConfiguration,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupConfigurationRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupConfigurationResponse.SerializeToString,
            ),
            'UpdateRisingWaveNodeGroupRestartAt': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateRisingWaveNodeGroupRestartAt,
                    request_deserializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRestartAtRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRestartAtResponse.SerializeToString,
            ),
            'DeletePersistentVolumeClaims': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePersistentVolumeClaims,
                    request_deserializer=services_dot_k8s__pb2.DeletePersistentVolumeClaimsRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeletePersistentVolumeClaimsResponse.SerializeToString,
            ),
            'GetPersistentVolumeClaims': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPersistentVolumeClaims,
                    request_deserializer=services_dot_k8s__pb2.GetPersistentVolumeClaimsRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetPersistentVolumeClaimsResponse.SerializeToString,
            ),
            'CreatePersistentVolumeClaim': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePersistentVolumeClaim,
                    request_deserializer=services_dot_k8s__pb2.CreatePersistentVolumeClaimRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreatePersistentVolumeClaimResponse.SerializeToString,
            ),
            'GetHelmRelease': grpc.unary_unary_rpc_method_handler(
                    servicer.GetHelmRelease,
                    request_deserializer=services_dot_k8s__pb2.GetHelmReleaseRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetHelmReleaseResponse.SerializeToString,
            ),
            'InstallHelmRelease': grpc.unary_unary_rpc_method_handler(
                    servicer.InstallHelmRelease,
                    request_deserializer=services_dot_k8s__pb2.InstallHelmReleaseRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.InstallHelmReleaseResponse.SerializeToString,
            ),
            'UpgradeHelmRelease': grpc.unary_unary_rpc_method_handler(
                    servicer.UpgradeHelmRelease,
                    request_deserializer=services_dot_k8s__pb2.UpgradeHelmReleaseRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpgradeHelmReleaseResponse.SerializeToString,
            ),
            'UninstallHelmRelease': grpc.unary_unary_rpc_method_handler(
                    servicer.UninstallHelmRelease,
                    request_deserializer=services_dot_k8s__pb2.UninstallHelmReleaseRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UninstallHelmReleaseResponse.SerializeToString,
            ),
            'GetPodPhases': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPodPhases,
                    request_deserializer=services_dot_k8s__pb2.GetPodPhasesRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetPodPhasesResponse.SerializeToString,
            ),
            'RestartStatefulSet': grpc.unary_unary_rpc_method_handler(
                    servicer.RestartStatefulSet,
                    request_deserializer=services_dot_k8s__pb2.RestartStatefulSetRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.RestartStatefulSetResponse.SerializeToString,
            ),
            'GetStatefulSetReplicasStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetStatefulSetReplicasStatus,
                    request_deserializer=services_dot_k8s__pb2.GetStatefulSetReplicasStatusRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetStatefulSetReplicasStatusResponse.SerializeToString,
            ),
            'GetDeploymentReplicasStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDeploymentReplicasStatus,
                    request_deserializer=services_dot_k8s__pb2.GetDeploymentReplicasStatusRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetDeploymentReplicasStatusResponse.SerializeToString,
            ),
            'RestartDeployment': grpc.unary_unary_rpc_method_handler(
                    servicer.RestartDeployment,
                    request_deserializer=services_dot_k8s__pb2.RestartDeploymentRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.RestartDeploymentResponse.SerializeToString,
            ),
            'CreateServiceMonitor': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateServiceMonitor,
                    request_deserializer=services_dot_k8s__pb2.CreateServiceMonitorRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateServiceMonitorResponse.SerializeToString,
            ),
            'DeleteServiceMonitor': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteServiceMonitor,
                    request_deserializer=services_dot_k8s__pb2.DeleteServiceMonitorRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteServiceMonitorResponse.SerializeToString,
            ),
            'CreateAzureServiceMonitor': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateAzureServiceMonitor,
                    request_deserializer=services_dot_k8s__pb2.CreateServiceMonitorRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateServiceMonitorResponse.SerializeToString,
            ),
            'DeleteAzureServiceMonitor': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteAzureServiceMonitor,
                    request_deserializer=services_dot_k8s__pb2.DeleteServiceMonitorRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteServiceMonitorResponse.SerializeToString,
            ),
            'CreatePodMonitoring': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePodMonitoring,
                    request_deserializer=services_dot_k8s__pb2.CreatePodMonitoringRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreatePodMonitoringResponse.SerializeToString,
            ),
            'DeletePodMonitoring': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePodMonitoring,
                    request_deserializer=services_dot_k8s__pb2.DeletePodMonitoringRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeletePodMonitoringResponse.SerializeToString,
            ),
            'CreateService': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateService,
                    request_deserializer=services_dot_k8s__pb2.CreateServiceRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateServiceResponse.SerializeToString,
            ),
            'CreateNetworkPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateNetworkPolicy,
                    request_deserializer=services_dot_k8s__pb2.CreateNetworkPolicyRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateNetworkPolicyResponse.SerializeToString,
            ),
            'CreateOrUpdateNetworkPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateOrUpdateNetworkPolicy,
                    request_deserializer=services_dot_k8s__pb2.CreateOrUpdateNetworkPolicyRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreateOrUpdateNetworkPolicyResponse.SerializeToString,
            ),
            'DeleteNetworkPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteNetworkPolicy,
                    request_deserializer=services_dot_k8s__pb2.DeleteNetworkPolicyRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeleteNetworkPolicyResponse.SerializeToString,
            ),
            'CreatePostgreSql': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePostgreSql,
                    request_deserializer=services_dot_k8s__pb2.CreatePostgreSqlRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.CreatePostgreSqlResponse.SerializeToString,
            ),
            'DeletePostgreSql': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePostgreSql,
                    request_deserializer=services_dot_k8s__pb2.DeletePostgreSqlRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.DeletePostgreSqlResponse.SerializeToString,
            ),
            'UpdatePostgreSql': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdatePostgreSql,
                    request_deserializer=services_dot_k8s__pb2.UpdatePostgreSqlRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.UpdatePostgreSqlResponse.SerializeToString,
            ),
            'GetPostgreSql': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPostgreSql,
                    request_deserializer=services_dot_k8s__pb2.GetPostgreSqlRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetPostgreSqlResponse.SerializeToString,
            ),
            'GetPods': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPods,
                    request_deserializer=services_dot_k8s__pb2.GetPodsRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetPodsResponse.SerializeToString,
            ),
            'GetClusterAccess': grpc.unary_unary_rpc_method_handler(
                    servicer.GetClusterAccess,
                    request_deserializer=services_dot_k8s__pb2.GetClusterAccessRequest.FromString,
                    response_serializer=services_dot_k8s__pb2.GetClusterAccessResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.k8s.K8sResourceManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.k8s.K8sResourceManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class K8sResourceManager(object):
    """K8sResourceManager offers APIs for native K8s resoruces management.
    The ID and namespace of all resources, if not specified, needs to follow the
    K8s naming restriction.
    """

    @staticmethod
    def CreateNamespace(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateNamespace',
            services_dot_k8s__pb2.CreateNamespaceRequest.SerializeToString,
            services_dot_k8s__pb2.CreateNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteNamespace(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteNamespace',
            services_dot_k8s__pb2.DeleteNamespaceRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetNamespace(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetNamespace',
            services_dot_k8s__pb2.GetNamespaceRequest.SerializeToString,
            services_dot_k8s__pb2.GetNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def LabelNamespace(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/LabelNamespace',
            services_dot_k8s__pb2.LabelNamespaceRequest.SerializeToString,
            services_dot_k8s__pb2.LabelNamespaceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateServiceAccount',
            services_dot_k8s__pb2.CreateServiceAccountRequest.SerializeToString,
            services_dot_k8s__pb2.CreateServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteServiceAccount',
            services_dot_k8s__pb2.DeleteServiceAccountRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetServiceAccount',
            services_dot_k8s__pb2.GetServiceAccountRequest.SerializeToString,
            services_dot_k8s__pb2.GetServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def AnnotateServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/AnnotateServiceAccount',
            services_dot_k8s__pb2.AnnotateServiceAccountRequest.SerializeToString,
            services_dot_k8s__pb2.AnnotateServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateConfigMap(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateConfigMap',
            services_dot_k8s__pb2.CreateConfigMapRequest.SerializeToString,
            services_dot_k8s__pb2.CreateConfigMapResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteConfigMap(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteConfigMap',
            services_dot_k8s__pb2.DeleteConfigMapRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteConfigMapResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetConfigMap(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetConfigMap',
            services_dot_k8s__pb2.GetConfigMapRequest.SerializeToString,
            services_dot_k8s__pb2.GetConfigMapResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateConfigMap(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateConfigMap',
            services_dot_k8s__pb2.UpdateConfigMapRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateConfigMapResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateSecret(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateSecret',
            services_dot_k8s__pb2.CreateSecretRequest.SerializeToString,
            services_dot_k8s__pb2.CreateSecretResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSecret(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteSecret',
            services_dot_k8s__pb2.DeleteSecretRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteSecretResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSecret(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetSecret',
            services_dot_k8s__pb2.GetSecretRequest.SerializeToString,
            services_dot_k8s__pb2.GetSecretResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateSecret(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateSecret',
            services_dot_k8s__pb2.UpdateSecretRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateSecretResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRisingWave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateRisingWave',
            services_dot_k8s__pb2.CreateRisingWaveRequest.SerializeToString,
            services_dot_k8s__pb2.CreateRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRisingWave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteRisingWave',
            services_dot_k8s__pb2.DeleteRisingWaveRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetRisingWave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetRisingWave',
            services_dot_k8s__pb2.GetRisingWaveRequest.SerializeToString,
            services_dot_k8s__pb2.GetRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveImage',
            services_dot_k8s__pb2.UpdateRisingWaveImageRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveImageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveLicenseKey(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveLicenseKey',
            services_dot_k8s__pb2.UpdateRisingWaveLicenseKeyRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveLicenseKeyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveSecretStore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveSecretStore',
            services_dot_k8s__pb2.UpdateRisingWaveSecretStoreRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveSecretStoreResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ScaleRisingWave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/ScaleRisingWave',
            services_dot_k8s__pb2.ScaleRisingWaveRequest.SerializeToString,
            services_dot_k8s__pb2.ScaleRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ScaleRisingWaveOneOf(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/ScaleRisingWaveOneOf',
            services_dot_k8s__pb2.ScaleRisingWaveRequestOneOf.SerializeToString,
            services_dot_k8s__pb2.ScaleRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartRisingWave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/StartRisingWave',
            services_dot_k8s__pb2.StartRisingWaveRequest.SerializeToString,
            services_dot_k8s__pb2.StartRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopRisingWave(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/StopRisingWave',
            services_dot_k8s__pb2.StopRisingWaveRequest.SerializeToString,
            services_dot_k8s__pb2.StopRisingWaveResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveComponents(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveComponents',
            services_dot_k8s__pb2.UpdateRisingWaveComponentsRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveComponentsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveMetaStore(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveMetaStore',
            services_dot_k8s__pb2.UpdateRisingWaveMetaStoreRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveMetaStoreResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def PutRisingWaveEnvVar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/PutRisingWaveEnvVar',
            services_dot_k8s__pb2.PutRisingWaveEnvRequest.SerializeToString,
            services_dot_k8s__pb2.PutRisingWaveEnvResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRisingWaveEnvVar(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteRisingWaveEnvVar',
            services_dot_k8s__pb2.DeleteRisingWaveEnvRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteRisingWaveEnvResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRisingWaveComputeNodeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateRisingWaveComputeNodeGroup',
            services_dot_k8s__pb2.CreateRisingWaveComputeNodeGroupRequest.SerializeToString,
            services_dot_k8s__pb2.CreateRisingWaveComputeNodeGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveComputeNodeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveComputeNodeGroup',
            services_dot_k8s__pb2.UpdateRisingWaveComputeNodeGroupRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveComputeNodeGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRisingWaveComputeNodeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteRisingWaveComputeNodeGroup',
            services_dot_k8s__pb2.DeleteRisingWaveComputeNodeGroupRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteRisingWaveComputeNodeGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRisingWaveNodeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateRisingWaveNodeGroup',
            services_dot_k8s__pb2.CreateRisingWaveNodeGroupRequest.SerializeToString,
            services_dot_k8s__pb2.CreateRisingWaveNodeGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveNodeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroup',
            services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveNodeGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteRisingWaveNodeGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteRisingWaveNodeGroup',
            services_dot_k8s__pb2.DeleteRisingWaveNodeGroupRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteRisingWaveNodeGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveNodeGroupConfiguration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroupConfiguration',
            services_dot_k8s__pb2.UpdateRisingWaveNodeGroupConfigurationRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveNodeGroupConfigurationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateRisingWaveNodeGroupRestartAt(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdateRisingWaveNodeGroupRestartAt',
            services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRestartAtRequest.SerializeToString,
            services_dot_k8s__pb2.UpdateRisingWaveNodeGroupRestartAtResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePersistentVolumeClaims(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeletePersistentVolumeClaims',
            services_dot_k8s__pb2.DeletePersistentVolumeClaimsRequest.SerializeToString,
            services_dot_k8s__pb2.DeletePersistentVolumeClaimsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPersistentVolumeClaims(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetPersistentVolumeClaims',
            services_dot_k8s__pb2.GetPersistentVolumeClaimsRequest.SerializeToString,
            services_dot_k8s__pb2.GetPersistentVolumeClaimsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePersistentVolumeClaim(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreatePersistentVolumeClaim',
            services_dot_k8s__pb2.CreatePersistentVolumeClaimRequest.SerializeToString,
            services_dot_k8s__pb2.CreatePersistentVolumeClaimResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetHelmRelease(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetHelmRelease',
            services_dot_k8s__pb2.GetHelmReleaseRequest.SerializeToString,
            services_dot_k8s__pb2.GetHelmReleaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def InstallHelmRelease(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/InstallHelmRelease',
            services_dot_k8s__pb2.InstallHelmReleaseRequest.SerializeToString,
            services_dot_k8s__pb2.InstallHelmReleaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpgradeHelmRelease(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpgradeHelmRelease',
            services_dot_k8s__pb2.UpgradeHelmReleaseRequest.SerializeToString,
            services_dot_k8s__pb2.UpgradeHelmReleaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UninstallHelmRelease(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UninstallHelmRelease',
            services_dot_k8s__pb2.UninstallHelmReleaseRequest.SerializeToString,
            services_dot_k8s__pb2.UninstallHelmReleaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPodPhases(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetPodPhases',
            services_dot_k8s__pb2.GetPodPhasesRequest.SerializeToString,
            services_dot_k8s__pb2.GetPodPhasesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RestartStatefulSet(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/RestartStatefulSet',
            services_dot_k8s__pb2.RestartStatefulSetRequest.SerializeToString,
            services_dot_k8s__pb2.RestartStatefulSetResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetStatefulSetReplicasStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetStatefulSetReplicasStatus',
            services_dot_k8s__pb2.GetStatefulSetReplicasStatusRequest.SerializeToString,
            services_dot_k8s__pb2.GetStatefulSetReplicasStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDeploymentReplicasStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetDeploymentReplicasStatus',
            services_dot_k8s__pb2.GetDeploymentReplicasStatusRequest.SerializeToString,
            services_dot_k8s__pb2.GetDeploymentReplicasStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RestartDeployment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/RestartDeployment',
            services_dot_k8s__pb2.RestartDeploymentRequest.SerializeToString,
            services_dot_k8s__pb2.RestartDeploymentResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateServiceMonitor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateServiceMonitor',
            services_dot_k8s__pb2.CreateServiceMonitorRequest.SerializeToString,
            services_dot_k8s__pb2.CreateServiceMonitorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteServiceMonitor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteServiceMonitor',
            services_dot_k8s__pb2.DeleteServiceMonitorRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteServiceMonitorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateAzureServiceMonitor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateAzureServiceMonitor',
            services_dot_k8s__pb2.CreateServiceMonitorRequest.SerializeToString,
            services_dot_k8s__pb2.CreateServiceMonitorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteAzureServiceMonitor(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteAzureServiceMonitor',
            services_dot_k8s__pb2.DeleteServiceMonitorRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteServiceMonitorResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePodMonitoring(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreatePodMonitoring',
            services_dot_k8s__pb2.CreatePodMonitoringRequest.SerializeToString,
            services_dot_k8s__pb2.CreatePodMonitoringResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePodMonitoring(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeletePodMonitoring',
            services_dot_k8s__pb2.DeletePodMonitoringRequest.SerializeToString,
            services_dot_k8s__pb2.DeletePodMonitoringResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateService(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateService',
            services_dot_k8s__pb2.CreateServiceRequest.SerializeToString,
            services_dot_k8s__pb2.CreateServiceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateNetworkPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateNetworkPolicy',
            services_dot_k8s__pb2.CreateNetworkPolicyRequest.SerializeToString,
            services_dot_k8s__pb2.CreateNetworkPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateOrUpdateNetworkPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreateOrUpdateNetworkPolicy',
            services_dot_k8s__pb2.CreateOrUpdateNetworkPolicyRequest.SerializeToString,
            services_dot_k8s__pb2.CreateOrUpdateNetworkPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteNetworkPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeleteNetworkPolicy',
            services_dot_k8s__pb2.DeleteNetworkPolicyRequest.SerializeToString,
            services_dot_k8s__pb2.DeleteNetworkPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePostgreSql(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/CreatePostgreSql',
            services_dot_k8s__pb2.CreatePostgreSqlRequest.SerializeToString,
            services_dot_k8s__pb2.CreatePostgreSqlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePostgreSql(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/DeletePostgreSql',
            services_dot_k8s__pb2.DeletePostgreSqlRequest.SerializeToString,
            services_dot_k8s__pb2.DeletePostgreSqlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdatePostgreSql(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/UpdatePostgreSql',
            services_dot_k8s__pb2.UpdatePostgreSqlRequest.SerializeToString,
            services_dot_k8s__pb2.UpdatePostgreSqlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPostgreSql(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetPostgreSql',
            services_dot_k8s__pb2.GetPostgreSqlRequest.SerializeToString,
            services_dot_k8s__pb2.GetPostgreSqlResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPods(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetPods',
            services_dot_k8s__pb2.GetPodsRequest.SerializeToString,
            services_dot_k8s__pb2.GetPodsResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetClusterAccess(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.k8s.K8sResourceManager/GetClusterAccess',
            services_dot_k8s__pb2.GetClusterAccessRequest.SerializeToString,
            services_dot_k8s__pb2.GetClusterAccessResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
