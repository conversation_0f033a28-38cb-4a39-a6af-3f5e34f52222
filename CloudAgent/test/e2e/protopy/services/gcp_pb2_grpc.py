# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services.common import data_pb2 as services_dot_common_dot_data__pb2
from services import gcp_pb2 as services_dot_gcp__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/gcp_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class GcpResourceManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateDataDirectoryDeletionTask = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateDataDirectoryDeletionTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.FromString,
                _registered_method=True)
        self.CreateDataDirectoryCloneTask = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateDataDirectoryCloneTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.FromString,
                _registered_method=True)
        self.CreateIAMPolicyRoleBinding = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateIAMPolicyRoleBinding',
                request_serializer=services_dot_gcp__pb2.CreateIAMPolicyRoleBindingRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.CreateIAMPolicyRoleBindingResponse.FromString,
                _registered_method=True)
        self.DeleteIAMPolicyRoleBinding = channel.unary_unary(
                '/services.gcp.GcpResourceManager/DeleteIAMPolicyRoleBinding',
                request_serializer=services_dot_gcp__pb2.DeleteIAMPolicyRoleBindingRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.DeleteIAMPolicyRoleBindingResponse.FromString,
                _registered_method=True)
        self.GetIAMPolicyRoleBinding = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetIAMPolicyRoleBinding',
                request_serializer=services_dot_gcp__pb2.GetIAMPolicyRoleBindingRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.GetIAMPolicyRoleBindingResponse.FromString,
                _registered_method=True)
        self.CreateIAMPolicyKSABinding = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateIAMPolicyKSABinding',
                request_serializer=services_dot_gcp__pb2.CreateIAMPolicyKSABindingRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.CreateIAMPolicyKSABindingResponse.FromString,
                _registered_method=True)
        self.DeleteIAMPolicyKSABinding = channel.unary_unary(
                '/services.gcp.GcpResourceManager/DeleteIAMPolicyKSABinding',
                request_serializer=services_dot_gcp__pb2.DeleteIAMPolicyKSABindingRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.DeleteIAMPolicyKSABindingResponse.FromString,
                _registered_method=True)
        self.GetIAMPolicyKSABinding = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetIAMPolicyKSABinding',
                request_serializer=services_dot_gcp__pb2.GetIAMPolicyKSABindingRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.GetIAMPolicyKSABindingResponse.FromString,
                _registered_method=True)
        self.CreateIAMServiceAccount = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateIAMServiceAccount',
                request_serializer=services_dot_gcp__pb2.CreateIAMServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.CreateIAMServiceAccountResponse.FromString,
                _registered_method=True)
        self.DeleteIAMServiceAccount = channel.unary_unary(
                '/services.gcp.GcpResourceManager/DeleteIAMServiceAccount',
                request_serializer=services_dot_gcp__pb2.DeleteIAMServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.DeleteIAMServiceAccountResponse.FromString,
                _registered_method=True)
        self.GetIAMServiceAccount = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetIAMServiceAccount',
                request_serializer=services_dot_gcp__pb2.GetIAMServiceAccountRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.GetIAMServiceAccountResponse.FromString,
                _registered_method=True)
        self.CreateIPAddress = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateIPAddress',
                request_serializer=services_dot_gcp__pb2.CreateIPAddressRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.CreateIPAddressResponse.FromString,
                _registered_method=True)
        self.DeleteIPAddress = channel.unary_unary(
                '/services.gcp.GcpResourceManager/DeleteIPAddress',
                request_serializer=services_dot_gcp__pb2.DeleteIPAddressRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.DeleteIPAddressResponse.FromString,
                _registered_method=True)
        self.GetIPAddress = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetIPAddress',
                request_serializer=services_dot_gcp__pb2.GetIPAddressRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.GetIPAddressResponse.FromString,
                _registered_method=True)
        self.CreatePrivateServiceConnectEndpoint = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreatePrivateServiceConnectEndpoint',
                request_serializer=services_dot_gcp__pb2.CreatePrivateServiceConnectEndpointRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.CreatePrivateServiceConnectEndpointResponse.FromString,
                _registered_method=True)
        self.DeletePrivateServiceConnectEndpoint = channel.unary_unary(
                '/services.gcp.GcpResourceManager/DeletePrivateServiceConnectEndpoint',
                request_serializer=services_dot_gcp__pb2.DeletePrivateServiceConnectEndpointRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.DeletePrivateServiceConnectEndpointResponse.FromString,
                _registered_method=True)
        self.GetPrivateServiceConnectEndpoint = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetPrivateServiceConnectEndpoint',
                request_serializer=services_dot_gcp__pb2.GetPrivateServiceConnectEndpointRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.GetPrivateServiceConnectEndpointResponse.FromString,
                _registered_method=True)
        self.CreateSQLInstance = channel.unary_unary(
                '/services.gcp.GcpResourceManager/CreateSQLInstance',
                request_serializer=services_dot_gcp__pb2.CreateSQLInstanceRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.CreateSQLInstanceResponse.FromString,
                _registered_method=True)
        self.DeleteSQLInstance = channel.unary_unary(
                '/services.gcp.GcpResourceManager/DeleteSQLInstance',
                request_serializer=services_dot_gcp__pb2.DeleteSQLInstanceRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.DeleteSQLInstanceResponse.FromString,
                _registered_method=True)
        self.StartSQLInstance = channel.unary_unary(
                '/services.gcp.GcpResourceManager/StartSQLInstance',
                request_serializer=services_dot_gcp__pb2.StartSQLInstanceRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.StartSQLInstanceResponse.FromString,
                _registered_method=True)
        self.StopSQLInstance = channel.unary_unary(
                '/services.gcp.GcpResourceManager/StopSQLInstance',
                request_serializer=services_dot_gcp__pb2.StopSQLInstanceRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.StopSQLInstanceResponse.FromString,
                _registered_method=True)
        self.GetSQLInstance = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetSQLInstance',
                request_serializer=services_dot_gcp__pb2.GetSQLInstanceRequest.SerializeToString,
                response_deserializer=services_dot_gcp__pb2.GetSQLInstanceResponse.FromString,
                _registered_method=True)
        self.GetManifest = channel.unary_unary(
                '/services.gcp.GcpResourceManager/GetManifest',
                request_serializer=services_dot_common_dot_data__pb2.GetManifestRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.GetManifestResponse.FromString,
                _registered_method=True)


class GcpResourceManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateDataDirectoryDeletionTask(self, request, context):
        """CreateDataDirectoryDeletionTask creates a task for GCP folder deletion.
        Caller should call the task manager service to manage the created task.
        Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDataDirectoryCloneTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIAMPolicyRoleBinding(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIAMPolicyRoleBinding(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIAMPolicyRoleBinding(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIAMPolicyKSABinding(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIAMPolicyKSABinding(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIAMPolicyKSABinding(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIAMServiceAccount(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIAMServiceAccount(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIAMServiceAccount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIPAddress(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIPAddress(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIPAddress(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreatePrivateServiceConnectEndpoint(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePrivateServiceConnectEndpoint(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPrivateServiceConnectEndpoint(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateSQLInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSQLInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartSQLInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopSQLInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSQLInstance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetManifest(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_GcpResourceManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateDataDirectoryDeletionTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataDirectoryDeletionTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.SerializeToString,
            ),
            'CreateDataDirectoryCloneTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataDirectoryCloneTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.SerializeToString,
            ),
            'CreateIAMPolicyRoleBinding': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIAMPolicyRoleBinding,
                    request_deserializer=services_dot_gcp__pb2.CreateIAMPolicyRoleBindingRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.CreateIAMPolicyRoleBindingResponse.SerializeToString,
            ),
            'DeleteIAMPolicyRoleBinding': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIAMPolicyRoleBinding,
                    request_deserializer=services_dot_gcp__pb2.DeleteIAMPolicyRoleBindingRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.DeleteIAMPolicyRoleBindingResponse.SerializeToString,
            ),
            'GetIAMPolicyRoleBinding': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIAMPolicyRoleBinding,
                    request_deserializer=services_dot_gcp__pb2.GetIAMPolicyRoleBindingRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.GetIAMPolicyRoleBindingResponse.SerializeToString,
            ),
            'CreateIAMPolicyKSABinding': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIAMPolicyKSABinding,
                    request_deserializer=services_dot_gcp__pb2.CreateIAMPolicyKSABindingRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.CreateIAMPolicyKSABindingResponse.SerializeToString,
            ),
            'DeleteIAMPolicyKSABinding': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIAMPolicyKSABinding,
                    request_deserializer=services_dot_gcp__pb2.DeleteIAMPolicyKSABindingRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.DeleteIAMPolicyKSABindingResponse.SerializeToString,
            ),
            'GetIAMPolicyKSABinding': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIAMPolicyKSABinding,
                    request_deserializer=services_dot_gcp__pb2.GetIAMPolicyKSABindingRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.GetIAMPolicyKSABindingResponse.SerializeToString,
            ),
            'CreateIAMServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIAMServiceAccount,
                    request_deserializer=services_dot_gcp__pb2.CreateIAMServiceAccountRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.CreateIAMServiceAccountResponse.SerializeToString,
            ),
            'DeleteIAMServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIAMServiceAccount,
                    request_deserializer=services_dot_gcp__pb2.DeleteIAMServiceAccountRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.DeleteIAMServiceAccountResponse.SerializeToString,
            ),
            'GetIAMServiceAccount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIAMServiceAccount,
                    request_deserializer=services_dot_gcp__pb2.GetIAMServiceAccountRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.GetIAMServiceAccountResponse.SerializeToString,
            ),
            'CreateIPAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIPAddress,
                    request_deserializer=services_dot_gcp__pb2.CreateIPAddressRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.CreateIPAddressResponse.SerializeToString,
            ),
            'DeleteIPAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIPAddress,
                    request_deserializer=services_dot_gcp__pb2.DeleteIPAddressRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.DeleteIPAddressResponse.SerializeToString,
            ),
            'GetIPAddress': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIPAddress,
                    request_deserializer=services_dot_gcp__pb2.GetIPAddressRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.GetIPAddressResponse.SerializeToString,
            ),
            'CreatePrivateServiceConnectEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.CreatePrivateServiceConnectEndpoint,
                    request_deserializer=services_dot_gcp__pb2.CreatePrivateServiceConnectEndpointRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.CreatePrivateServiceConnectEndpointResponse.SerializeToString,
            ),
            'DeletePrivateServiceConnectEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePrivateServiceConnectEndpoint,
                    request_deserializer=services_dot_gcp__pb2.DeletePrivateServiceConnectEndpointRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.DeletePrivateServiceConnectEndpointResponse.SerializeToString,
            ),
            'GetPrivateServiceConnectEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPrivateServiceConnectEndpoint,
                    request_deserializer=services_dot_gcp__pb2.GetPrivateServiceConnectEndpointRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.GetPrivateServiceConnectEndpointResponse.SerializeToString,
            ),
            'CreateSQLInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateSQLInstance,
                    request_deserializer=services_dot_gcp__pb2.CreateSQLInstanceRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.CreateSQLInstanceResponse.SerializeToString,
            ),
            'DeleteSQLInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSQLInstance,
                    request_deserializer=services_dot_gcp__pb2.DeleteSQLInstanceRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.DeleteSQLInstanceResponse.SerializeToString,
            ),
            'StartSQLInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.StartSQLInstance,
                    request_deserializer=services_dot_gcp__pb2.StartSQLInstanceRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.StartSQLInstanceResponse.SerializeToString,
            ),
            'StopSQLInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.StopSQLInstance,
                    request_deserializer=services_dot_gcp__pb2.StopSQLInstanceRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.StopSQLInstanceResponse.SerializeToString,
            ),
            'GetSQLInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSQLInstance,
                    request_deserializer=services_dot_gcp__pb2.GetSQLInstanceRequest.FromString,
                    response_serializer=services_dot_gcp__pb2.GetSQLInstanceResponse.SerializeToString,
            ),
            'GetManifest': grpc.unary_unary_rpc_method_handler(
                    servicer.GetManifest,
                    request_deserializer=services_dot_common_dot_data__pb2.GetManifestRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.GetManifestResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.gcp.GcpResourceManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.gcp.GcpResourceManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class GcpResourceManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateDataDirectoryDeletionTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateDataDirectoryDeletionTask',
            services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDataDirectoryCloneTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateDataDirectoryCloneTask',
            services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIAMPolicyRoleBinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateIAMPolicyRoleBinding',
            services_dot_gcp__pb2.CreateIAMPolicyRoleBindingRequest.SerializeToString,
            services_dot_gcp__pb2.CreateIAMPolicyRoleBindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteIAMPolicyRoleBinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/DeleteIAMPolicyRoleBinding',
            services_dot_gcp__pb2.DeleteIAMPolicyRoleBindingRequest.SerializeToString,
            services_dot_gcp__pb2.DeleteIAMPolicyRoleBindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIAMPolicyRoleBinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetIAMPolicyRoleBinding',
            services_dot_gcp__pb2.GetIAMPolicyRoleBindingRequest.SerializeToString,
            services_dot_gcp__pb2.GetIAMPolicyRoleBindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIAMPolicyKSABinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateIAMPolicyKSABinding',
            services_dot_gcp__pb2.CreateIAMPolicyKSABindingRequest.SerializeToString,
            services_dot_gcp__pb2.CreateIAMPolicyKSABindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteIAMPolicyKSABinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/DeleteIAMPolicyKSABinding',
            services_dot_gcp__pb2.DeleteIAMPolicyKSABindingRequest.SerializeToString,
            services_dot_gcp__pb2.DeleteIAMPolicyKSABindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIAMPolicyKSABinding(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetIAMPolicyKSABinding',
            services_dot_gcp__pb2.GetIAMPolicyKSABindingRequest.SerializeToString,
            services_dot_gcp__pb2.GetIAMPolicyKSABindingResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIAMServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateIAMServiceAccount',
            services_dot_gcp__pb2.CreateIAMServiceAccountRequest.SerializeToString,
            services_dot_gcp__pb2.CreateIAMServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteIAMServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/DeleteIAMServiceAccount',
            services_dot_gcp__pb2.DeleteIAMServiceAccountRequest.SerializeToString,
            services_dot_gcp__pb2.DeleteIAMServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIAMServiceAccount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetIAMServiceAccount',
            services_dot_gcp__pb2.GetIAMServiceAccountRequest.SerializeToString,
            services_dot_gcp__pb2.GetIAMServiceAccountResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIPAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateIPAddress',
            services_dot_gcp__pb2.CreateIPAddressRequest.SerializeToString,
            services_dot_gcp__pb2.CreateIPAddressResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteIPAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/DeleteIPAddress',
            services_dot_gcp__pb2.DeleteIPAddressRequest.SerializeToString,
            services_dot_gcp__pb2.DeleteIPAddressResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIPAddress(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetIPAddress',
            services_dot_gcp__pb2.GetIPAddressRequest.SerializeToString,
            services_dot_gcp__pb2.GetIPAddressResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreatePrivateServiceConnectEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreatePrivateServiceConnectEndpoint',
            services_dot_gcp__pb2.CreatePrivateServiceConnectEndpointRequest.SerializeToString,
            services_dot_gcp__pb2.CreatePrivateServiceConnectEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeletePrivateServiceConnectEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/DeletePrivateServiceConnectEndpoint',
            services_dot_gcp__pb2.DeletePrivateServiceConnectEndpointRequest.SerializeToString,
            services_dot_gcp__pb2.DeletePrivateServiceConnectEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetPrivateServiceConnectEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetPrivateServiceConnectEndpoint',
            services_dot_gcp__pb2.GetPrivateServiceConnectEndpointRequest.SerializeToString,
            services_dot_gcp__pb2.GetPrivateServiceConnectEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateSQLInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/CreateSQLInstance',
            services_dot_gcp__pb2.CreateSQLInstanceRequest.SerializeToString,
            services_dot_gcp__pb2.CreateSQLInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSQLInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/DeleteSQLInstance',
            services_dot_gcp__pb2.DeleteSQLInstanceRequest.SerializeToString,
            services_dot_gcp__pb2.DeleteSQLInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartSQLInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/StartSQLInstance',
            services_dot_gcp__pb2.StartSQLInstanceRequest.SerializeToString,
            services_dot_gcp__pb2.StartSQLInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopSQLInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/StopSQLInstance',
            services_dot_gcp__pb2.StopSQLInstanceRequest.SerializeToString,
            services_dot_gcp__pb2.StopSQLInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSQLInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetSQLInstance',
            services_dot_gcp__pb2.GetSQLInstanceRequest.SerializeToString,
            services_dot_gcp__pb2.GetSQLInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetManifest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.gcp.GcpResourceManager/GetManifest',
            services_dot_common_dot_data__pb2.GetManifestRequest.SerializeToString,
            services_dot_common_dot_data__pb2.GetManifestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
