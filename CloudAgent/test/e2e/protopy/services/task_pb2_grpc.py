# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import task_pb2 as services_dot_task__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/task_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class TaskManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetTaskStatus = channel.unary_unary(
                '/services.task.TaskManager/GetTaskStatus',
                request_serializer=services_dot_task__pb2.GetTaskStatusRequest.SerializeToString,
                response_deserializer=services_dot_task__pb2.GetTaskStatusResponse.FromString,
                _registered_method=True)
        self.CleanupTask = channel.unary_unary(
                '/services.task.TaskManager/CleanupTask',
                request_serializer=services_dot_task__pb2.CleanupTaskRequest.SerializeToString,
                response_deserializer=services_dot_task__pb2.CleanupTaskResponse.FromString,
                _registered_method=True)


class TaskManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetTaskStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CleanupTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TaskManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetTaskStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTaskStatus,
                    request_deserializer=services_dot_task__pb2.GetTaskStatusRequest.FromString,
                    response_serializer=services_dot_task__pb2.GetTaskStatusResponse.SerializeToString,
            ),
            'CleanupTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CleanupTask,
                    request_deserializer=services_dot_task__pb2.CleanupTaskRequest.FromString,
                    response_serializer=services_dot_task__pb2.CleanupTaskResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.task.TaskManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.task.TaskManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class TaskManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetTaskStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.task.TaskManager/GetTaskStatus',
            services_dot_task__pb2.GetTaskStatusRequest.SerializeToString,
            services_dot_task__pb2.GetTaskStatusResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CleanupTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.task.TaskManager/CleanupTask',
            services_dot_task__pb2.CleanupTaskRequest.SerializeToString,
            services_dot_task__pb2.CleanupTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
