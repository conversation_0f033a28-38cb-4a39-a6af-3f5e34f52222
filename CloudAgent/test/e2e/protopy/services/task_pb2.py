# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/task.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/task.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import task_pb2 as common_dot_task__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import deletion_pb2 as common_dot_deletion__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13services/task.proto\x12\rservices.task\x1a\x11\x63ommon/task.proto\x1a\x15\x63ommon/resource.proto\x1a\x15\x63ommon/deletion.proto\"D\n\x14GetTaskStatusRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"E\n\x15GetTaskStatusResponse\x12,\n\x06status\x18\x01 \x01(\x0b\x32\x1c.common.resource.task.Status\"B\n\x12\x43leanupTaskRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"G\n\x13\x43leanupTaskResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status2\xc3\x01\n\x0bTaskManager\x12\\\n\rGetTaskStatus\x12#.services.task.GetTaskStatusRequest\x1a$.services.task.GetTaskStatusResponse\"\x00\x12V\n\x0b\x43leanupTask\x12!.services.task.CleanupTaskRequest\x1a\".services.task.CleanupTaskResponse\"\x00\x42:Z8github.com/risingwavelabs/cloudagent/pbgen/services/taskb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.task_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z8github.com/risingwavelabs/cloudagent/pbgen/services/task'
  _globals['_GETTASKSTATUSREQUEST']._serialized_start=103
  _globals['_GETTASKSTATUSREQUEST']._serialized_end=171
  _globals['_GETTASKSTATUSRESPONSE']._serialized_start=173
  _globals['_GETTASKSTATUSRESPONSE']._serialized_end=242
  _globals['_CLEANUPTASKREQUEST']._serialized_start=244
  _globals['_CLEANUPTASKREQUEST']._serialized_end=310
  _globals['_CLEANUPTASKRESPONSE']._serialized_start=312
  _globals['_CLEANUPTASKRESPONSE']._serialized_end=383
  _globals['_TASKMANAGER']._serialized_start=386
  _globals['_TASKMANAGER']._serialized_end=581
# @@protoc_insertion_point(module_scope)
