# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import byoc_pb2 as services_dot_byoc__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/byoc_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class ByocResourceManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateApplyByocModuleTask = channel.unary_unary(
                '/services.byoc.ByocResourceManager/CreateApplyByocModuleTask',
                request_serializer=services_dot_byoc__pb2.CreateApplyByocModuleTaskRequest.SerializeToString,
                response_deserializer=services_dot_byoc__pb2.CreateApplyByocModuleTaskResponse.FromString,
                _registered_method=True)
        self.CreateRetrieveByocModuleOutputTask = channel.unary_unary(
                '/services.byoc.ByocResourceManager/CreateRetrieveByocModuleOutputTask',
                request_serializer=services_dot_byoc__pb2.CreateRetrieveByocModuleOutputTaskRequest.SerializeToString,
                response_deserializer=services_dot_byoc__pb2.CreateRetrieveByocModuleOutputTaskResponse.FromString,
                _registered_method=True)


class ByocResourceManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateApplyByocModuleTask(self, request, context):
        """CreateUpdateByocTask creates a task to update the byoc terraform package.
        Caller should call the task manager service to manage the created task.
        Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateRetrieveByocModuleOutputTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ByocResourceManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateApplyByocModuleTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateApplyByocModuleTask,
                    request_deserializer=services_dot_byoc__pb2.CreateApplyByocModuleTaskRequest.FromString,
                    response_serializer=services_dot_byoc__pb2.CreateApplyByocModuleTaskResponse.SerializeToString,
            ),
            'CreateRetrieveByocModuleOutputTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateRetrieveByocModuleOutputTask,
                    request_deserializer=services_dot_byoc__pb2.CreateRetrieveByocModuleOutputTaskRequest.FromString,
                    response_serializer=services_dot_byoc__pb2.CreateRetrieveByocModuleOutputTaskResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.byoc.ByocResourceManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.byoc.ByocResourceManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class ByocResourceManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateApplyByocModuleTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.byoc.ByocResourceManager/CreateApplyByocModuleTask',
            services_dot_byoc__pb2.CreateApplyByocModuleTaskRequest.SerializeToString,
            services_dot_byoc__pb2.CreateApplyByocModuleTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateRetrieveByocModuleOutputTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.byoc.ByocResourceManager/CreateRetrieveByocModuleOutputTask',
            services_dot_byoc__pb2.CreateRetrieveByocModuleOutputTaskRequest.SerializeToString,
            services_dot_byoc__pb2.CreateRetrieveByocModuleOutputTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
