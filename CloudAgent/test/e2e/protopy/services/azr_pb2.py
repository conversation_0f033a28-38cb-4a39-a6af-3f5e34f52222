# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/azr.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/azr.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import deletion_pb2 as common_dot_deletion__pb2
from common import update_pb2 as common_dot_update__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import k8s_pb2 as common_dot_k8s__pb2
from common import azr_pb2 as common_dot_azr__pb2
from services.common import data_pb2 as services_dot_common_dot_data__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12services/azr.proto\x12\x0cservices.azr\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/deletion.proto\x1a\x13\x63ommon/update.proto\x1a\x15\x63ommon/resource.proto\x1a\x10\x63ommon/k8s.proto\x1a\x10\x63ommon/azr.proto\x1a\x1aservices/common/data.proto\"Q\n!CreateUserAssignedIdentityRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"V\n\"CreateUserAssignedIdentityResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"N\n\x1eGetUserAssignedIdentityRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"s\n\x1fGetUserAssignedIdentityResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x14\n\x0cprincipal_id\x18\x02 \x01(\t\x12\x11\n\tclient_id\x18\x03 \x01(\t\"Q\n!DeleteUserAssignedIdentityRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"V\n\"DeleteUserAssignedIdentityResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xb2\x01\n(CreateFederatedIdentityCredentialRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12#\n\x1buser_assigned_identity_name\x18\x02 \x01(\t\x12\x33\n\x0fservice_account\x18\x03 \x01(\x0b\x32\x1a.common.k8s.ServiceAccount\"]\n)CreateFederatedIdentityCredentialResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"U\n%GetFederatedIdentityCredentialRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"Q\n&GetFederatedIdentityCredentialResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"X\n(DeleteFederatedIdentityCredentialRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"]\n)DeleteFederatedIdentityCredentialResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\x98\x01\n\x1b\x43reateRoleAssignmentRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x14\n\x0cprincipal_id\x18\x02 \x01(\t\x12\x35\n\x0frole_assignment\x18\x03 \x01(\x0b\x32\x1c.services.azr.RoleAssignment\"P\n\x1c\x43reateRoleAssignmentResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"H\n\x18GetRoleAssignmentRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"D\n\x19GetRoleAssignmentResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"K\n\x1b\x44\x65leteRoleAssignmentRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"P\n\x1c\x44\x65leteRoleAssignmentResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"_\n\x0eRoleAssignment\x12<\n\x12\x62lob_access_option\x18\x01 \x01(\x0b\x32\x1e.services.azr.BlobAccessOptionH\x00\x42\x0f\n\raccess_option\"L\n\x10\x42lobAccessOption\x12\x11\n\tcontainer\x18\x01 \x01(\t\x12\x0c\n\x04\x64irs\x18\x02 \x03(\t\x12\x17\n\x0fstorage_account\x18\x03 \x01(\t\"\x8e\x02\n\x1c\x43reatePrivateEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x1f\n\x17private_link_service_id\x18\x02 \x01(\t\x12\x1e\n\x16private_link_subnet_id\x18\x03 \x01(\t\x12M\n\nextra_tags\x18\x04 \x03(\x0b\x32\x39.services.azr.CreatePrivateEndpointRequest.ExtraTagsEntry\x1a\x30\n\x0e\x45xtraTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"Q\n\x1d\x43reatePrivateEndpointResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"I\n\x19GetPrivateEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xa8\x01\n\x1aGetPrivateEndpointResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x44\n\x17private_endpoint_status\x18\x02 \x01(\x0e\x32#.services.azr.PrivateEndpointStatus\x12\x1b\n\x13private_endpoint_ip\x18\x03 \x01(\t\"L\n\x1c\x44\x65letePrivateEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"Q\n\x1d\x44\x65letePrivateEndpointResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"m\n\x15\x43reatePGServerRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12&\n\x04spec\x18\x02 \x01(\x0b\x32\x18.common.azr.PGServerSpec\"J\n\x16\x43reatePGServerResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"E\n\x15\x44\x65letePGServerRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"J\n\x16\x44\x65letePGServerResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"D\n\x14StartPGServerRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"G\n\x15StartPGServerResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"C\n\x13StopPGServerRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"F\n\x14StopPGServerResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"B\n\x12GetPGServerRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\x99\x01\n\x13GetPGServerResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x18\n\x0b\x64omain_name\x18\x02 \x01(\tH\x00\x88\x01\x01\x12/\n\x0cserver_state\x18\x03 \x01(\x0e\x32\x19.common.azr.PGServerStateB\x0e\n\x0c_domain_name*f\n\x15PrivateEndpointStatus\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\x0c\n\x08\x44\x45LETING\x10\x01\x12\n\n\x06\x46\x41ILED\x10\x02\x12\r\n\tSUCCEEDED\x10\x03\x12\x0c\n\x08UPDATING\x10\x04\x32\xb7\x12\n\x12\x41zrResourceManager\x12\x81\x01\n\x1a\x43reateUserAssignedIdentity\x12/.services.azr.CreateUserAssignedIdentityRequest\x1a\x30.services.azr.CreateUserAssignedIdentityResponse\"\x00\x12\x81\x01\n\x1a\x44\x65leteUserAssignedIdentity\x12/.services.azr.DeleteUserAssignedIdentityRequest\x1a\x30.services.azr.DeleteUserAssignedIdentityResponse\"\x00\x12x\n\x17GetUserAssignedIdentity\x12,.services.azr.GetUserAssignedIdentityRequest\x1a-.services.azr.GetUserAssignedIdentityResponse\"\x00\x12\x92\x01\n\x1f\x43reateDataDirectoryDeletionTask\x12\x35.services.data.CreateDataDirectoryDeletionTaskRequest\x1a\x36.services.data.CreateDataDirectoryDeletionTaskResponse\"\x00\x12\x89\x01\n\x1c\x43reateDataDirectoryCloneTask\x12\x32.services.data.CreateDataDirectoryCloneTaskRequest\x1a\x33.services.data.CreateDataDirectoryCloneTaskResponse\"\x00\x12\x96\x01\n!CreateFederatedIdentityCredential\x12\x36.services.azr.CreateFederatedIdentityCredentialRequest\x1a\x37.services.azr.CreateFederatedIdentityCredentialResponse\"\x00\x12\x96\x01\n!DeleteFederatedIdentityCredential\x12\x36.services.azr.DeleteFederatedIdentityCredentialRequest\x1a\x37.services.azr.DeleteFederatedIdentityCredentialResponse\"\x00\x12\x8d\x01\n\x1eGetFederatedIdentityCredential\x12\x33.services.azr.GetFederatedIdentityCredentialRequest\x1a\x34.services.azr.GetFederatedIdentityCredentialResponse\"\x00\x12o\n\x14\x43reateRoleAssignment\x12).services.azr.CreateRoleAssignmentRequest\x1a*.services.azr.CreateRoleAssignmentResponse\"\x00\x12o\n\x14\x44\x65leteRoleAssignment\x12).services.azr.DeleteRoleAssignmentRequest\x1a*.services.azr.DeleteRoleAssignmentResponse\"\x00\x12\x66\n\x11GetRoleAssignment\x12&.services.azr.GetRoleAssignmentRequest\x1a\'.services.azr.GetRoleAssignmentResponse\"\x00\x12r\n\x15\x43reatePrivateEndpoint\x12*.services.azr.CreatePrivateEndpointRequest\x1a+.services.azr.CreatePrivateEndpointResponse\"\x00\x12r\n\x15\x44\x65letePrivateEndpoint\x12*.services.azr.DeletePrivateEndpointRequest\x1a+.services.azr.DeletePrivateEndpointResponse\"\x00\x12i\n\x12GetPrivateEndpoint\x12\'.services.azr.GetPrivateEndpointRequest\x1a(.services.azr.GetPrivateEndpointResponse\"\x00\x12]\n\x0e\x43reatePGServer\x12#.services.azr.CreatePGServerRequest\x1a$.services.azr.CreatePGServerResponse\"\x00\x12]\n\x0e\x44\x65letePGServer\x12#.services.azr.DeletePGServerRequest\x1a$.services.azr.DeletePGServerResponse\"\x00\x12Z\n\rStartPGServer\x12\".services.azr.StartPGServerRequest\x1a#.services.azr.StartPGServerResponse\"\x00\x12W\n\x0cStopPGServer\x12!.services.azr.StopPGServerRequest\x1a\".services.azr.StopPGServerResponse\"\x00\x12T\n\x0bGetPGServer\x12 .services.azr.GetPGServerRequest\x1a!.services.azr.GetPGServerResponse\"\x00\x12V\n\x0bGetManifest\x12!.services.data.GetManifestRequest\x1a\".services.data.GetManifestResponse\"\x00\x42\x39Z7github.com/risingwavelabs/cloudagent/pbgen/services/azrb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.azr_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z7github.com/risingwavelabs/cloudagent/pbgen/services/azr'
  _globals['_CREATEPRIVATEENDPOINTREQUEST_EXTRATAGSENTRY']._loaded_options = None
  _globals['_CREATEPRIVATEENDPOINTREQUEST_EXTRATAGSENTRY']._serialized_options = b'8\001'
  _globals['_PRIVATEENDPOINTSTATUS']._serialized_start=3680
  _globals['_PRIVATEENDPOINTSTATUS']._serialized_end=3782
  _globals['_CREATEUSERASSIGNEDIDENTITYREQUEST']._serialized_start=190
  _globals['_CREATEUSERASSIGNEDIDENTITYREQUEST']._serialized_end=271
  _globals['_CREATEUSERASSIGNEDIDENTITYRESPONSE']._serialized_start=273
  _globals['_CREATEUSERASSIGNEDIDENTITYRESPONSE']._serialized_end=359
  _globals['_GETUSERASSIGNEDIDENTITYREQUEST']._serialized_start=361
  _globals['_GETUSERASSIGNEDIDENTITYREQUEST']._serialized_end=439
  _globals['_GETUSERASSIGNEDIDENTITYRESPONSE']._serialized_start=441
  _globals['_GETUSERASSIGNEDIDENTITYRESPONSE']._serialized_end=556
  _globals['_DELETEUSERASSIGNEDIDENTITYREQUEST']._serialized_start=558
  _globals['_DELETEUSERASSIGNEDIDENTITYREQUEST']._serialized_end=639
  _globals['_DELETEUSERASSIGNEDIDENTITYRESPONSE']._serialized_start=641
  _globals['_DELETEUSERASSIGNEDIDENTITYRESPONSE']._serialized_end=727
  _globals['_CREATEFEDERATEDIDENTITYCREDENTIALREQUEST']._serialized_start=730
  _globals['_CREATEFEDERATEDIDENTITYCREDENTIALREQUEST']._serialized_end=908
  _globals['_CREATEFEDERATEDIDENTITYCREDENTIALRESPONSE']._serialized_start=910
  _globals['_CREATEFEDERATEDIDENTITYCREDENTIALRESPONSE']._serialized_end=1003
  _globals['_GETFEDERATEDIDENTITYCREDENTIALREQUEST']._serialized_start=1005
  _globals['_GETFEDERATEDIDENTITYCREDENTIALREQUEST']._serialized_end=1090
  _globals['_GETFEDERATEDIDENTITYCREDENTIALRESPONSE']._serialized_start=1092
  _globals['_GETFEDERATEDIDENTITYCREDENTIALRESPONSE']._serialized_end=1173
  _globals['_DELETEFEDERATEDIDENTITYCREDENTIALREQUEST']._serialized_start=1175
  _globals['_DELETEFEDERATEDIDENTITYCREDENTIALREQUEST']._serialized_end=1263
  _globals['_DELETEFEDERATEDIDENTITYCREDENTIALRESPONSE']._serialized_start=1265
  _globals['_DELETEFEDERATEDIDENTITYCREDENTIALRESPONSE']._serialized_end=1358
  _globals['_CREATEROLEASSIGNMENTREQUEST']._serialized_start=1361
  _globals['_CREATEROLEASSIGNMENTREQUEST']._serialized_end=1513
  _globals['_CREATEROLEASSIGNMENTRESPONSE']._serialized_start=1515
  _globals['_CREATEROLEASSIGNMENTRESPONSE']._serialized_end=1595
  _globals['_GETROLEASSIGNMENTREQUEST']._serialized_start=1597
  _globals['_GETROLEASSIGNMENTREQUEST']._serialized_end=1669
  _globals['_GETROLEASSIGNMENTRESPONSE']._serialized_start=1671
  _globals['_GETROLEASSIGNMENTRESPONSE']._serialized_end=1739
  _globals['_DELETEROLEASSIGNMENTREQUEST']._serialized_start=1741
  _globals['_DELETEROLEASSIGNMENTREQUEST']._serialized_end=1816
  _globals['_DELETEROLEASSIGNMENTRESPONSE']._serialized_start=1818
  _globals['_DELETEROLEASSIGNMENTRESPONSE']._serialized_end=1898
  _globals['_ROLEASSIGNMENT']._serialized_start=1900
  _globals['_ROLEASSIGNMENT']._serialized_end=1995
  _globals['_BLOBACCESSOPTION']._serialized_start=1997
  _globals['_BLOBACCESSOPTION']._serialized_end=2073
  _globals['_CREATEPRIVATEENDPOINTREQUEST']._serialized_start=2076
  _globals['_CREATEPRIVATEENDPOINTREQUEST']._serialized_end=2346
  _globals['_CREATEPRIVATEENDPOINTREQUEST_EXTRATAGSENTRY']._serialized_start=2298
  _globals['_CREATEPRIVATEENDPOINTREQUEST_EXTRATAGSENTRY']._serialized_end=2346
  _globals['_CREATEPRIVATEENDPOINTRESPONSE']._serialized_start=2348
  _globals['_CREATEPRIVATEENDPOINTRESPONSE']._serialized_end=2429
  _globals['_GETPRIVATEENDPOINTREQUEST']._serialized_start=2431
  _globals['_GETPRIVATEENDPOINTREQUEST']._serialized_end=2504
  _globals['_GETPRIVATEENDPOINTRESPONSE']._serialized_start=2507
  _globals['_GETPRIVATEENDPOINTRESPONSE']._serialized_end=2675
  _globals['_DELETEPRIVATEENDPOINTREQUEST']._serialized_start=2677
  _globals['_DELETEPRIVATEENDPOINTREQUEST']._serialized_end=2753
  _globals['_DELETEPRIVATEENDPOINTRESPONSE']._serialized_start=2755
  _globals['_DELETEPRIVATEENDPOINTRESPONSE']._serialized_end=2836
  _globals['_CREATEPGSERVERREQUEST']._serialized_start=2838
  _globals['_CREATEPGSERVERREQUEST']._serialized_end=2947
  _globals['_CREATEPGSERVERRESPONSE']._serialized_start=2949
  _globals['_CREATEPGSERVERRESPONSE']._serialized_end=3023
  _globals['_DELETEPGSERVERREQUEST']._serialized_start=3025
  _globals['_DELETEPGSERVERREQUEST']._serialized_end=3094
  _globals['_DELETEPGSERVERRESPONSE']._serialized_start=3096
  _globals['_DELETEPGSERVERRESPONSE']._serialized_end=3170
  _globals['_STARTPGSERVERREQUEST']._serialized_start=3172
  _globals['_STARTPGSERVERREQUEST']._serialized_end=3240
  _globals['_STARTPGSERVERRESPONSE']._serialized_start=3242
  _globals['_STARTPGSERVERRESPONSE']._serialized_end=3313
  _globals['_STOPPGSERVERREQUEST']._serialized_start=3315
  _globals['_STOPPGSERVERREQUEST']._serialized_end=3382
  _globals['_STOPPGSERVERRESPONSE']._serialized_start=3384
  _globals['_STOPPGSERVERRESPONSE']._serialized_end=3454
  _globals['_GETPGSERVERREQUEST']._serialized_start=3456
  _globals['_GETPGSERVERREQUEST']._serialized_end=3522
  _globals['_GETPGSERVERRESPONSE']._serialized_start=3525
  _globals['_GETPGSERVERRESPONSE']._serialized_end=3678
  _globals['_AZRRESOURCEMANAGER']._serialized_start=3785
  _globals['_AZRRESOURCEMANAGER']._serialized_end=6144
# @@protoc_insertion_point(module_scope)
