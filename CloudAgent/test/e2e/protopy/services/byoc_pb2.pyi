from common import creation_pb2 as _creation_pb2
from common import resource_pb2 as _resource_pb2
from common import byoc_pb2 as _byoc_pb2
from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class CreateApplyByocModuleTaskRequest(_message.Message):
    __slots__ = ("resource_meta", "module_options", "apply_options", "package_options", "task_tolerations", "task_affinity")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    MODULE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    APPLY_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    TASK_TOLERATIONS_FIELD_NUMBER: _ClassVar[int]
    TASK_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    module_options: _byoc_pb2.ModuleOptions
    apply_options: _byoc_pb2.ApplyOptions
    package_options: _byoc_pb2.PackageOptions
    task_tolerations: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Toleration]
    task_affinity: _k8s_pb2.Affinity
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., module_options: _Optional[_Union[_byoc_pb2.ModuleOptions, _Mapping]] = ..., apply_options: _Optional[_Union[_byoc_pb2.ApplyOptions, _Mapping]] = ..., package_options: _Optional[_Union[_byoc_pb2.PackageOptions, _Mapping]] = ..., task_tolerations: _Optional[_Iterable[_Union[_k8s_pb2.Toleration, _Mapping]]] = ..., task_affinity: _Optional[_Union[_k8s_pb2.Affinity, _Mapping]] = ...) -> None: ...

class CreateApplyByocModuleTaskResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateRetrieveByocModuleOutputTaskRequest(_message.Message):
    __slots__ = ("resource_meta", "output_key", "module_options", "output_options", "package_options", "task_tolerations", "task_affinity")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_KEY_FIELD_NUMBER: _ClassVar[int]
    MODULE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    TASK_TOLERATIONS_FIELD_NUMBER: _ClassVar[int]
    TASK_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    output_key: str
    module_options: _byoc_pb2.ModuleOptions
    output_options: _byoc_pb2.OutputOptions
    package_options: _byoc_pb2.PackageOptions
    task_tolerations: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Toleration]
    task_affinity: _k8s_pb2.Affinity
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., output_key: _Optional[str] = ..., module_options: _Optional[_Union[_byoc_pb2.ModuleOptions, _Mapping]] = ..., output_options: _Optional[_Union[_byoc_pb2.OutputOptions, _Mapping]] = ..., package_options: _Optional[_Union[_byoc_pb2.PackageOptions, _Mapping]] = ..., task_tolerations: _Optional[_Iterable[_Union[_k8s_pb2.Toleration, _Mapping]]] = ..., task_affinity: _Optional[_Union[_k8s_pb2.Affinity, _Mapping]] = ...) -> None: ...

class CreateRetrieveByocModuleOutputTaskResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...
