# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/byoc.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/byoc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import byoc_pb2 as common_dot_byoc__pb2
from common import k8s_pb2 as common_dot_k8s__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13services/byoc.proto\x12\rservices.byoc\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/resource.proto\x1a\x11\x63ommon/byoc.proto\x1a\x10\x63ommon/k8s.proto\"\xcb\x02\n CreateApplyByocModuleTaskRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x32\n\x0emodule_options\x18\x02 \x01(\x0b\x32\x1a.common.byoc.ModuleOptions\x12\x30\n\rapply_options\x18\x03 \x01(\x0b\x32\x19.common.byoc.ApplyOptions\x12\x34\n\x0fpackage_options\x18\x04 \x01(\x0b\x32\x1b.common.byoc.PackageOptions\x12\x30\n\x10task_tolerations\x18\x05 \x03(\x0b\x32\x16.common.k8s.Toleration\x12+\n\rtask_affinity\x18\x06 \x01(\x0b\x32\x14.common.k8s.Affinity\"U\n!CreateApplyByocModuleTaskResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xea\x02\n)CreateRetrieveByocModuleOutputTaskRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x12\n\noutput_key\x18\x02 \x01(\t\x12\x32\n\x0emodule_options\x18\x03 \x01(\x0b\x32\x1a.common.byoc.ModuleOptions\x12\x32\n\x0eoutput_options\x18\x04 \x01(\x0b\x32\x1a.common.byoc.OutputOptions\x12\x34\n\x0fpackage_options\x18\x05 \x01(\x0b\x32\x1b.common.byoc.PackageOptions\x12\x30\n\x10task_tolerations\x18\x06 \x03(\x0b\x32\x16.common.k8s.Toleration\x12+\n\rtask_affinity\x18\x07 \x01(\x0b\x32\x14.common.k8s.Affinity\"^\n*CreateRetrieveByocModuleOutputTaskResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status2\xb6\x02\n\x13\x42yocResourceManager\x12\x80\x01\n\x19\x43reateApplyByocModuleTask\x12/.services.byoc.CreateApplyByocModuleTaskRequest\x1a\x30.services.byoc.CreateApplyByocModuleTaskResponse\"\x00\x12\x9b\x01\n\"CreateRetrieveByocModuleOutputTask\x12\x38.services.byoc.CreateRetrieveByocModuleOutputTaskRequest\x1a\x39.services.byoc.CreateRetrieveByocModuleOutputTaskResponse\"\x00\x42:Z8github.com/risingwavelabs/cloudagent/pbgen/services/byocb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.byoc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z8github.com/risingwavelabs/cloudagent/pbgen/services/byoc'
  _globals['_CREATEAPPLYBYOCMODULETASKREQUEST']._serialized_start=122
  _globals['_CREATEAPPLYBYOCMODULETASKREQUEST']._serialized_end=453
  _globals['_CREATEAPPLYBYOCMODULETASKRESPONSE']._serialized_start=455
  _globals['_CREATEAPPLYBYOCMODULETASKRESPONSE']._serialized_end=540
  _globals['_CREATERETRIEVEBYOCMODULEOUTPUTTASKREQUEST']._serialized_start=543
  _globals['_CREATERETRIEVEBYOCMODULEOUTPUTTASKREQUEST']._serialized_end=905
  _globals['_CREATERETRIEVEBYOCMODULEOUTPUTTASKRESPONSE']._serialized_start=907
  _globals['_CREATERETRIEVEBYOCMODULEOUTPUTTASKRESPONSE']._serialized_end=1001
  _globals['_BYOCRESOURCEMANAGER']._serialized_start=1004
  _globals['_BYOCRESOURCEMANAGER']._serialized_end=1314
# @@protoc_insertion_point(module_scope)
