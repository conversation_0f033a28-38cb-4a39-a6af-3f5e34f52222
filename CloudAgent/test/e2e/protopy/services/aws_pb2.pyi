from common import creation_pb2 as _creation_pb2
from common import deletion_pb2 as _deletion_pb2
from common import update_pb2 as _update_pb2
from common import resource_pb2 as _resource_pb2
from common import k8s_pb2 as _k8s_pb2
from common import aws_pb2 as _aws_pb2
from services.common import data_pb2 as _data_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class VPCEndpointServiceReachabilityStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    REACHABILITY_UNSPECIFIED: _ClassVar[VPCEndpointServiceReachabilityStatus]
    SUCCESS: _ClassVar[VPCEndpointServiceReachabilityStatus]
    NOT_FOUND: _ClassVar[VPCEndpointServiceReachabilityStatus]

class VPCEndpointStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    STATUS_UNSPECIFIED: _ClassVar[VPCEndpointStatus]
    PENDING_ACCEPTANCE: _ClassVar[VPCEndpointStatus]
    PENDING: _ClassVar[VPCEndpointStatus]
    AVAILABLE: _ClassVar[VPCEndpointStatus]
    REJECTED: _ClassVar[VPCEndpointStatus]
    EXPIRED: _ClassVar[VPCEndpointStatus]
    FAILED: _ClassVar[VPCEndpointStatus]
    DELETING: _ClassVar[VPCEndpointStatus]
    DELETED: _ClassVar[VPCEndpointStatus]
REACHABILITY_UNSPECIFIED: VPCEndpointServiceReachabilityStatus
SUCCESS: VPCEndpointServiceReachabilityStatus
NOT_FOUND: VPCEndpointServiceReachabilityStatus
STATUS_UNSPECIFIED: VPCEndpointStatus
PENDING_ACCEPTANCE: VPCEndpointStatus
PENDING: VPCEndpointStatus
AVAILABLE: VPCEndpointStatus
REJECTED: VPCEndpointStatus
EXPIRED: VPCEndpointStatus
FAILED: VPCEndpointStatus
DELETING: VPCEndpointStatus
DELETED: VPCEndpointStatus

class IPPermission(_message.Message):
    __slots__ = ("protocol", "from_port", "to_port", "cidrs", "source_security_group_ids", "description")
    PROTOCOL_FIELD_NUMBER: _ClassVar[int]
    FROM_PORT_FIELD_NUMBER: _ClassVar[int]
    TO_PORT_FIELD_NUMBER: _ClassVar[int]
    CIDRS_FIELD_NUMBER: _ClassVar[int]
    SOURCE_SECURITY_GROUP_IDS_FIELD_NUMBER: _ClassVar[int]
    DESCRIPTION_FIELD_NUMBER: _ClassVar[int]
    protocol: str
    from_port: int
    to_port: int
    cidrs: _containers.RepeatedScalarFieldContainer[str]
    source_security_group_ids: _containers.RepeatedScalarFieldContainer[str]
    description: str
    def __init__(self, protocol: _Optional[str] = ..., from_port: _Optional[int] = ..., to_port: _Optional[int] = ..., cidrs: _Optional[_Iterable[str]] = ..., source_security_group_ids: _Optional[_Iterable[str]] = ..., description: _Optional[str] = ...) -> None: ...

class CreateSecurityGroupRequest(_message.Message):
    __slots__ = ("resource_meta", "outbound_ip_permissions", "inbound_ip_permissions")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    OUTBOUND_IP_PERMISSIONS_FIELD_NUMBER: _ClassVar[int]
    INBOUND_IP_PERMISSIONS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    outbound_ip_permissions: _containers.RepeatedCompositeFieldContainer[IPPermission]
    inbound_ip_permissions: _containers.RepeatedCompositeFieldContainer[IPPermission]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., outbound_ip_permissions: _Optional[_Iterable[_Union[IPPermission, _Mapping]]] = ..., inbound_ip_permissions: _Optional[_Iterable[_Union[IPPermission, _Mapping]]] = ...) -> None: ...

class CreateSecurityGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteSecurityGroupRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteSecurityGroupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class GetSecurityGroupRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetSecurityGroupResponse(_message.Message):
    __slots__ = ("status", "security_group_id")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    SECURITY_GROUP_ID_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    security_group_id: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., security_group_id: _Optional[str] = ...) -> None: ...

class CreateIAMPolicyRequest(_message.Message):
    __slots__ = ("resource_meta", "access_options")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    ACCESS_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    access_options: _containers.RepeatedCompositeFieldContainer[IAMAccessOption]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., access_options: _Optional[_Iterable[_Union[IAMAccessOption, _Mapping]]] = ...) -> None: ...

class IAMAccessOption(_message.Message):
    __slots__ = ("s3_access_option",)
    S3_ACCESS_OPTION_FIELD_NUMBER: _ClassVar[int]
    s3_access_option: IAMS3AccessOption
    def __init__(self, s3_access_option: _Optional[_Union[IAMS3AccessOption, _Mapping]] = ...) -> None: ...

class CreateIAMPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetIAMPolicyRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetIAMPolicyResponse(_message.Message):
    __slots__ = ("status", "policy_arn")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    POLICY_ARN_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    policy_arn: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., policy_arn: _Optional[str] = ...) -> None: ...

class DeleteIAMPolicyRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteIAMPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateIAMRoleRequest(_message.Message):
    __slots__ = ("resource_meta", "policy_refs", "service_account")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    POLICY_REFS_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    policy_refs: _containers.RepeatedCompositeFieldContainer[_resource_pb2.Meta]
    service_account: _k8s_pb2.ServiceAccount
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., policy_refs: _Optional[_Iterable[_Union[_resource_pb2.Meta, _Mapping]]] = ..., service_account: _Optional[_Union[_k8s_pb2.ServiceAccount, _Mapping]] = ...) -> None: ...

class CreateIAMRoleResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetIAMRoleRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetIAMRoleResponse(_message.Message):
    __slots__ = ("status", "role_arn")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    ROLE_ARN_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    role_arn: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., role_arn: _Optional[str] = ...) -> None: ...

class DeleteIAMRoleRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteIAMRoleResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class IAMS3AccessOption(_message.Message):
    __slots__ = ("bucket", "dir", "dirs")
    BUCKET_FIELD_NUMBER: _ClassVar[int]
    DIR_FIELD_NUMBER: _ClassVar[int]
    DIRS_FIELD_NUMBER: _ClassVar[int]
    bucket: str
    dir: str
    dirs: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, bucket: _Optional[str] = ..., dir: _Optional[str] = ..., dirs: _Optional[_Iterable[str]] = ...) -> None: ...

class IAMPrivateLinkAccessOption(_message.Message):
    __slots__ = ("vpc_endpoints_tag_key", "vpc_endpoints_tag_val")
    VPC_ENDPOINTS_TAG_KEY_FIELD_NUMBER: _ClassVar[int]
    VPC_ENDPOINTS_TAG_VAL_FIELD_NUMBER: _ClassVar[int]
    vpc_endpoints_tag_key: str
    vpc_endpoints_tag_val: str
    def __init__(self, vpc_endpoints_tag_key: _Optional[str] = ..., vpc_endpoints_tag_val: _Optional[str] = ...) -> None: ...

class DeleteVPCEndpointsRequest(_message.Message):
    __slots__ = ("tag_key", "tag_value")
    TAG_KEY_FIELD_NUMBER: _ClassVar[int]
    TAG_VALUE_FIELD_NUMBER: _ClassVar[int]
    tag_key: str
    tag_value: str
    def __init__(self, tag_key: _Optional[str] = ..., tag_value: _Optional[str] = ...) -> None: ...

class DeleteVPCEndpointsResponse(_message.Message):
    __slots__ = ("vpc_endpoint_ids", "error_message")
    VPC_ENDPOINT_IDS_FIELD_NUMBER: _ClassVar[int]
    ERROR_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    vpc_endpoint_ids: _containers.RepeatedScalarFieldContainer[str]
    error_message: str
    def __init__(self, vpc_endpoint_ids: _Optional[_Iterable[str]] = ..., error_message: _Optional[str] = ...) -> None: ...

class CreateSecurityGroupPolicyRequest(_message.Message):
    __slots__ = ("resource_meta", "security_group_ids", "pod_labels_selector")
    class PodLabelsSelectorEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SECURITY_GROUP_IDS_FIELD_NUMBER: _ClassVar[int]
    POD_LABELS_SELECTOR_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    security_group_ids: _containers.RepeatedScalarFieldContainer[str]
    pod_labels_selector: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., security_group_ids: _Optional[_Iterable[str]] = ..., pod_labels_selector: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateSecurityGroupPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteSecurityGroupPolicyRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteSecurityGroupPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class GetSecurityGroupPolicyRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetSecurityGroupPolicyResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class CheckVPCEndpointServiceReachabilityRequest(_message.Message):
    __slots__ = ("resource_meta", "service_name")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    service_name: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., service_name: _Optional[str] = ...) -> None: ...

class CheckVPCEndpointServiceReachabilityResponse(_message.Message):
    __slots__ = ("resource_meta", "status")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    STATUS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    status: VPCEndpointServiceReachabilityStatus
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., status: _Optional[_Union[VPCEndpointServiceReachabilityStatus, str]] = ...) -> None: ...

class CreateVPCEndpointRequest(_message.Message):
    __slots__ = ("resource_meta", "service_name", "security_group_ids", "subnet_ids", "private_dns_enabled", "extra_tags")
    class ExtraTagsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    SECURITY_GROUP_IDS_FIELD_NUMBER: _ClassVar[int]
    SUBNET_IDS_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_DNS_ENABLED_FIELD_NUMBER: _ClassVar[int]
    EXTRA_TAGS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    service_name: str
    security_group_ids: _containers.RepeatedScalarFieldContainer[str]
    subnet_ids: _containers.RepeatedScalarFieldContainer[str]
    private_dns_enabled: bool
    extra_tags: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., service_name: _Optional[str] = ..., security_group_ids: _Optional[_Iterable[str]] = ..., subnet_ids: _Optional[_Iterable[str]] = ..., private_dns_enabled: bool = ..., extra_tags: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateVPCEndpointResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteVPCEndpointRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteVPCEndpointResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class GetVPCEndpointRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetVPCEndpointResponse(_message.Message):
    __slots__ = ("status", "endpoint_id", "endpoint_state", "endpoint_dns")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_ID_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_STATE_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_DNS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    endpoint_id: str
    endpoint_state: VPCEndpointStatus
    endpoint_dns: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., endpoint_id: _Optional[str] = ..., endpoint_state: _Optional[_Union[VPCEndpointStatus, str]] = ..., endpoint_dns: _Optional[str] = ...) -> None: ...

class CreateDBInstanceRequest(_message.Message):
    __slots__ = ("resource_meta", "spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    spec: _aws_pb2.DBInstanceSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., spec: _Optional[_Union[_aws_pb2.DBInstanceSpec, _Mapping]] = ...) -> None: ...

class CreateDBInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteDBInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteDBInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class StartDBInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StartDBInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class StopDBInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StopDBInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class GetDBInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetDBInstanceResponse(_message.Message):
    __slots__ = ("status", "endpoint", "instance_status")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    endpoint: _aws_pb2.DBInstanceEndpoint
    instance_status: _aws_pb2.DBInstanceStatus
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., endpoint: _Optional[_Union[_aws_pb2.DBInstanceEndpoint, _Mapping]] = ..., instance_status: _Optional[_Union[_aws_pb2.DBInstanceStatus, str]] = ...) -> None: ...
