from common import task_pb2 as _task_pb2
from common import resource_pb2 as _resource_pb2
from common import deletion_pb2 as _deletion_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GetTaskStatusRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetTaskStatusResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _task_pb2.Status
    def __init__(self, status: _Optional[_Union[_task_pb2.Status, _Mapping]] = ...) -> None: ...

class CleanupTaskRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class CleanupTaskResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...
