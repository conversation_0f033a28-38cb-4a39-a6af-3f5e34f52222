# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import aws_pb2 as services_dot_aws__pb2
from services.common import data_pb2 as services_dot_common_dot_data__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/aws_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class AwsResourceManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateDataDirectoryDeletionTask = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateDataDirectoryDeletionTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.FromString,
                _registered_method=True)
        self.CreateDataDirectoryCloneTask = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateDataDirectoryCloneTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.FromString,
                _registered_method=True)
        self.CreateSecurityGroup = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateSecurityGroup',
                request_serializer=services_dot_aws__pb2.CreateSecurityGroupRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CreateSecurityGroupResponse.FromString,
                _registered_method=True)
        self.DeleteSecurityGroup = channel.unary_unary(
                '/services.aws.AwsResourceManager/DeleteSecurityGroup',
                request_serializer=services_dot_aws__pb2.DeleteSecurityGroupRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.DeleteSecurityGroupResponse.FromString,
                _registered_method=True)
        self.GetSecurityGroup = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetSecurityGroup',
                request_serializer=services_dot_aws__pb2.GetSecurityGroupRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.GetSecurityGroupResponse.FromString,
                _registered_method=True)
        self.CreateSecurityGroupPolicy = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateSecurityGroupPolicy',
                request_serializer=services_dot_aws__pb2.CreateSecurityGroupPolicyRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CreateSecurityGroupPolicyResponse.FromString,
                _registered_method=True)
        self.DeleteSecurityGroupPolicy = channel.unary_unary(
                '/services.aws.AwsResourceManager/DeleteSecurityGroupPolicy',
                request_serializer=services_dot_aws__pb2.DeleteSecurityGroupPolicyRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.DeleteSecurityGroupPolicyResponse.FromString,
                _registered_method=True)
        self.GetSecurityGroupPolicy = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetSecurityGroupPolicy',
                request_serializer=services_dot_aws__pb2.GetSecurityGroupPolicyRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.GetSecurityGroupPolicyResponse.FromString,
                _registered_method=True)
        self.CreateIAMPolicy = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateIAMPolicy',
                request_serializer=services_dot_aws__pb2.CreateIAMPolicyRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CreateIAMPolicyResponse.FromString,
                _registered_method=True)
        self.DeleteIAMPolicy = channel.unary_unary(
                '/services.aws.AwsResourceManager/DeleteIAMPolicy',
                request_serializer=services_dot_aws__pb2.DeleteIAMPolicyRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.DeleteIAMPolicyResponse.FromString,
                _registered_method=True)
        self.GetIAMPolicy = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetIAMPolicy',
                request_serializer=services_dot_aws__pb2.GetIAMPolicyRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.GetIAMPolicyResponse.FromString,
                _registered_method=True)
        self.CreateIAMRole = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateIAMRole',
                request_serializer=services_dot_aws__pb2.CreateIAMRoleRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CreateIAMRoleResponse.FromString,
                _registered_method=True)
        self.DeleteIAMRole = channel.unary_unary(
                '/services.aws.AwsResourceManager/DeleteIAMRole',
                request_serializer=services_dot_aws__pb2.DeleteIAMRoleRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.DeleteIAMRoleResponse.FromString,
                _registered_method=True)
        self.GetIAMRole = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetIAMRole',
                request_serializer=services_dot_aws__pb2.GetIAMRoleRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.GetIAMRoleResponse.FromString,
                _registered_method=True)
        self.CheckVPCEndpointServiceReachability = channel.unary_unary(
                '/services.aws.AwsResourceManager/CheckVPCEndpointServiceReachability',
                request_serializer=services_dot_aws__pb2.CheckVPCEndpointServiceReachabilityRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CheckVPCEndpointServiceReachabilityResponse.FromString,
                _registered_method=True)
        self.CreateVPCEndpoint = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateVPCEndpoint',
                request_serializer=services_dot_aws__pb2.CreateVPCEndpointRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CreateVPCEndpointResponse.FromString,
                _registered_method=True)
        self.DeleteVPCEndpoint = channel.unary_unary(
                '/services.aws.AwsResourceManager/DeleteVPCEndpoint',
                request_serializer=services_dot_aws__pb2.DeleteVPCEndpointRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.DeleteVPCEndpointResponse.FromString,
                _registered_method=True)
        self.GetVPCEndpoint = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetVPCEndpoint',
                request_serializer=services_dot_aws__pb2.GetVPCEndpointRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.GetVPCEndpointResponse.FromString,
                _registered_method=True)
        self.CreateSimpleDataReplicationTask = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateSimpleDataReplicationTask',
                request_serializer=services_dot_common_dot_data__pb2.CreateSimpleDataReplicationTaskRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.CreateSimpleDataReplicationTaskResponse.FromString,
                _registered_method=True)
        self.CreateDBInstance = channel.unary_unary(
                '/services.aws.AwsResourceManager/CreateDBInstance',
                request_serializer=services_dot_aws__pb2.CreateDBInstanceRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.CreateDBInstanceResponse.FromString,
                _registered_method=True)
        self.DeleteDBInstance = channel.unary_unary(
                '/services.aws.AwsResourceManager/DeleteDBInstance',
                request_serializer=services_dot_aws__pb2.DeleteDBInstanceRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.DeleteDBInstanceResponse.FromString,
                _registered_method=True)
        self.StartDBInstance = channel.unary_unary(
                '/services.aws.AwsResourceManager/StartDBInstance',
                request_serializer=services_dot_aws__pb2.StartDBInstanceRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.StartDBInstanceResponse.FromString,
                _registered_method=True)
        self.StopDBInstance = channel.unary_unary(
                '/services.aws.AwsResourceManager/StopDBInstance',
                request_serializer=services_dot_aws__pb2.StopDBInstanceRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.StopDBInstanceResponse.FromString,
                _registered_method=True)
        self.GetDBInstance = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetDBInstance',
                request_serializer=services_dot_aws__pb2.GetDBInstanceRequest.SerializeToString,
                response_deserializer=services_dot_aws__pb2.GetDBInstanceResponse.FromString,
                _registered_method=True)
        self.GetManifest = channel.unary_unary(
                '/services.aws.AwsResourceManager/GetManifest',
                request_serializer=services_dot_common_dot_data__pb2.GetManifestRequest.SerializeToString,
                response_deserializer=services_dot_common_dot_data__pb2.GetManifestResponse.FromString,
                _registered_method=True)


class AwsResourceManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateDataDirectoryDeletionTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDataDirectoryCloneTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateSecurityGroup(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSecurityGroup(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSecurityGroup(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateSecurityGroupPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSecurityGroupPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSecurityGroupPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIAMPolicy(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIAMPolicy(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIAMPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateIAMRole(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteIAMRole(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetIAMRole(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CheckVPCEndpointServiceReachability(self, request, context):
        """Expected a successful status
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateVPCEndpoint(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteVPCEndpoint(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetVPCEndpoint(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateSimpleDataReplicationTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateDBInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteDBInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartDBInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopDBInstance(self, request, context):
        """Expected a SCHEDULED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDBInstance(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetManifest(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AwsResourceManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateDataDirectoryDeletionTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataDirectoryDeletionTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.SerializeToString,
            ),
            'CreateDataDirectoryCloneTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDataDirectoryCloneTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.SerializeToString,
            ),
            'CreateSecurityGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateSecurityGroup,
                    request_deserializer=services_dot_aws__pb2.CreateSecurityGroupRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CreateSecurityGroupResponse.SerializeToString,
            ),
            'DeleteSecurityGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSecurityGroup,
                    request_deserializer=services_dot_aws__pb2.DeleteSecurityGroupRequest.FromString,
                    response_serializer=services_dot_aws__pb2.DeleteSecurityGroupResponse.SerializeToString,
            ),
            'GetSecurityGroup': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSecurityGroup,
                    request_deserializer=services_dot_aws__pb2.GetSecurityGroupRequest.FromString,
                    response_serializer=services_dot_aws__pb2.GetSecurityGroupResponse.SerializeToString,
            ),
            'CreateSecurityGroupPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateSecurityGroupPolicy,
                    request_deserializer=services_dot_aws__pb2.CreateSecurityGroupPolicyRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CreateSecurityGroupPolicyResponse.SerializeToString,
            ),
            'DeleteSecurityGroupPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSecurityGroupPolicy,
                    request_deserializer=services_dot_aws__pb2.DeleteSecurityGroupPolicyRequest.FromString,
                    response_serializer=services_dot_aws__pb2.DeleteSecurityGroupPolicyResponse.SerializeToString,
            ),
            'GetSecurityGroupPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSecurityGroupPolicy,
                    request_deserializer=services_dot_aws__pb2.GetSecurityGroupPolicyRequest.FromString,
                    response_serializer=services_dot_aws__pb2.GetSecurityGroupPolicyResponse.SerializeToString,
            ),
            'CreateIAMPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIAMPolicy,
                    request_deserializer=services_dot_aws__pb2.CreateIAMPolicyRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CreateIAMPolicyResponse.SerializeToString,
            ),
            'DeleteIAMPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIAMPolicy,
                    request_deserializer=services_dot_aws__pb2.DeleteIAMPolicyRequest.FromString,
                    response_serializer=services_dot_aws__pb2.DeleteIAMPolicyResponse.SerializeToString,
            ),
            'GetIAMPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIAMPolicy,
                    request_deserializer=services_dot_aws__pb2.GetIAMPolicyRequest.FromString,
                    response_serializer=services_dot_aws__pb2.GetIAMPolicyResponse.SerializeToString,
            ),
            'CreateIAMRole': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateIAMRole,
                    request_deserializer=services_dot_aws__pb2.CreateIAMRoleRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CreateIAMRoleResponse.SerializeToString,
            ),
            'DeleteIAMRole': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteIAMRole,
                    request_deserializer=services_dot_aws__pb2.DeleteIAMRoleRequest.FromString,
                    response_serializer=services_dot_aws__pb2.DeleteIAMRoleResponse.SerializeToString,
            ),
            'GetIAMRole': grpc.unary_unary_rpc_method_handler(
                    servicer.GetIAMRole,
                    request_deserializer=services_dot_aws__pb2.GetIAMRoleRequest.FromString,
                    response_serializer=services_dot_aws__pb2.GetIAMRoleResponse.SerializeToString,
            ),
            'CheckVPCEndpointServiceReachability': grpc.unary_unary_rpc_method_handler(
                    servicer.CheckVPCEndpointServiceReachability,
                    request_deserializer=services_dot_aws__pb2.CheckVPCEndpointServiceReachabilityRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CheckVPCEndpointServiceReachabilityResponse.SerializeToString,
            ),
            'CreateVPCEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateVPCEndpoint,
                    request_deserializer=services_dot_aws__pb2.CreateVPCEndpointRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CreateVPCEndpointResponse.SerializeToString,
            ),
            'DeleteVPCEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteVPCEndpoint,
                    request_deserializer=services_dot_aws__pb2.DeleteVPCEndpointRequest.FromString,
                    response_serializer=services_dot_aws__pb2.DeleteVPCEndpointResponse.SerializeToString,
            ),
            'GetVPCEndpoint': grpc.unary_unary_rpc_method_handler(
                    servicer.GetVPCEndpoint,
                    request_deserializer=services_dot_aws__pb2.GetVPCEndpointRequest.FromString,
                    response_serializer=services_dot_aws__pb2.GetVPCEndpointResponse.SerializeToString,
            ),
            'CreateSimpleDataReplicationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateSimpleDataReplicationTask,
                    request_deserializer=services_dot_common_dot_data__pb2.CreateSimpleDataReplicationTaskRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.CreateSimpleDataReplicationTaskResponse.SerializeToString,
            ),
            'CreateDBInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDBInstance,
                    request_deserializer=services_dot_aws__pb2.CreateDBInstanceRequest.FromString,
                    response_serializer=services_dot_aws__pb2.CreateDBInstanceResponse.SerializeToString,
            ),
            'DeleteDBInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteDBInstance,
                    request_deserializer=services_dot_aws__pb2.DeleteDBInstanceRequest.FromString,
                    response_serializer=services_dot_aws__pb2.DeleteDBInstanceResponse.SerializeToString,
            ),
            'StartDBInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.StartDBInstance,
                    request_deserializer=services_dot_aws__pb2.StartDBInstanceRequest.FromString,
                    response_serializer=services_dot_aws__pb2.StartDBInstanceResponse.SerializeToString,
            ),
            'StopDBInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.StopDBInstance,
                    request_deserializer=services_dot_aws__pb2.StopDBInstanceRequest.FromString,
                    response_serializer=services_dot_aws__pb2.StopDBInstanceResponse.SerializeToString,
            ),
            'GetDBInstance': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDBInstance,
                    request_deserializer=services_dot_aws__pb2.GetDBInstanceRequest.FromString,
                    response_serializer=services_dot_aws__pb2.GetDBInstanceResponse.SerializeToString,
            ),
            'GetManifest': grpc.unary_unary_rpc_method_handler(
                    servicer.GetManifest,
                    request_deserializer=services_dot_common_dot_data__pb2.GetManifestRequest.FromString,
                    response_serializer=services_dot_common_dot_data__pb2.GetManifestResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.aws.AwsResourceManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.aws.AwsResourceManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class AwsResourceManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateDataDirectoryDeletionTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateDataDirectoryDeletionTask',
            services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateDataDirectoryDeletionTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDataDirectoryCloneTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateDataDirectoryCloneTask',
            services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateDataDirectoryCloneTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateSecurityGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateSecurityGroup',
            services_dot_aws__pb2.CreateSecurityGroupRequest.SerializeToString,
            services_dot_aws__pb2.CreateSecurityGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSecurityGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/DeleteSecurityGroup',
            services_dot_aws__pb2.DeleteSecurityGroupRequest.SerializeToString,
            services_dot_aws__pb2.DeleteSecurityGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSecurityGroup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetSecurityGroup',
            services_dot_aws__pb2.GetSecurityGroupRequest.SerializeToString,
            services_dot_aws__pb2.GetSecurityGroupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateSecurityGroupPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateSecurityGroupPolicy',
            services_dot_aws__pb2.CreateSecurityGroupPolicyRequest.SerializeToString,
            services_dot_aws__pb2.CreateSecurityGroupPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSecurityGroupPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/DeleteSecurityGroupPolicy',
            services_dot_aws__pb2.DeleteSecurityGroupPolicyRequest.SerializeToString,
            services_dot_aws__pb2.DeleteSecurityGroupPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetSecurityGroupPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetSecurityGroupPolicy',
            services_dot_aws__pb2.GetSecurityGroupPolicyRequest.SerializeToString,
            services_dot_aws__pb2.GetSecurityGroupPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIAMPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateIAMPolicy',
            services_dot_aws__pb2.CreateIAMPolicyRequest.SerializeToString,
            services_dot_aws__pb2.CreateIAMPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteIAMPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/DeleteIAMPolicy',
            services_dot_aws__pb2.DeleteIAMPolicyRequest.SerializeToString,
            services_dot_aws__pb2.DeleteIAMPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIAMPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetIAMPolicy',
            services_dot_aws__pb2.GetIAMPolicyRequest.SerializeToString,
            services_dot_aws__pb2.GetIAMPolicyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateIAMRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateIAMRole',
            services_dot_aws__pb2.CreateIAMRoleRequest.SerializeToString,
            services_dot_aws__pb2.CreateIAMRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteIAMRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/DeleteIAMRole',
            services_dot_aws__pb2.DeleteIAMRoleRequest.SerializeToString,
            services_dot_aws__pb2.DeleteIAMRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetIAMRole(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetIAMRole',
            services_dot_aws__pb2.GetIAMRoleRequest.SerializeToString,
            services_dot_aws__pb2.GetIAMRoleResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CheckVPCEndpointServiceReachability(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CheckVPCEndpointServiceReachability',
            services_dot_aws__pb2.CheckVPCEndpointServiceReachabilityRequest.SerializeToString,
            services_dot_aws__pb2.CheckVPCEndpointServiceReachabilityResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateVPCEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateVPCEndpoint',
            services_dot_aws__pb2.CreateVPCEndpointRequest.SerializeToString,
            services_dot_aws__pb2.CreateVPCEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteVPCEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/DeleteVPCEndpoint',
            services_dot_aws__pb2.DeleteVPCEndpointRequest.SerializeToString,
            services_dot_aws__pb2.DeleteVPCEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetVPCEndpoint(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetVPCEndpoint',
            services_dot_aws__pb2.GetVPCEndpointRequest.SerializeToString,
            services_dot_aws__pb2.GetVPCEndpointResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateSimpleDataReplicationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateSimpleDataReplicationTask',
            services_dot_common_dot_data__pb2.CreateSimpleDataReplicationTaskRequest.SerializeToString,
            services_dot_common_dot_data__pb2.CreateSimpleDataReplicationTaskResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateDBInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/CreateDBInstance',
            services_dot_aws__pb2.CreateDBInstanceRequest.SerializeToString,
            services_dot_aws__pb2.CreateDBInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteDBInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/DeleteDBInstance',
            services_dot_aws__pb2.DeleteDBInstanceRequest.SerializeToString,
            services_dot_aws__pb2.DeleteDBInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StartDBInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/StartDBInstance',
            services_dot_aws__pb2.StartDBInstanceRequest.SerializeToString,
            services_dot_aws__pb2.StartDBInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def StopDBInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/StopDBInstance',
            services_dot_aws__pb2.StopDBInstanceRequest.SerializeToString,
            services_dot_aws__pb2.StopDBInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetDBInstance(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetDBInstance',
            services_dot_aws__pb2.GetDBInstanceRequest.SerializeToString,
            services_dot_aws__pb2.GetDBInstanceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetManifest(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.aws.AwsResourceManager/GetManifest',
            services_dot_common_dot_data__pb2.GetManifestRequest.SerializeToString,
            services_dot_common_dot_data__pb2.GetManifestResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
