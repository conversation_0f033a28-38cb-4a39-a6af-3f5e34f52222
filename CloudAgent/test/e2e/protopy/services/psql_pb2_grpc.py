# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import psql_pb2 as services_dot_psql__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/psql_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PsqlManagerStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.CreateDatabase = channel.unary_unary(
                '/services.psql.PsqlManager/CreateDatabase',
                request_serializer=services_dot_psql__pb2.CreateDatabaseRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.CreateDatabaseResponse.FromString,
                _registered_method=True)
        self.DeleteDatabase = channel.unary_unary(
                '/services.psql.PsqlManager/DeleteDatabase',
                request_serializer=services_dot_psql__pb2.DeleteDatabaseRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.DeleteDatabaseResponse.FromString,
                _registered_method=True)
        self.CreateUser = channel.unary_unary(
                '/services.psql.PsqlManager/CreateUser',
                request_serializer=services_dot_psql__pb2.CreateUserRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.CreateUserResponse.FromString,
                _registered_method=True)
        self.DeleteUser = channel.unary_unary(
                '/services.psql.PsqlManager/DeleteUser',
                request_serializer=services_dot_psql__pb2.DeleteUserRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.DeleteUserResponse.FromString,
                _registered_method=True)
        self.TruncateTables = channel.unary_unary(
                '/services.psql.PsqlManager/TruncateTables',
                request_serializer=services_dot_psql__pb2.TruncateTablesRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.TruncateTablesResponse.FromString,
                _registered_method=True)
        self.UpdateSystemParameters = channel.unary_unary(
                '/services.psql.PsqlManager/UpdateSystemParameters',
                request_serializer=services_dot_psql__pb2.UpdateSystemParametersRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.UpdateSystemParametersResponse.FromString,
                _registered_method=True)
        self.CloneDatabase = channel.unary_unary(
                '/services.psql.PsqlManager/CloneDatabase',
                request_serializer=services_dot_psql__pb2.CloneDatabaseRequest.SerializeToString,
                response_deserializer=services_dot_psql__pb2.CloneDatabaseResponse.FromString,
                _registered_method=True)


class PsqlManagerServicer(object):
    """Missing associated documentation comment in .proto file."""

    def CreateDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CreateUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteUser(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def TruncateTables(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpdateSystemParameters(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CloneDatabase(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PsqlManagerServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'CreateDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateDatabase,
                    request_deserializer=services_dot_psql__pb2.CreateDatabaseRequest.FromString,
                    response_serializer=services_dot_psql__pb2.CreateDatabaseResponse.SerializeToString,
            ),
            'DeleteDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteDatabase,
                    request_deserializer=services_dot_psql__pb2.DeleteDatabaseRequest.FromString,
                    response_serializer=services_dot_psql__pb2.DeleteDatabaseResponse.SerializeToString,
            ),
            'CreateUser': grpc.unary_unary_rpc_method_handler(
                    servicer.CreateUser,
                    request_deserializer=services_dot_psql__pb2.CreateUserRequest.FromString,
                    response_serializer=services_dot_psql__pb2.CreateUserResponse.SerializeToString,
            ),
            'DeleteUser': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteUser,
                    request_deserializer=services_dot_psql__pb2.DeleteUserRequest.FromString,
                    response_serializer=services_dot_psql__pb2.DeleteUserResponse.SerializeToString,
            ),
            'TruncateTables': grpc.unary_unary_rpc_method_handler(
                    servicer.TruncateTables,
                    request_deserializer=services_dot_psql__pb2.TruncateTablesRequest.FromString,
                    response_serializer=services_dot_psql__pb2.TruncateTablesResponse.SerializeToString,
            ),
            'UpdateSystemParameters': grpc.unary_unary_rpc_method_handler(
                    servicer.UpdateSystemParameters,
                    request_deserializer=services_dot_psql__pb2.UpdateSystemParametersRequest.FromString,
                    response_serializer=services_dot_psql__pb2.UpdateSystemParametersResponse.SerializeToString,
            ),
            'CloneDatabase': grpc.unary_unary_rpc_method_handler(
                    servicer.CloneDatabase,
                    request_deserializer=services_dot_psql__pb2.CloneDatabaseRequest.FromString,
                    response_serializer=services_dot_psql__pb2.CloneDatabaseResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.psql.PsqlManager', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.psql.PsqlManager', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class PsqlManager(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def CreateDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/CreateDatabase',
            services_dot_psql__pb2.CreateDatabaseRequest.SerializeToString,
            services_dot_psql__pb2.CreateDatabaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/DeleteDatabase',
            services_dot_psql__pb2.DeleteDatabaseRequest.SerializeToString,
            services_dot_psql__pb2.DeleteDatabaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CreateUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/CreateUser',
            services_dot_psql__pb2.CreateUserRequest.SerializeToString,
            services_dot_psql__pb2.CreateUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteUser(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/DeleteUser',
            services_dot_psql__pb2.DeleteUserRequest.SerializeToString,
            services_dot_psql__pb2.DeleteUserResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def TruncateTables(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/TruncateTables',
            services_dot_psql__pb2.TruncateTablesRequest.SerializeToString,
            services_dot_psql__pb2.TruncateTablesResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def UpdateSystemParameters(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/UpdateSystemParameters',
            services_dot_psql__pb2.UpdateSystemParametersRequest.SerializeToString,
            services_dot_psql__pb2.UpdateSystemParametersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CloneDatabase(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.psql.PsqlManager/CloneDatabase',
            services_dot_psql__pb2.CloneDatabaseRequest.SerializeToString,
            services_dot_psql__pb2.CloneDatabaseResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
