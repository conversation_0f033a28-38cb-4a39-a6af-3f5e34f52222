# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/telemetry/prometheus.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/telemetry/prometheus.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#services/telemetry/prometheus.proto\x12\x1dservices.telemetry.prometheus\"\xe2\x01\n\x0cProxyRequest\x12>\n\x0bhttp_method\x18\x01 \x01(\x0e\x32).services.telemetry.prometheus.HTTPMethod\x12\x39\n\x08\x65ndpoint\x18\x02 \x01(\x0e\x32\'.services.telemetry.prometheus.Endpoint\x12\x0f\n\x07payload\x18\x03 \x01(\x0c\x12\x46\n\x0f\x65ndpoint_params\x18\x04 \x01(\x0b\x32-.services.telemetry.prometheus.EndpointParams\"5\n\rProxyResponse\x12\x13\n\x0bstatus_code\x18\x01 \x01(\r\x12\x0f\n\x07payload\x18\x02 \x01(\x0c\"/\n\x19LabelValuesEndpointParams\x12\x12\n\nlabel_name\x18\x01 \x01(\t\"s\n\x0e\x45ndpointParams\x12W\n\x13label_values_params\x18\x01 \x01(\x0b\x32\x38.services.telemetry.prometheus.LabelValuesEndpointParamsH\x00\x42\x08\n\x06params\"\x7f\n\rScrapeRequest\x12\x11\n\tnamespace\x18\x01 \x01(\t\x12\x1e\n\x16\x61\x63\x63\x65pt_encoding_header\x18\x02 \x01(\t\x12\x0f\n\x07include\x18\x03 \x03(\t\x12\x0f\n\x07\x65xclude\x18\x04 \x03(\t\x12\x19\n\x11max_response_size\x18\x05 \x01(\r\"x\n\x0eScrapeResponse\x12\x17\n\x0bstatus_code\x18\x01 \x01(\rB\x02\x18\x01\x12\x0f\n\x07payload\x18\x02 \x01(\x0c\x12\x1b\n\x13\x63ontent_type_header\x18\x03 \x01(\t\x12\x1f\n\x17\x63ontent_encoding_header\x18\x04 \x01(\t*7\n\nHTTPMethod\x12\x16\n\x12METHOD_UNSPECIFIED\x10\x00\x12\x07\n\x03GET\x10\x01\x12\x08\n\x04POST\x10\x02*j\n\x08\x45ndpoint\x12\x18\n\x14\x45NDPOINT_UNSPECIFIED\x10\x00\x12\t\n\x05QUERY\x10\x01\x12\x0f\n\x0bQUERY_RANGE\x10\x02\x12\n\n\x06SERIES\x10\x03\x12\x10\n\x0cLABEL_VALUES\x10\x04\x12\n\n\x06LABELS\x10\x05\x32\xdb\x01\n\nPrometheus\x12\x64\n\x05Proxy\x12+.services.telemetry.prometheus.ProxyRequest\x1a,.services.telemetry.prometheus.ProxyResponse\"\x00\x12g\n\x06Scrape\x12,.services.telemetry.prometheus.ScrapeRequest\x1a-.services.telemetry.prometheus.ScrapeResponse\"\x00\x42JZHgithub.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheusb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.telemetry.prometheus_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'ZHgithub.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus'
  _globals['_SCRAPERESPONSE'].fields_by_name['status_code']._loaded_options = None
  _globals['_SCRAPERESPONSE'].fields_by_name['status_code']._serialized_options = b'\030\001'
  _globals['_HTTPMETHOD']._serialized_start=771
  _globals['_HTTPMETHOD']._serialized_end=826
  _globals['_ENDPOINT']._serialized_start=828
  _globals['_ENDPOINT']._serialized_end=934
  _globals['_PROXYREQUEST']._serialized_start=71
  _globals['_PROXYREQUEST']._serialized_end=297
  _globals['_PROXYRESPONSE']._serialized_start=299
  _globals['_PROXYRESPONSE']._serialized_end=352
  _globals['_LABELVALUESENDPOINTPARAMS']._serialized_start=354
  _globals['_LABELVALUESENDPOINTPARAMS']._serialized_end=401
  _globals['_ENDPOINTPARAMS']._serialized_start=403
  _globals['_ENDPOINTPARAMS']._serialized_end=518
  _globals['_SCRAPEREQUEST']._serialized_start=520
  _globals['_SCRAPEREQUEST']._serialized_end=647
  _globals['_SCRAPERESPONSE']._serialized_start=649
  _globals['_SCRAPERESPONSE']._serialized_end=769
  _globals['_PROMETHEUS']._serialized_start=937
  _globals['_PROMETHEUS']._serialized_end=1156
# @@protoc_insertion_point(module_scope)
