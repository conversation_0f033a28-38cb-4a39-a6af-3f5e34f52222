from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class HTTPMethod(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    METHOD_UNSPECIFIED: _ClassVar[HTTPMethod]
    GET: _ClassVar[HTTPMethod]
    POST: _ClassVar[HTTPMethod]

class Endpoint(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    ENDPOINT_UNSPECIFIED: _ClassVar[Endpoint]
    QUERY: _ClassVar[Endpoint]
    QUERY_RANGE: _ClassVar[Endpoint]
    SERIES: _ClassVar[Endpoint]
    LABEL_VALUES: _ClassVar[Endpoint]
    LABELS: _ClassVar[Endpoint]
METHOD_UNSPECIFIED: HTTPMethod
GET: HTTPMethod
POST: HTTPMethod
ENDPOINT_UNSPECIFIED: Endpoint
QUERY: Endpoint
QUERY_RANGE: Endpoint
SERIES: Endpoint
LABEL_VALUES: Endpoint
LABELS: Endpoint

class ProxyRequest(_message.Message):
    __slots__ = ("http_method", "endpoint", "payload", "endpoint_params")
    HTTP_METHOD_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    PAYLOAD_FIELD_NUMBER: _ClassVar[int]
    ENDPOINT_PARAMS_FIELD_NUMBER: _ClassVar[int]
    http_method: HTTPMethod
    endpoint: Endpoint
    payload: bytes
    endpoint_params: EndpointParams
    def __init__(self, http_method: _Optional[_Union[HTTPMethod, str]] = ..., endpoint: _Optional[_Union[Endpoint, str]] = ..., payload: _Optional[bytes] = ..., endpoint_params: _Optional[_Union[EndpointParams, _Mapping]] = ...) -> None: ...

class ProxyResponse(_message.Message):
    __slots__ = ("status_code", "payload")
    STATUS_CODE_FIELD_NUMBER: _ClassVar[int]
    PAYLOAD_FIELD_NUMBER: _ClassVar[int]
    status_code: int
    payload: bytes
    def __init__(self, status_code: _Optional[int] = ..., payload: _Optional[bytes] = ...) -> None: ...

class LabelValuesEndpointParams(_message.Message):
    __slots__ = ("label_name",)
    LABEL_NAME_FIELD_NUMBER: _ClassVar[int]
    label_name: str
    def __init__(self, label_name: _Optional[str] = ...) -> None: ...

class EndpointParams(_message.Message):
    __slots__ = ("label_values_params",)
    LABEL_VALUES_PARAMS_FIELD_NUMBER: _ClassVar[int]
    label_values_params: LabelValuesEndpointParams
    def __init__(self, label_values_params: _Optional[_Union[LabelValuesEndpointParams, _Mapping]] = ...) -> None: ...

class ScrapeRequest(_message.Message):
    __slots__ = ("namespace", "accept_encoding_header", "include", "exclude", "max_response_size")
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    ACCEPT_ENCODING_HEADER_FIELD_NUMBER: _ClassVar[int]
    INCLUDE_FIELD_NUMBER: _ClassVar[int]
    EXCLUDE_FIELD_NUMBER: _ClassVar[int]
    MAX_RESPONSE_SIZE_FIELD_NUMBER: _ClassVar[int]
    namespace: str
    accept_encoding_header: str
    include: _containers.RepeatedScalarFieldContainer[str]
    exclude: _containers.RepeatedScalarFieldContainer[str]
    max_response_size: int
    def __init__(self, namespace: _Optional[str] = ..., accept_encoding_header: _Optional[str] = ..., include: _Optional[_Iterable[str]] = ..., exclude: _Optional[_Iterable[str]] = ..., max_response_size: _Optional[int] = ...) -> None: ...

class ScrapeResponse(_message.Message):
    __slots__ = ("status_code", "payload", "content_type_header", "content_encoding_header")
    STATUS_CODE_FIELD_NUMBER: _ClassVar[int]
    PAYLOAD_FIELD_NUMBER: _ClassVar[int]
    CONTENT_TYPE_HEADER_FIELD_NUMBER: _ClassVar[int]
    CONTENT_ENCODING_HEADER_FIELD_NUMBER: _ClassVar[int]
    status_code: int
    payload: bytes
    content_type_header: str
    content_encoding_header: str
    def __init__(self, status_code: _Optional[int] = ..., payload: _Optional[bytes] = ..., content_type_header: _Optional[str] = ..., content_encoding_header: _Optional[str] = ...) -> None: ...
