# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services.telemetry import prometheus_pb2 as services_dot_telemetry_dot_prometheus__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/telemetry/prometheus_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class PrometheusStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Proxy = channel.unary_unary(
                '/services.telemetry.prometheus.Prometheus/Proxy',
                request_serializer=services_dot_telemetry_dot_prometheus__pb2.ProxyRequest.SerializeToString,
                response_deserializer=services_dot_telemetry_dot_prometheus__pb2.ProxyResponse.FromString,
                _registered_method=True)
        self.Scrape = channel.unary_unary(
                '/services.telemetry.prometheus.Prometheus/Scrape',
                request_serializer=services_dot_telemetry_dot_prometheus__pb2.ScrapeRequest.SerializeToString,
                response_deserializer=services_dot_telemetry_dot_prometheus__pb2.ScrapeResponse.FromString,
                _registered_method=True)


class PrometheusServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Proxy(self, request, context):
        """Proxy sends the prometheus request to cluster's prometheus compatible
        endpoint and returns the response payload and code.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def Scrape(self, request, context):
        """Scrape will scrape the metrics endpoint under user namespace.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PrometheusServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Proxy': grpc.unary_unary_rpc_method_handler(
                    servicer.Proxy,
                    request_deserializer=services_dot_telemetry_dot_prometheus__pb2.ProxyRequest.FromString,
                    response_serializer=services_dot_telemetry_dot_prometheus__pb2.ProxyResponse.SerializeToString,
            ),
            'Scrape': grpc.unary_unary_rpc_method_handler(
                    servicer.Scrape,
                    request_deserializer=services_dot_telemetry_dot_prometheus__pb2.ScrapeRequest.FromString,
                    response_serializer=services_dot_telemetry_dot_prometheus__pb2.ScrapeResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.telemetry.prometheus.Prometheus', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.telemetry.prometheus.Prometheus', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class Prometheus(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Proxy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.telemetry.prometheus.Prometheus/Proxy',
            services_dot_telemetry_dot_prometheus__pb2.ProxyRequest.SerializeToString,
            services_dot_telemetry_dot_prometheus__pb2.ProxyResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def Scrape(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.telemetry.prometheus.Prometheus/Scrape',
            services_dot_telemetry_dot_prometheus__pb2.ScrapeRequest.SerializeToString,
            services_dot_telemetry_dot_prometheus__pb2.ScrapeResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
