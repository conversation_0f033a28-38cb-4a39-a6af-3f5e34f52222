from common import creation_pb2 as _creation_pb2
from common import deletion_pb2 as _deletion_pb2
from common import resource_pb2 as _resource_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Connection(_message.Message):
    __slots__ = ("host", "port", "database", "username", "password")
    HOST_FIELD_NUMBER: _ClassVar[int]
    PORT_FIELD_NUMBER: _ClassVar[int]
    DATABASE_FIELD_NUMBER: _ClassVar[int]
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    PASSWORD_FIELD_NUMBER: _ClassVar[int]
    host: str
    port: int
    database: str
    username: str
    password: str
    def __init__(self, host: _Optional[str] = ..., port: _Optional[int] = ..., database: _Optional[str] = ..., username: _Optional[str] = ..., password: _Optional[str] = ...) -> None: ...

class CreateDatabaseRequest(_message.Message):
    __slots__ = ("connection", "database")
    CONNECTION_FIELD_NUMBER: _ClassVar[int]
    DATABASE_FIELD_NUMBER: _ClassVar[int]
    connection: Connection
    database: str
    def __init__(self, connection: _Optional[_Union[Connection, _Mapping]] = ..., database: _Optional[str] = ...) -> None: ...

class CreateDatabaseResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteDatabaseRequest(_message.Message):
    __slots__ = ("connection", "database")
    CONNECTION_FIELD_NUMBER: _ClassVar[int]
    DATABASE_FIELD_NUMBER: _ClassVar[int]
    connection: Connection
    database: str
    def __init__(self, connection: _Optional[_Union[Connection, _Mapping]] = ..., database: _Optional[str] = ...) -> None: ...

class DeleteDatabaseResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateUserRequest(_message.Message):
    __slots__ = ("connection", "username", "password", "privileged_databases")
    CONNECTION_FIELD_NUMBER: _ClassVar[int]
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    PASSWORD_FIELD_NUMBER: _ClassVar[int]
    PRIVILEGED_DATABASES_FIELD_NUMBER: _ClassVar[int]
    connection: Connection
    username: str
    password: str
    privileged_databases: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, connection: _Optional[_Union[Connection, _Mapping]] = ..., username: _Optional[str] = ..., password: _Optional[str] = ..., privileged_databases: _Optional[_Iterable[str]] = ...) -> None: ...

class CreateUserResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteUserRequest(_message.Message):
    __slots__ = ("connection", "username")
    CONNECTION_FIELD_NUMBER: _ClassVar[int]
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    connection: Connection
    username: str
    def __init__(self, connection: _Optional[_Union[Connection, _Mapping]] = ..., username: _Optional[str] = ...) -> None: ...

class DeleteUserResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class TruncateTablesRequest(_message.Message):
    __slots__ = ("connection",)
    CONNECTION_FIELD_NUMBER: _ClassVar[int]
    connection: Connection
    def __init__(self, connection: _Optional[_Union[Connection, _Mapping]] = ...) -> None: ...

class TruncateTablesResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class UpdateSystemParametersRequest(_message.Message):
    __slots__ = ("connection", "system_parameters")
    class SystemParametersEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    CONNECTION_FIELD_NUMBER: _ClassVar[int]
    SYSTEM_PARAMETERS_FIELD_NUMBER: _ClassVar[int]
    connection: Connection
    system_parameters: _containers.ScalarMap[str, str]
    def __init__(self, connection: _Optional[_Union[Connection, _Mapping]] = ..., system_parameters: _Optional[_Mapping[str, str]] = ...) -> None: ...

class UpdateSystemParametersResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class CloneDatabaseRequest(_message.Message):
    __slots__ = ("resource_meta", "source_connection", "target_connection")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SOURCE_CONNECTION_FIELD_NUMBER: _ClassVar[int]
    TARGET_CONNECTION_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    source_connection: Connection
    target_connection: Connection
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., source_connection: _Optional[_Union[Connection, _Mapping]] = ..., target_connection: _Optional[_Union[Connection, _Mapping]] = ...) -> None: ...

class CloneDatabaseResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...
