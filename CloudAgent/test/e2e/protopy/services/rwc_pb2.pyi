from common import creation_pb2 as _creation_pb2
from common import resource_pb2 as _resource_pb2
from common import k8s_pb2 as _k8s_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class KafkaSecurityProtocol(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    PROTOCOL_UNSPECIFIED: _ClassVar[KafkaSecurityProtocol]
    SASL_PLAINTEXT: _ClassVar[KafkaSecurityProtocol]
    SASL_SSL: _ClassVar[KafkaSecurityProtocol]
    SSL: _ClassVar[KafkaSecurityProtocol]

class KafkaSaslMechanism(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    MECHANISM_UNSPECIFIED: _ClassVar[KafkaSaslMechanism]
    PLAIN: _ClassVar[KafkaSaslMechanism]
    SCRAM_SHA_256: _ClassVar[KafkaSaslMechanism]
    SCRAM_SHA_512: _ClassVar[KafkaSaslMechanism]

class PostgresSslMode(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    SSL_MODE_UNSPECIFIED: _ClassVar[PostgresSslMode]
    DISABLED: _ClassVar[PostgresSslMode]
    PREFERRED: _ClassVar[PostgresSslMode]
    REQUIRED: _ClassVar[PostgresSslMode]
    VERIFY_CA: _ClassVar[PostgresSslMode]
    VERIFY_FULL: _ClassVar[PostgresSslMode]

class SchemaFormat(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    FORMAT_UNSPECIFIED: _ClassVar[SchemaFormat]
    AVRO: _ClassVar[SchemaFormat]
    PROTOBUF: _ClassVar[SchemaFormat]
    JSON: _ClassVar[SchemaFormat]

class SchemaLocation(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    LOCATION_UNSPECIFIED: _ClassVar[SchemaLocation]
    WEB_LOCATION: _ClassVar[SchemaLocation]
    SCHEMA_REGISTRY: _ClassVar[SchemaLocation]
    S3: _ClassVar[SchemaLocation]
PROTOCOL_UNSPECIFIED: KafkaSecurityProtocol
SASL_PLAINTEXT: KafkaSecurityProtocol
SASL_SSL: KafkaSecurityProtocol
SSL: KafkaSecurityProtocol
MECHANISM_UNSPECIFIED: KafkaSaslMechanism
PLAIN: KafkaSaslMechanism
SCRAM_SHA_256: KafkaSaslMechanism
SCRAM_SHA_512: KafkaSaslMechanism
SSL_MODE_UNSPECIFIED: PostgresSslMode
DISABLED: PostgresSslMode
PREFERRED: PostgresSslMode
REQUIRED: PostgresSslMode
VERIFY_CA: PostgresSslMode
VERIFY_FULL: PostgresSslMode
FORMAT_UNSPECIFIED: SchemaFormat
AVRO: SchemaFormat
PROTOBUF: SchemaFormat
JSON: SchemaFormat
LOCATION_UNSPECIFIED: SchemaLocation
WEB_LOCATION: SchemaLocation
SCHEMA_REGISTRY: SchemaLocation
S3: SchemaLocation

class MetaNodeBackupRequest(_message.Message):
    __slots__ = ("resource_meta", "rw_name", "rw_namespace")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    rw_name: str
    rw_namespace: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ...) -> None: ...

class MetaNodeBackupResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class ValidateSourceRequest(_message.Message):
    __slots__ = ("rw_name", "rw_namespace", "props")
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    PROPS_FIELD_NUMBER: _ClassVar[int]
    rw_name: str
    rw_namespace: str
    props: str
    def __init__(self, rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ..., props: _Optional[str] = ...) -> None: ...

class ValidateSourceResponse(_message.Message):
    __slots__ = ("error_message",)
    ERROR_MESSAGE_FIELD_NUMBER: _ClassVar[int]
    error_message: str
    def __init__(self, error_message: _Optional[str] = ...) -> None: ...

class GetClusterInfoRequest(_message.Message):
    __slots__ = ("rw_name", "rw_namespace")
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    rw_name: str
    rw_namespace: str
    def __init__(self, rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ...) -> None: ...

class GetClusterInfoResponse(_message.Message):
    __slots__ = ("output",)
    OUTPUT_FIELD_NUMBER: _ClassVar[int]
    output: str
    def __init__(self, output: _Optional[str] = ...) -> None: ...

class CordonWorkersRequest(_message.Message):
    __slots__ = ("rw_name", "rw_namespace", "work_ids")
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    WORK_IDS_FIELD_NUMBER: _ClassVar[int]
    rw_name: str
    rw_namespace: str
    work_ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ..., work_ids: _Optional[_Iterable[str]] = ...) -> None: ...

class CordonWorkersResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class ResizeWorkersRequest(_message.Message):
    __slots__ = ("rw_name", "rw_namespace", "added_work_ids", "deleting_work_ids")
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    ADDED_WORK_IDS_FIELD_NUMBER: _ClassVar[int]
    DELETING_WORK_IDS_FIELD_NUMBER: _ClassVar[int]
    rw_name: str
    rw_namespace: str
    added_work_ids: _containers.RepeatedScalarFieldContainer[str]
    deleting_work_ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ..., added_work_ids: _Optional[_Iterable[str]] = ..., deleting_work_ids: _Optional[_Iterable[str]] = ...) -> None: ...

class ResizeWorkersResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteWorkersRequest(_message.Message):
    __slots__ = ("rw_name", "rw_namespace", "work_ids")
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    WORK_IDS_FIELD_NUMBER: _ClassVar[int]
    rw_name: str
    rw_namespace: str
    work_ids: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ..., work_ids: _Optional[_Iterable[str]] = ...) -> None: ...

class DeleteWorkersResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class DeleteSnapshotRequest(_message.Message):
    __slots__ = ("rw_name", "rw_namespace", "snapshot_id", "rw_version")
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    SNAPSHOT_ID_FIELD_NUMBER: _ClassVar[int]
    RW_VERSION_FIELD_NUMBER: _ClassVar[int]
    rw_name: str
    rw_namespace: str
    snapshot_id: int
    rw_version: str
    def __init__(self, rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ..., snapshot_id: _Optional[int] = ..., rw_version: _Optional[str] = ...) -> None: ...

class DeleteSnapshotResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class RestoreMetaRequestEtcd(_message.Message):
    __slots__ = ("etcd_endpoints", "etcd_auth", "etcd_username", "etcd_password")
    ETCD_ENDPOINTS_FIELD_NUMBER: _ClassVar[int]
    ETCD_AUTH_FIELD_NUMBER: _ClassVar[int]
    ETCD_USERNAME_FIELD_NUMBER: _ClassVar[int]
    ETCD_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    etcd_endpoints: str
    etcd_auth: bool
    etcd_username: str
    etcd_password: str
    def __init__(self, etcd_endpoints: _Optional[str] = ..., etcd_auth: bool = ..., etcd_username: _Optional[str] = ..., etcd_password: _Optional[str] = ...) -> None: ...

class RestoreMetaRequestSql(_message.Message):
    __slots__ = ("sql_endpoint",)
    SQL_ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    sql_endpoint: str
    def __init__(self, sql_endpoint: _Optional[str] = ...) -> None: ...

class RestoreMetaRequest(_message.Message):
    __slots__ = ("resource_meta", "service_account", "rw_image_tag", "meta_snapshot_id", "meta_store_type", "backup_storage_url", "backup_storage_dir", "hummock_storage_url", "hummock_storage_dir", "etcd_config", "sql_config", "envs")
    class EnvsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    RW_IMAGE_TAG_FIELD_NUMBER: _ClassVar[int]
    META_SNAPSHOT_ID_FIELD_NUMBER: _ClassVar[int]
    META_STORE_TYPE_FIELD_NUMBER: _ClassVar[int]
    BACKUP_STORAGE_URL_FIELD_NUMBER: _ClassVar[int]
    BACKUP_STORAGE_DIR_FIELD_NUMBER: _ClassVar[int]
    HUMMOCK_STORAGE_URL_FIELD_NUMBER: _ClassVar[int]
    HUMMOCK_STORAGE_DIR_FIELD_NUMBER: _ClassVar[int]
    ETCD_CONFIG_FIELD_NUMBER: _ClassVar[int]
    SQL_CONFIG_FIELD_NUMBER: _ClassVar[int]
    ENVS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    service_account: str
    rw_image_tag: str
    meta_snapshot_id: int
    meta_store_type: str
    backup_storage_url: str
    backup_storage_dir: str
    hummock_storage_url: str
    hummock_storage_dir: str
    etcd_config: RestoreMetaRequestEtcd
    sql_config: RestoreMetaRequestSql
    envs: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., service_account: _Optional[str] = ..., rw_image_tag: _Optional[str] = ..., meta_snapshot_id: _Optional[int] = ..., meta_store_type: _Optional[str] = ..., backup_storage_url: _Optional[str] = ..., backup_storage_dir: _Optional[str] = ..., hummock_storage_url: _Optional[str] = ..., hummock_storage_dir: _Optional[str] = ..., etcd_config: _Optional[_Union[RestoreMetaRequestEtcd, _Mapping]] = ..., sql_config: _Optional[_Union[RestoreMetaRequestSql, _Mapping]] = ..., envs: _Optional[_Mapping[str, str]] = ...) -> None: ...

class RestoreMetaResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class VacuumEtcdMetaRequest(_message.Message):
    __slots__ = ("pod_name", "pod_namespace", "etcd_username", "etcd_password")
    POD_NAME_FIELD_NUMBER: _ClassVar[int]
    POD_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    ETCD_USERNAME_FIELD_NUMBER: _ClassVar[int]
    ETCD_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    pod_name: str
    pod_namespace: str
    etcd_username: str
    etcd_password: str
    def __init__(self, pod_name: _Optional[str] = ..., pod_namespace: _Optional[str] = ..., etcd_username: _Optional[str] = ..., etcd_password: _Optional[str] = ...) -> None: ...

class VacuumEtcdMetaResponse(_message.Message):
    __slots__ = ()
    def __init__(self) -> None: ...

class GenDiagnosisReportRequest(_message.Message):
    __slots__ = ("service_name", "namespace")
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    service_name: str
    namespace: str
    def __init__(self, service_name: _Optional[str] = ..., namespace: _Optional[str] = ...) -> None: ...

class GenDiagnosisReportResponse(_message.Message):
    __slots__ = ("report",)
    REPORT_FIELD_NUMBER: _ClassVar[int]
    report: str
    def __init__(self, report: _Optional[str] = ...) -> None: ...

class GenDiagnosisReportStreamRequest(_message.Message):
    __slots__ = ("namespace", "service_name", "gzip_compressed")
    NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    SERVICE_NAME_FIELD_NUMBER: _ClassVar[int]
    GZIP_COMPRESSED_FIELD_NUMBER: _ClassVar[int]
    namespace: str
    service_name: str
    gzip_compressed: bool
    def __init__(self, namespace: _Optional[str] = ..., service_name: _Optional[str] = ..., gzip_compressed: bool = ...) -> None: ...

class GenDiagnosisReportStreamResponse(_message.Message):
    __slots__ = ("report_chunk",)
    REPORT_CHUNK_FIELD_NUMBER: _ClassVar[int]
    report_chunk: bytes
    def __init__(self, report_chunk: _Optional[bytes] = ...) -> None: ...

class MetaMigrationRequest(_message.Message):
    __slots__ = ("resource_meta", "rw_name", "rw_namespace", "etcd_endpoints", "sql_endpoint", "task_image", "task_resources", "task_tolerations", "task_affinity")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    RW_NAME_FIELD_NUMBER: _ClassVar[int]
    RW_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    ETCD_ENDPOINTS_FIELD_NUMBER: _ClassVar[int]
    SQL_ENDPOINT_FIELD_NUMBER: _ClassVar[int]
    TASK_IMAGE_FIELD_NUMBER: _ClassVar[int]
    TASK_RESOURCES_FIELD_NUMBER: _ClassVar[int]
    TASK_TOLERATIONS_FIELD_NUMBER: _ClassVar[int]
    TASK_AFFINITY_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    rw_name: str
    rw_namespace: str
    etcd_endpoints: str
    sql_endpoint: str
    task_image: str
    task_resources: _k8s_pb2.ResourceRequirements
    task_tolerations: _containers.RepeatedCompositeFieldContainer[_k8s_pb2.Toleration]
    task_affinity: _k8s_pb2.Affinity
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., rw_name: _Optional[str] = ..., rw_namespace: _Optional[str] = ..., etcd_endpoints: _Optional[str] = ..., sql_endpoint: _Optional[str] = ..., task_image: _Optional[str] = ..., task_resources: _Optional[_Union[_k8s_pb2.ResourceRequirements, _Mapping]] = ..., task_tolerations: _Optional[_Iterable[_Union[_k8s_pb2.Toleration, _Mapping]]] = ..., task_affinity: _Optional[_Union[_k8s_pb2.Affinity, _Mapping]] = ...) -> None: ...

class MetaMigrationResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class KafkaConfig(_message.Message):
    __slots__ = ("server", "security_protocol", "sasl_mechanism", "sasl_username", "sasl_password", "ca_certificate")
    SERVER_FIELD_NUMBER: _ClassVar[int]
    SECURITY_PROTOCOL_FIELD_NUMBER: _ClassVar[int]
    SASL_MECHANISM_FIELD_NUMBER: _ClassVar[int]
    SASL_USERNAME_FIELD_NUMBER: _ClassVar[int]
    SASL_PASSWORD_FIELD_NUMBER: _ClassVar[int]
    CA_CERTIFICATE_FIELD_NUMBER: _ClassVar[int]
    server: str
    security_protocol: KafkaSecurityProtocol
    sasl_mechanism: KafkaSaslMechanism
    sasl_username: str
    sasl_password: str
    ca_certificate: str
    def __init__(self, server: _Optional[str] = ..., security_protocol: _Optional[_Union[KafkaSecurityProtocol, str]] = ..., sasl_mechanism: _Optional[_Union[KafkaSaslMechanism, str]] = ..., sasl_username: _Optional[str] = ..., sasl_password: _Optional[str] = ..., ca_certificate: _Optional[str] = ...) -> None: ...

class FetchKafkaTopicRequest(_message.Message):
    __slots__ = ("kafka",)
    KAFKA_FIELD_NUMBER: _ClassVar[int]
    kafka: KafkaConfig
    def __init__(self, kafka: _Optional[_Union[KafkaConfig, _Mapping]] = ...) -> None: ...

class FetchKafkaTopicResponse(_message.Message):
    __slots__ = ("topics",)
    TOPICS_FIELD_NUMBER: _ClassVar[int]
    topics: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, topics: _Optional[_Iterable[str]] = ...) -> None: ...

class FetchKafkaMessageRequest(_message.Message):
    __slots__ = ("kafka", "topic")
    KAFKA_FIELD_NUMBER: _ClassVar[int]
    TOPIC_FIELD_NUMBER: _ClassVar[int]
    kafka: KafkaConfig
    topic: str
    def __init__(self, kafka: _Optional[_Union[KafkaConfig, _Mapping]] = ..., topic: _Optional[str] = ...) -> None: ...

class FetchKafkaMessageResponse(_message.Message):
    __slots__ = ("key", "value")
    KEY_FIELD_NUMBER: _ClassVar[int]
    VALUE_FIELD_NUMBER: _ClassVar[int]
    key: bytes
    value: bytes
    def __init__(self, key: _Optional[bytes] = ..., value: _Optional[bytes] = ...) -> None: ...

class PostgresConfig(_message.Message):
    __slots__ = ("hostname", "port", "username", "password", "database", "ssl_mode")
    HOSTNAME_FIELD_NUMBER: _ClassVar[int]
    PORT_FIELD_NUMBER: _ClassVar[int]
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    PASSWORD_FIELD_NUMBER: _ClassVar[int]
    DATABASE_FIELD_NUMBER: _ClassVar[int]
    SSL_MODE_FIELD_NUMBER: _ClassVar[int]
    hostname: str
    port: int
    username: str
    password: str
    database: str
    ssl_mode: PostgresSslMode
    def __init__(self, hostname: _Optional[str] = ..., port: _Optional[int] = ..., username: _Optional[str] = ..., password: _Optional[str] = ..., database: _Optional[str] = ..., ssl_mode: _Optional[_Union[PostgresSslMode, str]] = ...) -> None: ...

class FetchPostgresTableRequest(_message.Message):
    __slots__ = ("postgres",)
    POSTGRES_FIELD_NUMBER: _ClassVar[int]
    postgres: PostgresConfig
    def __init__(self, postgres: _Optional[_Union[PostgresConfig, _Mapping]] = ...) -> None: ...

class FetchPostgresTableResponse(_message.Message):
    __slots__ = ("tables",)
    TABLES_FIELD_NUMBER: _ClassVar[int]
    tables: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, tables: _Optional[_Iterable[str]] = ...) -> None: ...

class SchemaSchemaRegistryConfig(_message.Message):
    __slots__ = ("topic", "username", "password")
    TOPIC_FIELD_NUMBER: _ClassVar[int]
    USERNAME_FIELD_NUMBER: _ClassVar[int]
    PASSWORD_FIELD_NUMBER: _ClassVar[int]
    topic: str
    username: str
    password: str
    def __init__(self, topic: _Optional[str] = ..., username: _Optional[str] = ..., password: _Optional[str] = ...) -> None: ...

class SchemaS3Config(_message.Message):
    __slots__ = ("region", "accessKeyId", "secretAccessKey")
    REGION_FIELD_NUMBER: _ClassVar[int]
    ACCESSKEYID_FIELD_NUMBER: _ClassVar[int]
    SECRETACCESSKEY_FIELD_NUMBER: _ClassVar[int]
    region: str
    accessKeyId: str
    secretAccessKey: str
    def __init__(self, region: _Optional[str] = ..., accessKeyId: _Optional[str] = ..., secretAccessKey: _Optional[str] = ...) -> None: ...

class FetchSourceSchemaRequest(_message.Message):
    __slots__ = ("format", "location", "url", "schema_registry", "s3")
    FORMAT_FIELD_NUMBER: _ClassVar[int]
    LOCATION_FIELD_NUMBER: _ClassVar[int]
    URL_FIELD_NUMBER: _ClassVar[int]
    SCHEMA_REGISTRY_FIELD_NUMBER: _ClassVar[int]
    S3_FIELD_NUMBER: _ClassVar[int]
    format: SchemaFormat
    location: SchemaLocation
    url: str
    schema_registry: SchemaSchemaRegistryConfig
    s3: SchemaS3Config
    def __init__(self, format: _Optional[_Union[SchemaFormat, str]] = ..., location: _Optional[_Union[SchemaLocation, str]] = ..., url: _Optional[str] = ..., schema_registry: _Optional[_Union[SchemaSchemaRegistryConfig, _Mapping]] = ..., s3: _Optional[_Union[SchemaS3Config, _Mapping]] = ...) -> None: ...

class FetchSourceSchemaResponse(_message.Message):
    __slots__ = ("files",)
    FILES_FIELD_NUMBER: _ClassVar[int]
    files: _containers.RepeatedCompositeFieldContainer[RawSchemaFile]
    def __init__(self, files: _Optional[_Iterable[_Union[RawSchemaFile, _Mapping]]] = ...) -> None: ...

class RawSchemaFile(_message.Message):
    __slots__ = ("content", "name")
    CONTENT_FIELD_NUMBER: _ClassVar[int]
    NAME_FIELD_NUMBER: _ClassVar[int]
    content: bytes
    name: str
    def __init__(self, content: _Optional[bytes] = ..., name: _Optional[str] = ...) -> None: ...
