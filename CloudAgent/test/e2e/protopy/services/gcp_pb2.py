# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/gcp.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/gcp.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import deletion_pb2 as common_dot_deletion__pb2
from common import update_pb2 as common_dot_update__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import k8s_pb2 as common_dot_k8s__pb2
from common import gcp_pb2 as common_dot_gcp__pb2
from services.common import data_pb2 as services_dot_common_dot_data__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12services/gcp.proto\x12\x0cservices.gcp\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/deletion.proto\x1a\x13\x63ommon/update.proto\x1a\x15\x63ommon/resource.proto\x1a\x10\x63ommon/k8s.proto\x1a\x10\x63ommon/gcp.proto\x1a\x1aservices/common/data.proto\"\xa4\x01\n!CreateIAMPolicyRoleBindingRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12 \n\x18IAM_service_account_name\x18\x02 \x01(\t\x12/\n\x0crole_binding\x18\x03 \x01(\x0b\x32\x19.services.gcp.RoleBinding\"V\n\"CreateIAMPolicyRoleBindingResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"N\n\x1eGetIAMPolicyRoleBindingRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"J\n\x1fGetIAMPolicyRoleBindingResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"Q\n!DeleteIAMPolicyRoleBindingRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"V\n\"DeleteIAMPolicyRoleBindingResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xa7\x01\n CreateIAMPolicyKSABindingRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12 \n\x18IAM_service_account_name\x18\x02 \x01(\t\x12\x33\n\x0fservice_account\x18\x03 \x01(\x0b\x32\x1a.common.k8s.ServiceAccount\"U\n!CreateIAMPolicyKSABindingResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"M\n\x1dGetIAMPolicyKSABindingRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"I\n\x1eGetIAMPolicyKSABindingResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"P\n DeleteIAMPolicyKSABindingRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"U\n!DeleteIAMPolicyKSABindingResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"N\n\x1e\x43reateIAMServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"S\n\x1f\x43reateIAMServiceAccountResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"K\n\x1bGetIAMServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"^\n\x1cGetIAMServiceAccountResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x15\n\raccount_email\x18\x02 \x01(\t\"N\n\x1e\x44\x65leteIAMServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"S\n\x1f\x44\x65leteIAMServiceAccountResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"Z\n\x0bRoleBinding\x12:\n\x11gcs_access_option\x18\x01 \x01(\x0b\x32\x1d.services.gcp.GCSAccessOptionH\x00\x42\x0f\n\raccess_option\"e\n\x0fGCSAccessOption\x12\x0e\n\x06\x62ucket\x18\x01 \x01(\t\x12\x0f\n\x03\x64ir\x18\x02 \x01(\tB\x02\x18\x01\x12\x0c\n\x04\x64irs\x18\x04 \x03(\t\x12#\n\x04role\x18\x03 \x01(\x0e\x32\x15.services.gcp.GCSRole\"Y\n\x16\x43reateIPAddressRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x11\n\tip_subnet\x18\x02 \x01(\t\"K\n\x17\x43reateIPAddressResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"C\n\x13GetIPAddressRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"h\n\x14GetIPAddressResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x13\n\x0bip_selflink\x18\x02 \x01(\t\x12\x12\n\nip_address\x18\x03 \x01(\t\"F\n\x16\x44\x65leteIPAddressRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"K\n\x17\x44\x65leteIPAddressResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\x9b\x02\n*CreatePrivateServiceConnectEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x1a\n\x12private_service_ip\x18\x02 \x01(\t\x12\x0e\n\x06target\x18\x03 \x01(\t\x12_\n\x0c\x65xtra_labels\x18\x04 \x03(\x0b\x32I.services.gcp.CreatePrivateServiceConnectEndpointRequest.ExtraLabelsEntry\x1a\x32\n\x10\x45xtraLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"_\n+CreatePrivateServiceConnectEndpointResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"W\n\'GetPrivateServiceConnectEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\x98\x01\n(GetPrivateServiceConnectEndpointResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12+\n\npsc_status\x18\x02 \x01(\x0e\x32\x17.services.gcp.PscStatus\x12\x16\n\nip_address\x18\x03 \x01(\tB\x02\x18\x01\"Z\n*DeletePrivateServiceConnectEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"_\n+DeletePrivateServiceConnectEndpointResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xe6\x01\n\x18\x43reateSQLInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12)\n\x04spec\x18\x02 \x01(\x0b\x32\x1b.common.gcp.SQLInstanceSpec\x12\x42\n\x06labels\x18\x03 \x03(\x0b\x32\x32.services.gcp.CreateSQLInstanceRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"M\n\x19\x43reateSQLInstanceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"H\n\x18\x44\x65leteSQLInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"M\n\x19\x44\x65leteSQLInstanceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"G\n\x17StartSQLInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"J\n\x18StartSQLInstanceResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"F\n\x16StopSQLInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"I\n\x17StopSQLInstanceResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"E\n\x15GetSQLInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xb1\x01\n\x16GetSQLInstanceResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x1f\n\x12private_ip_address\x18\x02 \x01(\tH\x00\x88\x01\x01\x12\x36\n\x0finstance_status\x18\x03 \x01(\x0e\x32\x1d.common.gcp.SQLInstanceStatusB\x15\n\x13_private_ip_address*;\n\x07GCSRole\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x10\n\x0cOBJECT_ADMIN\x10\x01\x12\x11\n\rOBJECT_VIEWER\x10\x02*X\n\tPscStatus\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\x0b\n\x07PENDING\x10\x01\x12\x0c\n\x08\x41\x43\x43\x45PTED\x10\x02\x12\x0c\n\x08REJECTED\x10\x03\x12\n\n\x06\x43LOSED\x10\x04\x32\xd2\x15\n\x12GcpResourceManager\x12\x92\x01\n\x1f\x43reateDataDirectoryDeletionTask\x12\x35.services.data.CreateDataDirectoryDeletionTaskRequest\x1a\x36.services.data.CreateDataDirectoryDeletionTaskResponse\"\x00\x12\x89\x01\n\x1c\x43reateDataDirectoryCloneTask\x12\x32.services.data.CreateDataDirectoryCloneTaskRequest\x1a\x33.services.data.CreateDataDirectoryCloneTaskResponse\"\x00\x12\x81\x01\n\x1a\x43reateIAMPolicyRoleBinding\x12/.services.gcp.CreateIAMPolicyRoleBindingRequest\x1a\x30.services.gcp.CreateIAMPolicyRoleBindingResponse\"\x00\x12\x81\x01\n\x1a\x44\x65leteIAMPolicyRoleBinding\x12/.services.gcp.DeleteIAMPolicyRoleBindingRequest\x1a\x30.services.gcp.DeleteIAMPolicyRoleBindingResponse\"\x00\x12x\n\x17GetIAMPolicyRoleBinding\x12,.services.gcp.GetIAMPolicyRoleBindingRequest\x1a-.services.gcp.GetIAMPolicyRoleBindingResponse\"\x00\x12~\n\x19\x43reateIAMPolicyKSABinding\x12..services.gcp.CreateIAMPolicyKSABindingRequest\x1a/.services.gcp.CreateIAMPolicyKSABindingResponse\"\x00\x12~\n\x19\x44\x65leteIAMPolicyKSABinding\x12..services.gcp.DeleteIAMPolicyKSABindingRequest\x1a/.services.gcp.DeleteIAMPolicyKSABindingResponse\"\x00\x12u\n\x16GetIAMPolicyKSABinding\x12+.services.gcp.GetIAMPolicyKSABindingRequest\x1a,.services.gcp.GetIAMPolicyKSABindingResponse\"\x00\x12x\n\x17\x43reateIAMServiceAccount\x12,.services.gcp.CreateIAMServiceAccountRequest\x1a-.services.gcp.CreateIAMServiceAccountResponse\"\x00\x12x\n\x17\x44\x65leteIAMServiceAccount\x12,.services.gcp.DeleteIAMServiceAccountRequest\x1a-.services.gcp.DeleteIAMServiceAccountResponse\"\x00\x12o\n\x14GetIAMServiceAccount\x12).services.gcp.GetIAMServiceAccountRequest\x1a*.services.gcp.GetIAMServiceAccountResponse\"\x00\x12`\n\x0f\x43reateIPAddress\x12$.services.gcp.CreateIPAddressRequest\x1a%.services.gcp.CreateIPAddressResponse\"\x00\x12`\n\x0f\x44\x65leteIPAddress\x12$.services.gcp.DeleteIPAddressRequest\x1a%.services.gcp.DeleteIPAddressResponse\"\x00\x12W\n\x0cGetIPAddress\x12!.services.gcp.GetIPAddressRequest\x1a\".services.gcp.GetIPAddressResponse\"\x00\x12\x9c\x01\n#CreatePrivateServiceConnectEndpoint\x12\x38.services.gcp.CreatePrivateServiceConnectEndpointRequest\x1a\x39.services.gcp.CreatePrivateServiceConnectEndpointResponse\"\x00\x12\x9c\x01\n#DeletePrivateServiceConnectEndpoint\x12\x38.services.gcp.DeletePrivateServiceConnectEndpointRequest\x1a\x39.services.gcp.DeletePrivateServiceConnectEndpointResponse\"\x00\x12\x93\x01\n GetPrivateServiceConnectEndpoint\x12\x35.services.gcp.GetPrivateServiceConnectEndpointRequest\x1a\x36.services.gcp.GetPrivateServiceConnectEndpointResponse\"\x00\x12\x66\n\x11\x43reateSQLInstance\x12&.services.gcp.CreateSQLInstanceRequest\x1a\'.services.gcp.CreateSQLInstanceResponse\"\x00\x12\x66\n\x11\x44\x65leteSQLInstance\x12&.services.gcp.DeleteSQLInstanceRequest\x1a\'.services.gcp.DeleteSQLInstanceResponse\"\x00\x12\x63\n\x10StartSQLInstance\x12%.services.gcp.StartSQLInstanceRequest\x1a&.services.gcp.StartSQLInstanceResponse\"\x00\x12`\n\x0fStopSQLInstance\x12$.services.gcp.StopSQLInstanceRequest\x1a%.services.gcp.StopSQLInstanceResponse\"\x00\x12]\n\x0eGetSQLInstance\x12#.services.gcp.GetSQLInstanceRequest\x1a$.services.gcp.GetSQLInstanceResponse\"\x00\x12V\n\x0bGetManifest\x12!.services.data.GetManifestRequest\x1a\".services.data.GetManifestResponse\"\x00\x42\x39Z7github.com/risingwavelabs/cloudagent/pbgen/services/gcpb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.gcp_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z7github.com/risingwavelabs/cloudagent/pbgen/services/gcp'
  _globals['_GCSACCESSOPTION'].fields_by_name['dir']._loaded_options = None
  _globals['_GCSACCESSOPTION'].fields_by_name['dir']._serialized_options = b'\030\001'
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTREQUEST_EXTRALABELSENTRY']._loaded_options = None
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTREQUEST_EXTRALABELSENTRY']._serialized_options = b'8\001'
  _globals['_GETPRIVATESERVICECONNECTENDPOINTRESPONSE'].fields_by_name['ip_address']._loaded_options = None
  _globals['_GETPRIVATESERVICECONNECTENDPOINTRESPONSE'].fields_by_name['ip_address']._serialized_options = b'\030\001'
  _globals['_CREATESQLINSTANCEREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATESQLINSTANCEREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_GCSROLE']._serialized_start=4370
  _globals['_GCSROLE']._serialized_end=4429
  _globals['_PSCSTATUS']._serialized_start=4431
  _globals['_PSCSTATUS']._serialized_end=4519
  _globals['_CREATEIAMPOLICYROLEBINDINGREQUEST']._serialized_start=191
  _globals['_CREATEIAMPOLICYROLEBINDINGREQUEST']._serialized_end=355
  _globals['_CREATEIAMPOLICYROLEBINDINGRESPONSE']._serialized_start=357
  _globals['_CREATEIAMPOLICYROLEBINDINGRESPONSE']._serialized_end=443
  _globals['_GETIAMPOLICYROLEBINDINGREQUEST']._serialized_start=445
  _globals['_GETIAMPOLICYROLEBINDINGREQUEST']._serialized_end=523
  _globals['_GETIAMPOLICYROLEBINDINGRESPONSE']._serialized_start=525
  _globals['_GETIAMPOLICYROLEBINDINGRESPONSE']._serialized_end=599
  _globals['_DELETEIAMPOLICYROLEBINDINGREQUEST']._serialized_start=601
  _globals['_DELETEIAMPOLICYROLEBINDINGREQUEST']._serialized_end=682
  _globals['_DELETEIAMPOLICYROLEBINDINGRESPONSE']._serialized_start=684
  _globals['_DELETEIAMPOLICYROLEBINDINGRESPONSE']._serialized_end=770
  _globals['_CREATEIAMPOLICYKSABINDINGREQUEST']._serialized_start=773
  _globals['_CREATEIAMPOLICYKSABINDINGREQUEST']._serialized_end=940
  _globals['_CREATEIAMPOLICYKSABINDINGRESPONSE']._serialized_start=942
  _globals['_CREATEIAMPOLICYKSABINDINGRESPONSE']._serialized_end=1027
  _globals['_GETIAMPOLICYKSABINDINGREQUEST']._serialized_start=1029
  _globals['_GETIAMPOLICYKSABINDINGREQUEST']._serialized_end=1106
  _globals['_GETIAMPOLICYKSABINDINGRESPONSE']._serialized_start=1108
  _globals['_GETIAMPOLICYKSABINDINGRESPONSE']._serialized_end=1181
  _globals['_DELETEIAMPOLICYKSABINDINGREQUEST']._serialized_start=1183
  _globals['_DELETEIAMPOLICYKSABINDINGREQUEST']._serialized_end=1263
  _globals['_DELETEIAMPOLICYKSABINDINGRESPONSE']._serialized_start=1265
  _globals['_DELETEIAMPOLICYKSABINDINGRESPONSE']._serialized_end=1350
  _globals['_CREATEIAMSERVICEACCOUNTREQUEST']._serialized_start=1352
  _globals['_CREATEIAMSERVICEACCOUNTREQUEST']._serialized_end=1430
  _globals['_CREATEIAMSERVICEACCOUNTRESPONSE']._serialized_start=1432
  _globals['_CREATEIAMSERVICEACCOUNTRESPONSE']._serialized_end=1515
  _globals['_GETIAMSERVICEACCOUNTREQUEST']._serialized_start=1517
  _globals['_GETIAMSERVICEACCOUNTREQUEST']._serialized_end=1592
  _globals['_GETIAMSERVICEACCOUNTRESPONSE']._serialized_start=1594
  _globals['_GETIAMSERVICEACCOUNTRESPONSE']._serialized_end=1688
  _globals['_DELETEIAMSERVICEACCOUNTREQUEST']._serialized_start=1690
  _globals['_DELETEIAMSERVICEACCOUNTREQUEST']._serialized_end=1768
  _globals['_DELETEIAMSERVICEACCOUNTRESPONSE']._serialized_start=1770
  _globals['_DELETEIAMSERVICEACCOUNTRESPONSE']._serialized_end=1853
  _globals['_ROLEBINDING']._serialized_start=1855
  _globals['_ROLEBINDING']._serialized_end=1945
  _globals['_GCSACCESSOPTION']._serialized_start=1947
  _globals['_GCSACCESSOPTION']._serialized_end=2048
  _globals['_CREATEIPADDRESSREQUEST']._serialized_start=2050
  _globals['_CREATEIPADDRESSREQUEST']._serialized_end=2139
  _globals['_CREATEIPADDRESSRESPONSE']._serialized_start=2141
  _globals['_CREATEIPADDRESSRESPONSE']._serialized_end=2216
  _globals['_GETIPADDRESSREQUEST']._serialized_start=2218
  _globals['_GETIPADDRESSREQUEST']._serialized_end=2285
  _globals['_GETIPADDRESSRESPONSE']._serialized_start=2287
  _globals['_GETIPADDRESSRESPONSE']._serialized_end=2391
  _globals['_DELETEIPADDRESSREQUEST']._serialized_start=2393
  _globals['_DELETEIPADDRESSREQUEST']._serialized_end=2463
  _globals['_DELETEIPADDRESSRESPONSE']._serialized_start=2465
  _globals['_DELETEIPADDRESSRESPONSE']._serialized_end=2540
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTREQUEST']._serialized_start=2543
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTREQUEST']._serialized_end=2826
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTREQUEST_EXTRALABELSENTRY']._serialized_start=2776
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTREQUEST_EXTRALABELSENTRY']._serialized_end=2826
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTRESPONSE']._serialized_start=2828
  _globals['_CREATEPRIVATESERVICECONNECTENDPOINTRESPONSE']._serialized_end=2923
  _globals['_GETPRIVATESERVICECONNECTENDPOINTREQUEST']._serialized_start=2925
  _globals['_GETPRIVATESERVICECONNECTENDPOINTREQUEST']._serialized_end=3012
  _globals['_GETPRIVATESERVICECONNECTENDPOINTRESPONSE']._serialized_start=3015
  _globals['_GETPRIVATESERVICECONNECTENDPOINTRESPONSE']._serialized_end=3167
  _globals['_DELETEPRIVATESERVICECONNECTENDPOINTREQUEST']._serialized_start=3169
  _globals['_DELETEPRIVATESERVICECONNECTENDPOINTREQUEST']._serialized_end=3259
  _globals['_DELETEPRIVATESERVICECONNECTENDPOINTRESPONSE']._serialized_start=3261
  _globals['_DELETEPRIVATESERVICECONNECTENDPOINTRESPONSE']._serialized_end=3356
  _globals['_CREATESQLINSTANCEREQUEST']._serialized_start=3359
  _globals['_CREATESQLINSTANCEREQUEST']._serialized_end=3589
  _globals['_CREATESQLINSTANCEREQUEST_LABELSENTRY']._serialized_start=3544
  _globals['_CREATESQLINSTANCEREQUEST_LABELSENTRY']._serialized_end=3589
  _globals['_CREATESQLINSTANCERESPONSE']._serialized_start=3591
  _globals['_CREATESQLINSTANCERESPONSE']._serialized_end=3668
  _globals['_DELETESQLINSTANCEREQUEST']._serialized_start=3670
  _globals['_DELETESQLINSTANCEREQUEST']._serialized_end=3742
  _globals['_DELETESQLINSTANCERESPONSE']._serialized_start=3744
  _globals['_DELETESQLINSTANCERESPONSE']._serialized_end=3821
  _globals['_STARTSQLINSTANCEREQUEST']._serialized_start=3823
  _globals['_STARTSQLINSTANCEREQUEST']._serialized_end=3894
  _globals['_STARTSQLINSTANCERESPONSE']._serialized_start=3896
  _globals['_STARTSQLINSTANCERESPONSE']._serialized_end=3970
  _globals['_STOPSQLINSTANCEREQUEST']._serialized_start=3972
  _globals['_STOPSQLINSTANCEREQUEST']._serialized_end=4042
  _globals['_STOPSQLINSTANCERESPONSE']._serialized_start=4044
  _globals['_STOPSQLINSTANCERESPONSE']._serialized_end=4117
  _globals['_GETSQLINSTANCEREQUEST']._serialized_start=4119
  _globals['_GETSQLINSTANCEREQUEST']._serialized_end=4188
  _globals['_GETSQLINSTANCERESPONSE']._serialized_start=4191
  _globals['_GETSQLINSTANCERESPONSE']._serialized_end=4368
  _globals['_GCPRESOURCEMANAGER']._serialized_start=4522
  _globals['_GCPRESOURCEMANAGER']._serialized_end=7292
# @@protoc_insertion_point(module_scope)
