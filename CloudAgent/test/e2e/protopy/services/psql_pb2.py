# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/psql.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/psql.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import deletion_pb2 as common_dot_deletion__pb2
from common import resource_pb2 as common_dot_resource__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x13services/psql.proto\x12\rservices.psql\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/deletion.proto\x1a\x15\x63ommon/resource.proto\"^\n\nConnection\x12\x0c\n\x04host\x18\x01 \x01(\t\x12\x0c\n\x04port\x18\x02 \x01(\r\x12\x10\n\x08\x64\x61tabase\x18\x03 \x01(\t\x12\x10\n\x08username\x18\x04 \x01(\t\x12\x10\n\x08password\x18\x05 \x01(\t\"X\n\x15\x43reateDatabaseRequest\x12-\n\nconnection\x18\x01 \x01(\x0b\x32\x19.services.psql.Connection\x12\x10\n\x08\x64\x61tabase\x18\x02 \x01(\t\"J\n\x16\x43reateDatabaseResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"X\n\x15\x44\x65leteDatabaseRequest\x12-\n\nconnection\x18\x01 \x01(\x0b\x32\x19.services.psql.Connection\x12\x10\n\x08\x64\x61tabase\x18\x02 \x01(\t\"J\n\x16\x44\x65leteDatabaseResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\x84\x01\n\x11\x43reateUserRequest\x12-\n\nconnection\x18\x01 \x01(\x0b\x32\x19.services.psql.Connection\x12\x10\n\x08username\x18\x02 \x01(\t\x12\x10\n\x08password\x18\x03 \x01(\t\x12\x1c\n\x14privileged_databases\x18\x04 \x03(\t\"F\n\x12\x43reateUserResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"T\n\x11\x44\x65leteUserRequest\x12-\n\nconnection\x18\x01 \x01(\x0b\x32\x19.services.psql.Connection\x12\x10\n\x08username\x18\x02 \x01(\t\"F\n\x12\x44\x65leteUserResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"F\n\x15TruncateTablesRequest\x12-\n\nconnection\x18\x01 \x01(\x0b\x32\x19.services.psql.Connection\"\x18\n\x16TruncateTablesResponse\"\xe6\x01\n\x1dUpdateSystemParametersRequest\x12-\n\nconnection\x18\x01 \x01(\x0b\x32\x19.services.psql.Connection\x12]\n\x11system_parameters\x18\x02 \x03(\x0b\x32\x42.services.psql.UpdateSystemParametersRequest.SystemParametersEntry\x1a\x37\n\x15SystemParametersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\" \n\x1eUpdateSystemParametersResponse\"\xb0\x01\n\x14\x43loneDatabaseRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x34\n\x11source_connection\x18\x02 \x01(\x0b\x32\x19.services.psql.Connection\x12\x34\n\x11target_connection\x18\x03 \x01(\x0b\x32\x19.services.psql.Connection\"I\n\x15\x43loneDatabaseResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status2\xb1\x05\n\x0bPsqlManager\x12_\n\x0e\x43reateDatabase\x12$.services.psql.CreateDatabaseRequest\x1a%.services.psql.CreateDatabaseResponse\"\x00\x12_\n\x0e\x44\x65leteDatabase\x12$.services.psql.DeleteDatabaseRequest\x1a%.services.psql.DeleteDatabaseResponse\"\x00\x12S\n\nCreateUser\x12 .services.psql.CreateUserRequest\x1a!.services.psql.CreateUserResponse\"\x00\x12S\n\nDeleteUser\x12 .services.psql.DeleteUserRequest\x1a!.services.psql.DeleteUserResponse\"\x00\x12_\n\x0eTruncateTables\x12$.services.psql.TruncateTablesRequest\x1a%.services.psql.TruncateTablesResponse\"\x00\x12w\n\x16UpdateSystemParameters\x12,.services.psql.UpdateSystemParametersRequest\x1a-.services.psql.UpdateSystemParametersResponse\"\x00\x12\\\n\rCloneDatabase\x12#.services.psql.CloneDatabaseRequest\x1a$.services.psql.CloneDatabaseResponse\"\x00\x42:Z8github.com/risingwavelabs/cloudagent/pbgen/services/psqlb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.psql_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z8github.com/risingwavelabs/cloudagent/pbgen/services/psql'
  _globals['_UPDATESYSTEMPARAMETERSREQUEST_SYSTEMPARAMETERSENTRY']._loaded_options = None
  _globals['_UPDATESYSTEMPARAMETERSREQUEST_SYSTEMPARAMETERSENTRY']._serialized_options = b'8\001'
  _globals['_CONNECTION']._serialized_start=107
  _globals['_CONNECTION']._serialized_end=201
  _globals['_CREATEDATABASEREQUEST']._serialized_start=203
  _globals['_CREATEDATABASEREQUEST']._serialized_end=291
  _globals['_CREATEDATABASERESPONSE']._serialized_start=293
  _globals['_CREATEDATABASERESPONSE']._serialized_end=367
  _globals['_DELETEDATABASEREQUEST']._serialized_start=369
  _globals['_DELETEDATABASEREQUEST']._serialized_end=457
  _globals['_DELETEDATABASERESPONSE']._serialized_start=459
  _globals['_DELETEDATABASERESPONSE']._serialized_end=533
  _globals['_CREATEUSERREQUEST']._serialized_start=536
  _globals['_CREATEUSERREQUEST']._serialized_end=668
  _globals['_CREATEUSERRESPONSE']._serialized_start=670
  _globals['_CREATEUSERRESPONSE']._serialized_end=740
  _globals['_DELETEUSERREQUEST']._serialized_start=742
  _globals['_DELETEUSERREQUEST']._serialized_end=826
  _globals['_DELETEUSERRESPONSE']._serialized_start=828
  _globals['_DELETEUSERRESPONSE']._serialized_end=898
  _globals['_TRUNCATETABLESREQUEST']._serialized_start=900
  _globals['_TRUNCATETABLESREQUEST']._serialized_end=970
  _globals['_TRUNCATETABLESRESPONSE']._serialized_start=972
  _globals['_TRUNCATETABLESRESPONSE']._serialized_end=996
  _globals['_UPDATESYSTEMPARAMETERSREQUEST']._serialized_start=999
  _globals['_UPDATESYSTEMPARAMETERSREQUEST']._serialized_end=1229
  _globals['_UPDATESYSTEMPARAMETERSREQUEST_SYSTEMPARAMETERSENTRY']._serialized_start=1174
  _globals['_UPDATESYSTEMPARAMETERSREQUEST_SYSTEMPARAMETERSENTRY']._serialized_end=1229
  _globals['_UPDATESYSTEMPARAMETERSRESPONSE']._serialized_start=1231
  _globals['_UPDATESYSTEMPARAMETERSRESPONSE']._serialized_end=1263
  _globals['_CLONEDATABASEREQUEST']._serialized_start=1266
  _globals['_CLONEDATABASEREQUEST']._serialized_end=1442
  _globals['_CLONEDATABASERESPONSE']._serialized_start=1444
  _globals['_CLONEDATABASERESPONSE']._serialized_end=1517
  _globals['_PSQLMANAGER']._serialized_start=1520
  _globals['_PSQLMANAGER']._serialized_end=2209
# @@protoc_insertion_point(module_scope)
