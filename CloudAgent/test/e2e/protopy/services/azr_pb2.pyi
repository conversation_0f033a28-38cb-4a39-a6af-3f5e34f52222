from common import creation_pb2 as _creation_pb2
from common import deletion_pb2 as _deletion_pb2
from common import update_pb2 as _update_pb2
from common import resource_pb2 as _resource_pb2
from common import k8s_pb2 as _k8s_pb2
from common import azr_pb2 as _azr_pb2
from services.common import data_pb2 as _data_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class PrivateEndpointStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    STATUS_UNSPECIFIED: _ClassVar[PrivateEndpointStatus]
    DELETING: _ClassVar[PrivateEndpointStatus]
    FAILED: _ClassVar[PrivateEndpointStatus]
    SUCCEEDED: _ClassVar[PrivateEndpointStatus]
    UPDATING: _ClassVar[PrivateEndpointStatus]
STATUS_UNSPECIFIED: PrivateEndpointStatus
DELETING: PrivateEndpointStatus
FAILED: PrivateEndpointStatus
SUCCEEDED: PrivateEndpointStatus
UPDATING: PrivateEndpointStatus

class CreateUserAssignedIdentityRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class CreateUserAssignedIdentityResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetUserAssignedIdentityRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetUserAssignedIdentityResponse(_message.Message):
    __slots__ = ("status", "principal_id", "client_id")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PRINCIPAL_ID_FIELD_NUMBER: _ClassVar[int]
    CLIENT_ID_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    principal_id: str
    client_id: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., principal_id: _Optional[str] = ..., client_id: _Optional[str] = ...) -> None: ...

class DeleteUserAssignedIdentityRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteUserAssignedIdentityResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateFederatedIdentityCredentialRequest(_message.Message):
    __slots__ = ("resource_meta", "user_assigned_identity_name", "service_account")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    USER_ASSIGNED_IDENTITY_NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    user_assigned_identity_name: str
    service_account: _k8s_pb2.ServiceAccount
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., user_assigned_identity_name: _Optional[str] = ..., service_account: _Optional[_Union[_k8s_pb2.ServiceAccount, _Mapping]] = ...) -> None: ...

class CreateFederatedIdentityCredentialResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetFederatedIdentityCredentialRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetFederatedIdentityCredentialResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteFederatedIdentityCredentialRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteFederatedIdentityCredentialResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateRoleAssignmentRequest(_message.Message):
    __slots__ = ("resource_meta", "principal_id", "role_assignment")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    PRINCIPAL_ID_FIELD_NUMBER: _ClassVar[int]
    ROLE_ASSIGNMENT_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    principal_id: str
    role_assignment: RoleAssignment
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., principal_id: _Optional[str] = ..., role_assignment: _Optional[_Union[RoleAssignment, _Mapping]] = ...) -> None: ...

class CreateRoleAssignmentResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetRoleAssignmentRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetRoleAssignmentResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteRoleAssignmentRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteRoleAssignmentResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class RoleAssignment(_message.Message):
    __slots__ = ("blob_access_option",)
    BLOB_ACCESS_OPTION_FIELD_NUMBER: _ClassVar[int]
    blob_access_option: BlobAccessOption
    def __init__(self, blob_access_option: _Optional[_Union[BlobAccessOption, _Mapping]] = ...) -> None: ...

class BlobAccessOption(_message.Message):
    __slots__ = ("container", "dirs", "storage_account")
    CONTAINER_FIELD_NUMBER: _ClassVar[int]
    DIRS_FIELD_NUMBER: _ClassVar[int]
    STORAGE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    container: str
    dirs: _containers.RepeatedScalarFieldContainer[str]
    storage_account: str
    def __init__(self, container: _Optional[str] = ..., dirs: _Optional[_Iterable[str]] = ..., storage_account: _Optional[str] = ...) -> None: ...

class CreatePrivateEndpointRequest(_message.Message):
    __slots__ = ("resource_meta", "private_link_service_id", "private_link_subnet_id", "extra_tags")
    class ExtraTagsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_LINK_SERVICE_ID_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_LINK_SUBNET_ID_FIELD_NUMBER: _ClassVar[int]
    EXTRA_TAGS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    private_link_service_id: str
    private_link_subnet_id: str
    extra_tags: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., private_link_service_id: _Optional[str] = ..., private_link_subnet_id: _Optional[str] = ..., extra_tags: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreatePrivateEndpointResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetPrivateEndpointRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetPrivateEndpointResponse(_message.Message):
    __slots__ = ("status", "private_endpoint_status", "private_endpoint_ip")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_ENDPOINT_STATUS_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_ENDPOINT_IP_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    private_endpoint_status: PrivateEndpointStatus
    private_endpoint_ip: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., private_endpoint_status: _Optional[_Union[PrivateEndpointStatus, str]] = ..., private_endpoint_ip: _Optional[str] = ...) -> None: ...

class DeletePrivateEndpointRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeletePrivateEndpointResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreatePGServerRequest(_message.Message):
    __slots__ = ("resource_meta", "spec")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    spec: _azr_pb2.PGServerSpec
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., spec: _Optional[_Union[_azr_pb2.PGServerSpec, _Mapping]] = ...) -> None: ...

class CreatePGServerResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeletePGServerRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeletePGServerResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class StartPGServerRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StartPGServerResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class StopPGServerRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StopPGServerResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class GetPGServerRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetPGServerResponse(_message.Message):
    __slots__ = ("status", "domain_name", "server_state")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    DOMAIN_NAME_FIELD_NUMBER: _ClassVar[int]
    SERVER_STATE_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    domain_name: str
    server_state: _azr_pb2.PGServerState
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., domain_name: _Optional[str] = ..., server_state: _Optional[_Union[_azr_pb2.PGServerState, str]] = ...) -> None: ...
