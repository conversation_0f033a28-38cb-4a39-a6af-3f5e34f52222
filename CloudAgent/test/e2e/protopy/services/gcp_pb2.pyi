from common import creation_pb2 as _creation_pb2
from common import deletion_pb2 as _deletion_pb2
from common import update_pb2 as _update_pb2
from common import resource_pb2 as _resource_pb2
from common import k8s_pb2 as _k8s_pb2
from common import gcp_pb2 as _gcp_pb2
from services.common import data_pb2 as _data_pb2
from google.protobuf.internal import containers as _containers
from google.protobuf.internal import enum_type_wrapper as _enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class GCSRole(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    UNKNOWN: _ClassVar[GCSRole]
    OBJECT_ADMIN: _ClassVar[GCSRole]
    OBJECT_VIEWER: _ClassVar[GCSRole]

class PscStatus(int, metaclass=_enum_type_wrapper.EnumTypeWrapper):
    __slots__ = ()
    STATUS_UNSPECIFIED: _ClassVar[PscStatus]
    PENDING: _ClassVar[PscStatus]
    ACCEPTED: _ClassVar[PscStatus]
    REJECTED: _ClassVar[PscStatus]
    CLOSED: _ClassVar[PscStatus]
UNKNOWN: GCSRole
OBJECT_ADMIN: GCSRole
OBJECT_VIEWER: GCSRole
STATUS_UNSPECIFIED: PscStatus
PENDING: PscStatus
ACCEPTED: PscStatus
REJECTED: PscStatus
CLOSED: PscStatus

class CreateIAMPolicyRoleBindingRequest(_message.Message):
    __slots__ = ("resource_meta", "IAM_service_account_name", "role_binding")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    IAM_SERVICE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    ROLE_BINDING_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    IAM_service_account_name: str
    role_binding: RoleBinding
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., IAM_service_account_name: _Optional[str] = ..., role_binding: _Optional[_Union[RoleBinding, _Mapping]] = ...) -> None: ...

class CreateIAMPolicyRoleBindingResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetIAMPolicyRoleBindingRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetIAMPolicyRoleBindingResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteIAMPolicyRoleBindingRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteIAMPolicyRoleBindingResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateIAMPolicyKSABindingRequest(_message.Message):
    __slots__ = ("resource_meta", "IAM_service_account_name", "service_account")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    IAM_SERVICE_ACCOUNT_NAME_FIELD_NUMBER: _ClassVar[int]
    SERVICE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    IAM_service_account_name: str
    service_account: _k8s_pb2.ServiceAccount
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., IAM_service_account_name: _Optional[str] = ..., service_account: _Optional[_Union[_k8s_pb2.ServiceAccount, _Mapping]] = ...) -> None: ...

class CreateIAMPolicyKSABindingResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetIAMPolicyKSABindingRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetIAMPolicyKSABindingResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteIAMPolicyKSABindingRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteIAMPolicyKSABindingResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateIAMServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class CreateIAMServiceAccountResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetIAMServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetIAMServiceAccountResponse(_message.Message):
    __slots__ = ("status", "account_email")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    ACCOUNT_EMAIL_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    account_email: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., account_email: _Optional[str] = ...) -> None: ...

class DeleteIAMServiceAccountRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteIAMServiceAccountResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class RoleBinding(_message.Message):
    __slots__ = ("gcs_access_option",)
    GCS_ACCESS_OPTION_FIELD_NUMBER: _ClassVar[int]
    gcs_access_option: GCSAccessOption
    def __init__(self, gcs_access_option: _Optional[_Union[GCSAccessOption, _Mapping]] = ...) -> None: ...

class GCSAccessOption(_message.Message):
    __slots__ = ("bucket", "dir", "dirs", "role")
    BUCKET_FIELD_NUMBER: _ClassVar[int]
    DIR_FIELD_NUMBER: _ClassVar[int]
    DIRS_FIELD_NUMBER: _ClassVar[int]
    ROLE_FIELD_NUMBER: _ClassVar[int]
    bucket: str
    dir: str
    dirs: _containers.RepeatedScalarFieldContainer[str]
    role: GCSRole
    def __init__(self, bucket: _Optional[str] = ..., dir: _Optional[str] = ..., dirs: _Optional[_Iterable[str]] = ..., role: _Optional[_Union[GCSRole, str]] = ...) -> None: ...

class CreateIPAddressRequest(_message.Message):
    __slots__ = ("resource_meta", "ip_subnet")
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    IP_SUBNET_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    ip_subnet: str
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., ip_subnet: _Optional[str] = ...) -> None: ...

class CreateIPAddressResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetIPAddressRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetIPAddressResponse(_message.Message):
    __slots__ = ("status", "ip_selflink", "ip_address")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    IP_SELFLINK_FIELD_NUMBER: _ClassVar[int]
    IP_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    ip_selflink: str
    ip_address: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., ip_selflink: _Optional[str] = ..., ip_address: _Optional[str] = ...) -> None: ...

class DeleteIPAddressRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteIPAddressResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreatePrivateServiceConnectEndpointRequest(_message.Message):
    __slots__ = ("resource_meta", "private_service_ip", "target", "extra_labels")
    class ExtraLabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_SERVICE_IP_FIELD_NUMBER: _ClassVar[int]
    TARGET_FIELD_NUMBER: _ClassVar[int]
    EXTRA_LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    private_service_ip: str
    target: str
    extra_labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., private_service_ip: _Optional[str] = ..., target: _Optional[str] = ..., extra_labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreatePrivateServiceConnectEndpointResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class GetPrivateServiceConnectEndpointRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetPrivateServiceConnectEndpointResponse(_message.Message):
    __slots__ = ("status", "psc_status", "ip_address")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PSC_STATUS_FIELD_NUMBER: _ClassVar[int]
    IP_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    psc_status: PscStatus
    ip_address: str
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., psc_status: _Optional[_Union[PscStatus, str]] = ..., ip_address: _Optional[str] = ...) -> None: ...

class DeletePrivateServiceConnectEndpointRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeletePrivateServiceConnectEndpointResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class CreateSQLInstanceRequest(_message.Message):
    __slots__ = ("resource_meta", "spec", "labels")
    class LabelsEntry(_message.Message):
        __slots__ = ("key", "value")
        KEY_FIELD_NUMBER: _ClassVar[int]
        VALUE_FIELD_NUMBER: _ClassVar[int]
        key: str
        value: str
        def __init__(self, key: _Optional[str] = ..., value: _Optional[str] = ...) -> None: ...
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    SPEC_FIELD_NUMBER: _ClassVar[int]
    LABELS_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    spec: _gcp_pb2.SQLInstanceSpec
    labels: _containers.ScalarMap[str, str]
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ..., spec: _Optional[_Union[_gcp_pb2.SQLInstanceSpec, _Mapping]] = ..., labels: _Optional[_Mapping[str, str]] = ...) -> None: ...

class CreateSQLInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _creation_pb2.Status
    def __init__(self, status: _Optional[_Union[_creation_pb2.Status, _Mapping]] = ...) -> None: ...

class DeleteSQLInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class DeleteSQLInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _deletion_pb2.Status
    def __init__(self, status: _Optional[_Union[_deletion_pb2.Status, _Mapping]] = ...) -> None: ...

class StartSQLInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StartSQLInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class StopSQLInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class StopSQLInstanceResponse(_message.Message):
    __slots__ = ("status",)
    STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _update_pb2.Status
    def __init__(self, status: _Optional[_Union[_update_pb2.Status, _Mapping]] = ...) -> None: ...

class GetSQLInstanceRequest(_message.Message):
    __slots__ = ("resource_meta",)
    RESOURCE_META_FIELD_NUMBER: _ClassVar[int]
    resource_meta: _resource_pb2.Meta
    def __init__(self, resource_meta: _Optional[_Union[_resource_pb2.Meta, _Mapping]] = ...) -> None: ...

class GetSQLInstanceResponse(_message.Message):
    __slots__ = ("status", "private_ip_address", "instance_status")
    STATUS_FIELD_NUMBER: _ClassVar[int]
    PRIVATE_IP_ADDRESS_FIELD_NUMBER: _ClassVar[int]
    INSTANCE_STATUS_FIELD_NUMBER: _ClassVar[int]
    status: _resource_pb2.Status
    private_ip_address: str
    instance_status: _gcp_pb2.SQLInstanceStatus
    def __init__(self, status: _Optional[_Union[_resource_pb2.Status, _Mapping]] = ..., private_ip_address: _Optional[str] = ..., instance_status: _Optional[_Union[_gcp_pb2.SQLInstanceStatus, str]] = ...) -> None: ...
