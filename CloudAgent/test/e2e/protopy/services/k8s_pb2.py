# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/k8s.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/k8s.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import deletion_pb2 as common_dot_deletion__pb2
from common import update_pb2 as common_dot_update__pb2
from common import k8s_pb2 as common_dot_k8s__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import risingwave_pb2 as common_dot_risingwave__pb2
from common import prometheus_pb2 as common_dot_prometheus__pb2
from common import gmp_pb2 as common_dot_gmp__pb2
from common import postgresql_pb2 as common_dot_postgresql__pb2
from google.protobuf import duration_pb2 as google_dot_protobuf_dot_duration__pb2
from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12services/k8s.proto\x12\x0cservices.k8s\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/deletion.proto\x1a\x13\x63ommon/update.proto\x1a\x10\x63ommon/k8s.proto\x1a\x15\x63ommon/resource.proto\x1a\x17\x63ommon/risingwave.proto\x1a\x17\x63ommon/prometheus.proto\x1a\x10\x63ommon/gmp.proto\x1a\x17\x63ommon/postgresql.proto\x1a\x1egoogle/protobuf/duration.proto\x1a\x1fgoogle/protobuf/timestamp.proto\"\xb7\x01\n\x16\x43reateNamespaceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12@\n\x06labels\x18\x02 \x03(\x0b\x32\x30.services.k8s.CreateNamespaceRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"K\n\x17\x43reateNamespaceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xb5\x01\n\x15LabelNamespaceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12?\n\x06labels\x18\x02 \x03(\x0b\x32/.services.k8s.LabelNamespaceRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x18\n\x16LabelNamespaceResponse\"C\n\x13GetNamespaceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"?\n\x14GetNamespaceResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"F\n\x16\x44\x65leteNamespaceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"K\n\x17\x44\x65leteNamespaceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"K\n\x1b\x43reateServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"P\n\x1c\x43reateServiceAccountResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"H\n\x18GetServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"D\n\x19GetServiceAccountResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"K\n\x1b\x44\x65leteServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"P\n\x1c\x44\x65leteServiceAccountResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xc5\x01\n\x1d\x41nnotateServiceAccountRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12G\n\x06labels\x18\x02 \x03(\x0b\x32\x37.services.k8s.AnnotateServiceAccountRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\" \n\x1e\x41nnotateServiceAccountResponse\"v\n\x16\x43reateConfigMapRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12.\n\x0f\x63onfig_map_spec\x18\x02 \x01(\x0b\x32\x15.common.k8s.ConfigMap\"K\n\x17\x43reateConfigMapResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"C\n\x13GetConfigMapRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"o\n\x14GetConfigMapResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12.\n\x0f\x63onfig_map_spec\x18\x02 \x01(\x0b\x32\x15.common.k8s.ConfigMap\"\xb2\x01\n\x16UpdateConfigMapRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x37\n\x14\x66rom_config_map_spec\x18\x02 \x01(\x0b\x32\x15.common.k8s.ConfigMapB\x02\x18\x01\x12\x31\n\x12to_config_map_spec\x18\x03 \x01(\x0b\x32\x15.common.k8s.ConfigMap\"\x19\n\x17UpdateConfigMapResponse\"F\n\x16\x44\x65leteConfigMapRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"K\n\x17\x44\x65leteConfigMapResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"l\n\x13\x43reateSecretRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\'\n\x0bsecret_spec\x18\x02 \x01(\x0b\x32\x12.common.k8s.Secret\"H\n\x14\x43reateSecretResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"@\n\x10GetSecretRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"e\n\x11GetSecretResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\'\n\x0bsecret_spec\x18\x02 \x01(\x0b\x32\x12.common.k8s.Secret\"C\n\x13\x44\x65leteSecretRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"l\n\x13UpdateSecretRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\'\n\x0bsecret_spec\x18\x02 \x01(\x0b\x32\x12.common.k8s.Secret\"\x16\n\x14UpdateSecretResponse\"H\n\x14\x44\x65leteSecretResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xee\x02\n\x17\x43reateRisingWaveRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x32\n\x0frisingwave_spec\x18\x02 \x01(\x0b\x32\x19.common.rw.RisingWaveSpec\x12\x41\n\x06labels\x18\x03 \x03(\x0b\x32\x31.services.k8s.CreateRisingWaveRequest.LabelsEntry\x12K\n\x0b\x61nnotations\x18\x04 \x03(\x0b\x32\x36.services.k8s.CreateRisingWaveRequest.AnnotationsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x32\n\x10\x41nnotationsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\" \n\x0eSpecificGroups\x12\x0e\n\x06groups\x18\x01 \x03(\t\"+\n\x07\x45nvVars\x12 \n\x04vars\x18\x01 \x03(\x0b\x32\x12.common.k8s.EnvVar\"\x17\n\x07\x45nvKeys\x12\x0c\n\x04keys\x18\x01 \x03(\t\"\xc7\x03\n\x17PutRisingWaveEnvRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x37\n\x0fspecific_groups\x18\x02 \x01(\x0b\x32\x1c.services.k8s.SpecificGroupsH\x00\x12\x14\n\nall_groups\x18\x03 \x01(\x08H\x00\x12\x33\n\x12\x63ompute_env_change\x18\x04 \x01(\x0b\x32\x15.services.k8s.EnvVarsH\x01\x12\x35\n\x14\x63ompactor_env_change\x18\x05 \x01(\x0b\x32\x15.services.k8s.EnvVarsH\x01\x12\x36\n\x15standalone_env_change\x18\x06 \x01(\x0b\x32\x15.services.k8s.EnvVarsH\x01\x12\x30\n\x0fmeta_env_change\x18\x07 \x01(\x0b\x32\x15.services.k8s.EnvVarsH\x01\x12\x34\n\x13\x66rontend_env_change\x18\x08 \x01(\x0b\x32\x15.services.k8s.EnvVarsH\x01\x42\x16\n\x14node_group_selectionB\x0b\n\tcomponent\"\x1a\n\x18PutRisingWaveEnvResponse\"\xca\x03\n\x1a\x44\x65leteRisingWaveEnvRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x37\n\x0fspecific_groups\x18\x02 \x01(\x0b\x32\x1c.services.k8s.SpecificGroupsH\x00\x12\x14\n\nall_groups\x18\x03 \x01(\x08H\x00\x12\x33\n\x12\x63ompute_env_change\x18\x04 \x01(\x0b\x32\x15.services.k8s.EnvKeysH\x01\x12\x35\n\x14\x63ompactor_env_change\x18\x05 \x01(\x0b\x32\x15.services.k8s.EnvKeysH\x01\x12\x36\n\x15standalone_env_change\x18\x06 \x01(\x0b\x32\x15.services.k8s.EnvKeysH\x01\x12\x30\n\x0fmeta_env_change\x18\x07 \x01(\x0b\x32\x15.services.k8s.EnvKeysH\x01\x12\x34\n\x13\x66rontend_env_change\x18\x08 \x01(\x0b\x32\x15.services.k8s.EnvKeysH\x01\x42\x16\n\x14node_group_selectionB\x0b\n\tcomponent\"\x1d\n\x1b\x44\x65leteRisingWaveEnvResponse\"L\n\x18\x43reateRisingWaveResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"D\n\x14GetRisingWaveRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xac\x01\n\x15GetRisingWaveResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x32\n\x0frisingwave_spec\x18\x02 \x01(\x0b\x32\x19.common.rw.RisingWaveSpec\x12\x36\n\x11risingwave_status\x18\x03 \x01(\x0b\x32\x1b.common.rw.RisingWaveStatus\"G\n\x17\x44\x65leteRisingWaveRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"L\n\x18\x44\x65leteRisingWaveResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"_\n\x1cUpdateRisingWaveImageRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x11\n\timage_tag\x18\x02 \x01(\t\"\x1f\n\x1dUpdateRisingWaveImageResponse\"f\n!UpdateRisingWaveLicenseKeyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x13\n\x0bsecret_name\x18\x02 \x01(\t\"$\n\"UpdateRisingWaveLicenseKeyResponse\"{\n\"UpdateRisingWaveSecretStoreRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x13\n\x0bsecret_name\x18\x02 \x01(\t\x12\x12\n\nsecret_key\x18\x03 \x01(\t\"%\n#UpdateRisingWaveSecretStoreResponse\"\xfb\x02\n\x16ScaleRisingWaveRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12-\n\x0fmeta_scale_spec\x18\x02 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x31\n\x13\x66rontend_scale_spec\x18\x03 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x30\n\x12\x63ompute_scale_spec\x18\x04 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x32\n\x14\x63ompactor_scale_spec\x18\x05 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x36\n\x14\x63onnector_scale_spec\x18\x06 \x01(\x0b\x32\x14.common.rw.ScaleSpecB\x02\x18\x01\x12\x33\n\x15standalone_scale_spec\x18\x07 \x01(\x0b\x32\x14.common.rw.ScaleSpec\"\x19\n\x17ScaleRisingWaveResponse\"\xbc\x01\n\x1bScaleRisingWaveRequestOneOf\x12/\n\x0fstandalone_spec\x18\x01 \x01(\x0b\x32\x14.common.rw.ScaleSpecH\x00\x12\x36\n\x0c\x63luster_spec\x18\x02 \x01(\x0b\x32\x1e.services.k8s.ClusterScaleSpecH\x00\x12,\n\rresource_meta\x18\x03 \x01(\x0b\x32\x15.common.resource.MetaB\x06\n\x04mode\"\xda\x01\n\x10\x43lusterScaleSpec\x12-\n\x0fmeta_scale_spec\x18\x02 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x31\n\x13\x66rontend_scale_spec\x18\x03 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x30\n\x12\x63ompute_scale_spec\x18\x04 \x01(\x0b\x32\x14.common.rw.ScaleSpec\x12\x32\n\x14\x63ompactor_scale_spec\x18\x05 \x01(\x0b\x32\x14.common.rw.ScaleSpec\"T\n\x19RisingWaveReplicaOverride\x12\x10\n\x08replicas\x18\x01 \x01(\r\x12\x11\n\tcomponent\x18\x02 \x01(\t\x12\x12\n\nnode_group\x18\x03 \x01(\t\"\x82\x01\n\x16StartRisingWaveRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12:\n\toverrides\x18\x02 \x03(\x0b\x32\'.services.k8s.RisingWaveReplicaOverride\"\x19\n\x17StartRisingWaveResponse\"E\n\x15StopRisingWaveRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\x18\n\x16StopRisingWaveResponse\"\xc5\x01\n!UpdateRisingWaveComponentsRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x32\n\x0f\x63omponents_spec\x18\x02 \x01(\x0b\x32\x19.common.rw.ComponentsSpec\x12#\n\x16\x65nable_standalone_mode\x18\x03 \x01(\x08H\x00\x88\x01\x01\x42\x19\n\x17_enable_standalone_mode\"$\n\"UpdateRisingWaveComponentsResponse\"\x83\x01\n UpdateRisingWaveMetaStoreRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x31\n\x0fmeta_store_spec\x18\x02 \x01(\x0b\x32\x18.common.rw.MetaStoreSpec\"#\n!UpdateRisingWaveMetaStoreResponse\"\x85\x01\n\'CreateRisingWaveComputeNodeGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12,\n\nnode_group\x18\x02 \x01(\x0b\x32\x18.common.rw.NodeGroupSpec\"\\\n(CreateRisingWaveComputeNodeGroupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\x85\x01\n\'UpdateRisingWaveComputeNodeGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12,\n\nnode_group\x18\x02 \x01(\x0b\x32\x18.common.rw.NodeGroupSpec\"Z\n(UpdateRisingWaveComputeNodeGroupResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"p\n\'DeleteRisingWaveComputeNodeGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x17\n\x0fnode_group_name\x18\x02 \x01(\t\"\\\n(DeleteRisingWaveComputeNodeGroupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xab\x01\n CreateRisingWaveNodeGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\tcomponent\x18\x02 \x01(\x0e\x32\x18.common.rw.ComponentType\x12,\n\nnode_group\x18\x03 \x01(\x0b\x32\x18.common.rw.NodeGroupSpec\"U\n!CreateRisingWaveNodeGroupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xab\x01\n UpdateRisingWaveNodeGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\tcomponent\x18\x02 \x01(\x0e\x32\x18.common.rw.ComponentType\x12,\n\nnode_group\x18\x03 \x01(\x0b\x32\x18.common.rw.NodeGroupSpec\"S\n!UpdateRisingWaveNodeGroupResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"\x96\x01\n DeleteRisingWaveNodeGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\tcomponent\x18\x02 \x01(\x0e\x32\x18.common.rw.ComponentType\x12\x17\n\x0fnode_group_name\x18\x03 \x01(\t\"U\n!DeleteRisingWaveNodeGroupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xca\x01\n-UpdateRisingWaveNodeGroupConfigurationRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\tcomponent\x18\x02 \x01(\x0e\x32\x18.common.rw.ComponentType\x12\x12\n\nnode_group\x18\x03 \x01(\t\x12*\n\x0bnode_config\x18\x04 \x01(\x0b\x32\x15.common.rw.NodeConfig\"`\n.UpdateRisingWaveNodeGroupConfigurationResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"\xc9\x01\n)UpdateRisingWaveNodeGroupRestartAtRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\tcomponent\x18\x02 \x01(\x0e\x32\x18.common.rw.ComponentType\x12\x12\n\nnode_group\x18\x03 \x01(\t\x12-\n\trestartAt\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\\\n*UpdateRisingWaveNodeGroupRestartAtResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"S\n#DeletePersistentVolumeClaimsRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"X\n$DeletePersistentVolumeClaimsResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"P\n GetPersistentVolumeClaimsRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"L\n!GetPersistentVolumeClaimsResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"\x87\x01\n\"CreatePersistentVolumeClaimRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x33\n\x04spec\x18\x02 \x01(\x0b\x32%.common.k8s.PersistentVolumeClaimSpec\"W\n#CreatePersistentVolumeClaimResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\x9e\x01\n\x19InstallHelmReleaseRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\x0crelease_meta\x18\x02 \x01(\x0b\x32\x15.common.resource.Meta\x12\x11\n\tchart_url\x18\x03 \x01(\t\x12\x13\n\x0bvalues_json\x18\x04 \x01(\t\"N\n\x1aInstallHelmReleaseResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xaf\x01\n\x19UpgradeHelmReleaseRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\x0crelease_meta\x18\x02 \x01(\x0b\x32\x15.common.resource.Meta\x12\x11\n\tchart_url\x18\x03 \x01(\t\x12\x13\n\x0bvalues_json\x18\x04 \x01(\t\x12\x0f\n\x07install\x18\x05 \x01(\x08\"N\n\x1aUpgradeHelmReleaseResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"x\n\x1bUninstallHelmReleaseRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\x0crelease_meta\x18\x02 \x01(\x0b\x32\x15.common.resource.Meta\"P\n\x1cUninstallHelmReleaseResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"D\n\x15GetHelmReleaseRequest\x12+\n\x0crelease_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"G\n\x16GetHelmReleaseResponse\x12-\n\x0chelm_release\x18\x01 \x01(\x0b\x32\x17.common.k8s.HelmRelease\"C\n\x13GetPodPhasesRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xa9\x01\n\x14GetPodPhasesResponse\x12H\n\x0cpod_to_phase\x18\x01 \x03(\x0b\x32\x32.services.k8s.GetPodPhasesResponse.PodToPhaseEntry\x1aG\n\x0fPodToPhaseEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12#\n\x05value\x18\x02 \x01(\x0e\x32\x14.common.k8s.PodPhase:\x02\x38\x01\"I\n\x19RestartStatefulSetRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\x1c\n\x1aRestartStatefulSetResponse\"H\n\x18RestartDeploymentRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\x1b\n\x19RestartDeploymentResponse\"S\n#GetStatefulSetReplicasStatusRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"O\n$GetStatefulSetReplicasStatusResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"R\n\"GetDeploymentReplicasStatusRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"N\n#GetDeploymentReplicasStatusResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"\x8b\x03\n\x1b\x43reateServiceMonitorRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x43\n\x14service_monitor_spec\x18\x02 \x01(\x0b\x32%.common.prometheus.ServiceMonitorSpec\x12\x45\n\x06labels\x18\x03 \x03(\x0b\x32\x35.services.k8s.CreateServiceMonitorRequest.LabelsEntry\x12O\n\x0b\x61nnotations\x18\x04 \x03(\x0b\x32:.services.k8s.CreateServiceMonitorRequest.AnnotationsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x32\n\x10\x41nnotationsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"P\n\x1c\x43reateServiceMonitorResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xff\x02\n\x1a\x43reatePodMonitoringRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12:\n\x13pod_monitoring_spec\x18\x02 \x01(\x0b\x32\x1d.common.gmp.PodMonitoringSpec\x12\x44\n\x06labels\x18\x03 \x03(\x0b\x32\x34.services.k8s.CreatePodMonitoringRequest.LabelsEntry\x12N\n\x0b\x61nnotations\x18\x04 \x03(\x0b\x32\x39.services.k8s.CreatePodMonitoringRequest.AnnotationsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\x1a\x32\n\x10\x41nnotationsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"O\n\x1b\x43reatePodMonitoringResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xe2\x01\n\x14\x43reateServiceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12-\n\x0cservice_spec\x18\x02 \x01(\x0b\x32\x17.common.k8s.ServiceSpec\x12>\n\x06labels\x18\x03 \x03(\x0b\x32..services.k8s.CreateServiceRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"I\n\x15\x43reateServiceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xec\x01\n\x1a\x43reateNetworkPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\x04spec\x18\x02 \x01(\x0b\x32\x1d.common.k8s.NetworkPolicySpec\x12\x44\n\x06labels\x18\x03 \x03(\x0b\x32\x34.services.k8s.CreateNetworkPolicyRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"O\n\x1b\x43reateNetworkPolicyResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"\xfc\x01\n\"CreateOrUpdateNetworkPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12+\n\x04spec\x18\x02 \x01(\x0b\x32\x1d.common.k8s.NetworkPolicySpec\x12L\n\x06labels\x18\x03 \x03(\x0b\x32<.services.k8s.CreateOrUpdateNetworkPolicyRequest.LabelsEntry\x1a-\n\x0bLabelsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"%\n#CreateOrUpdateNetworkPolicyResponse\"\x83\x01\n\x17\x43reatePostgreSqlRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12:\n\x0fpostgresql_spec\x18\x02 \x01(\x0b\x32!.common.postgresql.PostgreSqlSpec\"L\n\x18\x43reatePostgreSqlResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"G\n\x17\x44\x65letePostgreSqlRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"L\n\x18\x44\x65letePostgreSqlResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\x83\x01\n\x17UpdatePostgreSqlRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12:\n\x0fpostgresql_spec\x18\x02 \x01(\x0b\x32!.common.postgresql.PostgreSqlSpec\"O\n\x18UpdatePostgreSqlResponse\x12\x33\n\x06status\x18\x01 \x01(\x0e\x32#.common.postgresql.UpdateStatusCode\"D\n\x14GetPostgreSqlRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xdc\x01\n\x15GetPostgreSqlResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12:\n\x0fpostgresql_spec\x18\x02 \x01(\x0b\x32!.common.postgresql.PostgreSqlSpec\x12)\n\nsecret_ref\x18\x03 \x01(\x0b\x32\x15.common.resource.Meta\x12\x33\n\x0b\x63redentials\x18\x04 \x01(\x0b\x32\x1e.common.postgresql.Credentials\"J\n\x1a\x44\x65leteNetworkPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"O\n\x1b\x44\x65leteNetworkPolicyResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"K\n\x1b\x44\x65leteServiceMonitorRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"P\n\x1c\x44\x65leteServiceMonitorResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"J\n\x1a\x44\x65letePodMonitoringRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"O\n\x1b\x44\x65letePodMonitoringResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"#\n\x0eGetPodsRequest\x12\x11\n\tnamespace\x18\x01 \x01(\t\"0\n\x0fGetPodsResponse\x12\x1d\n\x04pods\x18\x01 \x03(\x0b\x32\x0f.common.k8s.Pod\"E\n\x17GetClusterAccessRequest\x12*\n\x07timeout\x18\x01 \x01(\x0b\x32\x19.google.protobuf.Duration\"j\n\x18GetClusterAccessResponse\x12\x18\n\x10\x63luster_endpoint\x18\x01 \x01(\t\x12%\n\x1d\x63luster_ca_certificate_base64\x18\x02 \x01(\t\x12\r\n\x05token\x18\x03 \x01(\t2\xe5\x39\n\x12K8sResourceManager\x12`\n\x0f\x43reateNamespace\x12$.services.k8s.CreateNamespaceRequest\x1a%.services.k8s.CreateNamespaceResponse\"\x00\x12`\n\x0f\x44\x65leteNamespace\x12$.services.k8s.DeleteNamespaceRequest\x1a%.services.k8s.DeleteNamespaceResponse\"\x00\x12W\n\x0cGetNamespace\x12!.services.k8s.GetNamespaceRequest\x1a\".services.k8s.GetNamespaceResponse\"\x00\x12]\n\x0eLabelNamespace\x12#.services.k8s.LabelNamespaceRequest\x1a$.services.k8s.LabelNamespaceResponse\"\x00\x12o\n\x14\x43reateServiceAccount\x12).services.k8s.CreateServiceAccountRequest\x1a*.services.k8s.CreateServiceAccountResponse\"\x00\x12o\n\x14\x44\x65leteServiceAccount\x12).services.k8s.DeleteServiceAccountRequest\x1a*.services.k8s.DeleteServiceAccountResponse\"\x00\x12\x66\n\x11GetServiceAccount\x12&.services.k8s.GetServiceAccountRequest\x1a\'.services.k8s.GetServiceAccountResponse\"\x00\x12u\n\x16\x41nnotateServiceAccount\x12+.services.k8s.AnnotateServiceAccountRequest\x1a,.services.k8s.AnnotateServiceAccountResponse\"\x00\x12`\n\x0f\x43reateConfigMap\x12$.services.k8s.CreateConfigMapRequest\x1a%.services.k8s.CreateConfigMapResponse\"\x00\x12`\n\x0f\x44\x65leteConfigMap\x12$.services.k8s.DeleteConfigMapRequest\x1a%.services.k8s.DeleteConfigMapResponse\"\x00\x12W\n\x0cGetConfigMap\x12!.services.k8s.GetConfigMapRequest\x1a\".services.k8s.GetConfigMapResponse\"\x00\x12`\n\x0fUpdateConfigMap\x12$.services.k8s.UpdateConfigMapRequest\x1a%.services.k8s.UpdateConfigMapResponse\"\x00\x12W\n\x0c\x43reateSecret\x12!.services.k8s.CreateSecretRequest\x1a\".services.k8s.CreateSecretResponse\"\x00\x12W\n\x0c\x44\x65leteSecret\x12!.services.k8s.DeleteSecretRequest\x1a\".services.k8s.DeleteSecretResponse\"\x00\x12N\n\tGetSecret\x12\x1e.services.k8s.GetSecretRequest\x1a\x1f.services.k8s.GetSecretResponse\"\x00\x12W\n\x0cUpdateSecret\x12!.services.k8s.UpdateSecretRequest\x1a\".services.k8s.UpdateSecretResponse\"\x00\x12\x63\n\x10\x43reateRisingWave\x12%.services.k8s.CreateRisingWaveRequest\x1a&.services.k8s.CreateRisingWaveResponse\"\x00\x12\x63\n\x10\x44\x65leteRisingWave\x12%.services.k8s.DeleteRisingWaveRequest\x1a&.services.k8s.DeleteRisingWaveResponse\"\x00\x12Z\n\rGetRisingWave\x12\".services.k8s.GetRisingWaveRequest\x1a#.services.k8s.GetRisingWaveResponse\"\x00\x12r\n\x15UpdateRisingWaveImage\x12*.services.k8s.UpdateRisingWaveImageRequest\x1a+.services.k8s.UpdateRisingWaveImageResponse\"\x00\x12\x81\x01\n\x1aUpdateRisingWaveLicenseKey\x12/.services.k8s.UpdateRisingWaveLicenseKeyRequest\x1a\x30.services.k8s.UpdateRisingWaveLicenseKeyResponse\"\x00\x12\x84\x01\n\x1bUpdateRisingWaveSecretStore\x12\x30.services.k8s.UpdateRisingWaveSecretStoreRequest\x1a\x31.services.k8s.UpdateRisingWaveSecretStoreResponse\"\x00\x12\x63\n\x0fScaleRisingWave\x12$.services.k8s.ScaleRisingWaveRequest\x1a%.services.k8s.ScaleRisingWaveResponse\"\x03\x88\x02\x01\x12j\n\x14ScaleRisingWaveOneOf\x12).services.k8s.ScaleRisingWaveRequestOneOf\x1a%.services.k8s.ScaleRisingWaveResponse\"\x00\x12`\n\x0fStartRisingWave\x12$.services.k8s.StartRisingWaveRequest\x1a%.services.k8s.StartRisingWaveResponse\"\x00\x12]\n\x0eStopRisingWave\x12#.services.k8s.StopRisingWaveRequest\x1a$.services.k8s.StopRisingWaveResponse\"\x00\x12\x81\x01\n\x1aUpdateRisingWaveComponents\x12/.services.k8s.UpdateRisingWaveComponentsRequest\x1a\x30.services.k8s.UpdateRisingWaveComponentsResponse\"\x00\x12~\n\x19UpdateRisingWaveMetaStore\x12..services.k8s.UpdateRisingWaveMetaStoreRequest\x1a/.services.k8s.UpdateRisingWaveMetaStoreResponse\"\x00\x12\x66\n\x13PutRisingWaveEnvVar\x12%.services.k8s.PutRisingWaveEnvRequest\x1a&.services.k8s.PutRisingWaveEnvResponse\"\x00\x12o\n\x16\x44\x65leteRisingWaveEnvVar\x12(.services.k8s.DeleteRisingWaveEnvRequest\x1a).services.k8s.DeleteRisingWaveEnvResponse\"\x00\x12\x96\x01\n CreateRisingWaveComputeNodeGroup\x12\x35.services.k8s.CreateRisingWaveComputeNodeGroupRequest\x1a\x36.services.k8s.CreateRisingWaveComputeNodeGroupResponse\"\x03\x88\x02\x01\x12\x96\x01\n UpdateRisingWaveComputeNodeGroup\x12\x35.services.k8s.UpdateRisingWaveComputeNodeGroupRequest\x1a\x36.services.k8s.UpdateRisingWaveComputeNodeGroupResponse\"\x03\x88\x02\x01\x12\x96\x01\n DeleteRisingWaveComputeNodeGroup\x12\x35.services.k8s.DeleteRisingWaveComputeNodeGroupRequest\x1a\x36.services.k8s.DeleteRisingWaveComputeNodeGroupResponse\"\x03\x88\x02\x01\x12~\n\x19\x43reateRisingWaveNodeGroup\x12..services.k8s.CreateRisingWaveNodeGroupRequest\x1a/.services.k8s.CreateRisingWaveNodeGroupResponse\"\x00\x12~\n\x19UpdateRisingWaveNodeGroup\x12..services.k8s.UpdateRisingWaveNodeGroupRequest\x1a/.services.k8s.UpdateRisingWaveNodeGroupResponse\"\x00\x12~\n\x19\x44\x65leteRisingWaveNodeGroup\x12..services.k8s.DeleteRisingWaveNodeGroupRequest\x1a/.services.k8s.DeleteRisingWaveNodeGroupResponse\"\x00\x12\xa5\x01\n&UpdateRisingWaveNodeGroupConfiguration\x12;.services.k8s.UpdateRisingWaveNodeGroupConfigurationRequest\x1a<.services.k8s.UpdateRisingWaveNodeGroupConfigurationResponse\"\x00\x12\x99\x01\n\"UpdateRisingWaveNodeGroupRestartAt\x12\x37.services.k8s.UpdateRisingWaveNodeGroupRestartAtRequest\x1a\x38.services.k8s.UpdateRisingWaveNodeGroupRestartAtResponse\"\x00\x12\x87\x01\n\x1c\x44\x65letePersistentVolumeClaims\x12\x31.services.k8s.DeletePersistentVolumeClaimsRequest\x1a\x32.services.k8s.DeletePersistentVolumeClaimsResponse\"\x00\x12~\n\x19GetPersistentVolumeClaims\x12..services.k8s.GetPersistentVolumeClaimsRequest\x1a/.services.k8s.GetPersistentVolumeClaimsResponse\"\x00\x12\x84\x01\n\x1b\x43reatePersistentVolumeClaim\x12\x30.services.k8s.CreatePersistentVolumeClaimRequest\x1a\x31.services.k8s.CreatePersistentVolumeClaimResponse\"\x00\x12]\n\x0eGetHelmRelease\x12#.services.k8s.GetHelmReleaseRequest\x1a$.services.k8s.GetHelmReleaseResponse\"\x00\x12i\n\x12InstallHelmRelease\x12\'.services.k8s.InstallHelmReleaseRequest\x1a(.services.k8s.InstallHelmReleaseResponse\"\x00\x12i\n\x12UpgradeHelmRelease\x12\'.services.k8s.UpgradeHelmReleaseRequest\x1a(.services.k8s.UpgradeHelmReleaseResponse\"\x00\x12o\n\x14UninstallHelmRelease\x12).services.k8s.UninstallHelmReleaseRequest\x1a*.services.k8s.UninstallHelmReleaseResponse\"\x00\x12W\n\x0cGetPodPhases\x12!.services.k8s.GetPodPhasesRequest\x1a\".services.k8s.GetPodPhasesResponse\"\x00\x12i\n\x12RestartStatefulSet\x12\'.services.k8s.RestartStatefulSetRequest\x1a(.services.k8s.RestartStatefulSetResponse\"\x00\x12\x87\x01\n\x1cGetStatefulSetReplicasStatus\x12\x31.services.k8s.GetStatefulSetReplicasStatusRequest\x1a\x32.services.k8s.GetStatefulSetReplicasStatusResponse\"\x00\x12\x84\x01\n\x1bGetDeploymentReplicasStatus\x12\x30.services.k8s.GetDeploymentReplicasStatusRequest\x1a\x31.services.k8s.GetDeploymentReplicasStatusResponse\"\x00\x12\x66\n\x11RestartDeployment\x12&.services.k8s.RestartDeploymentRequest\x1a\'.services.k8s.RestartDeploymentResponse\"\x00\x12o\n\x14\x43reateServiceMonitor\x12).services.k8s.CreateServiceMonitorRequest\x1a*.services.k8s.CreateServiceMonitorResponse\"\x00\x12o\n\x14\x44\x65leteServiceMonitor\x12).services.k8s.DeleteServiceMonitorRequest\x1a*.services.k8s.DeleteServiceMonitorResponse\"\x00\x12t\n\x19\x43reateAzureServiceMonitor\x12).services.k8s.CreateServiceMonitorRequest\x1a*.services.k8s.CreateServiceMonitorResponse\"\x00\x12t\n\x19\x44\x65leteAzureServiceMonitor\x12).services.k8s.DeleteServiceMonitorRequest\x1a*.services.k8s.DeleteServiceMonitorResponse\"\x00\x12l\n\x13\x43reatePodMonitoring\x12(.services.k8s.CreatePodMonitoringRequest\x1a).services.k8s.CreatePodMonitoringResponse\"\x00\x12l\n\x13\x44\x65letePodMonitoring\x12(.services.k8s.DeletePodMonitoringRequest\x1a).services.k8s.DeletePodMonitoringResponse\"\x00\x12Z\n\rCreateService\x12\".services.k8s.CreateServiceRequest\x1a#.services.k8s.CreateServiceResponse\"\x00\x12l\n\x13\x43reateNetworkPolicy\x12(.services.k8s.CreateNetworkPolicyRequest\x1a).services.k8s.CreateNetworkPolicyResponse\"\x00\x12\x84\x01\n\x1b\x43reateOrUpdateNetworkPolicy\x12\x30.services.k8s.CreateOrUpdateNetworkPolicyRequest\x1a\x31.services.k8s.CreateOrUpdateNetworkPolicyResponse\"\x00\x12l\n\x13\x44\x65leteNetworkPolicy\x12(.services.k8s.DeleteNetworkPolicyRequest\x1a).services.k8s.DeleteNetworkPolicyResponse\"\x00\x12\x63\n\x10\x43reatePostgreSql\x12%.services.k8s.CreatePostgreSqlRequest\x1a&.services.k8s.CreatePostgreSqlResponse\"\x00\x12\x63\n\x10\x44\x65letePostgreSql\x12%.services.k8s.DeletePostgreSqlRequest\x1a&.services.k8s.DeletePostgreSqlResponse\"\x00\x12\x63\n\x10UpdatePostgreSql\x12%.services.k8s.UpdatePostgreSqlRequest\x1a&.services.k8s.UpdatePostgreSqlResponse\"\x00\x12Z\n\rGetPostgreSql\x12\".services.k8s.GetPostgreSqlRequest\x1a#.services.k8s.GetPostgreSqlResponse\"\x00\x12H\n\x07GetPods\x12\x1c.services.k8s.GetPodsRequest\x1a\x1d.services.k8s.GetPodsResponse\"\x00\x12\x63\n\x10GetClusterAccess\x12%.services.k8s.GetClusterAccessRequest\x1a&.services.k8s.GetClusterAccessResponse\"\x00\x42\x39Z7github.com/risingwavelabs/cloudagent/pbgen/services/k8sb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.k8s_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z7github.com/risingwavelabs/cloudagent/pbgen/services/k8s'
  _globals['_CREATENAMESPACEREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATENAMESPACEREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_LABELNAMESPACEREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_LABELNAMESPACEREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_ANNOTATESERVICEACCOUNTREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_ANNOTATESERVICEACCOUNTREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_UPDATECONFIGMAPREQUEST'].fields_by_name['from_config_map_spec']._loaded_options = None
  _globals['_UPDATECONFIGMAPREQUEST'].fields_by_name['from_config_map_spec']._serialized_options = b'\030\001'
  _globals['_CREATERISINGWAVEREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATERISINGWAVEREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_CREATERISINGWAVEREQUEST_ANNOTATIONSENTRY']._loaded_options = None
  _globals['_CREATERISINGWAVEREQUEST_ANNOTATIONSENTRY']._serialized_options = b'8\001'
  _globals['_SCALERISINGWAVEREQUEST'].fields_by_name['connector_scale_spec']._loaded_options = None
  _globals['_SCALERISINGWAVEREQUEST'].fields_by_name['connector_scale_spec']._serialized_options = b'\030\001'
  _globals['_GETPODPHASESRESPONSE_PODTOPHASEENTRY']._loaded_options = None
  _globals['_GETPODPHASESRESPONSE_PODTOPHASEENTRY']._serialized_options = b'8\001'
  _globals['_CREATESERVICEMONITORREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATESERVICEMONITORREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_CREATESERVICEMONITORREQUEST_ANNOTATIONSENTRY']._loaded_options = None
  _globals['_CREATESERVICEMONITORREQUEST_ANNOTATIONSENTRY']._serialized_options = b'8\001'
  _globals['_CREATEPODMONITORINGREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATEPODMONITORINGREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_CREATEPODMONITORINGREQUEST_ANNOTATIONSENTRY']._loaded_options = None
  _globals['_CREATEPODMONITORINGREQUEST_ANNOTATIONSENTRY']._serialized_options = b'8\001'
  _globals['_CREATESERVICEREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATESERVICEREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_CREATENETWORKPOLICYREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATENETWORKPOLICYREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_CREATEORUPDATENETWORKPOLICYREQUEST_LABELSENTRY']._loaded_options = None
  _globals['_CREATEORUPDATENETWORKPOLICYREQUEST_LABELSENTRY']._serialized_options = b'8\001'
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['ScaleRisingWave']._loaded_options = None
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['ScaleRisingWave']._serialized_options = b'\210\002\001'
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['CreateRisingWaveComputeNodeGroup']._loaded_options = None
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['CreateRisingWaveComputeNodeGroup']._serialized_options = b'\210\002\001'
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['UpdateRisingWaveComputeNodeGroup']._loaded_options = None
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['UpdateRisingWaveComputeNodeGroup']._serialized_options = b'\210\002\001'
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['DeleteRisingWaveComputeNodeGroup']._loaded_options = None
  _globals['_K8SRESOURCEMANAGER'].methods_by_name['DeleteRisingWaveComputeNodeGroup']._serialized_options = b'\210\002\001'
  _globals['_CREATENAMESPACEREQUEST']._serialized_start=303
  _globals['_CREATENAMESPACEREQUEST']._serialized_end=486
  _globals['_CREATENAMESPACEREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATENAMESPACEREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATENAMESPACERESPONSE']._serialized_start=488
  _globals['_CREATENAMESPACERESPONSE']._serialized_end=563
  _globals['_LABELNAMESPACEREQUEST']._serialized_start=566
  _globals['_LABELNAMESPACEREQUEST']._serialized_end=747
  _globals['_LABELNAMESPACEREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_LABELNAMESPACEREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_LABELNAMESPACERESPONSE']._serialized_start=749
  _globals['_LABELNAMESPACERESPONSE']._serialized_end=773
  _globals['_GETNAMESPACEREQUEST']._serialized_start=775
  _globals['_GETNAMESPACEREQUEST']._serialized_end=842
  _globals['_GETNAMESPACERESPONSE']._serialized_start=844
  _globals['_GETNAMESPACERESPONSE']._serialized_end=907
  _globals['_DELETENAMESPACEREQUEST']._serialized_start=909
  _globals['_DELETENAMESPACEREQUEST']._serialized_end=979
  _globals['_DELETENAMESPACERESPONSE']._serialized_start=981
  _globals['_DELETENAMESPACERESPONSE']._serialized_end=1056
  _globals['_CREATESERVICEACCOUNTREQUEST']._serialized_start=1058
  _globals['_CREATESERVICEACCOUNTREQUEST']._serialized_end=1133
  _globals['_CREATESERVICEACCOUNTRESPONSE']._serialized_start=1135
  _globals['_CREATESERVICEACCOUNTRESPONSE']._serialized_end=1215
  _globals['_GETSERVICEACCOUNTREQUEST']._serialized_start=1217
  _globals['_GETSERVICEACCOUNTREQUEST']._serialized_end=1289
  _globals['_GETSERVICEACCOUNTRESPONSE']._serialized_start=1291
  _globals['_GETSERVICEACCOUNTRESPONSE']._serialized_end=1359
  _globals['_DELETESERVICEACCOUNTREQUEST']._serialized_start=1361
  _globals['_DELETESERVICEACCOUNTREQUEST']._serialized_end=1436
  _globals['_DELETESERVICEACCOUNTRESPONSE']._serialized_start=1438
  _globals['_DELETESERVICEACCOUNTRESPONSE']._serialized_end=1518
  _globals['_ANNOTATESERVICEACCOUNTREQUEST']._serialized_start=1521
  _globals['_ANNOTATESERVICEACCOUNTREQUEST']._serialized_end=1718
  _globals['_ANNOTATESERVICEACCOUNTREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_ANNOTATESERVICEACCOUNTREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_ANNOTATESERVICEACCOUNTRESPONSE']._serialized_start=1720
  _globals['_ANNOTATESERVICEACCOUNTRESPONSE']._serialized_end=1752
  _globals['_CREATECONFIGMAPREQUEST']._serialized_start=1754
  _globals['_CREATECONFIGMAPREQUEST']._serialized_end=1872
  _globals['_CREATECONFIGMAPRESPONSE']._serialized_start=1874
  _globals['_CREATECONFIGMAPRESPONSE']._serialized_end=1949
  _globals['_GETCONFIGMAPREQUEST']._serialized_start=1951
  _globals['_GETCONFIGMAPREQUEST']._serialized_end=2018
  _globals['_GETCONFIGMAPRESPONSE']._serialized_start=2020
  _globals['_GETCONFIGMAPRESPONSE']._serialized_end=2131
  _globals['_UPDATECONFIGMAPREQUEST']._serialized_start=2134
  _globals['_UPDATECONFIGMAPREQUEST']._serialized_end=2312
  _globals['_UPDATECONFIGMAPRESPONSE']._serialized_start=2314
  _globals['_UPDATECONFIGMAPRESPONSE']._serialized_end=2339
  _globals['_DELETECONFIGMAPREQUEST']._serialized_start=2341
  _globals['_DELETECONFIGMAPREQUEST']._serialized_end=2411
  _globals['_DELETECONFIGMAPRESPONSE']._serialized_start=2413
  _globals['_DELETECONFIGMAPRESPONSE']._serialized_end=2488
  _globals['_CREATESECRETREQUEST']._serialized_start=2490
  _globals['_CREATESECRETREQUEST']._serialized_end=2598
  _globals['_CREATESECRETRESPONSE']._serialized_start=2600
  _globals['_CREATESECRETRESPONSE']._serialized_end=2672
  _globals['_GETSECRETREQUEST']._serialized_start=2674
  _globals['_GETSECRETREQUEST']._serialized_end=2738
  _globals['_GETSECRETRESPONSE']._serialized_start=2740
  _globals['_GETSECRETRESPONSE']._serialized_end=2841
  _globals['_DELETESECRETREQUEST']._serialized_start=2843
  _globals['_DELETESECRETREQUEST']._serialized_end=2910
  _globals['_UPDATESECRETREQUEST']._serialized_start=2912
  _globals['_UPDATESECRETREQUEST']._serialized_end=3020
  _globals['_UPDATESECRETRESPONSE']._serialized_start=3022
  _globals['_UPDATESECRETRESPONSE']._serialized_end=3044
  _globals['_DELETESECRETRESPONSE']._serialized_start=3046
  _globals['_DELETESECRETRESPONSE']._serialized_end=3118
  _globals['_CREATERISINGWAVEREQUEST']._serialized_start=3121
  _globals['_CREATERISINGWAVEREQUEST']._serialized_end=3487
  _globals['_CREATERISINGWAVEREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATERISINGWAVEREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATERISINGWAVEREQUEST_ANNOTATIONSENTRY']._serialized_start=3437
  _globals['_CREATERISINGWAVEREQUEST_ANNOTATIONSENTRY']._serialized_end=3487
  _globals['_SPECIFICGROUPS']._serialized_start=3489
  _globals['_SPECIFICGROUPS']._serialized_end=3521
  _globals['_ENVVARS']._serialized_start=3523
  _globals['_ENVVARS']._serialized_end=3566
  _globals['_ENVKEYS']._serialized_start=3568
  _globals['_ENVKEYS']._serialized_end=3591
  _globals['_PUTRISINGWAVEENVREQUEST']._serialized_start=3594
  _globals['_PUTRISINGWAVEENVREQUEST']._serialized_end=4049
  _globals['_PUTRISINGWAVEENVRESPONSE']._serialized_start=4051
  _globals['_PUTRISINGWAVEENVRESPONSE']._serialized_end=4077
  _globals['_DELETERISINGWAVEENVREQUEST']._serialized_start=4080
  _globals['_DELETERISINGWAVEENVREQUEST']._serialized_end=4538
  _globals['_DELETERISINGWAVEENVRESPONSE']._serialized_start=4540
  _globals['_DELETERISINGWAVEENVRESPONSE']._serialized_end=4569
  _globals['_CREATERISINGWAVERESPONSE']._serialized_start=4571
  _globals['_CREATERISINGWAVERESPONSE']._serialized_end=4647
  _globals['_GETRISINGWAVEREQUEST']._serialized_start=4649
  _globals['_GETRISINGWAVEREQUEST']._serialized_end=4717
  _globals['_GETRISINGWAVERESPONSE']._serialized_start=4720
  _globals['_GETRISINGWAVERESPONSE']._serialized_end=4892
  _globals['_DELETERISINGWAVEREQUEST']._serialized_start=4894
  _globals['_DELETERISINGWAVEREQUEST']._serialized_end=4965
  _globals['_DELETERISINGWAVERESPONSE']._serialized_start=4967
  _globals['_DELETERISINGWAVERESPONSE']._serialized_end=5043
  _globals['_UPDATERISINGWAVEIMAGEREQUEST']._serialized_start=5045
  _globals['_UPDATERISINGWAVEIMAGEREQUEST']._serialized_end=5140
  _globals['_UPDATERISINGWAVEIMAGERESPONSE']._serialized_start=5142
  _globals['_UPDATERISINGWAVEIMAGERESPONSE']._serialized_end=5173
  _globals['_UPDATERISINGWAVELICENSEKEYREQUEST']._serialized_start=5175
  _globals['_UPDATERISINGWAVELICENSEKEYREQUEST']._serialized_end=5277
  _globals['_UPDATERISINGWAVELICENSEKEYRESPONSE']._serialized_start=5279
  _globals['_UPDATERISINGWAVELICENSEKEYRESPONSE']._serialized_end=5315
  _globals['_UPDATERISINGWAVESECRETSTOREREQUEST']._serialized_start=5317
  _globals['_UPDATERISINGWAVESECRETSTOREREQUEST']._serialized_end=5440
  _globals['_UPDATERISINGWAVESECRETSTORERESPONSE']._serialized_start=5442
  _globals['_UPDATERISINGWAVESECRETSTORERESPONSE']._serialized_end=5479
  _globals['_SCALERISINGWAVEREQUEST']._serialized_start=5482
  _globals['_SCALERISINGWAVEREQUEST']._serialized_end=5861
  _globals['_SCALERISINGWAVERESPONSE']._serialized_start=5863
  _globals['_SCALERISINGWAVERESPONSE']._serialized_end=5888
  _globals['_SCALERISINGWAVEREQUESTONEOF']._serialized_start=5891
  _globals['_SCALERISINGWAVEREQUESTONEOF']._serialized_end=6079
  _globals['_CLUSTERSCALESPEC']._serialized_start=6082
  _globals['_CLUSTERSCALESPEC']._serialized_end=6300
  _globals['_RISINGWAVEREPLICAOVERRIDE']._serialized_start=6302
  _globals['_RISINGWAVEREPLICAOVERRIDE']._serialized_end=6386
  _globals['_STARTRISINGWAVEREQUEST']._serialized_start=6389
  _globals['_STARTRISINGWAVEREQUEST']._serialized_end=6519
  _globals['_STARTRISINGWAVERESPONSE']._serialized_start=6521
  _globals['_STARTRISINGWAVERESPONSE']._serialized_end=6546
  _globals['_STOPRISINGWAVEREQUEST']._serialized_start=6548
  _globals['_STOPRISINGWAVEREQUEST']._serialized_end=6617
  _globals['_STOPRISINGWAVERESPONSE']._serialized_start=6619
  _globals['_STOPRISINGWAVERESPONSE']._serialized_end=6643
  _globals['_UPDATERISINGWAVECOMPONENTSREQUEST']._serialized_start=6646
  _globals['_UPDATERISINGWAVECOMPONENTSREQUEST']._serialized_end=6843
  _globals['_UPDATERISINGWAVECOMPONENTSRESPONSE']._serialized_start=6845
  _globals['_UPDATERISINGWAVECOMPONENTSRESPONSE']._serialized_end=6881
  _globals['_UPDATERISINGWAVEMETASTOREREQUEST']._serialized_start=6884
  _globals['_UPDATERISINGWAVEMETASTOREREQUEST']._serialized_end=7015
  _globals['_UPDATERISINGWAVEMETASTORERESPONSE']._serialized_start=7017
  _globals['_UPDATERISINGWAVEMETASTORERESPONSE']._serialized_end=7052
  _globals['_CREATERISINGWAVECOMPUTENODEGROUPREQUEST']._serialized_start=7055
  _globals['_CREATERISINGWAVECOMPUTENODEGROUPREQUEST']._serialized_end=7188
  _globals['_CREATERISINGWAVECOMPUTENODEGROUPRESPONSE']._serialized_start=7190
  _globals['_CREATERISINGWAVECOMPUTENODEGROUPRESPONSE']._serialized_end=7282
  _globals['_UPDATERISINGWAVECOMPUTENODEGROUPREQUEST']._serialized_start=7285
  _globals['_UPDATERISINGWAVECOMPUTENODEGROUPREQUEST']._serialized_end=7418
  _globals['_UPDATERISINGWAVECOMPUTENODEGROUPRESPONSE']._serialized_start=7420
  _globals['_UPDATERISINGWAVECOMPUTENODEGROUPRESPONSE']._serialized_end=7510
  _globals['_DELETERISINGWAVECOMPUTENODEGROUPREQUEST']._serialized_start=7512
  _globals['_DELETERISINGWAVECOMPUTENODEGROUPREQUEST']._serialized_end=7624
  _globals['_DELETERISINGWAVECOMPUTENODEGROUPRESPONSE']._serialized_start=7626
  _globals['_DELETERISINGWAVECOMPUTENODEGROUPRESPONSE']._serialized_end=7718
  _globals['_CREATERISINGWAVENODEGROUPREQUEST']._serialized_start=7721
  _globals['_CREATERISINGWAVENODEGROUPREQUEST']._serialized_end=7892
  _globals['_CREATERISINGWAVENODEGROUPRESPONSE']._serialized_start=7894
  _globals['_CREATERISINGWAVENODEGROUPRESPONSE']._serialized_end=7979
  _globals['_UPDATERISINGWAVENODEGROUPREQUEST']._serialized_start=7982
  _globals['_UPDATERISINGWAVENODEGROUPREQUEST']._serialized_end=8153
  _globals['_UPDATERISINGWAVENODEGROUPRESPONSE']._serialized_start=8155
  _globals['_UPDATERISINGWAVENODEGROUPRESPONSE']._serialized_end=8238
  _globals['_DELETERISINGWAVENODEGROUPREQUEST']._serialized_start=8241
  _globals['_DELETERISINGWAVENODEGROUPREQUEST']._serialized_end=8391
  _globals['_DELETERISINGWAVENODEGROUPRESPONSE']._serialized_start=8393
  _globals['_DELETERISINGWAVENODEGROUPRESPONSE']._serialized_end=8478
  _globals['_UPDATERISINGWAVENODEGROUPCONFIGURATIONREQUEST']._serialized_start=8481
  _globals['_UPDATERISINGWAVENODEGROUPCONFIGURATIONREQUEST']._serialized_end=8683
  _globals['_UPDATERISINGWAVENODEGROUPCONFIGURATIONRESPONSE']._serialized_start=8685
  _globals['_UPDATERISINGWAVENODEGROUPCONFIGURATIONRESPONSE']._serialized_end=8781
  _globals['_UPDATERISINGWAVENODEGROUPRESTARTATREQUEST']._serialized_start=8784
  _globals['_UPDATERISINGWAVENODEGROUPRESTARTATREQUEST']._serialized_end=8985
  _globals['_UPDATERISINGWAVENODEGROUPRESTARTATRESPONSE']._serialized_start=8987
  _globals['_UPDATERISINGWAVENODEGROUPRESTARTATRESPONSE']._serialized_end=9079
  _globals['_DELETEPERSISTENTVOLUMECLAIMSREQUEST']._serialized_start=9081
  _globals['_DELETEPERSISTENTVOLUMECLAIMSREQUEST']._serialized_end=9164
  _globals['_DELETEPERSISTENTVOLUMECLAIMSRESPONSE']._serialized_start=9166
  _globals['_DELETEPERSISTENTVOLUMECLAIMSRESPONSE']._serialized_end=9254
  _globals['_GETPERSISTENTVOLUMECLAIMSREQUEST']._serialized_start=9256
  _globals['_GETPERSISTENTVOLUMECLAIMSREQUEST']._serialized_end=9336
  _globals['_GETPERSISTENTVOLUMECLAIMSRESPONSE']._serialized_start=9338
  _globals['_GETPERSISTENTVOLUMECLAIMSRESPONSE']._serialized_end=9414
  _globals['_CREATEPERSISTENTVOLUMECLAIMREQUEST']._serialized_start=9417
  _globals['_CREATEPERSISTENTVOLUMECLAIMREQUEST']._serialized_end=9552
  _globals['_CREATEPERSISTENTVOLUMECLAIMRESPONSE']._serialized_start=9554
  _globals['_CREATEPERSISTENTVOLUMECLAIMRESPONSE']._serialized_end=9641
  _globals['_INSTALLHELMRELEASEREQUEST']._serialized_start=9644
  _globals['_INSTALLHELMRELEASEREQUEST']._serialized_end=9802
  _globals['_INSTALLHELMRELEASERESPONSE']._serialized_start=9804
  _globals['_INSTALLHELMRELEASERESPONSE']._serialized_end=9882
  _globals['_UPGRADEHELMRELEASEREQUEST']._serialized_start=9885
  _globals['_UPGRADEHELMRELEASEREQUEST']._serialized_end=10060
  _globals['_UPGRADEHELMRELEASERESPONSE']._serialized_start=10062
  _globals['_UPGRADEHELMRELEASERESPONSE']._serialized_end=10140
  _globals['_UNINSTALLHELMRELEASEREQUEST']._serialized_start=10142
  _globals['_UNINSTALLHELMRELEASEREQUEST']._serialized_end=10262
  _globals['_UNINSTALLHELMRELEASERESPONSE']._serialized_start=10264
  _globals['_UNINSTALLHELMRELEASERESPONSE']._serialized_end=10344
  _globals['_GETHELMRELEASEREQUEST']._serialized_start=10346
  _globals['_GETHELMRELEASEREQUEST']._serialized_end=10414
  _globals['_GETHELMRELEASERESPONSE']._serialized_start=10416
  _globals['_GETHELMRELEASERESPONSE']._serialized_end=10487
  _globals['_GETPODPHASESREQUEST']._serialized_start=10489
  _globals['_GETPODPHASESREQUEST']._serialized_end=10556
  _globals['_GETPODPHASESRESPONSE']._serialized_start=10559
  _globals['_GETPODPHASESRESPONSE']._serialized_end=10728
  _globals['_GETPODPHASESRESPONSE_PODTOPHASEENTRY']._serialized_start=10657
  _globals['_GETPODPHASESRESPONSE_PODTOPHASEENTRY']._serialized_end=10728
  _globals['_RESTARTSTATEFULSETREQUEST']._serialized_start=10730
  _globals['_RESTARTSTATEFULSETREQUEST']._serialized_end=10803
  _globals['_RESTARTSTATEFULSETRESPONSE']._serialized_start=10805
  _globals['_RESTARTSTATEFULSETRESPONSE']._serialized_end=10833
  _globals['_RESTARTDEPLOYMENTREQUEST']._serialized_start=10835
  _globals['_RESTARTDEPLOYMENTREQUEST']._serialized_end=10907
  _globals['_RESTARTDEPLOYMENTRESPONSE']._serialized_start=10909
  _globals['_RESTARTDEPLOYMENTRESPONSE']._serialized_end=10936
  _globals['_GETSTATEFULSETREPLICASSTATUSREQUEST']._serialized_start=10938
  _globals['_GETSTATEFULSETREPLICASSTATUSREQUEST']._serialized_end=11021
  _globals['_GETSTATEFULSETREPLICASSTATUSRESPONSE']._serialized_start=11023
  _globals['_GETSTATEFULSETREPLICASSTATUSRESPONSE']._serialized_end=11102
  _globals['_GETDEPLOYMENTREPLICASSTATUSREQUEST']._serialized_start=11104
  _globals['_GETDEPLOYMENTREPLICASSTATUSREQUEST']._serialized_end=11186
  _globals['_GETDEPLOYMENTREPLICASSTATUSRESPONSE']._serialized_start=11188
  _globals['_GETDEPLOYMENTREPLICASSTATUSRESPONSE']._serialized_end=11266
  _globals['_CREATESERVICEMONITORREQUEST']._serialized_start=11269
  _globals['_CREATESERVICEMONITORREQUEST']._serialized_end=11664
  _globals['_CREATESERVICEMONITORREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATESERVICEMONITORREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATESERVICEMONITORREQUEST_ANNOTATIONSENTRY']._serialized_start=3437
  _globals['_CREATESERVICEMONITORREQUEST_ANNOTATIONSENTRY']._serialized_end=3487
  _globals['_CREATESERVICEMONITORRESPONSE']._serialized_start=11666
  _globals['_CREATESERVICEMONITORRESPONSE']._serialized_end=11746
  _globals['_CREATEPODMONITORINGREQUEST']._serialized_start=11749
  _globals['_CREATEPODMONITORINGREQUEST']._serialized_end=12132
  _globals['_CREATEPODMONITORINGREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATEPODMONITORINGREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATEPODMONITORINGREQUEST_ANNOTATIONSENTRY']._serialized_start=3437
  _globals['_CREATEPODMONITORINGREQUEST_ANNOTATIONSENTRY']._serialized_end=3487
  _globals['_CREATEPODMONITORINGRESPONSE']._serialized_start=12134
  _globals['_CREATEPODMONITORINGRESPONSE']._serialized_end=12213
  _globals['_CREATESERVICEREQUEST']._serialized_start=12216
  _globals['_CREATESERVICEREQUEST']._serialized_end=12442
  _globals['_CREATESERVICEREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATESERVICEREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATESERVICERESPONSE']._serialized_start=12444
  _globals['_CREATESERVICERESPONSE']._serialized_end=12517
  _globals['_CREATENETWORKPOLICYREQUEST']._serialized_start=12520
  _globals['_CREATENETWORKPOLICYREQUEST']._serialized_end=12756
  _globals['_CREATENETWORKPOLICYREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATENETWORKPOLICYREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATENETWORKPOLICYRESPONSE']._serialized_start=12758
  _globals['_CREATENETWORKPOLICYRESPONSE']._serialized_end=12837
  _globals['_CREATEORUPDATENETWORKPOLICYREQUEST']._serialized_start=12840
  _globals['_CREATEORUPDATENETWORKPOLICYREQUEST']._serialized_end=13092
  _globals['_CREATEORUPDATENETWORKPOLICYREQUEST_LABELSENTRY']._serialized_start=441
  _globals['_CREATEORUPDATENETWORKPOLICYREQUEST_LABELSENTRY']._serialized_end=486
  _globals['_CREATEORUPDATENETWORKPOLICYRESPONSE']._serialized_start=13094
  _globals['_CREATEORUPDATENETWORKPOLICYRESPONSE']._serialized_end=13131
  _globals['_CREATEPOSTGRESQLREQUEST']._serialized_start=13134
  _globals['_CREATEPOSTGRESQLREQUEST']._serialized_end=13265
  _globals['_CREATEPOSTGRESQLRESPONSE']._serialized_start=13267
  _globals['_CREATEPOSTGRESQLRESPONSE']._serialized_end=13343
  _globals['_DELETEPOSTGRESQLREQUEST']._serialized_start=13345
  _globals['_DELETEPOSTGRESQLREQUEST']._serialized_end=13416
  _globals['_DELETEPOSTGRESQLRESPONSE']._serialized_start=13418
  _globals['_DELETEPOSTGRESQLRESPONSE']._serialized_end=13494
  _globals['_UPDATEPOSTGRESQLREQUEST']._serialized_start=13497
  _globals['_UPDATEPOSTGRESQLREQUEST']._serialized_end=13628
  _globals['_UPDATEPOSTGRESQLRESPONSE']._serialized_start=13630
  _globals['_UPDATEPOSTGRESQLRESPONSE']._serialized_end=13709
  _globals['_GETPOSTGRESQLREQUEST']._serialized_start=13711
  _globals['_GETPOSTGRESQLREQUEST']._serialized_end=13779
  _globals['_GETPOSTGRESQLRESPONSE']._serialized_start=13782
  _globals['_GETPOSTGRESQLRESPONSE']._serialized_end=14002
  _globals['_DELETENETWORKPOLICYREQUEST']._serialized_start=14004
  _globals['_DELETENETWORKPOLICYREQUEST']._serialized_end=14078
  _globals['_DELETENETWORKPOLICYRESPONSE']._serialized_start=14080
  _globals['_DELETENETWORKPOLICYRESPONSE']._serialized_end=14159
  _globals['_DELETESERVICEMONITORREQUEST']._serialized_start=14161
  _globals['_DELETESERVICEMONITORREQUEST']._serialized_end=14236
  _globals['_DELETESERVICEMONITORRESPONSE']._serialized_start=14238
  _globals['_DELETESERVICEMONITORRESPONSE']._serialized_end=14318
  _globals['_DELETEPODMONITORINGREQUEST']._serialized_start=14320
  _globals['_DELETEPODMONITORINGREQUEST']._serialized_end=14394
  _globals['_DELETEPODMONITORINGRESPONSE']._serialized_start=14396
  _globals['_DELETEPODMONITORINGRESPONSE']._serialized_end=14475
  _globals['_GETPODSREQUEST']._serialized_start=14477
  _globals['_GETPODSREQUEST']._serialized_end=14512
  _globals['_GETPODSRESPONSE']._serialized_start=14514
  _globals['_GETPODSRESPONSE']._serialized_end=14562
  _globals['_GETCLUSTERACCESSREQUEST']._serialized_start=14564
  _globals['_GETCLUSTERACCESSREQUEST']._serialized_end=14633
  _globals['_GETCLUSTERACCESSRESPONSE']._serialized_start=14635
  _globals['_GETCLUSTERACCESSRESPONSE']._serialized_end=14741
  _globals['_K8SRESOURCEMANAGER']._serialized_start=14744
  _globals['_K8SRESOURCEMANAGER']._serialized_end=22141
# @@protoc_insertion_point(module_scope)
