# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc
import warnings

from services import rwc_pb2 as services_dot_rwc__pb2

GRPC_GENERATED_VERSION = '1.67.0'
GRPC_VERSION = grpc.__version__
_version_not_supported = False

try:
    from grpc._utilities import first_version_is_lower
    _version_not_supported = first_version_is_lower(GRPC_VERSION, GRPC_GENERATED_VERSION)
except ImportError:
    _version_not_supported = True

if _version_not_supported:
    raise RuntimeError(
        f'The grpc package installed is at version {GRPC_VERSION},'
        + f' but the generated code in services/rwc_pb2_grpc.py depends on'
        + f' grpcio>={GRPC_GENERATED_VERSION}.'
        + f' Please upgrade your grpc module to grpcio>={GRPC_GENERATED_VERSION}'
        + f' or downgrade your generated code using grpcio-tools<={GRPC_VERSION}.'
    )


class RisingwaveControlStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.MetaNodeBackup = channel.unary_unary(
                '/services.rwc.RisingwaveControl/MetaNodeBackup',
                request_serializer=services_dot_rwc__pb2.MetaNodeBackupRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.MetaNodeBackupResponse.FromString,
                _registered_method=True)
        self.ValidateSource = channel.unary_unary(
                '/services.rwc.RisingwaveControl/ValidateSource',
                request_serializer=services_dot_rwc__pb2.ValidateSourceRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.ValidateSourceResponse.FromString,
                _registered_method=True)
        self.GetClusterInfo = channel.unary_unary(
                '/services.rwc.RisingwaveControl/GetClusterInfo',
                request_serializer=services_dot_rwc__pb2.GetClusterInfoRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.GetClusterInfoResponse.FromString,
                _registered_method=True)
        self.CordonWorkers = channel.unary_unary(
                '/services.rwc.RisingwaveControl/CordonWorkers',
                request_serializer=services_dot_rwc__pb2.CordonWorkersRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.CordonWorkersResponse.FromString,
                _registered_method=True)
        self.ResizeWorkers = channel.unary_unary(
                '/services.rwc.RisingwaveControl/ResizeWorkers',
                request_serializer=services_dot_rwc__pb2.ResizeWorkersRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.ResizeWorkersResponse.FromString,
                _registered_method=True)
        self.DeleteWorkers = channel.unary_unary(
                '/services.rwc.RisingwaveControl/DeleteWorkers',
                request_serializer=services_dot_rwc__pb2.DeleteWorkersRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.DeleteWorkersResponse.FromString,
                _registered_method=True)
        self.DeleteSnapshot = channel.unary_unary(
                '/services.rwc.RisingwaveControl/DeleteSnapshot',
                request_serializer=services_dot_rwc__pb2.DeleteSnapshotRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.DeleteSnapshotResponse.FromString,
                _registered_method=True)
        self.RestoreMeta = channel.unary_unary(
                '/services.rwc.RisingwaveControl/RestoreMeta',
                request_serializer=services_dot_rwc__pb2.RestoreMetaRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.RestoreMetaResponse.FromString,
                _registered_method=True)
        self.VacuumEtcdMeta = channel.unary_unary(
                '/services.rwc.RisingwaveControl/VacuumEtcdMeta',
                request_serializer=services_dot_rwc__pb2.VacuumEtcdMetaRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.VacuumEtcdMetaResponse.FromString,
                _registered_method=True)
        self.GenDiagnosisReport = channel.unary_unary(
                '/services.rwc.RisingwaveControl/GenDiagnosisReport',
                request_serializer=services_dot_rwc__pb2.GenDiagnosisReportRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.GenDiagnosisReportResponse.FromString,
                _registered_method=True)
        self.GenDiagnosisReportStream = channel.unary_stream(
                '/services.rwc.RisingwaveControl/GenDiagnosisReportStream',
                request_serializer=services_dot_rwc__pb2.GenDiagnosisReportStreamRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.GenDiagnosisReportStreamResponse.FromString,
                _registered_method=True)
        self.MetaMigration = channel.unary_unary(
                '/services.rwc.RisingwaveControl/MetaMigration',
                request_serializer=services_dot_rwc__pb2.MetaMigrationRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.MetaMigrationResponse.FromString,
                _registered_method=True)
        self.FetchKafkaTopic = channel.unary_unary(
                '/services.rwc.RisingwaveControl/FetchKafkaTopic',
                request_serializer=services_dot_rwc__pb2.FetchKafkaTopicRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.FetchKafkaTopicResponse.FromString,
                _registered_method=True)
        self.FetchKafkaMessage = channel.unary_unary(
                '/services.rwc.RisingwaveControl/FetchKafkaMessage',
                request_serializer=services_dot_rwc__pb2.FetchKafkaMessageRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.FetchKafkaMessageResponse.FromString,
                _registered_method=True)
        self.FetchPostgresTable = channel.unary_unary(
                '/services.rwc.RisingwaveControl/FetchPostgresTable',
                request_serializer=services_dot_rwc__pb2.FetchPostgresTableRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.FetchPostgresTableResponse.FromString,
                _registered_method=True)
        self.FetchSourceSchema = channel.unary_unary(
                '/services.rwc.RisingwaveControl/FetchSourceSchema',
                request_serializer=services_dot_rwc__pb2.FetchSourceSchemaRequest.SerializeToString,
                response_deserializer=services_dot_rwc__pb2.FetchSourceSchemaResponse.FromString,
                _registered_method=True)


class RisingwaveControlServicer(object):
    """Missing associated documentation comment in .proto file."""

    def MetaNodeBackup(self, request, context):
        """MetaNodeBackup creates a task for meta backup creation.
        Caller should call the task manager service to manage the created task.
        Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ValidateSource(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetClusterInfo(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CordonWorkers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResizeWorkers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteWorkers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteSnapshot(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RestoreMeta(self, request, context):
        """RestoreMeta creates a task for restoring a snapshot.
        Caller should call the task manager service to manage the created task.
        Expected a CREATED status on success.
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def VacuumEtcdMeta(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenDiagnosisReport(self, request, context):
        """RW Diagnosis Report
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GenDiagnosisReportStream(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MetaMigration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchKafkaTopic(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchKafkaMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchPostgresTable(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def FetchSourceSchema(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RisingwaveControlServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'MetaNodeBackup': grpc.unary_unary_rpc_method_handler(
                    servicer.MetaNodeBackup,
                    request_deserializer=services_dot_rwc__pb2.MetaNodeBackupRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.MetaNodeBackupResponse.SerializeToString,
            ),
            'ValidateSource': grpc.unary_unary_rpc_method_handler(
                    servicer.ValidateSource,
                    request_deserializer=services_dot_rwc__pb2.ValidateSourceRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.ValidateSourceResponse.SerializeToString,
            ),
            'GetClusterInfo': grpc.unary_unary_rpc_method_handler(
                    servicer.GetClusterInfo,
                    request_deserializer=services_dot_rwc__pb2.GetClusterInfoRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.GetClusterInfoResponse.SerializeToString,
            ),
            'CordonWorkers': grpc.unary_unary_rpc_method_handler(
                    servicer.CordonWorkers,
                    request_deserializer=services_dot_rwc__pb2.CordonWorkersRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.CordonWorkersResponse.SerializeToString,
            ),
            'ResizeWorkers': grpc.unary_unary_rpc_method_handler(
                    servicer.ResizeWorkers,
                    request_deserializer=services_dot_rwc__pb2.ResizeWorkersRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.ResizeWorkersResponse.SerializeToString,
            ),
            'DeleteWorkers': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteWorkers,
                    request_deserializer=services_dot_rwc__pb2.DeleteWorkersRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.DeleteWorkersResponse.SerializeToString,
            ),
            'DeleteSnapshot': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteSnapshot,
                    request_deserializer=services_dot_rwc__pb2.DeleteSnapshotRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.DeleteSnapshotResponse.SerializeToString,
            ),
            'RestoreMeta': grpc.unary_unary_rpc_method_handler(
                    servicer.RestoreMeta,
                    request_deserializer=services_dot_rwc__pb2.RestoreMetaRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.RestoreMetaResponse.SerializeToString,
            ),
            'VacuumEtcdMeta': grpc.unary_unary_rpc_method_handler(
                    servicer.VacuumEtcdMeta,
                    request_deserializer=services_dot_rwc__pb2.VacuumEtcdMetaRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.VacuumEtcdMetaResponse.SerializeToString,
            ),
            'GenDiagnosisReport': grpc.unary_unary_rpc_method_handler(
                    servicer.GenDiagnosisReport,
                    request_deserializer=services_dot_rwc__pb2.GenDiagnosisReportRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.GenDiagnosisReportResponse.SerializeToString,
            ),
            'GenDiagnosisReportStream': grpc.unary_stream_rpc_method_handler(
                    servicer.GenDiagnosisReportStream,
                    request_deserializer=services_dot_rwc__pb2.GenDiagnosisReportStreamRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.GenDiagnosisReportStreamResponse.SerializeToString,
            ),
            'MetaMigration': grpc.unary_unary_rpc_method_handler(
                    servicer.MetaMigration,
                    request_deserializer=services_dot_rwc__pb2.MetaMigrationRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.MetaMigrationResponse.SerializeToString,
            ),
            'FetchKafkaTopic': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchKafkaTopic,
                    request_deserializer=services_dot_rwc__pb2.FetchKafkaTopicRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.FetchKafkaTopicResponse.SerializeToString,
            ),
            'FetchKafkaMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchKafkaMessage,
                    request_deserializer=services_dot_rwc__pb2.FetchKafkaMessageRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.FetchKafkaMessageResponse.SerializeToString,
            ),
            'FetchPostgresTable': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchPostgresTable,
                    request_deserializer=services_dot_rwc__pb2.FetchPostgresTableRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.FetchPostgresTableResponse.SerializeToString,
            ),
            'FetchSourceSchema': grpc.unary_unary_rpc_method_handler(
                    servicer.FetchSourceSchema,
                    request_deserializer=services_dot_rwc__pb2.FetchSourceSchemaRequest.FromString,
                    response_serializer=services_dot_rwc__pb2.FetchSourceSchemaResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'services.rwc.RisingwaveControl', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))
    server.add_registered_method_handlers('services.rwc.RisingwaveControl', rpc_method_handlers)


 # This class is part of an EXPERIMENTAL API.
class RisingwaveControl(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def MetaNodeBackup(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/MetaNodeBackup',
            services_dot_rwc__pb2.MetaNodeBackupRequest.SerializeToString,
            services_dot_rwc__pb2.MetaNodeBackupResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ValidateSource(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/ValidateSource',
            services_dot_rwc__pb2.ValidateSourceRequest.SerializeToString,
            services_dot_rwc__pb2.ValidateSourceResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GetClusterInfo(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/GetClusterInfo',
            services_dot_rwc__pb2.GetClusterInfoRequest.SerializeToString,
            services_dot_rwc__pb2.GetClusterInfoResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def CordonWorkers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/CordonWorkers',
            services_dot_rwc__pb2.CordonWorkersRequest.SerializeToString,
            services_dot_rwc__pb2.CordonWorkersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def ResizeWorkers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/ResizeWorkers',
            services_dot_rwc__pb2.ResizeWorkersRequest.SerializeToString,
            services_dot_rwc__pb2.ResizeWorkersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteWorkers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/DeleteWorkers',
            services_dot_rwc__pb2.DeleteWorkersRequest.SerializeToString,
            services_dot_rwc__pb2.DeleteWorkersResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def DeleteSnapshot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/DeleteSnapshot',
            services_dot_rwc__pb2.DeleteSnapshotRequest.SerializeToString,
            services_dot_rwc__pb2.DeleteSnapshotResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def RestoreMeta(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/RestoreMeta',
            services_dot_rwc__pb2.RestoreMetaRequest.SerializeToString,
            services_dot_rwc__pb2.RestoreMetaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def VacuumEtcdMeta(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/VacuumEtcdMeta',
            services_dot_rwc__pb2.VacuumEtcdMetaRequest.SerializeToString,
            services_dot_rwc__pb2.VacuumEtcdMetaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenDiagnosisReport(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/GenDiagnosisReport',
            services_dot_rwc__pb2.GenDiagnosisReportRequest.SerializeToString,
            services_dot_rwc__pb2.GenDiagnosisReportResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def GenDiagnosisReportStream(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(
            request,
            target,
            '/services.rwc.RisingwaveControl/GenDiagnosisReportStream',
            services_dot_rwc__pb2.GenDiagnosisReportStreamRequest.SerializeToString,
            services_dot_rwc__pb2.GenDiagnosisReportStreamResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def MetaMigration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/MetaMigration',
            services_dot_rwc__pb2.MetaMigrationRequest.SerializeToString,
            services_dot_rwc__pb2.MetaMigrationResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchKafkaTopic(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/FetchKafkaTopic',
            services_dot_rwc__pb2.FetchKafkaTopicRequest.SerializeToString,
            services_dot_rwc__pb2.FetchKafkaTopicResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchKafkaMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/FetchKafkaMessage',
            services_dot_rwc__pb2.FetchKafkaMessageRequest.SerializeToString,
            services_dot_rwc__pb2.FetchKafkaMessageResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchPostgresTable(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/FetchPostgresTable',
            services_dot_rwc__pb2.FetchPostgresTableRequest.SerializeToString,
            services_dot_rwc__pb2.FetchPostgresTableResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)

    @staticmethod
    def FetchSourceSchema(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(
            request,
            target,
            '/services.rwc.RisingwaveControl/FetchSourceSchema',
            services_dot_rwc__pb2.FetchSourceSchemaRequest.SerializeToString,
            services_dot_rwc__pb2.FetchSourceSchemaResponse.FromString,
            options,
            channel_credentials,
            insecure,
            call_credentials,
            compression,
            wait_for_ready,
            timeout,
            metadata,
            _registered_method=True)
