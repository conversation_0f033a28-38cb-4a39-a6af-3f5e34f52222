# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: services/aws.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'services/aws.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import creation_pb2 as common_dot_creation__pb2
from common import deletion_pb2 as common_dot_deletion__pb2
from common import update_pb2 as common_dot_update__pb2
from common import resource_pb2 as common_dot_resource__pb2
from common import k8s_pb2 as common_dot_k8s__pb2
from common import aws_pb2 as common_dot_aws__pb2
from services.common import data_pb2 as services_dot_common_dot_data__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x12services/aws.proto\x12\x0cservices.aws\x1a\x15\x63ommon/creation.proto\x1a\x15\x63ommon/deletion.proto\x1a\x13\x63ommon/update.proto\x1a\x15\x63ommon/resource.proto\x1a\x10\x63ommon/k8s.proto\x1a\x10\x63ommon/aws.proto\x1a\x1aservices/common/data.proto\"\x8b\x01\n\x0cIPPermission\x12\x10\n\x08protocol\x18\x01 \x01(\t\x12\x11\n\tfrom_port\x18\x02 \x01(\x05\x12\x0f\n\x07to_port\x18\x03 \x01(\x05\x12\r\n\x05\x63idrs\x18\x04 \x03(\t\x12!\n\x19source_security_group_ids\x18\x05 \x03(\t\x12\x13\n\x0b\x64\x65scription\x18\x06 \x01(\t\"\xc3\x01\n\x1a\x43reateSecurityGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12;\n\x17outbound_ip_permissions\x18\x02 \x03(\x0b\x32\x1a.services.aws.IPPermission\x12:\n\x16inbound_ip_permissions\x18\x03 \x03(\x0b\x32\x1a.services.aws.IPPermission\"O\n\x1b\x43reateSecurityGroupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"J\n\x1a\x44\x65leteSecurityGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"O\n\x1b\x44\x65leteSecurityGroupResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"G\n\x17GetSecurityGroupRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"^\n\x18GetSecurityGroupResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x19\n\x11security_group_id\x18\x03 \x01(\t\"}\n\x16\x43reateIAMPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x35\n\x0e\x61\x63\x63\x65ss_options\x18\x02 \x03(\x0b\x32\x1d.services.aws.IAMAccessOption\"e\n\x0fIAMAccessOption\x12;\n\x10s3_access_option\x18\x01 \x01(\x0b\x32\x1f.services.aws.IAMS3AccessOptionH\x00\x42\x0f\n\raccess_optionJ\x04\x08\x02\x10\x03\"K\n\x17\x43reateIAMPolicyResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"C\n\x13GetIAMPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"S\n\x14GetIAMPolicyResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x12\n\npolicy_arn\x18\x03 \x01(\t\"F\n\x16\x44\x65leteIAMPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"K\n\x17\x44\x65leteIAMPolicyResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"\xa5\x01\n\x14\x43reateIAMRoleRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12*\n\x0bpolicy_refs\x18\x02 \x03(\x0b\x32\x15.common.resource.Meta\x12\x33\n\x0fservice_account\x18\x03 \x01(\x0b\x32\x1a.common.k8s.ServiceAccount\"I\n\x15\x43reateIAMRoleResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"A\n\x11GetIAMRoleRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"O\n\x12GetIAMRoleResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x10\n\x08role_arn\x18\x03 \x01(\t\"D\n\x14\x44\x65leteIAMRoleRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"I\n\x15\x44\x65leteIAMRoleResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"B\n\x11IAMS3AccessOption\x12\x0e\n\x06\x62ucket\x18\x01 \x01(\t\x12\x0f\n\x03\x64ir\x18\x02 \x01(\tB\x02\x18\x01\x12\x0c\n\x04\x64irs\x18\x03 \x03(\t\"Z\n\x1aIAMPrivateLinkAccessOption\x12\x1d\n\x15vpc_endpoints_tag_key\x18\x01 \x01(\t\x12\x1d\n\x15vpc_endpoints_tag_val\x18\x02 \x01(\t\"?\n\x19\x44\x65leteVPCEndpointsRequest\x12\x0f\n\x07tag_key\x18\x01 \x01(\t\x12\x11\n\ttag_value\x18\x02 \x01(\t\"M\n\x1a\x44\x65leteVPCEndpointsResponse\x12\x18\n\x10vpc_endpoint_ids\x18\x01 \x03(\t\x12\x15\n\rerror_message\x18\x02 \x01(\t\"\x8a\x02\n CreateSecurityGroupPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x1a\n\x12security_group_ids\x18\x02 \x03(\t\x12\x62\n\x13pod_labels_selector\x18\x03 \x03(\x0b\x32\x45.services.aws.CreateSecurityGroupPolicyRequest.PodLabelsSelectorEntry\x1a\x38\n\x16PodLabelsSelectorEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"U\n!CreateSecurityGroupPolicyResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"P\n DeleteSecurityGroupPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"U\n!DeleteSecurityGroupPolicyResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"M\n\x1dGetSecurityGroupPolicyRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"I\n\x1eGetSecurityGroupPolicyResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\"p\n*CheckVPCEndpointServiceReachabilityRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x14\n\x0cservice_name\x18\x02 \x01(\t\"\x9f\x01\n+CheckVPCEndpointServiceReachabilityResponse\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x42\n\x06status\x18\x02 \x01(\x0e\x32\x32.services.aws.VPCEndpointServiceReachabilityStatus\"\xa8\x02\n\x18\x43reateVPCEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12\x14\n\x0cservice_name\x18\x02 \x01(\t\x12\x1a\n\x12security_group_ids\x18\x03 \x03(\t\x12\x12\n\nsubnet_ids\x18\x04 \x03(\t\x12\x1b\n\x13private_dns_enabled\x18\x05 \x01(\x08\x12I\n\nextra_tags\x18\x06 \x03(\x0b\x32\x35.services.aws.CreateVPCEndpointRequest.ExtraTagsEntry\x1a\x30\n\x0e\x45xtraTagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"M\n\x19\x43reateVPCEndpointResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"H\n\x18\x44\x65leteVPCEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"M\n\x19\x44\x65leteVPCEndpointResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"E\n\x15GetVPCEndpointRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xa5\x01\n\x16GetVPCEndpointResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x13\n\x0b\x65ndpoint_id\x18\x02 \x01(\t\x12\x37\n\x0e\x65ndpoint_state\x18\x03 \x01(\x0e\x32\x1f.services.aws.VPCEndpointStatus\x12\x14\n\x0c\x65ndpoint_dns\x18\x04 \x01(\t\"q\n\x17\x43reateDBInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\x12(\n\x04spec\x18\x02 \x01(\x0b\x32\x1a.common.aws.DBInstanceSpec\"L\n\x18\x43reateDBInstanceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.creation.Status\"G\n\x17\x44\x65leteDBInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"L\n\x18\x44\x65leteDBInstanceResponse\x12\x30\n\x06status\x18\x01 \x01(\x0b\x32 .common.resource.deletion.Status\"F\n\x16StartDBInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"I\n\x17StartDBInstanceResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"E\n\x15StopDBInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"H\n\x16StopDBInstanceResponse\x12.\n\x06status\x18\x01 \x01(\x0b\x32\x1e.common.resource.update.Status\"D\n\x14GetDBInstanceRequest\x12,\n\rresource_meta\x18\x01 \x01(\x0b\x32\x15.common.resource.Meta\"\xa9\x01\n\x15GetDBInstanceResponse\x12\'\n\x06status\x18\x01 \x01(\x0b\x32\x17.common.resource.Status\x12\x30\n\x08\x65ndpoint\x18\x02 \x01(\x0b\x32\x1e.common.aws.DBInstanceEndpoint\x12\x35\n\x0finstance_status\x18\x03 \x01(\x0e\x32\x1c.common.aws.DBInstanceStatus*`\n$VPCEndpointServiceReachabilityStatus\x12\x1c\n\x18REACHABILITY_UNSPECIFIED\x10\x00\x12\x0b\n\x07SUCCESS\x10\x01\x12\r\n\tNOT_FOUND\x10\x02*\xa1\x01\n\x11VPCEndpointStatus\x12\x16\n\x12STATUS_UNSPECIFIED\x10\x00\x12\x16\n\x12PENDING_ACCEPTANCE\x10\x01\x12\x0b\n\x07PENDING\x10\x02\x12\r\n\tAVAILABLE\x10\x03\x12\x0c\n\x08REJECTED\x10\x04\x12\x0b\n\x07\x45XPIRED\x10\x05\x12\n\n\x06\x46\x41ILED\x10\x06\x12\x0c\n\x08\x44\x45LETING\x10\x07\x12\x0b\n\x07\x44\x45LETED\x10\x08\x32\xb7\x15\n\x12\x41wsResourceManager\x12\x92\x01\n\x1f\x43reateDataDirectoryDeletionTask\x12\x35.services.data.CreateDataDirectoryDeletionTaskRequest\x1a\x36.services.data.CreateDataDirectoryDeletionTaskResponse\"\x00\x12\x89\x01\n\x1c\x43reateDataDirectoryCloneTask\x12\x32.services.data.CreateDataDirectoryCloneTaskRequest\x1a\x33.services.data.CreateDataDirectoryCloneTaskResponse\"\x00\x12l\n\x13\x43reateSecurityGroup\x12(.services.aws.CreateSecurityGroupRequest\x1a).services.aws.CreateSecurityGroupResponse\"\x00\x12l\n\x13\x44\x65leteSecurityGroup\x12(.services.aws.DeleteSecurityGroupRequest\x1a).services.aws.DeleteSecurityGroupResponse\"\x00\x12\x63\n\x10GetSecurityGroup\x12%.services.aws.GetSecurityGroupRequest\x1a&.services.aws.GetSecurityGroupResponse\"\x00\x12~\n\x19\x43reateSecurityGroupPolicy\x12..services.aws.CreateSecurityGroupPolicyRequest\x1a/.services.aws.CreateSecurityGroupPolicyResponse\"\x00\x12~\n\x19\x44\x65leteSecurityGroupPolicy\x12..services.aws.DeleteSecurityGroupPolicyRequest\x1a/.services.aws.DeleteSecurityGroupPolicyResponse\"\x00\x12u\n\x16GetSecurityGroupPolicy\x12+.services.aws.GetSecurityGroupPolicyRequest\x1a,.services.aws.GetSecurityGroupPolicyResponse\"\x00\x12`\n\x0f\x43reateIAMPolicy\x12$.services.aws.CreateIAMPolicyRequest\x1a%.services.aws.CreateIAMPolicyResponse\"\x00\x12`\n\x0f\x44\x65leteIAMPolicy\x12$.services.aws.DeleteIAMPolicyRequest\x1a%.services.aws.DeleteIAMPolicyResponse\"\x00\x12W\n\x0cGetIAMPolicy\x12!.services.aws.GetIAMPolicyRequest\x1a\".services.aws.GetIAMPolicyResponse\"\x00\x12Z\n\rCreateIAMRole\x12\".services.aws.CreateIAMRoleRequest\x1a#.services.aws.CreateIAMRoleResponse\"\x00\x12Z\n\rDeleteIAMRole\x12\".services.aws.DeleteIAMRoleRequest\x1a#.services.aws.DeleteIAMRoleResponse\"\x00\x12Q\n\nGetIAMRole\x12\x1f.services.aws.GetIAMRoleRequest\x1a .services.aws.GetIAMRoleResponse\"\x00\x12\x9c\x01\n#CheckVPCEndpointServiceReachability\x12\x38.services.aws.CheckVPCEndpointServiceReachabilityRequest\x1a\x39.services.aws.CheckVPCEndpointServiceReachabilityResponse\"\x00\x12\x66\n\x11\x43reateVPCEndpoint\x12&.services.aws.CreateVPCEndpointRequest\x1a\'.services.aws.CreateVPCEndpointResponse\"\x00\x12\x66\n\x11\x44\x65leteVPCEndpoint\x12&.services.aws.DeleteVPCEndpointRequest\x1a\'.services.aws.DeleteVPCEndpointResponse\"\x00\x12]\n\x0eGetVPCEndpoint\x12#.services.aws.GetVPCEndpointRequest\x1a$.services.aws.GetVPCEndpointResponse\"\x00\x12\x92\x01\n\x1f\x43reateSimpleDataReplicationTask\x12\x35.services.data.CreateSimpleDataReplicationTaskRequest\x1a\x36.services.data.CreateSimpleDataReplicationTaskResponse\"\x00\x12\x63\n\x10\x43reateDBInstance\x12%.services.aws.CreateDBInstanceRequest\x1a&.services.aws.CreateDBInstanceResponse\"\x00\x12\x63\n\x10\x44\x65leteDBInstance\x12%.services.aws.DeleteDBInstanceRequest\x1a&.services.aws.DeleteDBInstanceResponse\"\x00\x12`\n\x0fStartDBInstance\x12$.services.aws.StartDBInstanceRequest\x1a%.services.aws.StartDBInstanceResponse\"\x00\x12]\n\x0eStopDBInstance\x12#.services.aws.StopDBInstanceRequest\x1a$.services.aws.StopDBInstanceResponse\"\x00\x12Z\n\rGetDBInstance\x12\".services.aws.GetDBInstanceRequest\x1a#.services.aws.GetDBInstanceResponse\"\x00\x12V\n\x0bGetManifest\x12!.services.data.GetManifestRequest\x1a\".services.data.GetManifestResponse\"\x00\x42\x39Z7github.com/risingwavelabs/cloudagent/pbgen/services/awsb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'services.aws_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z7github.com/risingwavelabs/cloudagent/pbgen/services/aws'
  _globals['_IAMS3ACCESSOPTION'].fields_by_name['dir']._loaded_options = None
  _globals['_IAMS3ACCESSOPTION'].fields_by_name['dir']._serialized_options = b'\030\001'
  _globals['_CREATESECURITYGROUPPOLICYREQUEST_PODLABELSSELECTORENTRY']._loaded_options = None
  _globals['_CREATESECURITYGROUPPOLICYREQUEST_PODLABELSSELECTORENTRY']._serialized_options = b'8\001'
  _globals['_CREATEVPCENDPOINTREQUEST_EXTRATAGSENTRY']._loaded_options = None
  _globals['_CREATEVPCENDPOINTREQUEST_EXTRATAGSENTRY']._serialized_options = b'8\001'
  _globals['_VPCENDPOINTSERVICEREACHABILITYSTATUS']._serialized_start=4990
  _globals['_VPCENDPOINTSERVICEREACHABILITYSTATUS']._serialized_end=5086
  _globals['_VPCENDPOINTSTATUS']._serialized_start=5089
  _globals['_VPCENDPOINTSTATUS']._serialized_end=5250
  _globals['_IPPERMISSION']._serialized_start=191
  _globals['_IPPERMISSION']._serialized_end=330
  _globals['_CREATESECURITYGROUPREQUEST']._serialized_start=333
  _globals['_CREATESECURITYGROUPREQUEST']._serialized_end=528
  _globals['_CREATESECURITYGROUPRESPONSE']._serialized_start=530
  _globals['_CREATESECURITYGROUPRESPONSE']._serialized_end=609
  _globals['_DELETESECURITYGROUPREQUEST']._serialized_start=611
  _globals['_DELETESECURITYGROUPREQUEST']._serialized_end=685
  _globals['_DELETESECURITYGROUPRESPONSE']._serialized_start=687
  _globals['_DELETESECURITYGROUPRESPONSE']._serialized_end=766
  _globals['_GETSECURITYGROUPREQUEST']._serialized_start=768
  _globals['_GETSECURITYGROUPREQUEST']._serialized_end=839
  _globals['_GETSECURITYGROUPRESPONSE']._serialized_start=841
  _globals['_GETSECURITYGROUPRESPONSE']._serialized_end=935
  _globals['_CREATEIAMPOLICYREQUEST']._serialized_start=937
  _globals['_CREATEIAMPOLICYREQUEST']._serialized_end=1062
  _globals['_IAMACCESSOPTION']._serialized_start=1064
  _globals['_IAMACCESSOPTION']._serialized_end=1165
  _globals['_CREATEIAMPOLICYRESPONSE']._serialized_start=1167
  _globals['_CREATEIAMPOLICYRESPONSE']._serialized_end=1242
  _globals['_GETIAMPOLICYREQUEST']._serialized_start=1244
  _globals['_GETIAMPOLICYREQUEST']._serialized_end=1311
  _globals['_GETIAMPOLICYRESPONSE']._serialized_start=1313
  _globals['_GETIAMPOLICYRESPONSE']._serialized_end=1396
  _globals['_DELETEIAMPOLICYREQUEST']._serialized_start=1398
  _globals['_DELETEIAMPOLICYREQUEST']._serialized_end=1468
  _globals['_DELETEIAMPOLICYRESPONSE']._serialized_start=1470
  _globals['_DELETEIAMPOLICYRESPONSE']._serialized_end=1545
  _globals['_CREATEIAMROLEREQUEST']._serialized_start=1548
  _globals['_CREATEIAMROLEREQUEST']._serialized_end=1713
  _globals['_CREATEIAMROLERESPONSE']._serialized_start=1715
  _globals['_CREATEIAMROLERESPONSE']._serialized_end=1788
  _globals['_GETIAMROLEREQUEST']._serialized_start=1790
  _globals['_GETIAMROLEREQUEST']._serialized_end=1855
  _globals['_GETIAMROLERESPONSE']._serialized_start=1857
  _globals['_GETIAMROLERESPONSE']._serialized_end=1936
  _globals['_DELETEIAMROLEREQUEST']._serialized_start=1938
  _globals['_DELETEIAMROLEREQUEST']._serialized_end=2006
  _globals['_DELETEIAMROLERESPONSE']._serialized_start=2008
  _globals['_DELETEIAMROLERESPONSE']._serialized_end=2081
  _globals['_IAMS3ACCESSOPTION']._serialized_start=2083
  _globals['_IAMS3ACCESSOPTION']._serialized_end=2149
  _globals['_IAMPRIVATELINKACCESSOPTION']._serialized_start=2151
  _globals['_IAMPRIVATELINKACCESSOPTION']._serialized_end=2241
  _globals['_DELETEVPCENDPOINTSREQUEST']._serialized_start=2243
  _globals['_DELETEVPCENDPOINTSREQUEST']._serialized_end=2306
  _globals['_DELETEVPCENDPOINTSRESPONSE']._serialized_start=2308
  _globals['_DELETEVPCENDPOINTSRESPONSE']._serialized_end=2385
  _globals['_CREATESECURITYGROUPPOLICYREQUEST']._serialized_start=2388
  _globals['_CREATESECURITYGROUPPOLICYREQUEST']._serialized_end=2654
  _globals['_CREATESECURITYGROUPPOLICYREQUEST_PODLABELSSELECTORENTRY']._serialized_start=2598
  _globals['_CREATESECURITYGROUPPOLICYREQUEST_PODLABELSSELECTORENTRY']._serialized_end=2654
  _globals['_CREATESECURITYGROUPPOLICYRESPONSE']._serialized_start=2656
  _globals['_CREATESECURITYGROUPPOLICYRESPONSE']._serialized_end=2741
  _globals['_DELETESECURITYGROUPPOLICYREQUEST']._serialized_start=2743
  _globals['_DELETESECURITYGROUPPOLICYREQUEST']._serialized_end=2823
  _globals['_DELETESECURITYGROUPPOLICYRESPONSE']._serialized_start=2825
  _globals['_DELETESECURITYGROUPPOLICYRESPONSE']._serialized_end=2910
  _globals['_GETSECURITYGROUPPOLICYREQUEST']._serialized_start=2912
  _globals['_GETSECURITYGROUPPOLICYREQUEST']._serialized_end=2989
  _globals['_GETSECURITYGROUPPOLICYRESPONSE']._serialized_start=2991
  _globals['_GETSECURITYGROUPPOLICYRESPONSE']._serialized_end=3064
  _globals['_CHECKVPCENDPOINTSERVICEREACHABILITYREQUEST']._serialized_start=3066
  _globals['_CHECKVPCENDPOINTSERVICEREACHABILITYREQUEST']._serialized_end=3178
  _globals['_CHECKVPCENDPOINTSERVICEREACHABILITYRESPONSE']._serialized_start=3181
  _globals['_CHECKVPCENDPOINTSERVICEREACHABILITYRESPONSE']._serialized_end=3340
  _globals['_CREATEVPCENDPOINTREQUEST']._serialized_start=3343
  _globals['_CREATEVPCENDPOINTREQUEST']._serialized_end=3639
  _globals['_CREATEVPCENDPOINTREQUEST_EXTRATAGSENTRY']._serialized_start=3591
  _globals['_CREATEVPCENDPOINTREQUEST_EXTRATAGSENTRY']._serialized_end=3639
  _globals['_CREATEVPCENDPOINTRESPONSE']._serialized_start=3641
  _globals['_CREATEVPCENDPOINTRESPONSE']._serialized_end=3718
  _globals['_DELETEVPCENDPOINTREQUEST']._serialized_start=3720
  _globals['_DELETEVPCENDPOINTREQUEST']._serialized_end=3792
  _globals['_DELETEVPCENDPOINTRESPONSE']._serialized_start=3794
  _globals['_DELETEVPCENDPOINTRESPONSE']._serialized_end=3871
  _globals['_GETVPCENDPOINTREQUEST']._serialized_start=3873
  _globals['_GETVPCENDPOINTREQUEST']._serialized_end=3942
  _globals['_GETVPCENDPOINTRESPONSE']._serialized_start=3945
  _globals['_GETVPCENDPOINTRESPONSE']._serialized_end=4110
  _globals['_CREATEDBINSTANCEREQUEST']._serialized_start=4112
  _globals['_CREATEDBINSTANCEREQUEST']._serialized_end=4225
  _globals['_CREATEDBINSTANCERESPONSE']._serialized_start=4227
  _globals['_CREATEDBINSTANCERESPONSE']._serialized_end=4303
  _globals['_DELETEDBINSTANCEREQUEST']._serialized_start=4305
  _globals['_DELETEDBINSTANCEREQUEST']._serialized_end=4376
  _globals['_DELETEDBINSTANCERESPONSE']._serialized_start=4378
  _globals['_DELETEDBINSTANCERESPONSE']._serialized_end=4454
  _globals['_STARTDBINSTANCEREQUEST']._serialized_start=4456
  _globals['_STARTDBINSTANCEREQUEST']._serialized_end=4526
  _globals['_STARTDBINSTANCERESPONSE']._serialized_start=4528
  _globals['_STARTDBINSTANCERESPONSE']._serialized_end=4601
  _globals['_STOPDBINSTANCEREQUEST']._serialized_start=4603
  _globals['_STOPDBINSTANCEREQUEST']._serialized_end=4672
  _globals['_STOPDBINSTANCERESPONSE']._serialized_start=4674
  _globals['_STOPDBINSTANCERESPONSE']._serialized_end=4746
  _globals['_GETDBINSTANCEREQUEST']._serialized_start=4748
  _globals['_GETDBINSTANCEREQUEST']._serialized_end=4816
  _globals['_GETDBINSTANCERESPONSE']._serialized_start=4819
  _globals['_GETDBINSTANCERESPONSE']._serialized_end=4988
  _globals['_AWSRESOURCEMANAGER']._serialized_start=5253
  _globals['_AWSRESOURCEMANAGER']._serialized_end=7996
# @@protoc_insertion_point(module_scope)
