# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: task/rwc.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'task/rwc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0etask/rwc.proto\x12\x08task.rwc\"R\n\x0bRisectlTask\x12\x17\n\x0frisingwave_name\x18\x01 \x01(\t\x12\x1c\n\x14risingwave_namespace\x18\x02 \x01(\t\x12\x0c\n\x04\x61rgs\x18\x03 \x03(\tB5Z3github.com/risingwavelabs/cloudagent/pbgen/task/rwcb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task.rwc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z3github.com/risingwavelabs/cloudagent/pbgen/task/rwc'
  _globals['_RISECTLTASK']._serialized_start=28
  _globals['_RISECTLTASK']._serialized_end=110
# @@protoc_insertion_point(module_scope)
