from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class AWSDirectoryCleanUpTask(_message.Message):
    __slots__ = ("bucket", "directory")
    BUCKET_FIELD_NUMBER: _ClassVar[int]
    DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    bucket: str
    directory: str
    def __init__(self, bucket: _Optional[str] = ..., directory: _Optional[str] = ...) -> None: ...

class AWSSimpleDataReplicationTask(_message.Message):
    __slots__ = ("source_bucket", "source_directory", "sink_bucket", "sink_directory")
    SOURCE_BUCKET_FIELD_NUMBER: _ClassVar[int]
    SOURCE_DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    SINK_BUCKET_FIELD_NUMBER: _ClassVar[int]
    SINK_DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    source_bucket: str
    source_directory: str
    sink_bucket: str
    sink_directory: str
    def __init__(self, source_bucket: _Optional[str] = ..., source_directory: _Optional[str] = ..., sink_bucket: _Optional[str] = ..., sink_directory: _Optional[str] = ...) -> None: ...

class AWSDirectoryCloneTask(_message.Message):
    __slots__ = ("source_directory_name", "source_bucket_name", "destination_directory_name", "destination_bucket_name", "cursor", "clone_size")
    SOURCE_DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    SOURCE_BUCKET_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_BUCKET_NAME_FIELD_NUMBER: _ClassVar[int]
    CURSOR_FIELD_NUMBER: _ClassVar[int]
    CLONE_SIZE_FIELD_NUMBER: _ClassVar[int]
    source_directory_name: str
    source_bucket_name: str
    destination_directory_name: str
    destination_bucket_name: str
    cursor: str
    clone_size: int
    def __init__(self, source_directory_name: _Optional[str] = ..., source_bucket_name: _Optional[str] = ..., destination_directory_name: _Optional[str] = ..., destination_bucket_name: _Optional[str] = ..., cursor: _Optional[str] = ..., clone_size: _Optional[int] = ...) -> None: ...
