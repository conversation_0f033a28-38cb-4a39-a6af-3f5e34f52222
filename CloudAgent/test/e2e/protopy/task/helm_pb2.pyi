from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class InstallReleaseTask(_message.Message):
    __slots__ = ("release_name", "release_namespace", "values_json", "chart_url")
    RELEASE_NAME_FIELD_NUMBER: _ClassVar[int]
    RELEASE_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    VALUES_JSON_FIELD_NUMBER: _ClassVar[int]
    CHART_URL_FIELD_NUMBER: _ClassVar[int]
    release_name: str
    release_namespace: str
    values_json: str
    chart_url: str
    def __init__(self, release_name: _Optional[str] = ..., release_namespace: _Optional[str] = ..., values_json: _Optional[str] = ..., chart_url: _Optional[str] = ...) -> None: ...

class UpgradeReleaseTask(_message.Message):
    __slots__ = ("release_name", "release_namespace", "values_json", "chart_url", "install")
    RELEASE_NAME_FIELD_NUMBER: _ClassVar[int]
    RELEASE_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    VALUES_JSON_FIELD_NUMBER: _ClassVar[int]
    CHART_URL_FIELD_NUMBER: _ClassVar[int]
    INSTALL_FIELD_NUMBER: _ClassVar[int]
    release_name: str
    release_namespace: str
    values_json: str
    chart_url: str
    install: bool
    def __init__(self, release_name: _Optional[str] = ..., release_namespace: _Optional[str] = ..., values_json: _Optional[str] = ..., chart_url: _Optional[str] = ..., install: bool = ...) -> None: ...

class UninstallReleaseTask(_message.Message):
    __slots__ = ("release_name", "release_namespace")
    RELEASE_NAME_FIELD_NUMBER: _ClassVar[int]
    RELEASE_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    release_name: str
    release_namespace: str
    def __init__(self, release_name: _Optional[str] = ..., release_namespace: _Optional[str] = ...) -> None: ...
