from task import rwc_pb2 as _rwc_pb2
from task import gcp_pb2 as _gcp_pb2
from task import helm_pb2 as _helm_pb2
from task import aws_pb2 as _aws_pb2
from task import azr_pb2 as _azr_pb2
from task import byoc_pb2 as _byoc_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class Task(_message.Message):
    __slots__ = ("risectl_task", "gcs_directory_clean_up_task", "install_helm_release_task", "upgrade_helm_release_task", "uninstall_helm_release_task", "aws_directory_clean_up_task", "azr_directory_clean_up_task", "aws_simple_data_replication_task", "apply_byoc_module_task", "retrieve_byoc_module_output_task", "aws_directory_clone_task", "gcp_directory_clone_task", "azr_directory_clone_task")
    RISECTL_TASK_FIELD_NUMBER: _ClassVar[int]
    GCS_DIRECTORY_CLEAN_UP_TASK_FIELD_NUMBER: _ClassVar[int]
    INSTALL_HELM_RELEASE_TASK_FIELD_NUMBER: _ClassVar[int]
    UPGRADE_HELM_RELEASE_TASK_FIELD_NUMBER: _ClassVar[int]
    UNINSTALL_HELM_RELEASE_TASK_FIELD_NUMBER: _ClassVar[int]
    AWS_DIRECTORY_CLEAN_UP_TASK_FIELD_NUMBER: _ClassVar[int]
    AZR_DIRECTORY_CLEAN_UP_TASK_FIELD_NUMBER: _ClassVar[int]
    AWS_SIMPLE_DATA_REPLICATION_TASK_FIELD_NUMBER: _ClassVar[int]
    APPLY_BYOC_MODULE_TASK_FIELD_NUMBER: _ClassVar[int]
    RETRIEVE_BYOC_MODULE_OUTPUT_TASK_FIELD_NUMBER: _ClassVar[int]
    AWS_DIRECTORY_CLONE_TASK_FIELD_NUMBER: _ClassVar[int]
    GCP_DIRECTORY_CLONE_TASK_FIELD_NUMBER: _ClassVar[int]
    AZR_DIRECTORY_CLONE_TASK_FIELD_NUMBER: _ClassVar[int]
    risectl_task: _rwc_pb2.RisectlTask
    gcs_directory_clean_up_task: _gcp_pb2.GCSDirectoryCleanUpTask
    install_helm_release_task: _helm_pb2.InstallReleaseTask
    upgrade_helm_release_task: _helm_pb2.UpgradeReleaseTask
    uninstall_helm_release_task: _helm_pb2.UninstallReleaseTask
    aws_directory_clean_up_task: _aws_pb2.AWSDirectoryCleanUpTask
    azr_directory_clean_up_task: _azr_pb2.AZRDirectoryCleanUpTask
    aws_simple_data_replication_task: _aws_pb2.AWSSimpleDataReplicationTask
    apply_byoc_module_task: _byoc_pb2.ApplyByocModuleTask
    retrieve_byoc_module_output_task: _byoc_pb2.RetrieveByocModuleOutputTask
    aws_directory_clone_task: _aws_pb2.AWSDirectoryCloneTask
    gcp_directory_clone_task: _gcp_pb2.GCSDirectoryCloneTask
    azr_directory_clone_task: _azr_pb2.AZRDirectoryCloneTask
    def __init__(self, risectl_task: _Optional[_Union[_rwc_pb2.RisectlTask, _Mapping]] = ..., gcs_directory_clean_up_task: _Optional[_Union[_gcp_pb2.GCSDirectoryCleanUpTask, _Mapping]] = ..., install_helm_release_task: _Optional[_Union[_helm_pb2.InstallReleaseTask, _Mapping]] = ..., upgrade_helm_release_task: _Optional[_Union[_helm_pb2.UpgradeReleaseTask, _Mapping]] = ..., uninstall_helm_release_task: _Optional[_Union[_helm_pb2.UninstallReleaseTask, _Mapping]] = ..., aws_directory_clean_up_task: _Optional[_Union[_aws_pb2.AWSDirectoryCleanUpTask, _Mapping]] = ..., azr_directory_clean_up_task: _Optional[_Union[_azr_pb2.AZRDirectoryCleanUpTask, _Mapping]] = ..., aws_simple_data_replication_task: _Optional[_Union[_aws_pb2.AWSSimpleDataReplicationTask, _Mapping]] = ..., apply_byoc_module_task: _Optional[_Union[_byoc_pb2.ApplyByocModuleTask, _Mapping]] = ..., retrieve_byoc_module_output_task: _Optional[_Union[_byoc_pb2.RetrieveByocModuleOutputTask, _Mapping]] = ..., aws_directory_clone_task: _Optional[_Union[_aws_pb2.AWSDirectoryCloneTask, _Mapping]] = ..., gcp_directory_clone_task: _Optional[_Union[_gcp_pb2.GCSDirectoryCloneTask, _Mapping]] = ..., azr_directory_clone_task: _Optional[_Union[_azr_pb2.AZRDirectoryCloneTask, _Mapping]] = ...) -> None: ...
