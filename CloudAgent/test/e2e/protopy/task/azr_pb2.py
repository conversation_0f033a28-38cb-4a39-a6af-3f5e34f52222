# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: task/azr.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'task/azr.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0etask/azr.proto\x12\x08task.azr\"X\n\x17\x41ZRDirectoryCleanUpTask\x12\x11\n\tcontainer\x18\x01 \x01(\t\x12\x11\n\tdirectory\x18\x02 \x01(\t\x12\x17\n\x0fstorage_account\x18\x03 \x01(\t\"\x86\x02\n\x15\x41ZRDirectoryCloneTask\x12\x1d\n\x15source_directory_name\x18\x01 \x01(\t\x12\x1d\n\x15source_container_name\x18\x02 \x01(\t\x12\"\n\x1a\x64\x65stination_directory_name\x18\x03 \x01(\t\x12\"\n\x1a\x64\x65stination_container_name\x18\x04 \x01(\t\x12\x1e\n\x16source_storage_account\x18\x05 \x01(\t\x12#\n\x1b\x64\x65stination_storage_account\x18\x06 \x01(\t\x12\x0e\n\x06\x63ursor\x18\x07 \x01(\t\x12\x12\n\nclone_size\x18\x08 \x01(\x05\x42\x35Z3github.com/risingwavelabs/cloudagent/pbgen/task/azrb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task.azr_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z3github.com/risingwavelabs/cloudagent/pbgen/task/azr'
  _globals['_AZRDIRECTORYCLEANUPTASK']._serialized_start=28
  _globals['_AZRDIRECTORYCLEANUPTASK']._serialized_end=116
  _globals['_AZRDIRECTORYCLONETASK']._serialized_start=119
  _globals['_AZRDIRECTORYCLONETASK']._serialized_end=381
# @@protoc_insertion_point(module_scope)
