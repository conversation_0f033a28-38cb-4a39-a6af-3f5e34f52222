from google.protobuf.internal import containers as _containers
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Iterable as _Iterable, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class RisectlTask(_message.Message):
    __slots__ = ("risingwave_name", "risingwave_namespace", "args")
    RISINGWAVE_NAME_FIELD_NUMBER: _ClassVar[int]
    RISINGWAVE_NAMESPACE_FIELD_NUMBER: _ClassVar[int]
    ARGS_FIELD_NUMBER: _ClassVar[int]
    risingwave_name: str
    risingwave_namespace: str
    args: _containers.RepeatedScalarFieldContainer[str]
    def __init__(self, risingwave_name: _Optional[str] = ..., risingwave_namespace: _Optional[str] = ..., args: _Optional[_Iterable[str]] = ...) -> None: ...
