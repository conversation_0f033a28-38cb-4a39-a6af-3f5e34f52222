# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: task/task.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'task/task.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from task import rwc_pb2 as task_dot_rwc__pb2
from task import gcp_pb2 as task_dot_gcp__pb2
from task import helm_pb2 as task_dot_helm__pb2
from task import aws_pb2 as task_dot_aws__pb2
from task import azr_pb2 as task_dot_azr__pb2
from task import byoc_pb2 as task_dot_byoc__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ftask/task.proto\x12\x04task\x1a\x0etask/rwc.proto\x1a\x0etask/gcp.proto\x1a\x0ftask/helm.proto\x1a\x0etask/aws.proto\x1a\x0etask/azr.proto\x1a\x0ftask/byoc.proto\"\xa5\x07\n\x04Task\x12-\n\x0crisectl_task\x18\x01 \x01(\x0b\x32\x15.task.rwc.RisectlTaskH\x00\x12H\n\x1bgcs_directory_clean_up_task\x18\x02 \x01(\x0b\x32!.task.gcp.GCSDirectoryCleanUpTaskH\x00\x12\x42\n\x19install_helm_release_task\x18\x03 \x01(\x0b\x32\x1d.task.helm.InstallReleaseTaskH\x00\x12\x42\n\x19upgrade_helm_release_task\x18\x04 \x01(\x0b\x32\x1d.task.helm.UpgradeReleaseTaskH\x00\x12\x46\n\x1buninstall_helm_release_task\x18\x05 \x01(\x0b\x32\x1f.task.helm.UninstallReleaseTaskH\x00\x12H\n\x1b\x61ws_directory_clean_up_task\x18\x06 \x01(\x0b\x32!.task.aws.AWSDirectoryCleanUpTaskH\x00\x12H\n\x1b\x61zr_directory_clean_up_task\x18\x07 \x01(\x0b\x32!.task.azr.AZRDirectoryCleanUpTaskH\x00\x12R\n aws_simple_data_replication_task\x18\x08 \x01(\x0b\x32&.task.aws.AWSSimpleDataReplicationTaskH\x00\x12@\n\x16\x61pply_byoc_module_task\x18\t \x01(\x0b\x32\x1e.task.byoc.ApplyByocModuleTaskH\x00\x12S\n retrieve_byoc_module_output_task\x18\n \x01(\x0b\x32\'.task.byoc.RetrieveByocModuleOutputTaskH\x00\x12\x43\n\x18\x61ws_directory_clone_task\x18\x0b \x01(\x0b\x32\x1f.task.aws.AWSDirectoryCloneTaskH\x00\x12\x43\n\x18gcp_directory_clone_task\x18\x0c \x01(\x0b\x32\x1f.task.gcp.GCSDirectoryCloneTaskH\x00\x12\x43\n\x18\x61zr_directory_clone_task\x18\r \x01(\x0b\x32\x1f.task.azr.AZRDirectoryCloneTaskH\x00\x42\x06\n\x04taskB1Z/github.com/risingwavelabs/cloudagent/pbgen/taskb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task.task_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z/github.com/risingwavelabs/cloudagent/pbgen/task'
  _globals['_TASK']._serialized_start=124
  _globals['_TASK']._serialized_end=1057
# @@protoc_insertion_point(module_scope)
