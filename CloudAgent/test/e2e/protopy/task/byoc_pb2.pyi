from common import byoc_pb2 as _byoc_pb2
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Mapping as _Mapping, Optional as _Optional, Union as _Union

DESCRIPTOR: _descriptor.FileDescriptor

class ApplyByocModuleTask(_message.Message):
    __slots__ = ("module_options", "apply_options", "package_options")
    MODULE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    APPLY_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    module_options: _byoc_pb2.ModuleOptions
    apply_options: _byoc_pb2.ApplyOptions
    package_options: _byoc_pb2.PackageOptions
    def __init__(self, module_options: _Optional[_Union[_byoc_pb2.ModuleOptions, _Mapping]] = ..., apply_options: _Optional[_Union[_byoc_pb2.ApplyOptions, _Mapping]] = ..., package_options: _Optional[_Union[_byoc_pb2.PackageOptions, _Mapping]] = ...) -> None: ...

class RetrieveByocModuleOutputTask(_message.Message):
    __slots__ = ("output_key", "module_options", "output_options", "package_options")
    OUTPUT_KEY_FIELD_NUMBER: _ClassVar[int]
    MODULE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    OUTPUT_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    PACKAGE_OPTIONS_FIELD_NUMBER: _ClassVar[int]
    output_key: str
    module_options: _byoc_pb2.ModuleOptions
    output_options: _byoc_pb2.OutputOptions
    package_options: _byoc_pb2.PackageOptions
    def __init__(self, output_key: _Optional[str] = ..., module_options: _Optional[_Union[_byoc_pb2.ModuleOptions, _Mapping]] = ..., output_options: _Optional[_Union[_byoc_pb2.OutputOptions, _Mapping]] = ..., package_options: _Optional[_Union[_byoc_pb2.PackageOptions, _Mapping]] = ...) -> None: ...
