# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: task/aws.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'task/aws.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0etask/aws.proto\x12\x08task.aws\"<\n\x17\x41WSDirectoryCleanUpTask\x12\x0e\n\x06\x62ucket\x18\x01 \x01(\t\x12\x11\n\tdirectory\x18\x02 \x01(\t\"|\n\x1c\x41WSSimpleDataReplicationTask\x12\x15\n\rsource_bucket\x18\x01 \x01(\t\x12\x18\n\x10source_directory\x18\x02 \x01(\t\x12\x13\n\x0bsink_bucket\x18\x03 \x01(\t\x12\x16\n\x0esink_directory\x18\x04 \x01(\t\"\xbb\x01\n\x15\x41WSDirectoryCloneTask\x12\x1d\n\x15source_directory_name\x18\x01 \x01(\t\x12\x1a\n\x12source_bucket_name\x18\x02 \x01(\t\x12\"\n\x1a\x64\x65stination_directory_name\x18\x03 \x01(\t\x12\x1f\n\x17\x64\x65stination_bucket_name\x18\x04 \x01(\t\x12\x0e\n\x06\x63ursor\x18\x05 \x01(\t\x12\x12\n\nclone_size\x18\x06 \x01(\x05\x42\x35Z3github.com/risingwavelabs/cloudagent/pbgen/task/awsb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task.aws_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z3github.com/risingwavelabs/cloudagent/pbgen/task/aws'
  _globals['_AWSDIRECTORYCLEANUPTASK']._serialized_start=28
  _globals['_AWSDIRECTORYCLEANUPTASK']._serialized_end=88
  _globals['_AWSSIMPLEDATAREPLICATIONTASK']._serialized_start=90
  _globals['_AWSSIMPLEDATAREPLICATIONTASK']._serialized_end=214
  _globals['_AWSDIRECTORYCLONETASK']._serialized_start=217
  _globals['_AWSDIRECTORYCLONETASK']._serialized_end=404
# @@protoc_insertion_point(module_scope)
