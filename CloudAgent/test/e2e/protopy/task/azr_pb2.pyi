from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from typing import ClassVar as _ClassVar, Optional as _Optional

DESCRIPTOR: _descriptor.FileDescriptor

class AZRDirectoryCleanUpTask(_message.Message):
    __slots__ = ("container", "directory", "storage_account")
    CONTAINER_FIELD_NUMBER: _ClassVar[int]
    DIRECTORY_FIELD_NUMBER: _ClassVar[int]
    STORAGE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    container: str
    directory: str
    storage_account: str
    def __init__(self, container: _Optional[str] = ..., directory: _Optional[str] = ..., storage_account: _Optional[str] = ...) -> None: ...

class AZRDirectoryCloneTask(_message.Message):
    __slots__ = ("source_directory_name", "source_container_name", "destination_directory_name", "destination_container_name", "source_storage_account", "destination_storage_account", "cursor", "clone_size")
    SOURCE_DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    SOURCE_CONTAINER_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_DIRECTORY_NAME_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_CONTAINER_NAME_FIELD_NUMBER: _ClassVar[int]
    SOURCE_STORAGE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    DESTINATION_STORAGE_ACCOUNT_FIELD_NUMBER: _ClassVar[int]
    CURSOR_FIELD_NUMBER: _ClassVar[int]
    CLONE_SIZE_FIELD_NUMBER: _ClassVar[int]
    source_directory_name: str
    source_container_name: str
    destination_directory_name: str
    destination_container_name: str
    source_storage_account: str
    destination_storage_account: str
    cursor: str
    clone_size: int
    def __init__(self, source_directory_name: _Optional[str] = ..., source_container_name: _Optional[str] = ..., destination_directory_name: _Optional[str] = ..., destination_container_name: _Optional[str] = ..., source_storage_account: _Optional[str] = ..., destination_storage_account: _Optional[str] = ..., cursor: _Optional[str] = ..., clone_size: _Optional[int] = ...) -> None: ...
