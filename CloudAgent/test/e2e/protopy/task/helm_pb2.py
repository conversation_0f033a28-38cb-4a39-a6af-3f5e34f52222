# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: task/helm.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'task/helm.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ftask/helm.proto\x12\ttask.helm\"m\n\x12InstallReleaseTask\x12\x14\n\x0crelease_name\x18\x01 \x01(\t\x12\x19\n\x11release_namespace\x18\x02 \x01(\t\x12\x13\n\x0bvalues_json\x18\x03 \x01(\t\x12\x11\n\tchart_url\x18\x04 \x01(\t\"~\n\x12UpgradeReleaseTask\x12\x14\n\x0crelease_name\x18\x01 \x01(\t\x12\x19\n\x11release_namespace\x18\x02 \x01(\t\x12\x13\n\x0bvalues_json\x18\x03 \x01(\t\x12\x11\n\tchart_url\x18\x04 \x01(\t\x12\x0f\n\x07install\x18\x05 \x01(\x08\"G\n\x14UninstallReleaseTask\x12\x14\n\x0crelease_name\x18\x01 \x01(\t\x12\x19\n\x11release_namespace\x18\x02 \x01(\tB6Z4github.com/risingwavelabs/cloudagent/pbgen/task/helmb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task.helm_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z4github.com/risingwavelabs/cloudagent/pbgen/task/helm'
  _globals['_INSTALLRELEASETASK']._serialized_start=30
  _globals['_INSTALLRELEASETASK']._serialized_end=139
  _globals['_UPGRADERELEASETASK']._serialized_start=141
  _globals['_UPGRADERELEASETASK']._serialized_end=267
  _globals['_UNINSTALLRELEASETASK']._serialized_start=269
  _globals['_UNINSTALLRELEASETASK']._serialized_end=340
# @@protoc_insertion_point(module_scope)
