# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# NO CHECKED-IN PROTOBUF GENCODE
# source: task/byoc.proto
# Protobuf Python Version: 5.27.2
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import runtime_version as _runtime_version
from google.protobuf import symbol_database as _symbol_database
from google.protobuf.internal import builder as _builder
_runtime_version.ValidateProtobufRuntimeVersion(
    _runtime_version.Domain.PUBLIC,
    5,
    27,
    2,
    '',
    'task/byoc.proto'
)
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from common import byoc_pb2 as common_dot_byoc__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x0ftask/byoc.proto\x12\ttask.byoc\x1a\x11\x63ommon/byoc.proto\"\xb1\x01\n\x13\x41pplyByocModuleTask\x12\x32\n\x0emodule_options\x18\x01 \x01(\x0b\x32\x1a.common.byoc.ModuleOptions\x12\x30\n\rapply_options\x18\x02 \x01(\x0b\x32\x19.common.byoc.ApplyOptions\x12\x34\n\x0fpackage_options\x18\x03 \x01(\x0b\x32\x1b.common.byoc.PackageOptions\"\xd0\x01\n\x1cRetrieveByocModuleOutputTask\x12\x12\n\noutput_key\x18\x01 \x01(\t\x12\x32\n\x0emodule_options\x18\x02 \x01(\x0b\x32\x1a.common.byoc.ModuleOptions\x12\x32\n\x0eoutput_options\x18\x03 \x01(\x0b\x32\x1a.common.byoc.OutputOptions\x12\x34\n\x0fpackage_options\x18\x04 \x01(\x0b\x32\x1b.common.byoc.PackageOptionsB6Z4github.com/risingwavelabs/cloudagent/pbgen/task/byocb\x06proto3')

_globals = globals()
_builder.BuildMessageAndEnumDescriptors(DESCRIPTOR, _globals)
_builder.BuildTopDescriptorsAndMessages(DESCRIPTOR, 'task.byoc_pb2', _globals)
if not _descriptor._USE_C_DESCRIPTORS:
  _globals['DESCRIPTOR']._loaded_options = None
  _globals['DESCRIPTOR']._serialized_options = b'Z4github.com/risingwavelabs/cloudagent/pbgen/task/byoc'
  _globals['_APPLYBYOCMODULETASK']._serialized_start=50
  _globals['_APPLYBYOCMODULETASK']._serialized_end=227
  _globals['_RETRIEVEBYOCMODULEOUTPUTTASK']._serialized_start=230
  _globals['_RETRIEVEBYOCMODULEOUTPUTTASK']._serialized_end=438
# @@protoc_insertion_point(module_scope)
