apiVersion: risingwave.risingwavelabs.com/v1alpha1
kind: RisingWave
metadata:
  name: {name}
  namespace: {namespace}
spec:
  stateStore:
    memory: true
  metaStore:
    memory: true
  image: ghcr.io/risingwavelabs/risingwave:{risingwave_version}
  components:
    meta:
      nodeGroups:
      - replicas: 1
        name: ""
    frontend:
      nodeGroups:
      - replicas: 1
        name: ""
    compute:
      nodeGroups:
      - replicas: 1
        name: ""
    compactor:
      nodeGroups:
      - replicas: 1
        name: ""
---
apiVersion: v1
kind: Service
metadata:
  name: demosrc
  namespace: {namespace}
spec:
  selector:
    app: demosrc
  ports:
  - name: kafka-svc
    protocol: TCP
    port: 9092
    targetPort: 9092
  type: ClusterIP
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: demosrc
  namespace: {namespace}
  labels:
    app: demosrc
spec:
  replicas: 1
  selector:
    matchLabels:
      app: demosrc
  template:
    metadata:
      labels:
        app: demosrc
    spec:
      containers:
      - name: producer-twitter
        image: {datagen_image}
        command:
          - /bin/sh
        args:
          - -c
          - 'while true; do /datagen --heavytail --mode twitter --qps 10 kafka --brokers 127.0.0.1:9092 | while read line ; do echo $line; done; echo "trying to restart producer..."; sleep 5; done'
      - name: kafka
        image: bitnami/kafka:3.2.3
        ports:
        - containerPort: 9092
        env:
        - name: KAFKA_ENABLE_KRAFT
          value: "yes"
        - name: KAFKA_CFG_PROCESS_ROLES
          value: broker,controller
        - name: KAFKA_CFG_CONTROLLER_LISTENER_NAMES
          value: CONTROLLER
        - name: KAFKA_CFG_LISTENERS
          value: PLAINTEXT://:9092,CONTROLLER://:9093
        - name: KAFKA_CFG_LISTENER_SECURITY_PROTOCOL_MAP
          value: CONTROLLER:PLAINTEXT,PLAINTEXT:PLAINTEXT
        - name: KAFKA_CFG_ADVERTISED_LISTENERS
          value: PLAINTEXT://demosrc.{namespace}.svc.cluster.local:9092
        - name: KAFKA_BROKER_ID
          value: "1"
        - name: KAFKA_CFG_CONTROLLER_QUORUM_VOTERS
          value: 1@127.0.0.1:9093
        - name: ALLOW_PLAINTEXT_LISTENER
          value: "yes"
        - name: KAFKA_CFG_AUTO_CREATE_TOPICS_ENABLE
          value: "true"
        - name: KAFKA_CFG_LOG_RETENTION_BYTES
          value: "10737418240"
        - name: KAFKA_CFG_LOG_RETENTION_HOURS
          value: "168"
        - name: KAFKA_CFG_DEFAULT_REPLICATION_FACTOR
          value: "1"
        - name: KAFKA_CFG_NUM_NETWORK_THREADS
          value: "16"
        - name: KAFKA_CFG_NUM_IO_THREADS
          value: "10"
        - name: KAFKA_CFG_NODE_ID
          value: "1"
