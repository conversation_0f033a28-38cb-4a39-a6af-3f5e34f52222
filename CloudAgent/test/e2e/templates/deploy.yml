apiVersion: v1
kind: Namespace
metadata:
  name: cloudagent
  labels:
    name: cloudagent
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: cloudagent
  namespace: cloudagent
rules:
- apiGroups:
  - 'crossplane.io'
  - 'services.k8s.aws'
  - 'ec2.services.k8s.aws'
  - 'risingwave.risingwavelabs.com'
  - 'batch'
  - 'policy' # for starting new pods 
  - 'apps' # for starting statefulsets
  - 'rbac.authorization.k8s.io'
  - 'iam.services.k8s.aws'
  - 'iamserviceaccounts.iam.cnrm.cloud.google.com'
  - 'iampolicies.iam.cnrm.cloud.google.com'
  - 'iam.cnrm.cloud.google.com'
  - 'compute.cnrm.cloud.google.com'
  - 'vpcresources.k8s.aws' # for AWS CNI resource: SecurityGroupPolicy
  - '' # core API 
  - 'monitoring.coreos.com'
  - 'monitoring.googleapis.com'
  resources: ["*"]
  verbs: ["*"]
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: {cloud_agent_service_account_name}
  namespace: cloudagent
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: cloudagent-role-binding
subjects:
- namespace: cloudagent
  kind: ServiceAccount
  name: {cloud_agent_service_account_name}
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: {cloud_agent_cluster_role_name}
---
apiVersion: apps/v1
kind: Deployment
metadata:
  name: cloudagent
  namespace: cloudagent
  labels:
    app: cloudagent
spec:
  replicas: 1
  selector:
    matchLabels:
      app: cloudagent
  strategy:
    type: Recreate
  template:
    metadata:
      labels:
        app: cloudagent
    spec:
      serviceAccountName: cloudagent
      containers:
      - name: cloudagent
        image: {cloud_agent_image}
        imagePullPolicy: IfNotPresent
        ports:
        - containerPort: 40001
        - containerPort: 40090
        volumeMounts:
        - name: app
          mountPath: /app
        - name: go-build
          mountPath: /root/.cache/go-build
        - name: go-mod
          mountPath: /go/pkg/mod
        - name: google-cloud-key
          mountPath: /etc/key
        env:
        - name: GOOGLE_APPLICATION_CREDENTIALS
          value: /etc/key/key.json
      volumes:
      - name: google-cloud-key
        # The secret must contain a file named 'key.json' with your service account credentials.
        secret:
          secretName: {gcp_secret}
          items:
            - key: key.json
              path: key.json
      - name: app
        persistentVolumeClaim:
          claimName: source-code
      - name: go-build
        persistentVolumeClaim:
          claimName: go-build
      - name: go-mod
        persistentVolumeClaim:
          claimName: go-mod
---
apiVersion: v1
kind: Service
metadata:
  name: cloudagent
  namespace: cloudagent
spec:
  selector:
    app: cloudagent
  ports:
  - port: 40001
    name: agent-port
    targetPort: 40001
    nodePort: {agent_port}
  - port: 40090
    name: metrics-port
    targetPort: 40090
    nodePort: {metrics_port}
  type: NodePort
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: source-code
  namespace: cloudagent
spec:
  storageClassName: standard
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 2Gi
  hostPath:
    path: /source_code_root
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: source-code
  namespace: cloudagent
spec:
  volumeName: source-code
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: source-code
  namespace: cloudagent
spec:
  storageClassName: standard
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 2Gi
  hostPath:
    path: /source_code_root
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: source-code
  namespace: cloudagent
spec:
  volumeName: source-code
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: go-build
  namespace: cloudagent
spec:
  storageClassName: standard
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 2Gi
  hostPath:
    path: /go-cache/go-build
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: go-build
  namespace: cloudagent
spec:
  volumeName: go-build
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: PersistentVolume
metadata:
  name: go-mod
  namespace: cloudagent
spec:
  storageClassName: standard
  accessModes:
    - ReadWriteOnce
  capacity:
    storage: 2Gi
  hostPath:
    path: /go-cache/mod
---
apiVersion: v1
kind: PersistentVolumeClaim
metadata:
  name: go-mod
  namespace: cloudagent
spec:
  volumeName: go-mod
  accessModes:
    - ReadWriteOnce
  resources:
    requests:
      storage: 2Gi
---
apiVersion: v1
kind: Pod
metadata:
  name: psql
spec:
  containers:
  - name: psql
    image: local/psql-client:latest
    imagePullPolicy: IfNotPresent
    args:
    - sleep
    - infinity
