apiVersion: v1
kind: Service
metadata:
  name: risingwave-etcd
  namespace: {namespace}
  labels:
    app: risingwave-etcd
spec:
  ports:
  - port: 2388
    name: client
  - port: 2389
    name: peer
  selector:
    app: risingwave-etcd
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: risingwave-etcd
  name: {name}
  namespace: {namespace}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: risingwave-etcd
  serviceName: risingwave-etcd
  template:
    metadata:
      labels:
        app: risingwave-etcd
    spec:
      containers:
        - name: etcd
          image: bitnami/etcd:latest
          imagePullPolicy: IfNotPresent
          env:
          - name: ALLOW_NONE_AUTHENTICATION
            value: "yes"
          - name: ETCDCTL_API
            value: "3"
          ports:
          - containerPort: 2389
            name: peer
            protocol: TCP
          - containerPort: 2380
            name: client
            protocol: TCP
