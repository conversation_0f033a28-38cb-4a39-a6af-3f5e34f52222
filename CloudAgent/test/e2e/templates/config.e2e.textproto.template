k8s_config {{
  cluster_id: "local-dev"
  in_cluster_auth {{}}
  task_config {{
    image: "{task_runner_image_tag}"
    service_account: "{service_account_name}"
    namespace: "cloudagent"
    pull_policy: PULL_IF_NOT_PRESENT
  }}
  allow_helm_charts: "https://charts.bitnami.com/bitnami/etcd-*"
}}
tls_config {{
  cert_path: "certs/tls.crt"
  key_path: "certs/tls.key"
  client_ca_path: "certs/ca.crt"
}}
aws_config {{
  account_id: ""
  region: "{aws_region}"
  oidc_provider: ""
  vpc_id: "{aws_vpc_id}"
  static_creds {{
    access_key_id: "{aws_access_key_id}"
    secret_access_key: "{aws_secret_access_key}"
  }}
  default_tags: {aws_default_tags}
}}
