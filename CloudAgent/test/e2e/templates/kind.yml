kind: Cluster
apiVersion: kind.x-k8s.io/v1alpha4
networking:
  # WARNING: It is _strongly_ recommended that you keep this the default
  # (127.0.0.1) for security reasons. However it is possible to change this.
  apiServerAddress: "0.0.0.0"
nodes:
- role: control-plane
  extraMounts:
  - hostPath: {project_dir}
    containerPath: /source_code_root
  - hostPath: {cache_dir}
    containerPath: /go-cache
  extraPortMappings:
  - containerPort: {agent_port}
    hostPort: {agent_port}
  - containerPort: {metrics_port}
    hostPort: {metrics_port}
