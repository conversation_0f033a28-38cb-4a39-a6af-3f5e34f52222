apiVersion: v1
kind: ConfigMap
metadata:
  name: risingwave-customize-config
  namespace: {namespace}
data:
  risingwave.toml: |-
    [system]
    backup_storage_url = "s3://{bucket_name}"
    backup_storage_directory = "{backup_directory}"

    [streaming]
    checkpoint_interval_ms = 100

    [storage]
    data_directory = "{data_directory}"
---
apiVersion: v1
kind: Service
metadata:
  name: risingwave-etcd
  namespace: {namespace}
  labels:
    app: risingwave-etcd
spec:
  ports:
  - port: 2388
    name: client
  - port: 2389
    name: peer
  selector:
    app: risingwave-etcd
---
apiVersion: apps/v1
kind: StatefulSet
metadata:
  labels:
    app: risingwave-etcd
  name: {etcd_name}
  namespace: {namespace}
spec:
  replicas: 1
  selector:
    matchLabels:
      app: risingwave-etcd
  serviceName: risingwave-etcd
  template:
    metadata:
      labels:
        app: risingwave-etcd
    spec:
      containers:
        - name: etcd
          image: quay.io/coreos/etcd:latest
          imagePullPolicy: IfNotPresent
          command:
          - /usr/local/bin/etcd
          args:
          - "--listen-client-urls"
          - "http://0.0.0.0:2388"
          - "--advertise-client-urls"
          - "http://risingwave-etcd-0:2388"
          - "--listen-peer-urls"
          - "http://0.0.0.0:2389"
          - "--initial-advertise-peer-urls"
          - "http://risingwave-etcd-0:2389"
          - "--listen-metrics-urls"
          - "http://0.0.0.0:2379"
          - "--name"
          - "risingwave-etcd"
          - "--max-txn-ops"
          - "999999"
          - "--max-request-bytes"
          - "10485760"
          - "--auto-compaction-mode"
          - periodic
          - "--auto-compaction-retention"
          - 1m
          - "--snapshot-count"
          - "10000"
          env:
          - name: ALLOW_NONE_AUTHENTICATION
            value: "1"
          - name: ETCDCTL_API
            value: "3"
          ports:
          - containerPort: 2389
            name: peer
            protocol: TCP
          - containerPort: 2388
            name: client
            protocol: TCP
---
apiVersion: v1
kind: Secret
metadata:
  name: s3-credentials
  namespace: {namespace}
type: Opaque
stringData: 
  AccessKeyID: {aws_access_key_id}
  SecretAccessKey: {aws_secret_access_key}
---
apiVersion: risingwave.risingwavelabs.com/v1alpha1
kind: RisingWave
metadata:
  name: {name}
  namespace: {namespace}
spec:
  configuration:
    configMap:
      name: risingwave-customize-config
      key: risingwave.toml
  metaStore:
    etcd:
      endpoint: risingwave-etcd:2388
  stateStore:
    dataDirectory: {data_directory}
    s3:
      bucket: {bucket_name}
      credentials:
        secretName: s3-credentials
      region: {aws_region}
  image: ghcr.io/risingwavelabs/risingwave:{risingwave_version}
  components:
    meta:
      nodeGroups:
      - replicas: 1
        name: ""
        template:
          spec:
            resources:
              requests:
                cpu: 100m
                memory: 100Mi
    frontend:
      nodeGroups:
      - replicas: 1
        name: ""
        template:
          spec:
            resources:
              requests:
                cpu: 100m
                memory: 100Mi
    compute:
      nodeGroups:
      - replicas: 1
        name: ""
        template:
          spec:
            resources:
              requests:
                cpu: 100m
                memory: 100Mi
    compactor:
      nodeGroups:
      - replicas: 1
        name: ""
        template:
          spec:
            resources:
              requests:
                cpu: 100m
                memory: 100Mi
