import tempfile
import logging

from utils.command import run_command


logger = logging.getLogger(__name__)


def create_cluster(
    name: str, kind_config_template_path: str, **template_variables: object
):
    with open(kind_config_template_path) as file:
        content = file.read()

        with tempfile.NamedTemporaryFile() as tmp:
            cfg = content.format(**template_variables)

            logger.info("creating kind config file in {}".format(tmp.name))
            logger.info(cfg)

            tmp.write(cfg.encode())
            tmp.flush()

            run_command(
                ["kind", "create", "cluster", "--name", name, "--config", tmp.name]
            )


def delete_cluster(name: str):
    run_command(["kind", "delete", "cluster", "--name", name])
