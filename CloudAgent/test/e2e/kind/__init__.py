from shutil import which
import sys
import logging


logger = logging.getLogger("kind")


if which("kind") is None:
    logger.error(
        "kind is not found, please install kind first: https://kind.sigs.k8s.io/"
    )
    sys.exit(1)

if which("kubectl") is None:
    logger.error("kubectl is not found")
    sys.exit(1)

if which("helm") is None:
    logger.error("helm is not found")
    sys.exit(1)

if which("aws") is None:
    logger.error("aws CLI is not found")
    sys.exit(1)

if which("docker") is None:
    logger.error("docker is not found")
    sys.exit(1)
