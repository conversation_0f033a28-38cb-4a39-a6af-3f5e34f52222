import sys
import logging

from environment.clean import cleanup
from environment.base import *


logging.basicConfig(level=logging.INFO)


if __name__ == "__main__":
    if len(sys.argv) < 2:
        print("missing args: setup, clean")

    cmd = sys.argv[1]

    if cmd == "setup":
        setup()

    elif cmd == "cleanup":
        cleanup()

    elif cmd == "shutdown":
        shutdown()

    elif cmd == "step":
        locals()[sys.argv[2]]()
    
    else:
        print("command not found")
        sys.exit(1)
