import pytest
import uuid
import tempfile
import json
import time
from grpc import StatusCode, RpcError

from typing import Generator

from environment.base import (
    AWS_REGION,
    CLOUDAGENT_TESTING_NAMESPACE,
    CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY,
    AWS_S3_BUCKET_DEV,
    get_default_vpc,
    get_ec2_client,
    get_s3_client,
    get_aws_arn,
    get_testing_s3_bucket_name,
)

from conftest import endpoint
from environment.clean import (
    clean_security_group,
    clean_vpc_endpoints,
    clean_iam_role,
    clean_iam_policy,
    delete_s3_bucket,
)

from protopy.services.aws_pb2 import (
    CreateSecurityGroupRequest,
    GetSecurityGroupRequest,
    DeleteSecurityGroupRequest,
    CreateIAMPolicyRequest,
    GetIAMPolicyRequest,
    DeleteIAMPolicyRequest,
    CreateIAMRoleRequest,
    GetIAMRoleRequest,
    DeleteIAMRoleRequest,
    CreateVPCEndpointRequest,
    DeleteVPCEndpointRequest,
    GetVPCEndpointRequest,
    CreateSecurityGroupPolicyRequest,
    GetSecurityGroupPolicyRequest,
    DeleteSecurityGroupPolicyRequest,
)

from protopy.services.task_pb2 import (
    CleanupTaskRequest,
)

from protopy.services.common.data_pb2 import (
    CreateDataDirectoryDeletionTaskRequest,
    CreateSimpleDataReplicationTaskRequest,
    GetManifestRequest,
    GetManifestResponse,
)

from protopy.services.k8s_pb2 import (
    CreateNamespaceRequest,
)

from protopy.services.k8s_pb2_grpc import K8sResourceManagerStub
from protopy.services.aws_pb2_grpc import AwsResourceManagerStub
from protopy.services.task_pb2_grpc import TaskManagerStub
from protopy.services.aws_pb2 import (
    IAMS3AccessOption,
    IAMPrivateLinkAccessOption,
    IAMAccessOption,
)

from protopy.common.k8s_pb2 import ServiceAccount
from protopy.common.resource_pb2 import Meta
from protopy.common import resource_pb2, creation_pb2, deletion_pb2, task_pb2
from utils.command import run_command

from utils.resource import (
    wait,
    wait_task,
    resource_not_found_wrapper,
    resource_ready_wrapper,
    get_resource_wrapper,
    delete_resource_wrapper,
)

from utils.grpc import new_cloudagent_grpc_channel
from utils.k8s import create_namespace, delete_namespace
from utils.aws import create_bucket


def create_vpc_endpoints(tag_key: str, tag_value: str):
    c = get_ec2_client()
    res = c.create_vpc_endpoint(
        VpcEndpointType="Interface",
        VpcId=get_default_vpc(),
        ServiceName="com.amazonaws.{}.ec2".format(AWS_REGION),
        PrivateDnsEnabled=False,  # this is needed to fix "private-dns-enabled cannot be set because there is already a conflicting DNS domain"
        TagSpecifications=[
            {
                "ResourceType": "vpc-endpoint",
                "Tags": [
                    {
                        "Key": CLOUDAGENT_AWS_DEFAULT_TAG_TEST_OWNER_KEY,
                        "Value": get_aws_arn(),
                    },
                    {
                        "Key": tag_key,
                        "Value": tag_value,
                    },
                    {
                        "Key": "Name",
                        "Value": uuid.uuid4().hex,
                    },
                ],
            },
        ],
    )
    return res["VpcEndpoint"]["VpcEndpointId"]


def write_to_s3_files(bucket, items: list[tuple[str, str]]):
    c = get_s3_client()
    for item in items:
        with tempfile.NamedTemporaryFile() as tmp:
            key, content = item
            tmp.write(content.encode())
            tmp.flush()
            c.upload_file(
                tmp.name, bucket, key
            )
    c.close()


@pytest.fixture
def create_s3_and_data_for_replication_test():
    """
    return bucket name, data directory name, data objects count
    """
    bucket_name = get_testing_s3_bucket_name()
    source_dir = "replication-test-sourcedir/source"
    sink_dir = "replication-test-sinkdir/sink"
    source_data = [
        (f"{source_dir}/data/source-1.data", "source-data"),
        (f"{source_dir}/data/source-2.data", "source-data"),
        (f"{source_dir}/data/source-3.data", "source-data"),
    ]
    expected_sink_data = [
        (f"{sink_dir}/data/source-1.data", "source-data"),
        (f"{sink_dir}/data/source-2.data", "source-data"),
        (f"{sink_dir}/data/source-3.data", "source-data"),
    ]

    create_bucket(bucket_name, AWS_REGION)
    # put objects in it
    write_to_s3_files(bucket_name, source_data)

    yield bucket_name, source_dir, source_data, sink_dir, expected_sink_data

    delete_s3_bucket()


@pytest.fixture
def create_s3_bucket_and_data() -> Generator[str, str]:
    """
    return bucket name, data directory name
    """
    bucket_name = get_testing_s3_bucket_name()
    data_directory_name = "data-directory-delete-test"

    create_bucket(bucket_name, AWS_REGION)
    # put objects in it
    write_to_s3_files(bucket_name, [
        (f"{data_directory_name}/1.data", "data1"),
        (f"{data_directory_name}/2.data", "data2"),
        (f"{data_directory_name}/3.data", "data3"),
        (f"{data_directory_name}/出师表.data", "先帝创业未半而中道崩殂今天下三分益州疲弊此诚危急存亡之秋也"),
        (f"{data_directory_name}/manifest.json", '{"jimmy": "saulgoodman"}')
    ])

    yield bucket_name, data_directory_name

    delete_s3_bucket()


@pytest.fixture
def cleanup_iam_policy():
    yield
    clean_iam_policy()


@pytest.fixture
def cleanup_iam_role():
    yield
    clean_iam_role()


@pytest.fixture
def cleanup_vpc_endpoints():
    yield
    clean_vpc_endpoints()


@pytest.fixture
def provision_namespace():
    delete_namespace(CLOUDAGENT_TESTING_NAMESPACE, ignore_not_found=True)
    create_namespace(CLOUDAGENT_TESTING_NAMESPACE)


@pytest.fixture
def test_vpc_endpoint_namespace():
    ns = "vpce-test"
    delete_namespace(ns, ignore_not_found=True)
    create_namespace(ns)
    yield ns
    delete_namespace(ns, ignore_not_found=True)


@pytest.fixture
def cleanup_test_security_group():
    yield
    delete_namespace(CLOUDAGENT_TESTING_NAMESPACE, ignore_not_found=False)
    clean_security_group()


@pytest.fixture
def aws_resource_manager_stub(endpoint) -> AwsResourceManagerStub:
    return AwsResourceManagerStub(new_cloudagent_grpc_channel(endpoint))


@pytest.fixture
def task_stub(endpoint):
    return TaskManagerStub(new_cloudagent_grpc_channel(endpoint))


class TestAWS:
    def test_security_group(
        self,
        provision_namespace,
        aws_resource_manager_stub: AwsResourceManagerStub,
        cleanup_test_security_group,
    ):
        # test params
        security_group_meta_id = "test-create-sg"
        namespace = CLOUDAGENT_TESTING_NAMESPACE
        vpc_id = get_default_vpc()
        inbound = {
            "protocol": "tcp",
            "from_port": 22,
            "to_port": 22,
            "cidrs": ["0.0.0.0/0"],
            "description": "test description",
            "source_security_group_ids": [],
        }

        outbound = {
            "protocol": "udp",
            "from_port": 53,
            "to_port": 53,
            "cidrs": ["0.0.0.0/0"],
            "description": "test description",
            "source_security_group_ids": [],
        }

        # client
        aws_stub = aws_resource_manager_stub
        get_security_group = get_resource_wrapper(
            aws_stub.GetSecurityGroup,
            GetSecurityGroupRequest,
            security_group_meta_id,
            namespace,
        )

        # create security group
        def create_security_group(meta_id: str):
            return aws_stub.CreateSecurityGroup(
                CreateSecurityGroupRequest(
                    resource_meta=Meta(
                        id=meta_id,
                        namespace=CLOUDAGENT_TESTING_NAMESPACE,
                    ),
                    inbound_ip_permissions=[inbound],
                    outbound_ip_permissions=[outbound],
                )
            )

        result = create_security_group(meta_id=security_group_meta_id)
        assert result.status.code in [
            creation_pb2.StatusCode.CREATED,
            creation_pb2.StatusCode.SCHEDULED,
        ]

        # wait for resource ready
        seucirty_group_ready = resource_ready_wrapper(get_security_group)

        wait(seucirty_group_ready, 10)

        # validate security group
        def equal(aws_sg_permission, rpc_payload_permission):
            assert aws_sg_permission["IpProtocol"] == rpc_payload_permission["protocol"]
            assert aws_sg_permission["FromPort"] == rpc_payload_permission["from_port"]
            assert aws_sg_permission["ToPort"] == rpc_payload_permission["to_port"]

            assert len(aws_sg_permission["IpRanges"]) == len(
                rpc_payload_permission["cidrs"]
            )
            for i in range(len(aws_sg_permission["IpRanges"])):
                assert (
                    aws_sg_permission["IpRanges"][i]["CidrIp"]
                    in rpc_payload_permission["cidrs"]
                )
                assert (
                    aws_sg_permission["IpRanges"][i]["Description"]
                    == rpc_payload_permission["description"]
                )
            assert len(aws_sg_permission["UserIdGroupPairs"]) == len(
                rpc_payload_permission["source_security_group_ids"]
            )
            for i in range(len(aws_sg_permission["UserIdGroupPairs"])):
                assert (
                    aws_sg_permission["UserIdGroupPairs"][i]["GroupId"]
                    in rpc_payload_permission["source_security_group_ids"]
                )
                assert (
                    aws_sg_permission["UserIdGroupPairs"][i]["Description"]
                    == rpc_payload_permission["description"]
                )

        sg_id = get_security_group().security_group_id
        c = get_ec2_client()
        res = c.describe_security_groups(GroupIds=[sg_id])
        # - check response
        assert len(res["SecurityGroups"]) == 1
        assert len(res["SecurityGroups"][0]["IpPermissions"]) == 1
        # - check inbound
        equal(res["SecurityGroups"][0]["IpPermissions"][0], inbound)
        # - check outbound
        equal(res["SecurityGroups"][0]["IpPermissionsEgress"][0], outbound)

        # create security group again
        result = create_security_group(meta_id=security_group_meta_id)
        assert result.status.code == creation_pb2.StatusCode.ALREADY_EXISTS

        # create the another security group using sg id in IP permissions
        new_sg_meta_id = "test-user-groups-sg"
        inbound["cidrs"] = []
        inbound["source_security_group_ids"] = [sg_id]
        outbound["cidrs"] = []
        outbound["source_security_group_ids"] = [sg_id]
        aws_stub.CreateSecurityGroup(
            CreateSecurityGroupRequest(
                resource_meta=Meta(
                    id=new_sg_meta_id,
                    namespace=CLOUDAGENT_TESTING_NAMESPACE,
                ),
                inbound_ip_permissions=[inbound],
                outbound_ip_permissions=[outbound],
            )
        )
        get_security_group_2 = get_resource_wrapper(
            aws_stub.GetSecurityGroup,
            GetSecurityGroupRequest,
            new_sg_meta_id,
            namespace,
        )
        wait(resource_ready_wrapper(get_security_group_2), 10)
        new_sg_id = get_security_group_2().security_group_id
        # - validate the new security group's IP permission
        res = c.describe_security_groups(GroupIds=[new_sg_id])
        assert len(res["SecurityGroups"]) == 1
        assert len(res["SecurityGroups"][0]["IpPermissions"]) == 1
        equal(res["SecurityGroups"][0]["IpPermissions"][0], inbound)
        equal(res["SecurityGroups"][0]["IpPermissionsEgress"][0], outbound)
        c.close()
        # - delete the new security group
        res = delete_resource_wrapper(
            aws_stub.DeleteSecurityGroup,
            DeleteSecurityGroupRequest,
            new_sg_meta_id,
            namespace,
        )()
        assert res.status.code in [
            deletion_pb2.StatusCode.DELETED,
            deletion_pb2.SCHEDULED,
        ]

        # delete security group
        delete_security_group = delete_resource_wrapper(
            aws_stub.DeleteSecurityGroup,
            DeleteSecurityGroupRequest,
            security_group_meta_id,
            namespace,
        )
        result = delete_security_group()
        assert result.status.code in [
            deletion_pb2.StatusCode.DELETED,
            deletion_pb2.SCHEDULED,
        ]

        # wait for resource deleted
        seucirty_group_not_found = resource_not_found_wrapper(get_security_group)
        wait(seucirty_group_not_found, 10)

        # delete security group again
        result = delete_security_group()
        assert result.status.code == deletion_pb2.NOT_FOUND

    def test_vpc_endpoints(
        self,
        aws_resource_manager_stub: AwsResourceManagerStub,
        test_vpc_endpoint_namespace,
    ):
        aws_stub = aws_resource_manager_stub

        resource_id = "vpce-test"
        namespace = test_vpc_endpoint_namespace

        vpc_endpoint_resource_meta = Meta(id=resource_id, namespace=namespace)

        c = get_ec2_client()
        subnets = c.describe_subnets()
        subnet_ids = [s["SubnetId"] for s in subnets["Subnets"]]

        create_req = CreateVPCEndpointRequest(
            resource_meta=vpc_endpoint_resource_meta,
            service_name=f"com.amazonaws.{AWS_REGION}.ec2",
            security_group_ids=[],
            subnet_ids=subnet_ids,
            # This is needed for testing since we're using a public endpoint service for this test.
            # Otherwise, the following error will be raised:
            # > private-dns-enabled cannot be set because there
            # > is already a conflicting DNS domain for ec2.us-west-2.amazonaws.com in the
            private_dns_enabled=False, # This is needed for testing
        )

        delete_req = DeleteVPCEndpointRequest(resource_meta=vpc_endpoint_resource_meta)

        vpc_endpoint_getter = get_resource_wrapper(
            aws_stub.GetVPCEndpoint,
            GetVPCEndpointRequest,
            resource_id,
            namespace,
        )

        res = aws_stub.CreateVPCEndpoint(create_req)
        vpc_endpoint_ready = resource_ready_wrapper(vpc_endpoint_getter)
        wait(vpc_endpoint_ready, 60)

        res = aws_stub.CreateVPCEndpoint(create_req)
        assert res.status.code == creation_pb2.ALREADY_EXISTS

        res = aws_stub.DeleteVPCEndpoint(delete_req)
        vpc_endpoint_not_found = resource_not_found_wrapper(vpc_endpoint_getter)
        wait(vpc_endpoint_not_found, 60)

        res = aws_stub.DeleteVPCEndpoint(delete_req)
        assert res.status.code == deletion_pb2.NOT_FOUND

    def test_iam(
        self,
        provision_namespace,
        aws_resource_manager_stub,
        cleanup_iam_role,
        cleanup_iam_policy,
    ):
        aws_stub = aws_resource_manager_stub

        def create_iam_policy(meta_id: str):
            return aws_stub.CreateIAMPolicy(
                CreateIAMPolicyRequest(
                    resource_meta=Meta(
                        id=meta_id,
                        namespace=CLOUDAGENT_TESTING_NAMESPACE,
                    ),
                    access_options=[
                        IAMAccessOption(
                            s3_access_option=IAMS3AccessOption(
                                bucket=AWS_S3_BUCKET_DEV, dir="test-dir"
                            )
                        )
                    ],
                )
            )

        def create_iam_role(meta_id: str, policy_id: str):
            return aws_stub.CreateIAMRole(
                CreateIAMRoleRequest(
                    resource_meta=Meta(
                        id=meta_id,
                        namespace=CLOUDAGENT_TESTING_NAMESPACE,
                    ),
                    policy_refs=[
                        Meta(
                            id=policy_id,
                            namespace=CLOUDAGENT_TESTING_NAMESPACE,
                        )
                    ],
                    service_account=ServiceAccount(
                        name="test-sa", namespace=CLOUDAGENT_TESTING_NAMESPACE
                    ),
                )
            )

        policy_meta_id = "test-policy"
        role_meta_id = "test-role"
        get_iam_policy = get_resource_wrapper(
            aws_stub.GetIAMPolicy,
            GetIAMPolicyRequest,
            policy_meta_id,
            CLOUDAGENT_TESTING_NAMESPACE,
        )
        get_iam_role = get_resource_wrapper(
            aws_stub.GetIAMRole,
            GetIAMRoleRequest,
            role_meta_id,
            CLOUDAGENT_TESTING_NAMESPACE,
        )

        # create policy
        result = create_iam_policy(policy_meta_id)
        assert creation_pb2.StatusCode.SCHEDULED == result.status.code
        result = create_iam_policy(policy_meta_id)
        assert creation_pb2.StatusCode.ALREADY_EXISTS == result.status.code

        # create role
        result = create_iam_role(role_meta_id, policy_meta_id)
        assert creation_pb2.StatusCode.SCHEDULED == result.status.code
        result = create_iam_role(role_meta_id, policy_meta_id)
        assert creation_pb2.StatusCode.ALREADY_EXISTS == result.status.code

        # check policy readiness
        iam_policy_ready = resource_ready_wrapper(get_iam_policy)
        wait(iam_policy_ready, 30)
        # check role readiness
        iam_role_ready = resource_ready_wrapper(get_iam_role)
        wait(iam_role_ready, 30)

        # delete role
        delete_iam_role = delete_resource_wrapper(
            aws_stub.DeleteIAMRole,
            DeleteIAMRoleRequest,
            role_meta_id,
            CLOUDAGENT_TESTING_NAMESPACE,
        )
        result = delete_iam_role()
        assert deletion_pb2.SCHEDULED == result.status.code
        iam_role_not_found = resource_not_found_wrapper(get_iam_role)
        wait(iam_role_not_found, 10)

        # delete policy
        delete_iam_policy = delete_resource_wrapper(
            aws_stub.DeleteIAMPolicy,
            DeleteIAMPolicyRequest,
            policy_meta_id,
            CLOUDAGENT_TESTING_NAMESPACE,
        )
        result = delete_iam_policy()
        assert deletion_pb2.SCHEDULED == result.status.code
        iam_policy_not_found = resource_not_found_wrapper(get_iam_policy)
        wait(iam_policy_not_found, 10)

    def test_delete_data_directory(
        self,
        aws_resource_manager_stub,
        task_stub,
        create_s3_bucket_and_data,
    ):
        aws_stub = aws_resource_manager_stub
        c = get_s3_client()

        # parameter for testing
        task_id = "task-id"
        task_namespace = "cloudagent"

        # prepare data directory and data
        bucket, directory = create_s3_bucket_and_data

        # validate data directory is not empty
        res = c.list_objects_v2(Bucket=bucket, Prefix="{}/".format(directory))
        assert res["KeyCount"] != 0

        # start to empty data directory
        result = aws_stub.CreateDataDirectoryDeletionTask(
            CreateDataDirectoryDeletionTaskRequest(
                resource_meta=Meta(id=task_id),
                bucket_name=bucket,
                directory_name=directory,
            )
        )

        assert result.status.code == creation_pb2.CREATED

        wait_task(
            task_stub,
            Meta(id=task_id, namespace=task_namespace),
            task_pb2.SUCCESS,
            timeout_secs=60,
        )

        # clean up task resources
        result = task_stub.CleanupTask(
            CleanupTaskRequest(resource_meta=Meta(id=task_id))
        )
        assert result.status.code in [deletion_pb2.DELETED, deletion_pb2.SCHEDULED]
        wait_task(task_stub, Meta(id=task_id), task_pb2.NOT_FOUND)

        # validate result
        res = c.list_objects_v2(Bucket=bucket, Prefix="{}/".format(directory))
        assert res["KeyCount"] == 0
        c.close()

    def test_security_gourp_policy(
        self, aws_resource_manager_stub, provision_namespace
    ):
        aws_stub = aws_resource_manager_stub

        resource_meta = Meta(id="test-id", namespace=CLOUDAGENT_TESTING_NAMESPACE)
        security_group_ids = ["sg-1", "sg-2"]
        pod_labels_selector = {
            "k1": "v1",
            "k2,": "v2",
        }

        createReq = CreateSecurityGroupPolicyRequest(
            resource_meta=resource_meta,
            security_group_ids=security_group_ids,
            pod_labels_selector=pod_labels_selector,
        )

        res = aws_stub.CreateSecurityGroupPolicy(createReq)
        assert res.status.code == creation_pb2.CREATED

        res = aws_stub.CreateSecurityGroupPolicy(createReq)
        assert res.status.code == creation_pb2.ALREADY_EXISTS

        res = aws_stub.GetSecurityGroupPolicy(
            GetSecurityGroupPolicyRequest(resource_meta=resource_meta)
        )
        assert res.status.code == resource_pb2.READY

        res = aws_stub.DeleteSecurityGroupPolicy(
            DeleteSecurityGroupPolicyRequest(resource_meta=resource_meta)
        )
        assert res.status.code == deletion_pb2.SCHEDULED

        sgp_not_found = resource_not_found_wrapper(
            get_resource_wrapper(
                aws_stub.GetSecurityGroupPolicy,
                GetSecurityGroupPolicyRequest,
                resource_meta.id,
                resource_meta.namespace,
            )
        )
        wait(sgp_not_found, 10)


    def test_simple_data_replication(
        self,
        aws_resource_manager_stub,
        create_s3_and_data_for_replication_test,
        task_stub,
    ): 
        bucket_name, source_dir, source_data, sink_dir, expected_sink_data = create_s3_and_data_for_replication_test
        stub = aws_resource_manager_stub
        # parameter for testing
        task_id = "task-id"
        task_namespace = "cloudagent"

        result = stub.CreateSimpleDataReplicationTask(
            CreateSimpleDataReplicationTaskRequest(
                resource_meta=Meta(id=task_id, namespace=task_namespace),
                source_bucket=bucket_name,
                source_directory=source_dir,
                sink_bucket=bucket_name,
                sink_directory=sink_dir,
            )
        )
        assert result.status.code == creation_pb2.CREATED

        wait_task(
            task_stub, Meta(id=task_id, namespace=task_namespace), task_pb2.SUCCESS, timeout_secs=60
        )

        # clean up task resources
        result = task_stub.CleanupTask(
            CleanupTaskRequest(resource_meta=Meta(id=task_id))
        )
        assert result.status.code in [deletion_pb2.DELETED, deletion_pb2.SCHEDULED]
        wait_task(task_stub, Meta(id=task_id), task_pb2.NOT_FOUND)
        
        c = get_s3_client()
        response = c.list_objects_v2(
            Bucket=bucket_name,
            Prefix=sink_dir,
        )
        # check if data is replicated
        print(response)
        assert response["KeyCount"] == len(expected_sink_data)
        expected_keys = [item[0] for item in expected_sink_data]
        for item in response["Contents"]:
            assert item["Key"] in expected_keys
        for item in expected_sink_data:
            key, content = item
            response = c.get_object(
                Bucket=bucket_name,
                Key=key
            )
            assert response["Body"].read().decode() == content
        c.close()

    def test_get_manifest(
        self,
        aws_resource_manager_stub,
        create_s3_bucket_and_data,
    ):
        bucket, directory = create_s3_bucket_and_data
        stub = aws_resource_manager_stub
        
        start = time.time()
        res = stub.GetManifest(GetManifestRequest(bucket=bucket, backup_dir=directory))
        end = time.time()
        t1 = end - start

        manifest = json.loads(res.manifest_json)
        assert manifest["jimmy"] == "saulgoodman"

        # the cache should return the correct result
        start = time.time()
        res = stub.GetManifest(GetManifestRequest(bucket=bucket, backup_dir=directory))
        end = time.time()
        t2 = end - start
        manifest = json.loads(res.manifest_json)
        assert manifest["jimmy"] == "saulgoodman"

        # assert the cache is used, so it should be significantly faster with cache
        assert t2 < t1 

        # try get manifest for non-existent backup dir
        with pytest.raises(RpcError) as exc_info:
            stub.GetManifest(GetManifestRequest(bucket=bucket, backup_dir="non-existent-dir"))
        assert "not found" in str(exc_info.value).lower()
        # Check gRPC status code
        assert exc_info.value.code() == StatusCode.NOT_FOUND
