import tempfile
import pytest
import json
import time

from environment.base import (
    get_testing_s3_bucket_name,
    KIND_KUBECTL_CONTEXT,
    get_aws_credentials,
    TEMPLATE_PATH,
    RISINGWAVE_VERSION,
    AWS_REGION,
    DATAGEN_IMAGE,
    CLOUDAGENT_NAMESPACE,
)

from environment.clean import (
    delete_s3_bucket,
    delete_tasks,
)

from conftest import endpoint

from protopy.services.rwc_pb2 import (
    ValidateSourceRequest,
    MetaNodeBackupRequest,
    DeleteSnapshotRequest,
    RestoreMetaRequest,
    VacuumEtcdMetaRequest,
)

from protopy.services.task_pb2 import CleanupTaskRequest, GetTaskStatusRequest
from protopy.services.rwc_pb2_grpc import RisingwaveControlStub
from protopy.services.task_pb2_grpc import TaskManagerStub
from utils.command import run_command, get_command_output, get_command_output_raw
from utils.grpc import new_cloudagent_grpc_channel
from utils.files import read_file
from utils.resource import wait, wait_task
from protopy.common import creation_pb2, task_pb2, deletion_pb2
from protopy.common.resource_pb2 import Meta
from utils.k8s import delete_namespace, create_namespace
from utils.aws import create_bucket


def wait_for_statefulset(namespace, name, replicas=1):
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            f"--for=jsonpath={{.status.readyReplicas}}={replicas}",
            "--timeout=300s",
            "statefulset",
            name,
            "-n",
            namespace,
        ]
    )


def restart_statefulset(namespace, name):
    prev = get_command_output(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "get",
            "statefulset",
            name,
            "-n",
            namespace,
            "-o",
            "json",
        ]
    )
    prev_observed_generation = int(json.loads(prev)["status"]["observedGeneration"])
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "rollout",
            "restart",
            "statefulset",
            name,
            "-n",
            namespace,
        ]
    )
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            f"--for=jsonpath={{.status.observedGeneration}}={prev_observed_generation + 1}",
            "--timeout=300s",
            "statefulset",
            name,
            "-n",
            namespace,
        ]
    )
    wait_for_statefulset(namespace, name, replicas=1)


def exec_pod(namespace, name, commands):
    return get_command_output_raw(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "exec",
            "-n",
            namespace,
            name,
            "--",
        ]
        + commands
    )


def stop_risingwave(namespace, name):
    prev = get_command_output(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "get",
            "risingwave",
            name,
            "-n",
            namespace,
            "-o",
            "json",
        ]
    )
    prev_observed_generation = int(json.loads(prev)["metadata"]["generation"])

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "patch",
            "risingwave",
            name,
            "-n",
            namespace,
            "--type=json",
            '-p=[{"op":"replace", "path":"/spec/components/meta/nodeGroups/0/replicas", "value" : 0}, {"op":"replace","path":"/spec/components/frontend/nodeGroups/0/replicas" ,"value" : 0},{"op" : "replace" ,"path" : "/spec/components/compute/nodeGroups/0/replicas" ,"value" : 0},{"op" : "replace" ,"path" : "/spec/components/compactor/nodeGroups/0/replicas" ,"value" : 0}]',
        ]
    )
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            f"--for=jsonpath={{.metadata.generation}}={prev_observed_generation + 1}",
            "--timeout=300s",
            "risingwave",
            name,
            "-n",
            namespace,
        ]
    )
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            "--for",
            "condition=Upgrading=false",
            "--timeout=300s",
            "risingwave",
            name,
            "-n",
            namespace,
        ]
    )


def start_risingwave(namespace, name):
    prev = get_command_output(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "get",
            "risingwave",
            name,
            "-n",
            namespace,
            "-o",
            "json",
        ]
    )
    prev_observed_generation = int(json.loads(prev)["metadata"]["generation"])

    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "patch",
            "risingwave",
            name,
            "-n",
            namespace,
            "--type=json",
            '-p=[{"op" : "replace" ,"path" : "/spec/components/meta/nodeGroups/0/replicas" ,"value" : 1},{"op" : "replace" ,"path" : "/spec/components/frontend/nodeGroups/0/replicas" ,"value" : 1},{"op" : "replace" ,"path" : "/spec/components/compute/nodeGroups/0/replicas" ,"value" : 1},{"op" : "replace" ,"path" : "/spec/components/compactor/nodeGroups/0/replicas" ,"value" : 1}]',
        ]
    )
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            f"--for=jsonpath={{.metadata.generation}}={prev_observed_generation + 1}",
            "--timeout=300s",
            "risingwave",
            name,
            "-n",
            namespace,
        ]
    )
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            "--for",
            "condition=Upgrading=false",
            "--timeout=300s",
            "risingwave",
            name,
            "-n",
            namespace,
        ]
    )
    wait_risingwave_ready(namespace, name)


@pytest.fixture
def clean_task():
    yield
    delete_tasks()


@pytest.fixture
def rwc_stub(endpoint):
    return RisingwaveControlStub(new_cloudagent_grpc_channel(endpoint))


@pytest.fixture
def task_stub(endpoint):
    return TaskManagerStub(new_cloudagent_grpc_channel(endpoint))


@pytest.fixture
def create_s3_bucket() -> str:
    bucket_name = get_testing_s3_bucket_name()

    create_bucket(bucket_name, AWS_REGION)

    yield bucket_name

    delete_s3_bucket()


def kubectl_apply_template(filename: str, **kwargs):
    template = read_file("{}/{}".format(TEMPLATE_PATH, filename)).decode()
    with tempfile.NamedTemporaryFile() as tmp:
        tmp.write(template.format(**kwargs).encode())
        tmp.flush()
        run_command(
            [
                "kubectl",
                "--context",
                KIND_KUBECTL_CONTEXT,
                "apply",
                "-f",
                tmp.name,
            ]
        )


@pytest.fixture
def create_etcd():
    name = "risingwave-etcd"
    namespace = "etcd-test"

    delete_namespace(namespace, ignore_not_found=True)
    create_namespace(namespace)

    kubectl_apply_template(
        "etcd.yaml",
        name=name,
        namespace=namespace,
    )

    yield name, namespace

    delete_namespace(namespace, ignore_not_found=False)


@pytest.fixture
def create_risingwave_etcd_s3_backup(create_s3_bucket) -> str:
    bucket_name = create_s3_bucket
    access_key_id, secret_access_key = get_aws_credentials()
    name = "rw"
    namespace = "t-backup"
    backup_dir = "backup"
    data_dir = "data"
    version = RISINGWAVE_VERSION
    etcd_name = "risingwave-etcd"

    delete_namespace(namespace, ignore_not_found=True)

    create_namespace(namespace)

    kubectl_apply_template(
        "risingwave-etcd-s3-backup.yaml",
        bucket_name=bucket_name,
        backup_directory=backup_dir,
        data_directory=data_dir,
        aws_access_key_id=access_key_id,
        aws_secret_access_key=secret_access_key,
        aws_region=AWS_REGION,
        name=name,
        namespace=namespace,
        risingwave_version=version,
        etcd_name=etcd_name,
    )

    info = {
        "name": name,
        "namespace": namespace,
        "bucket_name": bucket_name,
        "backup_dir": backup_dir,
        "data_dir": data_dir,
        "version": version,
        # TODO: make the endpoint the template configurable.
        "etcd_endpoints": "risingwave-etcd:2388",
        "etcd_name": etcd_name,
    }

    yield info
    delete_namespace(namespace, ignore_not_found=False)


@pytest.fixture
def create_risingwave_with_kafka():
    name = "rw"
    namespace = "t-mem"

    delete_namespace(namespace, ignore_not_found=True)

    create_namespace(namespace)

    kubectl_apply_template(
        "risingwave-kafka-source.yaml",
        name=name,
        namespace=namespace,
        risingwave_version=RISINGWAVE_VERSION,
        datagen_image=DATAGEN_IMAGE,
    )

    yield name, namespace, "demosrc.{}.svc.cluster.local:9092".format(namespace)


def wait_risingwave_ready(namespace: str, name: str):
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "wait",
            "--for",
            "condition=Running",
            "--timeout=300s",
            "risingwave",
            name,
            "-n",
            namespace,
        ]
    )

    def try_psql():
        try:
            # verify the full functionality of the RisingWave cluster
            psql_exec(namespace, name, "CREATE TABLE IF NOT EXISTS test_psql(c int);")
            psql_exec(namespace, name, "INSERT INTO test_psql VALUES (1);")
            psql_exec(namespace, name, "FLUSH;")
            return True
        except:
            return False

    wait(
        try_psql,
        120,
    )


# TODO: use python postgres client to connect to risingwave.
def psql_exec(namespace: str, name: str, statement: str):
    """
    `psql_exec` uses psql client to run a statement in a risingwave instance

    :param namespace: namespace of the risingwave crd object.
    :param name: name of the risingwave crd object.

    """
    return get_command_output(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "exec",
            "psql",
            "--",
            "psql",
            "-h",
            "{name}-frontend.{namespace}.svc.cluster.local".format(
                name=name, namespace=namespace
            ),
            "-p",
            "4567",
            "-U",
            "root",
            "-d",
            "dev",
            "-t",
            "-c",
            statement,
        ]
    )


class TestRisingwaveControl:

    def test_meta_node_backup_and_restore(
        self, create_risingwave_etcd_s3_backup, rwc_stub, task_stub, clean_task
    ):
        # 1. start a RisingWave cluster with etcd and s3
        # 2. insert data A
        # 3. run backup task and wait for it
        # 4. insert data B
        # 5. stop the RisingWave cluster and re-create ETCD cluster
        # 6. run restore meta task and wait for it
        # 7. start the RisingWave cluster
        # 8. check if data A exists and data B does not exist

        info = create_risingwave_etcd_s3_backup
        rw_name = info["name"]
        rw_namespace = info["namespace"]
        backup_url = "s3://{}".format(info["bucket_name"])
        hummock_url = "s3://{}".format(info["bucket_name"])
        backup_dir = info["backup_dir"]
        hummock_dir = info["data_dir"]
        version = info["version"]
        etcd_endpoints = info["etcd_endpoints"]
        etcd_name = info["etcd_name"]

        backup_task_meta = Meta(id=rw_name + "-backup", namespace=CLOUDAGENT_NAMESPACE)
        restore_task_meta = Meta(id=rw_name + "-restore", namespace=rw_namespace)

        wait_risingwave_ready(rw_namespace, rw_name)

        # 1. write before backup
        psql_exec(rw_namespace, rw_name, "CREATE TABLE test (id INT);")
        psql_exec(rw_namespace, rw_name, "INSERT INTO test VALUES (1);")
        psql_exec(rw_namespace, rw_name, "FLUSH;")

        # start meta node backup
        result = rwc_stub.MetaNodeBackup(
            MetaNodeBackupRequest(
                resource_meta=backup_task_meta,
                rw_name=rw_name,
                rw_namespace=rw_namespace,
            )
        )
        assert result.status.code == creation_pb2.CREATED

        # wait for meta node backup task finishes
        wait_task(
            task_stub,
            backup_task_meta,
            task_pb2.SUCCESS,
            timeout_secs=60,
        )

        # write after backup
        psql_exec(rw_namespace, rw_name, "INSERT INTO test VALUES (2);")
        psql_exec(rw_namespace, rw_name, "FLUSH;")

        # the message should contain the snapshot ID
        res = task_stub.GetTaskStatus(
            GetTaskStatusRequest(resource_meta=backup_task_meta)
        )
        assert len(res.status.message) != 0
        print(res.status.message)
        assert "backup job succeeded" in res.status.message

        # check if the meta node backup is finished
        result = psql_exec(
            rw_namespace, rw_name, "SELECT COUNT(*) FROM rw_catalog.rw_meta_snapshot;"
        )
        assert result == "1"

        # cleanup meta node backup task
        result = task_stub.CleanupTask(
            CleanupTaskRequest(resource_meta=backup_task_meta)
        )

        assert result.status.code in [deletion_pb2.DELETED, deletion_pb2.SCHEDULED]

        # wait until the node backup task is deleted
        wait_task(task_stub, backup_task_meta, task_pb2.NOT_FOUND)

        # cleanup meta node backup task AGAIN
        result = task_stub.CleanupTask(
            CleanupTaskRequest(resource_meta=backup_task_meta)
        )

        assert result.status.code == deletion_pb2.NOT_FOUND
        
        # get snapshot ID
        snapshot_id = psql_exec(
            rw_namespace,
            rw_name,
            "SELECT meta_snapshot_id FROM rw_catalog.rw_meta_snapshot;",
        )

        print(snapshot_id, "snapshot_id")

        aws_access_key_id, aws_secret_access_key = get_aws_credentials()

        # stop risingwave
        stop_risingwave(rw_namespace, rw_name)

        # restart in-memory etcd
        restart_statefulset(rw_namespace, etcd_name)
        time.sleep(5)

        # restore meta store
        result = rwc_stub.RestoreMeta(
            RestoreMetaRequest(
                resource_meta=restore_task_meta,
                service_account="",
                rw_image_tag=RISINGWAVE_VERSION,
                meta_snapshot_id=int(snapshot_id),
                meta_store_type="etcd",
                backup_storage_url=backup_url,
                backup_storage_dir=backup_dir,
                hummock_storage_url=hummock_url,
                hummock_storage_dir=hummock_dir,
                etcd_endpoints=etcd_endpoints,
                etcd_auth=False,
                etcd_username="",
                etcd_password="",
                envs={
                    "AWS_ACCESS_KEY_ID": aws_access_key_id,
                    "AWS_SECRET_ACCESS_KEY": aws_secret_access_key,
                    "AWS_REGION": AWS_REGION,
                },
            )
        )
        assert result.status.code == creation_pb2.CREATED

        # wait for restore meta task finishes
        wait_task(
            task_stub,
            restore_task_meta,
            task_pb2.SUCCESS,
            timeout_secs=60,
        )

        # start risingwave
        start_risingwave(rw_namespace, rw_name)

        # check if the data is correct
        result = psql_exec(rw_namespace, rw_name, "SELECT COUNT(*) FROM test;")
        assert result == "1"

        # delete snapshot
        delete_snapshot_req = DeleteSnapshotRequest(
            rw_version=RISINGWAVE_VERSION,
            rw_name=rw_name,
            rw_namespace=rw_namespace,
            snapshot_id=int(snapshot_id),
        )
        rwc_stub.DeleteSnapshot(delete_snapshot_req)

        # check if the snapshot is deleted
        result = psql_exec(
            rw_namespace, rw_name, "SELECT COUNT(*) FROM rw_catalog.rw_meta_snapshot;"
        )
        assert result == "0"

        # delete snapshot AGAIN (clean ids which do not exist)
        rwc_stub.DeleteSnapshot(delete_snapshot_req)

    def test_validate_source(self, create_risingwave_with_kafka, rwc_stub):
        rw_name, rw_namespace, brokder_addr = create_risingwave_with_kafka

        task_id = rw_name + "-validate-source"
        task_namespace = rw_namespace

        wait_risingwave_ready(rw_namespace, rw_name)

        # start validate source
        result = rwc_stub.ValidateSource(
            ValidateSourceRequest(
                rw_name=rw_name,
                rw_namespace=rw_namespace,
                props=json.dumps(
                    {
                        "connector": "kafka",
                        "topic": "twitter",
                        "properties.bootstrap.server": brokder_addr,
                        "scan.startup.mode": "earliest",
                        "properties.group.id": "test_group",
                    }
                ),
            )
        )

        assert result.error_message == ""

    def test_vacuum_etcd_meta(self, rwc_stub, create_etcd):
        name, namespace = create_etcd

        wait_for_statefulset(namespace, name, replicas=1)

        etcd_command = [
            "env",
            "ETCDCTL_API=3",
            "etcdctl",
            "--user=root",
            "--password=rootPassword",
        ]

        out = (
            exec_pod(namespace, f"{name}-0", etcd_command + ["put", "k1", "v1"])
            .decode()
            .strip()
        )
        assert out == "OK"

        out = (
            exec_pod(namespace, f"{name}-0", etcd_command + ["put", "k2", "v2"])
            .decode()
            .strip()
        )
        assert out == "OK"

        rwc_stub.VacuumEtcdMeta(
            VacuumEtcdMetaRequest(
                pod_name=f"{name}-0",
                pod_namespace=namespace,
                etcd_username="root",
                etcd_password="rootPassword",
            )
        )

        out = (
            exec_pod(
                namespace, f"{name}-0", etcd_command + ["get", "--prefix=true", ""]
            )
            .decode()
            .strip()
        )
        print(out)
