import pytest
import time
from kubernetes import client, config
import json

from environment.base import (
    E2E_TEST_FOLDER_PATH,
    CLOUDAGENT_NODE_PORT,
    KIND_KUBECTL_CONTEXT,
    CLOUDAGENT_CLIENT_TLS_CERT_PATH,
    CLOUDAGENT_CLIENT_TLS_PRIVATE_KEY_PATH,
    CLOUDAGENT_CLIENT_TLS_ROOT_CERT_PATH,
    CLOUDAGENT_TESTING_NAMESPACE,
    CLOUDAGENT_NAMESPACE,
)


from protopy.services.k8s_pb2 import (
    AnnotateServiceAccountRequest,
    CreateConfigMapRequest,
    CreateNamespaceRequest,
    ScaleRisingWaveRequest,
    DeleteRisingWaveRequest,
    CreateRisingWaveRequest,
    CreateSecretRequest,
    CreateServiceAccountRequest,
    DeleteConfigMapRequest,
    DeleteNamespaceRequest,
    DeleteSecretRequest,
    DeleteServiceAccountRequest,
    GetConfigMapRequest,
    GetHelmReleaseRequest,
    GetNamespaceRequest,
    GetSecretRequest,
    GetServiceAccountRequest,
    InstallHelmReleaseRequest,
    UninstallHelmReleaseRequest,
    UpdateConfigMapRequest,
    UpgradeHelmReleaseRequest,
    CreateServiceMonitorRequest,
    CreateServiceMonitorResponse,
    DeleteServiceMonitorRequest,
    DeleteServiceMonitorResponse,
    CreatePodMonitoringRequest,
    CreatePodMonitoringResponse,
    DeletePodMonitoringRequest,
    DeletePodMonitoringResponse,
)

from protopy.services.k8s_pb2_grpc import K8sResourceManagerStub
from protopy.services.task_pb2_grpc import TaskManagerStub
from protopy.common.resource_pb2 import Meta
from protopy.common.risingwave_pb2 import RisingWaveSpec, ScaleSpec, StandaloneSpec, StateStoreSpec, StateStoreBackendMemory, MetaStoreSpec, MetaStoreBackendEtcd, Config, NodeConfig, NodeConfigurationConfigMap, ComponentsSpec, ComponentSpec, NodeGroupSpec, NodeGroupUpgradeStrategy,  UpgradeStrategyType, NodePodSpec, NodePodContainerSpec
from protopy.common.k8s_pb2 import SERVICE_TYPE_NODE_PORT, ResourceRequirements
from protopy.common.k8s_pb2 import ConfigMap, Secret, HelmReleaseStatus
from protopy.common import resource_pb2, creation_pb2, deletion_pb2, task_pb2
from utils.grpc import new_cloudagent_grpc_channel
from utils.resource import (
    wait,
    wait_task,
    resource_not_found_wrapper,
    resource_ready_wrapper,
    get_resource_wrapper,
    delete_resource_wrapper,
)
from utils.command import get_command_output
from utils.k8s import delete_namespace, create_namespace

from protopy.common.prometheus_pb2 import ServiceMonitorSpec, Endpoint
from protopy.common.gmp_pb2 import PodMonitoringSpec


@pytest.fixture
def namespace_for_helm_test():
    ns = "helm-test"
    delete_namespace(ns, ignore_not_found=True)
    create_namespace(ns)
    yield ns
    delete_namespace(ns, ignore_not_found=True)


@pytest.fixture
def namespace_for_service_monitor_management():
    svc_monitor_test_namespace = "svc-monitor-test"
    delete_namespace(svc_monitor_test_namespace, ignore_not_found=True)
    create_namespace(svc_monitor_test_namespace)
    yield svc_monitor_test_namespace
    delete_namespace(svc_monitor_test_namespace, ignore_not_found=True)



@pytest.fixture
def k8s_resource_manager_stub(endpoint):
    return K8sResourceManagerStub(new_cloudagent_grpc_channel(endpoint))


@pytest.fixture
def task_stub(endpoint):
    return TaskManagerStub(new_cloudagent_grpc_channel(endpoint))


@pytest.fixture
def setup_teardown(k8s_resource_manager_stub: K8sResourceManagerStub):
    """ Create and delete test ns before and after tests """
    print("creating test namespace if not exists")
    k8s_stub = k8s_resource_manager_stub
    result = k8s_stub.CreateNamespace(
        CreateNamespaceRequest(resource_meta=get_rw_meta())
    )
    assert result.status.code in [creation_pb2.StatusCode.CREATED, creation_pb2.StatusCode.ALREADY_EXISTS]
    # Create ConfigMap
    result = k8s_stub.CreateConfigMap(
        CreateConfigMapRequest(
            resource_meta=Meta(
                id="test-cm", namespace=CLOUDAGENT_TESTING_NAMESPACE
            ),
            config_map_spec=ConfigMap(
                data={"key": "val"},
            ),
        )
    )
    assert creation_pb2.StatusCode.CREATED == result.status.code
    
    yield
    
    # cleanup
    print("removing test risingwave instance")
    k8s_stub.DeleteRisingWave(DeleteRisingWaveRequest(
        resource_meta=get_rw_meta()
    ))
    
    print("removing test namespace")
    get_namespace = get_resource_wrapper(
        k8s_stub.GetNamespace, GetNamespaceRequest, CLOUDAGENT_TESTING_NAMESPACE, CLOUDAGENT_TESTING_NAMESPACE
    )
    result = k8s_stub.DeleteNamespace(
        DeleteNamespaceRequest(resource_meta=get_rw_meta())
    )
    namespace_not_found = resource_not_found_wrapper(get_namespace)
    wait(namespace_not_found, 4 * 60)


def get_rw_meta() -> Meta: 
    return Meta(id=CLOUDAGENT_TESTING_NAMESPACE, namespace=CLOUDAGENT_TESTING_NAMESPACE) 


def get_standalone_spec(labels: dict) -> StandaloneSpec:
    return StandaloneSpec(
        log_level="info",
        replicas=1,
        upgrade_strategy=NodeGroupUpgradeStrategy(type=UpgradeStrategyType.ROLLING_UPDATE), 
        node_pod_spec=NodePodSpec(
            tolerations=[],
            affinity={},
            container_spec=NodePodContainerSpec(
                resources=ResourceRequirements(
                    cpu_request="250m", 
                    cpu_limit="500m",
                    memory_request="64Mi",
                    memory_limit="128Mi"
                ),
                envs={"envName":"envVal"},
                volume_mounts=[]
            ),
            labels=labels,
            annotations={"annotationKey": "annotationVal"},
            volumes=[]
        )
    )


def get_rw_spec(is_standalone: bool, labels: dict) -> RisingWaveSpec:
    component_spec=ComponentSpec(
        node_groups=[NodeGroupSpec(
            name="default",
            replicas=1, 
            upgrade_strategy=NodeGroupUpgradeStrategy(
                type=UpgradeStrategyType.ROLLING_UPDATE
            ), 
            node_pod_spec=NodePodSpec(
                labels=labels,
                tolerations={},
                affinity={},
                container_spec=NodePodContainerSpec(
                    resources=ResourceRequirements(
                        cpu_request="1", 
                        cpu_limit="1",
                        memory_request="2Gi",
                        memory_limit="2Gi"
                    ),
                    envs={
                        "key": "value"
                    }, 
                    volume_mounts=[],
                ),
                volumes=[] 
            )
        )]
    )
    
    return RisingWaveSpec(
            image="ghcr.io/risingwavelabs/risingwave:v1.4.0",
            enable_default_service_monitor=True,
            state_store=StateStoreSpec(
                data_directory=CLOUDAGENT_TESTING_NAMESPACE,
                memory_state_store=StateStoreBackendMemory()
            ),
            meta_store_spec=MetaStoreSpec(
                etcd_backend=MetaStoreBackendEtcd(
                    endpoint="risingwave-etcd:2388"
                )
            ),
            config=Config(
                node_config=NodeConfig(
                    node_configuration_config_map=NodeConfigurationConfigMap(
                        name="test-cm", key="key"
                    )
                )
            ),
            frontend_service_type=SERVICE_TYPE_NODE_PORT,
            components=ComponentsSpec(
                meta_spec=component_spec,
                frontend_spec=component_spec,
                compute_spec=component_spec, 
                compactor_spec=component_spec,
                connector_spec=component_spec,
                standalone_component=get_standalone_spec(labels=labels)
            ), 
            enable_standalone_mode=is_standalone
        )
    

def get_create_rw_req(is_standalone: bool, labels: dict) -> CreateRisingWaveRequest:
    return CreateRisingWaveRequest(
        resource_meta=get_rw_meta(),
        labels={
            "cloud.risingwavelabs.com/tenant-name": "testrwinstance",
            "cloud.risingwavelabs.com/tenant-user": "1",
            "cloud.risingwavelabs.com/tenant-resource-namespace": CLOUDAGENT_TESTING_NAMESPACE,
            "cloud.risingwavelabs.com/tier": "Dummy-Tier"
        }, 
        annotations={
            "risingwave.risingwavelabs.com/inherit-label-prefix": "cloud.risingwavelabs.com"
        },
        risingwave_spec=get_rw_spec(is_standalone=is_standalone, labels=labels)
    )


def get_k8s_cli():
    config.load_kube_config("~/.kube/config")
    return client.CoreV1Api(api_client=config.new_client_from_config(context=KIND_KUBECTL_CONTEXT))


def validate_rw(is_standalone: bool, label_selector: str, max_wait_sec: int): 
    """ raises assertion error if number of pods is not as expected """
    
    cli = get_k8s_cli()
    timeout = time.time() + max_wait_sec
    rw_pods_names = []
    
    while True:
        time.sleep(5)
        assert time.time() < timeout, f"timeout while waiting for valid RW state. Standalone: {is_standalone}. Last RW pods: {rw_pods_names}"
        rw_pods = cli.list_namespaced_pod(namespace=CLOUDAGENT_TESTING_NAMESPACE, watch=False, timeout_seconds=2, label_selector=label_selector)
        # ignore terminating pods. They hang around for a while during the transition and can cause timeouts
        non_term_pods = [pod for pod in rw_pods.items if pod.status.phase != "Terminating"]
        num_pods = len(non_term_pods)
        rw_pods_names = [pod.metadata.name for pod in rw_pods.items]
        if not is_standalone:
            if num_pods == 5:
                return
        if is_standalone:
            if num_pods == 1:
                return


def get_scale_spec() -> ScaleSpec: 
    return ScaleSpec(
            replicas=1,
            resources=ResourceRequirements(
                cpu_request="250m", 
                cpu_limit="250m", 
                memory_request="61Mi",
                memory_limit="128Mi",
            )
        )


def get_rw_scale_req(is_standalone: bool) -> ScaleRisingWaveRequest: 
    scale_spec=get_scale_spec()
    if is_standalone:
        return ScaleRisingWaveRequest(
            resource_meta=get_rw_meta(),
            standalone_scale_spec=scale_spec
        )
        
    return ScaleRisingWaveRequest(
        resource_meta=get_rw_meta(),
        frontend_scale_spec=scale_spec,
        compute_scale_spec=scale_spec, 
        compactor_scale_spec=scale_spec, 
    )

class TestK8sResources:
    def test_get_create_payload(self, k8s_resource_manager_stub: K8sResourceManagerStub, setup_teardown):
        """
        Not actually a test. We use this get the payload for testing. See Readme
        """
         
        labels = {"test-pod":"1"}
        x = get_create_rw_req(is_standalone=True, labels=labels)
        print(x)
    
    def test_standalone_to_distributed(self, k8s_resource_manager_stub: K8sResourceManagerStub, setup_teardown):
        """
        Deploys a standalone RW instance and converts it into a distributed instance
        Validates if the number of pods are as expected each time.
        Does not validate on which nodes these pods are, since our test cluster only has a single node.
        """
        
        k8s_stub = k8s_resource_manager_stub
        labels = {"test-pod":"1"}
        labels_str = "test-pod=1"
        timeout_sec = 60
        
        # create and validate standalone setup
        k8s_stub.CreateRisingWave(get_create_rw_req(is_standalone=True, labels=labels))
        validate_rw(is_standalone=True, label_selector=labels_str, max_wait_sec=timeout_sec)
        
        # Go to cluster, then standalone back to cluster mode
        # for is_standalone in [False, True, False]:
        #     k8s_stub.ScaleRisingWave(get_rw_scale_req(is_standalone=is_standalone))
        #     validate_rw(is_standalone=is_standalone, label_selector=labels_str, max_wait_sec=timeout_sec)
        

    def test_k8s_resources(self, k8s_resource_manager_stub):
        k8s_stub = k8s_resource_manager_stub

        delete_namespace(CLOUDAGENT_TESTING_NAMESPACE, ignore_not_found=True)

        get_namespace = get_resource_wrapper(
            k8s_stub.GetNamespace, GetNamespaceRequest, CLOUDAGENT_TESTING_NAMESPACE, ""
        )
        namespace_not_found = resource_not_found_wrapper(get_namespace)

        # Create Namespace
        result = k8s_stub.CreateNamespace(
            CreateNamespaceRequest(resource_meta=Meta(id=CLOUDAGENT_TESTING_NAMESPACE))
        )
        assert creation_pb2.StatusCode.CREATED == result.status.code
        result = k8s_stub.CreateNamespace(
            CreateNamespaceRequest(resource_meta=Meta(id=CLOUDAGENT_TESTING_NAMESPACE))
        )
        assert creation_pb2.StatusCode.ALREADY_EXISTS == result.status.code

        # Create Service Account
        result = k8s_stub.CreateServiceAccount(
            CreateServiceAccountRequest(
                resource_meta=Meta(id="test-sa", namespace=CLOUDAGENT_TESTING_NAMESPACE)
            )
        )
        assert creation_pb2.StatusCode.CREATED == result.status.code
        result = k8s_stub.CreateServiceAccount(
            CreateServiceAccountRequest(
                resource_meta=Meta(id="test-sa", namespace=CLOUDAGENT_TESTING_NAMESPACE)
            )
        )
        assert creation_pb2.StatusCode.ALREADY_EXISTS == result.status.code

        get_service_account = get_resource_wrapper(
            k8s_stub.GetServiceAccount,
            GetServiceAccountRequest,
            "test-sa",
            CLOUDAGENT_TESTING_NAMESPACE,
        )
        service_account_ready = resource_ready_wrapper(get_service_account)
        assert service_account_ready()

        # Annotate Service Account
        k8s_stub.AnnotateServiceAccount(
            AnnotateServiceAccountRequest(
                resource_meta=Meta(
                    id="test-sa", namespace=CLOUDAGENT_TESTING_NAMESPACE
                ),
                labels={
                    "key": "val",
                },
            )
        )
        assert "'val'" == get_command_output(
            [
                "kubectl",
                "--context",
                KIND_KUBECTL_CONTEXT,
                "get",
                "serviceaccount",
                "test-sa",
                "-n",
                CLOUDAGENT_TESTING_NAMESPACE,
                "-o",
                "jsonpath='{.metadata.annotations.key}'",
            ]
        )

        # Delete Service Account
        delete_service_account = delete_resource_wrapper(
            k8s_stub.DeleteServiceAccount,
            DeleteServiceAccountRequest,
            "test-sa",
            namespace=CLOUDAGENT_TESTING_NAMESPACE,
        )

        result = delete_service_account()
        assert deletion_pb2.StatusCode.DELETED == result.status.code
        result = delete_service_account()
        assert deletion_pb2.StatusCode.NOT_FOUND == result.status.code

        # Create Secret
        result = k8s_stub.CreateSecret(
            CreateSecretRequest(
                resource_meta=Meta(
                    id="test-secret", namespace=CLOUDAGENT_TESTING_NAMESPACE
                ),
                secret_spec=Secret(
                    string_data={"key": "val"},
                ),
            )
        )
        assert creation_pb2.StatusCode.CREATED == result.status.code
        result = k8s_stub.CreateSecret(
            CreateSecretRequest(
                resource_meta=Meta(
                    id="test-secret", namespace=CLOUDAGENT_TESTING_NAMESPACE
                ),
            )
        )
        assert creation_pb2.StatusCode.ALREADY_EXISTS == result.status.code

        # Retreive Secret
        result = k8s_stub.GetSecret(
            GetSecretRequest(
                resource_meta=Meta(
                    id="test-secret", namespace=CLOUDAGENT_TESTING_NAMESPACE
                )
            )
        )
        assert resource_pb2.StatusCode.READY == result.status.code

        # Delete Secret
        delete_secret = delete_resource_wrapper(
            k8s_stub.DeleteSecret,
            DeleteSecretRequest,
            "test-secret",
            CLOUDAGENT_TESTING_NAMESPACE,
        )

        result = delete_secret()
        assert deletion_pb2.StatusCode.DELETED == result.status.code
        result = delete_secret()
        assert deletion_pb2.StatusCode.NOT_FOUND == result.status.code

        # Create ConfigMap
        result = k8s_stub.CreateConfigMap(
            CreateConfigMapRequest(
                resource_meta=Meta(
                    id="test-cm", namespace=CLOUDAGENT_TESTING_NAMESPACE
                ),
                config_map_spec=ConfigMap(
                    data={"key": "val"},
                ),
            )
        )
        assert creation_pb2.StatusCode.CREATED == result.status.code
        result = k8s_stub.CreateConfigMap(
            CreateConfigMapRequest(
                resource_meta=Meta(
                    id="test-cm", namespace=CLOUDAGENT_TESTING_NAMESPACE
                ),
            )
        )
        assert creation_pb2.StatusCode.ALREADY_EXISTS == result.status.code

        # Retreive ConfigMap
        result = k8s_stub.GetConfigMap(
            GetConfigMapRequest(
                resource_meta=Meta(id="test-cm", namespace=CLOUDAGENT_TESTING_NAMESPACE)
            )
        )
        assert resource_pb2.StatusCode.READY == result.status.code
        assert result.config_map_spec.data == {"key": "val"}

        k8s_stub.UpdateConfigMap(
            UpdateConfigMapRequest(
                resource_meta=Meta(
                    id="test-cm", namespace=CLOUDAGENT_TESTING_NAMESPACE
                ),
                from_config_map_spec=ConfigMap(
                    data={"key": "val"},
                ),
                to_config_map_spec=ConfigMap(
                    data={"key": "newval"},
                ),
            )
        )

        result = k8s_stub.GetConfigMap(
            GetConfigMapRequest(
                resource_meta=Meta(id="test-cm", namespace=CLOUDAGENT_TESTING_NAMESPACE)
            )
        )
        assert resource_pb2.StatusCode.READY == result.status.code
        assert result.config_map_spec.data == {"key": "newval"}

        # Delete ConfigMap
        delete_config_map = delete_resource_wrapper(
            k8s_stub.DeleteConfigMap,
            DeleteConfigMapRequest,
            "test-cm",
            CLOUDAGENT_TESTING_NAMESPACE,
        )

        result = delete_config_map()
        assert deletion_pb2.StatusCode.DELETED == result.status.code
        result = delete_config_map()
        assert deletion_pb2.StatusCode.NOT_FOUND == result.status.code

        # Delete Namespace
        result = k8s_stub.DeleteNamespace(
            DeleteNamespaceRequest(resource_meta=Meta(id=CLOUDAGENT_TESTING_NAMESPACE))
        )
        assert deletion_pb2.StatusCode.SCHEDULED == result.status.code

        wait(namespace_not_found, 10)

        result = k8s_stub.DeleteNamespace(
            DeleteNamespaceRequest(resource_meta=Meta(id=CLOUDAGENT_TESTING_NAMESPACE))
        )
        assert deletion_pb2.StatusCode.NOT_FOUND == result.status.code


    def test_manage_helm_releases(self, k8s_resource_manager_stub, task_stub, namespace_for_helm_test):
        k8s_stub = k8s_resource_manager_stub

        helm_release_id = "test-helm-release"
        helm_release_namespace = namespace_for_helm_test
        helm_release_version = "8.5.10"
        helm_release_chart_url = "https://charts.bitnami.com/bitnami/etcd-{}.tgz".format(helm_release_version)
        helm_release_values_json = json.dumps({
            "auth": {
                "rbac": {
                    "rootPassword": "rootPassword",
                },
            },
            "replicaCount": 1,
            # make provisioning slightly faster
            "livenessProbe": {
                "periodSeconds": 1,
                "timeoutSeconds": 3,
                "initialDelaySeconds": 10,
            },
            "readinessProbe": {
                "periodSeconds": 1,
                "timeoutSeconds": 3,
                "initialDelaySeconds": 10,
            },
            "logLevel": "info",
        })
        helm_release_upgrade_values_json = json.dumps({
            "auth": {
                "rbac": {
                    "rootPassword": "rootPassword",
                },
            },
            "logLevel": "debug",
        })

        install_task_id = "install-task"
        upgrade_task_id = "upgrade-task"
        uninstall_task_id = "unintall-task"
        task_namespace = CLOUDAGENT_NAMESPACE

        # install helm release
        res = k8s_stub.InstallHelmRelease(InstallHelmReleaseRequest(
            resource_meta=Meta(id=install_task_id, namespace=task_namespace),
            release_meta=Meta(id=helm_release_id, namespace=helm_release_namespace),
            chart_url=helm_release_chart_url,
            values_json=helm_release_values_json,
        ))
        assert res.status.code == creation_pb2.StatusCode.CREATED
        wait_task(task_stub, Meta(id=install_task_id, namespace=task_namespace), task_pb2.SUCCESS, 60)
        

        # get helm release
        res = k8s_stub.GetHelmRelease(GetHelmReleaseRequest(
            release_meta=Meta(id=helm_release_id, namespace=helm_release_namespace),
        ))
        assert res.helm_release.status == HelmReleaseStatus.DEPLOYED
        assert res.helm_release.version == helm_release_version

        # upgrade helm release
        res = k8s_stub.UpgradeHelmRelease(UpgradeHelmReleaseRequest(
            resource_meta=Meta(id=upgrade_task_id, namespace=task_namespace),
            release_meta=Meta(id=helm_release_id, namespace=helm_release_namespace),
            chart_url=helm_release_chart_url,
            values_json=helm_release_upgrade_values_json,
        ))
        assert res.status.code == creation_pb2.StatusCode.CREATED
        wait_task(task_stub, Meta(id=upgrade_task_id, namespace=task_namespace), task_pb2.SUCCESS, 60)

         # uninstall helm release
        res = k8s_stub.UninstallHelmRelease(UninstallHelmReleaseRequest(
            resource_meta=Meta(id=uninstall_task_id, namespace=task_namespace),
            release_meta=Meta(id=helm_release_id, namespace=helm_release_namespace),
        ))
        assert res.status.code == deletion_pb2.StatusCode.DELETED


    def test_service_monitor_management(self, namespace_for_service_monitor_management, k8s_resource_manager_stub):
        namespace = namespace_for_service_monitor_management
        k8s_stub = k8s_resource_manager_stub
        
        svc_monitor_name = "svc-monitor"
        svc_monitor_namespace = namespace
        svc_monitor_meta = Meta(id=svc_monitor_name, namespace=svc_monitor_namespace)

        pod_monitoring_name = "pod-monitoring"
        pod_monitoring_namespace = namespace
        pod_monitoring_meta = Meta(id=pod_monitoring_name, namespace=pod_monitoring_namespace)

        create_req = CreateServiceMonitorRequest(
            resource_meta=svc_monitor_meta,
            service_monitor_spec=ServiceMonitorSpec(endpoints=[Endpoint()])
        )

        res = k8s_stub.CreateServiceMonitor(create_req)
        assert res.status.code == creation_pb2.StatusCode.CREATED

        res = k8s_stub.CreateServiceMonitor(create_req)
        assert res.status.code == creation_pb2.StatusCode.ALREADY_EXISTS

        res = k8s_stub.DeleteServiceMonitor(DeleteServiceMonitorRequest(
            resource_meta=svc_monitor_meta
        ))
        assert res.status.code == deletion_pb2.StatusCode.DELETED

        res = k8s_stub.DeleteServiceMonitor(DeleteServiceMonitorRequest(
            resource_meta=svc_monitor_meta
        ))
        assert res.status.code == deletion_pb2.StatusCode.NOT_FOUND
        
        pod_monitoring_spec=PodMonitoringSpec(
            endpoints=[
                {
                    "port": '8080',
                }
            ],
        )

        res = k8s_stub.CreatePodMonitoring(CreatePodMonitoringRequest(
            resource_meta=pod_monitoring_meta,
            pod_monitoring_spec=pod_monitoring_spec
        ))
        assert res.status.code == creation_pb2.StatusCode.CREATED

        res = k8s_stub.CreatePodMonitoring(CreatePodMonitoringRequest(
            resource_meta=pod_monitoring_meta,
            pod_monitoring_spec=pod_monitoring_spec
        )) 
        assert res.status.code == creation_pb2.StatusCode.ALREADY_EXISTS

        res = k8s_stub.DeletePodMonitoring(DeletePodMonitoringRequest(
            resource_meta=pod_monitoring_meta
        ))
        assert res.status.code == deletion_pb2.StatusCode.DELETED

        res = k8s_stub.DeletePodMonitoring(DeletePodMonitoringRequest(
            resource_meta=pod_monitoring_meta
        ))
        assert res.status.code == deletion_pb2.StatusCode.NOT_FOUND
