import grpc
import logging

from environment.base import (
    CLOUDAGENT_NODE_PORT,
    CLOUDAGENT_CLIENT_TLS_ROOT_CERT_PATH,
    CLOUDAGENT_CLIENT_TLS_CERT_PATH,
    CLOUDAGENT_CLIENT_TLS_PRIVATE_KEY_PATH,
)

from utils.files import read_file


def new_cloudagent_grpc_channel(ep=""):
    target_ep = "localhost:{port}".format(port=CLOUDAGENT_NODE_PORT) if ep == "" else ep
    logging.info("CloudAgent endpoint: {}".format(target_ep))

    return new_grpc_mtls_channel(
        target_ep,
        CLOUDAGENT_CLIENT_TLS_ROOT_CERT_PATH,
        CLOUDAGENT_CLIENT_TLS_CERT_PATH,
        CLOUDAGENT_CLIENT_TLS_PRIVATE_KEY_PATH,
    )


def new_grpc_mtls_channel(ep, root_ca_path, cert_path, private_key_path):
    root_ca = read_file(root_ca_path)
    cert = read_file(cert_path)
    private_key = read_file(private_key_path)
    channel = grpc.secure_channel(
        target=ep,
        credentials=grpc.ssl_channel_credentials(
            root_certificates=root_ca,
            private_key=private_key,
            certificate_chain=cert,
        ),
    )
    return channel
