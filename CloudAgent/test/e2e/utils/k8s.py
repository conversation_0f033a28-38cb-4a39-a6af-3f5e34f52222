from utils.command import run_command
from environment.base import KIND_KUBECTL_CONTEXT


def create_namespace(ns: str):
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "create",
            "namespace",
            ns,
        ]
    )


def delete_namespace(ns: str, ignore_not_found: bool):
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "delete",
            "namespace",
            ns,
        ]
        + (["--ignore-not-found=true"] if ignore_not_found else [])
    )


def delete_all_jobs(ns: str):
    run_command(
        [
            "kubectl",
            "--context",
            KIND_KUBECTL_CONTEXT,
            "delete",
            "--all",
            "jobs",
            "-n",
            ns,
        ]
    )
