import time

from protopy.common.resource_pb2 import Meta
from protopy.common import resource_pb2, creation_pb2, deletion_pb2
from protopy.services.task_pb2 import GetTaskStatusRequest


def wait(f, timeout_secs):
    cnt = 0
    while True:
        if f():
            break
        time.sleep(1)
        cnt += 1
        if cnt >= timeout_secs:
            raise RuntimeError("timeout waiting for condition")


def get_resource_wrapper(getter, getter_req, meta_id, namespace):
    def f():
        return getter(getter_req(resource_meta=Meta(id=meta_id, namespace=namespace)))

    return f


def resource_ready_wrapper(resource_getter):
    def f():
        return resource_getter().status.code == resource_pb2.StatusCode.READY

    return f


def resource_not_found_wrapper(resource_getter):
    def f():
        return resource_getter().status.code == resource_pb2.StatusCode.NOT_FOUND

    return f


def delete_resource_wrapper(deleter, deleter_req, meta_id, namespace):
    def f():
        return deleter(deleter_req(resource_meta=Meta(id=meta_id, namespace=namespace)))

    return f


def wait_task(stub, resouce_meta, expected_status_code, timeout_secs=10):
    code = None

    def check_task_status():
        nonlocal code
        code = stub.GetTaskStatus(
            GetTaskStatusRequest(resource_meta=resouce_meta)
        ).status.code
        return code == expected_status_code

    try:
        wait(check_task_status, timeout_secs)
    except Exception as e:
        raise RuntimeError(f"last status code {code}") from e
