import subprocess
import sys
import os


def run_command(command: "list[str]", silent=False):
    """
    run a command and show its output
    @raise RuntimeError if the exit code of the command is not zero
    """

    p = subprocess.run(
        command, stdout=subprocess.DEVNULL if silent else sys.stdout, stderr=sys.stderr
    )

    if p.returncode != 0:
        raise RuntimeError(
            "command {} with exit code: {}".format(command, p.returncode)
        )


def get_command_output(command: "list[str]") -> str:
    return get_command_output_raw(command).decode().strip()


def get_command_output_raw(command: "list[str]") -> bytes:
    """
    run a command and return its output
    @raise RuntimeError if the exit code of the command is not zero
    """
    p = subprocess.run(command, stdout=subprocess.PIPE, stderr=sys.stderr)

    if p.returncode != 0:
        raise RuntimeError(
            "command {} with exit code: {}".format(command, p.returncode)
        )

    return p.stdout
