package main

import (
	"context"
	"flag"
	"fmt"
	"net/http"
	"os/signal"
	"syscall"
	"time"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg"
	"github.com/risingwavelabs/cloudagent/pkg/config"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/mtls"
	"github.com/risingwavelabs/cloudagent/pkg/server"
	"github.com/risingwavelabs/cloudagent/pkg/utils"

	"golang.org/x/sync/errgroup"
)

var log = logger.NewLogAgent("main")

func main() {
	// parse flags
	var (
		port       = flag.Int("port", 40001, "service port")
		zpagesPort = flag.Int("zpageport", 40090, "zpages port")
		version    = flag.Bool("version", false, "show version")
		configPath = flag.String("config_path", "", "agent configuration in text proto format")
	)
	flag.Parse()
	if *version {
		fmt.Println(pkg.CurrentVersion)
		return
	}
	config, err := config.Read(*configPath)
	if err != nil {
		log.Panicf("Cannot read config: %v", err)
	}
	log.Infof("Cluster config: %v", config)

	// setup TLS listener
	tlsConfig := utils.Unwrap(config.GetTlsConfig())
	listener, err := mtls.NewListener(mtls.ListenerOption{
		CertPath: utils.WithDefault(tlsConfig.GetCertPath(), "certs/tls.crt"),
		KeyPath:  utils.WithDefault(tlsConfig.GetKeyPath(), "certs/tls.key"),
		CaPath:   utils.WithDefault(tlsConfig.GetClientCaPath(), "certs/ca.crt"),
		Address:  fmt.Sprintf(":%d", *port),
	})
	if err != nil {
		log.Panicf("Failed to start listen on %d: %v", *port, err)
	}
	defer func() { _ = listener.Close() }()

	// register gRPC services
	s, err := server.NewAgent(context.Background(), config)
	if err != nil {
		log.Panicf("Failed to create agent server %s", err.Error())
	}

	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	defer stop()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	eg, ctx := errgroup.WithContext(ctx)

	eg.Go(func() error {
		log.Infof("Agent listening at %s", listener.Addr().String())
		if err := s.Serve(listener); err != nil {
			cancel()
			return err
		}
		log.Info("Agent quits normally")
		return nil
	})

	zpagesSrv := server.NewZpagesServer(*zpagesPort)
	eg.Go(func() error {
		log.Infof("Agent listening at :%d", *zpagesPort)
		if err := zpagesSrv.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			cancel()
			return err
		}
		return nil
	})

	eg.Go(func() error {
		<-ctx.Done()
		stop()
		const shutdownTimeout = 30 * time.Second
		closeCtx, closeCancel := context.WithTimeout(context.Background(), shutdownTimeout)
		defer closeCancel()
		// attempt to sequentially shutdown servers within shutdownTimeout.
		if err := eris.Join(
			server.GracefulStopAgent(closeCtx, s),
			zpagesSrv.Shutdown(closeCtx),
		); err != nil {
			log.Errorf("Failed to gracefully shutdown servers: %v", err)
		}
		return err
	})

	err = eg.Wait()
	if err != nil {
		log.Errorf("Server quits with error: %v", err)
	} else {
		log.Infof("Gracefully quit servers")
	}
}
