package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"

	"github.com/risingwavelabs/eris"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	"github.com/risingwavelabs/cloudagent/pkg/config"
	utilsproto "github.com/risingwavelabs/cloudagent/pkg/utils/proto"

	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/taskrunner"
)

func main() {
	var (
		encodedTaskSpec     = flag.String("task_spec", "", "task spec in base64 encoded serialized proto")
		encodedRunnerConfig = flag.String("runner_config", "", "runner config in base64 encoded serialized proto")
	)
	flag.Parse()

	log := logger.NewLogAgent("taskrunner")

	runnerCfg, err := config.FromBase64(*encodedRunnerConfig)
	if err != nil {
		log.Errorf("Cannot parse config %v, error: %v", *encodedRunnerConfig, err)
		os.Exit(1)
	}
	log.Infof("Runner config %v", runnerCfg)

	taskSpec := &pbtask.Task{}
	if err := utilsproto.FromBase64(*encodedTaskSpec, taskSpec); err != nil {
		log.Errorf("Cannot parse task %v, error: %v", *encodedTaskSpec, err)
		os.Exit(1)
	}
	log.Infof("Handling task %+v", taskSpec)

	ctx, stop := signal.NotifyContext(context.Background(), syscall.SIGINT, syscall.SIGTERM)
	ctx = logger.WithCtx(ctx, log)

	// Wrap the task running logic into an anonymous function so that defer can be
	// called before os.Exit(1)
	err = func() error {
		defer func() {
			if ctx.Err() != nil {
				log.Errorf("task is terminated/interrupted: %v", ctx.Err())
			}
			stop()
		}()

		taskrunner, err := taskrunner.InitTaskRunner(ctx, runnerCfg)
		if err != nil {
			return eris.Wrap(err, "failed to init taskrunner")
		}

		if err := taskrunner.Run(ctx, taskSpec); err != nil {
			return eris.Wrapf(err, "failed to run task %+v", taskSpec)
		}
		return nil
	}()

	if err != nil {
		log.Errorf("error executing task: %v", err)
		os.Exit(1)
	}
	log.Infof("Finished task %+v", taskSpec)
}
