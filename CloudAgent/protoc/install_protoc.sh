#!/bin/bash
CURRENT_DIR=$(dirname $0)

set -e

# check requirements
if ! [ -x "$(command -v curl)" ]; then
	echo "command 'curl' not found."
	exit 1
fi

# check OS and ISA, only support MacOS and Linux
_os=$(uname -s)

if [ "$_os" != "Linux" ] && [ "$_os" != "Darwin" ]; then
	echo "Unsupported OS: $_os"
	exit 1
else
	os=$_os
	if [ "$_os" == "Darwin" ]; then
		os="osx"
	fi
fi

_arch=$(uname -m)

if [ "$_arch" == "x86_64" ] || [ "$_arch" == "amd64" ]; then
	arch="x86_64"
elif [ "$_os" == "Linux" ] && [ "$_arch" == "i386" ]; then
	arch="x86_32"
elif [ "$_arch" == "arm64" ]; then
	arch="aarch_64"
elif [ "$_os" == "Darwin" ]; then
	arch="universal_binary"
else
	echo "Unsupported arch: $_arch"
	exit 1
fi

version="$1"

filename="protoc-$version-$os-$arch.zip"

# check if the binary exists
download_url="https://github.com/protocolbuffers/protobuf/releases/download/v$version/$filename"
status_code=$(curl -L -s -o /dev/null -I -w '%{http_code}' "$download_url")

echo "$download_url"

if [ "$status_code" != 200 ]; then
	echo "Error $status_code: Failed to install protoc."
	exit 1
fi

# download
filepath="$CURRENT_DIR/$filename"
curl -L -o "$filepath" "$download_url"
unzip "$filepath" -d "$CURRENT_DIR"
rm "$filepath"
