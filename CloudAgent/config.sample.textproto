k8s_config {
    cluster_id: ""
    static_token_auth {
        master_url: ""
        token: ""
    }
    allow_helm_charts: "https://charts.bitnami.com/bitnami/etcd-*"
}
aws_config {
    account_id: ""
    region: ""
    oidc_provider: ""
    static_creds {
        access_key_id: ""
        secret_access_key: ""
    }
    default_tags: ["sample-tag-1:tag1", "sample-tag-2:tag2"]
}
tls_config{
    cert_path: "certs/tls.crt"
    key_path: "certs/tls.key"
    client_ca_path: "certs/ca.crt"
}
