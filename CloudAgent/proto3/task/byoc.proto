syntax = "proto3";

package task.byoc;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/task/byoc";

import "common/byoc.proto";

message ApplyByocModuleTask {
  .common.byoc.ModuleOptions module_options = 1;
  .common.byoc.ApplyOptions apply_options = 2;
  .common.byoc.PackageOptions package_options = 3;
}

message RetrieveByocModuleOutputTask {
  string output_key = 1;
  .common.byoc.ModuleOptions module_options = 2;
  .common.byoc.OutputOptions output_options = 3;
  .common.byoc.PackageOptions package_options = 4;
}
