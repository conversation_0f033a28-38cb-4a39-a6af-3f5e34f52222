syntax = "proto3";

package task.aws;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/task/aws";

// AWSDirectoryCleanUpTask represents a task deleting all objects in a AWS
// directory.
message AWSDirectoryCleanUpTask {
  // AWS bucket the AWS directory belongs to
  string bucket = 1;
  // AWS directory to be deleted
  string directory = 2;
}

message AWSSimpleDataReplicationTask {
  string source_bucket = 1;
  string source_directory = 2;
  string sink_bucket = 3;
  string sink_directory = 4;
}

message AWSDirectoryCloneTask {
  string source_directory_name = 1;
  string source_bucket_name = 2;
  string destination_directory_name = 3;
  string destination_bucket_name = 4;
  string cursor = 5;
  int32 clone_size = 6;
}
