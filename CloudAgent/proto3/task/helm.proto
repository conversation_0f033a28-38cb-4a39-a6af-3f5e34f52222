syntax = "proto3";

package task.helm;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/task/helm";

message InstallReleaseTask {
  string release_name = 1;
  string release_namespace = 2;
  string values_json = 3;
  string chart_url = 4;
}

message UpgradeReleaseTask {
  string release_name = 1;
  string release_namespace = 2;
  string values_json = 3;
  string chart_url = 4;
  bool install = 5;
}

message UninstallReleaseTask {
  string release_name = 1;
  string release_namespace = 2;
}
