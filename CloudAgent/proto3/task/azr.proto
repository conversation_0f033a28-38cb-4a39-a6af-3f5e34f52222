syntax = "proto3";

package task.azr;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/task/azr";

// AWSDirectoryCleanUpTask represents a task deleting all objects in an AZR
// directory.
message AZRDirectoryCleanUpTask {
  // AZR container the directory belongs to
  string container = 1;
  // AZR directory to be deleted
  string directory = 2;
  // AZR storage account the container belongs to
  string storage_account = 3;
}

message AZRDirectoryCloneTask {
  string source_directory_name = 1;
  string source_container_name = 2;
  string destination_directory_name = 3;
  string destination_container_name = 4;
  string source_storage_account = 5;
  string destination_storage_account = 6;
  string cursor = 7;
  int32 clone_size = 8;
}
