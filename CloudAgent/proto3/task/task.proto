syntax = "proto3";

package task;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/task";

import "task/rwc.proto";
import "task/gcp.proto";
import "task/helm.proto";
import "task/aws.proto";
import "task/azr.proto";
import "task/byoc.proto";

// Task wraps the configuration of a task, which will be run by task runner.
message Task {
  oneof task {
    rwc.RisectlTask risectl_task = 1;
    gcp.GCSDirectoryCleanUpTask gcs_directory_clean_up_task = 2;
    helm.InstallReleaseTask install_helm_release_task = 3;
    helm.UpgradeReleaseTask upgrade_helm_release_task = 4;
    helm.UninstallReleaseTask uninstall_helm_release_task = 5;
    aws.AWSDirectoryCleanUpTask aws_directory_clean_up_task = 6;
    azr.AZRDirectoryCleanUpTask azr_directory_clean_up_task = 7;
    aws.AWSSimpleDataReplicationTask aws_simple_data_replication_task = 8;
    byoc.ApplyByocModuleTask apply_byoc_module_task = 9;
    byoc.RetrieveByocModuleOutputTask retrieve_byoc_module_output_task = 10;
    aws.AWSDirectoryCloneTask aws_directory_clone_task = 11;
    gcp.GCSDirectoryCloneTask gcp_directory_clone_task = 12;
    azr.AZRDirectoryCloneTask azr_directory_clone_task = 13;
  }
}
