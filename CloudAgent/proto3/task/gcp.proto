syntax = "proto3";

package task.gcp;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/task/gcp";

// GCSDirectoryCleanUpTask represents a task deleting all objects in a GCS
// directory.
message GCSDirectoryCleanUpTask {
  // GCS bucket the GCS directory belongs to
  string bucket = 1;
  // GCS directory to be deleted
  string directory = 2;
}

message GCSDirectoryCloneTask {
  string source_directory_name = 1;
  string source_bucket_name = 2;
  string destination_directory_name = 3;
  string destination_bucket_name = 4;
  string cursor = 5;
  int32 clone_size = 6;
}
