syntax = "proto3";

package common.aws;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/aws";

message DBInstanceSpec {
  // The DB instance identifier. This parameter is stored as a lowercase string. -required-
  string db_instance_identifier = 1;
  // The compute and memory capacity of the DB instance. -required-
  string db_instance_class = 2;
  // The amount of storage in gibibytes (GiB) to allocate for the DB instance.
  uint32 allocated_storage = 3;
  // The name of the database engine to be used for this instance. -required-
  string engine = 4;
  // The version number of the database engine to use.
  string engine_version = 5;
  // The meaning of this parameter differs according to the database engine you use.
  string db_name = 6;
  // The name for the master user.
  string master_username = 7;
  // The password for the master user.
  PasswordSecretRef master_user_password = 8;
  // A DB subnet group to associate with this DB instance.
  string db_subnet_group_name = 9;
  // A list of Amazon EC2 VPC security groups to associate with this DB instance.
  repeated string vpc_security_group_ids = 10;
  // Tags to assign to the DB instance.
  map<string, string> tags = 11;
  // A value that indicates whether the DB instance is encrypted.
  bool storageEncrypted = 12;
}

message PasswordSecretRef {
  string namespace = 1;
  string name = 2;
  string key = 3;
}

message DBInstanceEndpoint {
  string address = 1;
}

enum DBInstanceStatus {
  UNKNOWN = 0;
  NOT_READY = 1;
  AVAILABLE = 2;
  STOPPED = 3;
}
