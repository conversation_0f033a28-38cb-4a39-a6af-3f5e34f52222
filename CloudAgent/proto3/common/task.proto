syntax = "proto3";

package common.resource.task;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/resource/task";

enum StatusCode {
  UNKNOWN = 0;
  // The job is stil running
  RUNNING = 1;
  // The job failed
  FAILED = 2;
  // The job completed successfully
  SUCCESS = 3;
  // The job is not found
  NOT_FOUND = 4;
}

message Status {
  StatusCode code = 1;
  string message = 2;
}
