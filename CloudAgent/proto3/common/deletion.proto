syntax = "proto3";

package common.resource.deletion;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion";

enum StatusCode {
  UNKNOWN = 0;
  // The deletion request is acknowledged by CloudAgent and will be scheduled
  // for deletion. This code is for resources requiring a long running deletion.
  SCHEDULED = 1;
  // The deletion is done within the deletion RPC. This status is for short
  // running deletion that doesn't need to be run async. e.g. K8s ConfigMap
  // deletion.
  DELETED = 2;
  // The requested resource doesn't exist.
  NOT_FOUND = 3;
}

message Status {
  StatusCode code = 1;
}
