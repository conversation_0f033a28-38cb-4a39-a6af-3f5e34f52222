syntax = "proto3";

package common.azr;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/azr";

message PGServerSpec {
  // AzureName: The name of the resource in Azure. This is often the same as the
  // name of the resource in Kubernetes but it
  // doesn't have to be.
  string azure_name = 1;
  // Owner: The owner of the resource. The owner controls where the resource goes
  // when it is deployed. The owner also
  // controls the resources lifecycle. When the owner is deleted the resource
  // will also be deleted. Owner is expected to be a
  // reference to a resources.azure.com/ResourceGroup resource
  ResourceReference owner = 2;
  // Version: PostgreSQL Server version.
  string version = 3;
  // Location: The geo-location where the resource lives -required-
  string location = 4;
  // Sku: The SKU (pricing tier) of the server.
  PGServerSku sku = 5;
  // Storage: Storage properties of a server.
  PGServerStorage storage = 6;

  // AdministratorLogin: The administrator's login name of a server. Can only be
  // specified when the server is being created
  // (and is required for creation).
  string administrator_login = 7;
  // AdministratorLoginPassword: The administrator login password (required for
  // server creation).
  SecretKeyRef administrator_login_password = 8;

  // Network: Network properties of a server.
  PGServerNetwork network = 9;

  // Tags: Resource tags.
  map<string, string> tags = 10;
}

message ResourceReference {
  string arm_id = 1;
}

message PGServerSku {
  // Name: The name of the sku, typically, tier + family + cores, e.g.
  // Standard_D4s_v3. -required-
  string name = 1;
  // Tier: The tier of the particular SKU. e.g. Burstable. -required-
  string tier = 2;
}

message PGServerStorage {
  // StorageSizeGB: Max storage allowed for a server.
  int32 storage_size_gb = 1;
}

message SecretKeyRef {
  // Key that identifies the value to be extracted.
  string key = 1;
  // Name of the Secret to extract a value from.
  string name = 2;
}

message PGServerNetwork {
  // DelegatedSubnetResourceReference: delegated subnet arm resource id.
  ResourceReference delegated_subnet = 1;
  // PrivateDnsZoneArmResourceReference: private dns zone arm resource id.
  ResourceReference private_dns_zone = 2;
}

enum PGServerState {
  UNKNOWN = 0;
  UPDATING = 1;
  READY = 2;
  STOPPED = 3;
}

