syntax = "proto3";

package common.resource;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/resource";

enum StatusCode {
  UNKNOWN = 0;
  // Resource doesn't exist in the cluster.
  NOT_FOUND = 1;
  // Resource exists but is not ready for usage yet.
  NOT_READY = 2;
  // Resource exists and is fully ready.
  READY = 3;
  // Resource exists but is in a non-recoverable error state.
  ERROR = 4;
}

message Status {
  StatusCode code = 1;
  string message = 2;
}

// Meta contains metadata of a resource, which Cloud Agent uses to unqiuely
// create, identify and locate a resource for Create/Get/Delete
//
// CloudAgent expect this value to be specified by the upstream caller and will
// use this to construct the underlying provisioning. Different types of
// resources may have different constrainsts. CloudAgent expects the upstream
// provided metadata can meet these constraints.
message Meta {
  // id uniqely identifies a resouce in a namespace. Depending on the type of
  // resource, there might be other constraints over id. For example some
  // resources requires the id to be unique across namespaces and some resource
  // requires id to be within certain length.
  //
  // Detailed requirements for a certain type of resource should be documented
  // in the resource sepcific APIs.
  string id = 1;
  // The namesapce a resource belongs to.  Not all objects are required to be
  // scoped to a namespace -- the value of this field for those objects will be
  // empty.
  string namespace = 2;
}
