syntax = "proto3";

package common.resource.creation;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation";

enum StatusCode {
  UNKNOWN = 0;
  // The creation request is acknowledged by CloudAgent and will be scheduled
  // for creation. This code is for resources requiring a long running creation.
  SCHEDULED = 1;
  // The creation is done within the creation RPC. This status is for short
  // running creation that doesn't need to be run async. e.g. K8s ConfigMap
  // creation.
  CREATED = 2;
  // The requested resource already exists.
  ALREADY_EXISTS = 3;
}

message Status {
  StatusCode code = 1;
}
