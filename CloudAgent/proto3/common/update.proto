syntax = "proto3";

package common.resource.update;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update";

enum StatusCode {
  UNKNOWN = 0;
  // The update request is acknowledged by CloudAgent and will be scheduled
  // for update. This code is for resources requiring a long running update.
  SCHEDULED = 1;
  // The resource to update not found.
  NOT_FOUND = 2;
  // The resource update already exists. But may not completed, requires to
  // check resource status.
  ALREADY_EXISTS = 3;
  // The update is done within the creation RPC. This status is for short
  // running creation that doesn't need to be run async.
  UPDATED = 4;
}

message Status {
  StatusCode code = 1;
}
