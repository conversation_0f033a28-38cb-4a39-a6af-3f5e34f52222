syntax = "proto3";

package common.postgresql;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql";

import "common/k8s.proto";

message PostgreSqlSpec {
  // name of the team the cluster belongs to. Required field.
  // postgresql-operator support teams API and OAuth2 token to connect. But we don't enable teams API and don't use the field.
  string team_id = 1;

  // custom Docker image that overrides the docker_image operator parameter. It should be a Spilo image. Optional.
  string docker_image = 2;

  // CPU and memory requests and limits for the Postgres container. Optional.
  k8s.ResourceRequirements resources = 3;

  // total number of instances for a given cluster. The operator parameters max_instances and min_instances may also adjust this number. Required field.
  uint32 number_of_instances = 4;

  // properties of the persistent storage that stores Postgres data. Required field.
  PostgreSqlVolume volume = 5;

  // a map of usernames to user flags for the users that should be created in the cluster by the operator. Optional.
  map<string, StringArray> users = 6;

  // a map of database names to database owners for the databases that should be created by the operator. Optional.
  map<string, string> databases = 7;

  // Required field.
  PostgreSqlParam postgresql = 8;

  // a list of tolerations that apply to the cluster pods. Optional.
  repeated k8s.Toleration tolerations = 9;

  // Optional.
  k8s.NodeAffinity node_affinity = 10;

  // a map of key value pairs that gets attached as annotations to each pod created for the database. Optional.
  map<string, string> pod_annotations = 11;

  // a map of key value pairs that gets attached as annotations to the services created for the database cluster. Optional.
  map<string, string> service_annotations = 12;

}

message StringArray {
  repeated string value = 1;
}

message PostgreSqlVolume {
  // the size of the target volume. Usual Kubernetes size modifiers, i.e. Gi or Mi, apply. Required.
  string size = 1;

  // the name of the Kubernetes storage class to draw the persistent volume from. Optional.
  string storage_class = 2;
}

message PostgreSqlParam {
  // the Postgres major version of the cluster. Required field.
  string version = 1;

  // a dictionary of Postgres parameter names and values to apply to the resulting cluster. Optional.
  map<string, string> parameters = 2;
}

message Credentials {
  string username = 1;
  string password = 2;
}

enum UpdateStatusCode {
  UPDATE_UNKNOWN = 0;
  // The update request is acknowledged by CloudAgent and will be scheduled
  // for update. This code is for resources requiring a long running update.
  UPDATE_SCHEDULED = 1;
  // The resource to update not found.
  UPDATE_NOT_FOUND = 2;
  // The resource update already exists. But may not completed, requires to
  // check resource status.
  UPDATE_ALREADY_EXISTS = 3;
}
