syntax = "proto3";

package common.prometheus;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus";

import "common/k8s.proto";

message Endpoint {
  string port = 1;
  string interval = 2;
  // A prometheus parseable string representation of Duration
  // https://github.com/prometheus/common/blob/94bf9828e56d9670579b28a9f78237d3cd8d0395/model/time.go#L204
  string scrape_timeout = 3;
  repeated RelabelConfig metric_relabelings = 4;
}

message ServiceMonitorSpec {
  string job_label = 1;
  repeated string target_labels = 2;
  repeated Endpoint endpoints = 3;
  k8s.LabelSelector selector = 4;
}

message RelabelConfig {
  repeated string source_labels = 1;
  string regex = 2;
  string action = 3;
}
