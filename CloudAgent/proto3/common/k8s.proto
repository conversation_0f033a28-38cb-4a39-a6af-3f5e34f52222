syntax = "proto3";

package common.k8s;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/k8s";

message ConfigMap {
  bool immutable = 1;
  map<string, string> data = 2;
  map<string, bytes> binary_data = 3;
}

message Secret {
  bool immutable = 1;
  map<string, bytes> data = 2;
  map<string, string> string_data = 3;
  string type = 4;
}

message ServiceAccount {
  string name = 1;
  string namespace = 2;
}

enum HelmReleaseStatus {
  UNKNOWN = 0;
  DEPLOYED = 1;
  UNINSTALLING = 2;
  INSTALLING = 3;
  UPGRADING = 4;
  FAILED = 5;
  UNINSTALLED = 6;
}

message HelmRelease {
  string release_name = 1;
  string namespace = 2;
  HelmReleaseStatus status = 3;
  string version = 4;
}

enum ServiceType {
  SERVICE_TYPE_UNKNOWN = 0;
  SERVICE_TYPE_CLUSTER_IP = 1;
  SERVICE_TYPE_NODE_PORT = 2;
  SERVICE_TYPE_LOAD_BALANCER = 3;
  SERVICE_TYPE_EXTERNAL_NAME = 4;
}

enum TaintEffect {
  TAINT_EFFECT_UNKNOWN = 0;
  TAINT_EFFECT_NO_SCHEDULE = 1;
  TAINT_EFFECT_PREFER_NO_SCHEDULE = 2;
  TAINT_EFFECT_NO_EXECUTE = 3;
}

enum TolerationOperator {
  TOLERATION_OPERATOR_UNKNOWN = 0;
  TOLERATION_OPERATOR_EXISTS = 1;
  TOLERATION_OPERATOR_EQUAL = 2;
}

message Toleration {
  string key = 1;
  string value = 2;
  TaintEffect effect = 3;
  TolerationOperator operator = 4;
  optional int64 toleration_secs = 5;
}

// Affinity is a group of affinity scheduling rules.
message Affinity {
  // Describes node affinity scheduling rules for the pod.
  NodeAffinity node_affinity = 1;

  // Describes pod affinity scheduling rules (e.g. co-locate this pod in the
  // same node, zone, etc. as some other pod(s)).
  PodAffinity pod_affinity = 2;

  // Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod
  // in the same node, zone, etc. as some other pod(s)).
  PodAntiAffinity pod_anti_affinity = 3;
}

// Node affinity is a group of node affinity scheduling rules.
message NodeAffinity {
  // If the affinity requirements specified by this field are not met at
  // scheduling time, the pod will not be scheduled onto the node.
  // If the affinity requirements specified by this field cease to be met
  // at some point during pod execution (e.g. due to an update), the system
  // may or may not try to eventually evict the pod from its node.
  // +optional
  NodeSelector required_during_scheduling_ignored_during_execution = 1;

  // The scheduler will prefer to schedule pods to nodes that satisfy
  // the affinity expressions specified by this field, but it may choose
  // a node that violates one or more of the expressions. The node that is
  // most preferred is the one with the greatest sum of weights, i.e.
  // for each node that meets all of the scheduling requirements (resource
  // request, requiredDuringScheduling affinity expressions, etc.),
  // compute a sum by iterating through the elements of this field and adding
  // "weight" to the sum if the node matches the corresponding matchExpressions;
  // the node(s) with the highest sum are the most preferred. +optional
  repeated PreferredSchedulingTerm
      preferred_during_scheduling_ignored_during_execution = 2;
}

// A node selector represents the union of the results of one or more label
// queries over a set of nodes; that is, it represents the OR of the selectors
// represented by the node selector terms.
message NodeSelector {
  // A list of node selector terms. The terms are ORed.
  repeated NodeSelectorTerm node_selector_terms = 1;
}

// An empty preferred scheduling term matches all objects with implicit weight 0
// (i.e. it's a no-op). A null preferred scheduling term matches no objects
// (i.e. is also a no-op).
message PreferredSchedulingTerm {
  // Weight associated with matching the corresponding nodeSelectorTerm, in the
  // range 1-100.
  int32 weight = 1;

  // A node selector term, associated with the corresponding weight.
  NodeSelectorTerm preference = 2;
}

// A null or empty node selector term matches no objects. The requirements of
// them are ANDed.
// The TopologySelectorTerm type implements a subset of the NodeSelectorTerm.
message NodeSelectorTerm {
  // A list of node selector requirements by node's labels.
  // +optional
  repeated NodeSelectorRequirement match_expressions = 1;

  // A list of node selector requirements by node's fields.
  // +optional
  repeated NodeSelectorRequirement match_fields = 2;
}

// A node selector requirement is a selector that contains values, a key, and an
// operator that relates the key and values.
message NodeSelectorRequirement {
  // The label key that the selector applies to.
  string key = 1;

  // Represents a key's relationship to a set of values.
  // Valid operators are In, NotIn, Exists, DoesNotExist. Gt, and Lt.
  NodeSelectorOperator operator = 2;

  // An array of string values. If the operator is In or NotIn,
  // the values array must be non-empty. If the operator is Exists or
  // DoesNotExist, the values array must be empty. If the operator is Gt or Lt,
  // the values array must have a single element, which will be interpreted as
  // an integer. This array is replaced during a strategic merge patch.
  // +optional
  repeated string values = 3;
}

enum NodeSelectorOperator {
  NODE_SELECTOR_OPERATOR_UNKNOWN = 0;
  NODE_SELECTOR_OPERATOR_IN = 1;
  NODE_SELECTOR_OPERATOR_NOT_IN = 2;
  NODE_SELECTOR_OPERATOR_EXISTS = 3;
  NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST = 4;
  NODE_SELECTOR_OPERATOR_GT = 5;
  NODE_SELECTOR_OPERATOR_LT = 6;
}

// Pod affinity is a group of inter pod affinity scheduling rules.
message PodAffinity {
  // If the affinity requirements specified by this field are not met at
  // scheduling time, the pod will not be scheduled onto the node.
  // If the affinity requirements specified by this field cease to be met
  // at some point during pod execution (e.g. due to a pod label update), the
  // system may or may not try to eventually evict the pod from its node.
  // When there are multiple elements, the lists of nodes corresponding to each
  // podAffinityTerm are intersected, i.e. all terms must be satisfied.
  // +optional
  repeated PodAffinityTerm required_during_scheduling_ignored_during_execution =
      1;

  // The scheduler will prefer to schedule pods to nodes that satisfy
  // the affinity expressions specified by this field, but it may choose
  // a node that violates one or more of the expressions. The node that is
  // most preferred is the one with the greatest sum of weights, i.e.
  // for each node that meets all of the scheduling requirements (resource
  // request, requiredDuringScheduling affinity expressions, etc.),
  // compute a sum by iterating through the elements of this field and adding
  // "weight" to the sum if the node has pods which matches the corresponding
  // podAffinityTerm; the node(s) with the highest sum are the most preferred.
  // +optional
  repeated WeightedPodAffinityTerm
      preferred_during_scheduling_ignored_during_execution = 2;
}

// Pod anti affinity is a group of inter pod anti affinity scheduling rules.
message PodAntiAffinity {
  // If the anti-affinity requirements specified by this field are not met at
  // scheduling time, the pod will not be scheduled onto the node.
  // If the anti-affinity requirements specified by this field cease to be met
  // at some point during pod execution (e.g. due to a pod label update), the
  // system may or may not try to eventually evict the pod from its node.
  // When there are multiple elements, the lists of nodes corresponding to each
  // podAffinityTerm are intersected, i.e. all terms must be satisfied.
  repeated PodAffinityTerm required_during_scheduling_ignored_during_execution =
      1;

  // The scheduler will prefer to schedule pods to nodes that satisfy
  // the anti-affinity expressions specified by this field, but it may choose
  // a node that violates one or more of the expressions. The node that is
  // most preferred is the one with the greatest sum of weights, i.e.
  // for each node that meets all of the scheduling requirements (resource
  // request, requiredDuringScheduling anti-affinity expressions, etc.),
  // compute a sum by iterating through the elements of this field and adding
  // "weight" to the sum if the node has pods which matches the corresponding
  // podAffinityTerm; the node(s) with the highest sum are the most preferred.
  repeated WeightedPodAffinityTerm
      preferred_during_scheduling_ignored_during_execution = 2;
}

message PodAffinityTerm {
  // A label query over a set of resources, in this case pods.
  // +optional
  LabelSelector label_selector = 1;

  // namespaces specifies a static list of namespace names that the term applies
  // to. The term is applied to the union of the namespaces listed in this field
  // and the ones selected by namespaceSelector.
  // null or empty namespaces list and null namespaceSelector means "this pod's
  // namespace". +optional
  repeated string namespaces = 2;

  // This pod should be co-located (affinity) or not co-located (anti-affinity)
  // with the pods matching the labelSelector in the specified namespaces, where
  // co-located is defined as running on a node whose value of the label with
  // key topologyKey matches that of any node on which any of the selected pods
  // is running. Empty topologyKey is not allowed.
  string topology_key = 3;

  // A label query over the set of namespaces that the term applies to.
  // The term is applied to the union of the namespaces selected by this field
  // and the ones listed in the namespaces field.
  // null selector and null or empty namespaces list means "this pod's
  // namespace". An empty selector ({}) matches all namespaces. +optional
  LabelSelector namespace_selector = 4;
}

message WeightedPodAffinityTerm {
  // weight associated with matching the corresponding podAffinityTerm,
  // in the range 1-100.
  int32 weight = 1;

  // Required. A pod affinity term, associated with the corresponding weight.
  PodAffinityTerm pod_affinity_term = 2;
}

message LabelSelector {
  map<string, string> match_labels = 1;
  repeated LabelSelectorRequirement match_expressions = 2;
}

message LabelSelectorRequirement {
  string key = 1;
  LabelSelectorOperator operator = 2;
  repeated string values = 3;
}

enum LabelSelectorOperator {
  LABEL_SELECTOR_OPERATOR_UNKNOWN = 0;
  LABEL_SELECTOR_OPERATOR_IN = 1;
  LABEL_SELECTOR_OPERATOR_NOT_IN = 2;
  LABEL_SELECTOR_OPERATOR_EXISTS = 3;
  LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST = 4;
}

message ResourceRequirements {
  string cpu_request = 1;
  string cpu_limit = 2;
  string memory_request = 3;
  string memory_limit = 4;
}

enum PodPhase {
  POD_PHASE_UNKNOWN = 0;
  POD_PHASE_PENDING = 1;
  POD_PHASE_RUNNING = 2;
  POD_PHASE_SUCCEEDED = 3;
  POD_PHASE_FAILED = 4;
}

message Pod {
  string name = 1;
  map<string, string> labels = 2;
  PodPhase status_phase = 3;
}

message ServiceSpec {
  repeated ServicePort ports = 1;
  map<string, string> selector = 2;
}

message ServicePort {
  string name = 1;
  int32 port = 2;
}

// NetworkPolicySpec provides the specification of a NetworkPolicy
message NetworkPolicySpec {
  // podSelector selects the pods to which this NetworkPolicy object applies.
  // The array of ingress rules is applied to any pods selected by this field.
  // Multiple network policies can select the same set of pods. In this case,
  // the ingress rules for each are combined additively.
  // This field is NOT optional and follows standard label selector semantics.
  // An empty podSelector matches all pods in this namespace.
  LabelSelector pod_selector = 1;
  // ingress is a list of ingress rules to be applied to the selected pods.
  // Traffic is allowed to a pod if there are no NetworkPolicies selecting the
  // pod (and cluster policy otherwise allows the traffic), OR if the traffic
  // source is the pod's local node, OR if the traffic matches at least one
  // ingress rule across all of the NetworkPolicy objects whose podSelector
  // matches the pod. If this field is empty then this NetworkPolicy does not
  // allow any traffic (and serves solely to ensure that the pods it selects are
  // isolated by default)
  repeated NetworkPolicyIngressRule ingress = 2;
  // egress is a list of egress rules to be applied to the selected pods.
  // Outgoing traffic is allowed if there are no NetworkPolicies selecting the
  // pod (and cluster policy otherwise allows the traffic), OR if the traffic
  // matches at least one egress rule across all of the NetworkPolicy objects
  // whose podSelector matches the pod. If this field is empty then this
  // NetworkPolicy limits all outgoing traffic (and serves solely to ensure that
  // the pods it selects are isolated by default).
  repeated NetworkPolicyEgressRule egress = 3;
  // policyTypes is a list of rule types that the NetworkPolicy relates to.
  // Valid options are ["Ingress"], ["Egress"], or ["Ingress", "Egress"].
  // If this field is not specified, it will default based on the existence of
  // ingress or egress rules; policies that contain an egress section are
  // assumed to affect egress, and all policies (whether or not they contain an
  // ingress section) are assumed to affect ingress. If you want to write an
  // egress-only policy, you must explicitly specify policyTypes [ "Egress" ].
  // Likewise, if you want to write a policy that specifies that no egress is
  // allowed, you must specify a policyTypes value that include "Egress" (since
  // such a policy would not include an egress section and would otherwise
  // default to just [ "Ingress" ]).
  repeated NetworkPolicyType policy_types = 4;
}

// NetworkPolicyEgressRule describes a particular set of traffic that is allowed
// out of pods matched by a NetworkPolicySpec's podSelector. The traffic must
// match both ports and to.
message NetworkPolicyEgressRule {
  // ports is a list of destination ports for outgoing traffic.
  // Each item in this list is combined using a logical OR. If this field is
  // empty or missing, this rule matches all ports (traffic not restricted by
  // port). If this field is present and contains at least one item, then this
  // rule allows traffic only if the traffic matches at least one port in the
  // list. +optional
  repeated NetworkPolicyPort ports = 1;

  // to is a list of destinations for outgoing traffic of pods selected for this
  // rule. Items in this list are combined using a logical OR operation. If this
  // field is empty or missing, this rule matches all destinations (traffic not
  // restricted by destination). If this field is present and contains at least
  // one item, this rule allows traffic only if the traffic matches at least one
  // item in the to list. +optional
  repeated NetworkPolicyPeer to = 2;
}
// NetworkPolicyIngressRule describes a particular set of traffic that is
// allowed to the pods matched by a NetworkPolicySpec's podSelector. The traffic
// must match both ports and from.
message NetworkPolicyIngressRule {
  // ports is a list of ports which should be made accessible on the pods
  // selected for this rule. Each item in this list is combined using a logical
  // OR. If this field is empty or missing, this rule matches all ports (traffic
  // not restricted by port). If this field is present and contains at least one
  // item, then this rule allows traffic only if the traffic matches at least
  // one port in the list. +optional
  repeated NetworkPolicyPort ports = 1;

  // from is a list of sources which should be able to access the pods selected
  // for this rule. Items in this list are combined using a logical OR
  // operation. If this field is empty or missing, this rule matches all sources
  // (traffic not restricted by source). If this field is present and contains
  // at least one item, this rule allows traffic only if the traffic matches at
  // least one item in the from list. +optional
  repeated NetworkPolicyPeer from = 2;
}

// NetworkPolicyPort describes a port to allow traffic on
message NetworkPolicyPort {
  // protocol represents the protocol (TCP, UDP, or SCTP) which traffic must
  // match. If not specified, this field defaults to TCP. +optional
  NetworkProtocol protocol = 1;

  // port represents the port on the given protocol. This can either be a
  // numerical or named port on a pod. If this field is not provided, this
  // matches all port names and numbers. If present, only traffic on the
  // specified protocol AND port will be matched. +optional
  IntOrString port = 2;

  // endPort indicates that the range of ports from port to endPort if set,
  // inclusive, should be allowed by the policy. This field cannot be defined if
  // the port field is not defined or if the port field is defined as a named
  // (string) port. The endPort must be equal or greater than port. +optional
  int32 endPort = 3;
}

// NetworkPolicyPeer describes a peer to allow traffic to/from. Only certain
// combinations of fields are allowed
message NetworkPolicyPeer {
  // podSelector is a label selector which selects pods. This field follows
  // standard label selector semantics; if present but empty, it selects all
  // pods.
  //
  // If namespaceSelector is also set, then the NetworkPolicyPeer as a whole
  // selects the pods matching podSelector in the Namespaces selected by
  // NamespaceSelector. Otherwise it selects the pods matching podSelector in
  // the policy's own namespace. +optional
  LabelSelector podSelector = 1;

  // namespaceSelector selects namespaces using cluster-scoped labels. This
  // field follows standard label selector semantics; if present but empty, it
  // selects all namespaces.
  //
  // If podSelector is also set, then the NetworkPolicyPeer as a whole selects
  // the pods matching podSelector in the namespaces selected by
  // namespaceSelector. Otherwise it selects all pods in the namespaces selected
  // by namespaceSelector. +optional
  LabelSelector namespaceSelector = 2;

  // ipBlock defines policy on a particular IPBlock. If this field is set then
  // neither of the other fields can be.
  IPBlock ipBlock = 3;
}

message IPBlock {
  // cidr is a string representing the IPBlock
  // Valid examples are "***********/24" or "2001:db8::/64"
  string cidr = 1;

  // except is a slice of CIDRs that should not be included within an IPBlock
  // Valid examples are "***********/24" or "2001:db8::/64"
  // Except values will be rejected if they are outside the cidr range
  repeated string except = 2;
}

message IntOrString {
  optional int32 int_val = 1;

  optional string str_val = 2;
}

enum NetworkProtocol {
  NETWORK_PROTOCOL_UNKNOWN = 0;
  NETWORK_PROTOCOL_TCP = 1;
  NETWORK_PROTOCOL_UDP = 2;
  NETWORK_PROTOCOL_SCTP = 3;
}

enum NetworkPolicyType {
  NETWORK_POLICY_UNKNOWN = 0;
  NETWORK_POLICY_INGRESS = 1;
  NETWORK_POLICY_EGRESS = 2;
}

message PersistentVolumeClaimSpec {
  repeated PVCAccessMode access_modes = 1;
  LabelSelector selector = 2;
  VolumeResourceRequirements resources = 3;
  string volume_name = 4;
  string storage_class_name = 5;
}

enum PVCAccessMode {
  PVC_ACCESS_MODE_UNSPECIFIED = 0;
  PVC_ACCESS_MODE_READ_WRITE_ONCE = 1;
  PVC_ACCESS_MODE_READ_WRITE_MANY = 2;
  PVC_ACCESS_MODE_READ_ONLY_MANY = 3;
  PVC_ACCESS_MODE_READ_WRITE_ONCE_POD = 4;
}

message VolumeResourceRequirements {
  string storage_request = 1;
}

message VolumeMount {
  // This must match the Name of a Volume.
  string name = 1;

  // Mounted read-only if true, read-write otherwise (false or unspecified).
  // Defaults to false.
  // +optional
  bool read_only = 2;

  // Path within the container at which the volume should be mounted.  Must
  // not contain ':'.
  string mount_path = 3;

  // Path within the volume from which the container's volume should be mounted.
  // Defaults to "" (volume's root).
  // +optional
  string sub_path = 4;

  // mountPropagation determines how mounts are propagated from the host
  // to container and the other way around.
  // When not set, MountPropagationNone is used.
  // This field is beta in 1.10.
  // +optional
  string mount_propagation = 5;

  // Expanded path within the volume from which the container's volume should be
  // mounted. Behaves similarly to SubPath but environment variable references
  // $(VAR_NAME) are expanded using the container's environment. Defaults to ""
  // (volume's root). SubPathExpr and SubPath are mutually exclusive. +optional
  string sub_path_expr = 6;
}

message Volume {
  string name = 1;
  oneof volumne_source {
    PersistentVolumeClaimVolumeSource persistentVolumeClaim = 2;
    EmptyDirVolumeSource emptyDir = 3;
    SecretVolumeSource secret = 4;
  }
}

message PersistentVolumeClaimVolumeSource {
  // claimName is the name of a PersistentVolumeClaim in the same namespace as
  // the pod using this volume. More info:
  // https://kubernetes.io/docs/concepts/storage/persistent-volumes#persistentvolumeclaims
  string claim_name = 1;

  // readOnly Will force the ReadOnly setting in VolumeMounts.
  // Default false.
  // +opional
  bool read_only = 2;
}

message EmptyDirVolumeSource {
  // The medium field controls where emptyDir volumes are stored.
  // default value is "" which means it will be aligned with node config.
  string medium = 1;
  optional string size_limit = 2;
}

message SecretVolumeSource {
  string secret_name = 1;
  repeated KeyToPath items = 2;
  optional int32 default_mode = 3;
  optional bool is_optional = 4;
}

// KeyToPath maps a string key to a path within a volume.
message KeyToPath {
  // The key to project.
  string key = 1;

  // The relative path of the file to map the key to.
  // May not be an absolute path.
  // May not contain the path element '..'.
  // May not start with the string '..'.
  string path = 2;
  // Optional: mode bits to use on this file, should be a value between 0
  // and 0777. If not specified, the volume defaultMode will be used.
  // This might be in conflict with other options that affect the file
  // mode, like fsGroup, and the result can be other mode bits set.
  // +optional
  optional int32 mode = 3;
}

// EnvVar represents an environment variable present in a Container.
message EnvVar {
  // Required: Name of the environment variable.
  // When the RelaxedEnvironmentVariableValidation feature gate is disabled,
  // this must consist of alphabetic characters, digits, '_', '-', or '.', and
  // must not start with a digit. When the RelaxedEnvironmentVariableValidation
  // feature gate is enabled, this may contain any printable ASCII characters
  // except '='.
  string name = 1;

  // using the previously defined environment variables in the container and
  // any service environment variables.  If a variable cannot be resolved,
  // the reference in the input string will be unchanged.  Double $$ are
  // reduced to a single $, which allows for escaping the $(VAR_NAME)
  // syntax: i.e. "$$(VAR_NAME)" will produce the string literal
  // "$(VAR_NAME)".  Escaped references will never be expanded,
  // regardless of whether the variable exists or not.
  string value = 2;
}
