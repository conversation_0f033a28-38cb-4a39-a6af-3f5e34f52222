syntax = "proto3";

package common.gmp;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/gmp";

import "common/k8s.proto";

// API definition: https://github.com/GoogleCloudPlatform/prometheus-engine/blob/main/doc/api.md#podmonitoringspec
message PodMonitoringSpec {
  repeated Endpoint endpoints = 1;
  k8s.LabelSelector selector = 2;
  TargetLabels target_labels = 3;
}

message Endpoint {
  string port = 1;
  string interval = 2;
  // A prometheus parseable string representation of Duration
  // https://github.com/prometheus/common/blob/94bf9828e56d9670579b28a9f78237d3cd8d0395/model/time.go#L204
  string timeout = 3;
  repeated RelabelingRule metric_relabeling = 4;
}

message RelabelingRule {
  repeated string source_labels = 1;
  string regex = 2;
  string action = 3;
}

message TargetLabels {
  repeated LabelMapping fromPod = 1;
}

message LabelMapping {
  string from = 1;
  string to = 2;
}
