syntax = "proto3";

package common.rw;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/rw";

import "common/k8s.proto";

message RisingWaveSpec {
  // RisingWave container image info
  string image = 1;

  // Whether to create the service monitor resources.
  bool enable_default_service_monitor = 2;

  // Specs for RW state storage
  StateStoreSpec state_store = 3;

  // Specs for RW meta storage
  MetaStoreSpec meta_store_spec = 4;

  // Information about where to retrieve RisingWave configurations
  Config config = 5;

  // How RW will expose its services.
  k8s.ServiceType frontend_service_type = 6;

  // Specs of RW components
  ComponentsSpec components = 7;

  // Flag to indicate if full kubernetes address should be enabled for
  // components. If enabled, address will be [<pod>.]<service>.<namespace>.svc.
  // Otherwise, it will be [<pod>.]<service>. Enabling this flag on existing
  // RisingWave will cause incompatibility.
  bool enable_full_kubernetes_addr = 8;

  // Flag to control whether to deploy in standalone mode or distributed mode.
  // If standalone mode is used, spec.components will be ignored. Standalone
  // mode can be turned on/off dynamically.
  bool enable_standalone_mode = 9;

  // Flag to control whether to enable embedded serving mode. If enabled, the
  // frontend nodes will be created
  // with embedded serving node enabled, and the compute nodes will serve
  // streaming workload only.
  bool enable_embedded_serving_mode = 10;

  // TODO: CLOUD-3324
  Config compute_config = 11;

  // License key used to load license from secret
  // and unlock premium features of tenants.
  LicenseKey license_key = 12;

  SecretStore secret_store = 13;
}

message StateStoreSpec {
  string data_directory = 1;

  oneof backend {
    StateStoreBackendS3 s3_state_store = 2;
    StateStoreBackendGCS gcs_state_store = 3;
    StateStoreBackendAzblob azblob_state_store = 6;
    StateStoreBackendMemory memory_state_store = 4;
    StateStoreBackendLocalDisk local_disk_state_store = 5;
  }
}

// StateStoreBackendS3 contains spec of a S3 backed RisingWave state store. It's
// only a subset of the RisingWave CRD:
// - We assume cloud agent will handle S3 access through IAM role so credential
// fields are omitted.
// - We assume the cloud agent and the S3 bucket are in the same region so
// region field is omitted.
message StateStoreBackendS3 {
  string bucket = 1;
  string region = 2;
}

// StateStoreBackendGCS contains spec of a GCS backed RisingWave state store.
// It's only a subset of the RisingWave CRD:
// - We assume cloud agent will handle GCS access through web identity so
// credential fields are omitted.
message StateStoreBackendGCS {
  string bucket = 1;
}

message StateStoreBackendAzblob {
  string container = 1;
  string endpoint = 2;
}

message StateStoreBackendMemory {}

message StateStoreBackendLocalDisk {
  string root = 1;
}

message MetaStoreSpec {
  MetaStoreBackendEtcd etcd_backend = 1;
  MetaStoreBackendPostgreSql postgresql_backend = 2;
}

message MetaStoreBackendEtcd {
  string endpoint = 1;
}

message MetaStoreBackendPostgreSql {
  string database = 3;
  string host = 1;
  uint32 port = 2;
  PostgreSqlCredentials credentials = 4;
  map<string, string> options = 5;
}

message PostgreSqlCredentials {
  string secret_name = 1;
  string username_key_ref = 2;
  string password_key_ref = 3;
}

message Config {
  NodeConfig node_config = 1;
}

message NodeConfig {
  NodeConfigurationConfigMap node_configuration_config_map = 1;
}

message NodeConfigurationConfigMap {
  string name = 1;
  string key = 2;
}

message ComponentSpec {
  string log_level = 1;
  repeated NodeGroupSpec node_groups = 2;
}

message PersistentVolumeClaimPartialObjectMeta {
  string name = 1;
}

message PersistentVolumeClaimRetentionPolicy {
  PersistentVolumeClaimRetentionPolicyType when_deleted = 1;
  PersistentVolumeClaimRetentionPolicyType when_scaled = 2;
}

enum PersistentVolumeClaimRetentionPolicyType {
  UNKNOWN = 0;
  DELETE = 1;
  RETAIN = 2;
}

message PersistentVolumeClaim {
  PersistentVolumeClaimPartialObjectMeta metadata = 1;
  k8s.PersistentVolumeClaimSpec spec = 2;
}

message NodeGroupSpec {
  string name = 1;
  uint32 replicas = 2;
  NodeGroupUpgradeStrategy upgrade_strategy = 3;
  NodePodSpec node_pod_spec = 4;
  repeated PersistentVolumeClaim volume_claim_templates = 5;
  PersistentVolumeClaimRetentionPolicy
    persistent_volume_claim_retention_policy = 6;
  NodeConfig node_config = 7;
}

message NodePodSpec {
  repeated k8s.Toleration tolerations = 1;
  k8s.Affinity affinity = 2;
  NodePodContainerSpec container_spec = 3;
  string service_account = 4;
  map<string, string> labels = 5;
  map<string, string> annotations = 6;
  repeated k8s.Volume volumes = 7;
}

message NodePodContainerSpec {
  k8s.ResourceRequirements resources = 1;
  map<string, string> envs = 2;
  repeated k8s.VolumeMount volume_mounts = 3;
}

message NodeGroupUpgradeStrategy {
  UpgradeStrategyType type = 1;
}

enum UpgradeStrategyType {
  UNKNOWN_STRATEGY = 0;
  RECREATE = 1;
  ROLLING_UPDATE = 2;
  IN_PLACE_IF_POSSIBLE = 3;
  IN_PLACE_ONLY = 4;
}

message ComponentsSpec {
  // Specs for meta nodes.
  ComponentSpec meta_spec = 1;

  // Specs for frontend nodes.
  ComponentSpec frontend_spec = 2;

  // Specs for compute nodes.
  ComponentSpec compute_spec = 3;

  // Specs for compactor nodes.
  ComponentSpec compactor_spec = 4;

  // Specs for connector nodes.
  ComponentSpec connector_spec = 5 [deprecated = true];

  // Spec for standalone component.
  StandaloneSpec standalone_component = 6;
}

message StandaloneSpec {
  string log_level = 1;
  uint32 replicas = 2;
  NodeGroupUpgradeStrategy upgrade_strategy = 3;
  NodePodSpec node_pod_spec = 4;
}

enum RisingWaveStatusCode {
  UNKNOWN_RW_STATUS = 0;
  RW_READY = 1;
  RW_NOT_READY = 2;
  RW_UPGRADING = 3;
  RW_WAIT_FOR_OBSERVATION = 4;
}

message RisingWaveStatus {
  RisingWaveStatusCode status_code = 1;
  string state_store_root_path = 2;
}

// ScaleSpec specifies the replica and pod resources for a RW node group
message ScaleSpec {
  uint32 replicas = 1;
  // If present, set pods with the specified resources.
  common.k8s.ResourceRequirements resources = 2;

  // If present, override the existing affinity of the node group.
  k8s.Affinity affinity = 3;
}

message LicenseKey {
  string secret_name = 1;
}

message SecretStore {
  SecretStorePrivateKey private_key = 1;
}

message SecretStorePrivateKey {
  optional string value = 1;
  optional SecretStorePrivateKeySecretReference secret_ref = 2;
}

message SecretStorePrivateKeySecretReference {
  string name = 1;
  string key = 2;
}

enum ComponentType {
  UNKNOWN_COMPONENT = 0;
  META = 1;
  FRONTEND = 2;
  COMPUTE = 3;
  COMPACTOR = 4;
  STANDALONE = 5;
}
