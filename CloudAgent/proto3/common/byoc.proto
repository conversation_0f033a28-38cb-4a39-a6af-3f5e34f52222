syntax = "proto3";

package common.byoc;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/byoc";

import "google/protobuf/duration.proto";

message ModuleOptions {
  string module_path = 1;
  string backend_config_file_name = 2;
  bytes backend_config = 3;
  map<string, string> sensitive_variables = 8;
  string variable_file_name = 4;
  bytes variable_payload = 5;
  // If specified, for each key, in increasing order, replace all occurrences of
  // the key with the mapped environment variable values.
  // If the environment variable is not set, an empty string will be used for
  // replacement.
  map<string, string> backend_config_env_var_overrides = 6;
  map<string, string> taskrunner_env_overrides = 7;
}

message TFInitOptions {
  int32 retry = 1;
  google.protobuf.Duration retry_interval = 2;
}

message ApplyOptions {
  int32 retry = 1;
  google.protobuf.Duration retry_interval = 2;
  google.protobuf.Duration graceful_shutdown_period = 3;
  google.protobuf.Duration lock_expiration_duration = 4;
  TFInitOptions init_options = 5;
}

message PackageOptions {
  string root_path = 1;
  string tf_version_file_path = 2;
  string package_url = 3;
  string package_dest_name = 4;
}

message OutputOptions {
  int32 retry = 1;
  google.protobuf.Duration retry_interval = 2;
  TFInitOptions init_options = 3;
}
