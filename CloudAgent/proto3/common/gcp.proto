syntax = "proto3";

package common.gcp;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/common/gcp";

message SQLInstanceSpec {
  // Immutable. Optional. The name of the resource. Used for creation and
  // acquisition. When unset, the value of `metadata.name` is used as the
  // default.
  string resource_id = 1;
  // The type of the instance. The valid values are:-
  // 'SQL_INSTANCE_TYPE_UNSPECIFIED', 'CLOUD_SQL_INSTANCE',
  // 'ON_PREMISES_INSTANCE' and 'READ_REPLICA_INSTANCE'.
  string instance_type = 2;
  // The MySQL, PostgreSQL or SQL Server (beta) version to use. Supported values
  // include MYSQL_5_6, MYSQL_5_7, MYSQL_8_0, POSTGRES_9_6, POSTGRES_10,
  // POSTGRES_11, POSTGRES_12, POSTGRES_13, POSTGRES_14,
  // SQLSERVER_2017_STANDARD, SQLSERVER_2017_ENTERPRISE, SQLSERVER_2017_EXPRESS,
  // SQLSERVER_2017_WEB. Database Version Policies includes an up-to-date
  // reference of supported versions.
  string database_version = 3;
  // Immutable. The region the instance will sit in. Note, Cloud SQL is not
  // available in all regions. A valid region must be provided to use this
  // resource. If a region is not provided in the resource definition, the
  // provider region will be used instead, but this will be an apply-time error
  // for instances if the provider region is not supported with Cloud SQL. If
  // you choose not to provide the region argument for this resource, make sure
  // you understand this.
  string region = 4;
  // Initial root password. Required for MS SQL Server.
  RootPassword root_password = 5;
  // The settings to use for the database. The configuration is detailed below.
  SQLInstanceSettings settings = 6;
}

message SQLInstanceSettings {
  // -required-
  // The machine type to use. See tiers for more details and supported versions.
  // Postgres supports only shared-core machine types, and custom machine types
  // such as db-custom-2-13312. See the Custom Machine Type Documentation to
  // learn about specifying custom machine types.
  string tier = 1;

  // The size of data disk, in GB. Size of a running instance cannot be reduced
  // but can be increased. The minimum value is 10GB.
  int32 disk_size = 2;

  IPConfiguration ip_configuration = 3;

  // Configuration to protect against accidental instance deletion.
  bool deletion_protection_enabled = 4;

  repeated DatabaseFlag database_flags = 5;
}

message DatabaseFlag {
  string name = 1;
  string value = 2;
}

message RootPassword {
  optional string value = 1;
  optional RootPasswordValueFrom value_from = 2;
}

message RootPasswordValueFrom {
  // Reference to a value with the given key in the given Secret in the
  // resource's namespace.
  SecretKeyRef secretKeyRef = 1;
}

message SecretKeyRef {
  // Key that identifies the value to be extracted.
  string key = 1;
  // Name of the Secret to extract a value from.
  string name = 2;
}

message IPConfiguration {
  bool ipv4_enabled = 1;
  PrivateNetworkRef private_network_ref = 2;
  string allocated_ip_range = 3;
  CloudSQLSSLMode ssl_mode = 4;
}

enum CloudSQLSSLMode {
  CloudSQLSSLMode_UNKNOWN = 0;
  CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED = 1;
  CloudSQLSSLMode_ENCRYPTED_ONLY = 2;
  CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED = 3;
}

message PrivateNetworkRef {
  // Allowed value: The `selfLink` field of a `ComputeNetwork` resource.
  string external = 1;
}

enum SQLInstanceStatus {
  UNKNOWN = 0;
  UPDATING = 1;
  UP_TO_DATE = 2;
  STOPPED = 3;
}
