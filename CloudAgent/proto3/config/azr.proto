syntax = "proto3";

package config.azr;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/config/azr";

// Config contains Azure configuration data for CloudAgent to initialize
// authetication to Azure services
message Config {
  string subscription_id = 1;
  string location = 2;
  // Name of the resource group holding all the provisioned resources
  string resource_group = 3;
  string oidc_issuer = 6;
  oneof auth {
    AKSWorkloadIdentity aks_workload_identity = 4;

    StaticCredAuth static_creds = 5;
  }

  // Name of the storage account used to read/write to Azure blob
  string storage_account_name = 7 [deprecated = true];
}

// Auth option to use static credential to access Azure APIs.
// This is intended for test usage only. Do NOT use it in production
message StaticCredAuth {
  string client_secret = 1;
  string client_id = 2;
  string tenant_id = 3;
}

// Auth option to use AKS workload identity to access Azure APIs
// https://learn.microsoft.com/en-us/azure/aks/workload-identity-overview?tabs=dotnet
message AKSWorkloadIdentity {}
