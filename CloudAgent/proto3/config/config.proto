syntax = "proto3";

package config;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/config";

import "config/aws.proto";
import "config/gcp.proto";
import "config/azr.proto";
import "config/k8s.proto";
import "config/tls.proto";
import "config/local.proto";
import "config/telemetry.proto";

// Config contains configuration data for CloudAgent to initialize the service.
message Config {
  // Configuration for K8s cluster authentication.
  k8s.Config k8s_config = 1;
  // Configuration for cloud platform authentication.
  oneof cloud_provider_config {
    aws.Config aws_config = 2;
    gcp.Config gcp_config = 3;
    azr.Config azr_config = 7;
    local.Config local_config = 5;
  }
  tls.Config tls_config = 4;
  telemetry.Config telemetry_config = 6;
}
