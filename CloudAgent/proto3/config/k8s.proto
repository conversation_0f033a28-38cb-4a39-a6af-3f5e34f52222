syntax = "proto3";

package config.k8s;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/config/k8s";

import "common/k8s.proto";

message Config {
  string cluster_id = 1;

  string endpoint = 6;
  // Base64 encoded CA cert for K8S API server.
  string ca_certificate_base64 = 7;

  oneof auth {
    // Use static token of a service account to auth.
    StaticTokenAuth static_token_auth = 2;
    // In cluster auth, require the service account running agent to be
    // binded with required clusterrole
    InClusterAuth in_cluster_auth = 3;
  }

  TaskConfig task_config = 4;
  repeated string allow_helm_charts = 5;
}

message StaticTokenAuth {
  string master_url = 1;
  string token = 2;
}

message InClusterAuth {}

enum PullPolicy {
  UNKNOWN = 0;
  // The image registry of the task image will always be queried.
  PULL_ALWAYS = 1;
  // The task image is pulled only if it is not already present locally.
  PULL_IF_NOT_PRESENT = 2;
}

message TaskConfig {
  string image = 1;
  string service_account = 2;
  string namespace = 3;
  PullPolicy pull_policy = 4;
  // Toleration settings for the Task pod.
  repeated .common.k8s.Toleration tolerations = 5;
  // Affinity settings for the Task pod.
  .common.k8s.Affinity affinity = 6;
  map<string, string> labels = 7;
}
