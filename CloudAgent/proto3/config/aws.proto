syntax = "proto3";

package config.aws;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/config/aws";

// Config contains AWS configuration data for CloudAgent to initialize
// authetication to AWS services
message Config {
  // AWS account id
  string account_id = 1;
  // The region of the AWS services agent uses for regional services
  string region = 2;
  // OIDC provider of the EKS cluster agent is deployed to. Only required for
  // IAM related resources provisioning.
  string oidc_provider = 3;

  oneof auth {
    // AWS access key + secret key auth
    StaticCredAuth static_creds = 4;
    // EKS web identity auth -- requires the agent to be running by a K8s
    // service account configured to assume an IAM role.
    EKSWebIdendity eks_web_identity = 5;
  }

  string vpc_id = 6;

  // default tags for AWS resources, the key and the value of a tag is divided
  // by the first colon.
  repeated string default_tags = 7;
}

// Auth option to use static access key to access AWS APIs.
// This is intended for test usage only. Do NOT use it in production
message StaticCredAuth {
  string access_key_id = 1;
  string secret_access_key = 2;
}

// AWS auth option to use EKS OIDC provided identity to access AWS APIs
// The identity token should be retrieved through enviroment variables and this
// process should be handled automatically by AWS SDK.
message EKSWebIdendity {}
