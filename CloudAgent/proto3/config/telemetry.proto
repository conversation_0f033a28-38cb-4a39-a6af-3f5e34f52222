syntax = "proto3";

package config.telemetry;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/config/telemetry";

// Config contains AWS configuration data for CloudAgent to initialize
// authetication to AWS services
message Config {
  oneof metrics_config {
    AWSManagedPrometheusConfig amp_config = 1;
    GoogleManagedPrometheusConfig gmp_config = 2;
    LocalManagedPrometheusConfig local_prometheus_config = 3;
    AzureManagedPrometheusConfig azmp_config = 4;
  }
}

// We assume the AMP is provisioned in the same AWS account/region of the
// cluster.
message AWSManagedPrometheusConfig {
  string workspace_id = 1;
}

// We assume the GMP is provisioned in the same GCP project of the cluster.
message GoogleManagedPrometheusConfig {}

// We assume the AZP is provisioned in the same GCP project of the cluster.
message AzureManagedPrometheusConfig {
  string query_endpoint = 1;
}

// Will request the local Prometheus service in the cluster.
message LocalManagedPrometheusConfig {
  string url = 1;
}
