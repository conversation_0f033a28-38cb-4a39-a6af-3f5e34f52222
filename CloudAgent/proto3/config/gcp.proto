syntax = "proto3";

package config.gcp;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/config/gcp";

// Config contains GCP configuration data for CloudAgent to initialize
// authetication to GCP services
message Config {
  // GCP projec id
  string project_id = 1;

  oneof auth {
    // Auth with static Google service account key.
    StaticCredAuth static_creds = 2;
    // GKE web identity auth -- requires the agent to be running by a K8s
    // service account configured to assume Google service account.
    GKEWebIdendity gke_web_identity = 3;
  }

  // The region of the GCP services agent uses for regional services
  string region = 4;
  // The self-link to the tenant vpc
  string tenant_vpc = 5;
}

// Auth option to use static credential to access Google APIs.
// This is intended for test usage only. Do NOT use it in production
message StaticCredAuth {
  string google_application_credentials = 1;
}

// Auth option to use GKE workload identity to access Google APIs
// The identity token should be retrieved through enviroment variables and this
// process should be handled automatically by Google APIs SDK.
message GKEWebIdendity {}
