syntax = "proto3";

package services.byoc;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/byoc";

import "common/creation.proto";
import "common/resource.proto";
import "common/byoc.proto";
import "common/k8s.proto";

service ByocResourceManager {
  // CreateUpdateByocTask creates a task to update the byoc terraform package.
  // Caller should call the task manager service to manage the created task.
  // Expected a CREATED status on success.
  rpc CreateApplyByocModuleTask(CreateApplyByocModuleTaskRequest)
      returns (CreateApplyByocModuleTaskResponse) {}

  rpc CreateRetrieveByocModuleOutputTask(
      CreateRetrieveByocModuleOutputTaskRequest)
      returns (CreateRetrieveByocModuleOutputTaskResponse) {}
}

message CreateApplyByocModuleTaskRequest {
  .common.resource.Meta resource_meta = 1;
  .common.byoc.ModuleOptions module_options = 2;
  .common.byoc.ApplyOptions apply_options = 3;
  .common.byoc.PackageOptions package_options = 4;

  repeated .common.k8s.Toleration task_tolerations = 5;
  .common.k8s.Affinity task_affinity = 6;
}

message CreateApplyByocModuleTaskResponse {
  .common.resource.creation.Status status = 1;
}

message CreateRetrieveByocModuleOutputTaskRequest {
  .common.resource.Meta resource_meta = 1;
  string output_key = 2;
  .common.byoc.ModuleOptions module_options = 3;
  .common.byoc.OutputOptions output_options = 4;
  .common.byoc.PackageOptions package_options = 5;

  repeated .common.k8s.Toleration task_tolerations = 6;
  .common.k8s.Affinity task_affinity = 7;
}

message CreateRetrieveByocModuleOutputTaskResponse {
  .common.resource.creation.Status status = 1;
}
