syntax = "proto3";

package services.data;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/data";

import "common/creation.proto";
import "common/resource.proto";

// Note: Directory deletion tasks are not namespaced, the namespace value will
// be ignored.
message CreateDataDirectoryDeletionTaskRequest {
  .common.resource.Meta resource_meta = 1;
  string directory_name = 2;
  string bucket_name = 3;
  string region = 4 [ deprecated = true ];

  // This is an Azure specific field to specify the storage account the
  // bucket(container) belongs to.
  string storage_account_name = 5;
}

message CreateDataDirectoryDeletionTaskResponse {
  .common.resource.creation.Status status = 1;
}

// Note: Directory clone tasks are not namespaced, the namespace value will
// be ignored.
message CreateDataDirectoryCloneTaskRequest {
  .common.resource.Meta resource_meta = 1;
  string source_directory_name = 2;
  string source_bucket_name = 3;
  string destination_directory_name = 4;
  string destination_bucket_name = 5;

  // This is an Azure specific field to specify the storage account the
  // bucket(container) belongs to.
  string source_storage_account_name = 6;
  string destination_storage_account_name = 7;

  // The object cursor to resume the cloning tasks
  string cursor = 8;

  // The number of objects to clone in the task
  int32 clone_size = 9;
}

message CreateDataDirectoryCloneTaskResponse {
  .common.resource.creation.Status status = 1;
}

message CreateSimpleDataReplicationTaskRequest {
  .common.resource.Meta resource_meta = 1;
  string source_bucket = 2;
  string source_directory = 3;
  string sink_bucket = 4;
  string sink_directory = 5;
}

message CreateSimpleDataReplicationTaskResponse {
  .common.resource.creation.Status status = 1;
}

message GetManifestRequest {
  string bucket = 1;
  string backup_dir = 2;

  // This is an Azure specific field to specify the storage account the
  // bucket(container) belongs to.
  string storage_account_name = 3;
}

message GetManifestResponse { string manifest_json = 1; }
