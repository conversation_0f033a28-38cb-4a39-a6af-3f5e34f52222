syntax = "proto3";

package services.telemetry.prometheus;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus";

service Prometheus {
  // Proxy sends the prometheus request to cluster's prometheus compatible
  // endpoint and returns the response payload and code.
  rpc Proxy(ProxyRequest) returns (ProxyResponse) {}
  // Scrape will scrape the metrics endpoint under user namespace.
  rpc Scrape(ScrapeRequest) returns (ScrapeResponse) {}
}

message ProxyRequest {
  HTTPMethod http_method = 1;
  Endpoint endpoint = 2;
  bytes payload = 3;
  EndpointParams endpoint_params = 4;
}

message ProxyResponse {
  uint32 status_code = 1;
  bytes payload = 2;
}

// HTTP method of the Promethes API call.
enum HTTPMethod {
  METHOD_UNSPECIFIED = 0;
  GET = 1;
  POST = 2;
}

// Endpoint of the Promethes API call.
// Reference: https://prometheus.io/docs/prometheus/latest/querying/api/
enum Endpoint {
  ENDPOINT_UNSPECIFIED = 0;
  QUERY = 1;
  QUERY_RANGE = 2;
  SERIES = 3;
  LABEL_VALUES = 4;
  LABELS = 5;
}

message LabelValuesEndpointParams {
  string label_name = 1;
}

message EndpointParams {
  oneof params {
    LabelValuesEndpointParams label_values_params = 1;
  }
}

message ScrapeRequest {
  string namespace = 1;
  // If set, the scraping request should accept the specified encoding method
  // the request header.
  string accept_encoding_header = 2;
  // Add query parameters to filter metrics
  // Note that `include` and `exclude` should not be used together.
  repeated string include = 3;
  repeated string exclude = 4;

  uint32 max_response_size = 5;
}

message ScrapeResponse {
  // Status code.
  uint32 status_code = 1 [deprecated = true];
  // Metrics bytes data.
  bytes payload = 2;
  // The format of metrics data, need to negotiate the encoder based on the
  // content type.
  string content_type_header = 3;

  // If set, the payload is encoded in the specified encoding method.
  string content_encoding_header = 4;
}
