syntax = "proto3";

package services.task;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/task";

import "common/task.proto";
import "common/resource.proto";
import "common/deletion.proto";

service TaskManager {
  rpc GetTaskStatus(GetTaskStatusRequest) returns (GetTaskStatusResponse) {}
  rpc CleanupTask(CleanupTaskRequest) returns (CleanupTaskResponse) {}
}

// Note: Tasks are not namespaced, the namespace value will be ignored.
message GetTaskStatusRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetTaskStatusResponse {
  .common.resource.task.Status status = 1;
}

// Note: Tasks are not namespaced, the namespace value will be ignored.
message CleanupTaskRequest {
  .common.resource.Meta resource_meta = 1;
}

message CleanupTaskResponse {
  .common.resource.deletion.Status status = 1;
}
