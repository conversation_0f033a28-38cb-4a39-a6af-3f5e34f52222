syntax = "proto3";

package services.gcp;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/gcp";

import "common/creation.proto";
import "common/deletion.proto";
import "common/update.proto";
import "common/resource.proto";
import "common/k8s.proto";
import "common/gcp.proto";
import "services/common/data.proto";

service GcpResourceManager {
  // CreateDataDirectoryDeletionTask creates a task for GCP folder deletion.
  // Caller should call the task manager service to manage the created task.
  // Expected a CREATED status on success.
  rpc CreateDataDirectoryDeletionTask(
      data.CreateDataDirectoryDeletionTaskRequest)
      returns (data.CreateDataDirectoryDeletionTaskResponse) {}

  rpc CreateDataDirectoryCloneTask(
  data.CreateDataDirectoryCloneTaskRequest)
      returns (data.CreateDataDirectoryCloneTaskResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateIAMPolicyRoleBinding(CreateIAMPolicyRoleBindingRequest)
      returns (CreateIAMPolicyRoleBindingResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteIAMPolicyRoleBinding(DeleteIAMPolicyRoleBindingRequest)
      returns (DeleteIAMPolicyRoleBindingResponse) {}
  rpc GetIAMPolicyRoleBinding(GetIAMPolicyRoleBindingRequest)
      returns (GetIAMPolicyRoleBindingResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateIAMPolicyKSABinding(CreateIAMPolicyKSABindingRequest)
      returns (CreateIAMPolicyKSABindingResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteIAMPolicyKSABinding(DeleteIAMPolicyKSABindingRequest)
      returns (DeleteIAMPolicyKSABindingResponse) {}
  rpc GetIAMPolicyKSABinding(GetIAMPolicyKSABindingRequest)
      returns (GetIAMPolicyKSABindingResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateIAMServiceAccount(CreateIAMServiceAccountRequest)
      returns (CreateIAMServiceAccountResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteIAMServiceAccount(DeleteIAMServiceAccountRequest)
      returns (DeleteIAMServiceAccountResponse) {}
  rpc GetIAMServiceAccount(GetIAMServiceAccountRequest)
      returns (GetIAMServiceAccountResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateIPAddress(CreateIPAddressRequest)
      returns (CreateIPAddressResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteIPAddress(DeleteIPAddressRequest)
      returns (DeleteIPAddressResponse) {}
  rpc GetIPAddress(GetIPAddressRequest) returns (GetIPAddressResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreatePrivateServiceConnectEndpoint(
      CreatePrivateServiceConnectEndpointRequest)
      returns (CreatePrivateServiceConnectEndpointResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeletePrivateServiceConnectEndpoint(
      DeletePrivateServiceConnectEndpointRequest)
      returns (DeletePrivateServiceConnectEndpointResponse) {}
  rpc GetPrivateServiceConnectEndpoint(GetPrivateServiceConnectEndpointRequest)
      returns (GetPrivateServiceConnectEndpointResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateSQLInstance(CreateSQLInstanceRequest)
      returns (CreateSQLInstanceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteSQLInstance(DeleteSQLInstanceRequest)
      returns (DeleteSQLInstanceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc StartSQLInstance(StartSQLInstanceRequest)
      returns (StartSQLInstanceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc StopSQLInstance(StopSQLInstanceRequest)
      returns (StopSQLInstanceResponse) {}

  rpc GetSQLInstance(GetSQLInstanceRequest)
      returns (GetSQLInstanceResponse) {}

  rpc GetManifest(data.GetManifestRequest)
      returns (data.GetManifestResponse) {}
}

// Request for creating a rolebinding IAM policy. Empty policy is not accepted.
// So it's required to have at least one of the access options set.
message CreateIAMPolicyRoleBindingRequest {
  // The resource meta must meet the following restrictions:
  //- Name: as defined in RFC 1123. This is only used in k8 metadata name. i.e.:
  //    1. contain no more than 253 characters
  //    2. contain only lowercase alphanumeric characters, '-' or '.'
  //    3. start with an alphanumeric character
  //    4. end with an alphanumeric character
  // - Namespace:
  //    1. Namespace is REQUIRED
  //    2. Must be valid RFC 1123 Label Names, i.e.:
  //      a. contain at most 63 characters
  //      b. contain only lowercase alphanumeric characters or '-'
  //      c. start with an alphanumeric character
  //      d. end with an alphanumeric character
  .common.resource.Meta resource_meta = 1;

  // Name of the IAMServiceAccount to be attached. Note that this is just the
  // name and not the entire account email. E.g, for the account
  // <EMAIL>, IAM_service_account_name
  // will be "testaccount"
  string IAM_service_account_name = 2;

  // Which roles to bind.
  RoleBinding role_binding = 3;
}

message CreateIAMPolicyRoleBindingResponse {
  .common.resource.creation.Status status = 1;
}

message GetIAMPolicyRoleBindingRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetIAMPolicyRoleBindingResponse {
  .common.resource.Status status = 1;
}

message DeleteIAMPolicyRoleBindingRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteIAMPolicyRoleBindingResponse {
  .common.resource.deletion.Status status = 1;
}

// Request for creating a Kubernetes Service Account Binding IAM policy. Empty
// policy is not accepted. So it's required to have at least one of the access
// options set.
message CreateIAMPolicyKSABindingRequest {
  // The resource meta must meet the following restrictions:
  //- Name: as defined in RFC 1123. This is only used in k8 metadata name. i.e.:
  //    1. contain no more than 253 characters
  //    2. contain only lowercase alphanumeric characters, '-' or '.'
  //    3. start with an alphanumeric character
  //    4. end with an alphanumeric character
  // - Namespace:
  //    1. Namespace is REQUIRED
  //    2. Must be valid RFC 1123 Label Names, i.e.:
  //      a. contain at most 63 characters
  //      b. contain only lowercase alphanumeric characters or '-'
  //      c. start with an alphanumeric character
  //      d. end with an alphanumeric character
  .common.resource.Meta resource_meta = 1;

  // Name of the IAMServiceAccount to be attached. Note that this is just the
  // name and not the entire account email. E.g, for the account
  // <EMAIL>, IAM_service_account_name
  // will be "testaccount"
  string IAM_service_account_name = 2;

  // Which kubernetes service account to bind.
  .common.k8s.ServiceAccount service_account = 3;
}

message CreateIAMPolicyKSABindingResponse {
  .common.resource.creation.Status status = 1;
}

message GetIAMPolicyKSABindingRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetIAMPolicyKSABindingResponse {
  .common.resource.Status status = 1;
}

message DeleteIAMPolicyKSABindingRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteIAMPolicyKSABindingResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateIAMServiceAccountRequest {
  // The resource meta must meet the following restrictions:
  // - Name:
  //    1. contains between 6 and 30 characters
  //    2. contain only lowercase alphanumeric characters and '-'
  //    3. start with an alphanumeric character
  //    4. end with an alphanumeric character
  // - Namespace:
  //    1. Namespace is REQUIRED
  //    2. Must be valid RFC 1123 Label Names, i.e.:
  //      a. contain at most 63 characters
  //      b. contain only lowercase alphanumeric characters or '-'
  //      c. start with an alphanumeric character
  //      d. end with an alphanumeric character
  .common.resource.Meta resource_meta = 1;
}

message CreateIAMServiceAccountResponse {
  .common.resource.creation.Status status = 1;
}

message GetIAMServiceAccountRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetIAMServiceAccountResponse {
  .common.resource.Status status = 1;
  // The e-mail address of the service account. This value should be referenced
  // from any google_iam_policy data sources that would grant the service
  // account privileges.
  string account_email = 2;
}

message DeleteIAMServiceAccountRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteIAMServiceAccountResponse {
  .common.resource.deletion.Status status = 1;
}

// RoleBinding specifies the type of role to bind to an IAM Service Account.
// Only GCS role is currently supported
message RoleBinding {
  oneof access_option {
    // If specified, the create IAM policy will grant access to the specified
    // GCS location.
    GCSAccessOption gcs_access_option = 1;
  }
}

// GCSAccessOption specifies a GCS directory a GCP IAM Policy should give
// access to. It will give access to all operations towards the directory.
message GCSAccessOption {
  // GCS Bucket name.
  string bucket = 1;
  // The directory of the GCS bucket.
  string dir = 2 [deprecated = true];
  // List of directories of the S3 bucket.
  repeated string dirs = 4;
  // GCS Role
  GCSRole role = 3;
}

enum GCSRole {
  UNKNOWN = 0;
  OBJECT_ADMIN = 1;
  OBJECT_VIEWER = 2;
}

message CreateIPAddressRequest {
  .common.resource.Meta resource_meta = 1;
  // self-link to the subnet in which the ip will be provisioned
  string ip_subnet = 2;
}

message CreateIPAddressResponse {
  .common.resource.creation.Status status = 1;
}

message GetIPAddressRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetIPAddressResponse {
  .common.resource.Status status = 1;
  string ip_selflink = 2;
  string ip_address = 3;
}

message DeleteIPAddressRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteIPAddressResponse {
  .common.resource.deletion.Status status = 1;
}

message CreatePrivateServiceConnectEndpointRequest {
  .common.resource.Meta resource_meta = 1;
  // self-link of private service conenct ip address
  string private_service_ip = 2;
  // target self-link
  string target = 3;
  map<string, string> extra_labels = 4;
}

message CreatePrivateServiceConnectEndpointResponse {
  .common.resource.creation.Status status = 1;
}

message GetPrivateServiceConnectEndpointRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetPrivateServiceConnectEndpointResponse {
  .common.resource.Status status = 1;
  // The PSC connection status of the PSC Forwarding Rule. Possible values:
  // 'STATUS_UNSPECIFIED', 'PENDING', 'ACCEPTED', 'REJECTED', 'CLOSED'.
  PscStatus psc_status = 2;
  string ip_address = 3 [deprecated = true];
}

enum PscStatus {
  STATUS_UNSPECIFIED = 0;
  PENDING = 1;
  ACCEPTED = 2;
  REJECTED = 3;
  CLOSED = 4;
}

message DeletePrivateServiceConnectEndpointRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeletePrivateServiceConnectEndpointResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateSQLInstanceRequest {
  .common.resource.Meta resource_meta = 1;
  .common.gcp.SQLInstanceSpec spec = 2;
  map<string, string> labels = 3;
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
message CreateSQLInstanceResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteSQLInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, NOT_FOUND.
message DeleteSQLInstanceResponse {
  .common.resource.deletion.Status status = 1;
}

message StartSQLInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
message StartSQLInstanceResponse {
  .common.resource.update.Status status = 1;
}

message StopSQLInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
message StopSQLInstanceResponse {
  .common.resource.update.Status status = 1;
}

message GetSQLInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetSQLInstanceResponse {
  .common.resource.Status status = 1;
  optional string private_ip_address = 2;
  .common.gcp.SQLInstanceStatus instance_status = 3;
}

