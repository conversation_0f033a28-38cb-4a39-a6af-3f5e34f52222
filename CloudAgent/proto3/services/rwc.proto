syntax = "proto3";

package services.rwc;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/rwc";

import "common/creation.proto";
import "common/resource.proto";
import "common/k8s.proto";

service RisingwaveControl {
  // MetaNodeBackup creates a task for meta backup creation.
  // Caller should call the task manager service to manage the created task.
  // Expected a CREATED status on success.
  rpc MetaNodeBackup(MetaNodeBackupRequest) returns (MetaNodeBackupResponse) {}

  rpc ValidateSource(ValidateSourceRequest) returns (ValidateSourceResponse) {}

  rpc GetClusterInfo(GetClusterInfoRequest) returns (GetClusterInfoResponse) {}

  rpc CordonWorkers(CordonWorkersRequest) returns (CordonWorkersResponse) {}

  rpc ResizeWorkers(ResizeWorkersRequest) returns (ResizeWorkersResponse) {}

  rpc DeleteWorkers(DeleteWorkersRequest) returns (DeleteWorkersResponse) {}

  rpc DeleteSnapshot(DeleteSnapshotRequest) returns (DeleteSnapshotResponse) {}

  // RestoreMeta creates a task for restoring a snapshot.
  // Caller should call the task manager service to manage the created task.
  // Expected a CREATED status on success.
  rpc RestoreMeta(RestoreMetaRequest) returns (RestoreMetaResponse) {}

  rpc VacuumEtcdMeta(VacuumEtcdMetaRequest) returns (VacuumEtcdMetaResponse) {}

  // RW Diagnosis Report
  rpc GenDiagnosisReport(GenDiagnosisReportRequest)
      returns (GenDiagnosisReportResponse) {}
  rpc GenDiagnosisReportStream(GenDiagnosisReportStreamRequest)
      returns (stream GenDiagnosisReportStreamResponse) {}

  rpc MetaMigration(MetaMigrationRequest) returns (MetaMigrationResponse) {}

  rpc FetchKafkaTopic(FetchKafkaTopicRequest)
      returns (FetchKafkaTopicResponse) {}
  rpc FetchKafkaMessage(FetchKafkaMessageRequest)
      returns (FetchKafkaMessageResponse) {}
  rpc FetchPostgresTable(FetchPostgresTableRequest)
      returns (FetchPostgresTableResponse) {}
  rpc FetchSourceSchema(FetchSourceSchemaRequest)
      returns (FetchSourceSchemaResponse) {}
}

// Note: Meta backup tasks are not namespaced, the namespace value will be
// ignored.
message MetaNodeBackupRequest {
  .common.resource.Meta resource_meta = 1;
  string rw_name = 2;
  string rw_namespace = 3;
}

message MetaNodeBackupResponse {
  .common.resource.creation.Status status = 1;
}

message ValidateSourceRequest {
  string rw_name = 1;
  string rw_namespace = 2;
  // props field for `risectl meta validate-source --props <props>`
  // it contains the properties of the source creation.
  string props = 3;
}

message ValidateSourceResponse {
  // this field is set when validate source cmd failed. and it is set to empty
  // if no error.
  string error_message = 1;
}

message GetClusterInfoRequest {
  string rw_name = 1;
  string rw_namespace = 2;
}

message GetClusterInfoResponse {
  string output = 1;
}

message CordonWorkersRequest {
  string rw_name = 1;
  string rw_namespace = 2;
  repeated string work_ids = 3;
}

message CordonWorkersResponse {}

message ResizeWorkersRequest {
  string rw_name = 1;
  string rw_namespace = 2;
  repeated string added_work_ids = 3;
  repeated string deleting_work_ids = 4;
}

message ResizeWorkersResponse {}

message DeleteWorkersRequest {
  string rw_name = 1;
  string rw_namespace = 2;
  repeated string work_ids = 3;
}

message DeleteWorkersResponse {}

message DeleteSnapshotRequest {
  string rw_name = 1;
  string rw_namespace = 2;
  int64 snapshot_id = 3;
  string rw_version = 4;
}

message DeleteSnapshotResponse {}

message RestoreMetaRequestEtcd {
  string etcd_endpoints = 1;
  // ignore if allowNoneAuthentication is enabled.
  bool etcd_auth = 2;
  // ignore if allowNoneAuthentication is enabled.
  string etcd_username = 3;
  // ignore if allowNoneAuthentication is enabled.
  string etcd_password = 4;
}

message RestoreMetaRequestSql {
  string sql_endpoint = 1;
}

message RestoreMetaRequest {
  // the name will be the name of the Job object
  // the namespace where the restore process will be hosted.
  // the namespace should be where the service account object is hosted.
  .common.resource.Meta resource_meta = 1;
  // service_account is used to authenticate with the storage service (e.g. s3).
  string service_account = 2;
  // rw_image_tag is for starting the restore process. this is needed to
  // guarantee the grpc payload is compatible.
  string rw_image_tag = 3;
  // snapshot id in the kernel
  int64 meta_snapshot_id = 4;
  // valid values: etcd, sql
  string meta_store_type = 5;
  string backup_storage_url = 6;
  string backup_storage_dir = 7;
  string hummock_storage_url = 8;
  string hummock_storage_dir = 9;

  oneof metastore_config {
    RestoreMetaRequestEtcd etcd_config = 10;
    RestoreMetaRequestSql sql_config = 11;
  }

  map<string, string> envs = 14;
}

message RestoreMetaResponse {
  .common.resource.creation.Status status = 1;
}

message VacuumEtcdMetaRequest {
  string pod_name = 1;
  string pod_namespace = 2;
  string etcd_username = 3;
  string etcd_password = 4;
}

message VacuumEtcdMetaResponse {}

// RW Diagnosis Report
message GenDiagnosisReportRequest {
  // Access the corresponding RW tenant via static service name + tenant
  // namespace
  string service_name = 2;
  string namespace = 1;
}

message GenDiagnosisReportResponse {
  string report = 1;
}

message GenDiagnosisReportStreamRequest {
  // Access the corresponding RW tenant via static service name + tenant
  // namespace
  string namespace = 1;
  string service_name = 2;
  bool gzip_compressed = 3;
}

message GenDiagnosisReportStreamResponse {
  bytes report_chunk = 1;
}

message MetaMigrationRequest {
  .common.resource.Meta resource_meta = 1;
  string rw_name = 2;
  string rw_namespace = 3;
  string etcd_endpoints = 4;
  string sql_endpoint = 5;

  string task_image = 6;
  .common.k8s.ResourceRequirements task_resources = 7;
  repeated .common.k8s.Toleration task_tolerations = 8;
  .common.k8s.Affinity task_affinity = 9;
}

message MetaMigrationResponse {
  .common.resource.creation.Status status = 1;
}

enum KafkaSecurityProtocol {
  PROTOCOL_UNSPECIFIED = 0;
  SASL_PLAINTEXT = 1;
  SASL_SSL = 2;
  SSL = 3;
}

enum KafkaSaslMechanism {
  MECHANISM_UNSPECIFIED = 0;
  PLAIN = 1;
  SCRAM_SHA_256 = 2;
  SCRAM_SHA_512 = 3;
}

message KafkaConfig {
  string server = 1;
  optional KafkaSecurityProtocol security_protocol = 2;
  optional KafkaSaslMechanism sasl_mechanism = 3;
  optional string sasl_username = 4;
  optional string sasl_password = 5;
  optional string ca_certificate = 6;
}

message FetchKafkaTopicRequest {
  KafkaConfig kafka = 1;
}

message FetchKafkaTopicResponse {
  repeated string topics = 1;
}

message FetchKafkaMessageRequest {
  KafkaConfig kafka = 1;
  string topic = 2;
}

message FetchKafkaMessageResponse {
  bytes key = 1;
  bytes value = 2;
}

enum PostgresSslMode {
  SSL_MODE_UNSPECIFIED = 0;
  DISABLED = 1;
  PREFERRED = 2;
  REQUIRED = 3;
  VERIFY_CA = 4;
  VERIFY_FULL = 5;
}

message PostgresConfig {
  string hostname = 1;
  int32 port = 2;
  string username = 3;
  string password = 4;
  string database = 5;
  PostgresSslMode ssl_mode = 6;
}

message FetchPostgresTableRequest {
  PostgresConfig postgres = 1;
}

message FetchPostgresTableResponse {
  repeated string tables = 1;
}

enum SchemaFormat {
  FORMAT_UNSPECIFIED = 0;
  AVRO = 1;
  PROTOBUF = 2;
  JSON = 3;
}

enum SchemaLocation {
  LOCATION_UNSPECIFIED = 0;
  WEB_LOCATION = 1;
  SCHEMA_REGISTRY = 2;
  S3 = 3;
}

message SchemaSchemaRegistryConfig {
  string topic = 1;
  optional string username = 2;
  optional string password = 3;
}

message SchemaS3Config {
  string region = 1;
  optional string accessKeyId = 2;
  optional string secretAccessKey = 3;
}

message FetchSourceSchemaRequest {
  SchemaFormat format = 1;
  SchemaLocation location = 2;
  string url = 3;
  optional SchemaSchemaRegistryConfig schema_registry = 4;
  optional SchemaS3Config s3 = 5;
}

message FetchSourceSchemaResponse {
  repeated RawSchemaFile files = 1;
}

message RawSchemaFile {
  bytes content = 1;
  string name = 2;
}
