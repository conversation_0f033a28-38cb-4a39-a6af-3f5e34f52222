syntax = "proto3";

package services.azr;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/azr";

import "common/creation.proto";
import "common/deletion.proto";
import "common/update.proto";
import "common/resource.proto";
import "common/k8s.proto";
import "common/azr.proto";
import "services/common/data.proto";

service AzrResourceManager {
  // Expected a SCHEDULED status on success.
  rpc CreateUserAssignedIdentity(CreateUserAssignedIdentityRequest)
      returns (CreateUserAssignedIdentityResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteUserAssignedIdentity(DeleteUserAssignedIdentityRequest)
      returns (DeleteUserAssignedIdentityResponse) {}
  rpc GetUserAssignedIdentity(GetUserAssignedIdentityRequest)
      returns (GetUserAssignedIdentityResponse) {}

  // CreateDataDirectoryDeletionTask creates a task for AZR folder deletion.
  // Caller should call the task manager service to manage the created task.
  // Expected a CREATED status on success.
  rpc CreateDataDirectoryDeletionTask(
  data.CreateDataDirectoryDeletionTaskRequest)
      returns (data.CreateDataDirectoryDeletionTaskResponse) {}

  rpc CreateDataDirectoryCloneTask(
  data.CreateDataDirectoryCloneTaskRequest)
      returns (data.CreateDataDirectoryCloneTaskResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateFederatedIdentityCredential(
      CreateFederatedIdentityCredentialRequest)
      returns (CreateFederatedIdentityCredentialResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteFederatedIdentityCredential(
      DeleteFederatedIdentityCredentialRequest)
      returns (DeleteFederatedIdentityCredentialResponse) {}
  rpc GetFederatedIdentityCredential(GetFederatedIdentityCredentialRequest)
      returns (GetFederatedIdentityCredentialResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateRoleAssignment(CreateRoleAssignmentRequest)
      returns (CreateRoleAssignmentResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteRoleAssignment(DeleteRoleAssignmentRequest)
      returns (DeleteRoleAssignmentResponse) {}
  rpc GetRoleAssignment(GetRoleAssignmentRequest)
      returns (GetRoleAssignmentResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreatePrivateEndpoint(CreatePrivateEndpointRequest)
      returns (CreatePrivateEndpointResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeletePrivateEndpoint(DeletePrivateEndpointRequest)
      returns (DeletePrivateEndpointResponse) {}
  rpc GetPrivateEndpoint(GetPrivateEndpointRequest)
      returns (GetPrivateEndpointResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreatePGServer(CreatePGServerRequest)
      returns (CreatePGServerResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeletePGServer(DeletePGServerRequest)
      returns (DeletePGServerResponse) {}
  // Expected a SCHEDULED status on success.
  rpc StartPGServer(StartPGServerRequest)
      returns (StartPGServerResponse) {}
  // Expected a SCHEDULED status on success.
  rpc StopPGServer(StopPGServerRequest)
      returns (StopPGServerResponse) {}
  rpc GetPGServer(GetPGServerRequest)
      returns (GetPGServerResponse) {}

  rpc GetManifest(data.GetManifestRequest)
      returns (data.GetManifestResponse) {}
}

message CreateUserAssignedIdentityRequest {
  // The name of the resource will be the name of the provisoned user assigned
  // identity.
  // The name length should be between 3 to 128 chars.
  // Reference:
  // https://github.com/hashicorp/terraform-provider-azurerm/blob/cd6d8e760903c06b4d4246090b54b0873e028ebb/internal/services/managedidentity/user_assigned_identity_data_source.go#L33
  .common.resource.Meta resource_meta = 1;
}

message CreateUserAssignedIdentityResponse {
  .common.resource.creation.Status status = 1;
}

message GetUserAssignedIdentityRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetUserAssignedIdentityResponse {
  .common.resource.Status status = 1;

  string principal_id = 2;
  string client_id = 3;
}

message DeleteUserAssignedIdentityRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteUserAssignedIdentityResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateFederatedIdentityCredentialRequest {
  // The name of the resource must follow the following pattern:
  // ^[a-zA-Z0-9]{1}[a-zA-Z0-9-_]{2,119}$
  // Reference:
  // https://github.com/Azure/azure-service-operator/blob/209b27aabe1e5685340309b4b24cca47368ca07d/v2/api/managedidentity/v1api20230131/federated_identity_credential_types_gen.go#L349-L352
  .common.resource.Meta resource_meta = 1;

  // Name of the UserAssignedIdentity resource this credential is associated
  // with. Must be in the same namespace of the created credential.
  string user_assigned_identity_name = 2;

  // Which kubernetes service account to bind.
  .common.k8s.ServiceAccount service_account = 3;
}

message CreateFederatedIdentityCredentialResponse {
  .common.resource.creation.Status status = 1;
}

message GetFederatedIdentityCredentialRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetFederatedIdentityCredentialResponse {
  .common.resource.Status status = 1;
}

message DeleteFederatedIdentityCredentialRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteFederatedIdentityCredentialResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateRoleAssignmentRequest {
  .common.resource.Meta resource_meta = 1;

  string principal_id = 2;

  // Which roles to bind.
  RoleAssignment role_assignment = 3;
}

message CreateRoleAssignmentResponse {
  .common.resource.creation.Status status = 1;
}

message GetRoleAssignmentRequest {.common.resource.Meta resource_meta = 1;}

message GetRoleAssignmentResponse {.common.resource.Status status = 1;}

message DeleteRoleAssignmentRequest {.common.resource.Meta resource_meta = 1;}

message DeleteRoleAssignmentResponse {
  .common.resource.deletion.Status status = 1;
}

// RoleBinding specifies the type of role to bind to an IAM Service Account.
// Only AZBlob role is currently supported
message RoleAssignment {
  oneof access_option {
    // If specified, the create IAM policy will grant access to the specified
    // AZBlob location.
    BlobAccessOption blob_access_option = 1;
  }
}

// BlobAccessOption specifies an AZBlob directory a AZBlob IAM Policy should
// give access to. It will give access to all operations towards the directory.
message BlobAccessOption {
  // Blob container name.
  string container = 1;
  // List of directories of the AZBlob container.
  repeated string dirs = 2;

  string storage_account = 3;
}

message CreatePrivateEndpointRequest {
  .common.resource.Meta resource_meta = 1;
  string private_link_service_id = 2;
  string private_link_subnet_id = 3;
  map<string, string> extra_tags = 4;
}

message CreatePrivateEndpointResponse {
  .common.resource.creation.Status status = 1;
}

message GetPrivateEndpointRequest {.common.resource.Meta resource_meta = 1;}

message GetPrivateEndpointResponse {
  .common.resource.Status status = 1;
  // The private endpoint connection status of the private endpoint. Possible values:
  // 'Deleting', 'Failed', 'Succeeded', 'Updating'.
  PrivateEndpointStatus private_endpoint_status = 2;
  string private_endpoint_ip = 3;
}

// ref: https://github.com/Azure/azure-service-operator/blob/main/v2/api/network/v1api20220701/private_endpoint_status_private_endpoint_sub_resource_embedded_arm_types_gen.go#L68
// ref: https://github.com/Azure/azure-service-operator/blob/main/v2/api/network/v1api20220701/application_gateway_types_gen.go#L9430-L9435
enum PrivateEndpointStatus {
  STATUS_UNSPECIFIED = 0;
  DELETING = 1;
  FAILED = 2;
  SUCCEEDED = 3;
  UPDATING = 4;
}

message DeletePrivateEndpointRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeletePrivateEndpointResponse {
  .common.resource.deletion.Status status = 1;
}

message CreatePGServerRequest {
  .common.resource.Meta resource_meta = 1;
  .common.azr.PGServerSpec spec = 2;
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
message CreatePGServerResponse {
  .common.resource.creation.Status status = 1;
}

message DeletePGServerRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, NOT_FOUND.
message DeletePGServerResponse {
  .common.resource.deletion.Status status = 1;
}

message StartPGServerRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
message StartPGServerResponse {
  .common.resource.update.Status status = 1;
}

message StopPGServerRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
message StopPGServerResponse {
  .common.resource.update.Status status = 1;
}

message GetPGServerRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetPGServerResponse {
  .common.resource.Status status = 1;
  optional string domain_name = 2;
  .common.azr.PGServerState server_state = 3;
}
