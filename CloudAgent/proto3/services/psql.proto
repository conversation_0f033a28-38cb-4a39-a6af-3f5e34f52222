syntax = "proto3";

package services.psql;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/psql";

import "common/creation.proto";
import "common/deletion.proto";
import "common/resource.proto";

service PsqlManager {
  rpc CreateDatabase(CreateDatabaseRequest)returns (CreateDatabaseResponse) {}
  rpc DeleteDatabase(DeleteDatabaseRequest)returns (DeleteDatabaseResponse) {}

  rpc CreateUser(CreateUserRequest)returns (CreateUserResponse) {}
  rpc DeleteUser(DeleteUserRequest)returns (DeleteUserResponse) {}
  rpc TruncateTables(TruncateTablesRequest)returns (TruncateTablesResponse) {}
  rpc UpdateSystemParameters(UpdateSystemParametersRequest)returns (UpdateSystemParametersResponse) {}
  rpc CloneDatabase(CloneDatabaseRequest)returns (CloneDatabaseResponse) {}
}

message Connection {
  string host = 1;
  uint32 port = 2;
  string database = 3;
  string username = 4;
  string password = 5;
}

message CreateDatabaseRequest {
  Connection connection = 1;
  string database = 2;
}

message CreateDatabaseResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteDatabaseRequest {
  Connection connection = 1;
  string database = 2;
}

message DeleteDatabaseResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateUserRequest {
  Connection connection = 1;
  string username = 2;
  string password = 3;
  repeated string privileged_databases = 4;
}

message CreateUserResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteUserRequest {
  Connection connection = 1;
  string username = 2;
}

message DeleteUserResponse {
  .common.resource.deletion.Status status = 1;
}

message TruncateTablesRequest {
  Connection connection = 1;
}

message TruncateTablesResponse {}

message UpdateSystemParametersRequest {
  Connection connection = 1;
  map<string, string> system_parameters = 2;
}

message UpdateSystemParametersResponse {}

message CloneDatabaseRequest {
  .common.resource.Meta resource_meta = 1;
  Connection source_connection = 2;
  Connection target_connection = 3;
}

message CloneDatabaseResponse {
  .common.resource.creation.Status status = 1;
}
