syntax = "proto3";

package services.aws;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/aws";

import "common/creation.proto";
import "common/deletion.proto";
import "common/update.proto";
import "common/resource.proto";
import "common/k8s.proto";
import "common/aws.proto";
import "services/common/data.proto";

service AwsResourceManager {
  rpc CreateDataDirectoryDeletionTask(
  data.CreateDataDirectoryDeletionTaskRequest)
      returns (data.CreateDataDirectoryDeletionTaskResponse) {}

  rpc CreateDataDirectoryCloneTask(
  data.CreateDataDirectoryCloneTaskRequest)
      returns (data.CreateDataDirectoryCloneTaskResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateSecurityGroup(CreateSecurityGroupRequest)
      returns (CreateSecurityGroupResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteSecurityGroup(DeleteSecurityGroupRequest)
      returns (DeleteSecurityGroupResponse) {}
  rpc GetSecurityGroup(GetSecurityGroupRequest)
      returns (GetSecurityGroupResponse) {}

  rpc CreateSecurityGroupPolicy(CreateSecurityGroupPolicyRequest)
      returns (CreateSecurityGroupPolicyResponse) {}
  rpc DeleteSecurityGroupPolicy(DeleteSecurityGroupPolicyRequest)
      returns (DeleteSecurityGroupPolicyResponse) {}
  rpc GetSecurityGroupPolicy(GetSecurityGroupPolicyRequest)
      returns (GetSecurityGroupPolicyResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateIAMPolicy(CreateIAMPolicyRequest)
      returns (CreateIAMPolicyResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteIAMPolicy(DeleteIAMPolicyRequest)
      returns (DeleteIAMPolicyResponse) {}
  rpc GetIAMPolicy(GetIAMPolicyRequest) returns (GetIAMPolicyResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateIAMRole(CreateIAMRoleRequest) returns (CreateIAMRoleResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteIAMRole(DeleteIAMRoleRequest) returns (DeleteIAMRoleResponse) {}
  rpc GetIAMRole(GetIAMRoleRequest) returns (GetIAMRoleResponse) {}

  // Expected a successful status
  rpc CheckVPCEndpointServiceReachability(CheckVPCEndpointServiceReachabilityRequest)
      returns (CheckVPCEndpointServiceReachabilityResponse) {}
  // Expected a SCHEDULED status on success.
  rpc CreateVPCEndpoint(CreateVPCEndpointRequest)
      returns (CreateVPCEndpointResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteVPCEndpoint(DeleteVPCEndpointRequest)
      returns (DeleteVPCEndpointResponse) {}
  rpc GetVPCEndpoint(GetVPCEndpointRequest) returns (GetVPCEndpointResponse) {}

  rpc CreateSimpleDataReplicationTask(
  data.CreateSimpleDataReplicationTaskRequest)
      returns (data.CreateSimpleDataReplicationTaskResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateDBInstance(CreateDBInstanceRequest)
      returns (CreateDBInstanceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteDBInstance(DeleteDBInstanceRequest)
      returns (DeleteDBInstanceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc StartDBInstance(StartDBInstanceRequest)
      returns (StartDBInstanceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc StopDBInstance(StopDBInstanceRequest)
      returns (StopDBInstanceResponse) {}

  rpc GetDBInstance(GetDBInstanceRequest)
      returns (GetDBInstanceResponse) {}

  rpc GetManifest(data.GetManifestRequest)
      returns (data.GetManifestResponse) {}
}

message IPPermission {
  // IP protocol. A value of -1 indicates all protocols
  string protocol = 1;
  // The starting port number (included).
  int32 from_port = 2;
  // The ending port number (included).
  int32 to_port = 3;
  // CIDRs of this rule.
  repeated string cidrs = 4;
  // target security groups of this rule.
  repeated string source_security_group_ids = 5;
  // description of this permission. shown in the AWS console.
  string description = 6;
}

message CreateSecurityGroupRequest {
  .common.resource.Meta resource_meta = 1;
  repeated IPPermission outbound_ip_permissions = 2;
  repeated IPPermission inbound_ip_permissions = 3;
}

// Statuses - UNKNOWN, SCHEDULED, CREATED, ALREADY_EXISTS
message CreateSecurityGroupResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteSecurityGroupRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - UNKNOWN, SCHEDULED, DELETED, NOT_FOUND
message DeleteSecurityGroupResponse {
  .common.resource.deletion.Status status = 1;
}

message GetSecurityGroupRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - UNKNOWN, NOT_FOUND, NOT_READY, READY, ERROR
message GetSecurityGroupResponse {
  .common.resource.Status status = 1;
  string security_group_id = 3;
}

// Request for creating an IAM policy. Empty policy is not accepted. So it's
// required to have at least one of the access options set.
message CreateIAMPolicyRequest {
  // The resource meta must meet the following restrictions:
  // - Name:
  //    1. contains at most 128 characters.
  //    2. contain only lowercase alphanumeric characters, '-' or '.'
  //    3. start with an alphanumeric character
  //    4. end with an alphanumeric character
  // - Namespace:
  //    1. Namespace is REQUIRED
  //    2. Must be valid RFC 1123 Label Names, i.e.:
  //      a. contain at most 63 characters
  //      b. contain only lowercase alphanumeric characters or '-'
  //      c. start with an alphanumeric character
  //      d. end with an alphanumeric character
  .common.resource.Meta resource_meta = 1;
  repeated IAMAccessOption access_options = 2;
}

message IAMAccessOption {
  oneof access_option {
    // If specified, the created IAM policy will grant access to the specified
    // S3 location.
    IAMS3AccessOption s3_access_option = 1;
  }

  reserved 2;
}

message CreateIAMPolicyResponse {
  .common.resource.creation.Status status = 1;
}

message GetIAMPolicyRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetIAMPolicyResponse {
  .common.resource.Status status = 1;
  // ARN of the corresponding policy. Only populated when status is READY or
  // ALREAY_EXISTS
  string policy_arn = 3;
}

message DeleteIAMPolicyRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteIAMPolicyResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateIAMRoleRequest {
  // The resource meta must meet the following restrictions:
  // - Name:
  //    1. contains at most 64 characters.
  //    2. contain only lowercase alphanumeric characters, '-' or '.'
  //    3. start with an alphanumeric character
  //    4. end with an alphanumeric character
  // - Namespace:
  //    1. Namespace is REQUIRED
  //    2. Must be valid RFC 1123 Label Names, i.e.:
  //      a. contain at most 63 characters
  //      b. contain only lowercase alphanumeric characters or '-'
  //      c. start with an alphanumeric character
  //      d. end with an alphanumeric character
  .common.resource.Meta resource_meta = 1;

  // Metas of the attached AWS policies managed by agent.
  repeated .common.resource.Meta policy_refs = 2;

  // K8s service account that is allowed to assume the created
  // role.
  .common.k8s.ServiceAccount service_account = 3;
}

message CreateIAMRoleResponse {
  .common.resource.creation.Status status = 1;
}

message GetIAMRoleRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetIAMRoleResponse {
  .common.resource.Status status = 1;
  // ARN of the corresponding role. Only populated when status is READY or
  // ALREAY_EXISTS
  string role_arn = 3;
}

message DeleteIAMRoleRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteIAMRoleResponse {
  .common.resource.deletion.Status status = 1;
}

// IAMS3AccessOption specifies a S3 directory a AWS Policy should give access
// to.
// It will give access to all operations towards the directory.
message IAMS3AccessOption {
  // S3 Bucket name.
  string bucket = 1;
  // The directory of the S3 bucket.
  string dir = 2 [deprecated = true];

  // List of directories of the S3 bucket.
  repeated string dirs = 3;
}

message IAMPrivateLinkAccessOption {
  // ID of the SG the create PrivateLink can be associated with.
  // The SG must be in the same region as the agent and belongs to the same AWS
  // account.
  string vpc_endpoints_tag_key = 1;
  string vpc_endpoints_tag_val = 2;
}

message DeleteVPCEndpointsRequest {
  string tag_key = 1;
  string tag_value = 2;
}

message DeleteVPCEndpointsResponse {
  repeated string vpc_endpoint_ids = 1;
  string error_message = 2;
}

message CreateSecurityGroupPolicyRequest {
  .common.resource.Meta resource_meta = 1;
  repeated string security_group_ids = 2;
  map<string, string> pod_labels_selector = 3;
}

message CreateSecurityGroupPolicyResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteSecurityGroupPolicyRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteSecurityGroupPolicyResponse {
  .common.resource.deletion.Status status = 1;
}

message GetSecurityGroupPolicyRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetSecurityGroupPolicyResponse {
  .common.resource.Status status = 1;
}

message CheckVPCEndpointServiceReachabilityRequest {
  .common.resource.Meta resource_meta = 1;
  string service_name = 2;
}

message CheckVPCEndpointServiceReachabilityResponse {
  .common.resource.Meta resource_meta = 1;
  VPCEndpointServiceReachabilityStatus status = 2;
}

enum VPCEndpointServiceReachabilityStatus {
  REACHABILITY_UNSPECIFIED = 0;
  SUCCESS = 1;
  NOT_FOUND = 2;
}

message CreateVPCEndpointRequest {
  .common.resource.Meta resource_meta = 1;
  string service_name = 2;
  repeated string security_group_ids = 3;
  repeated string subnet_ids = 4;
  bool private_dns_enabled = 5;
  map<string, string> extra_tags = 6;
}

message CreateVPCEndpointResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteVPCEndpointRequest {
  .common.resource.Meta resource_meta = 1;
}

message DeleteVPCEndpointResponse {
  .common.resource.deletion.Status status = 1;
}

message GetVPCEndpointRequest {
  .common.resource.Meta resource_meta = 1;
}

// https://docs.aws.amazon.com/vpc/latest/privatelink/concepts.html#concepts-endpoint-states
enum VPCEndpointStatus {
  STATUS_UNSPECIFIED = 0;
  PENDING_ACCEPTANCE = 1;
  PENDING = 2;
  AVAILABLE = 3;
  REJECTED = 4;
  EXPIRED = 5;
  FAILED = 6;
  DELETING = 7;
  DELETED = 8;
}
message GetVPCEndpointResponse {
  .common.resource.Status status = 1;
  string endpoint_id = 2;
  VPCEndpointStatus endpoint_state = 3;
  string endpoint_dns = 4;
}

message CreateDBInstanceRequest {
  .common.resource.Meta resource_meta = 1;
  .common.aws.DBInstanceSpec spec = 2;
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
message CreateDBInstanceResponse {
  .common.resource.creation.Status status = 1;
}

message DeleteDBInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, NOT_FOUND.
message DeleteDBInstanceResponse {
  .common.resource.deletion.Status status = 1;
}

message StartDBInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
message StartDBInstanceResponse {
  .common.resource.update.Status status = 1;
}

message StopDBInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, ALREADY_EXISTS, NOT_FOUND.
message StopDBInstanceResponse {
  .common.resource.update.Status status = 1;
}

message GetDBInstanceRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetDBInstanceResponse {
  .common.resource.Status status = 1;
  .common.aws.DBInstanceEndpoint endpoint = 2;
  .common.aws.DBInstanceStatus instance_status = 3;
}
