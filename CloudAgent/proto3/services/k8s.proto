syntax = "proto3";

package services.k8s;

option go_package = "github.com/risingwavelabs/cloudagent/pbgen/services/k8s";

import "common/creation.proto";
import "common/deletion.proto";
import "common/update.proto";
import "common/k8s.proto";
import "common/resource.proto";
import "common/risingwave.proto";
import "common/prometheus.proto";
import "common/gmp.proto";
import "common/postgresql.proto";
import "google/protobuf/duration.proto";
import "google/protobuf/timestamp.proto";

// K8sResourceManager offers APIs for native K8s resoruces management.
// The ID and namespace of all resources, if not specified, needs to follow the
// K8s naming restriction.
service K8sResourceManager {
  // Expected a CREATED status on success.
  rpc CreateNamespace(CreateNamespaceRequest)
      returns (CreateNamespaceResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteNamespace(DeleteNamespaceRequest)
      returns (DeleteNamespaceResponse) {}
  rpc GetNamespace(GetNamespaceRequest) returns (GetNamespaceResponse) {}
  // LabelNamespace add labels to a specified namespace. If the key already
  // exists, new value will override the existing one.
  rpc LabelNamespace(LabelNamespaceRequest) returns (LabelNamespaceResponse) {}

  // Expected a CREATED status on success.
  rpc CreateServiceAccount(CreateServiceAccountRequest)
      returns (CreateServiceAccountResponse) {}
  // Expected a DELETED status on success.
  rpc DeleteServiceAccount(DeleteServiceAccountRequest)
      returns (DeleteServiceAccountResponse) {}
  rpc GetServiceAccount(GetServiceAccountRequest)
      returns (GetServiceAccountResponse) {}
  rpc AnnotateServiceAccount(AnnotateServiceAccountRequest)
      returns (AnnotateServiceAccountResponse) {}

  // Expected a CREATED status on success.
  rpc CreateConfigMap(CreateConfigMapRequest)
      returns (CreateConfigMapResponse) {}
  // Expected a DELETED status on success.
  rpc DeleteConfigMap(DeleteConfigMapRequest)
      returns (DeleteConfigMapResponse) {}
  rpc GetConfigMap(GetConfigMapRequest) returns (GetConfigMapResponse) {}
  // UpdateConfigMap generats a JSON merge patch that converts `from` to `to`
  // and apply the generated patch to the runtime object.
  rpc UpdateConfigMap(UpdateConfigMapRequest)
      returns (UpdateConfigMapResponse) {}

  // Expected a CREATED status on success.
  rpc CreateSecret(CreateSecretRequest) returns (CreateSecretResponse) {}
  // Expected a DELETED status on success.
  rpc DeleteSecret(DeleteSecretRequest) returns (DeleteSecretResponse) {}
  rpc GetSecret(GetSecretRequest) returns (GetSecretResponse) {}
  rpc UpdateSecret(UpdateSecretRequest) returns (UpdateSecretResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateRisingWave(CreateRisingWaveRequest)
      returns (CreateRisingWaveResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeleteRisingWave(DeleteRisingWaveRequest)
      returns (DeleteRisingWaveResponse) {}
  rpc GetRisingWave(GetRisingWaveRequest) returns (GetRisingWaveResponse) {}
  // UpdateRisingWave generats a JSON merge patch that converts `from` to `to`
  // and apply the generated patch to the runtime RW object.
  rpc UpdateRisingWaveImage(UpdateRisingWaveImageRequest)
      returns (UpdateRisingWaveImageResponse) {}
  rpc UpdateRisingWaveLicenseKey(UpdateRisingWaveLicenseKeyRequest)
      returns (UpdateRisingWaveLicenseKeyResponse) {}
  rpc UpdateRisingWaveSecretStore(UpdateRisingWaveSecretStoreRequest)
      returns (UpdateRisingWaveSecretStoreResponse) {}
  // Deprecated. Please use ScaleRisingWaveOneOf
  rpc ScaleRisingWave(ScaleRisingWaveRequest)
      returns (ScaleRisingWaveResponse) {
    option deprecated = true;
  }
  // Scale the replica of `default` node group
  rpc ScaleRisingWaveOneOf(ScaleRisingWaveRequestOneOf)
      returns (ScaleRisingWaveResponse) {}

  // StartRisingWave scale all node groups to previous replica
  rpc StartRisingWave(StartRisingWaveRequest)
      returns (StartRisingWaveResponse) {}
  // StopRisingWave scale all node groups to 0, and record the current replica
  rpc StopRisingWave(StopRisingWaveRequest)
      returns (StopRisingWaveResponse) {}

  // Expected a SCHEDULED status on success.
  rpc UpdateRisingWaveComponents(UpdateRisingWaveComponentsRequest)
      returns (UpdateRisingWaveComponentsResponse) {}
  // Expected a SCHEDULED status on success.
  rpc UpdateRisingWaveMetaStore(UpdateRisingWaveMetaStoreRequest)
      returns (UpdateRisingWaveMetaStoreResponse) {}
  // Add one or more environment variable to zero or more components.
  rpc PutRisingWaveEnvVar(PutRisingWaveEnvRequest)
      returns (PutRisingWaveEnvResponse) {}
  // Removes one or more environment variable from zero or more components.
  rpc DeleteRisingWaveEnvVar(DeleteRisingWaveEnvRequest)
      returns (DeleteRisingWaveEnvResponse) {}


  // Deprecated. Please use CreateRisingWaveNodeGroup.
  rpc CreateRisingWaveComputeNodeGroup(CreateRisingWaveComputeNodeGroupRequest)
      returns (CreateRisingWaveComputeNodeGroupResponse) {
    option deprecated = true;
  }
  // Deprecated. Please use UpdateRisingWaveNodeGroup.
  rpc UpdateRisingWaveComputeNodeGroup(UpdateRisingWaveComputeNodeGroupRequest)
      returns (UpdateRisingWaveComputeNodeGroupResponse) {
    option deprecated = true;
  }
  // Deprecated. Please use DeleteRisingWaveNodeGroup.
  rpc DeleteRisingWaveComputeNodeGroup(DeleteRisingWaveComputeNodeGroupRequest)
      returns (DeleteRisingWaveComputeNodeGroupResponse) {
    option deprecated = true;
  }
  
  // Expected a CREATED status on success.
  rpc CreateRisingWaveNodeGroup(CreateRisingWaveNodeGroupRequest)
      returns (CreateRisingWaveNodeGroupResponse) {}
  // Expected a UPDATED status on success.
  rpc UpdateRisingWaveNodeGroup(UpdateRisingWaveNodeGroupRequest)
      returns (UpdateRisingWaveNodeGroupResponse) {}
  // Expected a DELETED status on success.
  rpc DeleteRisingWaveNodeGroup(DeleteRisingWaveNodeGroupRequest)
      returns (DeleteRisingWaveNodeGroupResponse) {}

  // Expected a UPDATED status on success.
  rpc UpdateRisingWaveNodeGroupConfiguration(UpdateRisingWaveNodeGroupConfigurationRequest)
      returns (UpdateRisingWaveNodeGroupConfigurationResponse) {}
  // Expected a UPDATED status on success. Setting any value for this field will immediately trigger nodes restart.
  rpc UpdateRisingWaveNodeGroupRestartAt(UpdateRisingWaveNodeGroupRestartAtRequest)
      returns (UpdateRisingWaveNodeGroupRestartAtResponse) {}

  // Expected a SCHEDULED status on success.
  rpc DeletePersistentVolumeClaims(DeletePersistentVolumeClaimsRequest)
      returns (DeletePersistentVolumeClaimsResponse) {}
  rpc GetPersistentVolumeClaims(GetPersistentVolumeClaimsRequest)
      returns (GetPersistentVolumeClaimsResponse) {}
  rpc CreatePersistentVolumeClaim(CreatePersistentVolumeClaimRequest)
      returns (CreatePersistentVolumeClaimResponse) {}

  rpc GetHelmRelease(GetHelmReleaseRequest) returns (GetHelmReleaseResponse) {}
  rpc InstallHelmRelease(InstallHelmReleaseRequest)
      returns (InstallHelmReleaseResponse) {}
  rpc UpgradeHelmRelease(UpgradeHelmReleaseRequest)
      returns (UpgradeHelmReleaseResponse) {}
  rpc UninstallHelmRelease(UninstallHelmReleaseRequest)
      returns (UninstallHelmReleaseResponse) {}

  // GetPodPhases retrieves all pods' phases in a namespace. The resource id
  // will be a k8s namesapce and the resource namespace is omitted.
  // Returns a map from pod name to pod phase.
  rpc GetPodPhases(GetPodPhasesRequest) returns (GetPodPhasesResponse) {}

  // Expected a READY status on success.
  rpc RestartStatefulSet(RestartStatefulSetRequest)
      returns (RestartStatefulSetResponse) {}
  rpc GetStatefulSetReplicasStatus(GetStatefulSetReplicasStatusRequest)
      returns (GetStatefulSetReplicasStatusResponse) {}
  rpc GetDeploymentReplicasStatus(GetDeploymentReplicasStatusRequest)
      returns (GetDeploymentReplicasStatusResponse) {}
  rpc RestartDeployment(RestartDeploymentRequest)
      returns (RestartDeploymentResponse) {}

  // Expected a CREATED status on success.
  rpc CreateServiceMonitor(CreateServiceMonitorRequest)
      returns (CreateServiceMonitorResponse) {}

  // Expect a DELETED status on success
  rpc DeleteServiceMonitor(DeleteServiceMonitorRequest)
      returns (DeleteServiceMonitorResponse) {}

  // Expected a CREATED status on success.
  rpc CreateAzureServiceMonitor(CreateServiceMonitorRequest)
      returns (CreateServiceMonitorResponse) {}

  // Expect a DELETED status on success
  rpc DeleteAzureServiceMonitor(DeleteServiceMonitorRequest)
      returns (DeleteServiceMonitorResponse) {}

  // Expected a CREATED status on success.
  rpc CreatePodMonitoring(CreatePodMonitoringRequest)
      returns (CreatePodMonitoringResponse) {}

  // Expect a DELETED status on success
  rpc DeletePodMonitoring(DeletePodMonitoringRequest)
      returns (DeletePodMonitoringResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreateService(CreateServiceRequest) returns (CreateServiceResponse) {}

  // Expect a CREATED status on success
  rpc CreateNetworkPolicy(CreateNetworkPolicyRequest)
      returns (CreateNetworkPolicyResponse) {}

  rpc CreateOrUpdateNetworkPolicy(CreateOrUpdateNetworkPolicyRequest)
      returns (CreateOrUpdateNetworkPolicyResponse) {}

  // Expect a DELETED status on success
  rpc DeleteNetworkPolicy(DeleteNetworkPolicyRequest)
      returns (DeleteNetworkPolicyResponse) {}

  // Expected a SCHEDULED status on success.
  rpc CreatePostgreSql(CreatePostgreSqlRequest)
      returns (CreatePostgreSqlResponse) {}
  // Expected a SCHEDULED status on success.
  rpc DeletePostgreSql(DeletePostgreSqlRequest)
      returns (DeletePostgreSqlResponse) {}
  // Expected a SCHEDULED status on success.
  rpc UpdatePostgreSql(UpdatePostgreSqlRequest)
      returns (UpdatePostgreSqlResponse) {}
  rpc GetPostgreSql(GetPostgreSqlRequest) returns (GetPostgreSqlResponse) {}

  // GetPods retrieves all pods in a namespace. The resource id
  // will be a k8s namesapce and the resource namespace is omitted.
  rpc GetPods(GetPodsRequest) returns (GetPodsResponse) {}

  rpc GetClusterAccess(GetClusterAccessRequest)
      returns (GetClusterAccessResponse) {}
}

message CreateNamespaceRequest {
  common.resource.Meta resource_meta = 1;
  map<string, string> labels = 2;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateNamespaceResponse {
  .common.resource.creation.Status status = 1;
}

message LabelNamespaceRequest {
  common.resource.Meta resource_meta = 1;
  map<string, string> labels = 2;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message LabelNamespaceResponse {}

message GetNamespaceRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message GetNamespaceResponse {
  .common.resource.Status status = 1;
}

message DeleteNamespaceRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes SCHEDULED and NOT_FOUND.
// Otherwise an RPC error will be returned.
message DeleteNamespaceResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateServiceAccountRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateServiceAccountResponse {
  .common.resource.creation.Status status = 1;
}

message GetServiceAccountRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message GetServiceAccountResponse {
  .common.resource.Status status = 1;
}

message DeleteServiceAccountRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes DELETED and NOT_FOUND.
// Otherwise an RPC error will be returned.
message DeleteServiceAccountResponse {
  .common.resource.deletion.Status status = 1;
}

message AnnotateServiceAccountRequest {
  common.resource.Meta resource_meta = 1;
  map<string, string> labels = 2;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message AnnotateServiceAccountResponse {}

message CreateConfigMapRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.ConfigMap config_map_spec = 2;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateConfigMapResponse {
  .common.resource.creation.Status status = 1;
}

message GetConfigMapRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message GetConfigMapResponse {
  .common.resource.Status status = 1;
  common.k8s.ConfigMap config_map_spec = 2;
}

message UpdateConfigMapRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.ConfigMap from_config_map_spec = 2 [deprecated = true];
  common.k8s.ConfigMap to_config_map_spec = 3;
}

message UpdateConfigMapResponse {}

message DeleteConfigMapRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes DELETED and NOT_FOUND.
// Otherwise an RPC error will be returned.
message DeleteConfigMapResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateSecretRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.Secret secret_spec = 2;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateSecretResponse {
  .common.resource.creation.Status status = 1;
}

message GetSecretRequest {
  common.resource.Meta resource_meta = 1;
}

message GetSecretResponse {
  .common.resource.Status status = 1;
  common.k8s.Secret secret_spec = 2;
}

message DeleteSecretRequest {
  common.resource.Meta resource_meta = 1;
}

message UpdateSecretRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.Secret secret_spec = 2;
}

message UpdateSecretResponse {}


// Valid statuses includes DELETED and NOT_FOUND.
// Otherwise an RPC error will be returned.
message DeleteSecretResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateRisingWaveRequest {
  common.resource.Meta resource_meta = 1;
  common.rw.RisingWaveSpec risingwave_spec = 2;
  map<string, string> labels = 3;
  map<string, string> annotations = 4;
}

message SpecificGroups {
  repeated string groups = 1;
}

message EnvVars {
  repeated common.k8s.EnvVar vars = 1;
}

message EnvKeys {
  repeated string keys = 1;
}

message PutRisingWaveEnvRequest {
  common.resource.Meta resource_meta = 1;

  oneof node_group_selection {
    SpecificGroups specific_groups = 2;
    bool all_groups = 3;
  }

  oneof component {
    EnvVars compute_env_change = 4;
    EnvVars compactor_env_change = 5;
    EnvVars standalone_env_change = 6;
    EnvVars meta_env_change = 7;
    EnvVars frontend_env_change = 8;
  }
}

message PutRisingWaveEnvResponse {}

message DeleteRisingWaveEnvRequest {
  common.resource.Meta resource_meta = 1;

  oneof node_group_selection {
    SpecificGroups specific_groups = 2;
    bool all_groups = 3;
  }

  oneof component {
    EnvKeys compute_env_change = 4;
    EnvKeys compactor_env_change = 5;
    EnvKeys standalone_env_change = 6;
    EnvKeys meta_env_change = 7;
    EnvKeys frontend_env_change = 8;
  }
}

message DeleteRisingWaveEnvResponse {}

// Valid statuses includes SCHEDULED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateRisingWaveResponse {
  .common.resource.creation.Status status = 1;
}

message GetRisingWaveRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message GetRisingWaveResponse {
  .common.resource.Status status = 1;
  common.rw.RisingWaveSpec risingwave_spec = 2;
  common.rw.RisingWaveStatus risingwave_status = 3;
}

message DeleteRisingWaveRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes SCHEDULED and NOT_FOUND.
// Otherwise an RPC error will be returned.
message DeleteRisingWaveResponse {
  .common.resource.deletion.Status status = 1;
}

message UpdateRisingWaveImageRequest {
  common.resource.Meta resource_meta = 1;
  string image_tag = 2;
}

message UpdateRisingWaveImageResponse {}

message UpdateRisingWaveLicenseKeyRequest {
  common.resource.Meta resource_meta = 1;
  string secret_name = 2;
}

message UpdateRisingWaveLicenseKeyResponse {}

message UpdateRisingWaveSecretStoreRequest {
  common.resource.Meta resource_meta = 1;
  string secret_name = 2;
  string secret_key = 3;
}

message UpdateRisingWaveSecretStoreResponse {}

// Deprecated. Use ScaleRisingWaveRequestOneOf instead
message ScaleRisingWaveRequest {
  common.resource.Meta resource_meta = 1;
  // If present, set meta nodes with the specified replica and resources.
  // This will be a no-op if meta has no node group.
  common.rw.ScaleSpec meta_scale_spec = 2;
  // If present, set frontend nodes with the specified replica and resources.
  // This will be a no-op if frontend has no node group.
  common.rw.ScaleSpec frontend_scale_spec = 3;
  // If present, set compute nodes with the specified replica and resources.
  // This will be a no-op if compute has no node group.
  common.rw.ScaleSpec compute_scale_spec = 4;
  // If present, set compactor nodes with the specified replica and resources.
  // This will be a no-op if compactor has no node group.
  common.rw.ScaleSpec compactor_scale_spec = 5;
  // If present, set compactor nodes with the specified replica and resources.
  // This will be a no-op if connector has no node group.
  common.rw.ScaleSpec connector_scale_spec = 6 [deprecated = true];

  // If present, we will start the cluster in standalone mode.
  // This will be a no-op if standalone has no node group.
  common.rw.ScaleSpec standalone_scale_spec = 7;
}

message ScaleRisingWaveResponse {}

message ScaleRisingWaveRequestOneOf {
  oneof mode {
    common.rw.ScaleSpec standalone_spec = 1;
    ClusterScaleSpec cluster_spec = 2;
  }
  common.resource.Meta resource_meta = 3;
}

message ClusterScaleSpec {
  // If present, set meta nodes with the specified replica and resources.
  // This will be a no-op if meta has no node group.
  common.rw.ScaleSpec meta_scale_spec = 2;

  // If present, set frontend nodes with the specified replica and resources.
  // This will be a no-op if frontend has no node group.
  common.rw.ScaleSpec frontend_scale_spec = 3;

  // If present, set compute nodes with the specified replica and resources.
  // This will be a no-op if compute has no node group.
  common.rw.ScaleSpec compute_scale_spec = 4;

  // If present, set compactor nodes with the specified replica and resources.
  // This will be a no-op if compactor has no node group.
  common.rw.ScaleSpec compactor_scale_spec = 5;
}

message RisingWaveReplicaOverride {
  uint32 replicas = 1;
  string component = 2;
  string node_group = 3;
}

message StartRisingWaveRequest{
  common.resource.Meta resource_meta = 1;
  repeated RisingWaveReplicaOverride overrides = 2;
}

message StartRisingWaveResponse{}

message StopRisingWaveRequest{
  common.resource.Meta resource_meta = 1;
}

message StopRisingWaveResponse{}

message UpdateRisingWaveComponentsRequest {
  common.resource.Meta resource_meta = 1;
  common.rw.ComponentsSpec components_spec = 2;
  optional bool enable_standalone_mode = 3;
}

message UpdateRisingWaveComponentsResponse {}

message UpdateRisingWaveMetaStoreRequest {
  common.resource.Meta resource_meta = 1;
  common.rw.MetaStoreSpec meta_store_spec = 2;
}

message UpdateRisingWaveMetaStoreResponse {}

message CreateRisingWaveComputeNodeGroupRequest{
  common.resource.Meta resource_meta = 1;
  common.rw.NodeGroupSpec node_group = 2;
}

message CreateRisingWaveComputeNodeGroupResponse {
  .common.resource.creation.Status status = 1;
}

message UpdateRisingWaveComputeNodeGroupRequest{
  common.resource.Meta resource_meta = 1;
  common.rw.NodeGroupSpec node_group = 2;
}

message UpdateRisingWaveComputeNodeGroupResponse {
  .common.resource.update.Status status = 1;
}

message DeleteRisingWaveComputeNodeGroupRequest{
  common.resource.Meta resource_meta = 1;
  string node_group_name = 2;
}

message DeleteRisingWaveComputeNodeGroupResponse {
  .common.resource.deletion.Status status = 1;
}

message CreateRisingWaveNodeGroupRequest{
  .common.resource.Meta resource_meta = 1;
  .common.rw.ComponentType component = 2;
  .common.rw.NodeGroupSpec node_group = 3;
}

message CreateRisingWaveNodeGroupResponse {
  .common.resource.creation.Status status = 1;
}

message UpdateRisingWaveNodeGroupRequest{
  .common.resource.Meta resource_meta = 1;
  .common.rw.ComponentType component = 2;
  .common.rw.NodeGroupSpec node_group = 3;
}

message UpdateRisingWaveNodeGroupResponse {
  .common.resource.update.Status status = 1;
}

message DeleteRisingWaveNodeGroupRequest{
  .common.resource.Meta resource_meta = 1;
  .common.rw.ComponentType component = 2;
  string node_group_name = 3;
}

message DeleteRisingWaveNodeGroupResponse {
  .common.resource.deletion.Status status = 1;
}

message UpdateRisingWaveNodeGroupConfigurationRequest{
  .common.resource.Meta resource_meta = 1;
  .common.rw.ComponentType component = 2;
  string node_group = 3;
  .common.rw.NodeConfig node_config = 4;
}

message UpdateRisingWaveNodeGroupConfigurationResponse {
  .common.resource.update.Status status = 1;
}

message UpdateRisingWaveNodeGroupRestartAtRequest{
  .common.resource.Meta resource_meta = 1;
  .common.rw.ComponentType component = 2;
  string node_group = 3;
  google.protobuf.Timestamp restartAt = 4;
}

message UpdateRisingWaveNodeGroupRestartAtResponse {
  .common.resource.update.Status status = 1;
}

message DeletePersistentVolumeClaimsRequest {
  common.resource.Meta resource_meta = 1;
}

message DeletePersistentVolumeClaimsResponse {
  .common.resource.deletion.Status status = 1;
}

message GetPersistentVolumeClaimsRequest {
  common.resource.Meta resource_meta = 1;
}

message GetPersistentVolumeClaimsResponse {
  .common.resource.Status status = 1;
}

message CreatePersistentVolumeClaimRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.PersistentVolumeClaimSpec spec = 2;
}

message CreatePersistentVolumeClaimResponse {
  common.resource.creation.Status status = 1;
}

// managing helm release
message InstallHelmReleaseRequest {
  // task resource meta
  common.resource.Meta resource_meta = 1;
  common.resource.Meta release_meta = 2;
  string chart_url = 3;
  string values_json = 4;
}

message InstallHelmReleaseResponse {
  // the creation status of the corresponding task
  .common.resource.creation.Status status = 1;
}

message UpgradeHelmReleaseRequest {
  // task resource meta
  common.resource.Meta resource_meta = 1;
  common.resource.Meta release_meta = 2;
  string chart_url = 3;
  string values_json = 4;
  bool install = 5; // if true, install the release if it does not exist
}

message UpgradeHelmReleaseResponse {
  // the creation status of the corresponding task
  .common.resource.creation.Status status = 1;
}

message UninstallHelmReleaseRequest {
  // task resource meta
  common.resource.Meta resource_meta = 1;
  common.resource.Meta release_meta = 2;
}

message UninstallHelmReleaseResponse {
  // the creation status of the corresponding task
  .common.resource.creation.Status status = 1;
}

message GetHelmReleaseRequest {
  common.resource.Meta release_meta = 1;
}

message GetHelmReleaseResponse {
  .common.k8s.HelmRelease helm_release = 1;
}

message GetPodPhasesRequest {
  common.resource.Meta resource_meta = 1;
}

message GetPodPhasesResponse {
  map<string, common.k8s.PodPhase> pod_to_phase = 1;
}

// Valid statuses includes READY and NOT_FOUND.
// Otherwise an RPC error will be returned.
message RestartStatefulSetRequest {
  common.resource.Meta resource_meta = 1;
}

message RestartStatefulSetResponse {}

// Valid statuses includes READY and NOT_FOUND.
// Otherwise an RPC error will be returned.
message RestartDeploymentRequest {
  common.resource.Meta resource_meta = 1;
}

message RestartDeploymentResponse {}

// Valid statuses includes READY and NOT_FOUND.
// Otherwise an RPC error will be returned.
message GetStatefulSetReplicasStatusRequest {
  common.resource.Meta resource_meta = 1;
}

message GetStatefulSetReplicasStatusResponse {
  .common.resource.Status status = 1;
}

message GetDeploymentReplicasStatusRequest {
  common.resource.Meta resource_meta = 1;
}

message GetDeploymentReplicasStatusResponse {
  .common.resource.Status status = 1;
}

message CreateServiceMonitorRequest {
  common.resource.Meta resource_meta = 1;
  common.prometheus.ServiceMonitorSpec service_monitor_spec = 2;
  map<string, string> labels = 3;
  map<string, string> annotations = 4;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateServiceMonitorResponse {
  .common.resource.creation.Status status = 1;
}

message CreatePodMonitoringRequest {
  common.resource.Meta resource_meta = 1;
  common.gmp.PodMonitoringSpec pod_monitoring_spec = 2;
  map<string, string> labels = 3;
  map<string, string> annotations = 4;
}

// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreatePodMonitoringResponse {
  .common.resource.creation.Status status = 1;
}

message CreateServiceRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.ServiceSpec service_spec = 2;
  map<string, string> labels = 3;
}

// Valid statuses includes SCHEDULED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateServiceResponse {
  .common.resource.creation.Status status = 1;
}

message CreateNetworkPolicyRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.NetworkPolicySpec spec = 2;
  map<string, string> labels = 3;
}


// Valid statuses includes CREATED and ALREADY_EXISTS.
// Otherwise an RPC error will be returned.
message CreateNetworkPolicyResponse {
  .common.resource.creation.Status status = 1;
}

message CreateOrUpdateNetworkPolicyRequest {
  common.resource.Meta resource_meta = 1;
  common.k8s.NetworkPolicySpec spec = 2;
  map<string, string> labels = 3;
}

message CreateOrUpdateNetworkPolicyResponse {}

message CreatePostgreSqlRequest {
  .common.resource.Meta resource_meta = 1;
  .common.postgresql.PostgreSqlSpec postgresql_spec = 2;
}

// Statuses - SCHEDULED, ALREADY_EXISTS.
message CreatePostgreSqlResponse {
  .common.resource.creation.Status status = 1;
}

message DeletePostgreSqlRequest {
  .common.resource.Meta resource_meta = 1;
}

// Statuses - SCHEDULED, NOT_FOUND
message DeletePostgreSqlResponse {
  .common.resource.deletion.Status status = 1;
}

message UpdatePostgreSqlRequest {
  .common.resource.Meta resource_meta = 1;
  // allow to update: number_of_instances, resources, volume
  .common.postgresql.PostgreSqlSpec postgresql_spec = 2;
}

message UpdatePostgreSqlResponse {
  .common.postgresql.UpdateStatusCode status = 1;
}

message GetPostgreSqlRequest {
  .common.resource.Meta resource_meta = 1;
}

message GetPostgreSqlResponse {
  .common.resource.Status status = 1;
  .common.postgresql.PostgreSqlSpec postgresql_spec = 2;
  .common.resource.Meta secret_ref = 3;
  .common.postgresql.Credentials credentials = 4;
}

message DeleteNetworkPolicyRequest {
  common.resource.Meta resource_meta = 1;
}

// Valid statuses includes SCHEDULED and NOT_FOUND.
// Otherwise an RPC error will be returned.
message DeleteNetworkPolicyResponse {
  .common.resource.deletion.Status status = 1;
}

message DeleteServiceMonitorRequest {
  common.resource.Meta resource_meta = 1;
}

message DeleteServiceMonitorResponse {
  .common.resource.deletion.Status status = 1;
}

message DeletePodMonitoringRequest {
  common.resource.Meta resource_meta = 1;
}

message DeletePodMonitoringResponse {
  .common.resource.deletion.Status status = 1;
}

message GetPodsRequest {
  string namespace = 1;
}

message GetPodsResponse {
  repeated common.k8s.Pod pods = 1;
}

message GetClusterAccessRequest {
  // If not specified, a default 30 mins timeout will be enforced.
  // Timeout cannot exceed 24 hours
  google.protobuf.Duration timeout = 1;
}

message GetClusterAccessResponse {
  string cluster_endpoint = 1;
  string cluster_ca_certificate_base64 = 2;
  string token = 3;
}
