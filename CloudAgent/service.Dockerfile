FROM golang:1.24 as builder
WORKDIR /app
COPY . .

ARG VERSION='latest'
RUN go mod tidy
RUN go build -o ./cloudagent -ldflags "-X 'github.com/risingwavelabs/CloudAgent/pkg.CurrentVersion=${VERSION}' -extldflags '-static'" cmd/agent/main.go

## Output Runner
FROM alpine as runner
WORKDIR /app
COPY --from=builder /app/cloudagent /app/cloudagent
RUN apk add gcompat
RUN chmod +x /app/cloudagent

ENTRYPOINT ["./cloudagent", "-config_path", "./config/config.textproto", "-port", "40001"]
