package conversion

import (
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func ToPodPhaseProto(p corev1.PodPhase) (pbk8s.PodPhase, error) {
	switch p {
	case corev1.PodPending:
		return pbk8s.PodPhase_POD_PHASE_PENDING, nil
	case corev1.PodRunning:
		return pbk8s.PodPhase_POD_PHASE_RUNNING, nil
	case corev1.PodSucceeded:
		return pbk8s.PodPhase_POD_PHASE_SUCCEEDED, nil
	case corev1.PodFailed:
		return pbk8s.PodPhase_POD_PHASE_FAILED, nil
	case corev1.PodUnknown:
		return pbk8s.PodPhase_POD_PHASE_UNKNOWN, eris.Errorf("invalid service type %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return pbk8s.PodPhase_POD_PHASE_UNKNOWN, eris.New("unreachable")
}

func ToPodProto(p *corev1.Pod) (*pbk8s.Pod, error) {
	phase, err := ToPodPhaseProto(p.Status.Phase)
	if err != nil {
		return nil, err
	}

	return &pbk8s.Pod{
		Name:        p.Name,
		Labels:      p.Labels,
		StatusPhase: phase,
	}, nil
}
