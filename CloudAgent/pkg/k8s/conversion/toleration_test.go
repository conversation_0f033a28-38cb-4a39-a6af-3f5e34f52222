package conversion

import (
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromTolerationConversion(t *testing.T) {
	tests := []struct {
		input    *pbk8s.Toleration
		expected corev1.Toleration
	}{
		{
			input: &pbk8s.Toleration{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
				TolerationSecs: utils.Ptr(int64(10)),
			},

			expected: corev1.Toleration{
				Key:               "testkey",
				Value:             "testvalue",
				Effect:            corev1.TaintEffectNoExecute,
				Operator:          corev1.TolerationOpEqual,
				TolerationSeconds: utils.Ptr(int64(10)),
			},
		},
		{
			input: &pbk8s.Toleration{
				Key:      "testkey",
				Value:    "testvalue",
				Effect:   pbk8s.TaintEffect_TAINT_EFFECT_NO_SCHEDULE,
				Operator: pbk8s.TolerationOperator_TOLERATION_OPERATOR_EXISTS,
			},

			expected: corev1.Toleration{
				Key:               "testkey",
				Value:             "testvalue",
				Effect:            corev1.TaintEffectNoSchedule,
				Operator:          corev1.TolerationOpExists,
				TolerationSeconds: nil,
			},
		},
		{
			input: &pbk8s.Toleration{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_SCHEDULE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EXISTS,
				TolerationSecs: utils.Ptr(int64(0)),
			},

			expected: corev1.Toleration{
				Key:               "testkey",
				Value:             "testvalue",
				Effect:            corev1.TaintEffectNoSchedule,
				Operator:          corev1.TolerationOpExists,
				TolerationSeconds: utils.Ptr(int64(0)),
			},
		},
	}

	for _, tt := range tests {
		toleration, err := FromTolerationProto(tt.input)
		require.NoError(t, err)
		assert.Equal(t, toleration, tt.expected, "unexpected output, result: %v, test case: %v", toleration, tt)
	}
}

func TestTolerationConversionFailure(t *testing.T) {
	_, err := FromTolerationProto(
		&pbk8s.Toleration{
			Key:            "testkey",
			Value:          "testvalue",
			TolerationSecs: utils.Ptr(int64(10)),
		},
	)
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(err))
}

func TestToTolerationConversion(t *testing.T) {
	tests := []struct {
		expected *pbk8s.Toleration
		input    corev1.Toleration
	}{
		{
			expected: &pbk8s.Toleration{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
				TolerationSecs: utils.Ptr(int64(10)),
			},

			input: corev1.Toleration{
				Key:               "testkey",
				Value:             "testvalue",
				Effect:            corev1.TaintEffectNoExecute,
				Operator:          corev1.TolerationOpEqual,
				TolerationSeconds: utils.Ptr(int64(10)),
			},
		},
		{
			expected: &pbk8s.Toleration{
				Key:      "testkey",
				Value:    "testvalue",
				Effect:   pbk8s.TaintEffect_TAINT_EFFECT_NO_SCHEDULE,
				Operator: pbk8s.TolerationOperator_TOLERATION_OPERATOR_EXISTS,
			},

			input: corev1.Toleration{
				Key:               "testkey",
				Value:             "testvalue",
				Effect:            corev1.TaintEffectNoSchedule,
				Operator:          corev1.TolerationOpExists,
				TolerationSeconds: nil,
			},
		},
	}

	for _, tt := range tests {
		toleration, err := ToTolerationProto(tt.input)
		require.NoError(t, err)
		assert.Equal(t, toleration, tt.expected, "unexpected output, result: %v, test case: %v", toleration, tt)
	}
}
