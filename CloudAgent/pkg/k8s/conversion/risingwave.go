package conversion

import (
	"fmt"

	"github.com/risingwavelabs/eris"
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/utils/ptr"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func FromRisingWaveSpecProto(p *pbrw.RisingWaveSpec) (*rwv1alpha1.RisingWaveSpec, error) {
	frontendServiceType, err := FromServiceTypeProto(p.GetFrontendServiceType())
	if err != nil {
		return nil, err
	}
	metaStore, err := FromRisingWaveMetaStoreProto(p.GetMetaStoreSpec())
	if err != nil {
		return nil, err
	}

	stateStore, err := FromStateStoreProto(p.GetStateStore())
	if err != nil {
		return nil, err
	}

	metaSpec, err := FromComponentSpecProto(p.GetComponents().GetMetaSpec())
	if err != nil {
		return nil, err
	}

	frontendSpec, err := FromComponentSpecProto(p.GetComponents().GetFrontendSpec())
	if err != nil {
		return nil, err
	}

	computeSpec, err := FromComponentSpecProto(p.GetComponents().GetComputeSpec())
	if err != nil {
		return nil, err
	}

	if p.GetComputeConfig() != nil {
		computeConfigSpec := FromRisingWaveConfigProto(p.GetComputeConfig())
		for i := range computeSpec.NodeGroups {
			computeSpec.NodeGroups[i].Configuration = &computeConfigSpec.RisingWaveNodeConfiguration
		}
	}

	compactorSpec, err := FromComponentSpecProto(p.GetComponents().GetCompactorSpec())
	if err != nil {
		return nil, err
	}

	isStandalone := p.GetEnableStandaloneMode()
	var standaloneComponent *rwv1alpha1.RisingWaveStandaloneComponent
	sc := p.GetComponents().GetStandaloneComponent()
	if sc != nil {
		standaloneComponent, err = FromStandaloneSpecProto(sc)
		if err != nil {
			return nil, err
		}
	}

	return &rwv1alpha1.RisingWaveSpec{
		Configuration:               FromRisingWaveConfigProto(p.GetConfig()),
		MetaStore:                   metaStore,
		StateStore:                  stateStore,
		Image:                       p.GetImage(),
		FrontendServiceType:         frontendServiceType,
		EnableDefaultServiceMonitor: utils.Ptr(p.GetEnableDefaultServiceMonitor()),
		EnableFullKubernetesAddr:    utils.Ptr(p.GetEnableFullKubernetesAddr()),
		EnableEmbeddedServingMode:   utils.Ptr(p.GetEnableEmbeddedServingMode()),
		Components: rwv1alpha1.RisingWaveComponentsSpec{
			Standalone: standaloneComponent,
			Meta:       metaSpec,
			Frontend:   frontendSpec,
			Compute:    computeSpec,
			Compactor:  compactorSpec,
		},
		EnableStandaloneMode: &isStandalone,
		LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
			SecretName: p.GetLicenseKey().GetSecretName(),
		},
		SecretStore: FromSecretStoreProto(p.GetSecretStore()),
	}, nil
}

func ToRisingWaveSpecProto(s *rwv1alpha1.RisingWaveSpec) (*pbrw.RisingWaveSpec, error) {
	frontendServiceType, err := ToServiceTypeProto(s.FrontendServiceType)
	if err != nil {
		return nil, err
	}
	stateStore, err := ToStateStoreProto(s.StateStore)
	if err != nil {
		return nil, err
	}

	metaStore, err := ToRisingWaveMetaStoreProto(s.MetaStore)
	if err != nil {
		return nil, err
	}

	metaSpec, err := ToComponentSpecProto(s.Components.Meta)
	if err != nil {
		return nil, err
	}

	frontendSpec, err := ToComponentSpecProto(s.Components.Frontend)
	if err != nil {
		return nil, err
	}

	computeSpec, err := ToComponentSpecProto(s.Components.Compute)
	if err != nil {
		return nil, err
	}

	var computeConfig *pbrw.Config
	if len(s.Components.Compute.NodeGroups) != 0 && s.Components.Compute.NodeGroups[0].Configuration != nil {
		computeConfig = ToRisingWaveConfigProto(rwv1alpha1.RisingWaveConfigurationSpec{
			RisingWaveNodeConfiguration: *s.Components.Compute.NodeGroups[0].Configuration,
		})
	}

	compactorSpec, err := ToComponentSpecProto(s.Components.Compactor)
	if err != nil {
		return nil, err
	}

	enableDefaultServiceMonitor := false
	if s.EnableDefaultServiceMonitor != nil {
		enableDefaultServiceMonitor = *s.EnableDefaultServiceMonitor
	}

	enableFullKubernetesAddr := false
	if s.EnableFullKubernetesAddr != nil {
		enableFullKubernetesAddr = *s.EnableFullKubernetesAddr
	}

	standaloneComponent := &pbrw.StandaloneSpec{}
	if s.Components.Standalone != nil {
		standaloneComponent, err = ToStandaloneSpecProto(*s.Components.Standalone)
		if err != nil {
			return nil, err
		}
	}

	licenseKey := &pbrw.LicenseKey{}
	if s.LicenseKey != nil {
		licenseKey.SecretName = s.LicenseKey.SecretName
	}

	return &pbrw.RisingWaveSpec{
		Config:                      ToRisingWaveConfigProto(s.Configuration),
		ComputeConfig:               computeConfig,
		MetaStoreSpec:               metaStore,
		StateStore:                  stateStore,
		Image:                       s.Image,
		FrontendServiceType:         frontendServiceType,
		EnableDefaultServiceMonitor: enableDefaultServiceMonitor,
		EnableFullKubernetesAddr:    enableFullKubernetesAddr,
		EnableEmbeddedServingMode:   ptr.Deref(s.EnableEmbeddedServingMode, false),
		Components: &pbrw.ComponentsSpec{
			MetaSpec:            metaSpec,
			FrontendSpec:        frontendSpec,
			ComputeSpec:         computeSpec,
			CompactorSpec:       compactorSpec,
			StandaloneComponent: standaloneComponent,
		},
		EnableStandaloneMode: ptr.Deref(s.EnableStandaloneMode, false),
		LicenseKey:           licenseKey,
		SecretStore:          ToSecretStoreProto(s.SecretStore),
	}, nil
}

func FromRisingWaveConfigProto(p *pbrw.Config) rwv1alpha1.RisingWaveConfigurationSpec {
	return rwv1alpha1.RisingWaveConfigurationSpec{
		RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
			ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
				Name: p.GetNodeConfig().GetNodeConfigurationConfigMap().GetName(),
				Key:  p.GetNodeConfig().GetNodeConfigurationConfigMap().GetKey(),
			},
		},
	}
}

func FromRisingWaveNodeConfigProto(p *pbrw.NodeConfig) *rwv1alpha1.RisingWaveNodeConfiguration {
	res := &rwv1alpha1.RisingWaveNodeConfiguration{}
	if p.GetNodeConfigurationConfigMap() != nil {
		res.ConfigMap = &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
			Name: p.GetNodeConfigurationConfigMap().GetName(),
			Key:  p.GetNodeConfigurationConfigMap().GetKey(),
		}
	}
	return res
}

func ToRisingWaveConfigProto(s rwv1alpha1.RisingWaveConfigurationSpec) *pbrw.Config {
	return &pbrw.Config{
		NodeConfig: &pbrw.NodeConfig{
			NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
				Name: s.ConfigMap.Name,
				Key:  s.ConfigMap.Key,
			},
		},
	}
}

func ToRisingWaveNodeConfigProto(s *rwv1alpha1.RisingWaveNodeConfiguration) *pbrw.NodeConfig {
	return &pbrw.NodeConfig{
		NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
			Name: s.ConfigMap.Name,
			Key:  s.ConfigMap.Key,
		},
	}
}

func FromRisingWaveMetaStoreProto(p *pbrw.MetaStoreSpec) (rwv1alpha1.RisingWaveMetaStoreBackend, error) {
	if p.GetEtcdBackend() == nil && p.GetPostgresqlBackend() == nil {
		return rwv1alpha1.RisingWaveMetaStoreBackend{}, eris.New("meta store is not set").WithCode(eris.CodeInvalidArgument)
	}
	if p.GetEtcdBackend() != nil && p.GetPostgresqlBackend() != nil {
		return rwv1alpha1.RisingWaveMetaStoreBackend{}, eris.New("meta store has multiple values").WithCode(eris.CodeInvalidArgument)
	}
	if p.GetEtcdBackend() != nil {
		return rwv1alpha1.RisingWaveMetaStoreBackend{
			Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
				Endpoint: p.GetEtcdBackend().GetEndpoint(),
			},
		}, nil
	}
	if p.GetPostgresqlBackend() != nil {
		postgresql := p.GetPostgresqlBackend()
		return rwv1alpha1.RisingWaveMetaStoreBackend{
			PostgreSQL: &rwv1alpha1.RisingWaveMetaStoreBackendPostgreSQL{
				RisingWaveDBCredentials: rwv1alpha1.RisingWaveDBCredentials{
					SecretName:     postgresql.GetCredentials().GetSecretName(),
					UsernameKeyRef: postgresql.GetCredentials().GetUsernameKeyRef(),
					PasswordKeyRef: postgresql.GetCredentials().GetPasswordKeyRef(),
				},
				Host:     postgresql.GetHost(),
				Port:     postgresql.GetPort(),
				Database: postgresql.GetDatabase(),
				Options:  postgresql.GetOptions(),
			},
		}, nil
	}
	return rwv1alpha1.RisingWaveMetaStoreBackend{}, eris.New("invalid meta store type").WithCode(eris.CodeInvalidArgument)
}

func ToRisingWaveMetaStoreProto(ms rwv1alpha1.RisingWaveMetaStoreBackend) (*pbrw.MetaStoreSpec, error) {
	if ms.Etcd != nil {
		return &pbrw.MetaStoreSpec{
			EtcdBackend: &pbrw.MetaStoreBackendEtcd{
				Endpoint: ms.Etcd.Endpoint,
			},
		}, nil
	}
	if ms.PostgreSQL != nil {
		return &pbrw.MetaStoreSpec{
			PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
				Credentials: &pbrw.PostgreSqlCredentials{
					SecretName:     ms.PostgreSQL.SecretName,
					UsernameKeyRef: ms.PostgreSQL.UsernameKeyRef,
					PasswordKeyRef: ms.PostgreSQL.PasswordKeyRef,
				},
				Host:     ms.PostgreSQL.Host,
				Port:     ms.PostgreSQL.Port,
				Database: ms.PostgreSQL.Database,
				Options:  ms.PostgreSQL.Options,
			},
		}, nil
	}
	return nil, eris.Errorf("invalid meta store type: %v", ms).WithCode(eris.CodeInvalidArgument)
}

func FromStateStoreProto(p *pbrw.StateStoreSpec) (rwv1alpha1.RisingWaveStateStoreBackend, error) {
	s := rwv1alpha1.RisingWaveStateStoreBackend{
		DataDirectory: p.GetDataDirectory(),
	}
	switch storeType := p.GetBackend().(type) {
	case *pbrw.StateStoreSpec_S3StateStore:
		s.S3 = &rwv1alpha1.RisingWaveStateStoreBackendS3{
			RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
				UseServiceAccount: utils.Ptr(true),
			},
			Bucket: p.GetS3StateStore().GetBucket(),
			Region: p.GetS3StateStore().GetRegion(),
		}
	case *pbrw.StateStoreSpec_GcsStateStore:
		s.GCS = &rwv1alpha1.RisingWaveStateStoreBackendGCS{
			Bucket: p.GetGcsStateStore().GetBucket(),
			Root:   p.GetDataDirectory(),
			RisingWaveGCSCredentials: rwv1alpha1.RisingWaveGCSCredentials{
				UseWorkloadIdentity: utils.Ptr(true),
			},
		}
	case *pbrw.StateStoreSpec_AzblobStateStore:
		s.AzureBlob = &rwv1alpha1.RisingWaveStateStoreBackendAzureBlob{
			Container: p.GetAzblobStateStore().GetContainer(),
			Root:      p.GetDataDirectory(),
			Endpoint:  p.GetAzblobStateStore().GetEndpoint(),
			RisingWaveAzureBlobCredentials: rwv1alpha1.RisingWaveAzureBlobCredentials{
				UseServiceAccount: utils.Ptr(true),
			},
		}
	case *pbrw.StateStoreSpec_MemoryStateStore:
		s.Memory = utils.Ptr(true)
	case *pbrw.StateStoreSpec_LocalDiskStateStore:
		s.LocalDisk = &rwv1alpha1.RisingWaveStateStoreBackendLocalDisk{
			Root: p.GetLocalDiskStateStore().GetRoot(),
		}
	default:
		return rwv1alpha1.RisingWaveStateStoreBackend{}, eris.Errorf("invalid state store type: %v", storeType).WithCode(eris.CodeInvalidArgument)
	}
	return s, nil
}

func ToStateStoreProto(ss rwv1alpha1.RisingWaveStateStoreBackend) (*pbrw.StateStoreSpec, error) {
	p := &pbrw.StateStoreSpec{
		DataDirectory: ss.DataDirectory,
	}
	if ss.S3 != nil {
		p.Backend = &pbrw.StateStoreSpec_S3StateStore{
			S3StateStore: &pbrw.StateStoreBackendS3{
				Bucket: ss.S3.Bucket,
				Region: ss.S3.Region,
			},
		}
		return p, nil
	}
	if ss.GCS != nil {
		p.Backend = &pbrw.StateStoreSpec_GcsStateStore{
			GcsStateStore: &pbrw.StateStoreBackendGCS{
				Bucket: ss.GCS.Bucket,
			},
		}
		return p, nil
	}
	if ss.AzureBlob != nil {
		p.Backend = &pbrw.StateStoreSpec_AzblobStateStore{
			AzblobStateStore: &pbrw.StateStoreBackendAzblob{
				Container: ss.AzureBlob.Container,
				Endpoint:  ss.AzureBlob.Endpoint,
			},
		}
		return p, nil
	}
	if ss.LocalDisk != nil {
		p.Backend = &pbrw.StateStoreSpec_LocalDiskStateStore{
			LocalDiskStateStore: &pbrw.StateStoreBackendLocalDisk{
				Root: ss.LocalDisk.Root,
			},
		}
		return p, nil
	}
	return nil, eris.Errorf("invalid state store type: %v", ss).WithCode(eris.CodeInvalidArgument)
}

func FromComponentSpecProto(p *pbrw.ComponentSpec) (rwv1alpha1.RisingWaveComponent, error) {
	var nodeGroups []rwv1alpha1.RisingWaveNodeGroup
	for _, ngp := range p.GetNodeGroups() {
		ng, err := FromNodeGroupSpecProto(ngp)
		if err != nil {
			return rwv1alpha1.RisingWaveComponent{}, err
		}
		nodeGroups = append(nodeGroups, ng)
	}
	return rwv1alpha1.RisingWaveComponent{
		LogLevel:   p.GetLogLevel(),
		NodeGroups: nodeGroups,
	}, nil
}

func ToComponentSpecProto(c rwv1alpha1.RisingWaveComponent) (*pbrw.ComponentSpec, error) {
	var nodeGroups []*pbrw.NodeGroupSpec
	for _, ngp := range c.NodeGroups {
		ng, err := ToNodeGroupSpecProto(ngp)
		if err != nil {
			return nil, err
		}
		nodeGroups = append(nodeGroups, ng)
	}

	return &pbrw.ComponentSpec{
		LogLevel:   c.LogLevel,
		NodeGroups: nodeGroups,
	}, nil
}

func ToStandaloneSpecProto(c rwv1alpha1.RisingWaveStandaloneComponent) (*pbrw.StandaloneSpec, error) {
	nodePodSpec, err := ToNodePodSpecProto(c.Template)
	if err != nil {
		return nil, err
	}

	upStrategy, err := ToNodeGroupUpgradeStrategyProto(c.UpgradeStrategy)
	if err != nil {
		return nil, err
	}

	return &pbrw.StandaloneSpec{
		LogLevel:        c.LogLevel,
		UpgradeStrategy: upStrategy,
		Replicas:        uint32(c.Replicas),
		NodePodSpec:     nodePodSpec,
	}, nil
}

func FromStandaloneSpecProto(s *pbrw.StandaloneSpec) (*rwv1alpha1.RisingWaveStandaloneComponent, error) {
	if s == nil {
		return nil, fmt.Errorf("standalone component is nil")
	}

	upStrategy, err := FromNodeGroupUpgradeStrategyProto(s.GetUpgradeStrategy())
	if err != nil {
		return nil, err
	}

	nodePodSpec, err := FromNodePodSpecProto(s.GetNodePodSpec())
	if err != nil {
		return nil, err
	}

	return &rwv1alpha1.RisingWaveStandaloneComponent{
		LogLevel:        s.GetLogLevel(),
		UpgradeStrategy: upStrategy,
		Template:        nodePodSpec,
		Replicas:        int32(s.GetReplicas()),
	}, nil
}

func FromNodeGroupSpecProto(p *pbrw.NodeGroupSpec) (rwv1alpha1.RisingWaveNodeGroup, error) {
	upgradeStrategy, err := FromNodeGroupUpgradeStrategyProto(p.GetUpgradeStrategy())
	if err != nil {
		return rwv1alpha1.RisingWaveNodeGroup{}, err
	}

	nodePodSpec, err := FromNodePodSpecProto(p.GetNodePodSpec())
	if err != nil {
		return rwv1alpha1.RisingWaveNodeGroup{}, err
	}

	volumeClaimTemplates, err := FromPersistentVolumeClaimsProto(p.GetVolumeClaimTemplates())
	if err != nil {
		return rwv1alpha1.RisingWaveNodeGroup{}, err
	}

	var nodeConfig *rwv1alpha1.RisingWaveNodeConfiguration
	if p.GetNodeConfig() != nil {
		nodeConfig = FromRisingWaveNodeConfigProto(p.GetNodeConfig())
	}

	persistentVolumeClaimRetentionPolicy, err := FromPersistentVolumeRetentionPolicyProto(p.GetPersistentVolumeClaimRetentionPolicy())
	if err != nil {
		return rwv1alpha1.RisingWaveNodeGroup{}, err
	}

	return rwv1alpha1.RisingWaveNodeGroup{
		Name:                                 p.GetName(),
		Replicas:                             int32(p.GetReplicas()),
		UpgradeStrategy:                      upgradeStrategy,
		VolumeClaimTemplates:                 volumeClaimTemplates,
		Template:                             nodePodSpec,
		PersistentVolumeClaimRetentionPolicy: persistentVolumeClaimRetentionPolicy,
		Configuration:                        nodeConfig,
	}, nil
}

func ToNodeGroupSpecProto(ng rwv1alpha1.RisingWaveNodeGroup) (*pbrw.NodeGroupSpec, error) {
	upgradeStrategy, err := ToNodeGroupUpgradeStrategyProto(ng.UpgradeStrategy)
	if err != nil {
		return nil, err
	}

	nodePodSpec, err := ToNodePodSpecProto(ng.Template)
	if err != nil {
		return nil, err
	}

	volumeClaimTemplates, err := ToRWPersistentVolumeClaimsProto(ng.VolumeClaimTemplates)
	if err != nil {
		return nil, err
	}

	persistentVolumeClaimRetentionPolicy, err := ToPersistentVolumeRetentionPolicyProto(ng.PersistentVolumeClaimRetentionPolicy)
	if err != nil {
		return nil, err
	}

	var nodeConfig *pbrw.NodeConfig
	if ng.Configuration != nil {
		nodeConfig = ToRisingWaveNodeConfigProto(ng.Configuration)
	}

	return &pbrw.NodeGroupSpec{
		Name:                                 ng.Name,
		Replicas:                             uint32(ng.Replicas),
		UpgradeStrategy:                      upgradeStrategy,
		NodePodSpec:                          nodePodSpec,
		VolumeClaimTemplates:                 volumeClaimTemplates,
		PersistentVolumeClaimRetentionPolicy: persistentVolumeClaimRetentionPolicy,
		NodeConfig:                           nodeConfig,
	}, nil
}

func FromNodePodSpecProto(p *pbrw.NodePodSpec) (rwv1alpha1.RisingWaveNodePodTemplate, error) {
	var tolerations []corev1.Toleration
	for _, tp := range p.GetTolerations() {
		t, err := FromTolerationProto(tp)
		if err != nil {
			return rwv1alpha1.RisingWaveNodePodTemplate{}, err
		}
		tolerations = append(tolerations, t)
	}

	var err error
	var affinity *corev1.Affinity
	if p.GetAffinity() != nil {
		affinity, err = FromAffinityProto(p.GetAffinity())
		if err != nil {
			return rwv1alpha1.RisingWaveNodePodTemplate{}, err
		}
	}

	nodeContainerSpec, err := FromNodePodContainerSpecProto(p.GetContainerSpec())
	if err != nil {
		return rwv1alpha1.RisingWaveNodePodTemplate{}, err
	}

	var volumes []corev1.Volume
	for _, volumeProto := range p.GetVolumes() {
		v, err := FromVolumeProto(volumeProto)
		if err != nil {
			return rwv1alpha1.RisingWaveNodePodTemplate{}, err
		}
		volumes = append(volumes, v)
	}

	return rwv1alpha1.RisingWaveNodePodTemplate{
		ObjectMeta: rwv1alpha1.PartialObjectMeta{
			Labels:      p.GetLabels(),
			Annotations: p.GetAnnotations(),
		},
		Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
			Tolerations:             tolerations,
			Affinity:                affinity,
			RisingWaveNodeContainer: nodeContainerSpec,
			ServiceAccountName:      p.GetServiceAccount(),
			Volumes:                 volumes,
		},
	}, nil
}

func ToNodePodSpecProto(t rwv1alpha1.RisingWaveNodePodTemplate) (*pbrw.NodePodSpec, error) {
	var tolerations []*pbk8s.Toleration
	for _, tp := range t.Spec.Tolerations {
		t, err := ToTolerationProto(tp)
		if err != nil {
			return nil, err
		}
		tolerations = append(tolerations, t)
	}

	var err error
	var affinity *pbk8s.Affinity
	if t.Spec.Affinity != nil {
		affinity, err = ToAffinityProto(t.Spec.Affinity)
		if err != nil {
			return nil, err
		}
	}

	nodeContainerSpec, err := ToNodePodContainerSpecProto(t.Spec.RisingWaveNodeContainer)
	if err != nil {
		return nil, err
	}

	var volumes []*pbk8s.Volume
	for _, v := range t.Spec.Volumes {
		p, err := ToVolumeProto(v)
		if err != nil {
			return nil, err
		}
		volumes = append(volumes, p)
	}

	return &pbrw.NodePodSpec{
		Labels:         t.ObjectMeta.Labels,
		Tolerations:    tolerations,
		Affinity:       affinity,
		ContainerSpec:  nodeContainerSpec,
		ServiceAccount: t.Spec.ServiceAccountName,
		Volumes:        volumes,
		Annotations:    t.ObjectMeta.Annotations,
	}, nil
}

func FromNodePodContainerSpecProto(p *pbrw.NodePodContainerSpec) (rwv1alpha1.RisingWaveNodeContainer, error) {
	resources, err := FromResourceRequirementsProto(p.GetResources())
	if err != nil {
		return rwv1alpha1.RisingWaveNodeContainer{}, err
	}
	var envs []corev1.EnvVar
	for k, v := range p.GetEnvs() {
		envs = append(envs, corev1.EnvVar{
			Name:  k,
			Value: v,
		})
	}
	var volumeMounts []corev1.VolumeMount
	for _, vm := range p.GetVolumeMounts() {
		volumeMounts = append(volumeMounts, FromVolumeMountProto(vm))
	}
	return rwv1alpha1.RisingWaveNodeContainer{
		Resources:    *resources,
		Env:          envs,
		VolumeMounts: volumeMounts,
	}, nil
}

func ToNodePodContainerSpecProto(c rwv1alpha1.RisingWaveNodeContainer) (*pbrw.NodePodContainerSpec, error) {
	resources, err := ToResourceRequirementsProto(&c.Resources)
	envs := make(map[string]string)
	if err != nil {
		return nil, err
	}
	for _, e := range c.Env {
		envs[e.Name] = e.Value
	}
	var volumeMounts []*pbk8s.VolumeMount
	for _, vm := range c.VolumeMounts {
		volumeMounts = append(volumeMounts, ToVolumeMountProto(vm))
	}
	return &pbrw.NodePodContainerSpec{
		Resources:    resources,
		Envs:         envs,
		VolumeMounts: volumeMounts,
	}, nil
}

func FromNodeGroupUpgradeStrategyProto(p *pbrw.NodeGroupUpgradeStrategy) (rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy, error) {
	upgradeType, err := FromNodeGroupUpgradeStrategyTypeProto(p.GetType())
	if err != nil {
		return rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{}, err
	}
	return rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
		Type: upgradeType,
	}, nil
}

func ToNodeGroupUpgradeStrategyProto(s rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy) (*pbrw.NodeGroupUpgradeStrategy, error) {
	upgradeType, err := ToNodeGroupUpgradeStrategyTypeProto(s.Type)
	if err != nil {
		return nil, err
	}
	return &pbrw.NodeGroupUpgradeStrategy{
		Type: upgradeType,
	}, nil
}

func FromNodeGroupUpgradeStrategyTypeProto(p pbrw.UpgradeStrategyType) (rwv1alpha1.RisingWaveNodeGroupUpgradeStrategyType, error) {
	switch p {
	case pbrw.UpgradeStrategyType_ROLLING_UPDATE:
		return rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate, nil
	case pbrw.UpgradeStrategyType_RECREATE:
		return rwv1alpha1.RisingWaveUpgradeStrategyTypeRecreate, nil
	case pbrw.UpgradeStrategyType_IN_PLACE_ONLY:
		return rwv1alpha1.RisingWaveUpgradeStrategyTypeInPlaceOnly, nil
	case pbrw.UpgradeStrategyType_IN_PLACE_IF_POSSIBLE:
		return rwv1alpha1.RisingWaveUpgradeStrategyTypeInPlaceIfPossible, nil
	case pbrw.UpgradeStrategyType_UNKNOWN_STRATEGY:
		return "", eris.Errorf("invalid upgrade type %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToNodeGroupUpgradeStrategyTypeProto(t rwv1alpha1.RisingWaveNodeGroupUpgradeStrategyType) (pbrw.UpgradeStrategyType, error) {
	switch t {
	case rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate:
		return pbrw.UpgradeStrategyType_ROLLING_UPDATE, nil
	case rwv1alpha1.RisingWaveUpgradeStrategyTypeRecreate:
		return pbrw.UpgradeStrategyType_RECREATE, nil
	case rwv1alpha1.RisingWaveUpgradeStrategyTypeInPlaceOnly:
		return pbrw.UpgradeStrategyType_IN_PLACE_ONLY, nil
	case rwv1alpha1.RisingWaveUpgradeStrategyTypeInPlaceIfPossible:
		return pbrw.UpgradeStrategyType_IN_PLACE_IF_POSSIBLE, nil
	}
	return pbrw.UpgradeStrategyType_UNKNOWN_STRATEGY, eris.Errorf("invalid upgrade type %v", t).WithCode(eris.CodeInvalidArgument)
}

func FromPersistentVolumeClaimPartialObjectMetaProto(p *pbrw.PersistentVolumeClaimPartialObjectMeta) rwv1alpha1.PersistentVolumeClaimPartialObjectMeta {
	return rwv1alpha1.PersistentVolumeClaimPartialObjectMeta{
		Name: p.GetName(),
	}
}

func ToPersistentVolumeClaimPartialObjectMetaProto(m rwv1alpha1.PersistentVolumeClaimPartialObjectMeta) *pbrw.PersistentVolumeClaimPartialObjectMeta {
	return &pbrw.PersistentVolumeClaimPartialObjectMeta{
		Name: m.Name,
	}
}

func FromPersistentVolumeClaimsProto(p []*pbrw.PersistentVolumeClaim) ([]rwv1alpha1.PersistentVolumeClaim, error) {
	var volumeClaimTemplates []rwv1alpha1.PersistentVolumeClaim
	for _, volumeClaimProto := range p {
		volumeClaim, err := FromRWPersistentVolumeClaimProto(volumeClaimProto)
		if err != nil {
			return nil, err
		}
		volumeClaimTemplates = append(volumeClaimTemplates, volumeClaim)
	}
	return volumeClaimTemplates, nil
}

func ToRWPersistentVolumeClaimsProto(pvc []rwv1alpha1.PersistentVolumeClaim) ([]*pbrw.PersistentVolumeClaim, error) {
	var volumeClaimTemplates []*pbrw.PersistentVolumeClaim
	for _, volumeClaim := range pvc {
		volumeClaimProto, err := ToRWPersistentVolumeClaimProto(volumeClaim)
		if err != nil {
			return nil, err
		}
		volumeClaimTemplates = append(volumeClaimTemplates, volumeClaimProto)
	}
	return volumeClaimTemplates, nil
}

func FromRWPersistentVolumeClaimProto(p *pbrw.PersistentVolumeClaim) (rwv1alpha1.PersistentVolumeClaim, error) {
	spec, err := FromPersistentVolumeClaimSpecProto(p.GetSpec())
	if err != nil {
		return rwv1alpha1.PersistentVolumeClaim{}, err
	}
	return rwv1alpha1.PersistentVolumeClaim{
		PersistentVolumeClaimPartialObjectMeta: FromPersistentVolumeClaimPartialObjectMetaProto(p.GetMetadata()),
		Spec:                                   spec,
	}, nil
}

func ToRWPersistentVolumeClaimProto(pvc rwv1alpha1.PersistentVolumeClaim) (*pbrw.PersistentVolumeClaim, error) {
	spec, err := ToPersistentVolumeClaimSpecProto(pvc.Spec)
	if err != nil {
		return nil, err
	}
	return &pbrw.PersistentVolumeClaim{
		Metadata: ToPersistentVolumeClaimPartialObjectMetaProto(pvc.PersistentVolumeClaimPartialObjectMeta),
		Spec:     spec,
	}, nil
}

func FromPersistentVolumeRetentionPolicyProto(p *pbrw.PersistentVolumeClaimRetentionPolicy) (*v1.StatefulSetPersistentVolumeClaimRetentionPolicy, error) {
	if p == nil {
		return nil, nil
	}

	whenDeleted, err := FromPersistentVolumeRetentionPolicyTypeProto(p.GetWhenDeleted())
	if err != nil {
		return nil, err
	}

	whenScaled, err := FromPersistentVolumeRetentionPolicyTypeProto(p.GetWhenScaled())
	if err != nil {
		return nil, err
	}

	return &v1.StatefulSetPersistentVolumeClaimRetentionPolicy{
		WhenDeleted: whenDeleted,
		WhenScaled:  whenScaled,
	}, nil
}

func ToPersistentVolumeRetentionPolicyProto(s *v1.StatefulSetPersistentVolumeClaimRetentionPolicy) (*pbrw.PersistentVolumeClaimRetentionPolicy, error) {
	if s == nil {
		return nil, nil
	}

	whenDeleted, err := ToPersistentVolumeRetentionPolicyTypeProto(s.WhenDeleted)
	if err != nil {
		return nil, err
	}

	whenScaled, err := ToPersistentVolumeRetentionPolicyTypeProto(s.WhenScaled)
	if err != nil {
		return nil, err
	}

	return &pbrw.PersistentVolumeClaimRetentionPolicy{
		WhenDeleted: whenDeleted,
		WhenScaled:  whenScaled,
	}, nil
}

func FromPersistentVolumeRetentionPolicyTypeProto(p pbrw.PersistentVolumeClaimRetentionPolicyType) (v1.PersistentVolumeClaimRetentionPolicyType, error) {
	switch p {
	case pbrw.PersistentVolumeClaimRetentionPolicyType_DELETE:
		return v1.DeletePersistentVolumeClaimRetentionPolicyType, nil
	case pbrw.PersistentVolumeClaimRetentionPolicyType_RETAIN:
		return v1.RetainPersistentVolumeClaimRetentionPolicyType, nil
	case pbrw.PersistentVolumeClaimRetentionPolicyType_UNKNOWN:
		return "", eris.Errorf("invalid persistent volume retention policy type %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToPersistentVolumeRetentionPolicyTypeProto(t v1.PersistentVolumeClaimRetentionPolicyType) (pbrw.PersistentVolumeClaimRetentionPolicyType, error) {
	switch t {
	case v1.DeletePersistentVolumeClaimRetentionPolicyType:
		return pbrw.PersistentVolumeClaimRetentionPolicyType_DELETE, nil
	case v1.RetainPersistentVolumeClaimRetentionPolicyType:
		return pbrw.PersistentVolumeClaimRetentionPolicyType_RETAIN, nil
	}
	return pbrw.PersistentVolumeClaimRetentionPolicyType_UNKNOWN, eris.Errorf("invalid persistent volume claim retention policy type %v", t).WithCode(eris.CodeInvalidArgument)
}

func FromSecretStoreProto(p *pbrw.SecretStore) rwv1alpha1.RisingWaveSecretStore {
	s := rwv1alpha1.RisingWaveSecretStore{}
	if p == nil || p.GetPrivateKey() == nil {
		return s
	}

	if p.GetPrivateKey().GetValue() != "" {
		s.PrivateKey = rwv1alpha1.RisingWaveSecretStorePrivateKey{
			Value: ptr.To(p.GetPrivateKey().GetValue()),
		}
	} else if p.GetPrivateKey().GetSecretRef() != nil {
		secretRef := p.GetPrivateKey().GetSecretRef()
		s.PrivateKey = rwv1alpha1.RisingWaveSecretStorePrivateKey{
			SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
				Name: secretRef.GetName(),
				Key:  secretRef.GetKey(),
			},
		}
	}

	return s
}

func ToSecretStoreProto(ss rwv1alpha1.RisingWaveSecretStore) *pbrw.SecretStore {
	p := &pbrw.SecretStore{}

	if ss.PrivateKey.Value != nil {
		p.PrivateKey = &pbrw.SecretStorePrivateKey{
			Value: ss.PrivateKey.Value,
		}
	} else if ss.PrivateKey.SecretRef != nil {
		secretRef := ss.PrivateKey.SecretRef
		p.PrivateKey = &pbrw.SecretStorePrivateKey{
			SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
				Key:  secretRef.Key,
				Name: secretRef.Name,
			},
		}
	}
	return p
}
