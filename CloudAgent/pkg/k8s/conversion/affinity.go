package conversion

import (
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func FromAffinityProto(p *pbk8s.Affinity) (*corev1.Affinity, error) {
	var err error
	var nodeAffinity *corev1.NodeAffinity
	if p.GetNodeAffinity() != nil {
		nodeAffinity, err = FromNodeAffinityProto(p.GetNodeAffinity())
		if err != nil {
			return nil, err
		}
	}
	var podAffinity *corev1.PodAffinity
	if p.GetPodAffinity() != nil {
		podAffinity, err = FromPodAffinityProto(p.GetPodAffinity())
		if err != nil {
			return nil, err
		}
	}
	var podAntiAffinity *corev1.PodAntiAffinity
	if p.GetPodAntiAffinity() != nil {
		podAntiAffinity, err = FromPodAntiAffinityProto(p.GetPodAntiAffinity())
		if err != nil {
			return nil, err
		}
	}
	return &corev1.Affinity{
		NodeAffinity:    nodeAffinity,
		PodAffinity:     podAffinity,
		PodAntiAffinity: podAntiAffinity,
	}, nil
}

func ToAffinityProto(a *corev1.Affinity) (*pbk8s.Affinity, error) {
	var err error
	var nodeAffinity *pbk8s.NodeAffinity
	if a.NodeAffinity != nil {
		nodeAffinity, err = ToNodeAffinityProto(a.NodeAffinity)
		if err != nil {
			return nil, err
		}
	}
	var podAffinity *pbk8s.PodAffinity
	if a.PodAffinity != nil {
		podAffinity, err = ToPodAffinityProto(a.PodAffinity)
		if err != nil {
			return nil, err
		}
	}
	var podAntiAffinity *pbk8s.PodAntiAffinity
	if a.PodAntiAffinity != nil {
		podAntiAffinity, err = ToPodAntiAffinityProto(a.PodAntiAffinity)
		if err != nil {
			return nil, err
		}
	}
	return &pbk8s.Affinity{
		NodeAffinity:    nodeAffinity,
		PodAffinity:     podAffinity,
		PodAntiAffinity: podAntiAffinity,
	}, nil
}

func FromNodeAffinityProto(p *pbk8s.NodeAffinity) (*corev1.NodeAffinity, error) {
	var err error
	var requiredDuringSchedulingIgnoredDuringExecution *corev1.NodeSelector
	if p.GetRequiredDuringSchedulingIgnoredDuringExecution() != nil {
		requiredDuringSchedulingIgnoredDuringExecution, err = FromNodeSelectorProto(p.GetRequiredDuringSchedulingIgnoredDuringExecution())
		if err != nil {
			return nil, err
		}
	}

	var preferredDuringSchedulingIgnoredDuringExecution []corev1.PreferredSchedulingTerm
	for _, tp := range p.GetPreferredDuringSchedulingIgnoredDuringExecution() {
		term, err := FromPreferredSchedulingTermProto(tp)
		if err != nil {
			return nil, err
		}
		preferredDuringSchedulingIgnoredDuringExecution = append(preferredDuringSchedulingIgnoredDuringExecution, term)
	}
	return &corev1.NodeAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution:  requiredDuringSchedulingIgnoredDuringExecution,
		PreferredDuringSchedulingIgnoredDuringExecution: preferredDuringSchedulingIgnoredDuringExecution,
	}, nil
}

func ToNodeAffinityProto(a *corev1.NodeAffinity) (*pbk8s.NodeAffinity, error) {
	var err error
	var requiredDuringSchedulingIgnoredDuringExecution *pbk8s.NodeSelector
	if a.RequiredDuringSchedulingIgnoredDuringExecution != nil {
		requiredDuringSchedulingIgnoredDuringExecution, err = ToNodeSelectorProto(a.RequiredDuringSchedulingIgnoredDuringExecution)
		if err != nil {
			return nil, err
		}
	}

	var preferredDuringSchedulingIgnoredDuringExecution []*pbk8s.PreferredSchedulingTerm
	for _, tp := range a.PreferredDuringSchedulingIgnoredDuringExecution {
		term, err := ToPreferredSchedulingTermProto(tp)
		if err != nil {
			return nil, err
		}
		preferredDuringSchedulingIgnoredDuringExecution = append(preferredDuringSchedulingIgnoredDuringExecution, term)
	}
	return &pbk8s.NodeAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution:  requiredDuringSchedulingIgnoredDuringExecution,
		PreferredDuringSchedulingIgnoredDuringExecution: preferredDuringSchedulingIgnoredDuringExecution,
	}, nil
}

func FromPodAffinityProto(p *pbk8s.PodAffinity) (*corev1.PodAffinity, error) {
	var requiredDuringSchedulingIgnoredDuringExecution []corev1.PodAffinityTerm
	for _, tp := range p.GetRequiredDuringSchedulingIgnoredDuringExecution() {
		term, err := FromPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		requiredDuringSchedulingIgnoredDuringExecution = append(requiredDuringSchedulingIgnoredDuringExecution, term)
	}

	var preferredDuringSchedulingIgnoredDuringExecution []corev1.WeightedPodAffinityTerm
	for _, tp := range p.GetPreferredDuringSchedulingIgnoredDuringExecution() {
		term, err := FromWeightedPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		preferredDuringSchedulingIgnoredDuringExecution = append(preferredDuringSchedulingIgnoredDuringExecution, term)
	}
	return &corev1.PodAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution:  requiredDuringSchedulingIgnoredDuringExecution,
		PreferredDuringSchedulingIgnoredDuringExecution: preferredDuringSchedulingIgnoredDuringExecution,
	}, nil
}

func ToPodAffinityProto(a *corev1.PodAffinity) (*pbk8s.PodAffinity, error) {
	var requiredDuringSchedulingIgnoredDuringExecution []*pbk8s.PodAffinityTerm
	for _, tp := range a.RequiredDuringSchedulingIgnoredDuringExecution {
		term, err := ToPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		requiredDuringSchedulingIgnoredDuringExecution = append(requiredDuringSchedulingIgnoredDuringExecution, term)
	}

	var preferredDuringSchedulingIgnoredDuringExecution []*pbk8s.WeightedPodAffinityTerm
	for _, tp := range a.PreferredDuringSchedulingIgnoredDuringExecution {
		term, err := ToWeightedPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		preferredDuringSchedulingIgnoredDuringExecution = append(preferredDuringSchedulingIgnoredDuringExecution, term)
	}
	return &pbk8s.PodAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution:  requiredDuringSchedulingIgnoredDuringExecution,
		PreferredDuringSchedulingIgnoredDuringExecution: preferredDuringSchedulingIgnoredDuringExecution,
	}, nil
}

func FromPodAntiAffinityProto(p *pbk8s.PodAntiAffinity) (*corev1.PodAntiAffinity, error) {
	var requiredDuringSchedulingIgnoredDuringExecution []corev1.PodAffinityTerm
	for _, tp := range p.GetRequiredDuringSchedulingIgnoredDuringExecution() {
		term, err := FromPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		requiredDuringSchedulingIgnoredDuringExecution = append(requiredDuringSchedulingIgnoredDuringExecution, term)
	}

	var preferredDuringSchedulingIgnoredDuringExecution []corev1.WeightedPodAffinityTerm
	for _, tp := range p.GetPreferredDuringSchedulingIgnoredDuringExecution() {
		term, err := FromWeightedPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		preferredDuringSchedulingIgnoredDuringExecution = append(preferredDuringSchedulingIgnoredDuringExecution, term)
	}
	return &corev1.PodAntiAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution:  requiredDuringSchedulingIgnoredDuringExecution,
		PreferredDuringSchedulingIgnoredDuringExecution: preferredDuringSchedulingIgnoredDuringExecution,
	}, nil
}

func ToPodAntiAffinityProto(a *corev1.PodAntiAffinity) (*pbk8s.PodAntiAffinity, error) {
	var requiredDuringSchedulingIgnoredDuringExecution []*pbk8s.PodAffinityTerm
	for _, tp := range a.RequiredDuringSchedulingIgnoredDuringExecution {
		term, err := ToPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		requiredDuringSchedulingIgnoredDuringExecution = append(requiredDuringSchedulingIgnoredDuringExecution, term)
	}

	var preferredDuringSchedulingIgnoredDuringExecution []*pbk8s.WeightedPodAffinityTerm
	for _, tp := range a.PreferredDuringSchedulingIgnoredDuringExecution {
		term, err := ToWeightedPodAffinityTermProto(tp)
		if err != nil {
			return nil, err
		}
		preferredDuringSchedulingIgnoredDuringExecution = append(preferredDuringSchedulingIgnoredDuringExecution, term)
	}
	return &pbk8s.PodAntiAffinity{
		RequiredDuringSchedulingIgnoredDuringExecution:  requiredDuringSchedulingIgnoredDuringExecution,
		PreferredDuringSchedulingIgnoredDuringExecution: preferredDuringSchedulingIgnoredDuringExecution,
	}, nil
}

func FromNodeSelectorProto(p *pbk8s.NodeSelector) (*corev1.NodeSelector, error) {
	var nodeSelectorTerms []corev1.NodeSelectorTerm
	for _, t := range p.GetNodeSelectorTerms() {
		nodeSelectorTerm, err := FromNodeSelectorTermProto(t)
		if err != nil {
			return nil, err
		}
		nodeSelectorTerms = append(nodeSelectorTerms, nodeSelectorTerm)
	}
	return &corev1.NodeSelector{
		NodeSelectorTerms: nodeSelectorTerms,
	}, nil
}

func ToNodeSelectorProto(s *corev1.NodeSelector) (*pbk8s.NodeSelector, error) {
	var nodeSelectorTerms []*pbk8s.NodeSelectorTerm
	for _, t := range s.NodeSelectorTerms {
		nodeSelectorTerm, err := ToNodeSelectorTermProto(t)
		if err != nil {
			return nil, err
		}
		nodeSelectorTerms = append(nodeSelectorTerms, nodeSelectorTerm)
	}
	return &pbk8s.NodeSelector{
		NodeSelectorTerms: nodeSelectorTerms,
	}, nil
}

func FromPreferredSchedulingTermProto(p *pbk8s.PreferredSchedulingTerm) (corev1.PreferredSchedulingTerm, error) {
	preference, err := FromNodeSelectorTermProto(p.GetPreference())
	if err != nil {
		return corev1.PreferredSchedulingTerm{}, err
	}
	return corev1.PreferredSchedulingTerm{
		Weight:     p.GetWeight(),
		Preference: preference,
	}, nil
}

func ToPreferredSchedulingTermProto(t corev1.PreferredSchedulingTerm) (*pbk8s.PreferredSchedulingTerm, error) {
	preference, err := ToNodeSelectorTermProto(t.Preference)
	if err != nil {
		return nil, err
	}
	return &pbk8s.PreferredSchedulingTerm{
		Weight:     t.Weight,
		Preference: preference,
	}, nil
}

func FromNodeSelectorTermProto(p *pbk8s.NodeSelectorTerm) (corev1.NodeSelectorTerm, error) {
	var matchExpressions []corev1.NodeSelectorRequirement
	for _, e := range p.GetMatchExpressions() {
		matchExpression, err := FromNodeSelectorRequirementProto(e)
		if err != nil {
			return corev1.NodeSelectorTerm{}, err
		}
		matchExpressions = append(matchExpressions, matchExpression)
	}
	var matchFields []corev1.NodeSelectorRequirement
	for _, e := range p.GetMatchFields() {
		matchField, err := FromNodeSelectorRequirementProto(e)
		if err != nil {
			return corev1.NodeSelectorTerm{}, err
		}
		matchFields = append(matchFields, matchField)
	}
	return corev1.NodeSelectorTerm{
		MatchExpressions: matchExpressions,
		MatchFields:      matchFields,
	}, nil
}

func ToNodeSelectorTermProto(t corev1.NodeSelectorTerm) (*pbk8s.NodeSelectorTerm, error) {
	var matchExpressions []*pbk8s.NodeSelectorRequirement
	for _, e := range t.MatchExpressions {
		matchExpression, err := ToNodeSelectorRequirementProto(e)
		if err != nil {
			return nil, err
		}
		matchExpressions = append(matchExpressions, matchExpression)
	}
	var matchFields []*pbk8s.NodeSelectorRequirement
	for _, e := range t.MatchFields {
		matchField, err := ToNodeSelectorRequirementProto(e)
		if err != nil {
			return nil, err
		}
		matchFields = append(matchFields, matchField)
	}
	return &pbk8s.NodeSelectorTerm{
		MatchExpressions: matchExpressions,
		MatchFields:      matchFields,
	}, nil
}

func FromNodeSelectorRequirementProto(p *pbk8s.NodeSelectorRequirement) (corev1.NodeSelectorRequirement, error) {
	op, err := FromNodeSelectorOperatorProto(p.GetOperator())
	if err != nil {
		return corev1.NodeSelectorRequirement{}, err
	}
	return corev1.NodeSelectorRequirement{
		Key:      p.GetKey(),
		Operator: op,
		Values:   p.GetValues(),
	}, nil
}

func ToNodeSelectorRequirementProto(r corev1.NodeSelectorRequirement) (*pbk8s.NodeSelectorRequirement, error) {
	op, err := ToNodeSelectorOperatorProto(r.Operator)
	if err != nil {
		return nil, err
	}
	return &pbk8s.NodeSelectorRequirement{
		Key:      r.Key,
		Operator: op,
		Values:   r.Values,
	}, nil
}

func FromNodeSelectorOperatorProto(p pbk8s.NodeSelectorOperator) (corev1.NodeSelectorOperator, error) {
	switch p {
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN:
		return corev1.NodeSelectorOpIn, nil
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN:
		return corev1.NodeSelectorOpNotIn, nil
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_EXISTS:
		return corev1.NodeSelectorOpExists, nil
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST:
		return corev1.NodeSelectorOpDoesNotExist, nil
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_GT:
		return corev1.NodeSelectorOpGt, nil
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_LT:
		return corev1.NodeSelectorOpLt, nil
	case pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_UNKNOWN:
		return "", eris.Errorf("invalid node selector operator %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToNodeSelectorOperatorProto(o corev1.NodeSelectorOperator) (pbk8s.NodeSelectorOperator, error) {
	switch o {
	case corev1.NodeSelectorOpIn:
		return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN, nil
	case corev1.NodeSelectorOpNotIn:
		return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN, nil
	case corev1.NodeSelectorOpExists:
		return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_EXISTS, nil
	case corev1.NodeSelectorOpDoesNotExist:
		return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST, nil
	case corev1.NodeSelectorOpGt:
		return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_GT, nil
	case corev1.NodeSelectorOpLt:
		return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_LT, nil
	}
	return pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_UNKNOWN, eris.Errorf("invalid node selector operator %v", o).WithCode(eris.CodeInvalidArgument)
}

func FromPodAffinityTermProto(p *pbk8s.PodAffinityTerm) (corev1.PodAffinityTerm, error) {
	var err error
	var labelSelector *metav1.LabelSelector
	if p.GetLabelSelector() != nil {
		labelSelector, err = FromLabelSelectorProto(p.GetLabelSelector())
		if err != nil {
			return corev1.PodAffinityTerm{}, err
		}
	}
	var namespaceSelector *metav1.LabelSelector
	if p.GetNamespaceSelector() != nil {
		namespaceSelector, err = FromLabelSelectorProto(p.GetNamespaceSelector())
		if err != nil {
			return corev1.PodAffinityTerm{}, err
		}
	}

	return corev1.PodAffinityTerm{
		LabelSelector:     labelSelector,
		Namespaces:        p.GetNamespaces(),
		TopologyKey:       p.GetTopologyKey(),
		NamespaceSelector: namespaceSelector,
	}, nil
}

func ToPodAffinityTermProto(t corev1.PodAffinityTerm) (*pbk8s.PodAffinityTerm, error) {
	var err error
	var labelSelector *pbk8s.LabelSelector
	if t.LabelSelector != nil {
		labelSelector, err = ToLabelSelectorProto(t.LabelSelector)
		if err != nil {
			return nil, err
		}
	}
	var namespaceSelector *pbk8s.LabelSelector
	if t.NamespaceSelector != nil {
		namespaceSelector, err = ToLabelSelectorProto(t.NamespaceSelector)
		if err != nil {
			return nil, err
		}
	}

	return &pbk8s.PodAffinityTerm{
		LabelSelector:     labelSelector,
		Namespaces:        t.Namespaces,
		TopologyKey:       t.TopologyKey,
		NamespaceSelector: namespaceSelector,
	}, nil
}

func FromWeightedPodAffinityTermProto(p *pbk8s.WeightedPodAffinityTerm) (corev1.WeightedPodAffinityTerm, error) {
	podAffinityTerm, err := FromPodAffinityTermProto(p.GetPodAffinityTerm())
	if err != nil {
		return corev1.WeightedPodAffinityTerm{}, err
	}
	return corev1.WeightedPodAffinityTerm{
		Weight:          p.GetWeight(),
		PodAffinityTerm: podAffinityTerm,
	}, nil
}

func ToWeightedPodAffinityTermProto(t corev1.WeightedPodAffinityTerm) (*pbk8s.WeightedPodAffinityTerm, error) {
	podAffinityTerm, err := ToPodAffinityTermProto(t.PodAffinityTerm)
	if err != nil {
		return nil, err
	}
	return &pbk8s.WeightedPodAffinityTerm{
		Weight:          t.Weight,
		PodAffinityTerm: podAffinityTerm,
	}, nil
}

func FromLabelSelectorProto(p *pbk8s.LabelSelector) (*metav1.LabelSelector, error) {
	var matchExpressions []metav1.LabelSelectorRequirement
	for _, e := range p.GetMatchExpressions() {
		matchExpression, err := FromLabelSelectorRequirementProto(e)
		if err != nil {
			return nil, err
		}
		matchExpressions = append(matchExpressions, matchExpression)
	}
	return &metav1.LabelSelector{
		MatchLabels:      p.GetMatchLabels(),
		MatchExpressions: matchExpressions,
	}, nil
}

func ToLabelSelectorProto(s *metav1.LabelSelector) (*pbk8s.LabelSelector, error) {
	var matchExpressions []*pbk8s.LabelSelectorRequirement
	for _, e := range s.MatchExpressions {
		matchExpression, err := ToLabelSelectorRequirementProto(e)
		if err != nil {
			return nil, err
		}
		matchExpressions = append(matchExpressions, matchExpression)
	}
	return &pbk8s.LabelSelector{
		MatchLabels:      s.MatchLabels,
		MatchExpressions: matchExpressions,
	}, nil
}

func FromLabelSelectorRequirementProto(p *pbk8s.LabelSelectorRequirement) (metav1.LabelSelectorRequirement, error) {
	op, err := FromLabelSelectorOperatorProto(p.GetOperator())
	if err != nil {
		return metav1.LabelSelectorRequirement{}, err
	}
	return metav1.LabelSelectorRequirement{
		Key:      p.GetKey(),
		Operator: op,
		Values:   p.GetValues(),
	}, nil
}

func ToLabelSelectorRequirementProto(r metav1.LabelSelectorRequirement) (*pbk8s.LabelSelectorRequirement, error) {
	op, err := ToLabelSelectorOperatorProto(r.Operator)
	if err != nil {
		return nil, err
	}
	return &pbk8s.LabelSelectorRequirement{
		Key:      r.Key,
		Operator: op,
		Values:   r.Values,
	}, nil
}

func FromLabelSelectorOperatorProto(p pbk8s.LabelSelectorOperator) (metav1.LabelSelectorOperator, error) {
	switch p {
	case pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN:
		return metav1.LabelSelectorOpIn, nil
	case pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN:
		return metav1.LabelSelectorOpNotIn, nil
	case pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS:
		return metav1.LabelSelectorOpExists, nil
	case pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST:
		return metav1.LabelSelectorOpDoesNotExist, nil
	case pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_UNKNOWN:
		return "", eris.Errorf("invalid label selector operator %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToLabelSelectorOperatorProto(o metav1.LabelSelectorOperator) (pbk8s.LabelSelectorOperator, error) {
	switch o {
	case metav1.LabelSelectorOpIn:
		return pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN, nil
	case metav1.LabelSelectorOpNotIn:
		return pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN, nil
	case metav1.LabelSelectorOpExists:
		return pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS, nil
	case metav1.LabelSelectorOpDoesNotExist:
		return pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST, nil
	}
	return pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_UNKNOWN, eris.Errorf("invalid label selector operator %v", o).WithCode(eris.CodeInvalidArgument)
}
