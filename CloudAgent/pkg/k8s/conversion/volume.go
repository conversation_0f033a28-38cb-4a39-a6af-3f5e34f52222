package conversion

import (
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"

	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"
)

func ToVolumeMountProto(vm corev1.VolumeMount) *pbk8s.VolumeMount {
	var mountPropagation string
	if vm.MountPropagation != nil {
		mountPropagation = string(*vm.MountPropagation)
	}
	return &pbk8s.VolumeMount{
		Name:             vm.Name,
		ReadOnly:         vm.ReadOnly,
		MountPath:        vm.MountPath,
		SubPath:          vm.SubPath,
		MountPropagation: mountPropagation,
		SubPathExpr:      vm.SubPathExpr,
	}
}

func FromVolumeProto(p *pbk8s.Volume) (corev1.Volume, error) {
	v := corev1.Volume{
		Name: p.GetName(),
	}

	switch sourceType := p.GetVolumneSource().(type) {
	case *pbk8s.Volume_PersistentVolumeClaim:
		v.PersistentVolumeClaim = &corev1.PersistentVolumeClaimVolumeSource{
			ClaimName: p.GetPersistentVolumeClaim().GetClaimName(),
			ReadOnly:  p.GetPersistentVolumeClaim().GetReadOnly(),
		}
	case *pbk8s.Volume_EmptyDir:
		var sizeLimit *resource.Quantity
		if p.GetEmptyDir().SizeLimit != nil {
			parsedSizeLimit, err := resource.ParseQuantity(p.GetEmptyDir().GetSizeLimit())
			if err != nil {
				return corev1.Volume{}, err
			}
			sizeLimit = &parsedSizeLimit
		}
		v.EmptyDir = &corev1.EmptyDirVolumeSource{
			Medium:    corev1.StorageMedium(p.GetEmptyDir().GetMedium()),
			SizeLimit: sizeLimit,
		}
	case *pbk8s.Volume_Secret:
		var items []corev1.KeyToPath
		for _, i := range p.GetSecret().GetItems() {
			items = append(items, corev1.KeyToPath{
				Key:  i.GetKey(),
				Path: i.GetPath(),
				Mode: i.Mode,
			})
		}
		v.Secret = &corev1.SecretVolumeSource{
			SecretName:  p.GetSecret().GetSecretName(),
			Items:       items,
			DefaultMode: p.GetSecret().DefaultMode,
			Optional:    p.GetSecret().IsOptional,
		}

	default:
		return corev1.Volume{}, eris.Errorf("invalid volume source type: %v", sourceType).WithCode(eris.CodeInvalidArgument)
	}

	return v, nil
}

func ToVolumeProto(vm corev1.Volume) (*pbk8s.Volume, error) {
	switch {
	case vm.PersistentVolumeClaim != nil:
		return &pbk8s.Volume{
			Name: vm.Name,
			VolumneSource: &pbk8s.Volume_PersistentVolumeClaim{
				PersistentVolumeClaim: &pbk8s.PersistentVolumeClaimVolumeSource{
					ClaimName: vm.PersistentVolumeClaim.ClaimName,
					ReadOnly:  vm.PersistentVolumeClaim.ReadOnly,
				},
			},
		}, nil
	case vm.EmptyDir != nil:
		var sizeLimit *string
		if vm.EmptyDir.SizeLimit != nil {
			sizeLimit = ptr.To(vm.EmptyDir.SizeLimit.String())
		}
		return &pbk8s.Volume{
			Name: vm.Name,
			VolumneSource: &pbk8s.Volume_EmptyDir{
				EmptyDir: &pbk8s.EmptyDirVolumeSource{
					Medium:    string(vm.EmptyDir.Medium),
					SizeLimit: sizeLimit,
				},
			},
		}, nil
	case vm.Secret != nil:
		var items []*pbk8s.KeyToPath
		for _, i := range vm.Secret.Items {
			items = append(items, &pbk8s.KeyToPath{
				Key:  i.Key,
				Path: i.Path,
				Mode: i.Mode,
			})
		}
		return &pbk8s.Volume{
			Name: vm.Name,
			VolumneSource: &pbk8s.Volume_Secret{
				Secret: &pbk8s.SecretVolumeSource{
					SecretName:  vm.Secret.SecretName,
					Items:       items,
					DefaultMode: vm.Secret.DefaultMode,
					IsOptional:  vm.Secret.Optional,
				},
			},
		}, nil
	}

	return nil, eris.New("invalid volume source type, only pvc or empty dir is supported").WithCode(eris.CodeInvalidArgument)
}
