package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func TestFromResourceRequirementConversion(t *testing.T) {
	r, err := FromResourceRequirementsProto(&pbk8s.ResourceRequirements{
		CpuRequest:    "250m",
		CpuLimit:      "500m",
		MemoryRequest: "64Mi",
		MemoryLimit:   "128Mi",
	})

	require.NoError(t, err)
	expectedCPULimit := resource.NewScaledQuantity(500, resource.Milli)
	// Generate s filed
	_ = expectedCPULimit.String()
	expectedCPURequest := resource.NewScaledQuantity(250, resource.Milli)
	// Generate s filed
	_ = expectedCPURequest.String()
	assert.Equal(t, &corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedCPULimit,
			corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedCPURequest,
			corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
		},
	}, r)
}

func TestToResourceRequirementConversion(t *testing.T) {
	cpuLimit := resource.NewScaledQuantity(500, resource.Milli)
	// Generate s filed
	_ = cpuLimit.String()
	cpuRequest := resource.NewScaledQuantity(250, resource.Milli)
	// Generate s filed
	_ = cpuRequest.String()
	r := &corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *cpuLimit,
			corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *cpuRequest,
			corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
		},
	}
	p, err := ToResourceRequirementsProto(r)

	require.NoError(t, err)
	assert.Equal(t, &pbk8s.ResourceRequirements{
		CpuRequest:    "250m",
		CpuLimit:      "500m",
		MemoryRequest: "64Mi",
		MemoryLimit:   "128Mi",
	}, p)
}
