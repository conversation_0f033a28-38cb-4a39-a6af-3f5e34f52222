package conversion

import (
	"testing"

	prometheusv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
)

func TestFromServiceMonitorSpecProto(t *testing.T) {
	sm, err := FromServiceMonitorSpecProto(&pbprometheus.ServiceMonitorSpec{
		JobLabel:     "jl",
		TargetLabels: []string{"tl1", "tl2"},
		Endpoints: []*pbprometheus.Endpoint{
			{
				Port:          "p1",
				Interval:      "1m",
				ScrapeTimeout: "1s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []string{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:          "p2",
				Interval:      "2m",
				ScrapeTimeout: "2s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: &pbk8s.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []*pbk8s.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
				},
			},
		},
	})
	require.NoError(t, err)
	assert.Equal(t, sm, prometheusv1.ServiceMonitorSpec{
		JobLabel:     "jl",
		TargetLabels: []string{"tl1", "tl2"},
		Endpoints: []prometheusv1.Endpoint{
			{
				Port:          "p1",
				Interval:      "1m",
				ScrapeTimeout: "1s",
				MetricRelabelConfigs: []prometheusv1.RelabelConfig{
					{
						SourceLabels: []prometheusv1.LabelName{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []prometheusv1.LabelName{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:          "p2",
				Interval:      "2m",
				ScrapeTimeout: "2s",
				MetricRelabelConfigs: []prometheusv1.RelabelConfig{
					{
						SourceLabels: []prometheusv1.LabelName{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: metav1.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []metav1.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: metav1.LabelSelectorOpIn,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: metav1.LabelSelectorOpNotIn,
				},
			},
		},
	})
}
