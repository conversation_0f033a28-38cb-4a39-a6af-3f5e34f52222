package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromNetworkSpecConversion(t *testing.T) {
	tests := []struct {
		input    *pbk8s.NetworkPolicySpec
		expected networkingv1.NetworkPolicySpec
	}{
		{
			input: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Ingress: []*pbk8s.NetworkPolicyIngressRule{
					{
						Ports: []*pbk8s.NetworkPolicyPort{
							{
								Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
								Port: &pbk8s.IntOrString{
									IntVal: utils.Ptr(int32(8080)),
								},
								EndPort: 9090,
							},
							{
								Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
								Port: &pbk8s.IntOrString{
									IntVal: utils.Ptr(int32(8081)),
								},
							},
						},
						From: []*pbk8s.NetworkPolicyPeer{
							{
								PodSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k2": "v2"},
								},
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3"},
								},
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						Ports: []*pbk8s.NetworkPolicyPort{
							{
								Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
								Port: &pbk8s.IntOrString{
									IntVal: utils.Ptr(int32(8080)),
								},
								EndPort: 9090,
							},
						},
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
					{
						Ports: []*pbk8s.NetworkPolicyPort{
							{
								Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_UDP,
								Port: &pbk8s.IntOrString{
									StrVal: utils.Ptr("test"),
								},
							},
						},
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{
					pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
					pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
				},
			},

			expected: networkingv1.NetworkPolicySpec{
				PodSelector: metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Ingress: []networkingv1.NetworkPolicyIngressRule{
					{
						Ports: []networkingv1.NetworkPolicyPort{
							{
								Protocol: utils.Ptr(corev1.ProtocolTCP),
								Port:     utils.Ptr(intstr.FromInt(8080)),
								EndPort:  utils.Ptr(int32(9090)),
							},
							{
								Protocol: utils.Ptr(corev1.ProtocolTCP),
								Port:     utils.Ptr(intstr.FromInt(8081)),
							},
						},
						From: []networkingv1.NetworkPolicyPeer{
							{
								PodSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k2": "v2"},
								},
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3"},
								},
								IPBlock: &networkingv1.IPBlock{
									CIDR:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				Egress: []networkingv1.NetworkPolicyEgressRule{
					{
						Ports: []networkingv1.NetworkPolicyPort{
							{
								Protocol: utils.Ptr(corev1.ProtocolTCP),
								Port:     utils.Ptr(intstr.FromInt(8080)),
								EndPort:  utils.Ptr(int32(9090)),
							},
						},
						To: []networkingv1.NetworkPolicyPeer{
							{
								IPBlock: &networkingv1.IPBlock{
									CIDR:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
					{
						Ports: []networkingv1.NetworkPolicyPort{
							{
								Protocol: utils.Ptr(corev1.ProtocolUDP),
								Port:     utils.Ptr(intstr.FromString("test")),
							},
						},
						To: []networkingv1.NetworkPolicyPeer{
							{
								IPBlock: &networkingv1.IPBlock{
									CIDR:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				PolicyTypes: []networkingv1.PolicyType{
					networkingv1.PolicyTypeIngress,
					networkingv1.PolicyTypeEgress,
				},
			},
		},
	}

	for _, tt := range tests {
		spec, err := FromNetworkPolicySpecProto(tt.input)
		require.NoError(t, err)
		assert.Equal(t, spec, tt.expected, "unexpected output, result: %v, test case: %v", spec, tt)
	}
}
