package conversion

import (
	"k8s.io/apimachinery/pkg/runtime"

	pbgmp "github.com/risingwavelabs/cloudagent/pbgen/common/gmp"
)

func FromPodMonitoringSpecProto(p *pbgmp.PodMonitoringSpec) (map[string]interface{}, error) {
	var endpoints []interface{}
	for _, ep := range p.GetEndpoints() {
		endpoints = append(endpoints, FromGMPEndpointProto(ep))
	}
	selector, err := FromLabelSelectorProto(p.GetSelector())
	if err != nil {
		return nil, err
	}
	selectUnstructed, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&selector)
	if err != nil {
		return nil, err
	}
	return map[string]interface{}{
		"selector":     selectUnstructed,
		"endpoints":    endpoints,
		"targetLabels": FromGMPTargetLabelsProto(p.GetTargetLabels()),
	}, nil
}

func FromGMPEndpointProto(p *pbgmp.Endpoint) map[string]interface{} {
	var metricRelabeling []interface{}
	for _, relabelingRule := range p.GetMetricRelabeling() {
		metricRelabeling = append(metricRelabeling, FromGMPRelabelingRuleProto(relabelingRule))
	}
	return map[string]interface{}{
		"port":             p.GetPort(),
		"interval":         p.GetInterval(),
		"timeout":          p.GetTimeout(),
		"metricRelabeling": metricRelabeling,
	}
}

func toInterfaceSlice(from []string) []interface{} {
	to := make([]interface{}, len(from))
	for i, ele := range from {
		to[i] = ele
	}
	return to
}

func FromGMPRelabelingRuleProto(p *pbgmp.RelabelingRule) map[string]interface{} {
	return map[string]interface{}{
		"sourceLabels": toInterfaceSlice(p.GetSourceLabels()),
		"regex":        p.GetRegex(),
		"action":       p.GetAction(),
	}
}

func FromGMPTargetLabelsProto(p *pbgmp.TargetLabels) map[string]interface{} {
	var fromPod []interface{}
	for _, labelMapping := range p.GetFromPod() {
		fromPod = append(fromPod, FromGMPLabelMappingProto(labelMapping))
	}
	return map[string]interface{}{
		"fromPod": fromPod,
	}
}

func FromGMPLabelMappingProto(p *pbgmp.LabelMapping) map[string]interface{} {
	return map[string]interface{}{
		"from": p.GetFrom(),
		"to":   p.GetTo(),
	}
}
