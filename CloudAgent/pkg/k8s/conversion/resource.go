package conversion

import (
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	"github.com/risingwavelabs/eris"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func FromResourceRequirementsProto(p *pbk8s.ResourceRequirements) (*corev1.ResourceRequirements, error) {
	cpuRequest, err := resource.ParseQuantity(p.GetCpuRequest())
	if err != nil {
		return nil, err
	}
	cpuLimit, err := resource.ParseQuantity(p.GetCpuLimit())
	if err != nil {
		return nil, err
	}
	memRequest, err := resource.ParseQuantity(p.GetMemoryRequest())
	if err != nil {
		return nil, err
	}
	memLimit, err := resource.ParseQuantity(p.GetMemoryLimit())
	if err != nil {
		return nil, err
	}
	return &corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    cpuLimit,
			corev1.ResourceMemory: memLimit,
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    cpuRequest,
			corev1.ResourceMemory: memRequest,
		},
	}, nil
}

func FromVolumeResourceRequirementsProto(p *pbk8s.VolumeResourceRequirements) (*corev1.VolumeResourceRequirements, error) {
	storageRequest, err := resource.ParseQuantity(p.GetStorageRequest())
	if err != nil {
		return nil, err
	}
	return &corev1.VolumeResourceRequirements{
		Requests: corev1.ResourceList{
			corev1.ResourceStorage: storageRequest,
		},
	}, nil
}

func ToResourceRequirementsProto(r *corev1.ResourceRequirements) (*pbk8s.ResourceRequirements, error) {
	if r.Limits.Cpu() == nil {
		return nil, eris.Errorf("missing CPU limit: %v", r)
	}
	if r.Limits.Memory() == nil {
		return nil, eris.Errorf("missing RAM limit: %v", r)
	}
	if r.Requests.Cpu() == nil {
		return nil, eris.Errorf("missing CPU request: %v", r)
	}
	if r.Requests.Memory() == nil {
		return nil, eris.Errorf("missing RAM request: %v", r)
	}
	return &pbk8s.ResourceRequirements{

		CpuRequest:    r.Requests.Cpu().String(),
		CpuLimit:      r.Limits.Cpu().String(),
		MemoryRequest: r.Requests.Memory().String(),
		MemoryLimit:   r.Limits.Memory().String(),
	}, nil
}

func ToVolumeResourceRequirementsProto(r corev1.VolumeResourceRequirements) (*pbk8s.VolumeResourceRequirements, error) {
	if r.Requests.Storage() == nil {
		return nil, eris.Errorf("missing storage request: %v", r)
	}
	return &pbk8s.VolumeResourceRequirements{
		StorageRequest: r.Requests.Storage().String(),
	}, nil
}
