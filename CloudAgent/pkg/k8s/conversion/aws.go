package conversion

import (
	ackrdsv1alpha1 "github.com/aws-controllers-k8s/rds-controller/apis/v1alpha1"
	ackcorev1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	k8scorev1 "k8s.io/api/core/v1"

	pbaws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func FromDBInstanceSpecProto(p *pbaws.DBInstanceSpec) (*ackrdsv1alpha1.DBInstanceSpec, error) {
	vpcGroupID := make([]*string, len(p.GetVpcSecurityGroupIds()))
	for i, v := range p.GetVpcSecurityGroupIds() {
		var value = v
		vpcGroupID[i] = &value
	}
	var tags []*ackrdsv1alpha1.Tag
	for k, v := range p.GetTags() {
		var key = k
		var val = v
		tags = append(tags, &ackrdsv1alpha1.Tag{
			Key:   &key,
			Value: &val,
		})
	}
	return &ackrdsv1alpha1.DBInstanceSpec{
		DBInstanceIdentifier: utils.Ptr(p.GetDbInstanceIdentifier()),
		DBInstanceClass:      utils.Ptr(p.GetDbInstanceClass()),
		AllocatedStorage:     utils.Ptr(int64(p.GetAllocatedStorage())),
		Engine:               utils.Ptr(p.GetEngine()),
		EngineVersion:        utils.Ptr(p.GetEngineVersion()),
		DBName:               utils.Ptr(p.GetDbName()),
		MasterUsername:       utils.Ptr(p.GetMasterUsername()),
		MasterUserPassword: &ackcorev1alpha1.SecretKeyReference{
			SecretReference: k8scorev1.SecretReference{
				Name:      p.GetMasterUserPassword().GetName(),
				Namespace: p.GetMasterUserPassword().GetNamespace(),
			},
			Key: p.GetMasterUserPassword().GetKey(),
		},
		DBSubnetGroupName:   utils.Ptr(p.GetDbSubnetGroupName()),
		VPCSecurityGroupIDs: vpcGroupID,
		Tags:                tags,
		StorageEncrypted:    utils.Ptr(p.GetStorageEncrypted()),
	}, nil
}
