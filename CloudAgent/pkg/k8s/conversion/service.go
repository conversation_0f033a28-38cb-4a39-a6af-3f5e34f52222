package conversion

import (
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func FromServiceTypeProto(p pbk8s.ServiceType) (corev1.ServiceType, error) {
	switch p {
	case pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP:
		return corev1.ServiceTypeClusterIP, nil
	case pbk8s.ServiceType_SERVICE_TYPE_NODE_PORT:
		return corev1.ServiceTypeNodePort, nil
	case pbk8s.ServiceType_SERVICE_TYPE_LOAD_BALANCER:
		return corev1.ServiceTypeLoadBalancer, nil
	case pbk8s.ServiceType_SERVICE_TYPE_EXTERNAL_NAME:
		return corev1.ServiceTypeExternalName, nil
	case pbk8s.ServiceType_SERVICE_TYPE_UNKNOWN:
		return "", eris.<PERSON><PERSON>rf("invalid service type %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToServiceTypeProto(s corev1.ServiceType) (pbk8s.ServiceType, error) {
	switch s {
	case corev1.ServiceTypeClusterIP:
		return pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP, nil
	case corev1.ServiceTypeNodePort:
		return pbk8s.ServiceType_SERVICE_TYPE_NODE_PORT, nil
	case corev1.ServiceTypeLoadBalancer:
		return pbk8s.ServiceType_SERVICE_TYPE_LOAD_BALANCER, nil
	case corev1.ServiceTypeExternalName:
		return pbk8s.ServiceType_SERVICE_TYPE_EXTERNAL_NAME, nil
	}
	return pbk8s.ServiceType_SERVICE_TYPE_UNKNOWN, eris.Errorf("invalid service type %v", s).WithCode(eris.CodeInvalidArgument)
}

func FromServiceSpecProto(p *pbk8s.ServiceSpec) corev1.ServiceSpec {
	var ports []corev1.ServicePort
	for _, p := range p.GetPorts() {
		ports = append(ports, corev1.ServicePort{
			Name: p.GetName(),
			Port: p.GetPort(),
		})
	}

	return corev1.ServiceSpec{
		Ports:    ports,
		Selector: p.GetSelector(),
	}
}
