package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
)

func TestFromServiceMonitorK8SpecToUnstructured(t *testing.T) {
	pm, err := FromServiceMonitorK8SpecToUnstructured(&pbprometheus.ServiceMonitorSpec{
		JobLabel:     "jl",
		TargetLabels: []string{"tl1", "tl2"},
		Endpoints: []*pbprometheus.Endpoint{
			{
				Port:          "p1",
				Interval:      "1m",
				ScrapeTimeout: "1s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []string{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:          "p2",
				Interval:      "2m",
				ScrapeTimeout: "2s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: &pbk8s.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []*pbk8s.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
				},
			},
		},
	})
	require.NoError(t, err)
	assert.Equal(t, pm, map[string]interface{}{
		"endpoints": []interface{}{
			map[string]interface{}{
				"interval": "1m",
				"metricRelabelConfigs": []interface{}{
					map[string]interface{}{
						"action": "action1",
						"regex":  "re1",
						"sourceLabels": []interface{}{
							"sl1",
							"sl2",
						},
					},
					map[string]interface{}{
						"action": "action2",
						"regex":  "re2",
						"sourceLabels": []interface{}{
							"sl3",
							"sl4",
						},
					},
				},
				"port":          "p1",
				"scrapeTimeout": "1s",
			},
			map[string]interface{}{
				"interval": "2m",
				"metricRelabelConfigs": []interface{}{
					map[string]interface{}{
						"action": "action1",
						"regex":  "re1",
						"sourceLabels": []interface{}{
							"sl5",
							"sl6",
						},
					},
				},
				"port":          "p2",
				"scrapeTimeout": "2s",
			},
		},
		"jobLabels": "jl",
		"selector": map[string]interface{}{
			"matchExpressions": []interface{}{
				map[string]interface{}{
					"key":      "key1",
					"operator": "In",
					"values": []interface{}{
						"val1",
						"val2",
					},
				},
				map[string]interface{}{
					"key":      "key2",
					"operator": "NotIn",
					"values": []interface{}{
						"val3",
						"val4",
					},
				},
			},
			"matchLabels": map[string]interface{}{
				"k1": "v1",
				"k2": "v2",
			},
		},
		"targetLabels": []interface{}{
			"tl1",
			"tl2",
		},
	})
}
