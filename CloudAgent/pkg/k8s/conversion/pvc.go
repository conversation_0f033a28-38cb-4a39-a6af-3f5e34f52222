package conversion

import (
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func FromPersistentVolumeClaimSpecProto(p *pbk8s.PersistentVolumeClaimSpec) (corev1.PersistentVolumeClaimSpec, error) {
	var accessModes []corev1.PersistentVolumeAccessMode
	for _, accessModeProto := range p.GetAccessModes() {
		accessMode, err := FromPVCAccessModeProto(accessModeProto)
		if err != nil {
			return corev1.PersistentVolumeClaimSpec{}, err
		}
		accessModes = append(accessModes, accessMode)
	}
	var selector *metav1.LabelSelector
	var err error
	if p.GetSelector() != nil {
		selector, err = FromLabelSelectorProto(p.GetSelector())
		if err != nil {
			return corev1.PersistentVolumeClaimSpec{}, err
		}
	}

	resources, err := FromVolumeResourceRequirementsProto(p.GetResources())
	if err != nil {
		return corev1.PersistentVolumeClaimSpec{}, err
	}
	var storageClassName *string
	if p.GetStorageClassName() != "" {
		storageClassName = utils.Ptr(p.GetStorageClassName())
	}

	return corev1.PersistentVolumeClaimSpec{
		AccessModes:      accessModes,
		Selector:         selector,
		Resources:        *resources,
		VolumeName:       p.GetVolumeName(),
		StorageClassName: storageClassName,
	}, nil
}

func ToPersistentVolumeClaimSpecProto(s corev1.PersistentVolumeClaimSpec) (*pbk8s.PersistentVolumeClaimSpec, error) {
	var accessModes []pbk8s.PVCAccessMode
	for _, accessMode := range s.AccessModes {
		accessModeProto, err := ToPVCAccessModeProto(accessMode)
		if err != nil {
			return nil, err
		}
		accessModes = append(accessModes, accessModeProto)
	}
	var selector *pbk8s.LabelSelector
	var err error
	if s.Selector != nil {
		selector, err = ToLabelSelectorProto(s.Selector)
		if err != nil {
			return nil, err
		}
	}

	resources, err := ToVolumeResourceRequirementsProto(s.Resources)
	if err != nil {
		return nil, err
	}
	var storageClassName string
	if s.StorageClassName != nil {
		storageClassName = *s.StorageClassName
	}

	return &pbk8s.PersistentVolumeClaimSpec{
		AccessModes:      accessModes,
		Selector:         selector,
		Resources:        resources,
		VolumeName:       s.VolumeName,
		StorageClassName: storageClassName,
	}, nil
}

func FromPVCAccessModeProto(p pbk8s.PVCAccessMode) (corev1.PersistentVolumeAccessMode, error) {
	switch p {
	case pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE:
		return corev1.ReadWriteOnce, nil
	case pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_ONLY_MANY:
		return corev1.ReadOnlyMany, nil
	case pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_MANY:
		return corev1.ReadWriteMany, nil
	case pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE_POD:
		return corev1.ReadWriteOncePod, nil
	case pbk8s.PVCAccessMode_PVC_ACCESS_MODE_UNSPECIFIED:
		return "", eris.Errorf("invalid accessmode %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToPVCAccessModeProto(m corev1.PersistentVolumeAccessMode) (pbk8s.PVCAccessMode, error) {
	switch m {
	case corev1.ReadWriteOnce:
		return pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE, nil
	case corev1.ReadOnlyMany:
		return pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_ONLY_MANY, nil
	case corev1.ReadWriteMany:
		return pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_MANY, nil
	case corev1.ReadWriteOncePod:
		return pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE_POD, nil
	}
	return pbk8s.PVCAccessMode_PVC_ACCESS_MODE_UNSPECIFIED, eris.Errorf("invalid accessmode %v", m).WithCode(eris.CodeInvalidArgument)
}

func FromVolumeMountProto(p *pbk8s.VolumeMount) corev1.VolumeMount {
	var mountPropagation *corev1.MountPropagationMode
	if p.GetMountPropagation() != "" {
		mountPropagation = utils.Ptr(corev1.MountPropagationMode(p.GetMountPropagation()))
	}
	return corev1.VolumeMount{
		Name:             p.GetName(),
		ReadOnly:         p.GetReadOnly(),
		MountPath:        p.GetMountPath(),
		SubPath:          p.GetSubPath(),
		MountPropagation: mountPropagation,
		SubPathExpr:      p.GetSubPathExpr(),
	}
}
