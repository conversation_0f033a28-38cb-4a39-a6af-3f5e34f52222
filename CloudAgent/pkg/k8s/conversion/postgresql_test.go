package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	acidv1 "github.com/zalando/postgres-operator/pkg/apis/acid.zalan.do/v1"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbpostgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromPostgreSQLSpecProto(t *testing.T) {
	tests := []struct {
		name    string
		proto   *pbpostgresql.PostgreSqlSpec
		want    *acidv1.PostgresSpec
		wantErr bool
	}{
		{
			name: "minimal",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId: "test_team",
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Volume: &pbpostgresql.PostgreSqlVolume{
					Size: "10Gi",
				},
				Postgresql: &pbpostgresql.PostgreSqlParam{
					Version: "16",
				},
			},
			want: &acidv1.PostgresSpec{
				TeamID: "test_team",
				Resources: &acidv1.Resources{
					ResourceRequests: acidv1.ResourceDescription{
						CPU:    utils.Ptr("250m"),
						Memory: utils.Ptr("64Mi"),
					},
					ResourceLimits: acidv1.ResourceDescription{
						CPU:    utils.Ptr("500m"),
						Memory: utils.Ptr("128Mi"),
					},
				},
				NumberOfInstances: 1,
				Volume: acidv1.Volume{
					Size: "10Gi",
				},
				PostgresqlParam: acidv1.PostgresqlParam{
					PgVersion: "16",
				},
			},
		},
		{
			name: "missing team id",
			proto: &pbpostgresql.PostgreSqlSpec{
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Volume: &pbpostgresql.PostgreSqlVolume{
					Size: "10Gi",
				},
				Postgresql: &pbpostgresql.PostgreSqlParam{
					Version: "16",
				},
			},
			wantErr: true,
		},
		{
			name: "missing resources",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId:            "test_team",
				NumberOfInstances: 1,
				Volume: &pbpostgresql.PostgreSqlVolume{
					Size: "10Gi",
				},
				Postgresql: &pbpostgresql.PostgreSqlParam{
					Version: "16",
				},
			},
			wantErr: true,
		},
		{
			name: "missing volume",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId: "test_team",
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Postgresql: &pbpostgresql.PostgreSqlParam{
					Version: "16",
				},
			},
			wantErr: true,
		},
		{
			name: "missing volume size",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId: "test_team",
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Volume:            &pbpostgresql.PostgreSqlVolume{},
				Postgresql: &pbpostgresql.PostgreSqlParam{
					Version: "16",
				},
			},
			wantErr: true,
		},
		{
			name: "missing postgresql param",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId: "test_team",
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Volume: &pbpostgresql.PostgreSqlVolume{
					Size: "10Gi",
				},
			},
			wantErr: true,
		},
		{
			name: "missing postgresql version",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId: "test_team",
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Volume: &pbpostgresql.PostgreSqlVolume{
					Size: "10Gi",
				},
				Postgresql: &pbpostgresql.PostgreSqlParam{},
			},
			wantErr: true,
		},
		{
			name: "full",
			proto: &pbpostgresql.PostgreSqlSpec{
				TeamId:      "test_team",
				DockerImage: "test_image:v1",
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
				NumberOfInstances: 1,
				Volume: &pbpostgresql.PostgreSqlVolume{
					Size:         "10Gi",
					StorageClass: "test_storage_class",
				},
				Users: map[string]*pbpostgresql.StringArray{
					"user1": {
						Value: []string{"perm1", "perm2"},
					},
					"user2": {
						Value: []string{},
					},
				},
				Postgresql: &pbpostgresql.PostgreSqlParam{
					Version: "16",
					Parameters: map[string]string{
						"param1": "val1",
						"param2": "val2",
					},
				},
				Databases: map[string]string{
					"db1": "user1",
				},
				Tolerations: []*pbk8s.Toleration{
					{
						Key:            "testkey",
						Value:          "testvalue",
						Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
						Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
						TolerationSecs: utils.Ptr(int64(10)),
					},
				},
				NodeAffinity: &pbk8s.NodeAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
						NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
							{
								MatchExpressions: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										Values:   []string{"val1", "val2"},
									},
								},
								MatchFields: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key2",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
										Values:   []string{"val3", "val4"},
									},
								},
							},
						},
					},
				},
				PodAnnotations: map[string]string{
					"pod_annotation": "pod",
				},
				ServiceAnnotations: map[string]string{
					"service_annotation": "service",
				},
			},
			want: &acidv1.PostgresSpec{
				TeamID:      "test_team",
				DockerImage: "test_image:v1",
				Resources: &acidv1.Resources{
					ResourceRequests: acidv1.ResourceDescription{
						CPU:    utils.Ptr("250m"),
						Memory: utils.Ptr("64Mi"),
					},
					ResourceLimits: acidv1.ResourceDescription{
						CPU:    utils.Ptr("500m"),
						Memory: utils.Ptr("128Mi"),
					},
				},
				NumberOfInstances: 1,
				Volume: acidv1.Volume{
					Size:         "10Gi",
					StorageClass: "test_storage_class",
				},
				Users: map[string]acidv1.UserFlags{
					"user1": {"perm1", "perm2"},
					"user2": {},
				},
				Databases: map[string]string{
					"db1": "user1",
				},
				PostgresqlParam: acidv1.PostgresqlParam{
					PgVersion: "16",
					Parameters: map[string]string{
						"param1": "val1",
						"param2": "val2",
					},
				},
				Tolerations: []corev1.Toleration{
					{
						Key:               "testkey",
						Value:             "testvalue",
						Effect:            corev1.TaintEffectNoExecute,
						Operator:          corev1.TolerationOpEqual,
						TolerationSeconds: utils.Ptr(int64(10)),
					},
				},
				NodeAffinity: &corev1.NodeAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
						NodeSelectorTerms: []corev1.NodeSelectorTerm{
							{
								MatchExpressions: []corev1.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: corev1.NodeSelectorOpDoesNotExist,
										Values:   []string{"val1", "val2"},
									},
								},
								MatchFields: []corev1.NodeSelectorRequirement{
									{
										Key:      "key2",
										Operator: corev1.NodeSelectorOpNotIn,
										Values:   []string{"val3", "val4"},
									},
								},
							},
						},
					},
				},
				PodAnnotations: map[string]string{
					"pod_annotation": "pod",
				},
				ServiceAnnotations: map[string]string{
					"service_annotation": "service",
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FromPostgreSQLSpecProto(tt.proto)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)

			back, err := ToPostgreSQLSpecProto(got)
			require.NoError(t, err)
			assert.EqualValues(t, tt.proto, back)
		})
	}
}

func TestFromPostgreSQLResourcesProto(t *testing.T) {
	tests := []struct {
		name    string
		proto   *pbk8s.ResourceRequirements
		want    *acidv1.Resources
		wantErr bool
	}{
		{
			name: "regular",
			proto: &pbk8s.ResourceRequirements{
				CpuRequest:    "250m",
				CpuLimit:      "500m",
				MemoryRequest: "64Mi",
				MemoryLimit:   "128Mi",
			},
			want: &acidv1.Resources{
				ResourceRequests: acidv1.ResourceDescription{
					CPU:    utils.Ptr("250m"),
					Memory: utils.Ptr("64Mi"),
				},
				ResourceLimits: acidv1.ResourceDescription{
					CPU:    utils.Ptr("500m"),
					Memory: utils.Ptr("128Mi"),
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FromPostgreSQLResourcesProto(tt.proto)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)

			back, err := ToPostgreSQLResourcesProto(got)
			require.NoError(t, err)
			assert.EqualValues(t, tt.proto, back)
		})
	}
}

func TestFromPostgreSQLVolumeProto(t *testing.T) {
	tests := []struct {
		name    string
		proto   *pbpostgresql.PostgreSqlVolume
		want    *acidv1.Volume
		wantErr bool
	}{
		{
			name: "minimal",
			proto: &pbpostgresql.PostgreSqlVolume{
				Size: "10Gi",
			},
			want: &acidv1.Volume{
				Size: "10Gi",
			},
		},
		{
			name: "full",
			proto: &pbpostgresql.PostgreSqlVolume{
				Size:         "10Gi",
				StorageClass: "test_storage_class",
			},
			want: &acidv1.Volume{
				Size:         "10Gi",
				StorageClass: "test_storage_class",
			},
		},
		{
			name:    "nil",
			proto:   nil,
			wantErr: true,
		},
		{
			name: "missing size",
			proto: &pbpostgresql.PostgreSqlVolume{
				StorageClass: "test_storage_class",
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FromPostgreSQLVolumeProto(tt.proto)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)

			back, err := ToPostgreSQLVolumeProto(*got)
			require.NoError(t, err)
			assert.EqualValues(t, tt.proto, back)
		})
	}
}

func TestFromPostgreSQLParamProto(t *testing.T) {
	tests := []struct {
		name    string
		proto   *pbpostgresql.PostgreSqlParam
		want    *acidv1.PostgresqlParam
		wantErr bool
	}{
		{
			name: "minimal",
			proto: &pbpostgresql.PostgreSqlParam{
				Version: "16",
			},
			want: &acidv1.PostgresqlParam{
				PgVersion: "16",
			},
		},
		{
			name: "full",
			proto: &pbpostgresql.PostgreSqlParam{
				Version: "16",
				Parameters: map[string]string{
					"param1": "val1",
					"param2": "val2",
				},
			},
			want: &acidv1.PostgresqlParam{
				PgVersion: "16",
				Parameters: map[string]string{
					"param1": "val1",
					"param2": "val2",
				},
			},
		},
		{
			name:    "nil",
			proto:   nil,
			wantErr: true,
		},
		{
			name: "missing size",
			proto: &pbpostgresql.PostgreSqlParam{
				Parameters: map[string]string{
					"param1": "val1",
					"param2": "val2",
				},
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FromPostgreSQLParamProto(tt.proto)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)

			back, err := ToPostgreSQLParamProto(*got)
			require.NoError(t, err)
			assert.EqualValues(t, tt.proto, back)
		})
	}
}
