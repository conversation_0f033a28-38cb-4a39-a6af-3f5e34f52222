package conversion

import (
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func TestFromServiceTypeConversion(t *testing.T) {
	tests := []struct {
		input    pbk8s.ServiceType
		expected corev1.ServiceType
	}{
		{
			input:    pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
			expected: corev1.ServiceTypeClusterIP,
		},
		{
			input:    pbk8s.ServiceType_SERVICE_TYPE_NODE_PORT,
			expected: corev1.ServiceTypeNodePort,
		},
		{
			input:    pbk8s.ServiceType_SERVICE_TYPE_LOAD_BALANCER,
			expected: corev1.ServiceTypeLoadBalancer,
		},
		{
			input:    pbk8s.ServiceType_SERVICE_TYPE_EXTERNAL_NAME,
			expected: corev1.ServiceTypeExternalName,
		},
	}

	for _, tt := range tests {
		output, err := FromServiceTypeProto(tt.input)

		require.NoError(t, err, "unexpected err, input: %v", tt.input)
		assert.Equal(t, tt.expected, output)
	}
}

func TestFromServiceTypeConversionFailure(t *testing.T) {
	_, err := FromServiceTypeProto(pbk8s.ServiceType_SERVICE_TYPE_UNKNOWN)

	assert.Equal(t, eris.GetCode(err), eris.CodeInvalidArgument)
}

func TestToServiceConversion(t *testing.T) {
	tests := []struct {
		expected pbk8s.ServiceType
		input    corev1.ServiceType
	}{
		{
			expected: pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
			input:    corev1.ServiceTypeClusterIP,
		},
		{
			expected: pbk8s.ServiceType_SERVICE_TYPE_NODE_PORT,
			input:    corev1.ServiceTypeNodePort,
		},
		{
			expected: pbk8s.ServiceType_SERVICE_TYPE_LOAD_BALANCER,
			input:    corev1.ServiceTypeLoadBalancer,
		},
		{
			expected: pbk8s.ServiceType_SERVICE_TYPE_EXTERNAL_NAME,
			input:    corev1.ServiceTypeExternalName,
		},
	}

	for _, tt := range tests {
		output, err := ToServiceTypeProto(tt.input)

		require.NoError(t, err, "unexpected err, input: %v", tt.input)
		assert.Equal(t, tt.expected, output)
	}
}

func TestToServiceConversionFailure(t *testing.T) {
	_, err := ToServiceTypeProto("")

	assert.Equal(t, eris.GetCode(err), eris.CodeInvalidArgument)
}

func TestFromServiceConversion(t *testing.T) {
	output := FromServiceSpecProto(&pbk8s.ServiceSpec{
		Ports: []*pbk8s.ServicePort{
			{
				Name: "p1",
				Port: 1,
			},
			{
				Name: "p2",
				Port: 2,
			},
		},
		Selector: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	})

	assert.Equal(t, output, corev1.ServiceSpec{
		Ports: []corev1.ServicePort{
			{
				Name: "p1",
				Port: 1,
			},
			{
				Name: "p2",
				Port: 2,
			},
		},
		Selector: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	})
}
