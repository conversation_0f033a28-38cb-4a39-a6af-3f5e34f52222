package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func TestToPodPhaseConversion(t *testing.T) {
	tests := []struct {
		expected pbk8s.PodPhase
		input    corev1.PodPhase
	}{
		{
			expected: pbk8s.PodPhase_POD_PHASE_PENDING,
			input:    corev1.PodPending,
		},
		{
			expected: pbk8s.PodPhase_POD_PHASE_RUNNING,
			input:    corev1.PodRunning,
		},
		{
			expected: pbk8s.PodPhase_POD_PHASE_SUCCEEDED,
			input:    corev1.PodSucceeded,
		},
		{
			expected: pbk8s.PodPhase_POD_PHASE_FAILED,
			input:    corev1.PodFailed,
		},
	}

	for _, tt := range tests {
		output, err := ToPodPhaseProto(tt.input)

		require.NoError(t, err, "unexpected err, input: %v", tt.input)
		assert.Equal(t, tt.expected, output)
	}
}

func TestToPodProto(t *testing.T) {
	var (
		name        = "test"
		labels      = map[string]string{"app": "test"}
		statusPhase = corev1.PodRunning
	)
	pod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:   name,
			Labels: labels,
		},
		Status: corev1.PodStatus{
			Phase: statusPhase,
		},
	}

	protoStatusPhase, err := ToPodPhaseProto(statusPhase)
	require.NoError(t, err)

	protoPod, err := ToPodProto(pod)
	require.NoError(t, err)
	assert.Equal(t, &pbk8s.Pod{
		Name:        name,
		Labels:      labels,
		StatusPhase: protoStatusPhase,
	}, protoPod)
}
