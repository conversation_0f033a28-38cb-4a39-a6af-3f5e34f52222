package conversion

import (
	"slices"

	"maps"

	"github.com/risingwavelabs/eris"
	acidv1 "github.com/zalando/postgres-operator/pkg/apis/acid.zalan.do/v1"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbpostgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func FromPostgreSQLSpecProto(p *pbpostgresql.PostgreSqlSpec) (*acidv1.PostgresSpec, error) {
	if p.GetTeamId() == "" {
		return nil, eris.Errorf("spec.team_id is empty")
	}

	resources, err := FromPostgreSQLResourcesProto(p.GetResources())
	if err != nil {
		return nil, err
	}
	volume, err := FromPostgreSQLVolumeProto(p.GetVolume())
	if err != nil {
		return nil, err
	}
	var users map[string]acidv1.UserFlags
	if len(p.GetUsers()) != 0 {
		users = make(map[string]acidv1.UserFlags, len(p.GetUsers()))
		for k, v := range p.GetUsers() {
			users[k] = slices.Clone(v.GetValue())
		}
	}
	postgresql, err := FromPostgreSQLParamProto(p.GetPostgresql())
	if err != nil {
		return nil, err
	}

	var tolerations []corev1.Toleration
	for _, tp := range p.GetTolerations() {
		t, err := FromTolerationProto(tp)
		if err != nil {
			return nil, err
		}
		tolerations = append(tolerations, t)
	}
	var nodeAffinity *corev1.NodeAffinity
	if p.GetNodeAffinity() != nil {
		nodeAffinity, err = FromNodeAffinityProto(p.GetNodeAffinity())
		if err != nil {
			return nil, err
		}
	}
	return &acidv1.PostgresSpec{
		TeamID:             p.GetTeamId(),
		DockerImage:        p.GetDockerImage(),
		Resources:          resources,
		NumberOfInstances:  int32(p.GetNumberOfInstances()),
		Volume:             *volume,
		Users:              users,
		Databases:          maps.Clone(p.GetDatabases()),
		PostgresqlParam:    *postgresql,
		Tolerations:        tolerations,
		NodeAffinity:       nodeAffinity,
		PodAnnotations:     maps.Clone(p.GetPodAnnotations()),
		ServiceAnnotations: maps.Clone(p.GetServiceAnnotations()),
	}, nil
}

func ToPostgreSQLSpecProto(s *acidv1.PostgresSpec) (*pbpostgresql.PostgreSqlSpec, error) {
	resources, err := ToPostgreSQLResourcesProto(s.Resources)
	if err != nil {
		return nil, err
	}
	volume, err := ToPostgreSQLVolumeProto(s.Volume)
	if err != nil {
		return nil, err
	}
	var users map[string]*pbpostgresql.StringArray
	if len(s.Users) != 0 {
		users = make(map[string]*pbpostgresql.StringArray, len(s.Users))
		for k, v := range s.Users {
			users[k] = &pbpostgresql.StringArray{
				Value: slices.Clone(v),
			}
		}
	}
	postgresql, err := ToPostgreSQLParamProto(s.PostgresqlParam)
	if err != nil {
		return nil, err
	}

	var tolerations []*pbk8s.Toleration
	for _, tp := range s.Tolerations {
		t, err := ToTolerationProto(tp)
		if err != nil {
			return nil, err
		}
		tolerations = append(tolerations, t)
	}
	var nodeAffinity *pbk8s.NodeAffinity
	if s.NodeAffinity != nil {
		nodeAffinity, err = ToNodeAffinityProto(s.NodeAffinity)
		if err != nil {
			return nil, err
		}
	}
	return &pbpostgresql.PostgreSqlSpec{
		TeamId:             s.TeamID,
		DockerImage:        s.DockerImage,
		Resources:          resources,
		NumberOfInstances:  uint32(s.NumberOfInstances),
		Volume:             volume,
		Users:              users,
		Databases:          maps.Clone(s.Databases),
		Postgresql:         postgresql,
		Tolerations:        tolerations,
		NodeAffinity:       nodeAffinity,
		PodAnnotations:     maps.Clone(s.PodAnnotations),
		ServiceAnnotations: maps.Clone(s.ServiceAnnotations),
	}, nil
}

func FromPostgreSQLResourcesProto(p *pbk8s.ResourceRequirements) (*acidv1.Resources, error) {
	if p == nil {
		return nil, eris.Errorf("spec.resources is nil")
	}
	return &acidv1.Resources{
		ResourceRequests: acidv1.ResourceDescription{
			CPU:    utils.Ptr(p.GetCpuRequest()),
			Memory: utils.Ptr(p.GetMemoryRequest()),
		},
		ResourceLimits: acidv1.ResourceDescription{
			CPU:    utils.Ptr(p.GetCpuLimit()),
			Memory: utils.Ptr(p.GetMemoryLimit()),
		},
	}, nil
}

func ToPostgreSQLResourcesProto(s *acidv1.Resources) (*pbk8s.ResourceRequirements, error) {
	if s == nil {
		return nil, eris.Errorf("spec.resources is nil")
	}
	return &pbk8s.ResourceRequirements{
		CpuRequest:    *s.ResourceRequests.CPU,
		MemoryRequest: *s.ResourceRequests.Memory,
		CpuLimit:      *s.ResourceLimits.CPU,
		MemoryLimit:   *s.ResourceLimits.Memory,
	}, nil
}

func FromPostgreSQLVolumeProto(p *pbpostgresql.PostgreSqlVolume) (*acidv1.Volume, error) {
	if p == nil {
		return nil, eris.Errorf("spec.volume is nil")
	}
	if p.GetSize() == "" {
		return nil, eris.Errorf("spec.volume.size is empty")
	}
	return &acidv1.Volume{
		Size:         p.GetSize(),
		StorageClass: p.GetStorageClass(),
	}, nil
}

func ToPostgreSQLVolumeProto(s acidv1.Volume) (*pbpostgresql.PostgreSqlVolume, error) {
	return &pbpostgresql.PostgreSqlVolume{
		Size:         s.Size,
		StorageClass: s.StorageClass,
	}, nil
}

func FromPostgreSQLParamProto(p *pbpostgresql.PostgreSqlParam) (*acidv1.PostgresqlParam, error) {
	if p == nil {
		return nil, eris.Errorf("spec.postgresql is nil")
	}
	if p.GetVersion() == "" {
		return nil, eris.Errorf("spec.volume.version is empty")
	}
	return &acidv1.PostgresqlParam{
		PgVersion:  p.GetVersion(),
		Parameters: p.GetParameters(),
	}, nil
}

func ToPostgreSQLParamProto(s acidv1.PostgresqlParam) (*pbpostgresql.PostgreSqlParam, error) {
	return &pbpostgresql.PostgreSqlParam{
		Version:    s.PgVersion,
		Parameters: s.Parameters,
	}, nil
}
