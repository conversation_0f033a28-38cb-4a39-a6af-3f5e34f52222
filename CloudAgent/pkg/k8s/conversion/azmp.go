package conversion

import (
	prometheusv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"k8s.io/apimachinery/pkg/runtime"

	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
)

func FromServiceMonitorK8SpecToUnstructured(p *pbprometheus.ServiceMonitorSpec) (map[string]interface{}, error) {
	selector, err := FromLabelSelectorProto(p.GetSelector())
	if err != nil {
		return nil, err
	}
	selectorUnstructured, err := runtime.DefaultUnstructuredConverter.ToUnstructured(&selector)
	if err != nil {
		return nil, err
	}

	var endpointsUnstructured []interface{}
	for _, ep := range p.GetEndpoints() {
		endpointsUnstructured = append(endpointsUnstructured, FromPrometheusEndpointProtoToUnstructured(ep))
	}

	var targetLabelsUnstructured []interface{}
	for _, l := range p.GetTargetLabels() {
		targetLabelsUnstructured = append(targetLabelsUnstructured, l)
	}

	return map[string]interface{}{
		"selector":     selectorUnstructured,
		"endpoints":    endpointsUnstructured,
		"targetLabels": targetLabelsUnstructured,
		"jobLabels":    p.GetJobLabel(),
	}, nil
}

func FromPrometheusEndpointProtoToUnstructured(p *pbprometheus.Endpoint) map[string]interface{} {
	var metricRelabelConfigs []interface{}
	for _, c := range p.GetMetricRelabelings() {
		metricRelabelConfigs = append(metricRelabelConfigs, FromPrometheusRelabelConfigProtoToUnstructured(c))
	}
	return map[string]interface{}{
		"port":                 p.GetPort(),
		"interval":             string(prometheusv1.Duration(p.GetInterval())),
		"scrapeTimeout":        string(prometheusv1.Duration(p.GetScrapeTimeout())),
		"metricRelabelConfigs": metricRelabelConfigs,
	}
}

func FromPrometheusRelabelConfigProtoToUnstructured(p *pbprometheus.RelabelConfig) map[string]interface{} {
	var sourceLabels []interface{}
	for _, l := range p.GetSourceLabels() {
		sourceLabels = append(sourceLabels, l)
	}
	return map[string]interface{}{
		"sourceLabels": sourceLabels,
		"action":       p.GetAction(),
		"regex":        p.GetRegex(),
	}
}
