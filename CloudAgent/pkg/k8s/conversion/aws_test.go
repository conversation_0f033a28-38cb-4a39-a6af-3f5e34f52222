package conversion

import (
	"testing"

	ackrdsv1alpha1 "github.com/aws-controllers-k8s/rds-controller/apis/v1alpha1"
	ackcorev1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8scorev1 "k8s.io/api/core/v1"

	pbaws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromDBInstanceSpecProto(t *testing.T) {
	tests := []struct {
		name    string
		proto   *pbaws.DBInstanceSpec
		want    *ackrdsv1alpha1.DBInstanceSpec
		wantErr bool
	}{
		{
			name: "regular",
			proto: &pbaws.DBInstanceSpec{
				DbInstanceIdentifier: "test-id",
				DbInstanceClass:      "db.t4g.micro",
				AllocatedStorage:     20,
				Engine:               "postgres",
				EngineVersion:        "14",
				DbName:               "database1",
				MasterUsername:       "user1",
				MasterUserPassword: &pbaws.PasswordSecretRef{
					Namespace: "test-namespace",
					Name:      "test-name",
					Key:       "password",
				},
				DbSubnetGroupName:   "test-subnet",
				VpcSecurityGroupIds: []string{"sg-test"},
				Tags: map[string]string{
					"tag1": "value1",
				},
				StorageEncrypted: true,
			},
			want: &ackrdsv1alpha1.DBInstanceSpec{
				DBInstanceIdentifier: utils.Ptr("test-id"),
				DBInstanceClass:      utils.Ptr("db.t4g.micro"),
				AllocatedStorage:     utils.Ptr(int64(20)),
				Engine:               utils.Ptr("postgres"),
				EngineVersion:        utils.Ptr("14"),
				DBName:               utils.Ptr("database1"),
				MasterUsername:       utils.Ptr("user1"),
				MasterUserPassword: &ackcorev1alpha1.SecretKeyReference{
					SecretReference: k8scorev1.SecretReference{
						Namespace: "test-namespace",
						Name:      "test-name",
					},
					Key: "password",
				},
				DBSubnetGroupName:   utils.Ptr("test-subnet"),
				VPCSecurityGroupIDs: []*string{utils.Ptr("sg-test")},
				Tags: []*ackrdsv1alpha1.Tag{
					{Key: utils.Ptr("tag1"), Value: utils.Ptr("value1")},
				},
				StorageEncrypted: utils.Ptr(true),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FromDBInstanceSpecProto(tt.proto)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)
		})
	}
}
