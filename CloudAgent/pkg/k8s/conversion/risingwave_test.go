package conversion

import (
	"testing"

	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromRisingWaveConversion(t *testing.T) {
	expectedCPULimit := resource.NewScaledQuantity(500, resource.Milli)
	// Generate s filed
	_ = expectedCPULimit.String()
	expectedCPURequest := resource.NewScaledQuantity(250, resource.Milli)
	_ = expectedCPURequest.String()

	tests := map[string]struct {
		input    *pbrw.RisingWaveSpec
		expected *rwv1alpha1.RisingWaveSpec
	}{
		"standalone": {
			// standalone test
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_S3StateStore{
						S3StateStore: &pbrw.StateStoreBackendS3{
							Bucket: "test_bucket",
							Region: "test_region",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: true,
				Components: &pbrw.ComponentsSpec{
					StandaloneComponent: &pbrw.StandaloneSpec{
						LogLevel:        "info",
						UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE},
						NodePodSpec: &pbrw.NodePodSpec{
							ContainerSpec: &pbrw.NodePodContainerSpec{
								Envs: map[string]string{
									"key1": "val1",
								},
								Resources: &pbk8s.ResourceRequirements{
									CpuRequest:    "250m",
									CpuLimit:      "500m",
									MemoryRequest: "64Mi",
									MemoryLimit:   "128Mi",
								},
							},
							ServiceAccount: "test_sa",
							Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
							Labels:         map[string]string{"k1": "v1", "k2": "v2"},
						},
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					S3: &rwv1alpha1.RisingWaveStateStoreBackendS3{
						RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Region: "test_region",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: utils.Ptr(true),
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
						LogLevel:        "info",
						UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
						Template: rwv1alpha1.RisingWaveNodePodTemplate{
							ObjectMeta: rwv1alpha1.PartialObjectMeta{
								Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
								Labels:      map[string]string{"k1": "v1", "k2": "v2"},
							},
							Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
								RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
									Env: []corev1.EnvVar{
										{
											Name:  "key1",
											Value: "val1",
										},
									},
									Resources: corev1.ResourceRequirements{
										Limits: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("500m"),
											corev1.ResourceMemory: resource.MustParse("128Mi"),
										},
										Requests: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("250m"),
											corev1.ResourceMemory: resource.MustParse("64Mi"),
										},
									},
								},
								ServiceAccountName: "test_sa",
							},
						},
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
		},
		"s3": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_S3StateStore{
						S3StateStore: &pbrw.StateStoreBackendS3{
							Bucket: "test_bucket",
							Region: "test_region",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: false,
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									Affinity: &pbk8s.Affinity{
										NodeAffinity: &pbk8s.NodeAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
												NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
													{
														MatchExpressions: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key1",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
																Values:   []string{"val1", "val2"},
															},
														},
														MatchFields: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key2",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
																Values:   []string{"val3", "val4"},
															},
														},
													},
												},
											},
										},
									},
									Tolerations: []*pbk8s.Toleration{
										{
											Key:            "testkey",
											Value:          "testvalue",
											Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
											Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
											TolerationSecs: utils.Ptr(int64(10)),
										},
									},
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								PersistentVolumeClaimRetentionPolicy: &pbrw.PersistentVolumeClaimRetentionPolicy{
									WhenDeleted: pbrw.PersistentVolumeClaimRetentionPolicyType_DELETE,
									WhenScaled:  pbrw.PersistentVolumeClaimRetentionPolicyType_RETAIN,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					S3: &rwv1alpha1.RisingWaveStateStoreBackendS3{
						RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Region: "test_region",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: utils.Ptr(false),
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
										Affinity: &corev1.Affinity{
											NodeAffinity: &corev1.NodeAffinity{
												RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
													NodeSelectorTerms: []corev1.NodeSelectorTerm{
														{
															MatchExpressions: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key1",
																	Operator: corev1.NodeSelectorOpDoesNotExist,
																	Values:   []string{"val1", "val2"},
																},
															},
															MatchFields: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key2",
																	Operator: corev1.NodeSelectorOpNotIn,
																	Values:   []string{"val3", "val4"},
																},
															},
														},
													},
												},
											},
										},
										Tolerations: []corev1.Toleration{
											{
												Key:               "testkey",
												Value:             "testvalue",
												Effect:            corev1.TaintEffectNoExecute,
												Operator:          corev1.TolerationOpEqual,
												TolerationSeconds: utils.Ptr(int64(10)),
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								PersistentVolumeClaimRetentionPolicy: &v1.StatefulSetPersistentVolumeClaimRetentionPolicy{
									WhenDeleted: "Delete",
									WhenScaled:  "Retain",
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"gcs": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_GcsStateStore{
						GcsStateStore: &pbrw.StateStoreBackendGCS{
							Bucket: "test_bucket",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: false,
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					GCS: &rwv1alpha1.RisingWaveStateStoreBackendGCS{
						RisingWaveGCSCredentials: rwv1alpha1.RisingWaveGCSCredentials{
							UseWorkloadIdentity: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Root:   "test_dir",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: utils.Ptr(false),
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"azblob": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_AzblobStateStore{
						AzblobStateStore: &pbrw.StateStoreBackendAzblob{
							Container: "test_bucket",
							Endpoint:  "test_endpoint",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: false,
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					AzureBlob: &rwv1alpha1.RisingWaveStateStoreBackendAzureBlob{
						RisingWaveAzureBlobCredentials: rwv1alpha1.RisingWaveAzureBlobCredentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Container: "test_bucket",
						Endpoint:  "test_endpoint",
						Root:      "test_dir",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: utils.Ptr(false),
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"in memory": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				EnableStandaloneMode:        false,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_MemoryStateStore{
						MemoryStateStore: &pbrw.StateStoreBackendMemory{},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				EnableStandaloneMode:        utils.Ptr(false),
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					Memory:        utils.Ptr(true),
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
		},
		"local disk": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				EnableStandaloneMode:        false,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_LocalDiskStateStore{
						LocalDiskStateStore: &pbrw.StateStoreBackendLocalDisk{
							Root: "test_dir",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				EnableStandaloneMode:        utils.Ptr(false),
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					LocalDisk: &rwv1alpha1.RisingWaveStateStoreBackendLocalDisk{
						Root: "test_dir",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
		},
		"compute cache enabled": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_S3StateStore{
						S3StateStore: &pbrw.StateStoreBackendS3{
							Bucket: "test_bucket",
							Region: "test_region",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: false,
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									Affinity: &pbk8s.Affinity{
										NodeAffinity: &pbk8s.NodeAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
												NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
													{
														MatchExpressions: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key1",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
																Values:   []string{"val1", "val2"},
															},
														},
														MatchFields: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key2",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
																Values:   []string{"val3", "val4"},
															},
														},
													},
												},
											},
										},
									},
									Tolerations: []*pbk8s.Toleration{
										{
											Key:            "testkey",
											Value:          "testvalue",
											Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
											Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
											TolerationSecs: utils.Ptr(int64(10)),
										},
									},
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								NodeConfig: &pbrw.NodeConfig{
									NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
										Name: "test_compute_cm",
										Key:  "test_key",
									},
								},
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					S3: &rwv1alpha1.RisingWaveStateStoreBackendS3{
						RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Region: "test_region",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: utils.Ptr(false),
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
										Affinity: &corev1.Affinity{
											NodeAffinity: &corev1.NodeAffinity{
												RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
													NodeSelectorTerms: []corev1.NodeSelectorTerm{
														{
															MatchExpressions: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key1",
																	Operator: corev1.NodeSelectorOpDoesNotExist,
																	Values:   []string{"val1", "val2"},
																},
															},
															MatchFields: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key2",
																	Operator: corev1.NodeSelectorOpNotIn,
																	Values:   []string{"val3", "val4"},
																},
															},
														},
													},
												},
											},
										},
										Tolerations: []corev1.Toleration{
											{
												Key:               "testkey",
												Value:             "testvalue",
												Effect:            corev1.TaintEffectNoExecute,
												Operator:          corev1.TolerationOpEqual,
												TolerationSeconds: utils.Ptr(int64(10)),
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Configuration: &rwv1alpha1.RisingWaveNodeConfiguration{
									ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
										Name: "test_compute_cm",
										Key:  "test_key",
									},
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
										Labels:      map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"postgresql meta store": {
			input: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				EnableStandaloneMode:        false,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_S3StateStore{
						S3StateStore: &pbrw.StateStoreBackendS3{
							Bucket: "test_bucket",
							Region: "test_region",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					PostgresqlBackend: &pbrw.MetaStoreBackendPostgreSql{
						Host:     "test_host",
						Port:     5432,
						Database: "test_db",
						Credentials: &pbrw.PostgreSqlCredentials{
							SecretName: "test_secret",
						},
						Options: map[string]string{"key1": "val1"},
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
			expected: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				EnableStandaloneMode:        utils.Ptr(false),
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					S3: &rwv1alpha1.RisingWaveStateStoreBackendS3{
						RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Region: "test_region",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					PostgreSQL: &rwv1alpha1.RisingWaveMetaStoreBackendPostgreSQL{
						Host:     "test_host",
						Port:     5432,
						Database: "test_db",
						RisingWaveDBCredentials: rwv1alpha1.RisingWaveDBCredentials{
							SecretName: "test_secret",
						},
						Options: map[string]string{"key1": "val1"},
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			rw, err := FromRisingWaveSpecProto(tt.input)
			require.NoError(t, err)
			assert.Equal(t, tt.expected, rw, "unexpected output, result: %v, test case: %v", rw, tt)
		})
	}
}

func TestToRisingWaveConversion(t *testing.T) {
	expectedCPULimit := resource.NewScaledQuantity(500, resource.Milli)
	// Generate s filed
	_ = expectedCPULimit.String()
	expectedCPURequest := resource.NewScaledQuantity(250, resource.Milli)
	_ = expectedCPURequest.String()

	tests := map[string]struct {
		expected *pbrw.RisingWaveSpec
		input    *rwv1alpha1.RisingWaveSpec
	}{
		"gcp GCS test with affinity and toleration": {
			expected: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_GcsStateStore{
						GcsStateStore: &pbrw.StateStoreBackendGCS{
							Bucket: "test_bucket",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					StandaloneComponent: &pbrw.StandaloneSpec{
						LogLevel:        "info",
						UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE},
						NodePodSpec: &pbrw.NodePodSpec{
							ContainerSpec: &pbrw.NodePodContainerSpec{
								Envs: map[string]string{
									"key1": "val1",
								},
								Resources: &pbk8s.ResourceRequirements{
									CpuRequest:    "250m",
									CpuLimit:      "500m",
									MemoryRequest: "64Mi",
									MemoryLimit:   "128Mi",
								},
							},
							ServiceAccount: "test_sa",
							Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
							Labels:         map[string]string{"k1": "v1", "k2": "v2"},
						},
					},
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								PersistentVolumeClaimRetentionPolicy: &pbrw.PersistentVolumeClaimRetentionPolicy{
									WhenDeleted: pbrw.PersistentVolumeClaimRetentionPolicyType_DELETE,
									WhenScaled:  pbrw.PersistentVolumeClaimRetentionPolicyType_RETAIN,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			input: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					GCS: &rwv1alpha1.RisingWaveStateStoreBackendGCS{
						RisingWaveGCSCredentials: rwv1alpha1.RisingWaveGCSCredentials{
							UseWorkloadIdentity: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Root:   "test_dir",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
						LogLevel:        "info",
						UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
						Template: rwv1alpha1.RisingWaveNodePodTemplate{
							ObjectMeta: rwv1alpha1.PartialObjectMeta{
								Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
								Labels:      map[string]string{"k1": "v1", "k2": "v2"},
							},
							Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
								RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
									Env: []corev1.EnvVar{
										{
											Name:  "key1",
											Value: "val1",
										},
									},
									Resources: corev1.ResourceRequirements{
										Limits: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("500m"),
											corev1.ResourceMemory: resource.MustParse("128Mi"),
										},
										Requests: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("250m"),
											corev1.ResourceMemory: resource.MustParse("64Mi"),
										},
									},
								},
								ServiceAccountName: "test_sa",
							},
						},
					},
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								PersistentVolumeClaimRetentionPolicy: &v1.StatefulSetPersistentVolumeClaimRetentionPolicy{
									WhenDeleted: "Delete",
									WhenScaled:  "Retain",
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"test standalone conversion": {
			expected: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_S3StateStore{
						S3StateStore: &pbrw.StateStoreBackendS3{
							Bucket: "test_bucket",
							Region: "test_region",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: true,
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					StandaloneComponent: &pbrw.StandaloneSpec{
						LogLevel:        "info",
						UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE},
						NodePodSpec: &pbrw.NodePodSpec{
							ContainerSpec: &pbrw.NodePodContainerSpec{
								Envs: map[string]string{
									"key1": "val1",
								},
								Resources: &pbk8s.ResourceRequirements{
									CpuRequest:    "250m",
									CpuLimit:      "500m",
									MemoryRequest: "64Mi",
									MemoryLimit:   "128Mi",
								},
							},
							ServiceAccount: "test_sa",
							Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
							Labels:         map[string]string{"k1": "v1", "k2": "v2"},
						},
					},
					MetaSpec:      &pbrw.ComponentSpec{},
					FrontendSpec:  &pbrw.ComponentSpec{},
					ComputeSpec:   &pbrw.ComponentSpec{},
					CompactorSpec: &pbrw.ComponentSpec{},
				},
			},
			input: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					S3: &rwv1alpha1.RisingWaveStateStoreBackendS3{
						RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Region: "test_region",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				EnableStandaloneMode: utils.Ptr(true),
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
						LogLevel:        "info",
						UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
						Template: rwv1alpha1.RisingWaveNodePodTemplate{
							ObjectMeta: rwv1alpha1.PartialObjectMeta{
								Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
								Labels:      map[string]string{"k1": "v1", "k2": "v2"},
							},
							Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
								RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
									Env: []corev1.EnvVar{
										{
											Name:  "key1",
											Value: "val1",
										},
									},
									Resources: corev1.ResourceRequirements{
										Limits: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("500m"),
											corev1.ResourceMemory: resource.MustParse("128Mi"),
										},
										Requests: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("250m"),
											corev1.ResourceMemory: resource.MustParse("64Mi"),
										},
									},
								},
								ServiceAccountName: "test_sa",
							},
						},
					},
				},
			},
		},
		"aws s3 test with affinity and toleration": {
			expected: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_S3StateStore{
						S3StateStore: &pbrw.StateStoreBackendS3{
							Bucket: "test_bucket",
							Region: "test_region",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					StandaloneComponent: &pbrw.StandaloneSpec{
						LogLevel:        "info",
						UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE},
						NodePodSpec: &pbrw.NodePodSpec{
							ContainerSpec: &pbrw.NodePodContainerSpec{
								Envs: map[string]string{
									"key1": "val1",
								},
								Resources: &pbk8s.ResourceRequirements{
									CpuRequest:    "250m",
									CpuLimit:      "500m",
									MemoryRequest: "64Mi",
									MemoryLimit:   "128Mi",
								},
							},
							ServiceAccount: "test_sa",
							Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
							Labels:         map[string]string{"k1": "v1", "k2": "v2"},
						},
					},
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									Affinity: &pbk8s.Affinity{
										NodeAffinity: &pbk8s.NodeAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
												NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
													{
														MatchExpressions: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key1",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
																Values:   []string{"val1", "val2"},
															},
														},
														MatchFields: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key2",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
																Values:   []string{"val3", "val4"},
															},
														},
													},
												},
											},
										},
									},
									Tolerations: []*pbk8s.Toleration{
										{
											Key:            "testkey",
											Value:          "testvalue",
											Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
											Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
											TolerationSecs: utils.Ptr(int64(10)),
										},
									},
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			input: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					S3: &rwv1alpha1.RisingWaveStateStoreBackendS3{
						RisingWaveS3Credentials: rwv1alpha1.RisingWaveS3Credentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Bucket: "test_bucket",
						Region: "test_region",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
						LogLevel:        "info",
						UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
						Template: rwv1alpha1.RisingWaveNodePodTemplate{
							ObjectMeta: rwv1alpha1.PartialObjectMeta{
								Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
								Labels:      map[string]string{"k1": "v1", "k2": "v2"},
							},
							Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
								RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
									Env: []corev1.EnvVar{
										{
											Name:  "key1",
											Value: "val1",
										},
									},
									Resources: corev1.ResourceRequirements{
										Limits: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("500m"),
											corev1.ResourceMemory: resource.MustParse("128Mi"),
										},
										Requests: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("250m"),
											corev1.ResourceMemory: resource.MustParse("64Mi"),
										},
									},
								},
								ServiceAccountName: "test_sa",
							},
						},
					},
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
										Affinity: &corev1.Affinity{
											NodeAffinity: &corev1.NodeAffinity{
												RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
													NodeSelectorTerms: []corev1.NodeSelectorTerm{
														{
															MatchExpressions: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key1",
																	Operator: corev1.NodeSelectorOpDoesNotExist,
																	Values:   []string{"val1", "val2"},
																},
															},
															MatchFields: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key2",
																	Operator: corev1.NodeSelectorOpNotIn,
																	Values:   []string{"val3", "val4"},
																},
															},
														},
													},
												},
											},
										},
										Tolerations: []corev1.Toleration{
											{
												Key:               "testkey",
												Value:             "testvalue",
												Effect:            corev1.TaintEffectNoExecute,
												Operator:          corev1.TolerationOpEqual,
												TolerationSeconds: utils.Ptr(int64(10)),
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
		"azure blob test with affinity and toleration": {
			expected: &pbrw.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: true,
				EnableFullKubernetesAddr:    true,
				EnableEmbeddedServingMode:   true,
				FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
				Config: &pbrw.Config{
					NodeConfig: &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: &pbrw.StateStoreSpec{
					DataDirectory: "test_dir",
					Backend: &pbrw.StateStoreSpec_AzblobStateStore{
						AzblobStateStore: &pbrw.StateStoreBackendAzblob{
							Container: "test_bucket",
							Endpoint:  "test_endpoint",
						},
					},
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{
					EtcdBackend: &pbrw.MetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &pbrw.LicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: &pbrw.SecretStore{
					PrivateKey: &pbrw.SecretStorePrivateKey{
						SecretRef: &pbrw.SecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: &pbrw.ComponentsSpec{
					StandaloneComponent: &pbrw.StandaloneSpec{
						LogLevel:        "info",
						UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE},
						NodePodSpec: &pbrw.NodePodSpec{
							ContainerSpec: &pbrw.NodePodContainerSpec{
								Envs: map[string]string{
									"key1": "val1",
								},
								Resources: &pbk8s.ResourceRequirements{
									CpuRequest:    "250m",
									CpuLimit:      "500m",
									MemoryRequest: "64Mi",
									MemoryLimit:   "128Mi",
								},
							},
							ServiceAccount: "test_sa",
							Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
							Labels:         map[string]string{"k1": "v1", "k2": "v2"},
						},
					},
					MetaSpec: &pbrw.ComponentSpec{
						LogLevel: "info",
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
									Affinity: &pbk8s.Affinity{
										NodeAffinity: &pbk8s.NodeAffinity{
											RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
												NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
													{
														MatchExpressions: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key1",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
																Values:   []string{"val1", "val2"},
															},
														},
														MatchFields: []*pbk8s.NodeSelectorRequirement{
															{
																Key:      "key2",
																Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
																Values:   []string{"val3", "val4"},
															},
														},
													},
												},
											},
										},
									},
									Tolerations: []*pbk8s.Toleration{
										{
											Key:            "testkey",
											Value:          "testvalue",
											Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
											Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
											TolerationSecs: utils.Ptr(int64(10)),
										},
									},
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
								},
							},
						},
					},
					FrontendSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					ComputeSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
					CompactorSpec: &pbrw.ComponentSpec{
						NodeGroups: []*pbrw.NodeGroupSpec{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
									Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
								},
								NodePodSpec: &pbrw.NodePodSpec{
									ServiceAccount: "test_sa",
									Labels:         map[string]string{"k1": "v1", "k2": "v2"},
									ContainerSpec: &pbrw.NodePodContainerSpec{
										Envs: map[string]string{
											"key1": "val1",
										},
										Resources: &pbk8s.ResourceRequirements{
											CpuRequest:    "250m",
											CpuLimit:      "500m",
											MemoryRequest: "64Mi",
											MemoryLimit:   "128Mi",
										},
									},
								},
							},
						},
					},
				},
			},
			input: &rwv1alpha1.RisingWaveSpec{
				Image:                       "test_image",
				EnableDefaultServiceMonitor: utils.Ptr(true),
				EnableFullKubernetesAddr:    utils.Ptr(true),
				EnableEmbeddedServingMode:   utils.Ptr(true),
				FrontendServiceType:         corev1.ServiceTypeClusterIP,
				Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
					RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
						ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
							Name: "test_cm",
							Key:  "test_key",
						},
					},
				},
				StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
					DataDirectory: "test_dir",
					AzureBlob: &rwv1alpha1.RisingWaveStateStoreBackendAzureBlob{
						RisingWaveAzureBlobCredentials: rwv1alpha1.RisingWaveAzureBlobCredentials{
							UseServiceAccount: utils.Ptr(true),
						},
						Container: "test_bucket",
						Endpoint:  "test_endpoint",
						Root:      "test_dir",
					},
				},
				MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
					Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
						Endpoint: "test_etcd",
					},
				},
				LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
					SecretName: "test-secret-name",
				},
				SecretStore: rwv1alpha1.RisingWaveSecretStore{
					PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
						SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
							Key:  "test-secret-store-key",
							Name: "test-secret-store-name",
						},
					},
				},
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
						LogLevel:        "info",
						UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
						Template: rwv1alpha1.RisingWaveNodePodTemplate{
							ObjectMeta: rwv1alpha1.PartialObjectMeta{
								Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
								Labels:      map[string]string{"k1": "v1", "k2": "v2"},
							},
							Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
								RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
									Env: []corev1.EnvVar{
										{
											Name:  "key1",
											Value: "val1",
										},
									},
									Resources: corev1.ResourceRequirements{
										Limits: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("500m"),
											corev1.ResourceMemory: resource.MustParse("128Mi"),
										},
										Requests: corev1.ResourceList{
											corev1.ResourceCPU:    resource.MustParse("250m"),
											corev1.ResourceMemory: resource.MustParse("64Mi"),
										},
									},
								},
								ServiceAccountName: "test_sa",
							},
						},
					},
					Meta: rwv1alpha1.RisingWaveComponent{
						LogLevel: "info",
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "meta",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
										Affinity: &corev1.Affinity{
											NodeAffinity: &corev1.NodeAffinity{
												RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
													NodeSelectorTerms: []corev1.NodeSelectorTerm{
														{
															MatchExpressions: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key1",
																	Operator: corev1.NodeSelectorOpDoesNotExist,
																	Values:   []string{"val1", "val2"},
																},
															},
															MatchFields: []corev1.NodeSelectorRequirement{
																{
																	Key:      "key2",
																	Operator: corev1.NodeSelectorOpNotIn,
																	Values:   []string{"val3", "val4"},
																},
															},
														},
													},
												},
											},
										},
										Tolerations: []corev1.Toleration{
											{
												Key:               "testkey",
												Value:             "testvalue",
												Effect:            corev1.TaintEffectNoExecute,
												Operator:          corev1.TolerationOpEqual,
												TolerationSeconds: utils.Ptr(int64(10)),
											},
										},
									},
								},
							},
						},
					},
					Frontend: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "frontend",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compute: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compute",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
					Compactor: rwv1alpha1.RisingWaveComponent{
						NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
							{
								Name:     "compactor",
								Replicas: 1,
								UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
									Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
								},
								Template: rwv1alpha1.RisingWaveNodePodTemplate{
									ObjectMeta: rwv1alpha1.PartialObjectMeta{
										Labels: map[string]string{"k1": "v1", "k2": "v2"},
									},
									Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
										ServiceAccountName: "test_sa",
										RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
											Env: []corev1.EnvVar{
												{
													Name:  "key1",
													Value: "val1",
												},
											},
											Resources: corev1.ResourceRequirements{
												Limits: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPULimit,
													corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
												},
												Requests: corev1.ResourceList{
													corev1.ResourceCPU:    *expectedCPURequest,
													corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
												},
											},
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			rw, err := ToRisingWaveSpecProto(tt.input)
			require.NoError(t, err)
			assert.Equal(t, tt.expected, rw, "unexpected output, result: %v, test case: %v", rw, tt)
		})
	}
}
