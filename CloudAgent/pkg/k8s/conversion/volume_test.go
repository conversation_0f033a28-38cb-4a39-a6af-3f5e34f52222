package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	"k8s.io/utils/ptr"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromVolumeProto(t *testing.T) {
	sizeLimit := resource.NewQuantity(100*1024*1024, "BinarySI")
	// Generate `s` filed
	_ = sizeLimit.String()
	tests := map[string]struct {
		input    *pbk8s.Volume
		expected corev1.Volume
	}{
		"pvc": {
			input: &pbk8s.Volume{
				Name: "test_volume",
				VolumneSource: &pbk8s.Volume_PersistentVolumeClaim{
					PersistentVolumeClaim: &pbk8s.PersistentVolumeClaimVolumeSource{
						ClaimName: "test_claim_name",
						ReadOnly:  true,
					},
				},
			},
			expected: corev1.Volume{
				Name: "test_volume",
				VolumeSource: corev1.VolumeSource{
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: "test_claim_name",
						ReadOnly:  true,
					},
				},
			},
		},
		"empty dir": {
			input: &pbk8s.Volume{
				Name: "test_volume",
				VolumneSource: &pbk8s.Volume_EmptyDir{
					EmptyDir: &pbk8s.EmptyDirVolumeSource{
						Medium:    "Memory",
						SizeLimit: ptr.To("100Mi"),
					},
				},
			},
			expected: corev1.Volume{
				Name: "test_volume",
				VolumeSource: corev1.VolumeSource{
					EmptyDir: &corev1.EmptyDirVolumeSource{
						Medium:    "Memory",
						SizeLimit: sizeLimit,
					},
				},
			},
		},
		"secret": {
			input: &pbk8s.Volume{
				Name: "test_volume",
				VolumneSource: &pbk8s.Volume_Secret{
					Secret: &pbk8s.SecretVolumeSource{
						SecretName: "secret",
						Items: []*pbk8s.KeyToPath{
							{
								Key:  "k1",
								Path: "p1",
								Mode: utils.Ptr(int32(666)),
							},
						},
						DefaultMode: utils.Ptr(int32(666)),
						IsOptional:  utils.Ptr(false),
					},
				},
			},
			expected: corev1.Volume{
				Name: "test_volume",
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{
						SecretName: "secret",
						Items: []corev1.KeyToPath{
							{
								Key:  "k1",
								Path: "p1",
								Mode: utils.Ptr(int32(666)),
							},
						},
						DefaultMode: utils.Ptr(int32(666)),
						Optional:    utils.Ptr(false),
					},
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			spec, err := FromVolumeProto(tt.input)
			require.NoError(t, err)
			assert.Equal(t, spec, tt.expected, "unexpected output, result: %v, test case: %v", spec, tt)
		})
	}
}

func TestToVolumeProto(t *testing.T) {
	sizeLimit := resource.NewQuantity(100*1024*1024, "BinarySI")
	// Generate `s` filed
	_ = sizeLimit.String()
	tests := map[string]struct {
		input    corev1.Volume
		expected *pbk8s.Volume
	}{
		"pvc": {
			input: corev1.Volume{
				Name: "test_volume",
				VolumeSource: corev1.VolumeSource{
					PersistentVolumeClaim: &corev1.PersistentVolumeClaimVolumeSource{
						ClaimName: "test_claim_name",
						ReadOnly:  true,
					},
				},
			},
			expected: &pbk8s.Volume{
				Name: "test_volume",
				VolumneSource: &pbk8s.Volume_PersistentVolumeClaim{
					PersistentVolumeClaim: &pbk8s.PersistentVolumeClaimVolumeSource{
						ClaimName: "test_claim_name",
						ReadOnly:  true,
					},
				},
			},
		},
		"empty dir": {
			input: corev1.Volume{
				Name: "test_volume",
				VolumeSource: corev1.VolumeSource{
					EmptyDir: &corev1.EmptyDirVolumeSource{
						Medium:    "Memory",
						SizeLimit: sizeLimit,
					},
				},
			},
			expected: &pbk8s.Volume{
				Name: "test_volume",
				VolumneSource: &pbk8s.Volume_EmptyDir{
					EmptyDir: &pbk8s.EmptyDirVolumeSource{
						Medium:    "Memory",
						SizeLimit: ptr.To("100Mi"),
					},
				},
			},
		},
		"secret": {
			input: corev1.Volume{
				Name: "test_volume",
				VolumeSource: corev1.VolumeSource{
					Secret: &corev1.SecretVolumeSource{
						SecretName: "secret",
						Items: []corev1.KeyToPath{
							{
								Key:  "k1",
								Path: "p1",
								Mode: utils.Ptr(int32(666)),
							},
						},
						DefaultMode: utils.Ptr(int32(666)),
						Optional:    utils.Ptr(false),
					},
				},
			},
			expected: &pbk8s.Volume{
				Name: "test_volume",
				VolumneSource: &pbk8s.Volume_Secret{
					Secret: &pbk8s.SecretVolumeSource{
						SecretName: "secret",
						Items: []*pbk8s.KeyToPath{
							{
								Key:  "k1",
								Path: "p1",
								Mode: utils.Ptr(int32(666)),
							},
						},
						DefaultMode: utils.Ptr(int32(666)),
						IsOptional:  utils.Ptr(false),
					},
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			spec, err := ToVolumeProto(tt.input)
			require.NoError(t, err)
			assert.Equal(t, spec, tt.expected, "unexpected output, result: %v, test case: %v", spec, tt)
		})
	}
}

func TestToVolumeMountProto(t *testing.T) {
	input := corev1.VolumeMount{
		Name:             "test_volume_mount",
		ReadOnly:         true,
		MountPath:        "/temp/test",
		SubPath:          "/sub",
		MountPropagation: ptr.To(corev1.MountPropagationMode(corev1.MountOptionAnnotation)),
		SubPathExpr:      "/sub*",
	}
	expected := &pbk8s.VolumeMount{
		Name:             "test_volume_mount",
		ReadOnly:         true,
		MountPath:        "/temp/test",
		SubPath:          "/sub",
		MountPropagation: corev1.MountOptionAnnotation,
		SubPathExpr:      "/sub*",
	}
	spec := ToVolumeMountProto(input)
	assert.Equal(t, spec, expected, "unexpected output, result: %v, test case: %v", spec, t)
}

func TestFromVolumeMountProto(t *testing.T) {
	input := &pbk8s.VolumeMount{
		Name:             "test_volume_mount",
		ReadOnly:         true,
		MountPath:        "/temp/test",
		SubPath:          "/sub",
		MountPropagation: corev1.MountOptionAnnotation,
		SubPathExpr:      "/sub*",
	}
	expected := corev1.VolumeMount{
		Name:             "test_volume_mount",
		ReadOnly:         true,
		MountPath:        "/temp/test",
		SubPath:          "/sub",
		MountPropagation: ptr.To(corev1.MountPropagationMode(corev1.MountOptionAnnotation)),
		SubPathExpr:      "/sub*",
	}

	spec := FromVolumeMountProto(input)
	assert.Equal(t, spec, expected, "unexpected output, result: %v, test case: %v", spec, t)
}
