package conversion

import (
	"maps"

	asodb4pg "github.com/Azure/azure-service-operator/v2/api/dbforpostgresql/v1api20221201"
	asoruntime "github.com/Azure/azure-service-operator/v2/pkg/genruntime"

	"github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func FromPGServerSpecProto(spec *azr.PGServerSpec) (asodb4pg.FlexibleServer_Spec, error) {
	return asodb4pg.FlexibleServer_Spec{
		AzureName:                  spec.GetAzureName(),
		Owner:                      FromKnownResourceReferenceProto(spec.GetOwner()),
		Version:                    utils.Ptr(asodb4pg.ServerVersion(spec.GetVersion())),
		Location:                   utils.Ptr(spec.GetLocation()),
		Sku:                        FromPGServerSkuProto(spec.GetSku()),
		Storage:                    FromPGServerStorageProto(spec.GetStorage()),
		AdministratorLogin:         utils.Ptr(spec.GetAdministratorLogin()),
		AdministratorLoginPassword: FromSecretReferenceProto(spec.GetAdministratorLoginPassword()),
		Network:                    FromPGServerNetworkProto(spec.GetNetwork()),
		Tags:                       maps.Clone(spec.GetTags()),
	}, nil
}

func FromKnownResourceReferenceProto(owner *azr.ResourceReference) *asoruntime.KnownResourceReference {
	if owner == nil {
		return nil
	}
	return &asoruntime.KnownResourceReference{
		ARMID: owner.GetArmId(),
	}
}

func FromPGServerSkuProto(sku *azr.PGServerSku) *asodb4pg.Sku {
	return &asodb4pg.Sku{
		Name: utils.Ptr(sku.GetName()),
		Tier: utils.Ptr(asodb4pg.Sku_Tier(sku.GetTier())),
	}
}

func FromPGServerStorageProto(storage *azr.PGServerStorage) *asodb4pg.Storage {
	return &asodb4pg.Storage{
		StorageSizeGB: utils.Ptr(int(storage.GetStorageSizeGb())),
	}
}

func FromSecretReferenceProto(password *azr.SecretKeyRef) *asoruntime.SecretReference {
	return &asoruntime.SecretReference{
		Name: password.GetName(),
		Key:  password.GetKey(),
	}
}

func FromPGServerNetworkProto(network *azr.PGServerNetwork) *asodb4pg.Network {
	return &asodb4pg.Network{
		DelegatedSubnetResourceReference:   FromResourceReferenceProto(network.GetDelegatedSubnet()),
		PrivateDnsZoneArmResourceReference: FromResourceReferenceProto(network.GetPrivateDnsZone()),
	}
}

func FromResourceReferenceProto(ref *azr.ResourceReference) *asoruntime.ResourceReference {
	return &asoruntime.ResourceReference{
		ARMID: ref.GetArmId(),
	}
}
