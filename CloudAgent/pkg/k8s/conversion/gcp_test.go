package conversion

import (
	"testing"

	gcck8s "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	gccsql "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/sql/v1beta1"
	"github.com/aws/smithy-go/ptr"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromSQLInstanceSpecProto(t *testing.T) {
	tests := []struct {
		name    string
		proto   *pbgcp.SQLInstanceSpec
		want    *gccsql.SQLInstanceSpec
		wantErr bool
	}{
		{
			name: "regular",
			proto: &pbgcp.SQLInstanceSpec{
				ResourceId:      "test-id",
				InstanceType:    "CLOUD_SQL_INSTANCE",
				DatabaseVersion: "POSTGRES_14",
				Region:          "region1",
				RootPassword: &pbgcp.RootPassword{
					Value: utils.Ptr("password"),
				},
				Settings: &pbgcp.SQLInstanceSettings{
					Tier:     "test-tier",
					DiskSize: 20,
					IpConfiguration: &pbgcp.IPConfiguration{
						Ipv4Enabled: false,
						PrivateNetworkRef: &pbgcp.PrivateNetworkRef{
							External: "external-private-network",
						},
						AllocatedIpRange: "ip-range-name",
						SslMode:          pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_ENCRYPTED_ONLY,
					},
					DeletionProtectionEnabled: false,
					DatabaseFlags: []*pbgcp.DatabaseFlag{
						{
							Name:  "flag-name",
							Value: "flag-value",
						},
					},
				},
			},
			want: &gccsql.SQLInstanceSpec{
				ResourceID:      ptr.String("test-id"),
				InstanceType:    ptr.String("CLOUD_SQL_INSTANCE"),
				DatabaseVersion: ptr.String("POSTGRES_14"),
				Region:          ptr.String("region1"),
				RootPassword: &gccsql.InstanceRootPassword{
					Value: utils.Ptr("password"),
				},
				Settings: gccsql.InstanceSettings{
					Tier:     "test-tier",
					DiskSize: utils.Ptr(int64(20)),
					IpConfiguration: &gccsql.InstanceIpConfiguration{
						Ipv4Enabled: utils.Ptr(false),
						PrivateNetworkRef: &gcck8s.ResourceRef{
							External: "external-private-network",
						},
						AllocatedIpRange: utils.Ptr("ip-range-name"),
						SslMode:          utils.Ptr("ENCRYPTED_ONLY"),
					},
					DeletionProtectionEnabled: utils.Ptr(false),
					DatabaseFlags: []gccsql.InstanceDatabaseFlags{
						{Name: "flag-name", Value: "flag-value"},
					},
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := FromSQLInstanceSpecProto(tt.proto)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, got)
		})
	}
}
