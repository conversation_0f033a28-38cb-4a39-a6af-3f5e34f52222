package conversion

import (
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func TestFromAffinityConversion(t *testing.T) {
	tests := []struct {
		input    *pbk8s.Affinity
		expected *corev1.Affinity
	}{
		{
			input: &pbk8s.Affinity{
				NodeAffinity: &pbk8s.NodeAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
						NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
							{
								MatchExpressions: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_EXISTS,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []*pbk8s.PreferredSchedulingTerm{
						{
							Weight: 1,
							Preference: &pbk8s.NodeSelectorTerm{
								MatchExpressions: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_GT,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_LT,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
				},
				PodAffinity: &pbk8s.PodAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []*pbk8s.PodAffinityTerm{
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []*pbk8s.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},
				// Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod
				// in the same node, zone, etc. as some other pod(s)).
				PodAntiAffinity: &pbk8s.PodAntiAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []*pbk8s.PodAffinityTerm{
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []*pbk8s.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},
			},

			expected: &corev1.Affinity{
				NodeAffinity: &corev1.NodeAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
						NodeSelectorTerms: []corev1.NodeSelectorTerm{
							{
								MatchExpressions: []corev1.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: corev1.NodeSelectorOpDoesNotExist,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: corev1.NodeSelectorOpExists,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []corev1.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: corev1.NodeSelectorOpIn,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: corev1.NodeSelectorOpNotIn,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{
						{
							Weight: 1,
							Preference: corev1.NodeSelectorTerm{
								MatchExpressions: []corev1.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: corev1.NodeSelectorOpGt,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: corev1.NodeSelectorOpLt,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []corev1.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: corev1.NodeSelectorOpIn,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: corev1.NodeSelectorOpNotIn,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
				},

				PodAffinity: &corev1.PodAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
					},
				},
				PodAntiAffinity: &corev1.PodAntiAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		affinity, err := FromAffinityProto(tt.input)
		require.NoError(t, err)
		assert.Equal(t, affinity, tt.expected, "unexpected output, result: %v, test case: %v", affinity, tt)
	}
}

func TestInvalidNodeSelectorOperatorConversion(t *testing.T) {
	_, err := FromNodeSelectorOperatorProto(pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_UNKNOWN)
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(err))
}

func TestInvalidLabelSelectorOperatorConversion(t *testing.T) {
	_, err := FromLabelSelectorOperatorProto(pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_UNKNOWN)
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(err))
}

func TestToAffinityConversion(t *testing.T) {
	tests := []struct {
		expected *pbk8s.Affinity
		input    *corev1.Affinity
	}{
		{
			expected: &pbk8s.Affinity{
				NodeAffinity: &pbk8s.NodeAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
						NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
							{
								MatchExpressions: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_EXISTS,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []*pbk8s.PreferredSchedulingTerm{
						{
							Weight: 1,
							Preference: &pbk8s.NodeSelectorTerm{
								MatchExpressions: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_GT,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_LT,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []*pbk8s.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
				},
				PodAffinity: &pbk8s.PodAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []*pbk8s.PodAffinityTerm{
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []*pbk8s.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},
				// Describes pod anti-affinity scheduling rules (e.g. avoid putting this pod
				// in the same node, zone, etc. as some other pod(s)).
				PodAntiAffinity: &pbk8s.PodAntiAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []*pbk8s.PodAffinityTerm{
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
						{
							LabelSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []*pbk8s.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []*pbk8s.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: &pbk8s.PodAffinityTerm{
								LabelSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []*pbk8s.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_EXISTS,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_DOES_NOT_EXIST,
										},
									},
								},
							},
						},
					},
				},
			},

			input: &corev1.Affinity{
				NodeAffinity: &corev1.NodeAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
						NodeSelectorTerms: []corev1.NodeSelectorTerm{
							{
								MatchExpressions: []corev1.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: corev1.NodeSelectorOpDoesNotExist,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: corev1.NodeSelectorOpExists,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []corev1.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: corev1.NodeSelectorOpIn,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: corev1.NodeSelectorOpNotIn,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []corev1.PreferredSchedulingTerm{
						{
							Weight: 1,
							Preference: corev1.NodeSelectorTerm{
								MatchExpressions: []corev1.NodeSelectorRequirement{
									{
										Key:      "key1",
										Operator: corev1.NodeSelectorOpGt,
										Values:   []string{"val1", "val2"},
									},
									{
										Key:      "key2",
										Operator: corev1.NodeSelectorOpLt,
										Values:   []string{"val3", "val4"},
									},
								},
								MatchFields: []corev1.NodeSelectorRequirement{
									{
										Key:      "key3",
										Operator: corev1.NodeSelectorOpIn,
										Values:   []string{"val5", "val6"},
									},
									{
										Key:      "key4",
										Operator: corev1.NodeSelectorOpNotIn,
										Values:   []string{"val7", "val8"},
									},
								},
							},
						},
					},
				},

				PodAffinity: &corev1.PodAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
					},
				},
				PodAntiAffinity: &corev1.PodAntiAffinity{
					RequiredDuringSchedulingIgnoredDuringExecution: []corev1.PodAffinityTerm{
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns1", "ns2"},
							TopologyKey: "key1",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
						{
							LabelSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key1",
										Values:   []string{"val1", "val2"},
										Operator: metav1.LabelSelectorOpIn,
									},
									{
										Key:      "key2",
										Values:   []string{"val3", "val4"},
										Operator: metav1.LabelSelectorOpNotIn,
									},
								},
							},
							Namespaces:  []string{"ns3", "ns4"},
							TopologyKey: "key2",
							NamespaceSelector: &metav1.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
								MatchExpressions: []metav1.LabelSelectorRequirement{
									{
										Key:      "key3",
										Values:   []string{"val5", "val6"},
										Operator: metav1.LabelSelectorOpExists,
									},
									{
										Key:      "key4",
										Values:   []string{"val7", "val8"},
										Operator: metav1.LabelSelectorOpDoesNotExist,
									},
								},
							},
						},
					},
					PreferredDuringSchedulingIgnoredDuringExecution: []corev1.WeightedPodAffinityTerm{
						{
							Weight: 1,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns5", "ns6"},
								TopologyKey: "key3",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
						{
							Weight: 2,
							PodAffinityTerm: corev1.PodAffinityTerm{
								LabelSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key1",
											Values:   []string{"val1", "val2"},
											Operator: metav1.LabelSelectorOpIn,
										},
										{
											Key:      "key2",
											Values:   []string{"val3", "val4"},
											Operator: metav1.LabelSelectorOpNotIn,
										},
									},
								},
								Namespaces:  []string{"ns7", "ns8"},
								TopologyKey: "key4",
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3", "k4": "v4"},
									MatchExpressions: []metav1.LabelSelectorRequirement{
										{
											Key:      "key3",
											Values:   []string{"val5", "val6"},
											Operator: metav1.LabelSelectorOpExists,
										},
										{
											Key:      "key4",
											Values:   []string{"val7", "val8"},
											Operator: metav1.LabelSelectorOpDoesNotExist,
										},
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for _, tt := range tests {
		affinity, err := ToAffinityProto(tt.input)
		require.NoError(t, err)
		assert.Equal(t, affinity, tt.expected, "unexpected output, result: %v, test case: %v", affinity, tt)
	}
}
