package conversion

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestFromPersistentVolumeClaimSpecProto(t *testing.T) {
	storageResource := resource.NewQuantity(20*1024*1024*1024, "BinarySI")
	// Generate `s` filed
	_ = storageResource.String()
	tests := map[string]struct {
		input    *pbk8s.PersistentVolumeClaimSpec
		expected corev1.PersistentVolumeClaimSpec
	}{
		"normal case": {
			input: &pbk8s.PersistentVolumeClaimSpec{
				AccessModes: []pbk8s.PVCAccessMode{
					pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
				},
				Selector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: &pbk8s.VolumeResourceRequirements{
					StorageRequest: "20Gi",
				},
				VolumeName:       "test_volume",
				StorageClassName: "test_class",
			},
			expected: corev1.PersistentVolumeClaimSpec{
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteOnce,
				},
				Selector: &metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: *storageResource,
					},
				},
				VolumeName:       "test_volume",
				StorageClassName: utils.Ptr("test_class"),
			},
		},
		"omit optional fields": {
			input: &pbk8s.PersistentVolumeClaimSpec{
				Resources: &pbk8s.VolumeResourceRequirements{
					StorageRequest: "20Gi",
				},
			},
			expected: corev1.PersistentVolumeClaimSpec{
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: *storageResource,
					},
				},
			},
		},
		"multiple access modes": {
			input: &pbk8s.PersistentVolumeClaimSpec{
				AccessModes: []pbk8s.PVCAccessMode{
					pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
					pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE_POD,
				},
				Selector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: &pbk8s.VolumeResourceRequirements{
					StorageRequest: "20Gi",
				},
				VolumeName:       "test_volume",
				StorageClassName: "test_class",
			},
			expected: corev1.PersistentVolumeClaimSpec{
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteOnce,
					corev1.ReadWriteOncePod,
				},
				Selector: &metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: *storageResource,
					},
				},
				VolumeName:       "test_volume",
				StorageClassName: utils.Ptr("test_class"),
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			spec, err := FromPersistentVolumeClaimSpecProto(tt.input)
			require.NoError(t, err)
			assert.Equal(t, spec, tt.expected, "unexpected output, result: %v, test case: %v", spec, tt)
		})
	}
}

func TestToPersistentVolumeClaimSpecProto(t *testing.T) {
	storageResource := resource.NewQuantity(20*1024*1024*1024, "BinarySI")
	// Generate `s` filed
	_ = storageResource.String()
	tests := map[string]struct {
		input    corev1.PersistentVolumeClaimSpec
		expected *pbk8s.PersistentVolumeClaimSpec
	}{
		"normal case": {
			input: corev1.PersistentVolumeClaimSpec{
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteOnce,
				},
				Selector: &metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: *storageResource,
					},
				},
				VolumeName:       "test_volume",
				StorageClassName: utils.Ptr("test_class"),
			},
			expected: &pbk8s.PersistentVolumeClaimSpec{
				AccessModes: []pbk8s.PVCAccessMode{
					pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
				},
				Selector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: &pbk8s.VolumeResourceRequirements{
					StorageRequest: "20Gi",
				},
				VolumeName:       "test_volume",
				StorageClassName: "test_class",
			},
		},
		"omit optional fields": {
			input: corev1.PersistentVolumeClaimSpec{
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: *storageResource,
					},
				},
			},
			expected: &pbk8s.PersistentVolumeClaimSpec{
				Resources: &pbk8s.VolumeResourceRequirements{
					StorageRequest: "20Gi",
				},
			},
		},
		"multiple access modes": {
			input: corev1.PersistentVolumeClaimSpec{
				AccessModes: []corev1.PersistentVolumeAccessMode{
					corev1.ReadWriteOnce,
					corev1.ReadWriteOncePod,
				},
				Selector: &metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: corev1.VolumeResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceStorage: *storageResource,
					},
				},
				VolumeName:       "test_volume",
				StorageClassName: utils.Ptr("test_class"),
			},
			expected: &pbk8s.PersistentVolumeClaimSpec{
				AccessModes: []pbk8s.PVCAccessMode{
					pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
					pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE_POD,
				},
				Selector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Resources: &pbk8s.VolumeResourceRequirements{
					StorageRequest: "20Gi",
				},
				VolumeName:       "test_volume",
				StorageClassName: "test_class",
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			spec, err := ToPersistentVolumeClaimSpecProto(tt.input)
			require.NoError(t, err)
			assert.Equal(t, spec, tt.expected, "unexpected output, result: %v, test case: %v", spec, tt)
		})
	}
}
