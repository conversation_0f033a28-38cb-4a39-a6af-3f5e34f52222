package conversion

import (
	gcck8s "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	gccsql "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/sql/v1beta1"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	"github.com/risingwavelabs/eris"
)

const (
	CloudSQLSSLModeAllowUnencryptedAndEncrypted                  = "ALLOW_UNENCRYPTED_AND_ENCRYPTED"
	CloudSQLSSLModeEncryptedOnly                                 = "ENCRYPTED_ONLY"
	CloudSQLSSLModeEncryptedOnlyTrustedClientCertificateRequired = "TRUSTED_CLIENT_CERTIFICATE_REQUIRED"
)

func FromSQLInstanceSpecProto(p *pbgcp.SQLInstanceSpec) (*gccsql.SQLInstanceSpec, error) {
	settings, err := FromSQLInstanceSettings(p.GetSettings())
	if err != nil {
		return nil, err
	}
	return &gccsql.SQLInstanceSpec{
		ResourceID:      utils.Ptr(p.GetResourceId()),
		InstanceType:    utils.Ptr(p.GetInstanceType()),
		DatabaseVersion: utils.Ptr(p.GetDatabaseVersion()),
		Region:          utils.Ptr(p.GetRegion()),
		RootPassword:    FromRootPasswordProto(p.GetRootPassword()),
		Settings:        *settings,
	}, nil
}

func FromRootPasswordProto(p *pbgcp.RootPassword) *gccsql.InstanceRootPassword {
	var valueFrom *gccsql.InstanceValueFrom
	if p.GetValueFrom() != nil && p.GetValueFrom().GetSecretKeyRef() != nil {
		valueFrom = &gccsql.InstanceValueFrom{
			SecretKeyRef: &gcck8s.SecretKeyRef{
				Key:  p.GetValueFrom().GetSecretKeyRef().GetKey(),
				Name: p.GetValueFrom().GetSecretKeyRef().GetName(),
			},
		}
	}
	return &gccsql.InstanceRootPassword{
		Value:     p.Value,
		ValueFrom: valueFrom,
	}
}

func FromCloudSQLSSLMode(p pbgcp.CloudSQLSSLMode) (string, error) {
	switch p {
	case pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_ALLOW_UNENCRYPTED_AND_ENCRYPTED:
		return CloudSQLSSLModeAllowUnencryptedAndEncrypted, nil
	case pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_ENCRYPTED_ONLY:
		return CloudSQLSSLModeEncryptedOnly, nil
	case pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_TRUSTED_CLIENT_CERTIFICATE_REQUIRED:
		return CloudSQLSSLModeEncryptedOnlyTrustedClientCertificateRequired, nil
	case pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_UNKNOWN:
		return "", eris.Errorf("invalid CloudSQL SSL mode %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func FromSQLInstanceSettings(p *pbgcp.SQLInstanceSettings) (*gccsql.InstanceSettings, error) {
	var ipConfiguration *gccsql.InstanceIpConfiguration
	if p.GetIpConfiguration() != nil {
		sslMode := CloudSQLSSLModeAllowUnencryptedAndEncrypted
		var err error
		if p.GetIpConfiguration().GetSslMode() != pbgcp.CloudSQLSSLMode_CloudSQLSSLMode_UNKNOWN {
			sslMode, err = FromCloudSQLSSLMode(p.GetIpConfiguration().GetSslMode())
			if err != nil {
				return nil, err
			}
		}
		ipConfiguration = &gccsql.InstanceIpConfiguration{
			Ipv4Enabled: utils.Ptr(p.GetIpConfiguration().GetIpv4Enabled()),
			PrivateNetworkRef: &gcck8s.ResourceRef{
				External: p.GetIpConfiguration().GetPrivateNetworkRef().GetExternal(),
			},
			AllocatedIpRange: utils.Ptr(p.GetIpConfiguration().GetAllocatedIpRange()),
			SslMode:          &sslMode,
		}
	}
	var databaseFlags []gccsql.InstanceDatabaseFlags
	for _, flag := range p.GetDatabaseFlags() {
		databaseFlags = append(databaseFlags, gccsql.InstanceDatabaseFlags{
			Name:  flag.GetName(),
			Value: flag.GetValue(),
		})
	}

	return &gccsql.InstanceSettings{
		Tier:                      p.GetTier(),
		DiskSize:                  utils.Ptr(int64(p.GetDiskSize())),
		IpConfiguration:           ipConfiguration,
		DeletionProtectionEnabled: utils.Ptr(p.GetDeletionProtectionEnabled()),
		DatabaseFlags:             databaseFlags,
	}, nil
}
