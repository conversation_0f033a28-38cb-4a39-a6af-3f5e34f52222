package conversion

import (
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	"github.com/risingwavelabs/eris"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func FromNetworkPolicySpecProto(p *pbk8s.NetworkPolicySpec) (networkingv1.NetworkPolicySpec, error) {
	podSelector, err := FromLabelSelectorProto(p.GetPodSelector())
	if err != nil {
		return networkingv1.NetworkPolicySpec{}, err
	}
	var ingressRules []networkingv1.NetworkPolicyIngressRule
	for _, protoIngress := range p.GetIngress() {
		ingress, err := FromNetworkPolicyIngressRuleProto(protoIngress)
		if err != nil {
			return networkingv1.NetworkPolicySpec{}, err
		}
		ingressRules = append(ingressRules, ingress)
	}
	var egressRules []networkingv1.NetworkPolicyEgressRule
	for _, protoEgress := range p.GetEgress() {
		egress, err := FromNetworkPolicyEgressRuleProto(protoEgress)
		if err != nil {
			return networkingv1.NetworkPolicySpec{}, err
		}
		egressRules = append(egressRules, egress)
	}
	var types []networkingv1.PolicyType
	for _, protoType := range p.GetPolicyTypes() {
		policyType, err := FromNetworkPolicyTypeProto(protoType)
		if err != nil {
			return networkingv1.NetworkPolicySpec{}, err
		}
		types = append(types, policyType)
	}
	return networkingv1.NetworkPolicySpec{
		PodSelector: *podSelector,
		Ingress:     ingressRules,
		Egress:      egressRules,
		PolicyTypes: types,
	}, nil
}

func FromNetworkPolicyEgressRuleProto(p *pbk8s.NetworkPolicyEgressRule) (networkingv1.NetworkPolicyEgressRule, error) {
	var ports []networkingv1.NetworkPolicyPort
	for _, protoPort := range p.GetPorts() {
		port, err := FromNetworkPolicyPortProto(protoPort)
		if err != nil {
			return networkingv1.NetworkPolicyEgressRule{}, err
		}
		ports = append(ports, port)
	}
	var peers []networkingv1.NetworkPolicyPeer
	for _, protoPeer := range p.GetTo() {
		peer, err := FromNetworkPolicyPeerProto(protoPeer)
		if err != nil {
			return networkingv1.NetworkPolicyEgressRule{}, err
		}
		peers = append(peers, peer)
	}
	return networkingv1.NetworkPolicyEgressRule{
		Ports: ports,
		To:    peers,
	}, nil
}

func FromNetworkPolicyIngressRuleProto(p *pbk8s.NetworkPolicyIngressRule) (networkingv1.NetworkPolicyIngressRule, error) {
	var ports []networkingv1.NetworkPolicyPort
	for _, protoPort := range p.GetPorts() {
		port, err := FromNetworkPolicyPortProto(protoPort)
		if err != nil {
			return networkingv1.NetworkPolicyIngressRule{}, err
		}
		ports = append(ports, port)
	}
	var peers []networkingv1.NetworkPolicyPeer
	for _, protoPeer := range p.GetFrom() {
		peer, err := FromNetworkPolicyPeerProto(protoPeer)
		if err != nil {
			return networkingv1.NetworkPolicyIngressRule{}, err
		}
		peers = append(peers, peer)
	}
	return networkingv1.NetworkPolicyIngressRule{
		Ports: ports,
		From:  peers,
	}, nil
}

func FromNetworkPolicyPortProto(p *pbk8s.NetworkPolicyPort) (networkingv1.NetworkPolicyPort, error) {
	var err error
	var port *intstr.IntOrString
	if p.GetPort() != nil {
		port, err = FromIntOrStringProto(p.GetPort())
		if err != nil {
			return networkingv1.NetworkPolicyPort{}, err
		}
	}
	protocol, err := FromNetworkProtocolProto(p.GetProtocol())
	if err != nil {
		return networkingv1.NetworkPolicyPort{}, err
	}
	var endPort *int32
	if p.GetEndPort() != 0 {
		endPort = &p.EndPort
	}
	return networkingv1.NetworkPolicyPort{
		Port:     port,
		Protocol: &protocol,
		EndPort:  endPort,
	}, nil
}

func FromNetworkPolicyPeerProto(p *pbk8s.NetworkPolicyPeer) (networkingv1.NetworkPolicyPeer, error) {
	var err error
	var podSelector *metav1.LabelSelector
	if p.GetPodSelector() != nil {
		podSelector, err = FromLabelSelectorProto(p.GetPodSelector())
		if err != nil {
			return networkingv1.NetworkPolicyPeer{}, err
		}
	}
	var namespaceSelector *metav1.LabelSelector
	if p.GetNamespaceSelector() != nil {
		namespaceSelector, err = FromLabelSelectorProto(p.GetNamespaceSelector())
		if err != nil {
			return networkingv1.NetworkPolicyPeer{}, err
		}
	}
	var ipBlock *networkingv1.IPBlock
	if p.GetIpBlock() != nil {
		ipBlock = FromIPBlockProto(p.GetIpBlock())
	}
	return networkingv1.NetworkPolicyPeer{
		PodSelector:       podSelector,
		NamespaceSelector: namespaceSelector,
		IPBlock:           ipBlock,
	}, nil
}

func FromIPBlockProto(p *pbk8s.IPBlock) *networkingv1.IPBlock {
	return &networkingv1.IPBlock{
		CIDR:   p.GetCidr(),
		Except: p.GetExcept(),
	}
}

func FromIntOrStringProto(p *pbk8s.IntOrString) (*intstr.IntOrString, error) {
	if p != nil && p.IntVal != nil {
		return utils.Ptr(intstr.FromInt(int(p.GetIntVal()))), nil
	}
	if p != nil && p.StrVal != nil {
		return utils.Ptr(intstr.FromString(p.GetStrVal())), nil
	}
	return nil, eris.Errorf("invalid intstr %v", p)
}

func FromNetworkProtocolProto(p pbk8s.NetworkProtocol) (corev1.Protocol, error) {
	switch p {
	case pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP:
		return corev1.ProtocolTCP, nil
	case pbk8s.NetworkProtocol_NETWORK_PROTOCOL_UDP:
		return corev1.ProtocolUDP, nil
	case pbk8s.NetworkProtocol_NETWORK_PROTOCOL_SCTP:
		return corev1.ProtocolSCTP, nil
	case pbk8s.NetworkProtocol_NETWORK_PROTOCOL_UNKNOWN:
		return "", eris.Errorf("invalid protocol %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func FromNetworkPolicyTypeProto(p pbk8s.NetworkPolicyType) (networkingv1.PolicyType, error) {
	switch p {
	case pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS:
		return networkingv1.PolicyTypeIngress, nil
	case pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS:
		return networkingv1.PolicyTypeEgress, nil
	case pbk8s.NetworkPolicyType_NETWORK_POLICY_UNKNOWN:
		return "", eris.Errorf("invalid policy type %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}
