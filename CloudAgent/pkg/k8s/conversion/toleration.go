package conversion

import (
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
)

func FromTolerationProto(p *pbk8s.Toleration) (corev1.Toleration, error) {
	taintEffect, err := FromTaintEffectProto(p.GetEffect())
	if err != nil {
		return corev1.Toleration{}, err
	}
	tolerationOperator, err := FromTolerationOperatorProto(p.GetOperator())
	if err != nil {
		return corev1.Toleration{}, err
	}
	var tolerationSeconds *int64
	if p != nil && p.TolerationSecs != nil {
		tolerationSeconds = p.TolerationSecs
	}
	return corev1.Toleration{
		Key:               p.Get<PERSON>ey(),
		Operator:          tolerationOperator,
		Value:             p.GetValue(),
		Effect:            taintEffect,
		TolerationSeconds: tolerationSeconds,
	}, nil
}

func FromTaintEffectProto(p pbk8s.TaintEffect) (corev1.TaintEffect, error) {
	switch p {
	case pbk8s.TaintEffect_TAINT_EFFECT_NO_SCHEDULE:
		return corev1.TaintEffectNoSchedule, nil
	case pbk8s.TaintEffect_TAINT_EFFECT_PREFER_NO_SCHEDULE:
		return corev1.TaintEffectPreferNoSchedule, nil
	case pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE:
		return corev1.TaintEffectNoExecute, nil
	case pbk8s.TaintEffect_TAINT_EFFECT_UNKNOWN: // An empty effect matches all effects with key.
		return "", nil
	}
	return "", eris.Errorf("invalid taint effect %v", p).WithCode(eris.CodeInvalidArgument)
}

func FromTolerationOperatorProto(p pbk8s.TolerationOperator) (corev1.TolerationOperator, error) {
	switch p {
	case pbk8s.TolerationOperator_TOLERATION_OPERATOR_EXISTS:
		return corev1.TolerationOpExists, nil
	case pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL:
		return corev1.TolerationOpEqual, nil
	case pbk8s.TolerationOperator_TOLERATION_OPERATOR_UNKNOWN:
		return "", eris.Errorf("invalid toleration operator %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func ToTolerationProto(t corev1.Toleration) (*pbk8s.Toleration, error) {
	taintEffect, err := ToTaintEffectProto(t.Effect)
	if err != nil {
		return nil, err
	}
	tolerationOperator, err := ToTolerationOperatorProto(t.Operator)
	if err != nil {
		return nil, err
	}
	return &pbk8s.Toleration{
		Key:            t.Key,
		Operator:       tolerationOperator,
		Value:          t.Value,
		Effect:         taintEffect,
		TolerationSecs: t.TolerationSeconds,
	}, nil
}

func ToTaintEffectProto(e corev1.TaintEffect) (pbk8s.TaintEffect, error) {
	switch e {
	case corev1.TaintEffectNoSchedule:
		return pbk8s.TaintEffect_TAINT_EFFECT_NO_SCHEDULE, nil
	case corev1.TaintEffectPreferNoSchedule:
		return pbk8s.TaintEffect_TAINT_EFFECT_PREFER_NO_SCHEDULE, nil
	case corev1.TaintEffectNoExecute:
		return pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE, nil
	}
	return pbk8s.TaintEffect_TAINT_EFFECT_UNKNOWN, eris.Errorf("invalid taint effect %v", e).WithCode(eris.CodeInvalidArgument)
}

func ToTolerationOperatorProto(o corev1.TolerationOperator) (pbk8s.TolerationOperator, error) {
	switch o {
	case corev1.TolerationOpExists:
		return pbk8s.TolerationOperator_TOLERATION_OPERATOR_EXISTS, nil
	case corev1.TolerationOpEqual:
		return pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL, nil
	}
	return pbk8s.TolerationOperator_TOLERATION_OPERATOR_UNKNOWN, eris.Errorf("invalid toleration operator %v", o).WithCode(eris.CodeInvalidArgument)
}
