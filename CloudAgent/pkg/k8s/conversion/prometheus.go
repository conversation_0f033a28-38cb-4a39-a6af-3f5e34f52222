package conversion

import (
	prometheusv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"

	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
)

func FromServiceMonitorSpecProto(p *pbprometheus.ServiceMonitorSpec) (prometheusv1.ServiceMonitorSpec, error) {
	selector, err := FromLabelSelectorProto(p.GetSelector())
	if err != nil {
		return prometheusv1.ServiceMonitorSpec{}, err
	}
	var endpoints []prometheusv1.Endpoint
	for _, ep := range p.GetEndpoints() {
		endpoints = append(endpoints, FromPrometheusEndpointProto(ep))
	}
	return prometheusv1.ServiceMonitorSpec{
		JobLabel:     p.GetJobLabel(),
		TargetLabels: p.GetTargetLabels(),
		Endpoints:    endpoints,
		Selector:     *selector,
	}, nil
}

func FromPrometheusEndpointProto(p *pbprometheus.Endpoint) prometheusv1.Endpoint {
	var metricRelabelConfigs []prometheusv1.RelabelConfig
	for _, c := range p.GetMetricRelabelings() {
		metricRelabelConfigs = append(metricRelabelConfigs, FromPrometheusRelabelConfigProto(c))
	}
	return prometheusv1.Endpoint{
		Port:                 p.GetPort(),
		Interval:             prometheusv1.Duration(p.GetInterval()),
		ScrapeTimeout:        prometheusv1.Duration(p.GetScrapeTimeout()),
		MetricRelabelConfigs: metricRelabelConfigs,
	}
}

func FromPrometheusRelabelConfigProto(p *pbprometheus.RelabelConfig) prometheusv1.RelabelConfig {
	var sourceLabels []prometheusv1.LabelName
	for _, l := range p.GetSourceLabels() {
		sourceLabels = append(sourceLabels, prometheusv1.LabelName(l))
	}
	return prometheusv1.RelabelConfig{
		SourceLabels: sourceLabels,
		Action:       p.GetAction(),
		Regex:        p.GetRegex(),
	}
}
