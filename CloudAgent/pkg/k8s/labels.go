package k8s

const (
	// Kubernetes object annotation key.
	LabelNameKey      = "resources.risingwave.cloud/resource-id"
	LabelNamespaceKey = "resources.risingwave.cloud/resource-namespace"
	LabelProject      = "resources.risingwave.cloud/project"
)

type Labels map[string]string

// MergeWithDefaults merges the default labels to the resource specific labels.
// If the default labels contain a key that already exists in input labels, default
// value will override the input one.
func (l Labels) WithDefaults(resourceID, namespace string) Labels {
	for k, v := range DefaultTags(resourceID, namespace) {
		l[k] = v
	}
	return l
}

func DefaultTags(resourceID, namespace string) Labels {
	return Labels{
		LabelProject:      "risingwave-cloud",
		LabelNameKey:      resourceID,
		LabelNamespaceKey: namespace,
	}
}
