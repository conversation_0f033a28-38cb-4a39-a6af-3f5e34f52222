package k8s

import (
	"context"

	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/informers"
	coreinformers "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
	corelisters "k8s.io/client-go/listers/core/v1"
	"k8s.io/client-go/tools/cache"

	"github.com/risingwavelabs/cloudagent/pkg/logger"
)

type PodLister struct {
	kubeClient kubernetes.Interface

	sharedInformers informers.SharedInformerFactory
	podLister       corelisters.PodLister
	podInformer     coreinformers.PodInformer

	readyCh chan struct{}
}

func NewPodLister(
	kubeClient kubernetes.Interface,
) (*PodLister, error) {
	w := &PodLister{
		kubeClient: kubeClient,
	}

	// create the informers only in user namespace.
	w.sharedInformers = informers.NewSharedInformerFactoryWithOptions(w.kubeClient, 0)
	podInformer := w.sharedInformers.Core().V1().Pods()

	w.podInformer = podInformer
	w.podLister = podInformer.Lister()

	w.readyCh = make(chan struct{}, 1)
	return w, nil
}

func (w *PodLister) List(namespace string) ([]*corev1.Pod, error) {
	podList, err := w.podLister.Pods(namespace).List(labels.Everything())
	if err != nil {
		return []*corev1.Pod{}, nil
	}
	return podList, nil
}

// Run starts an asynchronous loop that monitors the status of cluster pods.
func (w *PodLister) run(ctx context.Context) {
	w.sharedInformers.Start(ctx.Done())

	logger.FromCtx(ctx).Info("Starting pod list-watch")
	defer logger.FromCtx(ctx).Info("Shutting pod list-watch")

	if !cache.WaitForNamedCacheSync("list-watcher", ctx.Done(), w.podInformer.Informer().HasSynced) {
		return
	}

	// send the ready channel
	logger.FromCtx(ctx).Info("Has synced the cache!")
	w.readyCh <- struct{}{}

	// wait the done signal
	<-ctx.Done()
}

func (w *PodLister) WaitReady(ctx context.Context) {
	go w.run(ctx)
	<-w.readyCh
	logger.FromCtx(ctx).Info("The lister is ready")
}
