package k8s

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"github.com/risingwavelabs/eris"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/cloudagent/pkg/utils"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	utilsproto "github.com/risingwavelabs/cloudagent/pkg/utils/proto"
)

const (
	TaskEphemeralVolumeName            = "ephemeral-volume"
	TaskEphemeralVolumePath            = "/ephemeral"
	TaskTmpVolumeName                  = "tmp-volume"
	TaskTmpVolumePath                  = "/tmp"
	TerminationGracefulShutdownDefault = int64(30)

	AnnotationKeySafeToEvictLocalVolumes = "cluster-autoscaler.kubernetes.io/safe-to-evict-local-volumes"
)

type RunTaskOption struct {
	Name               string
	Namespace          string
	Image              string
	Command            []string
	ImagePullPolicy    corev1.PullPolicy
	Resources          *corev1.ResourceRequirements
	ServiceAccountName string
	// default to TaskConfig.Tolerations by design, if there is no special requirement, we use the default value from TaskConfig.
	Tolerations []corev1.Toleration
	// default to TaskConfig.Affinity by design, if there is no special requirement, we use the default value from TaskConfig.
	Affinity     *corev1.Affinity
	BackOffLimit *int32
	Envs         map[string]string
	// default to TaskConfig.Labels by design, if there is no special requirement, we use the default value from TaskConfig.
	Labels map[string]string
	// if there is no special requirement, use the default kubernetes value of 30s.
	TerminationGracePeriodSecs *int64
}

func (o *RunTaskOption) Validate() error {
	if len(o.Name) == 0 {
		return eris.New("name cannot be empty")
	}
	if len(o.Namespace) == 0 {
		return eris.New("namespace cannot be empty")
	}
	if len(o.Image) == 0 {
		return eris.New("image cannot be empty")
	}
	if len(o.ImagePullPolicy) == 0 {
		return eris.New("image pull policy cannot be empty")
	}
	return nil
}

func (kc *KubernetesClient) RunTask(ctx context.Context, option RunTaskOption) error {
	if len(option.Namespace) == 0 {
		option.Namespace = kc.TaskConfig.Namespace
	}
	if option.Affinity == nil {
		option.Affinity = kc.TaskConfig.Affinity
	}
	if option.Tolerations == nil {
		option.Tolerations = kc.TaskConfig.Tolerations
	}
	if option.Labels == nil {
		option.Labels = kc.TaskConfig.Labels
	}
	if option.TerminationGracePeriodSecs == nil {
		option.TerminationGracePeriodSecs = utils.Ptr(TerminationGracefulShutdownDefault)
	}
	if option.Resources == nil {
		option.Resources = &corev1.ResourceRequirements{}
	}
	if err := option.Validate(); err != nil {
		return eris.WithCode(err, eris.CodeInvalidArgument)
	}

	job := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.Name,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				"cluster-autoscaler.kubernetes.io/safe-to-evict": "false",
			},
		},
		Spec: batchv1.JobSpec{
			Template: corev1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Labels: option.Labels,
					Annotations: map[string]string{
						AnnotationKeySafeToEvictLocalVolumes: strings.Join([]string{TaskEphemeralVolumeName, TaskTmpVolumeName}, ","),
					},
				},
				Spec: corev1.PodSpec{
					Volumes: []corev1.Volume{
						{
							Name: TaskEphemeralVolumeName,
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
						{
							Name: TaskTmpVolumeName,
							VolumeSource: corev1.VolumeSource{
								EmptyDir: &corev1.EmptyDirVolumeSource{},
							},
						},
					},
					Containers: []corev1.Container{
						{
							Name:            option.Name,
							Image:           option.Image,
							Command:         option.Command,
							ImagePullPolicy: option.ImagePullPolicy,
							VolumeMounts: []corev1.VolumeMount{
								{
									Name:      TaskEphemeralVolumeName,
									MountPath: TaskEphemeralVolumePath,
								},
								{
									Name:      TaskTmpVolumeName,
									MountPath: TaskTmpVolumePath,
								},
							},
							Resources: *option.Resources,
						},
					},
					RestartPolicy:                 corev1.RestartPolicyNever,
					ServiceAccountName:            option.ServiceAccountName,
					Tolerations:                   option.Tolerations,
					Affinity:                      option.Affinity,
					TerminationGracePeriodSeconds: option.TerminationGracePeriodSecs,
				},
			},
			BackoffLimit: option.BackOffLimit,
		},
	}

	for k, v := range option.Envs {
		job.Spec.Template.Spec.Containers[0].Env = append(
			job.Spec.Template.Spec.Containers[0].Env,
			corev1.EnvVar{Name: k, Value: v},
		)
	}

	if err := kc.Create(ctx, job); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.WithCode(err, eris.CodeAlreadyExists)
		}
		return eris.WithCode(err, eris.CodeInternal)
	}
	return nil
}

type TaskStatus struct {
	Code    TaskStatusCode
	Message string
}

type TaskStatusCode int

const (
	TaskStatusSuccess TaskStatusCode = iota + 1
	TaskStatusFailed
	TaskStatusRunning
)

func (kc *KubernetesClient) getPodLog(ctx context.Context, pod *corev1.Pod) (string, error) {
	req := kc.CoreV1().Pods(pod.Namespace).GetLogs(pod.Name, &corev1.PodLogOptions{})
	log, err := req.Stream(ctx)
	if err != nil {
		return "", eris.Wrapf(err, "failed to get pod log for pod: %s in namespace %s", pod.Name, pod.Namespace)
	}
	defer func() { _ = log.Close() }()

	buf := &bytes.Buffer{}
	if _, err := io.Copy(buf, log); err != nil {
		return "", eris.Wrapf(err, "failed to read log from stream for pod: %s in namespace %s", pod.Name, pod.Namespace)
	}

	return buf.String(), nil
}

// The job name label should have prefix. But in some Kubernetes version, the prefix is missing.
const legacyJobNameLabel = "job-name"

func (kc *KubernetesClient) getPodsByJobName(ctx context.Context, jobName, namespace string) ([]corev1.Pod, error) {
	podLabels := []client.MatchingLabels{
		{batchv1.JobNameLabel: jobName},
		{legacyJobNameLabel: jobName},
	}
	podList := &corev1.PodList{}
	for _, mLabel := range podLabels {
		tmpList := &corev1.PodList{}
		if err := kc.List(ctx, tmpList, mLabel, &client.ListOptions{
			Namespace: namespace,
		}); err != nil {
			return nil, eris.Wrapf(err, "failed to list job pod with label %v", mLabel)
		}
		podList.Items = append(podList.Items, tmpList.Items...)
	}
	if len(podList.Items) == 0 {
		return nil, eris.Errorf("no pod found for job: %s", jobName)
	}
	return podList.Items, nil
}

// getJobPodInfo returns the detailed status and log of the job pod.
func (kc *KubernetesClient) getJobPodInfo(ctx context.Context, jobName, namespace string) (string, error) {
	jobPods, err := kc.getPodsByJobName(ctx, jobName, namespace)
	if err != nil {
		return "", eris.Wrapf(err, "failed to get pod for job: %s/%s", namespace, jobName)
	}
	if len(jobPods) == 0 {
		return "", eris.Errorf("no pod found for job: %s", jobName)
	}
	jobPod := jobPods[0]

	// get condition statuses
	condBytes, err := json.Marshal(jobPod.Status.ContainerStatuses)
	condInfo := ""
	if err != nil {
		condInfo = fmt.Sprintf("failed to marshal conditions: %s", err.Error())
	} else {
		condInfo = string(condBytes)
	}

	for _, status := range jobPod.Status.ContainerStatuses {
		if status.State.Terminated != nil {
			log, err := kc.getPodLog(ctx, &jobPod)
			if err != nil {
				log = fmt.Sprintf("failed to get pod log: %s", err.Error())
			}
			return fmt.Sprintf("terminated, conditon statuses: %s, log: %s", condInfo, log), nil
		}
		if status.State.Running != nil {
			return "running", nil
		}
		if status.State.Waiting != nil {
			return fmt.Sprintf("waiting, conditon statuses: %s", condInfo), nil
		}
	}

	return "", eris.Errorf("unknown pod status, conditon statuses: %s", condInfo)
}

// GetTask gets the status of a running task.
func (kc *KubernetesClient) GetTaskStatus(ctx context.Context, name, namespace string) (*TaskStatus, error) {
	if len(namespace) == 0 {
		namespace = kc.TaskConfig.Namespace
	}
	job, err := GetResource[batchv1.Job](ctx, kc, name, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return nil, eris.WithCode(eris.Wrapf(err, "failed to get task: %s/%s", namespace, name), eris.CodeNotFound)
		}
		return nil, eris.WithCode(err, eris.CodeInternal)
	}

	podMessage, err := kc.getJobPodInfo(ctx, job.Name, namespace)
	if err != nil {
		podMessage = fmt.Sprintf("failed to get pod message: %s", err.Error())
	}
	for _, cond := range job.Status.Conditions {
		if cond.Type == batchv1.JobFailed && cond.Status == corev1.ConditionTrue {
			return &TaskStatus{Code: TaskStatusFailed, Message: cond.Message + " pod state:" + podMessage}, nil
		}
		if cond.Type == batchv1.JobComplete && cond.Status == corev1.ConditionTrue {
			return &TaskStatus{Code: TaskStatusSuccess, Message: podMessage}, nil
		}
	}

	return &TaskStatus{Code: TaskStatusRunning}, nil
}

// CleanupTask deletes the job object of a task. Note that the service account object is preserved
// for the next time to use.
// return NotFound error code if the task is not found.
func (kc *KubernetesClient) CleanupTask(ctx context.Context, name, namespace string) error {
	if len(namespace) == 0 {
		namespace = kc.TaskConfig.Namespace
	}
	// Keep job until both itself and its pods are deleted
	err := DeleteResource[batchv1.Job](ctx, kc, name, namespace, client.PropagationPolicy(metav1.DeletePropagationForeground))
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.WithCode(err, eris.CodeNotFound)
		}
		return eris.WithCode(err, eris.CodeInternal)
	}
	return nil
}

// StartTaskRunner starts CloudAgent task runner binary in the default task namespace.
func (kc *KubernetesClient) StartTaskRunner(ctx context.Context, taskName string, taskNamespace string, taskSpec *pbtask.Task) error {
	return kc.StartTaskRunnerWithOverrides(ctx, taskName, taskNamespace, taskSpec, TaskRunnerOverrides{})
}

type TaskRunnerOverrides struct {
	Affinity    **corev1.Affinity
	Tolerations *[]corev1.Toleration
	Envs        *map[string]string
}

func (kc *KubernetesClient) StartTaskRunnerWithOverrides(ctx context.Context, taskName string, taskNamespace string, taskSpec *pbtask.Task, overrides TaskRunnerOverrides) error {
	encodedTaskSpec, err := utilsproto.ToBase64(taskSpec)
	if err != nil {
		return eris.Wrap(err, "failed to encode task spec")
	}

	terminationGracePeriod := TerminationGracefulShutdownDefault
	if _, isByoc := taskSpec.GetTask().(*pbtask.Task_ApplyByocModuleTask); isByoc {
		terminationGracePeriod = taskSpec.GetApplyByocModuleTask().GetApplyOptions().GetGracefulShutdownPeriod().GetSeconds()
	}

	var envs map[string]string
	if overrides.Envs != nil {
		envs = *overrides.Envs
	}
	affinity := kc.TaskConfig.Affinity
	if overrides.Affinity != nil {
		affinity = *overrides.Affinity
	}
	tolerations := kc.TaskConfig.Tolerations
	if overrides.Tolerations != nil {
		tolerations = *overrides.Tolerations
	}

	return kc.RunTask(ctx, RunTaskOption{
		Name:                       taskName,
		Namespace:                  taskNamespace,
		Image:                      kc.TaskConfig.Image,
		Command:                    []string{"/app/taskrunner", "--task_spec", encodedTaskSpec, "--runner_config", kc.TaskConfig.EncodedRunnerConfig},
		ImagePullPolicy:            kc.TaskConfig.PullPolicy,
		ServiceAccountName:         kc.TaskConfig.ServiceAccount,
		Tolerations:                tolerations,
		Affinity:                   affinity,
		BackOffLimit:               utils.Ptr[int32](0),
		Envs:                       envs,
		TerminationGracePeriodSecs: utils.Ptr(terminationGracePeriod),
	})
}

func (kc *KubernetesClient) CancelTask(ctx context.Context, taskName, taskNamespace string) error {
	pods, err := kc.getPodsByJobName(ctx, taskName, taskNamespace)
	if err != nil {
		return eris.Wrapf(err, "failed to get pods by job: %s/%s", taskNamespace, taskName)
	}
	for _, pod := range pods {
		fmt.Println(pod)
	}
	return kc.CleanupTask(ctx, taskName, taskNamespace)
}
