package fake

import (
	"bytes"
	"context"
	"io"
	"net/url"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	dynamicfake "k8s.io/client-go/dynamic/fake"
	"k8s.io/client-go/kubernetes"
	kfake "k8s.io/client-go/kubernetes/fake"
	"k8s.io/client-go/rest"
	restfake "k8s.io/client-go/rest/fake"
	"k8s.io/client-go/tools/remotecommand"
	clientfake "sigs.k8s.io/controller-runtime/pkg/client/fake"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/scheme"

	typedcorev1 "k8s.io/client-go/kubernetes/typed/core/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"
)

type ExtendedCoreV1 struct {
	typedcorev1.CoreV1Interface
	restClient rest.Interface
}

// this workaround is needed since the original RESTClient method return a nil pointer directly.
// https://github.com/kubernetes/client-go/blob/089d04441d989ab5cd9538e69c0e0c4e33700173/kubernetes/typed/core/v1/fake/fake_core_client.go#L95-L100
func (c *ExtendedCoreV1) RESTClient() rest.Interface {
	return c.restClient
}

type ExtendedClientset struct {
	*kfake.Clientset
	typedcorev1.CoreV1Interface
}

func (f *ExtendedClientset) CoreV1() typedcorev1.CoreV1Interface {
	return f.CoreV1Interface
}

func NewClientset(objs ...runtime.Object) kubernetes.Interface {
	clientset := kfake.NewSimpleClientset(objs...)
	return &ExtendedClientset{
		Clientset: clientset,
		CoreV1Interface: &ExtendedCoreV1{
			CoreV1Interface: clientset.CoreV1(),
			restClient: &restfake.RESTClient{
				// fix restclient.Request.VersionedParams uses r.(*Request).c.(*RESTClient).content.(ClientContentConfig).GroupVersion
				// constructs empty params and return `no kind "PodExecOptions" is registered for version "" in scheme "pkg/runtime/scheme.go:100"`
				// in request.Error().
				GroupVersion: schema.GroupVersion{Group: "", Version: "v1"},
			},
		},
	}
}

func NewDynamiceInterface(objs ...runtime.Object) dynamic.Interface {
	return dynamicfake.NewSimpleDynamicClient(scheme.Scheme(), objs...)
}

func NewClient(initObjs ...k8sclient.Object) k8sclient.Client {
	return clientfake.NewClientBuilder().WithScheme(scheme.Scheme()).WithObjects(initObjs...).Build()
}

type SPDYExecutor struct {
	OnStdinRead func([]byte)
	ToStdout    []byte
	ToStderr    []byte
	config      *rest.Config
	method      string
	url         *url.URL
}

// fake constructor, the initialize parameter will be stored.
func (f *SPDYExecutor) New(config *rest.Config, method string, url *url.URL) (remotecommand.Executor, error) {
	f.config = config
	f.method = method
	f.url = url
	return f, nil
}

func (f *SPDYExecutor) Stream(options remotecommand.StreamOptions) error {
	return f.StreamWithContext(context.Background(), options)
}

func (f *SPDYExecutor) StreamWithContext(_ context.Context, options remotecommand.StreamOptions) error {
	if options.Stdin != nil {
		result, err := io.ReadAll(options.Stdin)
		if err != nil {
			return eris.Wrap(err, "failed to read from stdin")
		}
		if f.OnStdinRead != nil {
			f.OnStdinRead(result)
		}
	}
	if options.Stderr != nil {
		_, err := io.Copy(options.Stderr, bytes.NewReader(f.ToStderr))
		if err != nil {
			return eris.Wrap(err, "failed to write to stderr")
		}
	}
	if options.Stdout != nil {
		_, err := io.Copy(options.Stdout, bytes.NewReader(f.ToStdout))
		if err != nil {
			return eris.Wrap(err, "failed to write to stdout")
		}
	}
	return nil
}

func (f *SPDYExecutor) URL() *url.URL {
	return f.url
}

func (f *SPDYExecutor) Method() string {
	return f.method
}
