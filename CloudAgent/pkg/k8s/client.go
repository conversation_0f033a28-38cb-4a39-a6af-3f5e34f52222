package k8s

import (
	"context"
	"net/url"
	"strings"

	"github.com/pkg/errors"

	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/remotecommand"
	"sigs.k8s.io/controller-runtime/pkg/client"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbcfgk8s "github.com/risingwavelabs/cloudagent/pbgen/config/k8s"
	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/scheme"
	taskconfig "github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
)

type KubernetesClientInterface interface {
	client.Client
	kubernetes.Interface
	GetRestCfg() *rest.Config
	RunTask(ctx context.Context, option RunTaskOption) error
	GetTaskStatus(ctx context.Context, name, namespace string) (*TaskStatus, error)
	StartTaskRunner(ctx context.Context, taskName string, taskNamespace string, taskSpec *pbtask.Task) error
	ExecPod(ctx context.Context, name, namespace string, cmd []string) (stdoutStr string, stderrStr string, err error)
}

type KubernetesClient struct {
	client.Client
	kubernetes.Interface
	DynamicInterface dynamic.Interface
	restCfg          *rest.Config

	TaskConfig      taskconfig.Config
	NewSPDYExecutor func(config *rest.Config, method string, url *url.URL) (remotecommand.Executor, error)
}

func (kc *KubernetesClient) GetRestCfg() *rest.Config {
	return kc.restCfg
}

func InitDefaultClient(cfg *pbcfg.Config) (*KubernetesClient, error) {
	c, err := NewClient(cfg)
	if err != nil {
		return nil, err
	}
	return c, nil
}

func NewClient(cfg *pbcfg.Config) (*KubernetesClient, error) {
	taskConfig, err := taskconfig.FromConfigProto(cfg)
	if err != nil {
		return nil, err
	}
	k8sCfg := cfg.GetK8SConfig()
	switch authType := k8sCfg.GetAuth().(type) {
	case *pbcfgk8s.Config_StaticTokenAuth:
		return newClientWithStaticToken(k8sCfg.GetStaticTokenAuth().GetMasterUrl(), k8sCfg.GetStaticTokenAuth().GetToken(), taskConfig)
	case *pbcfgk8s.Config_InClusterAuth:
		return newInClusterClient(taskConfig)
	default:
		return nil, errors.Errorf("invaid auth type %v", authType)
	}
}

func newClientWithStaticToken(masterURL, token string, taskConfig taskconfig.Config) (*KubernetesClient, error) {
	restCfg, err := clientcmd.BuildConfigFromFlags(masterURL, "")
	if err != nil {
		return nil, errors.Wrap(err, "failed to build rest config from token")
	}
	restCfg.BearerToken = strings.TrimSuffix(token, "\n")
	kc, err := initClients(restCfg, taskConfig)
	if err != nil {
		return nil, errors.Wrap(err, "failed to build client from token")
	}
	return kc, nil
}

func newInClusterClient(taskConfig taskconfig.Config) (*KubernetesClient, error) {
	restCfg, err := rest.InClusterConfig()
	if err != nil {
		return nil, errors.Wrap(err, "failed to build in-cluster rest config")
	}
	kc, err := initClients(restCfg, taskConfig)
	if err != nil {
		return nil, errors.Wrap(err, "failed to build client from in cluster config")
	}
	return kc, nil
}

func initClients(restCfg *rest.Config, taskConfig taskconfig.Config) (*KubernetesClient, error) {
	crdClient, err := client.New(restCfg, client.Options{Scheme: scheme.Scheme()})
	if err != nil {
		return nil, errors.Wrap(err, "failed to create crc client")
	}
	clientset, err := kubernetes.NewForConfig(restCfg)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create clientset")
	}
	dynamicInterface, err := dynamic.NewForConfig(restCfg)
	if err != nil {
		return nil, errors.Wrap(err, "failed to create dynamicInterface")
	}
	return &KubernetesClient{
		crdClient, clientset, dynamicInterface, restCfg, taskConfig, remotecommand.NewSPDYExecutor,
	}, nil
}
