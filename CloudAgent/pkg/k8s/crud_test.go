package k8s

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"golang.org/x/net/context"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
)

func TestGetResource(t *testing.T) {
	expectedCM := &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Data: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	}
	c := fake.NewClient(expectedCM)
	ctx := context.Background()
	cm, err := GetResource[corev1.ConfigMap](ctx, c, "name", "ns")
	require.NoError(t, err)
	// The returned value will fill in TypeMeta and verison, so we cannot directly
	// compare the two object.
	assert.Equal(t, expectedCM.Name, cm.Name)
	assert.Equal(t, expectedCM.Namespace, cm.Namespace)
	assert.Equal(t, expectedCM.Data, cm.Data)

	_, err = GetResource[corev1.ConfigMap](ctx, c, "name", "not-exist")
	assert.True(t, k8sErrors.IsNotFound(err))
}

func TestDeleteResource(t *testing.T) {
	c := fake.NewClient(&corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Data: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	})
	ctx := context.Background()
	err := DeleteResource[corev1.ConfigMap](ctx, c, "name", "ns")
	require.NoError(t, err)
	err = DeleteResource[corev1.ConfigMap](ctx, c, "name", "ns")
	assert.True(t, k8sErrors.IsNotFound(err))
}
