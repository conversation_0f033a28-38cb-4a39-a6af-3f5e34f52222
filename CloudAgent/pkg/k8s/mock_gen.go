// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/k8s (interfaces: KubernetesClientInterface)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/k8s -package=k8s -destination=pkg/k8s/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/k8s KubernetesClientInterface
//

// Package k8s is a generated GoMock package.
package k8s

import (
	context "context"
	reflect "reflect"

	task "github.com/risingwavelabs/cloudagent/pbgen/task"
	gomock "go.uber.org/mock/gomock"
	meta "k8s.io/apimachinery/pkg/api/meta"
	runtime "k8s.io/apimachinery/pkg/runtime"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	discovery "k8s.io/client-go/discovery"
	v1 "k8s.io/client-go/kubernetes/typed/admissionregistration/v1"
	v1alpha1 "k8s.io/client-go/kubernetes/typed/admissionregistration/v1alpha1"
	v1beta1 "k8s.io/client-go/kubernetes/typed/admissionregistration/v1beta1"
	v1alpha10 "k8s.io/client-go/kubernetes/typed/apiserverinternal/v1alpha1"
	v10 "k8s.io/client-go/kubernetes/typed/apps/v1"
	v1beta10 "k8s.io/client-go/kubernetes/typed/apps/v1beta1"
	v1beta2 "k8s.io/client-go/kubernetes/typed/apps/v1beta2"
	v11 "k8s.io/client-go/kubernetes/typed/authentication/v1"
	v1alpha11 "k8s.io/client-go/kubernetes/typed/authentication/v1alpha1"
	v1beta11 "k8s.io/client-go/kubernetes/typed/authentication/v1beta1"
	v12 "k8s.io/client-go/kubernetes/typed/authorization/v1"
	v1beta12 "k8s.io/client-go/kubernetes/typed/authorization/v1beta1"
	v13 "k8s.io/client-go/kubernetes/typed/autoscaling/v1"
	v2 "k8s.io/client-go/kubernetes/typed/autoscaling/v2"
	v2beta1 "k8s.io/client-go/kubernetes/typed/autoscaling/v2beta1"
	v2beta2 "k8s.io/client-go/kubernetes/typed/autoscaling/v2beta2"
	v14 "k8s.io/client-go/kubernetes/typed/batch/v1"
	v1beta13 "k8s.io/client-go/kubernetes/typed/batch/v1beta1"
	v15 "k8s.io/client-go/kubernetes/typed/certificates/v1"
	v1alpha12 "k8s.io/client-go/kubernetes/typed/certificates/v1alpha1"
	v1beta14 "k8s.io/client-go/kubernetes/typed/certificates/v1beta1"
	v16 "k8s.io/client-go/kubernetes/typed/coordination/v1"
	v1alpha2 "k8s.io/client-go/kubernetes/typed/coordination/v1alpha2"
	v1beta15 "k8s.io/client-go/kubernetes/typed/coordination/v1beta1"
	v17 "k8s.io/client-go/kubernetes/typed/core/v1"
	v18 "k8s.io/client-go/kubernetes/typed/discovery/v1"
	v1beta16 "k8s.io/client-go/kubernetes/typed/discovery/v1beta1"
	v19 "k8s.io/client-go/kubernetes/typed/events/v1"
	v1beta17 "k8s.io/client-go/kubernetes/typed/events/v1beta1"
	v1beta18 "k8s.io/client-go/kubernetes/typed/extensions/v1beta1"
	v110 "k8s.io/client-go/kubernetes/typed/flowcontrol/v1"
	v1beta19 "k8s.io/client-go/kubernetes/typed/flowcontrol/v1beta1"
	v1beta20 "k8s.io/client-go/kubernetes/typed/flowcontrol/v1beta2"
	v1beta3 "k8s.io/client-go/kubernetes/typed/flowcontrol/v1beta3"
	v111 "k8s.io/client-go/kubernetes/typed/networking/v1"
	v1alpha13 "k8s.io/client-go/kubernetes/typed/networking/v1alpha1"
	v1beta110 "k8s.io/client-go/kubernetes/typed/networking/v1beta1"
	v112 "k8s.io/client-go/kubernetes/typed/node/v1"
	v1alpha14 "k8s.io/client-go/kubernetes/typed/node/v1alpha1"
	v1beta111 "k8s.io/client-go/kubernetes/typed/node/v1beta1"
	v113 "k8s.io/client-go/kubernetes/typed/policy/v1"
	v1beta112 "k8s.io/client-go/kubernetes/typed/policy/v1beta1"
	v114 "k8s.io/client-go/kubernetes/typed/rbac/v1"
	v1alpha15 "k8s.io/client-go/kubernetes/typed/rbac/v1alpha1"
	v1beta113 "k8s.io/client-go/kubernetes/typed/rbac/v1beta1"
	v1alpha3 "k8s.io/client-go/kubernetes/typed/resource/v1alpha3"
	v1beta114 "k8s.io/client-go/kubernetes/typed/resource/v1beta1"
	v1beta21 "k8s.io/client-go/kubernetes/typed/resource/v1beta2"
	v115 "k8s.io/client-go/kubernetes/typed/scheduling/v1"
	v1alpha16 "k8s.io/client-go/kubernetes/typed/scheduling/v1alpha1"
	v1beta115 "k8s.io/client-go/kubernetes/typed/scheduling/v1beta1"
	v116 "k8s.io/client-go/kubernetes/typed/storage/v1"
	v1alpha17 "k8s.io/client-go/kubernetes/typed/storage/v1alpha1"
	v1beta116 "k8s.io/client-go/kubernetes/typed/storage/v1beta1"
	v1alpha18 "k8s.io/client-go/kubernetes/typed/storagemigration/v1alpha1"
	rest "k8s.io/client-go/rest"
	client "sigs.k8s.io/controller-runtime/pkg/client"
)

// MockKubernetesClientInterface is a mock of KubernetesClientInterface interface.
type MockKubernetesClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockKubernetesClientInterfaceMockRecorder
	isgomock struct{}
}

// MockKubernetesClientInterfaceMockRecorder is the mock recorder for MockKubernetesClientInterface.
type MockKubernetesClientInterfaceMockRecorder struct {
	mock *MockKubernetesClientInterface
}

// NewMockKubernetesClientInterface creates a new mock instance.
func NewMockKubernetesClientInterface(ctrl *gomock.Controller) *MockKubernetesClientInterface {
	mock := &MockKubernetesClientInterface{ctrl: ctrl}
	mock.recorder = &MockKubernetesClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockKubernetesClientInterface) EXPECT() *MockKubernetesClientInterfaceMockRecorder {
	return m.recorder
}

// AdmissionregistrationV1 mocks base method.
func (m *MockKubernetesClientInterface) AdmissionregistrationV1() v1.AdmissionregistrationV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdmissionregistrationV1")
	ret0, _ := ret[0].(v1.AdmissionregistrationV1Interface)
	return ret0
}

// AdmissionregistrationV1 indicates an expected call of AdmissionregistrationV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AdmissionregistrationV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdmissionregistrationV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AdmissionregistrationV1))
}

// AdmissionregistrationV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) AdmissionregistrationV1alpha1() v1alpha1.AdmissionregistrationV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdmissionregistrationV1alpha1")
	ret0, _ := ret[0].(v1alpha1.AdmissionregistrationV1alpha1Interface)
	return ret0
}

// AdmissionregistrationV1alpha1 indicates an expected call of AdmissionregistrationV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AdmissionregistrationV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdmissionregistrationV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AdmissionregistrationV1alpha1))
}

// AdmissionregistrationV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) AdmissionregistrationV1beta1() v1beta1.AdmissionregistrationV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AdmissionregistrationV1beta1")
	ret0, _ := ret[0].(v1beta1.AdmissionregistrationV1beta1Interface)
	return ret0
}

// AdmissionregistrationV1beta1 indicates an expected call of AdmissionregistrationV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AdmissionregistrationV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AdmissionregistrationV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AdmissionregistrationV1beta1))
}

// AppsV1 mocks base method.
func (m *MockKubernetesClientInterface) AppsV1() v10.AppsV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppsV1")
	ret0, _ := ret[0].(v10.AppsV1Interface)
	return ret0
}

// AppsV1 indicates an expected call of AppsV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AppsV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppsV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AppsV1))
}

// AppsV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) AppsV1beta1() v1beta10.AppsV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppsV1beta1")
	ret0, _ := ret[0].(v1beta10.AppsV1beta1Interface)
	return ret0
}

// AppsV1beta1 indicates an expected call of AppsV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AppsV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppsV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AppsV1beta1))
}

// AppsV1beta2 mocks base method.
func (m *MockKubernetesClientInterface) AppsV1beta2() v1beta2.AppsV1beta2Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AppsV1beta2")
	ret0, _ := ret[0].(v1beta2.AppsV1beta2Interface)
	return ret0
}

// AppsV1beta2 indicates an expected call of AppsV1beta2.
func (mr *MockKubernetesClientInterfaceMockRecorder) AppsV1beta2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AppsV1beta2", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AppsV1beta2))
}

// AuthenticationV1 mocks base method.
func (m *MockKubernetesClientInterface) AuthenticationV1() v11.AuthenticationV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthenticationV1")
	ret0, _ := ret[0].(v11.AuthenticationV1Interface)
	return ret0
}

// AuthenticationV1 indicates an expected call of AuthenticationV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AuthenticationV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthenticationV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AuthenticationV1))
}

// AuthenticationV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) AuthenticationV1alpha1() v1alpha11.AuthenticationV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthenticationV1alpha1")
	ret0, _ := ret[0].(v1alpha11.AuthenticationV1alpha1Interface)
	return ret0
}

// AuthenticationV1alpha1 indicates an expected call of AuthenticationV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AuthenticationV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthenticationV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AuthenticationV1alpha1))
}

// AuthenticationV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) AuthenticationV1beta1() v1beta11.AuthenticationV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthenticationV1beta1")
	ret0, _ := ret[0].(v1beta11.AuthenticationV1beta1Interface)
	return ret0
}

// AuthenticationV1beta1 indicates an expected call of AuthenticationV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AuthenticationV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthenticationV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AuthenticationV1beta1))
}

// AuthorizationV1 mocks base method.
func (m *MockKubernetesClientInterface) AuthorizationV1() v12.AuthorizationV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthorizationV1")
	ret0, _ := ret[0].(v12.AuthorizationV1Interface)
	return ret0
}

// AuthorizationV1 indicates an expected call of AuthorizationV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AuthorizationV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthorizationV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AuthorizationV1))
}

// AuthorizationV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) AuthorizationV1beta1() v1beta12.AuthorizationV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AuthorizationV1beta1")
	ret0, _ := ret[0].(v1beta12.AuthorizationV1beta1Interface)
	return ret0
}

// AuthorizationV1beta1 indicates an expected call of AuthorizationV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AuthorizationV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AuthorizationV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AuthorizationV1beta1))
}

// AutoscalingV1 mocks base method.
func (m *MockKubernetesClientInterface) AutoscalingV1() v13.AutoscalingV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoscalingV1")
	ret0, _ := ret[0].(v13.AutoscalingV1Interface)
	return ret0
}

// AutoscalingV1 indicates an expected call of AutoscalingV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AutoscalingV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoscalingV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AutoscalingV1))
}

// AutoscalingV2 mocks base method.
func (m *MockKubernetesClientInterface) AutoscalingV2() v2.AutoscalingV2Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoscalingV2")
	ret0, _ := ret[0].(v2.AutoscalingV2Interface)
	return ret0
}

// AutoscalingV2 indicates an expected call of AutoscalingV2.
func (mr *MockKubernetesClientInterfaceMockRecorder) AutoscalingV2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoscalingV2", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AutoscalingV2))
}

// AutoscalingV2beta1 mocks base method.
func (m *MockKubernetesClientInterface) AutoscalingV2beta1() v2beta1.AutoscalingV2beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoscalingV2beta1")
	ret0, _ := ret[0].(v2beta1.AutoscalingV2beta1Interface)
	return ret0
}

// AutoscalingV2beta1 indicates an expected call of AutoscalingV2beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) AutoscalingV2beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoscalingV2beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AutoscalingV2beta1))
}

// AutoscalingV2beta2 mocks base method.
func (m *MockKubernetesClientInterface) AutoscalingV2beta2() v2beta2.AutoscalingV2beta2Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AutoscalingV2beta2")
	ret0, _ := ret[0].(v2beta2.AutoscalingV2beta2Interface)
	return ret0
}

// AutoscalingV2beta2 indicates an expected call of AutoscalingV2beta2.
func (mr *MockKubernetesClientInterfaceMockRecorder) AutoscalingV2beta2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AutoscalingV2beta2", reflect.TypeOf((*MockKubernetesClientInterface)(nil).AutoscalingV2beta2))
}

// BatchV1 mocks base method.
func (m *MockKubernetesClientInterface) BatchV1() v14.BatchV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchV1")
	ret0, _ := ret[0].(v14.BatchV1Interface)
	return ret0
}

// BatchV1 indicates an expected call of BatchV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) BatchV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).BatchV1))
}

// BatchV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) BatchV1beta1() v1beta13.BatchV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "BatchV1beta1")
	ret0, _ := ret[0].(v1beta13.BatchV1beta1Interface)
	return ret0
}

// BatchV1beta1 indicates an expected call of BatchV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) BatchV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "BatchV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).BatchV1beta1))
}

// CertificatesV1 mocks base method.
func (m *MockKubernetesClientInterface) CertificatesV1() v15.CertificatesV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CertificatesV1")
	ret0, _ := ret[0].(v15.CertificatesV1Interface)
	return ret0
}

// CertificatesV1 indicates an expected call of CertificatesV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) CertificatesV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CertificatesV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CertificatesV1))
}

// CertificatesV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) CertificatesV1alpha1() v1alpha12.CertificatesV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CertificatesV1alpha1")
	ret0, _ := ret[0].(v1alpha12.CertificatesV1alpha1Interface)
	return ret0
}

// CertificatesV1alpha1 indicates an expected call of CertificatesV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) CertificatesV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CertificatesV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CertificatesV1alpha1))
}

// CertificatesV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) CertificatesV1beta1() v1beta14.CertificatesV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CertificatesV1beta1")
	ret0, _ := ret[0].(v1beta14.CertificatesV1beta1Interface)
	return ret0
}

// CertificatesV1beta1 indicates an expected call of CertificatesV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) CertificatesV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CertificatesV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CertificatesV1beta1))
}

// CoordinationV1 mocks base method.
func (m *MockKubernetesClientInterface) CoordinationV1() v16.CoordinationV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoordinationV1")
	ret0, _ := ret[0].(v16.CoordinationV1Interface)
	return ret0
}

// CoordinationV1 indicates an expected call of CoordinationV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) CoordinationV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoordinationV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CoordinationV1))
}

// CoordinationV1alpha2 mocks base method.
func (m *MockKubernetesClientInterface) CoordinationV1alpha2() v1alpha2.CoordinationV1alpha2Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoordinationV1alpha2")
	ret0, _ := ret[0].(v1alpha2.CoordinationV1alpha2Interface)
	return ret0
}

// CoordinationV1alpha2 indicates an expected call of CoordinationV1alpha2.
func (mr *MockKubernetesClientInterfaceMockRecorder) CoordinationV1alpha2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoordinationV1alpha2", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CoordinationV1alpha2))
}

// CoordinationV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) CoordinationV1beta1() v1beta15.CoordinationV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoordinationV1beta1")
	ret0, _ := ret[0].(v1beta15.CoordinationV1beta1Interface)
	return ret0
}

// CoordinationV1beta1 indicates an expected call of CoordinationV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) CoordinationV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoordinationV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CoordinationV1beta1))
}

// CoreV1 mocks base method.
func (m *MockKubernetesClientInterface) CoreV1() v17.CoreV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CoreV1")
	ret0, _ := ret[0].(v17.CoreV1Interface)
	return ret0
}

// CoreV1 indicates an expected call of CoreV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) CoreV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CoreV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).CoreV1))
}

// Create mocks base method.
func (m *MockKubernetesClientInterface) Create(ctx context.Context, obj client.Object, opts ...client.CreateOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, obj}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Create", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockKubernetesClientInterfaceMockRecorder) Create(ctx, obj any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, obj}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Create), varargs...)
}

// Delete mocks base method.
func (m *MockKubernetesClientInterface) Delete(ctx context.Context, obj client.Object, opts ...client.DeleteOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, obj}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Delete", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Delete indicates an expected call of Delete.
func (mr *MockKubernetesClientInterfaceMockRecorder) Delete(ctx, obj any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, obj}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Delete", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Delete), varargs...)
}

// DeleteAllOf mocks base method.
func (m *MockKubernetesClientInterface) DeleteAllOf(ctx context.Context, obj client.Object, opts ...client.DeleteAllOfOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, obj}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "DeleteAllOf", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAllOf indicates an expected call of DeleteAllOf.
func (mr *MockKubernetesClientInterfaceMockRecorder) DeleteAllOf(ctx, obj any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, obj}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAllOf", reflect.TypeOf((*MockKubernetesClientInterface)(nil).DeleteAllOf), varargs...)
}

// Discovery mocks base method.
func (m *MockKubernetesClientInterface) Discovery() discovery.DiscoveryInterface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Discovery")
	ret0, _ := ret[0].(discovery.DiscoveryInterface)
	return ret0
}

// Discovery indicates an expected call of Discovery.
func (mr *MockKubernetesClientInterfaceMockRecorder) Discovery() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Discovery", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Discovery))
}

// DiscoveryV1 mocks base method.
func (m *MockKubernetesClientInterface) DiscoveryV1() v18.DiscoveryV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiscoveryV1")
	ret0, _ := ret[0].(v18.DiscoveryV1Interface)
	return ret0
}

// DiscoveryV1 indicates an expected call of DiscoveryV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) DiscoveryV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiscoveryV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).DiscoveryV1))
}

// DiscoveryV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) DiscoveryV1beta1() v1beta16.DiscoveryV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DiscoveryV1beta1")
	ret0, _ := ret[0].(v1beta16.DiscoveryV1beta1Interface)
	return ret0
}

// DiscoveryV1beta1 indicates an expected call of DiscoveryV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) DiscoveryV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DiscoveryV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).DiscoveryV1beta1))
}

// EventsV1 mocks base method.
func (m *MockKubernetesClientInterface) EventsV1() v19.EventsV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EventsV1")
	ret0, _ := ret[0].(v19.EventsV1Interface)
	return ret0
}

// EventsV1 indicates an expected call of EventsV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) EventsV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EventsV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).EventsV1))
}

// EventsV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) EventsV1beta1() v1beta17.EventsV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EventsV1beta1")
	ret0, _ := ret[0].(v1beta17.EventsV1beta1Interface)
	return ret0
}

// EventsV1beta1 indicates an expected call of EventsV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) EventsV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EventsV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).EventsV1beta1))
}

// ExecPod mocks base method.
func (m *MockKubernetesClientInterface) ExecPod(ctx context.Context, name, namespace string, cmd []string) (string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExecPod", ctx, name, namespace, cmd)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ExecPod indicates an expected call of ExecPod.
func (mr *MockKubernetesClientInterfaceMockRecorder) ExecPod(ctx, name, namespace, cmd any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExecPod", reflect.TypeOf((*MockKubernetesClientInterface)(nil).ExecPod), ctx, name, namespace, cmd)
}

// ExtensionsV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) ExtensionsV1beta1() v1beta18.ExtensionsV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ExtensionsV1beta1")
	ret0, _ := ret[0].(v1beta18.ExtensionsV1beta1Interface)
	return ret0
}

// ExtensionsV1beta1 indicates an expected call of ExtensionsV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) ExtensionsV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ExtensionsV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).ExtensionsV1beta1))
}

// FlowcontrolV1 mocks base method.
func (m *MockKubernetesClientInterface) FlowcontrolV1() v110.FlowcontrolV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlowcontrolV1")
	ret0, _ := ret[0].(v110.FlowcontrolV1Interface)
	return ret0
}

// FlowcontrolV1 indicates an expected call of FlowcontrolV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) FlowcontrolV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlowcontrolV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).FlowcontrolV1))
}

// FlowcontrolV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) FlowcontrolV1beta1() v1beta19.FlowcontrolV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlowcontrolV1beta1")
	ret0, _ := ret[0].(v1beta19.FlowcontrolV1beta1Interface)
	return ret0
}

// FlowcontrolV1beta1 indicates an expected call of FlowcontrolV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) FlowcontrolV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlowcontrolV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).FlowcontrolV1beta1))
}

// FlowcontrolV1beta2 mocks base method.
func (m *MockKubernetesClientInterface) FlowcontrolV1beta2() v1beta20.FlowcontrolV1beta2Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlowcontrolV1beta2")
	ret0, _ := ret[0].(v1beta20.FlowcontrolV1beta2Interface)
	return ret0
}

// FlowcontrolV1beta2 indicates an expected call of FlowcontrolV1beta2.
func (mr *MockKubernetesClientInterfaceMockRecorder) FlowcontrolV1beta2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlowcontrolV1beta2", reflect.TypeOf((*MockKubernetesClientInterface)(nil).FlowcontrolV1beta2))
}

// FlowcontrolV1beta3 mocks base method.
func (m *MockKubernetesClientInterface) FlowcontrolV1beta3() v1beta3.FlowcontrolV1beta3Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FlowcontrolV1beta3")
	ret0, _ := ret[0].(v1beta3.FlowcontrolV1beta3Interface)
	return ret0
}

// FlowcontrolV1beta3 indicates an expected call of FlowcontrolV1beta3.
func (mr *MockKubernetesClientInterfaceMockRecorder) FlowcontrolV1beta3() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FlowcontrolV1beta3", reflect.TypeOf((*MockKubernetesClientInterface)(nil).FlowcontrolV1beta3))
}

// Get mocks base method.
func (m *MockKubernetesClientInterface) Get(ctx context.Context, key types.NamespacedName, obj client.Object, opts ...client.GetOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, key, obj}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Get", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Get indicates an expected call of Get.
func (mr *MockKubernetesClientInterfaceMockRecorder) Get(ctx, key, obj any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, key, obj}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Get), varargs...)
}

// GetRestCfg mocks base method.
func (m *MockKubernetesClientInterface) GetRestCfg() *rest.Config {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRestCfg")
	ret0, _ := ret[0].(*rest.Config)
	return ret0
}

// GetRestCfg indicates an expected call of GetRestCfg.
func (mr *MockKubernetesClientInterfaceMockRecorder) GetRestCfg() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRestCfg", reflect.TypeOf((*MockKubernetesClientInterface)(nil).GetRestCfg))
}

// GetTaskStatus mocks base method.
func (m *MockKubernetesClientInterface) GetTaskStatus(ctx context.Context, name, namespace string) (*TaskStatus, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetTaskStatus", ctx, name, namespace)
	ret0, _ := ret[0].(*TaskStatus)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetTaskStatus indicates an expected call of GetTaskStatus.
func (mr *MockKubernetesClientInterfaceMockRecorder) GetTaskStatus(ctx, name, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetTaskStatus", reflect.TypeOf((*MockKubernetesClientInterface)(nil).GetTaskStatus), ctx, name, namespace)
}

// GroupVersionKindFor mocks base method.
func (m *MockKubernetesClientInterface) GroupVersionKindFor(obj runtime.Object) (schema.GroupVersionKind, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GroupVersionKindFor", obj)
	ret0, _ := ret[0].(schema.GroupVersionKind)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GroupVersionKindFor indicates an expected call of GroupVersionKindFor.
func (mr *MockKubernetesClientInterfaceMockRecorder) GroupVersionKindFor(obj any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GroupVersionKindFor", reflect.TypeOf((*MockKubernetesClientInterface)(nil).GroupVersionKindFor), obj)
}

// InternalV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) InternalV1alpha1() v1alpha10.InternalV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InternalV1alpha1")
	ret0, _ := ret[0].(v1alpha10.InternalV1alpha1Interface)
	return ret0
}

// InternalV1alpha1 indicates an expected call of InternalV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) InternalV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InternalV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).InternalV1alpha1))
}

// IsObjectNamespaced mocks base method.
func (m *MockKubernetesClientInterface) IsObjectNamespaced(obj runtime.Object) (bool, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "IsObjectNamespaced", obj)
	ret0, _ := ret[0].(bool)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// IsObjectNamespaced indicates an expected call of IsObjectNamespaced.
func (mr *MockKubernetesClientInterfaceMockRecorder) IsObjectNamespaced(obj any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "IsObjectNamespaced", reflect.TypeOf((*MockKubernetesClientInterface)(nil).IsObjectNamespaced), obj)
}

// List mocks base method.
func (m *MockKubernetesClientInterface) List(ctx context.Context, list client.ObjectList, opts ...client.ListOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, list}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "List", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// List indicates an expected call of List.
func (mr *MockKubernetesClientInterfaceMockRecorder) List(ctx, list any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, list}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "List", reflect.TypeOf((*MockKubernetesClientInterface)(nil).List), varargs...)
}

// NetworkingV1 mocks base method.
func (m *MockKubernetesClientInterface) NetworkingV1() v111.NetworkingV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkingV1")
	ret0, _ := ret[0].(v111.NetworkingV1Interface)
	return ret0
}

// NetworkingV1 indicates an expected call of NetworkingV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) NetworkingV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkingV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).NetworkingV1))
}

// NetworkingV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) NetworkingV1alpha1() v1alpha13.NetworkingV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkingV1alpha1")
	ret0, _ := ret[0].(v1alpha13.NetworkingV1alpha1Interface)
	return ret0
}

// NetworkingV1alpha1 indicates an expected call of NetworkingV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) NetworkingV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkingV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).NetworkingV1alpha1))
}

// NetworkingV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) NetworkingV1beta1() v1beta110.NetworkingV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NetworkingV1beta1")
	ret0, _ := ret[0].(v1beta110.NetworkingV1beta1Interface)
	return ret0
}

// NetworkingV1beta1 indicates an expected call of NetworkingV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) NetworkingV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NetworkingV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).NetworkingV1beta1))
}

// NodeV1 mocks base method.
func (m *MockKubernetesClientInterface) NodeV1() v112.NodeV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeV1")
	ret0, _ := ret[0].(v112.NodeV1Interface)
	return ret0
}

// NodeV1 indicates an expected call of NodeV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) NodeV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).NodeV1))
}

// NodeV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) NodeV1alpha1() v1alpha14.NodeV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeV1alpha1")
	ret0, _ := ret[0].(v1alpha14.NodeV1alpha1Interface)
	return ret0
}

// NodeV1alpha1 indicates an expected call of NodeV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) NodeV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).NodeV1alpha1))
}

// NodeV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) NodeV1beta1() v1beta111.NodeV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NodeV1beta1")
	ret0, _ := ret[0].(v1beta111.NodeV1beta1Interface)
	return ret0
}

// NodeV1beta1 indicates an expected call of NodeV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) NodeV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NodeV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).NodeV1beta1))
}

// Patch mocks base method.
func (m *MockKubernetesClientInterface) Patch(ctx context.Context, obj client.Object, patch client.Patch, opts ...client.PatchOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, obj, patch}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Patch", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Patch indicates an expected call of Patch.
func (mr *MockKubernetesClientInterfaceMockRecorder) Patch(ctx, obj, patch any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, obj, patch}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Patch", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Patch), varargs...)
}

// PolicyV1 mocks base method.
func (m *MockKubernetesClientInterface) PolicyV1() v113.PolicyV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PolicyV1")
	ret0, _ := ret[0].(v113.PolicyV1Interface)
	return ret0
}

// PolicyV1 indicates an expected call of PolicyV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) PolicyV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PolicyV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).PolicyV1))
}

// PolicyV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) PolicyV1beta1() v1beta112.PolicyV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PolicyV1beta1")
	ret0, _ := ret[0].(v1beta112.PolicyV1beta1Interface)
	return ret0
}

// PolicyV1beta1 indicates an expected call of PolicyV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) PolicyV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PolicyV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).PolicyV1beta1))
}

// RESTMapper mocks base method.
func (m *MockKubernetesClientInterface) RESTMapper() meta.RESTMapper {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RESTMapper")
	ret0, _ := ret[0].(meta.RESTMapper)
	return ret0
}

// RESTMapper indicates an expected call of RESTMapper.
func (mr *MockKubernetesClientInterfaceMockRecorder) RESTMapper() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RESTMapper", reflect.TypeOf((*MockKubernetesClientInterface)(nil).RESTMapper))
}

// RbacV1 mocks base method.
func (m *MockKubernetesClientInterface) RbacV1() v114.RbacV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RbacV1")
	ret0, _ := ret[0].(v114.RbacV1Interface)
	return ret0
}

// RbacV1 indicates an expected call of RbacV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) RbacV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RbacV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).RbacV1))
}

// RbacV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) RbacV1alpha1() v1alpha15.RbacV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RbacV1alpha1")
	ret0, _ := ret[0].(v1alpha15.RbacV1alpha1Interface)
	return ret0
}

// RbacV1alpha1 indicates an expected call of RbacV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) RbacV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RbacV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).RbacV1alpha1))
}

// RbacV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) RbacV1beta1() v1beta113.RbacV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RbacV1beta1")
	ret0, _ := ret[0].(v1beta113.RbacV1beta1Interface)
	return ret0
}

// RbacV1beta1 indicates an expected call of RbacV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) RbacV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RbacV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).RbacV1beta1))
}

// ResourceV1alpha3 mocks base method.
func (m *MockKubernetesClientInterface) ResourceV1alpha3() v1alpha3.ResourceV1alpha3Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceV1alpha3")
	ret0, _ := ret[0].(v1alpha3.ResourceV1alpha3Interface)
	return ret0
}

// ResourceV1alpha3 indicates an expected call of ResourceV1alpha3.
func (mr *MockKubernetesClientInterfaceMockRecorder) ResourceV1alpha3() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceV1alpha3", reflect.TypeOf((*MockKubernetesClientInterface)(nil).ResourceV1alpha3))
}

// ResourceV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) ResourceV1beta1() v1beta114.ResourceV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceV1beta1")
	ret0, _ := ret[0].(v1beta114.ResourceV1beta1Interface)
	return ret0
}

// ResourceV1beta1 indicates an expected call of ResourceV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) ResourceV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).ResourceV1beta1))
}

// ResourceV1beta2 mocks base method.
func (m *MockKubernetesClientInterface) ResourceV1beta2() v1beta21.ResourceV1beta2Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResourceV1beta2")
	ret0, _ := ret[0].(v1beta21.ResourceV1beta2Interface)
	return ret0
}

// ResourceV1beta2 indicates an expected call of ResourceV1beta2.
func (mr *MockKubernetesClientInterfaceMockRecorder) ResourceV1beta2() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResourceV1beta2", reflect.TypeOf((*MockKubernetesClientInterface)(nil).ResourceV1beta2))
}

// RunTask mocks base method.
func (m *MockKubernetesClientInterface) RunTask(ctx context.Context, option RunTaskOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunTask", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// RunTask indicates an expected call of RunTask.
func (mr *MockKubernetesClientInterfaceMockRecorder) RunTask(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunTask", reflect.TypeOf((*MockKubernetesClientInterface)(nil).RunTask), ctx, option)
}

// SchedulingV1 mocks base method.
func (m *MockKubernetesClientInterface) SchedulingV1() v115.SchedulingV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SchedulingV1")
	ret0, _ := ret[0].(v115.SchedulingV1Interface)
	return ret0
}

// SchedulingV1 indicates an expected call of SchedulingV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) SchedulingV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SchedulingV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).SchedulingV1))
}

// SchedulingV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) SchedulingV1alpha1() v1alpha16.SchedulingV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SchedulingV1alpha1")
	ret0, _ := ret[0].(v1alpha16.SchedulingV1alpha1Interface)
	return ret0
}

// SchedulingV1alpha1 indicates an expected call of SchedulingV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) SchedulingV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SchedulingV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).SchedulingV1alpha1))
}

// SchedulingV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) SchedulingV1beta1() v1beta115.SchedulingV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SchedulingV1beta1")
	ret0, _ := ret[0].(v1beta115.SchedulingV1beta1Interface)
	return ret0
}

// SchedulingV1beta1 indicates an expected call of SchedulingV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) SchedulingV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SchedulingV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).SchedulingV1beta1))
}

// Scheme mocks base method.
func (m *MockKubernetesClientInterface) Scheme() *runtime.Scheme {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scheme")
	ret0, _ := ret[0].(*runtime.Scheme)
	return ret0
}

// Scheme indicates an expected call of Scheme.
func (mr *MockKubernetesClientInterfaceMockRecorder) Scheme() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scheme", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Scheme))
}

// StartTaskRunner mocks base method.
func (m *MockKubernetesClientInterface) StartTaskRunner(ctx context.Context, taskName, taskNamespace string, taskSpec *task.Task) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartTaskRunner", ctx, taskName, taskNamespace, taskSpec)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartTaskRunner indicates an expected call of StartTaskRunner.
func (mr *MockKubernetesClientInterfaceMockRecorder) StartTaskRunner(ctx, taskName, taskNamespace, taskSpec any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartTaskRunner", reflect.TypeOf((*MockKubernetesClientInterface)(nil).StartTaskRunner), ctx, taskName, taskNamespace, taskSpec)
}

// Status mocks base method.
func (m *MockKubernetesClientInterface) Status() client.SubResourceWriter {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Status")
	ret0, _ := ret[0].(client.SubResourceWriter)
	return ret0
}

// Status indicates an expected call of Status.
func (mr *MockKubernetesClientInterfaceMockRecorder) Status() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Status", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Status))
}

// StorageV1 mocks base method.
func (m *MockKubernetesClientInterface) StorageV1() v116.StorageV1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StorageV1")
	ret0, _ := ret[0].(v116.StorageV1Interface)
	return ret0
}

// StorageV1 indicates an expected call of StorageV1.
func (mr *MockKubernetesClientInterfaceMockRecorder) StorageV1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StorageV1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).StorageV1))
}

// StorageV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) StorageV1alpha1() v1alpha17.StorageV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StorageV1alpha1")
	ret0, _ := ret[0].(v1alpha17.StorageV1alpha1Interface)
	return ret0
}

// StorageV1alpha1 indicates an expected call of StorageV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) StorageV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StorageV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).StorageV1alpha1))
}

// StorageV1beta1 mocks base method.
func (m *MockKubernetesClientInterface) StorageV1beta1() v1beta116.StorageV1beta1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StorageV1beta1")
	ret0, _ := ret[0].(v1beta116.StorageV1beta1Interface)
	return ret0
}

// StorageV1beta1 indicates an expected call of StorageV1beta1.
func (mr *MockKubernetesClientInterfaceMockRecorder) StorageV1beta1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StorageV1beta1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).StorageV1beta1))
}

// StoragemigrationV1alpha1 mocks base method.
func (m *MockKubernetesClientInterface) StoragemigrationV1alpha1() v1alpha18.StoragemigrationV1alpha1Interface {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StoragemigrationV1alpha1")
	ret0, _ := ret[0].(v1alpha18.StoragemigrationV1alpha1Interface)
	return ret0
}

// StoragemigrationV1alpha1 indicates an expected call of StoragemigrationV1alpha1.
func (mr *MockKubernetesClientInterfaceMockRecorder) StoragemigrationV1alpha1() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StoragemigrationV1alpha1", reflect.TypeOf((*MockKubernetesClientInterface)(nil).StoragemigrationV1alpha1))
}

// SubResource mocks base method.
func (m *MockKubernetesClientInterface) SubResource(subResource string) client.SubResourceClient {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "SubResource", subResource)
	ret0, _ := ret[0].(client.SubResourceClient)
	return ret0
}

// SubResource indicates an expected call of SubResource.
func (mr *MockKubernetesClientInterfaceMockRecorder) SubResource(subResource any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "SubResource", reflect.TypeOf((*MockKubernetesClientInterface)(nil).SubResource), subResource)
}

// Update mocks base method.
func (m *MockKubernetesClientInterface) Update(ctx context.Context, obj client.Object, opts ...client.UpdateOption) error {
	m.ctrl.T.Helper()
	varargs := []any{ctx, obj}
	for _, a := range opts {
		varargs = append(varargs, a)
	}
	ret := m.ctrl.Call(m, "Update", varargs...)
	ret0, _ := ret[0].(error)
	return ret0
}

// Update indicates an expected call of Update.
func (mr *MockKubernetesClientInterfaceMockRecorder) Update(ctx, obj any, opts ...any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	varargs := append([]any{ctx, obj}, opts...)
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Update", reflect.TypeOf((*MockKubernetesClientInterface)(nil).Update), varargs...)
}
