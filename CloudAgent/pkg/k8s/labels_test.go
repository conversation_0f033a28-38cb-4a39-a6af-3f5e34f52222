package k8s

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestWithDefaultLabels(t *testing.T) {
	labels := Labels{
		"resources.risingwave.cloud/project": "test",
		"foo":                                "bar",
	}.WithDefaults("id", "ns")

	assert.Equal(t, labels["resources.risingwave.cloud/project"], "risingwave-cloud")
	assert.Equal(t, labels["foo"], "bar")
	assert.Equal(t, labels["resources.risingwave.cloud/resource-id"], "id")
	assert.Equal(t, labels["resources.risingwave.cloud/resource-namespace"], "ns")
}
