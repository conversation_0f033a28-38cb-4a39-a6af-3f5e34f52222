package k8s

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
)

func TestExecPod(t *testing.T) {
	var (
		podName   = "pod-test"
		namespace = "ns-test"
		command   = []string{"uname", "-a"}
		cmdOutput = "output"
		cmdErr    = "err"
	)
	fakeSPDYExecutor := &fake.SPDYExecutor{
		ToStdout: []byte(cmdOutput),
		ToStderr: []byte(cmdErr),
	}

	kc := &KubernetesClient{
		Interface:       fake.NewClientset(),
		NewSPDYExecutor: fakeSPDYExecutor.New,
	}

	outStr, errStr, err := kc.ExecPod(context.Background(), podName, namespace, command)

	require.NoError(t, err)
	assert.Equal(t, cmdOutput, outStr)
	assert.Equal(t, cmdErr, errStr)
	assert.Equal(t,
		fmt.Sprintf("/namespaces/%s/pods/%s/exec", namespace, podName),
		fakeSPDYExecutor.URL().Path,
	)
	assert.Equal(t, fakeSPDYExecutor.URL().Query()["command"], command)
}
