package k8s

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
)

func TestListPods(t *testing.T) {
	testNamespace := "test-ns"
	fakeClientSet := fake.NewClientset()
	ctx := context.Background()
	var podStartTime metav1.Time
	for i := 0; i < 10; i++ {
		podName := fmt.Sprintf("test_pod_%d", i)
		var pod = v1.Pod{
			Status: v1.PodStatus{
				Phase:     v1.PodRunning,
				StartTime: &podStartTime,
				Conditions: []v1.PodCondition{
					{
						Type:   v1.PodReady,
						Status: v1.ConditionTrue,
					},
				},
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      podName,
				Namespace: testNamespace,
				Labels: map[string]string{
					"name": podName,
				},
			},
			Spec: v1.PodSpec{
				Containers: []v1.Container{{Name: "container1"}, {Name: "container2"}},
			},
		}
		_, err := fakeClientSet.CoreV1().Pods(testNamespace).Create(ctx, &pod, metav1.CreateOptions{})
		assert.NoError(t, err)
	}

	podLister, err := NewPodLister(fakeClientSet)
	assert.NoError(t, err)

	podLister.WaitReady(ctx)

	pods, err := podLister.List(testNamespace)
	assert.NoError(t, err)
	assert.Equal(t, 10, len(pods))

	pods, err = podLister.List("no-pods")
	assert.NoError(t, err)
	assert.Equal(t, len(pods), 0)
}
