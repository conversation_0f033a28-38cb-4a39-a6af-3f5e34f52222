package k8s

import (
	"context"
	"fmt"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	taskconfig "github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestStartNewMetaBackupTask(t *testing.T) {
	var (
		taskNamespace = "ns"
		taskName      = "task"
	)

	kc := &KubernetesClient{
		Client:    fake.NewClient(),
		Interface: fake.NewClientset(),
	}

	err := kc.RunTask(context.Background(), RunTaskOption{
		Name:               taskName,
		Namespace:          taskNamespace,
		Image:              "nginx:latest",
		ServiceAccountName: "sa",
		ImagePullPolicy:    corev1.PullIfNotPresent,
		Envs: map[string]string{
			"testk": "testv",
		},
	})
	require.NoError(t, err)

	job, err := GetResource[batchv1.Job](context.Background(), kc, taskName, taskNamespace)
	require.NoError(t, err)
	assert.Equal(t, taskName, job.Name)
	assert.Equal(t, job.Spec.Template.Spec.Containers[0].Env, []corev1.EnvVar{
		{
			Name:  "testk",
			Value: "testv",
		},
	})
}

func TestStartNewMetaBackupTask_exist(t *testing.T) {
	var (
		taskNamespace = "ns"
		taskName      = "task"
	)
	kc := &KubernetesClient{
		Client: fake.NewClient(&batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: taskNamespace,
				Name:      taskName,
			},
		}),
		Interface: fake.NewClientset(),
	}

	err := kc.RunTask(context.Background(), RunTaskOption{
		Name:               taskName,
		Namespace:          taskNamespace,
		Image:              "nginx:latest",
		Command:            nil,
		ServiceAccountName: "sa",
		ImagePullPolicy:    corev1.PullIfNotPresent,
	})
	require.True(t, utils.IsErrAlreadyExists(err))
}

func TestCleanupTask(t *testing.T) {
	var (
		taskNamespace = "ns"
		taskName      = "task"
	)

	kc := &KubernetesClient{
		Client: fake.NewClient(&batchv1.Job{
			ObjectMeta: metav1.ObjectMeta{
				Namespace: taskNamespace,
				Name:      taskName,
			},
		}),
		Interface: fake.NewClientset(),
	}

	err := kc.CleanupTask(context.Background(), taskName, taskNamespace)
	assert.NoError(t, err)
}

func TestCleanupTask_notFound(t *testing.T) {
	var (
		taskNamespace = "ns"
		taskName      = "task"
	)
	kc := &KubernetesClient{
		Client:    fake.NewClient(),
		Interface: fake.NewClientset(),
	}
	err := kc.CleanupTask(context.Background(), taskName, taskNamespace)
	assert.True(t, utils.IsErrNotFound(err))
}

type _testGetTaskStatusItem struct {
	job       *batchv1.Job
	jobPod    *corev1.Pod
	name      string
	namespace string
}

type _testGetTaskStatusExpected struct {
	errCode        eris.Code
	taskStatusCode *TaskStatus
}

var getTaskStatusTestData = []struct {
	item     _testGetTaskStatusItem
	expected _testGetTaskStatusExpected
}{
	// legacy job-name label key
	{
		item: _testGetTaskStatusItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "legacy-case-name",
					Namespace: "legacy-case-ns",
				},
			},
			jobPod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "legacy-case-ns",
					Name:      "legacy-case-name",
					Labels: map[string]string{
						legacyJobNameLabel: "running-name",
					},
				},
				Status: corev1.PodStatus{
					ContainerStatuses: []corev1.ContainerStatus{
						{
							State: corev1.ContainerState{
								Running: &corev1.ContainerStateRunning{},
							},
						},
					},
				},
			},
			name:      "legacy-case-name",
			namespace: "legacy-case-ns",
		},
		expected: _testGetTaskStatusExpected{
			errCode: 0,
			taskStatusCode: &TaskStatus{
				Code: TaskStatusRunning,
			},
		},
	},
	// not found
	{
		item: _testGetTaskStatusItem{
			name:      "not-found-name",
			namespace: "running-ns",
		},
		expected: _testGetTaskStatusExpected{
			errCode:        eris.CodeNotFound,
			taskStatusCode: nil,
		},
	},
	// found running task
	{
		item: _testGetTaskStatusItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Name:      "running-name",
					Namespace: "running-ns",
				},
			},
			jobPod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "running-ns",
					Name:      "running-name",
					Labels: map[string]string{
						batchv1.JobNameLabel: "running-name",
					},
				},
				Status: corev1.PodStatus{
					ContainerStatuses: []corev1.ContainerStatus{
						{
							State: corev1.ContainerState{
								Running: &corev1.ContainerStateRunning{},
							},
						},
					},
				},
			},
			name:      "running-name",
			namespace: "running-ns",
		},
		expected: _testGetTaskStatusExpected{
			errCode: 0,
			taskStatusCode: &TaskStatus{
				Code: TaskStatusRunning,
			},
		},
	},
	// found failed task
	{
		item: _testGetTaskStatusItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "failed-ns",
					Name:      "failed-name",
				},
				Status: batchv1.JobStatus{
					Conditions: []batchv1.JobCondition{
						{
							Status: corev1.ConditionTrue,
							Type:   batchv1.JobFailed,
						},
					},
				},
			},
			jobPod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "failed-ns",
					Name:      "failed-name",
					Labels: map[string]string{
						batchv1.JobNameLabel: "failed-name",
					},
				},
				Status: corev1.PodStatus{
					ContainerStatuses: []corev1.ContainerStatus{
						{
							State: corev1.ContainerState{
								Terminated: &corev1.ContainerStateTerminated{},
							},
						},
					},
				},
			},
			name:      "failed-name",
			namespace: "failed-ns",
		},
		expected: _testGetTaskStatusExpected{
			errCode: 0,
			taskStatusCode: &TaskStatus{
				Code: TaskStatusFailed,
			},
		},
	},
	// found success task
	{
		item: _testGetTaskStatusItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "success-ns",
					Name:      "success-name",
				},
				Status: batchv1.JobStatus{
					Conditions: []batchv1.JobCondition{
						{
							Status: corev1.ConditionTrue,
							Type:   batchv1.JobComplete,
						},
					},
				},
			},
			name:      "success-name",
			namespace: "success-ns",
		},
		expected: _testGetTaskStatusExpected{
			errCode: 0,
			taskStatusCode: &TaskStatus{
				Code: TaskStatusSuccess,
			},
		},
	},
}

func TestGetTaskStatus(t *testing.T) {
	for idx, data := range getTaskStatusTestData {
		t.Logf("running test %d", idx)
		fakeClient := fake.NewClient()
		fakeClienset := fake.NewClientset()
		if data.item.job != nil {
			fakeClient = fake.NewClient(data.item.job)
		}
		if data.item.jobPod != nil {
			fakeClienset = fake.NewClientset(data.item.jobPod)
		}
		kc := &KubernetesClient{
			Client:    fakeClient,
			Interface: fakeClienset,
		}

		status, err := kc.GetTaskStatus(context.Background(), data.item.name, data.item.namespace)
		if data.expected.errCode != 0 {
			assert.Equal(t, data.expected.errCode, eris.GetCode(err))
		} else {
			require.NoError(t, err)
			assert.Equal(t, data.expected.taskStatusCode.Code, status.Code)
		}
	}
}

func TestGetPodByJobName(t *testing.T) {
	jobName := "test"
	jobNamespace := "test-ns"
	pods := []client.Object{
		&corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pa",
				Namespace: jobNamespace,
				Labels: map[string]string{
					legacyJobNameLabel: jobName,
				},
			},
		},
		&corev1.Pod{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pb",
				Namespace: jobNamespace,
				Labels: map[string]string{
					batchv1.JobNameLabel: jobName,
				},
			},
		},
	}
	kc := &KubernetesClient{
		Client: fake.NewClient(pods...),
	}
	res, err := kc.getPodsByJobName(context.Background(), jobName, jobNamespace)
	require.NoError(t, err)
	require.Equal(t, 2, len(res))
	fmt.Println(res[0].Name, res[0].Labels, res[1].Name, res[1].Labels)
	assert.True(t,
		(res[0].Name == "pa" && res[0].Labels[legacyJobNameLabel] == jobName &&
			res[1].Name == "pb" && res[1].Labels[batchv1.JobNameLabel] == jobName) ||
			// OR
			(res[0].Name == "pb" && res[0].Labels[batchv1.JobNameLabel] == jobName &&
				res[1].Name == "pa" && res[1].Labels[legacyJobNameLabel] == jobName),
	)
}

func TestRunTaskDefaultAffinityAndToleration(t *testing.T) {
	var (
		taskNamespace = "ns"
		taskName      = "task"
		affinity      = &corev1.Affinity{
			NodeAffinity: &corev1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
					NodeSelectorTerms: []corev1.NodeSelectorTerm{
						{
							MatchExpressions: []corev1.NodeSelectorRequirement{
								{
									Key:      "test",
									Operator: "In",
									Values:   []string{"1", "2"},
								},
							},
						},
					},
				},
			},
		}
		toleration = []corev1.Toleration{
			{
				Key:      "test",
				Operator: "In",
				Value:    "test",
			},
		}
	)
	kc := &KubernetesClient{
		Client:    fake.NewClient(),
		Interface: fake.NewClientset(),
		TaskConfig: taskconfig.Config{
			Affinity:    affinity,
			Tolerations: toleration,
		},
	}

	err := kc.RunTask(context.Background(), RunTaskOption{
		Name:               taskName,
		Namespace:          taskNamespace,
		Image:              "nginx:latest",
		Command:            nil,
		ServiceAccountName: "sa",
		ImagePullPolicy:    corev1.PullIfNotPresent,
	})
	require.NoError(t, err)

	job, err := GetResource[batchv1.Job](context.Background(), kc, taskName, taskNamespace)
	require.NoError(t, err)

	assert.Equal(t, affinity, job.Spec.Template.Spec.Affinity)
	assert.Equal(t, toleration, job.Spec.Template.Spec.Tolerations)
}

func TestRunTaskAddLabels(t *testing.T) {
	var (
		taskNamespace = "ns"
		taskName      = "task"
		labels        = map[string]string{"test": "test"}
	)
	kc := &KubernetesClient{
		Client:    fake.NewClient(),
		Interface: fake.NewClientset(),
		TaskConfig: taskconfig.Config{
			Labels: labels,
		},
	}

	err := kc.RunTask(context.Background(), RunTaskOption{
		Name:               taskName,
		Namespace:          taskNamespace,
		Image:              "nginx:latest",
		Command:            nil,
		ServiceAccountName: "sa",
		ImagePullPolicy:    corev1.PullIfNotPresent,
	})
	require.NoError(t, err)

	job, err := GetResource[batchv1.Job](context.Background(), kc, taskName, taskNamespace)
	require.NoError(t, err)

	assert.Equal(t, labels, job.Spec.Template.Labels)
}

func TestRunTaskAnnotations(t *testing.T) {
	var (
		taskNamespace       = "ns"
		taskName            = "task"
		expectedAnnotations = map[string]string{"cluster-autoscaler.kubernetes.io/safe-to-evict-local-volumes": "ephemeral-volume,tmp-volume"}
	)
	kc := &KubernetesClient{
		Client:     fake.NewClient(),
		Interface:  fake.NewClientset(),
		TaskConfig: taskconfig.Config{},
	}

	err := kc.RunTask(context.Background(), RunTaskOption{
		Name:               taskName,
		Namespace:          taskNamespace,
		Image:              "nginx:latest",
		Command:            nil,
		ServiceAccountName: "sa",
		ImagePullPolicy:    corev1.PullIfNotPresent,
	})
	require.NoError(t, err)

	job, err := GetResource[batchv1.Job](context.Background(), kc, taskName, taskNamespace)
	require.NoError(t, err)

	assert.Equal(t, expectedAnnotations, job.Spec.Template.Annotations)
}
