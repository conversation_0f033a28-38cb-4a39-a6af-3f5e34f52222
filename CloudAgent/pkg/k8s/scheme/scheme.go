package scheme

import (
	asoauthorization "github.com/Azure/azure-service-operator/v2/api/authorization/v1api20220401"
	asodb4pg "github.com/Azure/azure-service-operator/v2/api/dbforpostgresql/v1api20221201"
	asomanagedidentity "github.com/Azure/azure-service-operator/v2/api/managedidentity/v1api20230131"
	asonetwork "github.com/Azure/azure-service-operator/v2/api/network/v1api20220701"
	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	gcciamg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/iam/v1beta1"
	gccsql "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/sql/v1beta1"
	ackec2v1alpha1 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"
	ackiamv1alpha1 "github.com/aws-controllers-k8s/iam-controller/apis/v1alpha1"
	ackrdsv1alpha1 "github.com/aws-controllers-k8s/rds-controller/apis/v1alpha1"
	ackcorev1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	vpcresources_v1beta1 "github.com/aws/amazon-vpc-resource-controller-k8s/apis/vpcresources/v1beta1"
	prometheusv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	acidv1 "github.com/zalando/postgres-operator/pkg/apis/acid.zalan.do/v1"
	appv1 "k8s.io/api/apps/v1"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/runtime"
	utilruntime "k8s.io/apimachinery/pkg/util/runtime"

	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

var (
	scheme         = runtime.NewScheme()
	parameterCodec *runtime.ParameterCodec
)

func init() {
	utilruntime.Must(gcciamg.AddToScheme(scheme))
	utilruntime.Must(gcccomputeg.AddToScheme(scheme))
	utilruntime.Must(gccsql.AddToScheme(scheme))
	utilruntime.Must(ackcorev1alpha1.AddToScheme(scheme))
	utilruntime.Must(ackec2v1alpha1.AddToScheme(scheme))
	utilruntime.Must(ackiamv1alpha1.AddToScheme(scheme))
	utilruntime.Must(ackrdsv1alpha1.AddToScheme(scheme))
	utilruntime.Must(corev1.SchemeBuilder.AddToScheme(scheme))
	utilruntime.Must(appv1.SchemeBuilder.AddToScheme(scheme))
	utilruntime.Must(batchv1.SchemeBuilder.AddToScheme(scheme))
	utilruntime.Must(rwv1alpha1.AddToScheme(scheme))
	utilruntime.Must(rbacv1.AddToScheme(scheme))
	utilruntime.Must(prometheusv1.AddToScheme(scheme))
	utilruntime.Must(vpcresources_v1beta1.AddToScheme(scheme))
	utilruntime.Must(networkingv1.AddToScheme(scheme))
	utilruntime.Must(asomanagedidentity.AddToScheme(scheme))
	utilruntime.Must(asoauthorization.AddToScheme(scheme))
	utilruntime.Must(asonetwork.AddToScheme(scheme))
	utilruntime.Must(acidv1.AddToScheme(scheme))
	utilruntime.Must(asodb4pg.AddToScheme(scheme))
	parameterCodec = utils.Ptr(runtime.NewParameterCodec(scheme))
}

func Scheme() *runtime.Scheme {
	return scheme
}

func ParameterCodec() runtime.ParameterCodec {
	return *parameterCodec
}
