package k8s

import (
	"context"
	"encoding/json"
	"fmt"

	"sigs.k8s.io/controller-runtime/pkg/client"
)

// ObjPtr represents the pointer of a type T, with the constraint that *T must
// implements client.Object.
type ObjPtr[T any] interface {
	client.Object
	*T
}

func GetResource[T any, P ObjPtr[T]](ctx context.Context, kc client.Client, name, namespace string) (P, error) {
	var t T
	var p P = &t
	err := kc.Get(ctx, client.ObjectKey{Namespace: namespace, Name: name}, p)
	if err != nil {
		return nil, err
	}
	return p, nil
}

func DeleteResource[T any, P ObjPtr[T]](ctx context.Context, kc client.Client, name, namespace string, opts ...client.DeleteOption) error {
	var t T
	var p P = &t
	b := []byte(fmt.Sprintf(`{"metadata":{"name":"%s","namespace":"%s"}}`, name, namespace))
	err := json.Unmarshal(b, p)
	if err != nil {
		return err
	}
	return kc.Delete(ctx, p, opts...)
}

func MergePatchResource[T any, P ObjPtr[T]](ctx context.Context, kc client.Client, from P, to P) error {
	return kc.Patch(ctx, to, client.MergeFrom(from))
}
