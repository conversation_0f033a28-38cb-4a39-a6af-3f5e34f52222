package taskconfig

import (
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbcfgk8s "github.com/risingwavelabs/cloudagent/pbgen/config/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	utilsproto "github.com/risingwavelabs/cloudagent/pkg/utils/proto"
)

type Config struct {
	Image               string
	ServiceAccount      string
	Namespace           string
	PullPolicy          corev1.PullPolicy
	EncodedRunnerConfig string
	Affinity            *corev1.Affinity
	Tolerations         []corev1.Toleration
	Labels              map[string]string
}

func TaskPullPolicyToK8sImagePullPolicy(p pbcfgk8s.PullPolicy) (corev1.PullPolicy, error) {
	switch p {
	case pbcfgk8s.PullPolicy_PULL_ALWAYS:
		return corev1.PullAlways, nil
	case pbcfgk8s.PullPolicy_PULL_IF_NOT_PRESENT:
		return corev1.PullIfNotPresent, nil
	case pbcfgk8s.PullPolicy_UNKNOWN:
		return "", eris.Errorf("unknown task pull policy %v", p).WithCode(eris.CodeInvalidArgument)
	}
	return "", eris.New("unreachable")
}

func FromConfigProto(protoCfg *pbcfg.Config) (Config, error) {
	protoTaskCfg := protoCfg.GetK8SConfig().GetTaskConfig()
	pullPolicy, err := TaskPullPolicyToK8sImagePullPolicy(protoTaskCfg.GetPullPolicy())
	if err != nil {
		return Config{}, err
	}
	if len(protoTaskCfg.GetImage()) == 0 {
		return Config{}, eris.New("task runner image cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(protoTaskCfg.GetServiceAccount()) == 0 {
		return Config{}, eris.New("task runner cluster servce account cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(protoTaskCfg.GetNamespace()) == 0 {
		return Config{}, eris.New("task runner namespace cannot be empty").WithCode(eris.CodeInvalidArgument)
	}

	encodedRunnerConfig, err := utilsproto.ToBase64(protoCfg)
	if err != nil {
		return Config{}, eris.Wrap(err, "failed to encode runner config")
	}

	var affinity *corev1.Affinity
	if protoTaskCfg.GetAffinity() != nil {
		affinity, err = conversion.FromAffinityProto(protoTaskCfg.GetAffinity())
		if err != nil {
			return Config{}, eris.Wrapf(err, "failed to parse task affinity settings: %v", protoTaskCfg.GetAffinity())
		}
	}

	var tolerations []corev1.Toleration
	for _, ele := range protoTaskCfg.GetTolerations() {
		t, err := conversion.FromTolerationProto(ele)
		if err != nil {
			return Config{}, eris.Wrapf(err, "failed to parse task toleration settings: %v", protoTaskCfg.GetTolerations())
		}
		tolerations = append(tolerations, t)
	}

	return Config{
		Image:               protoTaskCfg.GetImage(),
		ServiceAccount:      protoTaskCfg.GetServiceAccount(),
		Namespace:           protoTaskCfg.GetNamespace(),
		PullPolicy:          pullPolicy,
		EncodedRunnerConfig: encodedRunnerConfig,
		Affinity:            affinity,
		Tolerations:         tolerations,
		Labels:              protoTaskCfg.GetLabels(),
	}, nil
}
