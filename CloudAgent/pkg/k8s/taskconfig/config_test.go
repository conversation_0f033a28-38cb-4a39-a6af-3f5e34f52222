package taskconfig

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbcfgk8s "github.com/risingwavelabs/cloudagent/pbgen/config/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsproto "github.com/risingwavelabs/cloudagent/pkg/utils/proto"
)

func TestFromProtoConfig(t *testing.T) {
	pbCfg := pbcfg.Config{
		K8SConfig: &pbcfgk8s.Config{
			TaskConfig: &pbcfgk8s.TaskConfig{
				Image:          "image",
				Namespace:      "ns",
				ServiceAccount: "sa",
				PullPolicy:     pbcfgk8s.PullPolicy_PULL_IF_NOT_PRESENT,
				Affinity: &pbk8s.Affinity{
					NodeAffinity: &pbk8s.NodeAffinity{
						RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
							NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
								{
									MatchExpressions: []*pbk8s.NodeSelectorRequirement{
										{
											Key:      "key1",
											Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
											Values:   []string{"val1", "val2"},
										},
									},
								},
							},
						},
					},
				},
				Tolerations: []*pbk8s.Toleration{
					{
						Key:            "testkey",
						Value:          "testvalue",
						Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
						Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
						TolerationSecs: utils.Ptr(int64(10)),
					},
				},
			},
		},
	}
	cfg, err := FromConfigProto(&pbCfg)
	require.NoError(t, err)

	encodedRunnerConfig, err := utilsproto.ToBase64(&pbCfg)
	require.NoError(t, err)

	assert.Equal(t, cfg, Config{
		Image:               "image",
		Namespace:           "ns",
		ServiceAccount:      "sa",
		PullPolicy:          corev1.PullIfNotPresent,
		EncodedRunnerConfig: encodedRunnerConfig,
		Affinity: &corev1.Affinity{
			NodeAffinity: &corev1.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
					NodeSelectorTerms: []corev1.NodeSelectorTerm{
						{
							MatchExpressions: []corev1.NodeSelectorRequirement{
								{
									Key:      "key1",
									Operator: corev1.NodeSelectorOpDoesNotExist,
									Values:   []string{"val1", "val2"},
								},
							},
						},
					},
				},
			},
		},

		Tolerations: []corev1.Toleration{
			{
				Key:               "testkey",
				Value:             "testvalue",
				Effect:            corev1.TaintEffectNoExecute,
				Operator:          corev1.TolerationOpEqual,
				TolerationSeconds: utils.Ptr(int64(10)),
			},
		},
	})
}

func TestFromProtoConfigInvalid(t *testing.T) {
	pbCfg := pbcfg.Config{
		K8SConfig: &pbcfgk8s.Config{
			TaskConfig: &pbcfgk8s.TaskConfig{
				Image:          "image",
				Namespace:      "ns",
				ServiceAccount: "sa",
				PullPolicy:     pbcfgk8s.PullPolicy_UNKNOWN,
			},
		},
	}
	_, err := FromConfigProto(&pbCfg)
	assert.Error(t, err)
}
