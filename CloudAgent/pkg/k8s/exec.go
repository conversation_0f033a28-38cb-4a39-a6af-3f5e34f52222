package k8s

import (
	"bytes"
	"context"

	"github.com/risingwavelabs/eris"
	"k8s.io/client-go/tools/remotecommand"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/scheme"

	corev1 "k8s.io/api/core/v1"
)

var ErrFailedCommand = eris.New("failed to run command")

func (kc *KubernetesClient) ExecPod(ctx context.Context, name, namespace string, cmd []string) (string, string, error) {
	req := kc.
		CoreV1().
		RESTClient().
		Post().
		Resource("pods").
		Name(name).
		Namespace(namespace).
		SubResource("exec")
	execOption := &corev1.PodExecOptions{
		Command: cmd,
		Stdin:   false,
		Stdout:  true,
		Stderr:  true,
		TTY:     false,
	}
	req.VersionedParams(execOption, scheme.ParameterCodec())
	if req.Error() != nil {
		return "", "", eris.Wrapf(req.<PERSON>rror(), "failed to build request: %v", execOption)
	}
	exec, err := kc.NewSPDYExecutor(kc.GetRestCfg(), "POST", req.URL())
	if err != nil {
		return "", "", eris.Wrapf(err, "failed to build SPDY Executor, namespace: %s, pod name: %s", namespace, name)
	}

	outBuf := new(bytes.Buffer)
	errBuf := new(bytes.Buffer)

	err = exec.StreamWithContext(ctx, remotecommand.StreamOptions{
		Stdin:  nil,
		Stdout: outBuf,
		Stderr: errBuf,
	})

	outString := outBuf.String()
	errString := errBuf.String()
	if err != nil {
		// TODO: switch to eris.Join once it's supported.
		// https://github.com/risingwavelabs/eris/issues/22
		return "", "", eris.Wrapf(ErrFailedCommand, "err: %v, stdout: %s, stderr: %s", err, outString, errString)
	}

	return outString, errString, nil
}
