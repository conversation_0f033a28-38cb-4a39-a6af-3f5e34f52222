package config

import (
	"os"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbcfgk8s "github.com/risingwavelabs/cloudagent/pbgen/config/k8s"
)

func createTmpFile(name string, content []byte) (*os.File, error) {
	f, err := os.CreateTemp("", name)
	if err != nil {
		return nil, err
	}
	_, err = f.Write(content)
	if err != nil {
		return nil, err
	}
	return f, nil
}

func TestReadConfig(t *testing.T) {
	configPath, err := createTmpFile("config", []byte(`k8s_config {
    cluster_id: "they"
    static_token_auth {
        master_url: "killed"
        token: "kenny"
    }
}
	`))

	require.NoError(t, err)
	defer func() { _ = os.Remove(configPath.Name()) }()
	cfg, err := Read(configPath.Name())
	require.NoError(t, err)

	expectedCfg := &pbcfg.Config{
		K8SConfig: &pbcfgk8s.Config{
			ClusterId: "they",
			Auth: &pbcfgk8s.Config_StaticTokenAuth{
				StaticTokenAuth: &pbcfgk8s.StaticTokenAuth{
					MasterUrl: "killed",
					Token:     "kenny",
				},
			},
		},
	}
	assert.Equal(t, cfg.String(), expectedCfg.String())
}

func TestEncodeAndDecode(t *testing.T) {
	cfg := &pbcfg.Config{
		K8SConfig: &pbcfgk8s.Config{
			ClusterId: "they",
			Auth: &pbcfgk8s.Config_StaticTokenAuth{
				StaticTokenAuth: &pbcfgk8s.StaticTokenAuth{
					MasterUrl: "killed",
					Token:     "kenny",
				},
			},
		},
	}

	encoded, err := ToBase64(cfg)
	require.NoError(t, err)

	decoded, err := FromBase64(encoded)
	require.NoError(t, err)

	assert.Equal(t, cfg.String(), decoded.String())
}
