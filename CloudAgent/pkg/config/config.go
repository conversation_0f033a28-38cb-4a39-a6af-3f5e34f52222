package config

import (
	"os"

	"github.com/risingwavelabs/eris"
	"google.golang.org/protobuf/encoding/prototext"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	utilsproto "github.com/risingwavelabs/cloudagent/pkg/utils/proto"
)

func Read(path string) (*pbcfg.Config, error) {
	if path == "" {
		return nil, eris.Errorf("config path is not provided").WithCode(eris.CodeInvalidArgument)
	}
	raw, err := os.ReadFile(path)
	if err != nil {
		return nil, eris.Wrapf(err, "config cannot be read at %s", path)
	}
	clusterCfg := &pbcfg.Config{}
	if err := prototext.Unmarshal(raw, clusterCfg); err != nil {
		return nil, eris.Wrapf(err, "config cannot be parsed: %s", raw)
	}
	return clusterCfg, nil
}

func FromBase64(encode string) (*pbcfg.Config, error) {
	cfg := &pbcfg.Config{}
	err := utilsproto.FromBase64(encode, cfg)
	if err != nil {
		return nil, err
	}
	return cfg, nil
}

func ToBase64(cfg *pbcfg.Config) (string, error) {
	return utilsproto.ToBase64(cfg)
}
