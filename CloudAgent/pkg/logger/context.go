package logger

import (
	"context"
)

type loggerContextKeyType string

const loggerContextKey = loggerContextKeyType("logger")

func FromCtx(ctx context.Context) *LogAgent {
	if l := ctx.Value(loggerContextKey); l != nil {
		return l.(*LogAgent)
	}
	return NewLogAgent(defaultName)
}

func WithCtx(ctx context.Context, l *LogAgent) context.Context {
	return context.WithValue(ctx, loggerContextKey, l)
}
