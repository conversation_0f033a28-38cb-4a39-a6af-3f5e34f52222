package metadata

import (
	"context"

	"github.com/risingwavelabs/eris"
	grpcmeta "google.golang.org/grpc/metadata"
)

const (
	workflowIDKey   = "workflow-id"
	ginRequestIDKey = "gin-request-id"
)

type RequestMeta struct {
	WorkflowID   *string
	GinRequestID *string
}

func (m *RequestMeta) WriteGRPCMeta(ctx context.Context) context.Context {
	if m.WorkflowID != nil {
		ctx = grpcmeta.AppendToOutgoingContext(ctx, workflowIDKey, *m.WorkflowID)
	}
	if m.GinRequestID != nil {
		ctx = grpcmeta.AppendToOutgoingContext(ctx, ginRequestIDKey, *m.GinRequestID)
	}
	return ctx
}

func FromGRPCMeta(ctx context.Context) (RequestMeta, error) {
	rm := RequestMeta{}
	gm, ok := grpcmeta.FromIncomingContext(ctx)
	if !ok {
		return rm, eris.Errorf("failed to extract gRPC metadata from context %+v", ctx)
	}
	workflowID := gm.Get(workflowIDKey)
	if len(workflowID) > 0 {
		rm.WorkflowID = &workflowID[0]
	}
	ginRequestID := gm.Get(ginRequestIDKey)
	if len(ginRequestID) > 0 {
		rm.GinRequestID = &ginRequestID[0]
	}
	return rm, nil
}
