package logger

import (
	"fmt"
	"os"
	"slices"

	"go.uber.org/zap"
	"go.uber.org/zap/zapcore"
)

var DEBUG = os.Getenv("CLOUDAGENT_DEBUG") == "1"

type LogAgent struct {
	name   string
	fileds []zap.Field
}

const (
	defaultName = "default"
)

func NewLogAgent(name string) *LogAgent {
	return &LogAgent{name: name}
}

func (a *LogAgent) AppendFiled(field zap.Field) *LogAgent {
	a.fileds = append(a.fileds, field)
	return a
}

var defaultLogger = zap.New(
	zapcore.NewCore(
		zapcore.NewConsoleEncoder(zapcore.EncoderConfig{
			EncodeTime:     zapcore.ISO8601TimeEncoder,
			TimeKey:        "time",
			MessageKey:     "msg",
			LevelKey:       "level",
			CallerKey:      "caller",
			EncodeLevel:    zapcore.CapitalColorLevelEncoder,
			EncodeCaller:   zapcore.ShortCallerEncoder,
			EncodeDuration: zapcore.SecondsDurationEncoder,
		}),
		zapcore.AddSync(os.Stdout),
		zap.InfoLevel,
	),
	zap.AddCaller(),
	zap.AddCallerSkip(1),
)

// provide more information for debugging.
func (a *LogAgent) Debug(msg string, fields ...zap.Field) {
	defaultLogger.Debug(fmt.Sprintf("[%10s] %s", a.name, msg), slices.Concat(a.fileds, fields)...)
}

// provide basic observability.
func (a *LogAgent) Info(msg string, fields ...zap.Field) {
	defaultLogger.Info(fmt.Sprintf("[%10s] %s", a.name, msg), slices.Concat(a.fileds, fields)...)
}

// expected situation but worth a look.
func (a *LogAgent) Warn(msg string, fields ...zap.Field) {
	defaultLogger.Warn(fmt.Sprintf("[%10s] %s", a.name, msg), slices.Concat(a.fileds, fields)...)
}

// unexpected error causing broken connection.
func (a *LogAgent) Error(msg string, fields ...zap.Field) {
	defaultLogger.Error(fmt.Sprintf("[%10s] %s", a.name, msg), slices.Concat(a.fileds, fields)...)
}

// fatal error causing application shutdown.
func (a *LogAgent) Panic(msg string, fields ...zap.Field) {
	defaultLogger.Panic(fmt.Sprintf("[%10s] %s", a.name, msg), slices.Concat(a.fileds, fields)...)
}

// provide more information for debugging.
func (a *LogAgent) Debugf(msg string, args ...any) {
	defaultLogger.Debug(fmt.Sprintf("[%10s] %s", a.name, fmt.Sprintf(msg, args...)))
}

// provide basic observability.
func (a *LogAgent) Infof(msg string, args ...any) {
	defaultLogger.Info(fmt.Sprintf("[%10s] %s", a.name, fmt.Sprintf(msg, args...)))
}

// expected situation but worth a look.
func (a *LogAgent) Warnf(msg string, args ...any) {
	defaultLogger.Warn(fmt.Sprintf("[%10s] %s", a.name, fmt.Sprintf(msg, args...)))
}

// unexpected error causing broken connection.
func (a *LogAgent) Errorf(msg string, args ...any) {
	defaultLogger.Error(fmt.Sprintf("[%10s] %s", a.name, fmt.Sprintf(msg, args...)))
}

// fatal error causing application shutdown.
func (a *LogAgent) Panicf(msg string, args ...any) {
	defaultLogger.Panic(fmt.Sprintf("[%10s] %s", a.name, fmt.Sprintf(msg, args...)))
}
