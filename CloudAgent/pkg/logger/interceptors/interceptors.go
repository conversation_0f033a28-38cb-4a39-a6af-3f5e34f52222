package interceptors

import (
	"context"
	"time"

	middleware "github.com/grpc-ecosystem/go-grpc-middleware/v2"

	"github.com/risingwavelabs/cloudagent/pkg/logger/metadata"

	"go.uber.org/zap"
	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/risingwavelabs/cloudagent/pkg/logger"
)

const (
	workflowIDFieldKey     = "workflow-id"
	ginRequestIDFieldKey   = "gin-request-id"
	fullMethodFieldKey     = "full-method"
	latencyFieldKey        = "latency"
	codeFieldKey           = "grpc.code"
	errorFieldKey          = "grpc.error"
	isClientStreamFieldKey = "is-client-stream"
	isServerStreamFieldKey = "is-server-stream"
)

func newRPCLogger(ctx context.Context) *logger.LogAgent {
	l := logger.NewLogAgent("rpc")
	reqMeta, err := metadata.FromGRPCMeta(ctx)
	if err != nil {
		l.Warnf("failed to retrieve request metadata: %s", err)
		return l
	}
	if reqMeta.WorkflowID != nil {
		l.AppendFiled(zap.String(workflowIDFieldKey, *reqMeta.WorkflowID))
	}
	if reqMeta.GinRequestID != nil {
		l.AppendFiled(zap.String(ginRequestIDFieldKey, *reqMeta.GinRequestID))
	}
	return l
}

func UnaryServerInterceptor() grpc.UnaryServerInterceptor {
	return func(ctx context.Context, req any, info *grpc.UnaryServerInfo, handler grpc.UnaryHandler) (any, error) {
		start := time.Now()
		l := newRPCLogger(ctx)
		ctx = logger.WithCtx(ctx, l)
		res, err := handler(ctx, req)
		code := status.Code(err)
		fields := []zap.Field{
			zap.String(fullMethodFieldKey, info.FullMethod),
			zap.Duration(latencyFieldKey, time.Since(start)),
			zap.String(codeFieldKey, code.String()),
		}
		if err != nil {
			fields = append(fields, zap.Error(err))
		}

		switch code {
		case codes.Unknown, codes.DeadlineExceeded, codes.PermissionDenied, codes.Unauthenticated, codes.ResourceExhausted,
			codes.FailedPrecondition, codes.Aborted, codes.OutOfRange:
			l.Warn(
				"unary request throws warning",
				fields...,
			)

		case codes.Unimplemented, codes.Internal, codes.Unavailable, codes.DataLoss:
			l.Error(
				"unary request throws error",
				fields...,
			)
		case codes.OK, codes.Canceled, codes.InvalidArgument, codes.NotFound, codes.AlreadyExists:
			fallthrough
		default:
			l.Info(
				"unary request processed",
				fields...,
			)
		}
		return res, err
	}
}

func StreamServerInterceptor() grpc.StreamServerInterceptor {
	return func(srv any, stream grpc.ServerStream, info *grpc.StreamServerInfo, handler grpc.StreamHandler) error {
		start := time.Now()
		l := newRPCLogger(stream.Context())
		wrappedStream := middleware.WrapServerStream(stream)
		wrappedStream.WrappedContext = logger.WithCtx(stream.Context(), l)
		err := handler(srv, middleware.WrapServerStream(wrappedStream))
		code := status.Code(err)
		fields := []zap.Field{
			zap.String(fullMethodFieldKey, info.FullMethod),
			zap.Duration(latencyFieldKey, time.Since(start)),
			zap.String(codeFieldKey, code.String()),
			zap.Bool(isClientStreamFieldKey, info.IsClientStream),
			zap.Bool(isServerStreamFieldKey, info.IsServerStream),
		}
		if err != nil {
			fields = append(fields, zap.Error(err))
		}
		switch code {
		case codes.Unknown, codes.DeadlineExceeded, codes.PermissionDenied, codes.Unauthenticated, codes.ResourceExhausted,
			codes.FailedPrecondition, codes.Aborted, codes.OutOfRange:
			l.Warn(
				"stream request throws warning",
				fields...,
			)

		case codes.Unimplemented, codes.Internal, codes.Unavailable, codes.DataLoss:
			l.Error(
				"stream request throws error",
				fields...,
			)
		case codes.OK, codes.Canceled, codes.InvalidArgument, codes.NotFound, codes.AlreadyExists:
			fallthrough
		default:
			l.Info(
				"stream request processed",
				fields...,
			)
		}
		return err
	}
}

type RequestMetaExtractor func(context.Context) metadata.RequestMeta

func UnaryClientInterceptor(reqMetaExtractor RequestMetaExtractor) grpc.UnaryClientInterceptor {
	return func(ctx context.Context, method string, req, reply interface{}, cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker, opts ...grpc.CallOption) error {
		reqMeta := reqMetaExtractor(ctx)
		ctx = reqMeta.WriteGRPCMeta(ctx)
		return invoker(ctx, method, req, reply, cc, opts...)
	}
}

func StreamClientInterceptor(reqMetaExtractor RequestMetaExtractor) grpc.StreamClientInterceptor {
	return func(ctx context.Context, desc *grpc.StreamDesc, cc *grpc.ClientConn, method string, streamer grpc.Streamer, opts ...grpc.CallOption) (grpc.ClientStream, error) {
		reqMeta := reqMetaExtractor(ctx)
		ctx = reqMeta.WriteGRPCMeta(ctx)
		return streamer(ctx, desc, cc, method, opts...)
	}
}
