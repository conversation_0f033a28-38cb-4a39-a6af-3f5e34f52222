package risectl

import (
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	"github.com/risingwavelabs/risingwave-operator/pkg/consts"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	rwNamespace    = "rw-namespace"
	rwName         = "rw-name"
	standaloneName = rwName + "-0"
	metaNodeName   = "meta-name"
	cmdOutput      = "output"
)

func TestRisingwaveIsStandalone(t *testing.T) {
	for _, isStandalone := range []bool{true, false} {
		fakeSPDYExecutor := &fake.SPDYExecutor{
			ToStdout: []byte(cmdOutput),
		}
		kc := &k8s.KubernetesClient{
			Client: fake.NewClient(&rwv1alpha1.RisingWave{
				ObjectMeta: metav1.ObjectMeta{
					Name:      rwName,
					Namespace: rwNamespace,
				},
				Spec: rwv1alpha1.RisingWaveSpec{
					EnableStandaloneMode: utils.Ptr(isStandalone),
				},
			}),
			Interface:       fake.NewClientset(),
			NewSPDYExecutor: fakeSPDYExecutor.New,
		}

		runner := &MetaNodeExecutor{
			kc: kc,
		}
		gotStandalone, err := runner.risingwaveIsStandalone(context.Background(), rwName, rwNamespace)
		require.NoError(t, err)
		assert.EqualValues(t, isStandalone, gotStandalone)
	}
}

func TestRunRisectlCommand(t *testing.T) {
	tests := []struct {
		// description of test for logging only
		description string

		// true if standalone, false if cluster
		isStandalone bool

		// expected stdout of the command
		expected string
	}{
		{
			description:  "execute on standalone instance",
			isStandalone: true,
			expected:     fmt.Sprintf("/namespaces/%s/pods/%s/exec", rwNamespace, standaloneName),
		}, {
			description:  "execute on cluster instance",
			isStandalone: false,
			expected:     fmt.Sprintf("/namespaces/%s/pods/%s/exec", rwNamespace, metaNodeName),
		},
	}

	for _, test := range tests {
		fakeSPDYExecutor := &fake.SPDYExecutor{
			ToStdout: []byte(cmdOutput),
		}
		kc := &k8s.KubernetesClient{
			Client: fake.NewClient(&rwv1alpha1.RisingWave{
				ObjectMeta: metav1.ObjectMeta{
					Name:      rwName,
					Namespace: rwNamespace,
				},
				Spec: rwv1alpha1.RisingWaveSpec{
					EnableStandaloneMode: utils.Ptr(test.isStandalone),
				},
			}),
			// we provide both standalone and meta pod
			// code should execute on either, depending on the EnableStandalone flag in RW instance
			Interface: fake.NewClientset(
				&corev1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Name:      metaNodeName,
						Namespace: rwNamespace,
						Labels: map[string]string{
							consts.LabelRisingWaveComponent: consts.ComponentMeta,
							consts.LabelRisingWaveName:      rwName,
						},
					},
				},
				&corev1.Pod{
					ObjectMeta: metav1.ObjectMeta{
						Name:      standaloneName,
						Namespace: rwNamespace,
						Labels: map[string]string{
							consts.LabelRisingWaveComponent: consts.ComponentStandalone,
							consts.LabelRisingWaveName:      rwName,
						},
					},
				},
			),

			NewSPDYExecutor: fakeSPDYExecutor.New,
		}

		runner := &MetaNodeExecutor{
			kc: kc,
		}

		output, err := runner.RunRisectlCommand(context.Background(), CommandOption{
			RwName:      rwName,
			RwNamespace: rwNamespace,
		})

		// TODO: use test description
		require.NoError(t, err)
		assert.True(t, strings.Contains(output, cmdOutput))
		assert.Equal(t,
			test.expected,
			fakeSPDYExecutor.URL().Path,
		)
		assert.Equal(t, fakeSPDYExecutor.URL().Query()["command"], risectlCmd)
	}
}
