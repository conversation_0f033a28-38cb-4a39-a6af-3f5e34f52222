// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/risectl (interfaces: Executor)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/risectl -package=risectl -destination=pkg/risectl/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/risectl Executor
//

// Package risectl is a generated GoMock package.
package risectl

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockExecutor is a mock of Executor interface.
type MockExecutor struct {
	ctrl     *gomock.Controller
	recorder *MockExecutorMockRecorder
	isgomock struct{}
}

// MockExecutorMockRecorder is the mock recorder for MockExecutor.
type MockExecutorMockRecorder struct {
	mock *MockExecutor
}

// NewMockExecutor creates a new mock instance.
func NewMockExecutor(ctrl *gomock.Controller) *MockExecutor {
	mock := &MockExecutor{ctrl: ctrl}
	mock.recorder = &MockExecutorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockExecutor) EXPECT() *MockExecutorMockRecorder {
	return m.recorder
}

// RunRisectlCommand mocks base method.
func (m *MockExecutor) RunRisectlCommand(ctx context.Context, option CommandOption) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RunRisectlCommand", ctx, option)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// RunRisectlCommand indicates an expected call of RunRisectlCommand.
func (mr *MockExecutorMockRecorder) RunRisectlCommand(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RunRisectlCommand", reflect.TypeOf((*MockExecutor)(nil).RunRisectlCommand), ctx, option)
}
