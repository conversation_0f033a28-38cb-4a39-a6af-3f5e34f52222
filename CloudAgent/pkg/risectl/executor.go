package risectl

import (
	"context"
	"fmt"

	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"

	"github.com/risingwavelabs/eris"
	"github.com/risingwavelabs/risingwave-operator/pkg/consts"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/utils/ptr"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

var (
	risectlCmd = []string{"/usr/bin/env", "RW_META_ADDR=http://127.0.0.1:5690", "/risingwave/bin/risingwave", "ctl"}
)

// Required to specify how to call risectl.
type CommandOption struct {
	// The namespace which hold the meta pod
	RwNamespace string

	// The name of the risingwave instance listed under the label risingwave/name=risingwave
	// value is usually "risingwave"
	RwName string

	// The risectl command to run, e.g. []string{"meta", "cluster-info"}
	Cmd []string
}

// Run risectl directly in the meta pod.
type Executor interface {
	RunRisectlCommand(ctx context.Context, option CommandOption) (string, error)
}

type MetaNodeExecutor struct {
	kc k8s.KubernetesClientInterface
}

// NewMetaNodeExecutor returns a new metaNodeExecutor.
// Can be used for running risectl commands directly in the meta pod.
func NewMetaNodeExecutor(kc k8s.KubernetesClientInterface) Executor {
	return &MetaNodeExecutor{
		kc: kc,
	}
}

func (m *MetaNodeExecutor) getPodInner(ctx context.Context, rwName, rwNamespace, component string) (string, error) {
	metaPods, err := m.kc.CoreV1().Pods(rwNamespace).List(ctx, metav1.ListOptions{
		LabelSelector: labels.FormatLabels(map[string]string{
			consts.LabelRisingWaveComponent: component,
			consts.LabelRisingWaveName:      rwName,
		}),
	})
	if err != nil {
		return "", eris.Wrapf(err, "failed to list %s pods of risingwave %s/%s", component, rwNamespace, rwName)
	}
	if len(metaPods.Items) == 0 {
		return "", eris.Errorf("no %s pods found of risingwave %s/%s", component, rwNamespace, rwName)
	}
	return metaPods.Items[0].Name, nil
}

func (m *MetaNodeExecutor) getMetaNodePodName(ctx context.Context, rwName, rwNamespace string) (string, error) {
	return m.getPodInner(ctx, rwName, rwNamespace, consts.ComponentMeta)
}

func (m *MetaNodeExecutor) getStandalonePodName(ctx context.Context, rwName, rwNamespace string) (string, error) {
	return m.getPodInner(ctx, rwName, rwNamespace, consts.ComponentStandalone)
}

func (m *MetaNodeExecutor) getRisectlPodName(ctx context.Context, rwName, rwNamespace string) (string, error) {
	isStandalone, err := m.risingwaveIsStandalone(ctx, rwName, rwNamespace)
	if err != nil {
		return "", eris.Wrapf(err, "failed to determine if risingwave %s/%s is in standalone mode", rwNamespace, rwName)
	}

	if isStandalone {
		standalonePodName, err := m.getStandalonePodName(ctx, rwName, rwNamespace)
		if err != nil {
			return "", eris.Wrapf(err, "failed to get standalone pod of risingwave %s/%s", rwNamespace, rwName)
		}
		return standalonePodName, nil
	}

	// cluster mode
	metaNodePodName, err := m.getMetaNodePodName(ctx, rwName, rwNamespace)
	if err != nil {
		return "", eris.Wrapf(err, "failed to get meta pod of risingwave %s/%s", rwNamespace, rwName)
	}
	return metaNodePodName, nil
}

func (m *MetaNodeExecutor) risingwaveIsStandalone(ctx context.Context, rwName, rwNamespace string) (bool, error) {
	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, m.kc, rwName, rwNamespace)
	if err != nil {
		return false, eris.Wrapf(err, "failed to get risingwave %s/%s", rwNamespace, rwName)
	}
	return ptr.Deref(rw.Spec.EnableStandaloneMode, false), nil
}

// ErrFailedCommand is the error code returned by RunRisectlCommand on failure if the command itself failed.
var ErrFailedCommand = eris.New("failed to run risectl command")

// CommandFailed returns true if the error is ErrFailedCommand.
// If RunRisectlCommand failed for an unrelated reason returns false.
func CommandFailed(err error) bool {
	return eris.Is(err, ErrFailedCommand)
}

// RunRisectlCommand uses Kubernetes client to run a risectl command in the remote meta pod.
// Returns stdout and stderr of the command.
// May fail because of network issues, because command returns non-zero exit code, or because meta is down.
// Returns ErrFailedCommand on failure.
func (m *MetaNodeExecutor) RunRisectlCommand(ctx context.Context, option CommandOption) (string, error) {
	risectlPodName, err := m.getRisectlPodName(ctx, option.RwName, option.RwNamespace)
	if err != nil {
		return "", eris.Wrap(err, "failed to get resictl pod name")
	}
	o, e, err := m.kc.ExecPod(ctx, risectlPodName, option.RwNamespace, append(risectlCmd, option.Cmd...))
	if err != nil {
		return "", eris.Wrap(err, "failed to execute risectl command")
	}
	return fmt.Sprintf("stdout: %s, stderr: %s", o, e), nil
}
