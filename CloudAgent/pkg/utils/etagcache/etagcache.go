package etagcache

import "sync"

type Item struct {
	ETag string
	Data []byte
}

// Cache is a simple cache that stores items with an ETag.
// It is used to cache the result of a function that returns a byte slice and an ETag.
// The ETag is used to check if the item has changed since the last time it was cached.
// If the ETag has changed, the item is fetched from the source and the cache is updated.
// If the ETag has not changed, the item is returned from the cache.
// Note that etag is not like checksum, files with the same content might have different etags.
type Cache struct {
	cache map[string]Item
	mu    sync.RWMutex
}

func NewCache() *Cache {
	return &Cache{
		cache: make(map[string]Item),
	}
}

func (c *Cache) Get(key string, etag string) ([]byte, bool) {
	c.mu.RLock()
	defer c.mu.RUnlock()
	val, ok := c.cache[key]
	if !ok {
		return nil, false
	}
	if val.ETag != etag {
		return nil, false
	}
	return val.Data, true
}

func (c *Cache) Set(key string, val []byte, etag string) {
	c.mu.Lock()
	defer c.mu.Unlock()
	c.cache[key] = Item{
		ETag: etag,
		Data: val,
	}
}
