package utils

import (
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
)

func GetDummyRwSpec(isStandalone bool) *rwv1alpha1.RisingWaveSpec {
	expectedCPULimit := resource.NewScaledQuantity(500, resource.Milli)
	// Generate `s`` field
	_ = expectedCPULimit.String()
	expectedCPURequest := resource.NewScaledQuantity(250, resource.Milli)
	_ = expectedCPURequest.String()

	standalone := &rwv1alpha1.RisingWaveStandaloneComponent{
		Replicas:        1,
		LogLevel:        "info",
		UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
		Template: rwv1alpha1.RisingWaveNodePodTemplate{
			ObjectMeta: rwv1alpha1.PartialObjectMeta{
				Annotations: map[string]string{"ak1": "av1", "ak2": "av2"},
				Labels:      map[string]string{"k1": "v1", "k2": "v2"},
			},
			Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
				RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
					Env: []corev1.EnvVar{
						{
							Name:  "key1",
							Value: "val1",
						},
					},
					Resources: corev1.ResourceRequirements{
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("500m"),
							corev1.ResourceMemory: resource.MustParse("128Mi"),
						},
						Requests: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("250m"),
							corev1.ResourceMemory: resource.MustParse("64Mi"),
						},
					},
				},
				ServiceAccountName: "test_sa",
			},
		},
	}

	metaSpec := rwv1alpha1.RisingWaveComponent{
		LogLevel: "info",
		NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
					Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
				},
				Template: rwv1alpha1.RisingWaveNodePodTemplate{
					ObjectMeta: rwv1alpha1.PartialObjectMeta{
						Labels: map[string]string{"k1": "v1", "k2": "v2"},
					},
					Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
						ServiceAccountName: "test_sa",
						RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
							Env: []corev1.EnvVar{
								{
									Name:  "key1",
									Value: "val1",
								},
							},
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPULimit,
									corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPURequest,
									corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
								},
							},
						},
					},
				},
			},
		},
	}

	frontendSpec := rwv1alpha1.RisingWaveComponent{
		NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
					Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
				},
				Template: rwv1alpha1.RisingWaveNodePodTemplate{
					ObjectMeta: rwv1alpha1.PartialObjectMeta{
						Labels: map[string]string{"k1": "v1", "k2": "v2"},
					},
					Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
						ServiceAccountName: "test_sa",
						RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
							Env: []corev1.EnvVar{
								{
									Name:  "key1",
									Value: "val1",
								},
							},
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPULimit,
									corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPURequest,
									corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
								},
							},
						},
					},
				},
			},
		},
	}

	ComputeSpec := rwv1alpha1.RisingWaveComponent{
		NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
					Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
				},
				Template: rwv1alpha1.RisingWaveNodePodTemplate{
					ObjectMeta: rwv1alpha1.PartialObjectMeta{
						Labels: map[string]string{"k1": "v1", "k2": "v2"},
					},
					Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
						ServiceAccountName: "test_sa",
						RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
							Env: []corev1.EnvVar{
								{
									Name:  "key1",
									Value: "val1",
								},
							},
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPULimit,
									corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPURequest,
									corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
								},
							},
						},
					},
				},
			},
		},
	}

	CompactorSpec := rwv1alpha1.RisingWaveComponent{
		NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
					Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate,
				},
				Template: rwv1alpha1.RisingWaveNodePodTemplate{
					ObjectMeta: rwv1alpha1.PartialObjectMeta{
						Labels: map[string]string{"k1": "v1", "k2": "v2"},
					},
					Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
						ServiceAccountName: "test_sa",
						RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
							Env: []corev1.EnvVar{
								{
									Name:  "key1",
									Value: "val1",
								},
							},
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPULimit,
									corev1.ResourceMemory: *resource.NewQuantity(128*1024*1024, "BinarySI"),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    *expectedCPURequest,
									corev1.ResourceMemory: *resource.NewQuantity(64*1024*1024, "BinarySI"),
								},
							},
						},
					},
				},
			},
		},
	}

	return &rwv1alpha1.RisingWaveSpec{
		Image:                       "test_image",
		EnableDefaultServiceMonitor: Ptr(true),
		EnableFullKubernetesAddr:    Ptr(true),
		EnableEmbeddedServingMode:   Ptr(true),
		FrontendServiceType:         corev1.ServiceTypeClusterIP,
		EnableStandaloneMode:        Ptr(isStandalone),
		Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
			RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
				ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
					Name: "test_cm",
					Key:  "test_key",
				},
			},
		},
		StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
			DataDirectory: "test_dir",
			GCS: &rwv1alpha1.RisingWaveStateStoreBackendGCS{
				RisingWaveGCSCredentials: rwv1alpha1.RisingWaveGCSCredentials{
					UseWorkloadIdentity: Ptr(true),
				},
				Bucket: "test_bucket",
				Root:   "test_dir",
			},
		},
		MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
			Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
				Endpoint: "test_etcd",
			},
		},
		Components: rwv1alpha1.RisingWaveComponentsSpec{
			Standalone: standalone,
			Meta:       metaSpec,
			Frontend:   frontendSpec,
			Compute:    ComputeSpec,
			Compactor:  CompactorSpec,
		},
		LicenseKey: &rwv1alpha1.RisingWaveLicenseKey{
			SecretName: "test-secret-name",
		},
	}
}
