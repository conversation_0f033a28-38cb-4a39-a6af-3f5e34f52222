package utils

import "github.com/risingwavelabs/eris"

func IsErrNotFound(err error) bool {
	return eris.GetCode(err) == eris.CodeNotFound
}

func IsErrAlreadyExists(err error) bool {
	return eris.GetCode(err) == eris.CodeAlreadyExists
}

func IsInvalidArgument(err error) bool {
	return eris.GetCode(err) == eris.CodeInvalidArgument
}

func IsFailedPrecondition(err error) bool {
	return eris.GetCode(err) == eris.CodeFailedPrecondition
}

func IsErrAborted(err error) bool {
	return eris.GetCode(err) == eris.CodeAborted
}
