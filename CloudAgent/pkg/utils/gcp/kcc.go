package gcp

import (
	"encoding/json"

	k8sapi "k8s.io/api/core/v1"

	gcciamgalpha "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
)

// KccConditionToResourceStatus converts kcc resource conditions into a resource status.
func KccConditionToResourceStatus(conditions []gcciamgalpha.Condition) *pbresource.Status {
	for _, cond := range conditions {
		if cond.Type == gcciamgalpha.ReadyConditionType &&
			cond.Status == k8sapi.ConditionTrue {
			return &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			}
		}
	}

	msgbytes, err := json.Marshal(conditions)
	var msg string
	if err != nil {
		msg = "unable to get detailed conditions in JSON"
	} else {
		msg = string(msgbytes)
	}

	return &pbresource.Status{
		Code:    pbresource.StatusCode_NOT_READY,
		Message: msg,
	}
}

func PSCStatusToProto(status string) gcp.PscStatus {
	switch status {
	case "STATUS_UNSPECIFIED":
		return gcp.PscStatus_STATUS_UNSPECIFIED
	case "PENDING":
		return gcp.PscStatus_PENDING
	case "ACCEPTED":
		return gcp.PscStatus_ACCEPTED
	case "REJECTED":
		return gcp.PscStatus_REJECTED
	case "CLOSED":
		return gcp.PscStatus_CLOSED
	}
	return gcp.PscStatus_STATUS_UNSPECIFIED
}
