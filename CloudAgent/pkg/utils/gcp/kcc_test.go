package gcp

import (
	"testing"

	gcciamgalpha "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	"github.com/stretchr/testify/assert"
	k8sapi "k8s.io/api/core/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
)

func TestKccConditionToResourceStatus(t *testing.T) {
	tests := []struct {
		description  string
		conditions   []gcciamgalpha.Condition
		expectedCode pbresource.StatusCode
	}{
		{
			description: "OK status.",
			conditions: []gcciamgalpha.Condition{
				{
					Type:   gcciamgalpha.ReadyConditionType,
					Status: k8sapi.ConditionTrue,
				},
			},
			expectedCode: pbresource.StatusCode_READY,
		},
		{
			description: "Error status.",
			conditions: []gcciamgalpha.Condition{
				{
					Type:    "Error",
					Status:  k8sapi.ConditionTrue,
					Message: "resource is terminated",
				},
				{
					Type:    "NotReady",
					Status:  k8sapi.ConditionFalse,
					Message: "resource could not be initialized",
				},
			},
			expectedCode: pbresource.StatusCode_NOT_READY,
		},
		{
			description: "Error status when ready is true",
			conditions: []gcciamgalpha.Condition{
				{
					Type:    gcciamgalpha.ReadyConditionType,
					Status:  k8sapi.ConditionFalse,
					Reason:  "UpdateFailed",
					Message: "iam account not found",
				},
				{
					Type:    "Error",
					Status:  k8sapi.ConditionTrue,
					Message: "resource is synced",
				},
			},
			expectedCode: pbresource.StatusCode_NOT_READY,
		},
		{
			description: "Unready status.",
			conditions: []gcciamgalpha.Condition{
				{
					Type:    "NotReady",
					Status:  k8sapi.ConditionTrue,
					Message: "resource is recovering",
				},
			},
			expectedCode: pbresource.StatusCode_NOT_READY,
		},
	}

	for _, tt := range tests {
		s := KccConditionToResourceStatus(tt.conditions)
		assert.Equal(t, s.GetCode(), tt.expectedCode)
	}
}
