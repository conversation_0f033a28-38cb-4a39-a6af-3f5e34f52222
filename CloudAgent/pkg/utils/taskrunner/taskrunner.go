package utils

import (
	"fmt"

	"github.com/risingwavelabs/eris"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/common/resource/task"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

func TaskStatusToProtoTaskStatus(taskStatus *k8s.TaskStatus) (*pbtask.Status, error) {
	switch taskStatus.Code {
	case k8s.TaskStatusFailed:
		return &pbtask.Status{Code: pbtask.StatusCode_FAILED, Message: taskStatus.Message}, nil
	case k8s.TaskStatusSuccess:
		return &pbtask.Status{Code: pbtask.StatusCode_SUCCESS, Message: taskStatus.Message}, nil
	case k8s.TaskStatusRunning:
		return &pbtask.Status{Code: pbtask.StatusCode_RUNNING, Message: taskStatus.Message}, nil
	}
	return nil, eris.New(fmt.Sprintf("unknown task status: %d, message: %s", taskStatus.Code, taskStatus.Message))
}
