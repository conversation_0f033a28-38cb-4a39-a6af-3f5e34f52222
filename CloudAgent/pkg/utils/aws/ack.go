package aws

import (
	"encoding/json"
	"fmt"

	ackv1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	k8sapi "k8s.io/api/core/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/aws"
)

// toAckTag generates a Ack tag from a keyval pair.
// Different AWS Ack controllers maintains their own tag, but shares the same
// JSON representation. Therefore we encode the input into JSON first and then
// unmarshal the message into the target struct. Considering the number of tags
// are usually small(fewer than 10), we think the performance degradation is
// acceptable.
func toAckTag[T any](k, v string) (T, error) {
	var t T
	b := []byte(fmt.Sprintf(`{"key":"%s","value":"%s"}`, k, v))
	err := json.Unmarshal(b, &t)
	return t, err
}

// AckTagsFromTags generates Ack tags from plain AWS tags (string map).
func AckTagsFromTags[T any](tags Tags) ([]*T, error) {
	rtn := []*T{}
	for k, v := range tags {
		t, err := toAckTag[T](k, v)
		if err != nil {
			return nil, err
		}
		rtn = append(rtn, &t)
	}
	return rtn, nil
}

// AckTagsFromTags converts Ack resource conditions into a resource status.
func AckConditionToResourceStatus(conditions []*ackv1alpha1.Condition) *pbresource.Status {
	var refResourceTerminal, refResourceSynced bool
	for _, cond := range conditions {
		if cond.Type == ackv1alpha1.ConditionTypeResourceSynced &&
			cond.Status == k8sapi.ConditionTrue {
			refResourceSynced = true
			continue
		}
		if cond.Type == ackv1alpha1.ConditionTypeTerminal &&
			cond.Status == k8sapi.ConditionTrue {
			refResourceTerminal = true
		}
	}
	if refResourceSynced && !refResourceTerminal {
		return &pbresource.Status{
			Code: pbresource.StatusCode_READY,
		}
	}
	// ACK resource conditions is guaranteed to be marshalled to JSON therefore
	// ignore the error here.
	msgbytes, err := json.Marshal(conditions)
	var msg string
	if err != nil {
		msg = "unable to get detailed conditions in JSON"
	} else {
		msg = string(msgbytes)
	}

	if refResourceTerminal {
		return &pbresource.Status{
			Code:    pbresource.StatusCode_ERROR,
			Message: msg,
		}
	}
	return &pbresource.Status{
		Code:    pbresource.StatusCode_NOT_READY,
		Message: msg,
	}
}

func VPCEndpointStateToProto(status types.State) aws.VPCEndpointStatus {
	// k8s CRD returns lower case so cannot reuse the enum values.
	switch status { //nolint:exhaustive
	case "pendingAcceptance":
		return aws.VPCEndpointStatus_PENDING_ACCEPTANCE
	case "pending":
		return aws.VPCEndpointStatus_PENDING
	case "available":
		return aws.VPCEndpointStatus_AVAILABLE
	case "rejected":
		return aws.VPCEndpointStatus_REJECTED
	case "expired":
		return aws.VPCEndpointStatus_EXPIRED
	case "failed":
		return aws.VPCEndpointStatus_FAILED
	case "deleting":
		return aws.VPCEndpointStatus_DELETING
	case "deleted":
		return aws.VPCEndpointStatus_DELETED
	}
	return aws.VPCEndpointStatus_STATUS_UNSPECIFIED
}
