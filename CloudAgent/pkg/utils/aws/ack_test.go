package aws

import (
	"testing"

	ackec2 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"
	ackv1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8sapi "k8s.io/api/core/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestAckTagsFromTags(t *testing.T) {
	tags, err := AckTagsFromTags[ackec2.Tag](Tags{
		"Foo": "Bar",
	})
	require.NoError(t, err)
	assert.Equal(t, tags, []*ackec2.Tag{
		{Key: utils.Ptr("Foo"), Value: utils.Ptr("Bar")},
	})
}

func TestAckConditionToResourceStatus(t *testing.T) {
	tests := []struct {
		description  string
		conditions   []*ackv1alpha1.Condition
		expectedCode pbresource.StatusCode
	}{
		{
			description: "OK status.",
			conditions: []*ackv1alpha1.Condition{
				{
					Type:   ackv1alpha1.ConditionTypeResourceSynced,
					Status: k8sapi.ConditionTrue,
				},
				{
					Type:   ackv1alpha1.ConditionTypeLateInitialized,
					Status: k8sapi.ConditionTrue,
				},
			},
			expectedCode: pbresource.StatusCode_READY,
		},
		{
			description: "Error status.",
			conditions: []*ackv1alpha1.Condition{
				{
					Type:    ackv1alpha1.ConditionTypeTerminal,
					Status:  k8sapi.ConditionTrue,
					Message: utils.Ptr("resource is terminated"),
				},
				{
					Type:    ackv1alpha1.ConditionTypeLateInitialized,
					Status:  k8sapi.ConditionTrue,
					Message: utils.Ptr("resource is late initialized"),
				},
			},
			expectedCode: pbresource.StatusCode_ERROR,
		},
		{
			description: "Error status when synced is true",
			conditions: []*ackv1alpha1.Condition{
				{
					Type:    ackv1alpha1.ConditionTypeTerminal,
					Status:  k8sapi.ConditionTrue,
					Message: utils.Ptr("resource is terminated"),
				},
				{
					Type:    ackv1alpha1.ConditionTypeResourceSynced,
					Status:  k8sapi.ConditionTrue,
					Message: utils.Ptr("resource is synced"),
				},
			},
			expectedCode: pbresource.StatusCode_ERROR,
		},
		{
			description: "Unready status.",
			conditions: []*ackv1alpha1.Condition{
				{
					Type:    ackv1alpha1.ConditionTypeRecoverable,
					Status:  k8sapi.ConditionTrue,
					Message: utils.Ptr("resource is recovering"),
				},
				{
					Type:    ackv1alpha1.ConditionTypeLateInitialized,
					Status:  k8sapi.ConditionTrue,
					Message: utils.Ptr("resource is late initialized"),
				},
			},
			expectedCode: pbresource.StatusCode_NOT_READY,
		},
	}

	for _, tt := range tests {
		s := AckConditionToResourceStatus(tt.conditions)
		assert.Equal(t, s.GetCode(), tt.expectedCode)
	}
}
