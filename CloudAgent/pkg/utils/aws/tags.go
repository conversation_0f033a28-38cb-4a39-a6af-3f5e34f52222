package aws

import (
	"fmt"
	"maps"
	"strings"
	"time"
)

const (
	TagNamespaceKey = "RWC_Resource_Namespace"
	TagIDKey        = "RWC_Resource_ID"
	TagCreatedKey   = "Created"
	TagProjectKey   = "Project"
	TagEnvKey       = "EnvID"

	TagProjectValue = "RisingWave"
)

type Tags map[string]string

// With appends or overwrites the original tags with multiple tags.
func (t Tags) With(tags ...Tags) Tags {
	for _, m := range tags {
		maps.Copy(t, m)
	}
	return t
}

// <PERSON><PERSON> gets a copy of the tags.
func (t Tags) Clone() Tags {
	rtn := Tags{}
	maps.Copy(rtn, t)
	return rtn
}

func BasicDefaultTags(resourceID, namespace string) Tags {
	return Tags{
		TagProjectKey:   TagProjectValue,
		TagCreatedKey:   time.Now().String(),
		TagIDKey:        resourceID,
		TagNamespaceKey: namespace,
	}
}

// ParseTags parses string representation of tags from config.
func ParseTags(raw []string) (Tags, error) {
	tags := Tags{}
	if len(raw) == 0 {
		return tags, nil
	}
	for _, item := range raw {
		arr := strings.SplitN(item, ":", 2)
		if len(arr) != 2 {
			return nil, fmt.Errorf("invalid tag item: %s", item)
		}
		tags[arr[0]] = arr[1]
	}
	return tags, nil
}
