package aws

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWith(t *testing.T) {
	tags := BasicDefaultTags("id", "ns").With(
		Tags{
			"Project": "test",
		},
		Tags{
			"Foo": "Bar",
		},
	)

	assert.Equal(t, tags["Project"], "test")
	assert.Equal(t, tags["Foo"], "Bar")
	assert.Equal(t, tags["RWC_Resource_ID"], "id")
	assert.Equal(t, tags["RWC_Resource_Namespace"], "ns")
}

func TestParseTags(t *testing.T) {
	var (
		k1 = "test/test-owner-arn"
		v1 = "arn:aws:iam::00000000:user/user_name"

		k2 = "test/test-2"
		v2 = "test-2"
	)
	raw := []string{fmt.Sprintf("%s:%s", k1, v1), fmt.Sprintf("%s:%s", k2, v2)}
	tags, err := ParseTags(raw)
	require.NoError(t, err)

	assert.Equal(t, v1, tags[k1])
	assert.Equal(t, v2, tags[k2])
}

func TestParseTags_empty(t *testing.T) {
	tags, err := ParseTags(nil)
	require.NoError(t, err)

	assert.NotNil(t, tags)
	assert.Empty(t, tags)
}
