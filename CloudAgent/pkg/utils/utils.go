package utils

import (
	"context"
	"math/rand"
	"time"
)

func Ptr[T any](v T) *T {
	return &v
}

// return an empty value if the pointer is nil.
func Unwrap[T any](v *T) T {
	if v == nil {
		var rtn T
		return rtn
	}
	return *v
}

func WithDefault(value string, defaultVal string) string {
	if len(value) == 0 {
		return defaultVal
	}
	return value
}

func CurrentTimeYYYYMMDDHHMMSS() string {
	return time.Now().Format("20060131123156")
}

func RandomLetters(n int) string {
	rtn := []rune{}
	for i := 0; i < n; i++ {
		rtn = append(rtn, rune(rand.Intn(26)+'a'))
	}
	return string(rtn)
}

func Retry(ctx context.Context, f func(fctx context.Context) error, times int, interval time.Duration, exponential bool) error {
	if err := f(ctx); err != nil {
		times--
		if times == 0 {
			return err
		}
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-time.After(interval):
			if exponential {
				return Retry(ctx, f, times, interval*2, exponential)
			} else {
				return Retry(ctx, f, times, interval, exponential)
			}
		}
	}
	return nil
}

func IfElse[T any](cond bool, a T, b T) T {
	if cond {
		return a
	}
	return b
}
