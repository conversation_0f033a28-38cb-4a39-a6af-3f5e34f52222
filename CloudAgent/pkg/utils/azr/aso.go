package azr

import (
	asonetwork "github.com/Azure/azure-service-operator/v2/api/network/v1api20220701"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime/conditions"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/azr"
)

func ASOConditionsToResourceStatus(obj conditions.Conditioner) *pbresource.Status {
	ready := genruntime.GetReadyCondition(obj)
	if ready == nil {
		return &pbresource.Status{
			Code: pbresource.StatusCode_NOT_READY,
		}
	}
	if ready.Status == metav1.ConditionTrue {
		return &pbresource.Status{
			Code: pbresource.StatusCode_READY,
		}
	}
	// Reference: https://github.com/Azure/azure-service-operator/blob/16508133d54f5cc31b5482104cfa97b209458865/docs/hugo/content/design/resource-states.md#the-ready-condition
	if ready.Severity == conditions.ConditionSeverityError {
		return &pbresource.Status{
			Code:    pbresource.StatusCode_ERROR,
			Message: ready.String(),
		}
	}
	return &pbresource.Status{
		Code:    pbresource.StatusCode_NOT_READY,
		Message: ready.String(),
	}
}

func PrivateEndpointStatusToProto(status asonetwork.ApplicationGatewayProvisioningState_STATUS) azr.PrivateEndpointStatus {
	switch status {
	case asonetwork.ApplicationGatewayProvisioningState_STATUS_Deleting:
		return azr.PrivateEndpointStatus_DELETING
	case asonetwork.ApplicationGatewayProvisioningState_STATUS_Failed:
		return azr.PrivateEndpointStatus_FAILED
	case asonetwork.ApplicationGatewayProvisioningState_STATUS_Succeeded:
		return azr.PrivateEndpointStatus_SUCCEEDED
	case asonetwork.ApplicationGatewayProvisioningState_STATUS_Updating:
		return azr.PrivateEndpointStatus_UPDATING
	}
	return azr.PrivateEndpointStatus_STATUS_UNSPECIFIED
}
