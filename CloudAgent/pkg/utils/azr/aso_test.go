package azr

import (
	"testing"

	"github.com/Azure/azure-service-operator/v2/pkg/genruntime/conditions"
	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
)

type TestConditioner struct {
	Conditions []conditions.Condition
}

func (t *TestConditioner) GetConditions() conditions.Conditions {
	return t.Conditions
}

func (t *TestConditioner) SetConditions(conditions conditions.Conditions) {
	t.Conditions = conditions
}

func TestASOConditionsToResourceStatus(t *testing.T) {
	tests := map[string]struct {
		cond         TestConditioner
		expectedCode pbresource.StatusCode
	}{
		"ready": {
			cond: TestConditioner{
				Conditions: []conditions.Condition{
					{
						Type:   conditions.ConditionTypeReady,
						Status: metav1.ConditionTrue,
					},
				},
			},
			expectedCode: pbresource.StatusCode_READY,
		},
		"not ready, nil ready condition": {
			cond:         TestConditioner{},
			expectedCode: pbresource.StatusCode_NOT_READY,
		},
		"not ready, false ready condition": {
			cond: TestConditioner{
				Conditions: []conditions.Condition{
					{
						Type:     conditions.ConditionTypeReady,
						Status:   metav1.ConditionFalse,
						Severity: conditions.ConditionSeverityWarning,
					},
				},
			},
			expectedCode: pbresource.StatusCode_NOT_READY,
		},
		"fail": {
			cond: TestConditioner{
				Conditions: []conditions.Condition{
					{
						Type:     conditions.ConditionTypeReady,
						Status:   metav1.ConditionFalse,
						Severity: conditions.ConditionSeverityError,
					},
				},
			},
			expectedCode: pbresource.StatusCode_ERROR,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			s := ASOConditionsToResourceStatus(&tt.cond)
			assert.Equal(t, s.GetCode(), tt.expectedCode)
		})
	}
}
