package proto

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbcfgk8s "github.com/risingwavelabs/cloudagent/pbgen/config/k8s"
)

func TestEncodeAndDecode(t *testing.T) {
	cfg := &pbcfg.Config{
		K8SConfig: &pbcfgk8s.Config{
			ClusterId: "test_id",
			TaskConfig: &pbcfgk8s.TaskConfig{
				Image: "test_image",
			},
		},
	}

	encoded, err := ToBase64(cfg)
	require.NoError(t, err)
	decoded := &pbcfg.Config{}
	require.NoError(t, FromBase64(encoded, decoded))
	assert.Equal(t, cfg.String(), decoded.String())
}
