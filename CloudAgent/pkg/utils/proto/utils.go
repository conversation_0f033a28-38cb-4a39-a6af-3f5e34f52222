package proto

import (
	b64 "encoding/base64"

	"github.com/risingwavelabs/eris"
	"google.golang.org/protobuf/proto"
)

func FromBase64(encode string, msg proto.Message) error {
	decode, err := b64.StdEncoding.DecodeString(encode)
	if err != nil {
		return eris.Wrapf(err, "failed to decode base64 encoded msg %v", encode)
	}
	err = proto.Unmarshal(decode, msg)
	if err != nil {
		return eris.Wrapf(err, "failed to deserialize proto %v", decode)
	}
	return nil
}

func ToBase64(msg proto.Message) (string, error) {
	serialized, err := proto.Marshal(msg)
	if err != nil {
		return "", eris.Wrapf(err, "failed to serialize msg %v", msg)
	}
	return b64.StdEncoding.EncodeToString(serialized), nil
}
