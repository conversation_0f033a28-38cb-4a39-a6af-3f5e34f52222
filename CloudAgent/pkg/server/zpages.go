package server

import (
	"fmt"
	"net/http"
	"net/http/pprof"

	"github.com/risingwavelabs/cloudagent/pkg/metrics"
)

func NewZpagesServer(port int) *http.Server {
	m := http.NewServeMux()
	// metrics.
	m.Handle("/metricsz", metrics.NewHandler())
	// pprof
	m.HandleFunc("/debug/pprof/", pprof.Index)
	m.HandleFunc("/debug/pprof/cmdline", pprof.Cmdline)
	m.HandleFunc("/debug/pprof/profile", pprof.Profile)
	m.HandleFunc("/debug/pprof/symbol", pprof.Symbol)
	m.HandleFunc("/debug/pprof/trace", pprof.Trace)
	m.Handle("/debug/pprof/goroutine", pprof.Handler("goroutine"))
	m.Handle("/debug/pprof/heap", pprof.Handler("heap"))
	m.<PERSON>le("/debug/pprof/threadcreate", pprof.Handler("threadcreate"))
	m.<PERSON>le("/debug/pprof/block", pprof.Handler("block"))

	addr := fmt.Sprintf(":%d", port)
	srv := &http.Server{Addr: addr}
	srv.Handler = m
	return srv
}
