package server

import (
	"context"
	"fmt"

	"google.golang.org/grpc"
	"google.golang.org/grpc/reflection"

	"github.com/risingwavelabs/eris"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbtelemetrycfg "github.com/risingwavelabs/cloudagent/pbgen/config/telemetry"
	pbsvcagent "github.com/risingwavelabs/cloudagent/pbgen/services/agent"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	pbsvcbyoc "github.com/risingwavelabs/cloudagent/pbgen/services/byoc"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	pbsvcpsql "github.com/risingwavelabs/cloudagent/pbgen/services/psql"
	pbsvcrwc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
	pbsvctask "github.com/risingwavelabs/cloudagent/pbgen/services/task"
	pbsvcprom "github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	loginterceptors "github.com/risingwavelabs/cloudagent/pkg/logger/interceptors"
	providersaws "github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	providersazr "github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	providersbyoc "github.com/risingwavelabs/cloudagent/pkg/providers/byoc"
	providersgcp "github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	providerpsql "github.com/risingwavelabs/cloudagent/pkg/providers/psql"
	"github.com/risingwavelabs/cloudagent/pkg/providers/psql/postgres"
	providersrwc "github.com/risingwavelabs/cloudagent/pkg/providers/rwc"
	providerstask "github.com/risingwavelabs/cloudagent/pkg/providers/task"
	providerstelemetry "github.com/risingwavelabs/cloudagent/pkg/providers/telemetry"
	"github.com/risingwavelabs/cloudagent/pkg/service/azr"
	svcbyoc "github.com/risingwavelabs/cloudagent/pkg/service/byoc"
	svcpsql "github.com/risingwavelabs/cloudagent/pkg/service/psql"
	svcrwc "github.com/risingwavelabs/cloudagent/pkg/service/rwc"
	svctask "github.com/risingwavelabs/cloudagent/pkg/service/task"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/metrics"
	"github.com/risingwavelabs/cloudagent/pkg/service/agent"
	"github.com/risingwavelabs/cloudagent/pkg/service/aws"
	"github.com/risingwavelabs/cloudagent/pkg/service/gcp"
	servicek8s "github.com/risingwavelabs/cloudagent/pkg/service/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/service/telemetry"
)

func NewAgent(ctx context.Context, config *pbcfg.Config) (*grpc.Server, error) {
	log := logger.NewLogAgent("agent")
	ctx = logger.WithCtx(ctx, log)
	// init Kubernetes client
	k8sClient, err := k8s.InitDefaultClient(config)
	if err != nil {
		return nil, eris.WithCode(
			eris.Wrapf(err, "failed to init K8s client: %v", config.GetK8SConfig()),
			eris.CodeFailedPrecondition,
		)
	}
	s := grpc.NewServer(
		grpc.ChainUnaryInterceptor(
			loginterceptors.UnaryServerInterceptor(),
			metrics.NewGRPCUnaryInterceptor(),
		),
		grpc.ChainStreamInterceptor(
			loginterceptors.StreamServerInterceptor(),
			metrics.NewGRPCStreamInterceptor(),
		),
	)

	// init rwc service
	rwcProvider, err := providersrwc.NewProvider(providersrwc.NewProviderOption{
		Kc: k8sClient,
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to create rwc provider")
	}
	pbsvcrwc.RegisterRisingwaveControlServer(s, svcrwc.NewService(rwcProvider))

	// init byoc service
	byocProvider, err := providersbyoc.NewProvider(k8sClient)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create byoc provider")
	}
	pbsvcbyoc.RegisterByocResourceManagerServer(s, svcbyoc.NewService(byocProvider))

	// init task service
	taskProvider, err := providerstask.NewProvider(providerstask.NewProviderOption{
		Kc: k8sClient,
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to create task provider")
	}
	pbsvctask.RegisterTaskManagerServer(s, svctask.NewService(taskProvider))
	// init agent service
	pbsvcagent.RegisterAgentServer(s, agent.NewService())
	k8sService, err := servicek8s.NewService(k8sClient, config.GetK8SConfig().GetEndpoint(), config.GetK8SConfig().GetCaCertificateBase64())
	if err != nil {
		return nil, eris.Wrap(err, "failed to start K8s server")
	}
	pbsvck8s.RegisterK8SResourceManagerServer(s, k8sService)
	var awsProvider *providersaws.Provider
	var gcpProvider *providersgcp.Provider
	var azrProvider *providersazr.Provider
	switch clusterType := config.GetCloudProviderConfig().(type) {
	case *pbcfg.Config_AwsConfig:
		awsProvider, err = providersaws.NewProvider(ctx, providersaws.NewProviderOption{
			Kc:        k8sClient,
			AWSConfig: config.GetAwsConfig(),
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to create AWS provider")
		}
		awsService, err := aws.NewService(aws.NewServiceOption{
			Provider:     awsProvider,
			AccoutID:     config.GetAwsConfig().GetAccountId(),
			OIDCPrivider: config.GetAwsConfig().GetOidcProvider(),
			Region:       config.GetAwsConfig().GetRegion(),
			VPCID:        config.GetAwsConfig().GetVpcId(),
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to start AWS server")
		}
		pbsvcaws.RegisterAwsResourceManagerServer(s, awsService)
	case *pbcfg.Config_GcpConfig:
		gcpProvider, err = providersgcp.NewProvider(ctx, providersgcp.NewProviderOption{
			Kc:        k8sClient,
			GCPConfig: config.GetGcpConfig(),
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to create GCP provider")
		}
		gcpService, err := gcp.NewService(gcp.NewServiceOption{
			Provider:  gcpProvider,
			ProjectID: config.GetGcpConfig().GetProjectId(),
			Region:    config.GetGcpConfig().GetRegion(),
			TenantVPC: config.GetGcpConfig().GetTenantVpc(),
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to start GCP server")
		}
		pbsvcgcp.RegisterGcpResourceManagerServer(s, gcpService)
	case *pbcfg.Config_AzrConfig:
		azrProvider, err = providersazr.NewProvider(providersazr.NewProviderOption{
			Kc:        k8sClient,
			AZRConfig: config.GetAzrConfig(),
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to create AZR provider")
		}
		azrService, err := azr.NewService(azr.NewServiceOption{
			Provider:       azrProvider,
			SubscriptionID: config.GetAzrConfig().GetSubscriptionId(),
			ResourceGroup:  config.GetAzrConfig().GetResourceGroup(),
			Location:       config.GetAzrConfig().GetLocation(),
			OIDCIssuer:     config.GetAzrConfig().GetOidcIssuer(),
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to create AZR service")
		}
		pbsvcazr.RegisterAzrResourceManagerServer(s, azrService)
	case *pbcfg.Config_LocalConfig:
		break
	default:
		return nil, fmt.Errorf("invalid cluster type: %v", clusterType)
	}

	// will create a pod lister.
	podLister, err := k8s.NewPodLister(k8sClient)
	if err != nil {
		return nil, eris.WithCode(
			eris.Wrapf(err, "failed to init k8s pod lister: %v", config.GetK8SConfig()),
			eris.CodeFailedPrecondition,
		)
	}
	// init the pod lister, and wait ready.
	podLister.WaitReady(ctx)
	puller, err := providerstelemetry.NewMetricsPuller(providerstelemetry.NewMetricsPullerOption{
		PodLister: podLister,
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize metrics puller")
	}

	var promProvider providerstelemetry.PrometheusProvider
	metricsCfg := config.GetTelemetryConfig().GetMetricsConfig()
	switch metricsCfg.(type) {
	case *pbtelemetrycfg.Config_AmpConfig:
		promProvider, err = providerstelemetry.NewAMPPrometheusProvider(config.GetAwsConfig())
	case *pbtelemetrycfg.Config_GmpConfig:
		promProvider, err = providerstelemetry.NewGMPPrometheusProvider(ctx, config.GetGcpConfig())
	case *pbtelemetrycfg.Config_AzmpConfig:
		promProvider, err = providerstelemetry.NewAzMPProvider(config.GetAzrConfig())
	case *pbtelemetrycfg.Config_LocalPrometheusConfig:
		promProvider, err = providerstelemetry.NewLocalPrometheusProvider()
	default:
		log.Info("no metrics config detected, will not initialize metrics service")
	}
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize prometheus provider")
	}
	if promProvider == nil && config.GetTelemetryConfig().GetMetricsConfig() != nil {
		return nil, fmt.Errorf("cannot initialize provider from metrics config: %v", metricsCfg)
	}

	promService, err := telemetry.NewPrometheusService(telemetry.NewPrometheusServiceOption{
		Provider: promProvider,
		Puller:   puller,
		Config:   config,
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to start prometheus server")
	}
	pbsvcprom.RegisterPrometheusServer(s, promService)

	psqlService, err := svcpsql.NewService(providerpsql.NewProvider(postgres.NewProvider(), k8sClient))
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize psql service")
	}
	pbsvcpsql.RegisterPsqlManagerServer(s, psqlService)

	reflection.Register(s)
	return s, nil
}

func GracefulStopAgent(ctx context.Context, s *grpc.Server) error {
	ok := make(chan struct{})
	// GracefulStop will return instantly
	// when Stop it called, preventing this
	// goroutine from leaking.
	go func() {
		s.GracefulStop()
		close(ok)
	}()

	select {
	case <-ok:
		return nil
	case <-ctx.Done():
		s.Stop()
		return ctx.Err()
	}
}
