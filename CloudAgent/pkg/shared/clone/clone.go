package clone

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/utils"

	"github.com/risingwavelabs/eris"
)

type ObjectStoreClient[CopyReq any, ListReq any] interface {
	CopyObject(ctx context.Context, req CopyReq) error
	ListObjectsWithMarker(ctx context.Context, req ListReq) ([]string, string, error)
}

type CopyRequestBuilder[CopyReq any] func(srcObj, dstObj string) CopyReq
type ListRequestBuilder[ListReq any] func() ListReq

const (
	samplingRate             = 5
	cloneParallelism         = 10
	cloneObjectRetry         = 5
	cloneObjectRetryInterval = time.Second * 1
)

type Options struct {
	TaskID                   string
	TaskNamespace            string
	SourceDirectoryName      string
	SourceBucketName         string
	DestinationDirectoryName string
	DestinationBucketName    string
	Cursor                   string
	CloneSize                int32
}

func DataDirectoryInner[
	CopyReq any,
	ListReq any,
](
	ctx context.Context,
	client ObjectStoreClient[CopyReq, ListReq],
	options Options,
	buildCopyReq CopyRequestBuilder[CopyReq],
	buildListReq ListRequestBuilder[ListReq],
) error {
	var cloneErrors []error
	var mu sync.Mutex
	var successCopy, failedCopy int
	copyJobs := make(chan string, options.CloneSize)

	// Worker pool
	var wg sync.WaitGroup
	for i := 0; i < cloneParallelism; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for srcObj := range copyJobs {
				dstObj, err := createDstObjKey(srcObj, options.SourceDirectoryName, options.DestinationDirectoryName)
				if err != nil {
					mu.Lock()
					if failedCopy%samplingRate == 0 {
						cloneErrors = append(cloneErrors, err)
					}
					failedCopy++
					mu.Unlock()
					continue
				}
				copyReq := buildCopyReq(srcObj, dstObj)
				err = utils.Retry(
					ctx,
					func(fctx context.Context) error {
						if err := client.CopyObject(fctx, copyReq); err != nil {
							logger.FromCtx(ctx).Warn(err.Error())
							return err
						}
						return nil
					},
					cloneObjectRetry,
					cloneObjectRetryInterval,
					true)
				if err == nil {
					mu.Lock()
					successCopy++
					mu.Unlock()
				} else {
					mu.Lock()
					if failedCopy%samplingRate == 0 {
						cloneErrors = append(cloneErrors, err)
					}
					failedCopy++
					mu.Unlock()
				}
			}
		}()
	}

	// Enqueue jobs - add objectname to be copied into channel
	objects, resCursor, err := client.ListObjectsWithMarker(ctx, buildListReq())
	if err != nil {
		return eris.Wrapf(err, "could not list objects to be cloned")
	}
	for _, obj := range objects {
		copyJobs <- obj
	}

	close(copyJobs)
	wg.Wait()

	// Summary output
	logger.FromCtx(ctx).Info("===== COPY SUMMARY =====")
	logger.FromCtx(ctx).Infof("Total successful copies: %d", successCopy)
	logger.FromCtx(ctx).Infof("Total failed copies: %d", failedCopy)
	logger.FromCtx(ctx).Infof("Errors: %v", cloneErrors)
	logger.FromCtx(ctx).Infof("Next cursor: %v", resCursor)

	if failedCopy > 0 {
		return eris.Wrapf(eris.Join(cloneErrors...), "failed to clone %d objects", failedCopy)
	}
	return nil
}

func createDstObjKey(srcObjKey, srcDir, dstDir string) (string, error) {
	if !strings.Contains(srcObjKey, srcDir) {
		return "", eris.New(fmt.Sprintf("invalid input, source directory %v must be part of object path %v", srcDir, srcObjKey))
	}
	srcDir = strings.ReplaceAll(srcDir, "/", "")
	dstDir = strings.ReplaceAll(dstDir, "/", "")
	return strings.ReplaceAll(srcObjKey, srcDir, dstDir), nil
}
