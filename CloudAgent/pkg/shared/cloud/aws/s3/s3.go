package s3

import (
	"context"
	"io"
	"net/url"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	awsconfig "github.com/aws/aws-sdk-go-v2/config"
	awscredentials "github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/shared/cloud/aws/credentials"
)

type Bucket struct {
	Bucket string `json:"bucket"`
	Prefix string `json:"prefix"`
	Key    string `json:"key"`
	Region string `json:"region"`
}

func ReadObject(accessKey credentials.AccessKey, bucket Bucket) ([]byte, error) {
	client, err := getS3Client(accessKey, bucket.Region)
	if err != nil {
		return nil, eris.Wrap(err, "failed to get s3 client")
	}

	object, err := client.GetObject(context.TODO(), &s3.GetObjectInput{
		Bucket: aws.String(bucket.Bucket),
		Key:    aws.String(bucket.Key),
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to get s3 object")
	}

	bodyBytes, err := io.ReadAll(object.Body)
	defer func() { _ = object.Body.Close() }()
	if err != nil {
		return nil, eris.Wrap(err, "failed to read object body")
	}
	return bodyBytes, nil
}

// ParseS3Url parse url starts with 's3://'.
func ParseS3Url(s3url string) (Bucket, error) {
	u, err := url.Parse(s3url)
	if err != nil {
		return Bucket{}, eris.Wrap(err, "failed to parse s3 url")
	}
	if strings.ToLower(u.Scheme) != "s3" {
		return Bucket{}, eris.New("url is not s3 scheme")
	}
	bucket := u.Host
	key := strings.TrimPrefix(u.Path, "/")
	return Bucket{
		Bucket: bucket,
		Key:    key,
	}, nil
}

func getS3Client(accessKey credentials.AccessKey, region string) (client *s3.Client, err error) {
	var ops []func(*awsconfig.LoadOptions) error

	if accessKey == credentials.AnonymousAccessKey {
		ops = append(ops, awsconfig.WithCredentialsProvider(aws.AnonymousCredentials{}))
	} else {
		ops = append(ops, awsconfig.WithCredentialsProvider(
			awscredentials.NewStaticCredentialsProvider(accessKey.AccessKeyID, accessKey.SecretAccessKey, ""),
		))
	}
	if region != "" {
		ops = append(ops, awsconfig.WithRegion(region))
	}

	cfg, err := awsconfig.LoadDefaultConfig(
		context.TODO(),
		ops...,
	)
	if err != nil {
		return nil, eris.Wrap(err, "failed to load AWS config")
	}

	if accessKey == credentials.AnonymousAccessKey {
		return s3.NewFromConfig(cfg, func(options *s3.Options) {
			options.Credentials = aws.AnonymousCredentials{}
		}), nil
	}
	return s3.NewFromConfig(cfg), nil
}
