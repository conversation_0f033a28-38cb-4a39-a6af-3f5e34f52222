package httpreq

import (
	"context"
	"io"
	"net/http"

	"github.com/risingwavelabs/eris"
)

const (
	HTTPCodeKey = "http_code"
)

func Get(ctx context.Context, url string, modifier ...func(*http.Request)) ([]byte, error) {
	client := &http.Client{}
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, url, nil)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create request")
	}
	for _, m := range modifier {
		m(req)
	}

	rsp, err := client.Do(req)
	if err != nil {
		return nil, eris.Wrap(err, "failed to get web location")
	}
	bodyBytes, err := io.ReadAll(rsp.Body)
	defer func() { _ = rsp.Body.Close() }()
	if err != nil {
		return nil, eris.Wrap(err, "failed to read response body")
	}

	statusCode := rsp.StatusCode
	if statusCode < 200 || statusCode > 299 {
		return nil, eris.Errorf("failed to get web location, status code: %d, body: %s", statusCode, bodyBytes).
			WithCode(eris.CodeInternal).
			WithProperty(HTTPCodeKey, statusCode)
	}
	return bodyBytes, nil
}
