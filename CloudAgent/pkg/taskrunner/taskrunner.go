package taskrunner

import (
	"context"
	"encoding/json"
	"fmt"

	"github.com/pkg/errors"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	"github.com/risingwavelabs/cloudagent/pkg/helmx"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	providersaws "github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	providersazr "github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	providersbyoc "github.com/risingwavelabs/cloudagent/pkg/providers/byoc"
	providersgcp "github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/risectl"
)

const (
	AWS = "aws"
	GCP = "gcp"
)

type TaskRunner struct {
	executor risectl.Executor
	aws      *providersaws.Provider
	gcp      *providersgcp.Provider
	azr      *providersazr.Provider
	byoc     *providersbyoc.Provider
	helm     helmx.ServiceInterface
	config   *pbcfg.Config
	log      *logger.LogAgent
}

func InitTaskRunner(ctx context.Context, config *pbcfg.Config) (*TaskRunner, error) {
	k8sClient, err := k8s.NewClient(config)
	if err != nil {
		return nil, eris.Wrap(err, "failed to init k8s client")
	}

	taskRunner := &TaskRunner{
		executor: risectl.NewMetaNodeExecutor(k8sClient),
		config:   config,
		log:      logger.NewLogAgent("task runner"),
	}

	byocProvider, err := providersbyoc.NewProvider(k8sClient)
	if err != nil {
		return nil, eris.Wrap(err, "failed to init byoc provider")
	}
	taskRunner.byoc = byocProvider

	helmSvc, err := helmx.NewService(k8sClient.GetRestCfg(), config.GetK8SConfig().GetAllowHelmCharts())
	if err != nil {
		return nil, errors.Wrap(err, "failed to init helm service")
	}
	taskRunner.helm = helmSvc

	switch clusterType := config.GetCloudProviderConfig().(type) {
	case *pbcfg.Config_AwsConfig:
		awsProvider, err := providersaws.NewProvider(ctx, providersaws.NewProviderOption{
			Kc:        k8sClient,
			AWSConfig: config.GetAwsConfig(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to create AWS provider")
		}
		taskRunner.aws = awsProvider
	case *pbcfg.Config_GcpConfig:
		gcpProvider, err := providersgcp.NewProvider(ctx, providersgcp.NewProviderOption{
			Kc:        k8sClient,
			GCPConfig: config.GetGcpConfig(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to create GCP provider")
		}
		taskRunner.gcp = gcpProvider
	case *pbcfg.Config_AzrConfig:
		azrProvider, err := providersazr.NewProvider(providersazr.NewProviderOption{
			Kc:        k8sClient,
			AZRConfig: config.GetAzrConfig(),
		})
		if err != nil {
			return nil, errors.Wrap(err, "failed to create AZR provider")
		}
		taskRunner.azr = azrProvider
	case *pbcfg.Config_LocalConfig:
		break
	default:
		return nil, fmt.Errorf("invalid cluster type: %v", clusterType)
	}
	return taskRunner, nil
}

func (r *TaskRunner) Run(ctx context.Context, taskSpec *pbtask.Task) error {
	switch taskType := taskSpec.GetTask().(type) {
	case *pbtask.Task_RisectlTask:
		result, err := r.executor.RunRisectlCommand(ctx, risectl.CommandOption{
			RwNamespace: taskSpec.GetRisectlTask().GetRisingwaveNamespace(),
			RwName:      taskSpec.GetRisectlTask().GetRisingwaveName(),
			Cmd:         taskSpec.GetRisectlTask().GetArgs(),
		})
		if err != nil {
			return err
		}
		if _, err := fmt.Println(result); err != nil {
			r.log.Warnf("failed to write ridectl command output to stdout: %s", err.Error())
		}
		return nil
	case *pbtask.Task_GcsDirectoryCleanUpTask:
		option := providersgcp.DeleteDirectoryOptions{
			DirectoryName: taskSpec.GetGcsDirectoryCleanUpTask().GetDirectory(),
			BucketName:    taskSpec.GetGcsDirectoryCleanUpTask().GetBucket(),
		}

		err := r.gcp.DeleteDataDirectory(ctx, option)
		if err != nil {
			return err
		}
		return nil
	case *pbtask.Task_AzrDirectoryCleanUpTask:
		task := taskSpec.GetAzrDirectoryCleanUpTask()
		err := r.azr.DeleteDataDirectory(ctx, task.GetStorageAccount(), task.GetContainer(), task.GetDirectory())
		if err != nil {
			return err
		}
		return nil
	case *pbtask.Task_AwsDirectoryCleanUpTask:
		task := taskSpec.GetAwsDirectoryCleanUpTask()
		return r.aws.DeleteDataDirectory(ctx, task.GetBucket(), task.GetDirectory())
	case *pbtask.Task_InstallHelmReleaseTask:
		installTask := taskSpec.GetInstallHelmReleaseTask()
		values := map[string]any{}
		if err := json.Unmarshal([]byte(installTask.GetValuesJson()), &values); err != nil {
			return errors.Wrap(err, "failed to parse values json")
		}
		return r.helm.Install(ctx, helmx.InstallOption{
			ReleaseName: installTask.GetReleaseName(),
			Namespace:   installTask.GetReleaseNamespace(),
			ChartURL:    installTask.GetChartUrl(),
			Values:      values,
		})
	case *pbtask.Task_UpgradeHelmReleaseTask:
		upgradeTask := taskSpec.GetUpgradeHelmReleaseTask()
		values := map[string]any{}
		if err := json.Unmarshal([]byte(upgradeTask.GetValuesJson()), &values); err != nil {
			return errors.Wrap(err, "failed to parse values json")
		}
		return r.helm.Upgrade(ctx, helmx.UpgradeOption{
			ReleaseName: upgradeTask.GetReleaseName(),
			Namespace:   upgradeTask.GetReleaseNamespace(),
			ChartURL:    upgradeTask.GetChartUrl(),
			Values:      values,
			Install:     upgradeTask.GetInstall(),
		})
	case *pbtask.Task_UninstallHelmReleaseTask:
		uninstallTask := taskSpec.GetUninstallHelmReleaseTask()
		return r.helm.Uninstall(ctx, helmx.UninstallOption{
			ReleaseName: uninstallTask.GetReleaseName(),
			Namespace:   uninstallTask.GetReleaseNamespace(),
		})
	case *pbtask.Task_AwsSimpleDataReplicationTask:
		task := taskSpec.GetAwsSimpleDataReplicationTask()
		return r.aws.SimpleDataReplication(ctx, task.GetSourceBucket(), task.GetSourceDirectory(), task.GetSinkBucket(), task.GetSinkDirectory())
	case *pbtask.Task_ApplyByocModuleTask:
		task := taskSpec.GetApplyByocModuleTask()
		options := providersbyoc.ApplyModuleOptions{
			ApplyOptions:   task.GetApplyOptions(),
			ModuleOptions:  task.GetModuleOptions(),
			PackageOptions: task.GetPackageOptions(),
		}
		return r.byoc.ApplyByocModule(ctx, &options)
	case *pbtask.Task_RetrieveByocModuleOutputTask:
		task := taskSpec.GetRetrieveByocModuleOutputTask()
		options := providersbyoc.RetrieveModuleOutputOptions{
			OutputKey:      task.GetOutputKey(),
			ModuleOptions:  task.GetModuleOptions(),
			PackageOptions: task.GetPackageOptions(),
		}
		return r.byoc.RetrieveByocModuleOutput(ctx, &options)
	case *pbtask.Task_AwsDirectoryCloneTask:
		task := taskSpec.GetAwsDirectoryCloneTask()
		options := clone.Options{
			SourceDirectoryName:      task.GetSourceDirectoryName(),
			SourceBucketName:         task.GetSourceBucketName(),
			DestinationDirectoryName: task.GetDestinationDirectoryName(),
			DestinationBucketName:    task.GetDestinationBucketName(),
			Cursor:                   task.GetCursor(),
			CloneSize:                task.GetCloneSize(),
		}
		return r.aws.CloneDataDirectory(ctx, options)
	case *pbtask.Task_GcpDirectoryCloneTask:
		task := taskSpec.GetGcpDirectoryCloneTask()
		options := clone.Options{
			SourceDirectoryName:      task.GetSourceDirectoryName(),
			SourceBucketName:         task.GetSourceBucketName(),
			DestinationDirectoryName: task.GetDestinationDirectoryName(),
			DestinationBucketName:    task.GetDestinationBucketName(),
			Cursor:                   task.GetCursor(),
			CloneSize:                task.GetCloneSize(),
		}
		return r.gcp.CloneDataDirectory(ctx, options)
	case *pbtask.Task_AzrDirectoryCloneTask:
		task := taskSpec.GetAzrDirectoryCloneTask()
		options := clone.Options{
			SourceDirectoryName:      task.GetSourceDirectoryName(),
			SourceBucketName:         task.GetSourceContainerName(),
			DestinationDirectoryName: task.GetDestinationDirectoryName(),
			DestinationBucketName:    task.GetDestinationContainerName(),
			Cursor:                   task.GetCursor(),
			CloneSize:                task.GetCloneSize(),
		}
		return r.azr.CloneDataDirectory(ctx, task.GetSourceStorageAccount(), task.GetDestinationStorageAccount(), options)
	default:
		return fmt.Errorf("invalid task type: %v", taskType)
	}
}
