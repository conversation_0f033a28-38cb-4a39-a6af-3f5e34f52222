package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

var namespace = "cloudagent"
var subsystem = "http"

var HTTPRequestTotal = prometheus.NewCounterVec(prometheus.CounterOpts{
	Namespace: namespace,
	Subsystem: subsystem,
	Name:      "request_total",
	Help:      "The sent request number.",
}, []string{"host", "method", "code"})

var HTTPRequestDurationSeconds = prometheus.NewHistogramVec(prometheus.HistogramOpts{
	Namespace: namespace,
	Subsystem: subsystem,
	Name:      "request_duration_seconds",
	Help:      "The time client took to handle the request.",
	Buckets:   []float64{.005, .01, .025, .05, .1, .25, .5, 1, 2.5, 5, 10},
}, []string{"host", "method"})
