package metrics

import (
	"net/http"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	k8smetrics "sigs.k8s.io/controller-runtime/pkg/metrics"
)

func NewGRPCUnaryInterceptor() grpc.UnaryServerInterceptor {
	return grpcMetrics.UnaryServerInterceptor()
}

func NewGRPCStreamInterceptor() grpc.StreamServerInterceptor {
	return grpcMetrics.StreamServerInterceptor()
}

func NewHandler() http.Handler {
	reg := prometheus.NewRegistry()
	/* ----Register all collectors here --- */
	// gRPC metrics
	reg.MustRegister(grpcMetrics)
	// k8s API call metrics
	reg.MustRegister(k8smetrics.Registry.(*prometheus.Registry))

	// http metrics
	reg.MustRegister(HTTPRequestTotal)
	reg.MustRegister(HTTPRequestDurationSeconds)

	return promhttp.HandlerFor(
		reg,
		promhttp.HandlerOpts{
			EnableOpenMetrics: true,
		},
	)
}
