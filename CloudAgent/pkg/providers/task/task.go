package task

import (
	"context"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

type Provider struct {
	kc *k8s.KubernetesClient
}

type NewProviderOption struct {
	Kc *k8s.KubernetesClient
}

func NewProvider(option NewProviderOption) (*Provider, error) {
	if option.Kc == nil {
		return nil, eris.New("Kubernetes client cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	return &Provider{
		kc: option.Kc,
	}, nil
}

type Meta struct {
	ResourceID string
	Namespace  string
}

func (p *Provider) GetTaskStatus(ctx context.Context, option Meta) (*k8s.TaskStatus, error) {
	return p.kc.GetTaskStatus(ctx, option.ResourceID, option.Namespace)
}

func (p *Provider) CleanupTask(ctx context.Context, option Meta) error {
	return p.kc.CleanupTask(ctx, option.ResourceID, option.Namespace)
}
