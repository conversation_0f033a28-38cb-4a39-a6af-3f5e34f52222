package task

import (
	"context"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCleanupTask_normal(t *testing.T) {
	var (
		jobID        = "job-id"
		jobNamespace = "job-ns"
	)
	p, err := NewProvider(NewProviderOption{
		Kc: &k8s.KubernetesClient{
			Client: fake.NewClient(
				&batchv1.Job{
					ObjectMeta: metav1.ObjectMeta{
						Name:      jobID,
						Namespace: jobNamespace,
					},
				},
			),
			Interface: fake.NewClientset(),
			TaskConfig: taskconfig.Config{
				Namespace: jobNamespace,
			},
		},
	})
	require.NoError(t, err)

	err = p.CleanupTask(context.Background(), Meta{
		Namespace:  jobNamespace,
		ResourceID: jobID,
	})
	assert.NoError(t, err)
}

func TestCleanupMetaBackupTask_notFound(t *testing.T) {
	var (
		jobID        = "job-id"
		jobNamespace = "job-ns"
	)
	p, err := NewProvider(NewProviderOption{
		Kc: &k8s.KubernetesClient{
			Client:    fake.NewClient(),
			Interface: fake.NewClientset(),
			TaskConfig: taskconfig.Config{
				Namespace: jobNamespace,
			},
		},
	})
	require.NoError(t, err)

	err = p.CleanupTask(context.Background(), Meta{
		ResourceID: jobID,
	})
	assert.True(t, utils.IsErrNotFound(err))
}

type testGMBItem struct {
	job       *batchv1.Job
	jobPod    *corev1.Pod
	getOption Meta
}

type _testGetTaskExpected struct {
	errCode        eris.Code
	taskStatusCode *k8s.TaskStatus
}

var getTestData = []struct {
	item     testGMBItem
	expected _testGetTaskExpected
}{
	// not found
	{
		item: testGMBItem{
			getOption: Meta{
				Namespace:  "job-ns",
				ResourceID: "job-id",
			},
		},
		expected: _testGetTaskExpected{
			errCode:        eris.CodeNotFound,
			taskStatusCode: nil,
		},
	},
	// found running task
	{
		item: testGMBItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "job-ns",
					Name:      "job-id",
				},
			},
			jobPod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "job-ns",
					Name:      "pod-name",
					Labels: map[string]string{
						batchv1.JobNameLabel: "job-id",
					},
				},
				Status: corev1.PodStatus{
					ContainerStatuses: []corev1.ContainerStatus{
						{
							State: corev1.ContainerState{
								Running: &corev1.ContainerStateRunning{},
							},
						},
					},
				},
			},
			getOption: Meta{
				Namespace:  "job-ns",
				ResourceID: "job-id",
			},
		},
		expected: _testGetTaskExpected{
			errCode: 0,
			taskStatusCode: &k8s.TaskStatus{
				Code: k8s.TaskStatusRunning,
			},
		},
	},
	// found failed task
	{
		item: testGMBItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "job-ns",
					Name:      "job-id",
				},
				Status: batchv1.JobStatus{
					Conditions: []batchv1.JobCondition{
						{
							Status: corev1.ConditionTrue,
							Type:   batchv1.JobFailed,
						},
					},
				},
			},
			jobPod: &corev1.Pod{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "job-ns",
					Name:      "pod-name",
					Labels: map[string]string{
						batchv1.JobNameLabel: "job-id",
					},
				},
				Status: corev1.PodStatus{
					ContainerStatuses: []corev1.ContainerStatus{
						{
							State: corev1.ContainerState{
								Terminated: &corev1.ContainerStateTerminated{},
							},
						},
					},
				},
			},
			getOption: Meta{
				Namespace:  "job-ns",
				ResourceID: "job-id",
			},
		},
		expected: _testGetTaskExpected{
			errCode: 0,
			taskStatusCode: &k8s.TaskStatus{
				Code: k8s.TaskStatusFailed,
			},
		},
	},
	// found success task
	{
		item: testGMBItem{
			job: &batchv1.Job{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "job-ns",
					Name:      "job-id",
				},
				Status: batchv1.JobStatus{
					Conditions: []batchv1.JobCondition{
						{
							Status: corev1.ConditionTrue,
							Type:   batchv1.JobComplete,
						},
					},
				},
			},
			getOption: Meta{
				Namespace:  "job-ns",
				ResourceID: "job-id",
			},
		},
		expected: _testGetTaskExpected{
			errCode: 0,
			taskStatusCode: &k8s.TaskStatus{
				Code: k8s.TaskStatusSuccess,
			},
		},
	},
}

func TestGetTaskStatus(t *testing.T) {
	for idx, data := range getTestData {
		t.Logf("running test %d", idx)
		fakeClient := fake.NewClient()
		fakeClienset := fake.NewClientset()
		if data.item.job != nil {
			fakeClient = fake.NewClient(data.item.job)
		}
		if data.item.jobPod != nil {
			fakeClienset = fake.NewClientset(data.item.jobPod)
		}
		p, err := NewProvider(NewProviderOption{
			Kc: &k8s.KubernetesClient{
				Client:    fakeClient,
				Interface: fakeClienset,
				TaskConfig: taskconfig.Config{
					Namespace: data.item.getOption.Namespace,
				},
			},
		})
		require.NoError(t, err)

		status, err := p.GetTaskStatus(context.Background(), data.item.getOption)
		if data.expected.errCode != 0 {
			assert.Equal(t, data.expected.errCode, eris.GetCode(err))
		} else {
			assert.Equal(t, data.expected.taskStatusCode.Code, status.Code)
		}
	}
}
