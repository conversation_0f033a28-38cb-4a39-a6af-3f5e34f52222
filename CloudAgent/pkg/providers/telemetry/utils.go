package telemetry

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	dto "github.com/prometheus/client_model/go"
	"github.com/prometheus/common/expfmt"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/utils/ptr"

	"github.com/risingwavelabs/cloudagent/pkg/metrics"
)

var defaultClient = &http.Client{
	Timeout: time.Second * 15,
}

func doRequest(ctx context.Context, url string) (io.ReadCloser, error) {
	req, err := http.NewRequest(http.MethodGet, url, nil)
	if err != nil {
		return nil, err
	}
	req.Header.Set(acceptEncodingHeaderKey, encodingGzip)
	var start = time.Now()

	res, err := defaultClient.Do(req.WithContext(ctx))
	if err != nil {
		return nil, err
	}

	// to record the metrics.
	metrics.HTTPRequestTotal.With(prometheus.Labels{
		"host":   req.Host,
		"method": req.Method,
		"code":   strconv.Itoa(res.StatusCode),
	}).Inc()
	metrics.HTTPRequestDurationSeconds.With(prometheus.Labels{
		"host":   req.Host,
		"method": req.Method,
	}).Observe(time.Since(start).Seconds())

	if res.Header.Get(contentEncodingHeaderKey) == encodingGzip {
		gr, err := gzip.NewReader(res.Body)
		if err != nil {
			return nil, eris.Wrap(err, "failed to create gzip reader")
		}
		return gr, nil
	}

	return res.Body, nil
}

func endpoint(pod *corev1.Pod) (string, error) {
	if isRisingwavePod(pod) {
		return parseRisingwaveEndpoint(pod)
	}
	if isEtcdPod(pod) {
		return parseETCDEndpoint(pod)
	}
	return "", eris.New(fmt.Sprintf("cannot find the component labels, pod: [%s/%s]", pod.Namespace, pod.Name))
}

func urlWithFilter(endpoint string, include []string, exclude []string) (string, error) {
	if len(include) > 0 && len(exclude) > 0 {
		return "", eris.Errorf("`include` and `exclude` should not be used together").WithCode(eris.CodeInvalidArgument)
	}
	if len(include) > 0 {
		includeParams := url.Values{}
		for _, condition := range include {
			includeParams.Add("include", condition)
		}
		endpoint += "?" + includeParams.Encode()
	}

	if len(exclude) > 0 {
		excludeParams := url.Values{}
		for _, condition := range exclude {
			excludeParams.Add("exclude", condition)
		}
		endpoint += "?" + excludeParams.Encode()
	}
	return endpoint, nil
}

func parseRisingwaveEndpoint(pod *corev1.Pod) (string, error) {
	for _, container := range pod.Spec.Containers {
		for _, port := range container.Ports {
			if port.Name == "metrics" {
				return fmt.Sprintf("http://%s:%d/metrics", pod.Status.PodIP, port.ContainerPort), nil
			}
		}
	}
	return "", eris.New(fmt.Sprintf("failed to parse risingwave pod: [%s/%s]", pod.Namespace, pod.Name))
}

func parseETCDEndpoint(pod *corev1.Pod) (string, error) {
	for _, container := range pod.Spec.Containers {
		for _, port := range container.Ports {
			if port.Name == "client" {
				return fmt.Sprintf("http://%s:%d/metrics", pod.Status.PodIP, port.ContainerPort), nil
			}
		}
	}
	return "", eris.New(fmt.Sprintf("failed to parse etcd pod: [%s/%s], not find the port", pod.Namespace, pod.Name))
}

func isRisingwavePod(pod *corev1.Pod) bool {
	if len(pod.Labels) == 0 {
		return false
	}
	v, e := pod.Labels[RisingWaveComponentName]
	if !e {
		return false
	}

	return v != string(ETCDType)
}

func isEtcdPod(pod *corev1.Pod) bool {
	if len(pod.Labels) == 0 {
		return false
	}
	v, e := pod.Labels[RisingWaveComponentName]
	if !e {
		return false
	}

	return v == string(ETCDType)
}

// filterAndAppendMetaLabel removes go metrics and duplicate metrics with the
// same name and pod name, then append the meta label to the metrics list.
// The operation is in-place on the input `mfMap“
// All processed (metricsName, podName) pair will be recorded in-place inside
// `processed`.
func filterAndAppendMetaLabel(podName, component string, processed map[string]bool, mfMap map[string]*dto.MetricFamily) {
	for key, mf := range mfMap {
		newKey := fmt.Sprintf("%s_%s", key, podName)
		// The same (metrics, pod) combination is already processed, discard it.
		if _, ok := processed[newKey]; ok {
			delete(mfMap, key)
		}
		if strings.HasPrefix(key, "go_") {
			delete(mfMap, key)
		}
		processed[newKey] = true
		for _, m := range mf.GetMetric() {
			m.Label = append(m.Label, []*dto.LabelPair{
				{
					Name:  ptr.To("pod"),
					Value: &podName,
				},
				{
					Name:  ptr.To("component"),
					Value: &component,
				},
			}...)
		}
	}
}

func decode(mfs map[string]*dto.MetricFamily) ([]byte, error) {
	var buffer = bytes.NewBuffer([]byte{})

	enc := expfmt.NewEncoder(buffer, contentType)
	for _, mf := range mfs {
		err := enc.Encode(mf)
		if err != nil {
			return []byte{}, eris.Wrap(err, "failed to encode the metrics family data")
		}
	}
	if closer, ok := enc.(expfmt.Closer); ok {
		// This in particular takes care of the final "# EOF\n" line for OpenMetrics.
		err := closer.Close()
		if err != nil {
			return []byte{}, eris.Wrap(err, "failed to close the writer")
		}
	}
	return buffer.Bytes(), nil
}

func gzipData(data []byte) (compressedData []byte, err error) {
	var b bytes.Buffer
	gw := gzip.NewWriter(&b)

	_, err = gw.Write(data)
	if err != nil {
		return
	}

	if err = gw.Close(); err != nil {
		return
	}

	compressedData = b.Bytes()

	return
}
