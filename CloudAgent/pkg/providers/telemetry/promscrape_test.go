package telemetry

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
)

func TestEndpoint(t *testing.T) {
	tests := map[string]struct {
		labels map[string]string

		portName string
		port     int
		ip       string
		wantErr  bool
	}{
		"standalone": {
			labels: map[string]string{
				RisingWaveComponentName: "standalone",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
		},
		"frontend": {
			labels: map[string]string{
				RisingWaveComponentName: "frontend",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
		},
		"compute": {
			labels: map[string]string{
				RisingWaveComponentName: "compute",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
		},
		"etcd": {
			labels: map[string]string{
				RisingWaveComponentName: "etcd",
			},
			portName: "client",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
		},
		"etcd-err": {
			labels: map[string]string{
				RisingWaveComponentName: "etcd",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  true,
		},
		"non-risingwave-pod": {
			labels: map[string]string{
				"test": "test",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  true,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			podName := name
			testNamespace := name + "-ns"
			var podStartTime metav1.Time
			var pod = corev1.Pod{
				Status: corev1.PodStatus{
					Phase:     corev1.PodRunning,
					StartTime: &podStartTime,
					Conditions: []corev1.PodCondition{
						{
							Type:   corev1.PodReady,
							Status: corev1.ConditionTrue,
						},
					},
					PodIP: tt.ip,
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      podName,
					Namespace: testNamespace,
					Labels:    tt.labels,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name: "container1",
							Ports: []corev1.ContainerPort{
								{
									Name:          tt.portName,
									ContainerPort: int32(tt.port),
								},
							},
						}, {Name: "container2"}},
				},
			}
			e, err := endpoint(&pod)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)
			assert.Equal(t, fmt.Sprintf("http://%s:%d/metrics", tt.ip, tt.port), e)
		})
	}
}
func TestUrlWithFilter(t *testing.T) {
	tests := map[string]struct {
		labels   map[string]string
		portName string
		port     int
		ip       string
		wantErr  bool
		include  []string
		exclude  []string
	}{
		"none": {
			labels: map[string]string{
				RisingWaveComponentName: "none",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
		},
		"include": {
			labels: map[string]string{
				RisingWaveComponentName: "include",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
			include:  []string{"foo", "bar"},
		},
		"exclude": {
			labels: map[string]string{
				RisingWaveComponentName: "exclude",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  false,
			exclude:  []string{"foo", "bar"},
		},
		"include and exclude": {
			labels: map[string]string{
				RisingWaveComponentName: "include and exclude",
			},
			portName: "metrics",
			port:     443,
			ip:       "127.0.0.1",
			wantErr:  true,
			include:  []string{"foo"},
			exclude:  []string{"bar"},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			podName := name
			testNamespace := name + "-ns"
			var podStartTime metav1.Time
			var pod = corev1.Pod{
				Status: corev1.PodStatus{
					Phase:     corev1.PodRunning,
					StartTime: &podStartTime,
					Conditions: []corev1.PodCondition{
						{
							Type:   corev1.PodReady,
							Status: corev1.ConditionTrue,
						},
					},
					PodIP: tt.ip,
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      podName,
					Namespace: testNamespace,
					Labels:    tt.labels,
				},
				Spec: corev1.PodSpec{
					Containers: []corev1.Container{
						{
							Name: "container1",
							Ports: []corev1.ContainerPort{
								{
									Name:          tt.portName,
									ContainerPort: int32(tt.port),
								},
							},
						}, {Name: "container2"}},
				},
			}
			e, err := endpoint(&pod)
			assert.NoError(t, err)
			assert.Equal(t, fmt.Sprintf("http://%s:%d/metrics", tt.ip, tt.port), e)
			u, err := urlWithFilter(e, tt.include, tt.exclude)
			if tt.wantErr {
				assert.Error(t, err)
				return
			}

			parse, err := url.Parse(u)
			assert.NoError(t, err)
			assert.Equal(t, tt.include, parse.Query()["include"])
			assert.Equal(t, tt.exclude, parse.Query()["exclude"])
		})
	}
}

func TestScrape(t *testing.T) {
	customMetrics := []string{"test_foo", "test_bar", "test_up", "test_foobar"}
	svc := createMetricsServer(customMetrics)
	addr, err := net.ResolveTCPAddr("tcp", svc.Listener.Addr().String())
	assert.NoError(t, err)
	port := int32(addr.Port)

	tests := map[string]struct {
		namespace      string
		acceptEncoding string
		pods           []*corev1.Pod

		notFound      bool
		noData        bool
		wantErr       bool
		expectedLines []string
		include       []string
		exclude       []string
	}{
		"normal-etcd": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalEtcdPod("test-ns", port),
			},
			noData: false,
		},
		"normal-etcd-multiple": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalEtcdPodWithName("etcd1", "test-ns", port),
				generateNormalEtcdPodWithName("etcd2", "test-ns", port),
			},
			noData: false,
			expectedLines: []string{
				"# HELP standalone_test_metrics for test",
				"# TYPE standalone_test_metrics gauge",
				`standalone_test_metrics{url="/metrics",pod="etcd1",component="etcd"} 0`,
				`standalone_test_metrics{url="/metrics",pod="etcd2",component="etcd"} 0`,
			},
		},
		"normal-etcd-gzip": {
			namespace:      "test-ns",
			acceptEncoding: encodingGzip,
			pods: []*corev1.Pod{
				generateNormalEtcdPod("test-ns", port),
			},
			noData: false,
		},

		"no-endpoint-etcd": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					p := generateNormalEtcdPod("test-ns", port)
					newPod := p.DeepCopy()
					// update the endpoint.
					newPod.Spec.Containers[0].Ports = []corev1.ContainerPort{
						{
							Name:          "no-client",
							ContainerPort: 2222,
						},
					}
					return newPod
				}(),
			},
			noData: true,
		},

		"no-running-etcd": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					p := generateNormalEtcdPod("test-ns", port)
					newPod := p.DeepCopy()
					// update the status.
					newPod.Status = corev1.PodStatus{
						Phase: corev1.PodPending,
						Conditions: []corev1.PodCondition{
							{
								Type:   corev1.PodReady,
								Status: corev1.ConditionFalse,
							},
						},
						PodIP: "127.0.0.1",
					}
					return newPod
				}(),
			},
			noData: true,
		},

		"normal-standalone": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			noData: false,
		},

		"normal-standalone-gzip": {
			namespace:      "test-ns",
			acceptEncoding: encodingGzip,
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			noData: false,
		},

		"no-endpoint-standalone": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					p := generateNormalEtcdPod("test-ns", port)
					newPod := p.DeepCopy()
					// update the endpoint.
					newPod.Spec.Containers[0].Ports = []corev1.ContainerPort{
						{
							Name:          "no-metrics",
							ContainerPort: 2222,
						},
					}
					return newPod
				}(),
			},
			noData: true,
		},

		"no-running-standalone": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				func() *corev1.Pod {
					p := generateNormalStandalonePod("test-ns", port)
					newPod := p.DeepCopy()
					// update the status.
					newPod.Status = corev1.PodStatus{
						Phase: corev1.PodPending,
						Conditions: []corev1.PodCondition{
							{
								Type:   corev1.PodReady,
								Status: corev1.ConditionFalse,
							},
						},
						PodIP: "127.0.0.1",
					}
					return newPod
				}(),
			},
			noData: true,
		},

		"not-found-pods": {
			namespace: "not-found",
			pods: []*corev1.Pod{
				generateNormalEtcdPod("fake-ns", port),
				generateNormalStandalonePod("fake-ns", port),
			},
			noData:   true,
			notFound: true,
		},
		"normal-metrics": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			noData: false,
		},
		"normal-metrics-include": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			noData:  false,
			include: []string{"test_foo", "test_bar"},
		},
		"normal-metrics-exclude": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			noData:  false,
			exclude: []string{"test_foo", "test_bar"},
		},
		"normal-metrics-include-and-exclude": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			noData:  true,
			wantErr: true,
			include: []string{"test_foo"},
			exclude: []string{"test_bar"},
		},
		"normal-metrics-with-not-found-metrics": {
			namespace: "test-ns",
			pods: []*corev1.Pod{
				generateNormalStandalonePod("test-ns", port),
			},
			include: []string{"test_not_found_metrics"},
			exclude: []string{},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			lister, err := generateLister(tt.pods)
			assert.NoError(t, err)

			m, err := NewMetricsPuller(NewMetricsPullerOption{
				PodLister: lister,
			})
			assert.NoError(t, err)

			res, err := m.Scrape(context.Background(), ScrapeRequest{
				Namespace:            tt.namespace,
				AcceptEncodingHeader: tt.acceptEncoding,
				Include:              tt.include,
				Exclude:              tt.exclude,
			})

			// just test not found.
			if tt.notFound {
				assert.Error(t, err)
				assert.Equal(t, eris.GetCode(err), eris.CodeNotFound)
				return
			}
			// expect something wrong
			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			assert.NoError(t, err)

			if tt.noData {
				assert.Zero(t, len(res.Payload))
				return
			}

			if len(tt.expectedLines) != 0 {
				for _, l := range tt.expectedLines {
					assert.Contains(t, string(res.Payload), l)
				}
			}

			metricsData := res.Payload
			if tt.acceptEncoding == encodingGzip {
				assert.Equal(t, res.ContentEncodingHeader, encodingGzip)
				gr, err := gzip.NewReader(bytes.NewBuffer(res.Payload))
				require.NoError(t, err)
				metricsData, err = io.ReadAll(gr)
				require.NoError(t, err)
			}
			// check pod labels, component labels, and filter metrics
			if len(tt.include) > 0 {
				includeMap := map[string]struct{}{}
				for _, item := range tt.include {
					includeMap[item] = struct{}{}
				}
				for _, item := range customMetrics {
					contain := fmt.Sprintf("%s{pod=\"%s\",component=\"%s\"}", item, tt.pods[0].Name, tt.pods[0].Labels[RisingWaveComponentName])
					if _, ok := includeMap[item]; ok {
						assert.Contains(t, string(metricsData), contain)
					} else {
						assert.NotContains(t, string(metricsData), contain)
					}
				}
			} else {
				excludeMap := map[string]struct{}{}
				for _, item := range tt.exclude {
					excludeMap[item] = struct{}{}
				}
				for _, item := range customMetrics {
					contain := fmt.Sprintf("%s{pod=\"%s\",component=\"%s\"}", item, tt.pods[0].Name, tt.pods[0].Labels[RisingWaveComponentName])
					if _, ok := excludeMap[item]; ok {
						assert.NotContains(t, string(metricsData), contain)
					} else {
						assert.Contains(t, string(metricsData), contain)
					}
				}
			}
		})
	}
}

func generateNormalEtcdPodWithName(name, ns string, port int32) *corev1.Pod {
	podStartTime := metav1.Now()

	return &corev1.Pod{
		Status: corev1.PodStatus{
			Phase:     corev1.PodRunning,
			StartTime: &podStartTime,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
			PodIP: "127.0.0.1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: ns,
			Labels: map[string]string{
				"name":                  "etcd",
				RisingWaveComponentName: "etcd",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "etcd",
					Ports: []corev1.ContainerPort{
						{
							Name:          "client",
							ContainerPort: port,
						},
					},
				},
			},
		},
	}
}

func generateNormalEtcdPod(ns string, port int32) *corev1.Pod {
	return generateNormalEtcdPodWithName("etcd", ns, port)
}

func generateNormalStandalonePod(ns string, port int32) *corev1.Pod {
	podStartTime := metav1.Now()

	return &corev1.Pod{
		Status: corev1.PodStatus{
			Phase:     corev1.PodRunning,
			StartTime: &podStartTime,
			Conditions: []corev1.PodCondition{
				{
					Type:   corev1.PodReady,
					Status: corev1.ConditionTrue,
				},
			},
			PodIP: "127.0.0.1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      "standalone",
			Namespace: ns,
			Labels: map[string]string{
				"name":                  "standalone",
				RisingWaveComponentName: "standalone",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name: "standalone",
					Ports: []corev1.ContainerPort{
						{
							Name:          "metrics",
							ContainerPort: port,
						},
					},
				},
			},
		},
	}
}

func createMetricsServer(metrics []string) *httptest.Server {
	etcdMetricsTmp := `
# HELP etcd_test_metrics for test
# TYPE etcd_test_metrics gauge
etcd_test_metrics{url="%s"} 0
`
	standaloneMetricsTmp := `
# HELP standalone_test_metrics for test
# TYPE standalone_test_metrics gauge
standalone_test_metrics{url="%s"} 0
`

	return httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.Header().Set("Content-Type", "application/json")
		include := r.URL.Query()["include"]
		exclude := r.URL.Query()["exclude"]

		metricsTemplate := map[string]string{}
		var payload []byte

		switch r.URL.Path {
		case "/client":
			metricsTemplate["etcd_test_metrics"] = fmt.Sprintf(etcdMetricsTmp, r.URL.String())
		case "/metrics":
			metricsTemplate["standalone_test_metrics"] = fmt.Sprintf(standaloneMetricsTmp, r.URL.String())
		default:
			w.WriteHeader(http.StatusNotFound)
			return
		}
		for _, m := range metrics {
			template := fmt.Sprintf("#TYPE %s gauge\n%s{} 0\n", m, m)
			metricsTemplate[m] = template
		}
		// filter metrics
		if len(include) > 0 {
			includeMap := map[string]struct{}{}
			for _, item := range include {
				includeMap[item] = struct{}{}
			}
			for name, temp := range metricsTemplate {
				if _, ok := includeMap[name]; ok {
					// add metrics
					payload = append(payload, []byte(temp)...)
				}
			}
		} else {
			excludeMap := map[string]struct{}{}
			for _, m := range exclude {
				excludeMap[m] = struct{}{}
			}
			for name, temp := range metricsTemplate {
				if _, ok := excludeMap[name]; !ok {
					// add metrics
					payload = append(payload, []byte(temp)...)
				}
			}
		}

		var err error
		if r.Header.Get(acceptEncodingHeaderKey) == encodingGzip {
			payload, err = gzipData(payload)
			if err != nil {
				w.WriteHeader(http.StatusInternalServerError)
				return
			}
			w.Header().Set(contentEncodingHeaderKey, encodingGzip)
		}
		_, err = w.Write(payload)
		if err != nil {
			w.WriteHeader(http.StatusInternalServerError)
		}
		w.WriteHeader(http.StatusOK)
	}))
}

func generateLister(pods []*corev1.Pod) (*k8s.PodLister, error) {
	fakeClientSet := fake.NewClientset()
	for i := range pods {
		_, err := fakeClientSet.CoreV1().Pods(pods[i].Namespace).Create(context.Background(), pods[i], metav1.CreateOptions{})
		if err != nil {
			return nil, err
		}
	}

	podLister, err := k8s.NewPodLister(fakeClientSet)
	if err != nil {
		return nil, err
	}

	podLister.WaitReady(context.Background())
	return podLister, nil
}
