package telemetry

import (
	"context"
	"fmt"

	dto "github.com/prometheus/client_model/go"
	"github.com/prometheus/common/expfmt"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	agentlogger "github.com/risingwavelabs/cloudagent/pkg/logger"
)

const (
	encodingGzip            = "gzip"
	acceptEncodingHeaderKey = "Accept-Encoding"

	contentEncodingHeaderKey = "Content-Encoding"
)

type ScrapeRequest struct {
	Namespace            string
	AcceptEncodingHeader string
	Include              []string
	Exclude              []string
}

type ScrapeResponse struct {
	// Metrics bytes data.
	Payload []byte
	// The format of metrics data, need to negotiate the encoder based on the content type.
	ContentTypeHeader     string
	ContentEncodingHeader string
}

type MetricsPuller interface {
	Scrape(ctx context.Context, request ScrapeRequest) (*ScrapeResponse, error)
}

type NewMetricsPullerOption struct {
	PodLister *k8s.PodLister
}

func NewMetricsPuller(option NewMetricsPullerOption) (MetricsPuller, error) {
	return &metricsManager{
		lister: option.PodLister,
	}, nil
}

// MetricsManager provide the function to scrape the metrics from risingwave pods.
type metricsManager struct {
	lister *k8s.PodLister
}

var (
	contentType = expfmt.NewFormat(expfmt.TypeTextPlain)
)

func (m *metricsManager) Scrape(ctx context.Context, request ScrapeRequest) (*ScrapeResponse, error) {
	data, err := m.scapeMetrics(ctx, request)
	// will wrap the error outside.
	if err != nil {
		return &ScrapeResponse{}, err
	}
	contentEncodingHeader := ""
	if request.AcceptEncodingHeader == encodingGzip {
		contentEncodingHeader = encodingGzip
		data, err = gzipData(data)
		if err != nil {
			return &ScrapeResponse{}, err
		}
	}
	return &ScrapeResponse{
		Payload:               data,
		ContentTypeHeader:     string(contentType),
		ContentEncodingHeader: contentEncodingHeader,
	}, nil
}

func (m *metricsManager) scapeMetrics(ctx context.Context, request ScrapeRequest) ([]byte, error) {
	namespace := request.Namespace
	agentlogger.FromCtx(ctx).Infof("scrape all pods for namespace %s", namespace)

	// list all the active pods
	pods, err := m.lister.List(namespace)
	if err != nil {
		agentlogger.FromCtx(ctx).Errorf("failed to list pods, ns %s, err %v", namespace, err)
		return []byte{}, err
	}

	if len(pods) == 0 {
		return []byte{}, eris.Errorf("pods for namespace %s not found", namespace).WithCode(eris.CodeNotFound)
	}

	mergedMf := make(map[string]*dto.MetricFamily)
	processed := make(map[string]bool)
	for _, p := range pods {
		// ignore the not running pods.
		if p.Status.Phase != corev1.PodRunning {
			continue
		}

		// Just record the warning and skip the pods without labels.
		rawURL, err := endpoint(p)
		if err != nil {
			agentlogger.FromCtx(ctx).Warnf("failed to parse pod endpoint, pod %s, err %v", p.Name, err)
			continue
		}

		// add filter metrics
		// e.g. /metrics?include=foo&include=bar
		newURL, err := urlWithFilter(rawURL, request.Include, request.Exclude)
		if err != nil {
			agentlogger.FromCtx(ctx).Errorf("failed to get url with filter, pod %s, err %v", p.Name, err)
			return []byte{}, err
		}

		mfMap, err := m.fetchAndParseData(ctx, newURL)
		if err != nil {
			agentlogger.FromCtx(ctx).Errorf("failed to fetch and parse the metrics data, pod %s, err %v", p.Name, err)
			return []byte{}, err
		}

		// append the meta label to the metrics list.
		component, e := p.Labels[RisingWaveComponentName]
		if !e {
			agentlogger.FromCtx(ctx).Errorf("cannot get the component for pod %s", p.Name)
			return []byte{}, fmt.Errorf("cannot get the component for pod %s", p.Name)
		}

		filterAndAppendMetaLabel(p.Name, component, processed, mfMap)

		for key, mf := range mfMap {
			if merged, ok := mergedMf[key]; ok {
				if mf.GetType() != merged.GetType() {
					agentlogger.FromCtx(ctx).Errorf("metrics %s have different types from different pods, %v vs %v", key, mf.GetType(), merged.GetType())
					continue
				}
				merged.Metric = append(merged.Metric, mf.GetMetric()...)
			} else {
				mergedMf[key] = mf
			}
		}
	}

	data, err := decode(mergedMf)
	if err != nil {
		agentlogger.FromCtx(ctx).Errorf("failed to decode the data, err %v", err)
		return []byte{}, err
	}
	return data, nil
}

func (m *metricsManager) fetchAndParseData(ctx context.Context, url string) (map[string]*dto.MetricFamily, error) {
	var parser expfmt.TextParser

	proxyRes, err := doRequest(ctx, url)
	// Just record the warning, not return the error.
	// To avoid metrics to fail to fetch due to one abnormal pod.
	if err != nil {
		agentlogger.FromCtx(ctx).Warnf("failed to do request, url %s, err %v", url, err)
		return nil, nil
	}

	defer func() {
		_ = proxyRes.Close()
	}()

	mf, err := parser.TextToMetricFamilies(proxyRes)
	if err != nil {
		return nil, eris.Wrap(err, "failed to parse the data")
	}
	return mf, nil
}
