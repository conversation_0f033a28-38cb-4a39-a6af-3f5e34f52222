package telemetry

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestProxyPrometheus(t *testing.T) {
	type reqMeta struct {
		payload     []byte
		method      string
		path        string
		query       string
		contentType string
	}
	tests := map[string]struct {
		handler func(http.ResponseWriter, *http.Request)
		opt     ProxyPrometheusOption

		want     ProxyPrometheusResult
		wantErr  bool
		wantReqs []reqMeta
	}{
		"normal get": {
			handler: func(w http.ResponseWriter, _ *http.Request) {
				_, err := w.Write([]byte("OK"))
				if err != nil {
					t.<PERSON>rrorf("failed to write response %v", err)
				}
			},
			opt: ProxyPrometheusOption{
				Method:   http.MethodGet,
				Endpoint: "/api/v1/query",
				Payload:  []byte("foo=1&bar=2"),
			},

			want: ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("OK"),
			},
			wantReqs: []reqMeta{
				{
					payload: make([]byte, 0),
					method:  http.MethodGet,
					path:    "/api/v1/query",
					query:   "foo=1&bar=2",
				},
			},
		},
		"normal post": {
			handler: func(w http.ResponseWriter, _ *http.Request) {
				_, err := w.Write([]byte("OK"))
				if err != nil {
					t.Errorf("failed to write response %v", err)
				}
			},
			opt: ProxyPrometheusOption{
				Method:   http.MethodPost,
				Endpoint: "/api/v1/query_range",
				// Encode will sort the keys so the resulted payload is deterministic.
				Payload: []byte(url.Values{
					"foo": {"1"},
					"bar": {"2"},
				}.Encode()),
			},

			want: ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("OK"),
			},
			wantReqs: []reqMeta{
				{
					payload:     []byte("bar=2&foo=1"),
					method:      http.MethodPost,
					path:        "/api/v1/query_range",
					contentType: "application/x-www-form-urlencoded",
				},
			},
		},
		"http error": {
			handler: func(w http.ResponseWriter, _ *http.Request) {
				w.WriteHeader(http.StatusInternalServerError)
				_, err := w.Write([]byte("Error"))
				if err != nil {
					t.Errorf("failed to write response %v", err)
				}
			},
			opt: ProxyPrometheusOption{
				Method:   http.MethodPost,
				Endpoint: "/api/v1/query_range",
				// Encode will sort the keys so the resulted payload is deterministic.
				Payload: []byte(url.Values{
					"foo": {"1"},
					"bar": {"2"},
				}.Encode()),
			},

			want: ProxyPrometheusResult{
				Code:    http.StatusInternalServerError,
				Payload: []byte("Error"),
			},
			wantReqs: []reqMeta{
				{
					payload:     []byte("bar=2&foo=1"),
					method:      http.MethodPost,
					path:        "/api/v1/query_range",
					contentType: "application/x-www-form-urlencoded",
				},
			},
		},
		"invalid request": {
			wantErr: true,
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			var recordedReqs []reqMeta
			// The fake server records the request and calls the handler specified in
			// the test case.
			ts := httptest.NewServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
				bodyBytes, _ := io.ReadAll(r.Body)
				_ = r.Body.Close()
				r.Body = io.NopCloser(bytes.NewBuffer(bodyBytes))
				recordedReqs = append(recordedReqs, reqMeta{
					payload:     bodyBytes,
					method:      r.Method,
					path:        r.URL.Path,
					query:       r.URL.RawQuery,
					contentType: r.Header.Get("Content-type"),
				})
				tt.handler(w, r)
			}))
			defer ts.Close()

			provider := &prometheusProvider{
				client: ts.Client(),
			}

			tt.opt.BaseURL = ts.URL
			got, err := provider.ProxyPrometheus(context.Background(), tt.opt)

			if tt.wantErr {
				assert.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.Equal(t, tt.want, got)
			assert.Equal(t, tt.wantReqs, recordedReqs)
		})
	}
}
