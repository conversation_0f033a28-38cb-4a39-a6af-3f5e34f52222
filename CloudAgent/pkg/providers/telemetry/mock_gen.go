// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/telemetry (interfaces: PrometheusProvider,MetricsPuller)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/telemetry -package=telemetry -destination=pkg/providers/telemetry/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/telemetry PrometheusProvider,MetricsPuller
//

// Package telemetry is a generated GoMock package.
package telemetry

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockPrometheusProvider is a mock of PrometheusProvider interface.
type MockPrometheusProvider struct {
	ctrl     *gomock.Controller
	recorder *MockPrometheusProviderMockRecorder
	isgomock struct{}
}

// MockPrometheusProviderMockRecorder is the mock recorder for MockPrometheusProvider.
type MockPrometheusProviderMockRecorder struct {
	mock *MockPrometheusProvider
}

// NewMockPrometheusProvider creates a new mock instance.
func NewMockPrometheusProvider(ctrl *gomock.Controller) *MockPrometheusProvider {
	mock := &MockPrometheusProvider{ctrl: ctrl}
	mock.recorder = &MockPrometheusProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPrometheusProvider) EXPECT() *MockPrometheusProviderMockRecorder {
	return m.recorder
}

// ProxyPrometheus mocks base method.
func (m *MockPrometheusProvider) ProxyPrometheus(arg0 context.Context, arg1 ProxyPrometheusOption) (ProxyPrometheusResult, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ProxyPrometheus", arg0, arg1)
	ret0, _ := ret[0].(ProxyPrometheusResult)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ProxyPrometheus indicates an expected call of ProxyPrometheus.
func (mr *MockPrometheusProviderMockRecorder) ProxyPrometheus(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ProxyPrometheus", reflect.TypeOf((*MockPrometheusProvider)(nil).ProxyPrometheus), arg0, arg1)
}

// MockMetricsPuller is a mock of MetricsPuller interface.
type MockMetricsPuller struct {
	ctrl     *gomock.Controller
	recorder *MockMetricsPullerMockRecorder
	isgomock struct{}
}

// MockMetricsPullerMockRecorder is the mock recorder for MockMetricsPuller.
type MockMetricsPullerMockRecorder struct {
	mock *MockMetricsPuller
}

// NewMockMetricsPuller creates a new mock instance.
func NewMockMetricsPuller(ctrl *gomock.Controller) *MockMetricsPuller {
	mock := &MockMetricsPuller{ctrl: ctrl}
	mock.recorder = &MockMetricsPullerMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockMetricsPuller) EXPECT() *MockMetricsPullerMockRecorder {
	return m.recorder
}

// Scrape mocks base method.
func (m *MockMetricsPuller) Scrape(ctx context.Context, request ScrapeRequest) (*ScrapeResponse, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Scrape", ctx, request)
	ret0, _ := ret[0].(*ScrapeResponse)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Scrape indicates an expected call of Scrape.
func (mr *MockMetricsPullerMockRecorder) Scrape(ctx, request any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Scrape", reflect.TypeOf((*MockMetricsPuller)(nil).Scrape), ctx, request)
}
