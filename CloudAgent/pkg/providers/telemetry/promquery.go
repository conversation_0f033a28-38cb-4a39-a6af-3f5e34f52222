package telemetry

import (
	"bytes"
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"
	"net/url"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azcore/policy"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	"github.com/pkg/errors"
	promcfg "github.com/prometheus/common/config"
	"github.com/prometheus/common/sigv4"
	"github.com/risingwavelabs/eris"
	"google.golang.org/api/option"
	"google.golang.org/api/transport"

	pbcfgaws "github.com/risingwavelabs/cloudagent/pbgen/config/aws"
	pbcfgazr "github.com/risingwavelabs/cloudagent/pbgen/config/azr"
	pbcfggcp "github.com/risingwavelabs/cloudagent/pbgen/config/gcp"
)

type ProxyPrometheusOption struct {
	BaseURL  string
	Method   string
	Endpoint string
	Payload  []byte
}

type ProxyPrometheusResult struct {
	Code    int
	Payload []byte
}

type PrometheusProvider interface {
	ProxyPrometheus(context.Context, ProxyPrometheusOption) (ProxyPrometheusResult, error)
}

type prometheusProvider struct {
	client *http.Client
}

func NewGMPPrometheusProvider(ctx context.Context, cfg *pbcfggcp.Config) (PrometheusProvider, error) {
	if cfg == nil {
		return nil, eris.Errorf("GCP config cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	client, err := newGMPClient(ctx, cfg)
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize GMP HTTP client")
	}
	return &prometheusProvider{
		client: client,
	}, nil
}

func NewAMPPrometheusProvider(cfg *pbcfgaws.Config) (PrometheusProvider, error) {
	if cfg == nil {
		return nil, eris.Errorf("AWS config cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	client, err := newAMPClient(cfg)
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize AMP HTTP client")
	}
	return &prometheusProvider{
		client: client,
	}, nil
}

func NewAzMPProvider(cfg *pbcfgazr.Config) (PrometheusProvider, error) {
	if cfg == nil {
		return nil, eris.Errorf("AZR config cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	client, err := newAzMPClient(cfg)
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize azr HTTP client")
	}
	return &prometheusProvider{
		client: client,
	}, nil
}

func NewLocalPrometheusProvider() (PrometheusProvider, error) {
	return &prometheusProvider{
		client: &http.Client{},
	}, nil
}

func (provider *prometheusProvider) ProxyPrometheus(ctx context.Context, opt ProxyPrometheusOption) (ProxyPrometheusResult, error) {
	ep, err := getPrometheusEndpoint(opt)
	if err != nil {
		return ProxyPrometheusResult{}, eris.Wrap(err, "failed to construct prometheus url")
	}
	var req *http.Request
	switch opt.Method {
	case http.MethodGet:
		req, err = http.NewRequestWithContext(ctx, http.MethodGet, ep, bytes.NewReader([]byte("")))
		if err != nil {
			return ProxyPrometheusResult{}, eris.Wrapf(err, "failed to create get request, url: %v", ep)
		}
	case http.MethodPost:
		req, err = http.NewRequestWithContext(ctx, http.MethodPost, ep, bytes.NewReader(opt.Payload))
		if err != nil {
			return ProxyPrometheusResult{}, eris.Wrapf(err, "failed to create get request, url: %v", ep)
		}
		req.Header.Set("Content-Type", "application/x-www-form-urlencoded")
	default:
		return ProxyPrometheusResult{}, eris.Errorf("invalid http method: %v", opt.Method)
	}
	res, err := provider.client.Do(req)
	if err != nil {
		return ProxyPrometheusResult{}, eris.Wrapf(err, "failed to execute gmp request, req: %v", req)
	}
	defer res.Body.Close()
	bodyBytes, err := io.ReadAll(res.Body)
	if err != nil {
		return ProxyPrometheusResult{}, eris.Wrapf(err, "failed to read response body, res: %v", res)
	}
	return ProxyPrometheusResult{
		Code:    res.StatusCode,
		Payload: bodyBytes,
	}, nil
}

func getPrometheusEndpoint(opt ProxyPrometheusOption) (string, error) {
	endpoint, err := url.JoinPath(opt.BaseURL, opt.Endpoint)
	if err != nil {
		return "", eris.Wrapf(err, "cannot join base url and path, base: %v, path: %v", opt.BaseURL, opt.Endpoint)
	}
	if opt.Method == http.MethodGet && len(opt.Payload) > 0 {
		endpoint = endpoint + "?" + string(opt.Payload)
	}
	_, err = url.ParseRequestURI(endpoint)
	if err != nil {
		return "", eris.Wrapf(err, "invalid URL: %v", endpoint)
	}
	return endpoint, nil
}

func newGMPClient(ctx context.Context, cfg *pbcfggcp.Config) (*http.Client, error) {
	var (
		client    *http.Client
		clientErr error
	)
	switch authType := cfg.GetAuth().(type) {
	case *pbcfggcp.Config_StaticCreds:
		jsonCreds, err := base64.StdEncoding.DecodeString(cfg.GetStaticCreds().GetGoogleApplicationCredentials())
		if err != nil {
			return nil, errors.Wrap(err, "unable to decode gcp static creds")
		}
		client, _, clientErr = transport.NewHTTPClient(ctx, option.WithCredentialsJSON(jsonCreds))
	case *pbcfggcp.Config_GkeWebIdentity:
		client, _, clientErr = transport.NewHTTPClient(ctx)
	default:
		return nil, fmt.Errorf("invalid GCP auth type: %v", authType)
	}
	if clientErr != nil {
		return nil, errors.Wrap(clientErr, "failed to create new API HTTP client")
	}
	return client, nil
}

func newAMPClient(cfg *pbcfgaws.Config) (*http.Client, error) {
	sigv4Cfg := &sigv4.SigV4Config{
		Region: cfg.GetRegion(),
	}
	switch authType := cfg.GetAuth().(type) {
	case *pbcfgaws.Config_StaticCreds:
		sigv4Cfg.AccessKey = cfg.GetStaticCreds().GetAccessKeyId()
		sigv4Cfg.SecretKey = promcfg.Secret(cfg.GetStaticCreds().GetSecretAccessKey())
	case *pbcfgaws.Config_EksWebIdentity:
		break
	default:
		return nil, fmt.Errorf("invalid AWS auth type: %v", authType)
	}
	client := &http.Client{}
	t, err := sigv4.NewSigV4RoundTripper(sigv4Cfg, client.Transport)
	if err != nil {
		return nil, err
	}
	client.Transport = t
	return client, nil
}

// AzureTransport is a custom transport that adds the Azure AD token to the request.
type AzureTransport struct {
	baseTransport http.RoundTripper
	credential    azcore.TokenCredential
	scopes        []string
}

func (a *AzureTransport) RoundTrip(req *http.Request) (*http.Response, error) {
	token, err := a.credential.GetToken(req.Context(), policy.TokenRequestOptions{Scopes: a.scopes})
	if err != nil {
		return nil, err
	}
	req.Header.Set("Authorization", "Bearer "+token.Token)
	return a.baseTransport.RoundTrip(req)
}

func newAzMPClient(cfg *pbcfgazr.Config) (*http.Client, error) {
	var cred azcore.TokenCredential
	var err error

	switch authType := cfg.GetAuth().(type) {
	case *pbcfgazr.Config_StaticCreds:
		staticCreds := cfg.GetStaticCreds()
		cred, err = azidentity.NewClientSecretCredential(staticCreds.GetTenantId(), staticCreds.GetClientId(), staticCreds.GetClientSecret(), nil)
		if err != nil {
			return nil, fmt.Errorf("failed to obtain a credentials token from static creds: %v", err)
		}
	case *pbcfgazr.Config_AksWorkloadIdentity:
		cred, err = azidentity.NewDefaultAzureCredential(nil)
		if err != nil {
			return nil, fmt.Errorf("failed to obtain a crdetials token from workload identity: %v", err)
		}
	default:
		return nil, fmt.Errorf("invalid Azure auth type: %v", authType)
	}

	// Reference: https://learn.microsoft.com/en-us/entra/identity-platform/scopes-oidc#the-default-scope.
	transport := AzureTransport{
		baseTransport: http.DefaultTransport,
		credential:    cred,
		scopes:        []string{"https://prometheus.monitor.azure.com/.default"},
	}

	client := &http.Client{
		Transport: &transport,
	}

	return client, nil
}
