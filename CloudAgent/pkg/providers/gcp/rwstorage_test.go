package gcp

import (
	"context"
	"testing"

	gcs "cloud.google.com/go/storage"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/utils/etagcache"
)

func TestGetFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)

	provider := &Provider{
		gcsClient: mockGCSClient,
		cache:     etagcache.NewCache(),
	}

	BucketName := "my-bucket"
	manifestKey := "my-tenant-backup/manifest.json"
	content := []byte("test")

	objs := []*gcs.ObjectAttrs{{}}
	mockGCSClient.EXPECT().ListObjects(gomock.Any(), BucketName, manifestKey).Return(objs, nil)
	mockGCSClient.EXPECT().HeadObject(gomock.Any(), BucketName, manifestKey).Return(&gcs.ObjectAttrs{Etag: "test"}, nil)
	mockGCSClient.EXPECT().GetObject(gomock.Any(), BucketName, manifestKey).Return(content, nil)

	manifest, err := provider.GetFile(context.Background(), BucketName, manifestKey)
	assert.NoError(t, err)
	assert.Equal(t, manifest, content)
}

func TestGetFile_not_found(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)

	provider := &Provider{
		gcsClient: mockGCSClient,
		cache:     etagcache.NewCache(),
	}

	BucketName := "my-bucket"
	manifestKey := "my-tenant-backup/manifest.json"

	objs := []*gcs.ObjectAttrs{}
	mockGCSClient.EXPECT().ListObjects(gomock.Any(), BucketName, manifestKey).Return(objs, nil)

	_, err := provider.GetFile(context.Background(), BucketName, manifestKey)
	assert.Error(t, err)
	assert.Equal(t, eris.CodeNotFound, eris.GetCode(err))
}
