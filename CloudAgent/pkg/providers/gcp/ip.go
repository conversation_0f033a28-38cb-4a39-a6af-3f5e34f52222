package gcp

import (
	"context"

	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	"github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	utilsgcp "github.com/risingwavelabs/cloudagent/pkg/utils/gcp"
)

type CreateIPOption struct {
	Namespace  string
	ResourceID string
	ProjectID  string
	Region     string
	IPSubnet   string
}

type IPMeta struct {
	Status   *pbresource.Status
	Selflink string
	IP       string
}

func (provider *Provider) CreateIPAddress(ctx context.Context, option CreateIPOption) error {
	internalAddressType := "INTERNAL"
	ip := &gcccomputeg.ComputeAddress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				cnrmProjectIDAnnotation: option.ProjectID,
			},
		},
		Spec: gcccomputeg.ComputeAddressSpec{
			AddressType: &internalAddressType,
			SubnetworkRef: &v1alpha1.ResourceRef{
				External: option.IPSubnet,
			},
			Location: option.Region,
		},
	}

	err := provider.kc.Create(ctx, ip)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("ip address %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create ip address")
	}

	return nil
}

func (provider *Provider) DeleteIPAddress(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[gcccomputeg.ComputeAddress](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("ip address %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete ip address %s", resourceID)
	}
	return nil
}

func (provider *Provider) GetIPAddress(ctx context.Context, namespace, resourceID string) (*IPMeta, error) {
	ip, err := k8s.GetResource[gcccomputeg.ComputeAddress](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &IPMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get ip address %s", resourceID)
	}
	status := utilsgcp.KccConditionToResourceStatus(ip.Status.Conditions)
	res := &IPMeta{Status: status}

	if ip.Status.SelfLink != nil {
		res.Selflink = *ip.Status.SelfLink
	}
	if ip.Status.ObservedState != nil && ip.Status.ObservedState.Address != nil {
		res.IP = *ip.Status.ObservedState.Address
	} else if ip.Spec.Address != nil {
		res.IP = *ip.Spec.Address
	}

	return res, nil
}
