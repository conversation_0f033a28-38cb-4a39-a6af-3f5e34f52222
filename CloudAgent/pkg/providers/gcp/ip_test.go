package gcp

import (
	"context"
	"testing"

	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	"github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateIP(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	ctx := context.Background()
	option := CreateIPOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		ProjectID:  testGCPProjectID,
		Region:     "test-region",
		IPSubnet:   "test-subnet",
	}
	require.NoError(t, provider.CreateIPAddress(ctx, option))

	ip := gcccomputeg.ComputeAddress{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}

	require.NoError(t, provider.kc.Get(ctx, objKey, &ip))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIPAddress(ctx, option)))
	annotationRes, ok := ip.Annotations[cnrmProjectIDAnnotation]
	assert.True(t, ok)
	assert.Equal(t, testGCPProjectID, annotationRes)
}

func TestDeleteIP(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	ip := gcccomputeg.ComputeAddress{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&ip)
	provider := CreateFakeProvider(client)
	ctx := context.Background()
	require.NoError(t, provider.DeleteIPAddress(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	ip = gcccomputeg.ComputeAddress{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &ip)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteIPAddress(ctx, namespace, resourceID)))
}

func TestGetIP(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	var (
		testSelflink = "selflink"
		testIP       = "ip"
	)
	tests := []struct {
		description string
		ip          *gcccomputeg.ComputeAddress
		expectedIP  *IPMeta
		errCode     *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			ip: &gcccomputeg.ComputeAddress{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcccomputeg.ComputeAddressStatus{
					Conditions: []v1alpha1.Condition{
						{
							Type:   v1alpha1.ReadyConditionType,
							Status: v1.ConditionTrue,
						},
					},
					ObservedState: &gcccomputeg.AddressObservedStateStatus{
						Address: &testIP,
					},
					SelfLink: &testSelflink,
				},
			},
			expectedIP: &IPMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				IP:       testIP,
				Selflink: testSelflink,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			ip: &gcccomputeg.ComputeAddress{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcccomputeg.ComputeAddressStatus{
					Conditions: []v1alpha1.Condition{
						{
							Type:   "NotReady",
							Status: v1.ConditionTrue,
						},
					},
				},
			},
			expectedIP: &IPMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"status\":\"True\",\"type\":\"NotReady\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is in error state",
			ip: &gcccomputeg.ComputeAddress{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcccomputeg.ComputeAddressStatus{
					Conditions: []v1alpha1.Condition{
						{
							Type:    v1alpha1.ReadyConditionType,
							Status:  v1.ConditionFalse,
							Message: "resource in error state",
						},
					},
				},
			},
			expectedIP: &IPMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"message\":\"resource in error state\",\"status\":\"False\",\"type\":\"Ready\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			ip: &gcccomputeg.ComputeAddress{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "random namespace",
					Name:      "random id",
				},
			},
			expectedIP: &IPMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.ip)
		provider := CreateFakeProvider(client)
		const (
			resourceID = "resource"
			namespace  = "ns"
		)

		ip, err := provider.GetIPAddress(context.Background(), namespace, resourceID)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, tt.expectedIP, ip, "unexpected result for test %v, get %v, want %v", tt, ip, tt.expectedIP)
		}
	}
}
