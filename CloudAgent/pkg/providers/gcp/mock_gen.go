// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/gcp (interfaces: GcsClient,ObjectIterator,BucketHandle,ObjectHandle,SQLClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/gcp -package=gcp -destination=pkg/providers/gcp/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/gcp GcsClient,ObjectIterator,BucketHandle,ObjectHandle,SQLClient
//

// Package gcp is a generated GoMock package.
package gcp

import (
	context "context"
	reflect "reflect"

	storage "cloud.google.com/go/storage"
	gomock "go.uber.org/mock/gomock"
	sqladmin "google.golang.org/api/sqladmin/v1"
)

// MockGcsClient is a mock of GcsClient interface.
type MockGcsClient struct {
	ctrl     *gomock.Controller
	recorder *MockGcsClientMockRecorder
	isgomock struct{}
}

// MockGcsClientMockRecorder is the mock recorder for MockGcsClient.
type MockGcsClientMockRecorder struct {
	mock *MockGcsClient
}

// NewMockGcsClient creates a new mock instance.
func NewMockGcsClient(ctrl *gomock.Controller) *MockGcsClient {
	mock := &MockGcsClient{ctrl: ctrl}
	mock.recorder = &MockGcsClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockGcsClient) EXPECT() *MockGcsClientMockRecorder {
	return m.recorder
}

// CopyObject mocks base method.
func (m *MockGcsClient) CopyObject(ctx context.Context, req GCSCopyRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyObject", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CopyObject indicates an expected call of CopyObject.
func (mr *MockGcsClientMockRecorder) CopyObject(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyObject", reflect.TypeOf((*MockGcsClient)(nil).CopyObject), ctx, req)
}

// DeleteObject mocks base method.
func (m *MockGcsClient) DeleteObject(ctx context.Context, bucketName, objectName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteObject", ctx, bucketName, objectName)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteObject indicates an expected call of DeleteObject.
func (mr *MockGcsClientMockRecorder) DeleteObject(ctx, bucketName, objectName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteObject", reflect.TypeOf((*MockGcsClient)(nil).DeleteObject), ctx, bucketName, objectName)
}

// GetBucket mocks base method.
func (m *MockGcsClient) GetBucket(bucketName string) BucketHandle {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBucket", bucketName)
	ret0, _ := ret[0].(BucketHandle)
	return ret0
}

// GetBucket indicates an expected call of GetBucket.
func (mr *MockGcsClientMockRecorder) GetBucket(bucketName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBucket", reflect.TypeOf((*MockGcsClient)(nil).GetBucket), bucketName)
}

// GetObject mocks base method.
func (m *MockGcsClient) GetObject(ctx context.Context, bucketName, objectName string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObject", ctx, bucketName, objectName)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockGcsClientMockRecorder) GetObject(ctx, bucketName, objectName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockGcsClient)(nil).GetObject), ctx, bucketName, objectName)
}

// GetObjectIterator mocks base method.
func (m *MockGcsClient) GetObjectIterator(ctx context.Context, bucketName, dataDirectory string) ObjectIterator {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjectIterator", ctx, bucketName, dataDirectory)
	ret0, _ := ret[0].(ObjectIterator)
	return ret0
}

// GetObjectIterator indicates an expected call of GetObjectIterator.
func (mr *MockGcsClientMockRecorder) GetObjectIterator(ctx, bucketName, dataDirectory any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjectIterator", reflect.TypeOf((*MockGcsClient)(nil).GetObjectIterator), ctx, bucketName, dataDirectory)
}

// HeadObject mocks base method.
func (m *MockGcsClient) HeadObject(ctx context.Context, bucketName, objectName string) (*storage.ObjectAttrs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "HeadObject", ctx, bucketName, objectName)
	ret0, _ := ret[0].(*storage.ObjectAttrs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// HeadObject indicates an expected call of HeadObject.
func (mr *MockGcsClientMockRecorder) HeadObject(ctx, bucketName, objectName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "HeadObject", reflect.TypeOf((*MockGcsClient)(nil).HeadObject), ctx, bucketName, objectName)
}

// ListObjects mocks base method.
func (m *MockGcsClient) ListObjects(ctx context.Context, bucketName, objectName string) ([]*storage.ObjectAttrs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjects", ctx, bucketName, objectName)
	ret0, _ := ret[0].([]*storage.ObjectAttrs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjects indicates an expected call of ListObjects.
func (mr *MockGcsClientMockRecorder) ListObjects(ctx, bucketName, objectName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjects", reflect.TypeOf((*MockGcsClient)(nil).ListObjects), ctx, bucketName, objectName)
}

// ListObjectsWithMarker mocks base method.
func (m *MockGcsClient) ListObjectsWithMarker(ctx context.Context, req GCSListRequest) ([]string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjectsWithMarker", ctx, req)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListObjectsWithMarker indicates an expected call of ListObjectsWithMarker.
func (mr *MockGcsClientMockRecorder) ListObjectsWithMarker(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjectsWithMarker", reflect.TypeOf((*MockGcsClient)(nil).ListObjectsWithMarker), ctx, req)
}

// MockObjectIterator is a mock of ObjectIterator interface.
type MockObjectIterator struct {
	ctrl     *gomock.Controller
	recorder *MockObjectIteratorMockRecorder
	isgomock struct{}
}

// MockObjectIteratorMockRecorder is the mock recorder for MockObjectIterator.
type MockObjectIteratorMockRecorder struct {
	mock *MockObjectIterator
}

// NewMockObjectIterator creates a new mock instance.
func NewMockObjectIterator(ctrl *gomock.Controller) *MockObjectIterator {
	mock := &MockObjectIterator{ctrl: ctrl}
	mock.recorder = &MockObjectIteratorMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockObjectIterator) EXPECT() *MockObjectIteratorMockRecorder {
	return m.recorder
}

// Next mocks base method.
func (m *MockObjectIterator) Next() (*storage.ObjectAttrs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Next")
	ret0, _ := ret[0].(*storage.ObjectAttrs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Next indicates an expected call of Next.
func (mr *MockObjectIteratorMockRecorder) Next() *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Next", reflect.TypeOf((*MockObjectIterator)(nil).Next))
}

// MockBucketHandle is a mock of BucketHandle interface.
type MockBucketHandle struct {
	ctrl     *gomock.Controller
	recorder *MockBucketHandleMockRecorder
	isgomock struct{}
}

// MockBucketHandleMockRecorder is the mock recorder for MockBucketHandle.
type MockBucketHandleMockRecorder struct {
	mock *MockBucketHandle
}

// NewMockBucketHandle creates a new mock instance.
func NewMockBucketHandle(ctrl *gomock.Controller) *MockBucketHandle {
	mock := &MockBucketHandle{ctrl: ctrl}
	mock.recorder = &MockBucketHandleMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockBucketHandle) EXPECT() *MockBucketHandleMockRecorder {
	return m.recorder
}

// Attrs mocks base method.
func (m *MockBucketHandle) Attrs(ctx context.Context) (*storage.BucketAttrs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Attrs", ctx)
	ret0, _ := ret[0].(*storage.BucketAttrs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Attrs indicates an expected call of Attrs.
func (mr *MockBucketHandleMockRecorder) Attrs(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Attrs", reflect.TypeOf((*MockBucketHandle)(nil).Attrs), ctx)
}

// Create mocks base method.
func (m *MockBucketHandle) Create(ctx context.Context, bucketName string, attrs *storage.BucketAttrs) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Create", ctx, bucketName, attrs)
	ret0, _ := ret[0].(error)
	return ret0
}

// Create indicates an expected call of Create.
func (mr *MockBucketHandleMockRecorder) Create(ctx, bucketName, attrs any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Create", reflect.TypeOf((*MockBucketHandle)(nil).Create), ctx, bucketName, attrs)
}

// Object mocks base method.
func (m *MockBucketHandle) Object(name string) *storage.ObjectHandle {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Object", name)
	ret0, _ := ret[0].(*storage.ObjectHandle)
	return ret0
}

// Object indicates an expected call of Object.
func (mr *MockBucketHandleMockRecorder) Object(name any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Object", reflect.TypeOf((*MockBucketHandle)(nil).Object), name)
}

// MockObjectHandle is a mock of ObjectHandle interface.
type MockObjectHandle struct {
	ctrl     *gomock.Controller
	recorder *MockObjectHandleMockRecorder
	isgomock struct{}
}

// MockObjectHandleMockRecorder is the mock recorder for MockObjectHandle.
type MockObjectHandleMockRecorder struct {
	mock *MockObjectHandle
}

// NewMockObjectHandle creates a new mock instance.
func NewMockObjectHandle(ctrl *gomock.Controller) *MockObjectHandle {
	mock := &MockObjectHandle{ctrl: ctrl}
	mock.recorder = &MockObjectHandleMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockObjectHandle) EXPECT() *MockObjectHandleMockRecorder {
	return m.recorder
}

// Attrs mocks base method.
func (m *MockObjectHandle) Attrs(ctx context.Context) (*storage.ObjectAttrs, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Attrs", ctx)
	ret0, _ := ret[0].(*storage.ObjectAttrs)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Attrs indicates an expected call of Attrs.
func (mr *MockObjectHandleMockRecorder) Attrs(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Attrs", reflect.TypeOf((*MockObjectHandle)(nil).Attrs), ctx)
}

// CopierFrom mocks base method.
func (m *MockObjectHandle) CopierFrom(arg0 *storage.ObjectHandle) *storage.Copier {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopierFrom", arg0)
	ret0, _ := ret[0].(*storage.Copier)
	return ret0
}

// CopierFrom indicates an expected call of CopierFrom.
func (mr *MockObjectHandleMockRecorder) CopierFrom(arg0 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopierFrom", reflect.TypeOf((*MockObjectHandle)(nil).CopierFrom), arg0)
}

// NewReader mocks base method.
func (m *MockObjectHandle) NewReader(ctx context.Context) (*storage.Reader, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewReader", ctx)
	ret0, _ := ret[0].(*storage.Reader)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// NewReader indicates an expected call of NewReader.
func (mr *MockObjectHandleMockRecorder) NewReader(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewReader", reflect.TypeOf((*MockObjectHandle)(nil).NewReader), ctx)
}

// NewWriter mocks base method.
func (m *MockObjectHandle) NewWriter(ctx context.Context) *storage.Writer {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "NewWriter", ctx)
	ret0, _ := ret[0].(*storage.Writer)
	return ret0
}

// NewWriter indicates an expected call of NewWriter.
func (mr *MockObjectHandleMockRecorder) NewWriter(ctx any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "NewWriter", reflect.TypeOf((*MockObjectHandle)(nil).NewWriter), ctx)
}

// MockSQLClient is a mock of SQLClient interface.
type MockSQLClient struct {
	ctrl     *gomock.Controller
	recorder *MockSQLClientMockRecorder
	isgomock struct{}
}

// MockSQLClientMockRecorder is the mock recorder for MockSQLClient.
type MockSQLClientMockRecorder struct {
	mock *MockSQLClient
}

// NewMockSQLClient creates a new mock instance.
func NewMockSQLClient(ctrl *gomock.Controller) *MockSQLClient {
	mock := &MockSQLClient{ctrl: ctrl}
	mock.recorder = &MockSQLClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockSQLClient) EXPECT() *MockSQLClientMockRecorder {
	return m.recorder
}

// DescribeSQLInstance mocks base method.
func (m *MockSQLClient) DescribeSQLInstance(ctx context.Context, id string) (*sqladmin.DatabaseInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSQLInstance", ctx, id)
	ret0, _ := ret[0].(*sqladmin.DatabaseInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSQLInstance indicates an expected call of DescribeSQLInstance.
func (mr *MockSQLClientMockRecorder) DescribeSQLInstance(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSQLInstance", reflect.TypeOf((*MockSQLClient)(nil).DescribeSQLInstance), ctx, id)
}

// ListOperations mocks base method.
func (m *MockSQLClient) ListOperations(ctx context.Context, id string, maxResults int32) ([]*sqladmin.Operation, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListOperations", ctx, id, maxResults)
	ret0, _ := ret[0].([]*sqladmin.Operation)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListOperations indicates an expected call of ListOperations.
func (mr *MockSQLClientMockRecorder) ListOperations(ctx, id, maxResults any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListOperations", reflect.TypeOf((*MockSQLClient)(nil).ListOperations), ctx, id, maxResults)
}

// StartSQLInstance mocks base method.
func (m *MockSQLClient) StartSQLInstance(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartSQLInstance", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartSQLInstance indicates an expected call of StartSQLInstance.
func (mr *MockSQLClientMockRecorder) StartSQLInstance(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartSQLInstance", reflect.TypeOf((*MockSQLClient)(nil).StartSQLInstance), ctx, id)
}

// StopSQLInstance mocks base method.
func (m *MockSQLClient) StopSQLInstance(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopSQLInstance", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopSQLInstance indicates an expected call of StopSQLInstance.
func (mr *MockSQLClientMockRecorder) StopSQLInstance(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopSQLInstance", reflect.TypeOf((*MockSQLClient)(nil).StopSQLInstance), ctx, id)
}
