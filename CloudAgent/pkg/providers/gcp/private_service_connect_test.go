package gcp

import (
	"context"
	"testing"

	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	"github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbgcpsvc "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

var (
	testStatusString = "ACCEPTED"
	testStatusProto  = pbgcpsvc.PscStatus_ACCEPTED
	testIP           = "testip"
)

func TestCreateForwardingRule(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	ctx := context.Background()
	option := CreatePSCOption{
		ResourceID:              resourceID,
		Namespace:               namespace,
		ProjectID:               testGCPProjectID,
		Region:                  "test-region",
		NetworkVPC:              "test-mgmt-vpc",
		Target:                  "test-target",
		PrivateServiceConnectIP: "PSC-ip",
		ExtraLabels:             map[string]string{"envid": "env"},
	}
	require.NoError(t, provider.CreatePrivateServiceConnect(ctx, option))

	psc := gcccomputeg.ComputeForwardingRule{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}

	require.NoError(t, provider.kc.Get(ctx, objKey, &psc))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreatePrivateServiceConnect(ctx, option)))
	annotationRes, ok := psc.Annotations[cnrmProjectIDAnnotation]
	assert.True(t, ok)
	assert.Equal(t, testGCPProjectID, annotationRes)
	assert.Equal(t, map[string]string{"project": "risingwave", "envid": "env"}, psc.Labels)
}

func TestDeleteForwardingRule(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	psc := gcccomputeg.ComputeForwardingRule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&psc)
	provider := CreateFakeProvider(client)
	ctx := context.Background()
	require.NoError(t, provider.DeletePrivateServiceConnect(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	psc = gcccomputeg.ComputeForwardingRule{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &psc)))
	assert.True(t, utils.IsErrNotFound(provider.DeletePrivateServiceConnect(ctx, namespace, resourceID)))
}

func TestGetForwardingRule(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	tests := []struct {
		description  string
		psc          *gcccomputeg.ComputeForwardingRule
		expectedMeta *PrivateServiceConnectMeta
		errCode      *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			psc: &gcccomputeg.ComputeForwardingRule{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcccomputeg.ComputeForwardingRuleStatus{
					Conditions: []v1alpha1.Condition{
						{
							Type:   v1alpha1.ReadyConditionType,
							Status: v1.ConditionTrue,
						},
					},
					PscConnectionStatus: &testStatusString,
				},
				Spec: gcccomputeg.ComputeForwardingRuleSpec{
					IpAddress: &gcccomputeg.ForwardingruleIpAddress{
						Ip: &testIP,
					},
				},
			},
			expectedMeta: &PrivateServiceConnectMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PSCStatus: testStatusProto,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			psc: &gcccomputeg.ComputeForwardingRule{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcccomputeg.ComputeForwardingRuleStatus{
					Conditions: []v1alpha1.Condition{
						{
							Type:   "NotReady",
							Status: v1.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &PrivateServiceConnectMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"status\":\"True\",\"type\":\"NotReady\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is in error state",
			psc: &gcccomputeg.ComputeForwardingRule{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcccomputeg.ComputeForwardingRuleStatus{
					Conditions: []v1alpha1.Condition{
						{
							Type:    v1alpha1.ReadyConditionType,
							Status:  v1.ConditionFalse,
							Message: "resource in error state",
						},
					},
				},
			},
			expectedMeta: &PrivateServiceConnectMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"message\":\"resource in error state\",\"status\":\"False\",\"type\":\"Ready\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			psc: &gcccomputeg.ComputeForwardingRule{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "random namespace",
					Name:      "random id",
				},
			},
			expectedMeta: &PrivateServiceConnectMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.psc)
		provider := CreateFakeProvider(client)
		const (
			resourceID = "resource"
			namespace  = "ns"
		)

		meta, err := provider.GetPrivateServiceConnect(context.Background(), namespace, resourceID)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, meta, tt.expectedMeta, "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
		}
	}
}
