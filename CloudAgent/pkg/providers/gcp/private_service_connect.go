package gcp

import (
	"context"
	"maps"

	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	"github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbgcpsvc "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsgcp "github.com/risingwavelabs/cloudagent/pkg/utils/gcp"
)

type CreatePSCOption struct {
	Namespace               string
	ExtraLabels             map[string]string
	ResourceID              string
	ProjectID               string
	Region                  string
	Target                  string
	NetworkVPC              string
	PrivateServiceConnectIP string
}

type PrivateServiceConnectMeta struct {
	Status    *pbresource.Status
	PSCStatus pbgcpsvc.PscStatus
}

func (provider *Provider) CreatePrivateServiceConnect(ctx context.Context, option CreatePSCOption) error {
	loadbalacingScheme := ""
	labels := map[string]string{
		utils.TagProjectKey: utils.TagProjectValue,
	}
	maps.Copy(labels, option.ExtraLabels)
	forwardingRule := &gcccomputeg.ComputeForwardingRule{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				cnrmProjectIDAnnotation: option.ProjectID,
			},
			Labels: labels,
		},
		Spec: gcccomputeg.ComputeForwardingRuleSpec{
			Target: &gcccomputeg.ForwardingruleTarget{
				TargetTCPProxyRef: &v1alpha1.ResourceRef{
					External: option.Target,
				},
			},
			IpAddress: &gcccomputeg.ForwardingruleIpAddress{
				// AddressRef: &v1alpha1.ResourceRef{
				// 	External: option.PrivateServiceConnectIP,
				// },
				Ip: &option.PrivateServiceConnectIP,
			},
			NetworkRef: &v1alpha1.ResourceRef{
				External: option.NetworkVPC,
			},
			Location:            option.Region,
			LoadBalancingScheme: &loadbalacingScheme,
		},
	}

	err := provider.kc.Create(ctx, forwardingRule)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("forwarding rule %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create forwarding rule")
	}

	return nil
}

func (provider *Provider) DeletePrivateServiceConnect(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[gcccomputeg.ComputeForwardingRule](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("forwarding rule %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete forwarding rule %s", resourceID)
	}
	return nil
}
func (provider *Provider) GetPrivateServiceConnect(ctx context.Context, namespace, resourceID string) (*PrivateServiceConnectMeta, error) {
	psc, err := k8s.GetResource[gcccomputeg.ComputeForwardingRule](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &PrivateServiceConnectMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get forwarding rule %s", resourceID)
	}

	status := utilsgcp.KccConditionToResourceStatus(psc.Status.Conditions)

	var pscStatus string
	if psc.Status.PscConnectionStatus != nil {
		pscStatus = *psc.Status.PscConnectionStatus
	}
	return &PrivateServiceConnectMeta{
		Status:    status,
		PSCStatus: utilsgcp.PSCStatusToProto(pscStatus),
	}, nil
}
