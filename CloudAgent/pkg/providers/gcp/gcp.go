package gcp

import (
	"context"

	rbacv1 "k8s.io/api/rbac/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbcfggcp "github.com/risingwavelabs/cloudagent/pbgen/config/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/utils/etagcache"
)

type Provider struct {
	kc        *k8s.KubernetesClient
	gcsClient GcsClient
	sqlClient SQLClient
	cache     *etagcache.Cache
}

type NewProviderOption struct {
	Kc        *k8s.KubernetesClient
	GCPConfig *pbcfggcp.Config
}

func NewProvider(ctx context.Context, option NewProviderOption) (*Provider, error) {
	if option.Kc == nil {
		return nil, eris.New("Kubernetes client cannot be nil")
	}
	if option.GCPConfig == nil {
		return nil, eris.New("Kubernetes client cannot be nil")
	}

	gcsClient, err := newGcsClient(ctx, option.GCPConfig)
	if err != nil {
		return nil, err
	}

	sqlClient, err := newSQLClient(ctx, option.GCPConfig)
	if err != nil {
		return nil, err
	}

	return &Provider{
		kc:        option.Kc,
		gcsClient: &GcsClientImpl{gcsClient},
		sqlClient: sqlClient,
		cache:     etagcache.NewCache(),
	}, nil
}

// for testing purposes only.
func CreateFakeProvider(client k8sclient.Client) *Provider {
	k8sClient := &k8s.KubernetesClient{
		Client:    client,
		Interface: fake.NewClientset(&rbacv1.ClusterRole{}),

		TaskConfig: taskconfig.Config{
			Image:          "image",
			ServiceAccount: "serviceAccount",
			PullPolicy:     "Always",
		},
	}
	return &Provider{
		kc: k8sClient,
	}
}
