package gcp

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"google.golang.org/api/iterator"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskgcp "github.com/risingwavelabs/cloudagent/pbgen/task/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type DeleteDirectoryOptions struct {
	TaskID        string
	TaskNamespace string
	DirectoryName string
	BucketName    string
}

const (
	samplingRate        = 5
	deletionParallelism = 100
	deletionRetry       = 3
	cloneParallelism    = 10
	cloneTaskRetry      = 3
)

// creates a task in taskrunner for the async deletion of data directories.
func (provider *Provider) RunDataDirectoryDeletionTask(ctx context.Context, options DeleteDirectoryOptions) error {
	err := provider.kc.StartTaskRunner(ctx, options.TaskID, options.TaskNamespace, &pbtask.Task{
		Task: &pbtask.Task_GcsDirectoryCleanUpTask{
			GcsDirectoryCleanUpTask: &pbtaskgcp.GCSDirectoryCleanUpTask{
				Bucket:    options.BucketName,
				Directory: options.DirectoryName,
			},
		},
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to start directory deletion task")
	}
	return nil
}

func (provider *Provider) DeleteDataDirectory(ctx context.Context, options DeleteDirectoryOptions) error {
	var err error
	for i := 0; i < deletionRetry; i++ {
		err = provider.deleteDataDirectoryInner(ctx, options)
		if err == nil {
			return nil
		}
	}
	return err
}

func (provider *Provider) deleteDataDirectoryInner(ctx context.Context, options DeleteDirectoryOptions) error {
	err := provider.GetDataDirectory(ctx, options)
	if err != nil {
		if utils.IsErrNotFound(err) {
			return nil
		}
		return eris.Wrap(err, "error getting directory")
	}

	var delErrors []error
	var mu sync.Mutex
	directory := ensureDirectorySuffix(options.DirectoryName)
	objectsIter := provider.gcsClient.GetObjectIterator(ctx, options.BucketName, directory)

	// use parallelism to speed up deletion. Most of the time is spent waiting for the API
	workerPool := make(chan bool, deletionParallelism)
	failedToDelete := 0
	for {
		attrs, err := objectsIter.Next()
		if err == iterator.Done {
			break
		} else if err != nil {
			return eris.Wrap(err, "failed to get gcs object")
		}

		workerPool <- true
		go func(failedToDelete *int) {
			defer func() { <-workerPool }()
			if err := provider.gcsClient.DeleteObject(ctx, options.BucketName, attrs.Name); err != nil {
				mu.Lock()
				if *failedToDelete%samplingRate == 0 {
					delErrors = append(delErrors, err)
				}

				*failedToDelete++
				mu.Unlock()
			}
		}(&failedToDelete)
	}

	// wait for all workers to finish
	for i := 0; i < cap(workerPool); i++ {
		workerPool <- true
	}

	// delete channel
	close(workerPool)

	if failedToDelete > 0 {
		return eris.Wrapf(eris.Join(delErrors...), "failed to delete %d objects", failedToDelete)
	}

	return nil
}

func (provider *Provider) GetDataDirectoryDeletionStatus(ctx context.Context, options DeleteDirectoryOptions) (*k8s.TaskStatus, error) {
	status, err := provider.kc.GetTaskStatus(ctx, options.TaskID, options.TaskNamespace)
	return status, err
}

func (provider *Provider) GetDataDirectory(ctx context.Context, options DeleteDirectoryOptions) error {
	directory := ensureDirectorySuffix(options.DirectoryName)
	objectsIter := provider.gcsClient.GetObjectIterator(ctx, options.BucketName, directory)
	_, err := objectsIter.Next()
	if err == iterator.Done {
		return eris.Errorf("data directory %s is not found", options.DirectoryName).WithCode(eris.CodeNotFound)
	} else if err != nil {
		return eris.Wrapf(err, "error finding directory %s", options.DirectoryName)
	}

	return nil
}

func ensureDirectorySuffix(directory string) string {
	if !strings.HasSuffix(directory, "/") {
		directory += "/"
	}

	return directory
}

func createDstObjKey(srcObjKey, srcDir, dstDir string) (string, error) {
	if !strings.Contains(srcObjKey, srcDir) {
		return "", eris.New(fmt.Sprintf("invalid input, source directory %v must be part of object path %v", srcDir, srcObjKey))
	}
	srcDir = strings.ReplaceAll(srcDir, "/", "")
	dstDir = strings.ReplaceAll(dstDir, "/", "")
	return strings.ReplaceAll(srcObjKey, srcDir, dstDir), nil
}

// creates a task in taskrunner for the async cloning of data directories.
func (provider *Provider) RunDataDirectoryCloneTask(ctx context.Context, options clone.Options) error {
	if !strings.HasSuffix(options.SourceDirectoryName, "/") {
		options.SourceDirectoryName += "/"
	}
	err := provider.kc.StartTaskRunner(ctx, options.TaskID, options.TaskNamespace, &pbtask.Task{
		Task: &pbtask.Task_GcpDirectoryCloneTask{
			GcpDirectoryCloneTask: &pbtaskgcp.GCSDirectoryCloneTask{
				SourceDirectoryName:      options.SourceDirectoryName,
				SourceBucketName:         options.SourceBucketName,
				DestinationDirectoryName: options.DestinationDirectoryName,
				DestinationBucketName:    options.DestinationBucketName,
				Cursor:                   options.Cursor,
				CloneSize:                options.CloneSize,
			},
		},
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to start directory deletion task")
	}
	return nil
}

func (provider *Provider) CloneDataDirectory(ctx context.Context, options clone.Options) error {
	var err error
	buildCopyReq := func(srcObj, dstObj string) GCSCopyRequest {
		return GCSCopyRequest{
			SourceBucket: options.SourceBucketName,
			SourceKey:    srcObj,
			SinkBucket:   options.DestinationBucketName,
			SinkKey:      dstObj,
		}
	}

	buildListReq := func() GCSListRequest {
		return GCSListRequest{
			Bucket:    options.SourceBucketName,
			Directory: options.SourceDirectoryName,
			Marker:    options.Cursor,
			MaxSize:   int(options.CloneSize),
		}
	}
	for i := 0; i < cloneTaskRetry; i++ {
		err = clone.DataDirectoryInner[GCSCopyRequest, GCSListRequest](ctx, provider.gcsClient, options, buildCopyReq, buildListReq)
		if err == nil {
			return nil
		}
	}
	return err
}
