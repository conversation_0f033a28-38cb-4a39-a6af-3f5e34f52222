package gcp

import (
	"context"
	"slices"

	gccsql "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/sql/v1beta1"
	"github.com/risingwavelabs/eris"
	gapisql "google.golang.org/api/sqladmin/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type CreateSQLInstanceOption struct {
	ResourceID string
	Namespace  string
	ProjectID  string
	Region     string
	Spec       *pbgcp.SQLInstanceSpec
	Labels     map[string]string
}

func (provider *Provider) CreateSQLInstance(ctx context.Context, option CreateSQLInstanceOption) error {
	spec, err := conversion.FromSQLInstanceSpecProto(option.Spec)
	if err != nil {
		return eris.Wrapf(err, "failed to convert sqlinstance spec proto: %v, error: %s", option.Spec, err)
	}
	spec.Region = utils.Ptr(option.Region)
	err = provider.kc.Create(ctx, &gccsql.SQLInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				cnrmProjectIDAnnotation: option.ProjectID,
			},
			Labels: option.Labels,
		},
		Spec: *spec,
	})
	if err != nil && k8sErrors.IsAlreadyExists(err) {
		return eris.Errorf("sqlinstance already exists: %v", option).WithCode(eris.CodeAlreadyExists)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to create sqlinstance: %v", option)
	}
	return nil
}

func (provider *Provider) DeleteSQLInstance(ctx context.Context, resourceID string, namespace string) error {
	err := k8s.DeleteResource[gccsql.SQLInstance](ctx, provider.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("sqlinstance %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to delete sqlinstance %s", resourceID)
	}
	return nil
}

func (provider *Provider) StartSQLInstance(ctx context.Context, resourceID string, _ string) error {
	_, instanceStatus, err := provider.getSQLInstanceAndStatus(ctx, resourceID)
	if err != nil {
		return err
	}
	if instanceStatus == pbgcp.SQLInstanceStatus_UPDATING || instanceStatus == pbgcp.SQLInstanceStatus_UP_TO_DATE {
		return eris.New("sqlinstance is in updating state").WithCode(eris.CodeAlreadyExists)
	}
	return provider.sqlClient.StartSQLInstance(ctx, resourceID)
}

func (provider *Provider) StopSQLInstance(ctx context.Context, resourceID string, _ string) error {
	_, instanceStatus, err := provider.getSQLInstanceAndStatus(ctx, resourceID)
	if err != nil {
		return err
	}
	if instanceStatus == pbgcp.SQLInstanceStatus_UPDATING || instanceStatus == pbgcp.SQLInstanceStatus_STOPPED {
		return eris.New("sqlinstance is in updating state").WithCode(eris.CodeAlreadyExists)
	}
	return provider.sqlClient.StopSQLInstance(ctx, resourceID)
}

type SQLInstanceMeta struct {
	Status           *pbresource.Status
	PrivateIPAddress *string
	InstanceStatus   pbgcp.SQLInstanceStatus
}

func (provider *Provider) GetSQLInstance(ctx context.Context, resourceID string, namespace string) (*SQLInstanceMeta, error) {
	sqlinstance, instanceStatus, err := provider.getSQLInstanceAndStatus(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		_, err := k8s.GetResource[gccsql.SQLInstance](ctx, provider.kc, resourceID, namespace)
		if err != nil && k8sErrors.IsNotFound(err) {
			return &SQLInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_FOUND},
			}, nil
		}
		if err != nil {
			return nil, eris.Wrapf(err, "failed to get sqlinstance %s", resourceID)
		}
		// gcp resource is deleted but not synced to k8s
		return &SQLInstanceMeta{
			Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
		}, nil
	}
	if err != nil {
		return nil, err
	}
	var privateIPAddress *string
	for _, it := range sqlinstance.IpAddresses {
		if it.Type == "PRIVATE" {
			privateIPAddress = &it.IpAddress
		}
	}
	return &SQLInstanceMeta{
		Status:           mapStatus(instanceStatus),
		PrivateIPAddress: privateIPAddress,
		InstanceStatus:   instanceStatus,
	}, nil
}

func (provider *Provider) getSQLInstanceAndStatus(ctx context.Context, resourceID string) (*gapisql.DatabaseInstance, pbgcp.SQLInstanceStatus, error) {
	sqlinstance, err := provider.sqlClient.DescribeSQLInstance(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		return nil, pbgcp.SQLInstanceStatus_UNKNOWN, eris.Errorf("sqlinstance %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return nil, pbgcp.SQLInstanceStatus_UNKNOWN, err
	}
	const RecentOperationLimit = 5
	operations, err := provider.sqlClient.ListOperations(ctx, resourceID, RecentOperationLimit)
	if err != nil {
		return nil, pbgcp.SQLInstanceStatus_UNKNOWN, eris.Wrap(err, "failed to list operations")
	}
	isUpdating := hasUpdatingOperation(operations)
	instanceStatus := mapInstanceStatus(sqlinstance.Settings.ActivationPolicy, isUpdating)
	return sqlinstance, instanceStatus, nil
}

func mapInstanceStatus(activationPolicy string, updating bool) pbgcp.SQLInstanceStatus {
	if updating {
		return pbgcp.SQLInstanceStatus_UPDATING
	}
	switch activationPolicy {
	case "ALWAYS":
		return pbgcp.SQLInstanceStatus_UP_TO_DATE
	case "NEVER":
		return pbgcp.SQLInstanceStatus_STOPPED
	default:
		return pbgcp.SQLInstanceStatus_UNKNOWN
	}
}

func mapStatus(instanceStatus pbgcp.SQLInstanceStatus) *pbresource.Status {
	switch instanceStatus {
	case pbgcp.SQLInstanceStatus_STOPPED:
		return &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
	case pbgcp.SQLInstanceStatus_UPDATING:
		return &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
	case pbgcp.SQLInstanceStatus_UP_TO_DATE:
		return &pbresource.Status{Code: pbresource.StatusCode_READY}
	case pbgcp.SQLInstanceStatus_UNKNOWN:
		return &pbresource.Status{Code: pbresource.StatusCode_UNKNOWN}
	}
	return &pbresource.Status{Code: pbresource.StatusCode_UNKNOWN}
}

func hasUpdatingOperation(operations []*gapisql.Operation) bool {
	updating := []string{"UPDATE", "CREATE", "DELETE"}
	for _, operation := range operations {
		if operation.Status != "DONE" && slices.Contains(updating, operation.OperationType) {
			return true
		}
	}
	return false
}
