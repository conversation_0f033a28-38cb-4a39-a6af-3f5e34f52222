package gcp

import (
	"context"
	"fmt"
	"testing"

	gcciamg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/iam/v1beta1"
	gcciamgalpha "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8sapi "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbgcpsvc "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	testGCPProjectID = "1234"
	testBucket       = "test-bucket"
	testDir          = "test-dir"
	testDir1         = "test-dir1"
	testGSA          = "test-gsa"
)

func TestCreateIAMPolicyMemberObjectAdmin(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	ctx := context.Background()
	option := CreateIAMPolicyMemberOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		AccessOption: GCSAccessOption{
			Bucket:              testBucket,
			Dirs:                []string{testDir},
			Role:                "roles/storage.objectAdmin",
			ConditionExpression: fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/objects/%s/\")", testBucket, testDir),
		},
		ProjectID:         testGCPProjectID,
		IAMServiceAccount: testGSA,
	}
	require.NoError(t, provider.CreateIAMPolicyMember(ctx, option))

	member := gcciamg.IAMPolicyMember{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}

	require.NoError(t, provider.kc.Get(ctx, objKey, &member))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMPolicyMember(ctx, option)))
	annotationRes, ok := member.Annotations[cnrmProjectIDAnnotation]
	assert.True(t, ok)
	assert.Equal(t, testGCPProjectID, annotationRes)

	assert.Equal(t, fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/objects/%s/\")", testBucket, testDir), member.Spec.Condition.Expression)
}

func TestCreateIAMPolicyObjectViewer(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	ctx := context.Background()
	option := CreateIAMPolicyMemberOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		AccessOption: GCSAccessOption{
			Bucket:              testBucket,
			Dirs:                []string{testDir},
			ConditionExpression: fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/\") || resource.name == \"projects/_/buckets/%s\"", testBucket, testBucket),
			Role:                "roles/storage.objectViewer",
		},
		ProjectID:         testGCPProjectID,
		IAMServiceAccount: testGSA,
	}
	require.NoError(t, provider.CreateIAMPolicyMember(ctx, option))

	member := gcciamg.IAMPolicyMember{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}

	require.NoError(t, provider.kc.Get(ctx, objKey, &member))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMPolicyMember(ctx, option)))
	annotationRes, ok := member.Annotations[cnrmProjectIDAnnotation]
	assert.True(t, ok)
	assert.Equal(t, testGCPProjectID, annotationRes)

	assert.Equal(t, fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/\") || resource.name == \"projects/_/buckets/%s\"", testBucket, testBucket), member.Spec.Condition.Expression)
}

func TestGCSAccessOptionFromProtoValidAdminSingleDir(t *testing.T) {
	protoOption := &pbgcpsvc.GCSAccessOption{
		Bucket: testBucket,
		Dir:    testDir,
		Role:   pbgcpsvc.GCSRole_OBJECT_ADMIN,
	}

	option, err := FromGCSAccessOptionProto(protoOption)
	assert.NoError(t, err)

	assert.Equal(t, "roles/storage.objectAdmin", option.Role)
	assert.Equal(t, fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/objects/%s/\")", testBucket, testDir), option.ConditionExpression)
}

func TestGCSAccessOptionFromProtoValidAdmin(t *testing.T) {
	protoOption := &pbgcpsvc.GCSAccessOption{
		Bucket: testBucket,
		Dirs:   []string{testDir, testDir1},
		Role:   pbgcpsvc.GCSRole_OBJECT_ADMIN,
	}

	option, err := FromGCSAccessOptionProto(protoOption)
	assert.NoError(t, err)

	assert.Equal(t, "roles/storage.objectAdmin", option.Role)
	assert.Equal(t, fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/objects/%s/\") || resource.name.startsWith(\"projects/_/buckets/%s/objects/%s/\")", testBucket, testDir, testBucket, testDir1), option.ConditionExpression)
}

func TestGCSAccessOptionFromProtoValidViewerSingleDir(t *testing.T) {
	protoOption := &pbgcpsvc.GCSAccessOption{
		Bucket: testBucket,
		Dir:    testDir,
		Role:   pbgcpsvc.GCSRole_OBJECT_VIEWER,
	}

	option, err := FromGCSAccessOptionProto(protoOption)
	assert.NoError(t, err)

	assert.Equal(t, "roles/storage.objectViewer", option.Role)
	assert.Equal(t, fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/\") || resource.name == \"projects/_/buckets/%s\"", testBucket, testBucket), option.ConditionExpression)
}

func TestGCSAccessOptionFromProtoValidViewer(t *testing.T) {
	protoOption := &pbgcpsvc.GCSAccessOption{
		Bucket: testBucket,
		Dirs:   []string{testDir, testDir1},
		Role:   pbgcpsvc.GCSRole_OBJECT_VIEWER,
	}

	option, err := FromGCSAccessOptionProto(protoOption)
	assert.NoError(t, err)

	assert.Equal(t, "roles/storage.objectViewer", option.Role)
	assert.Equal(t, fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/\") || resource.name == \"projects/_/buckets/%s\"", testBucket, testBucket), option.ConditionExpression)
}

func TestGCSAccessOptionFromProtoInvalid(t *testing.T) {
	protoOption := &pbgcpsvc.GCSAccessOption{
		Bucket: "",
		Dir:    "",
		Role:   pbgcpsvc.GCSRole_OBJECT_ADMIN,
	}

	_, err := FromGCSAccessOptionProto(protoOption)
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(err))
}

func TestCreateIAMPolicyKSABinding(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID  = "resource"
		namespace   = "ns"
		saName      = "sa"
		saNamespace = "ns"
	)
	ctx := context.Background()
	option := CreateIAMPolicyKSABindingOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		KSA: &KubernetesServiceAccount{
			Name:      saName,
			Namespace: saNamespace,
		},
		ProjectID: testGCPProjectID,
	}
	require.NoError(t, provider.CreateIAMPolicyKSABinding(ctx, option))

	policy := gcciamg.IAMPolicy{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, provider.kc.Get(ctx, objKey, &policy))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMPolicyKSABinding(ctx, option)))

	annotationRes, ok := policy.Annotations[cnrmProjectIDAnnotation]
	assert.True(t, ok)
	assert.Equal(t, testGCPProjectID, annotationRes)
}

func TestCreateIAMPolicyKSABindingInvalid(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID  = "resource"
		namespace   = "ns"
		saName      = "sa"
		saNamespace = "ns"
	)
	ctx := context.Background()
	option := CreateIAMPolicyKSABindingOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		KSA:        &KubernetesServiceAccount{},
	}
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(provider.CreateIAMPolicyKSABinding(ctx, option)))
}

func TestDeleteIAMPolicyMember(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	member := gcciamg.IAMPolicyMember{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&member)
	provider := CreateFakeProvider(client)
	ctx := context.Background()
	require.NoError(t, provider.DeleteIAMPolicyMember(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	member = gcciamg.IAMPolicyMember{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &member)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteIAMPolicyMember(ctx, namespace, resourceID)))
}

func TestDeleteIAMPolicy(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	policy := gcciamg.IAMPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&policy)
	provider := CreateFakeProvider(client)
	ctx := context.Background()
	require.NoError(t, provider.DeleteIAMPolicy(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	policy = gcciamg.IAMPolicy{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &policy)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteIAMPolicy(ctx, namespace, resourceID)))
}

func TestCreateIAMServiceAccount(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client)
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	ctx := context.Background()
	option := CreateIAMServiceAccountOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		ProjectID:  testGCPProjectID,
	}
	require.NoError(t, provider.CreateIAMServiceAccount(ctx, option))

	account := gcciamg.IAMServiceAccount{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, provider.kc.Get(ctx, objKey, &account))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMServiceAccount(ctx, option)))

	annotationRes, ok := account.Annotations[cnrmProjectIDAnnotation]
	assert.True(t, ok)
	assert.Equal(t, testGCPProjectID, annotationRes)
}

func TestDeleteIAMServiceAccount(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	account := gcciamg.IAMServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&account)
	provider := CreateFakeProvider(client)
	ctx := context.Background()
	require.NoError(t, provider.DeleteIAMServiceAccount(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	account = gcciamg.IAMServiceAccount{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &account)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteIAMServiceAccount(ctx, namespace, resourceID)))
}

func TestGetIAMPolicy(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	tests := []struct {
		description  string
		policy       *gcciamg.IAMPolicyMember
		expectedMeta *IAMPolicyMeta
		errCode      *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			policy: &gcciamg.IAMPolicyMember{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMPolicyMemberStatus{
					Conditions: []gcciamgalpha.Condition{
						{
							Type:   gcciamgalpha.ReadyConditionType,
							Status: k8sapi.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			policy: &gcciamg.IAMPolicyMember{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMPolicyMemberStatus{
					Conditions: []gcciamgalpha.Condition{
						{
							Type:   "NotReady",
							Status: k8sapi.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"status\":\"True\",\"type\":\"NotReady\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is in error state",
			policy: &gcciamg.IAMPolicyMember{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMPolicyMemberStatus{
					Conditions: []gcciamgalpha.Condition{
						{
							Type:    gcciamgalpha.ReadyConditionType,
							Status:  k8sapi.ConditionFalse,
							Message: "resource in error state",
						},
					},
				},
			},
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"message\":\"resource in error state\",\"status\":\"False\",\"type\":\"Ready\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			policy: &gcciamg.IAMPolicyMember{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.policy)
		provider := CreateFakeProvider(client)
		const (
			resourceID = "resource"
			namespace  = "ns"
		)

		meta, err := provider.GetIAMPolicyMember(context.Background(), namespace, resourceID)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, meta, tt.expectedMeta, "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
		}
	}
}

func TestGetIAMRole(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	tests := []struct {
		description    string
		serviceAccount *gcciamg.IAMServiceAccount
		expectedMeta   *IAMServiceAccountMeta
		errCode        *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			serviceAccount: &gcciamg.IAMServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMServiceAccountStatus{
					Email: utils.Ptr(fmt.Sprintf("%s@%s.iam.gserviceaccount.com", resourceID, testGCPProjectID)),
					Conditions: []gcciamgalpha.Condition{
						{
							Type:   gcciamgalpha.ReadyConditionType,
							Status: k8sapi.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				AccountEmail: fmt.Sprintf("%s@%s.iam.gserviceaccount.com", resourceID, testGCPProjectID),
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			serviceAccount: &gcciamg.IAMServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMServiceAccountStatus{
					Conditions: []gcciamgalpha.Condition{
						{
							Type:    "NotReady",
							Status:  k8sapi.ConditionTrue,
							Message: "resource not ready",
						},
					},
				},
			},
			expectedMeta: &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"message\":\"resource not ready\",\"status\":\"True\",\"type\":\"NotReady\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is in error state",
			serviceAccount: &gcciamg.IAMServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMServiceAccountStatus{
					Conditions: []gcciamgalpha.Condition{
						{
							Type:    gcciamgalpha.ReadyConditionType,
							Status:  k8sapi.ConditionFalse,
							Message: "resource in error state",
						},
					},
				},
			},
			expectedMeta: &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"message\":\"resource in error state\",\"status\":\"False\",\"type\":\"Ready\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			serviceAccount: &gcciamg.IAMServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			expectedMeta: &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
		{
			description: "Normal case, email not present in a ready service account",
			serviceAccount: &gcciamg.IAMServiceAccount{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: gcciamg.IAMServiceAccountStatus{
					Conditions: []gcciamgalpha.Condition{
						{
							Type:   gcciamgalpha.ReadyConditionType,
							Status: k8sapi.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "email is nil in a ready service account &{{ } {resource  ns   999 0 0001-01-01 00:00:00 +0000 UTC <nil> <nil> map[] map[] [] [] []} {<nil> <nil> <nil> <nil>} {[{   True Ready}] <nil> <nil> <nil> <nil> <nil>}}",
				},
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.serviceAccount)
		provider := CreateFakeProvider(client)
		const (
			resourceID = "resource"
			namespace  = "ns"
		)

		meta, err := provider.GetIAMServiceAccount(context.Background(), namespace, resourceID)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, meta, tt.expectedMeta, "unexpect result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
		}
	}
}
