package gcp

import (
	"context"

	"github.com/risingwavelabs/eris"
)

func (provider *Provider) GetFile(ctx context.Context, bucket, key string) ([]byte, error) {
	objs, err := provider.gcsClient.ListObjects(ctx, bucket, key)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to list objects for %s/%s", bucket, key)
	}
	if len(objs) == 0 {
		return nil, eris.WithCode(eris.New("file not found"), eris.CodeNotFound)
	}

	attrs, err := provider.gcsClient.HeadObject(ctx, bucket, key)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get attrs for %s/%s", bucket, key)
	}
	etag := attrs.Etag
	val, ok := provider.cache.Get(key, etag)
	if ok {
		return val, nil
	}

	manifest, err := provider.gcsClient.GetObject(ctx, bucket, key)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get reader for %s/%s", bucket, key)
	}
	provider.cache.Set(key, manifest, etag)
	return manifest, nil
}
