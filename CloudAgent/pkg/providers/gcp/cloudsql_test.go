package gcp

import (
	"context"
	"encoding/json"
	"testing"

	gccsql "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/sql/v1beta1"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	gapisql "google.golang.org/api/sqladmin/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func generateSpec(modifiers ...func(*pbgcp.SQLInstanceSpec)) *pbgcp.SQLInstanceSpec {
	proto := &pbgcp.SQLInstanceSpec{
		ResourceId:      "test-sqlinstance",
		InstanceType:    "CLOUD_SQL_INSTANCE",
		DatabaseVersion: "POSTGRES_14",
		Region:          "region1",
		RootPassword: &pbgcp.RootPassword{
			Value: utils.Ptr("password"),
		},
		Settings: &pbgcp.SQLInstanceSettings{
			Tier:     "test-tier",
			DiskSize: 20,
			IpConfiguration: &pbgcp.IPConfiguration{
				Ipv4Enabled: false,
				PrivateNetworkRef: &pbgcp.PrivateNetworkRef{
					External: "external-private-network",
				},
				AllocatedIpRange: "ip-range-name",
			},
			DeletionProtectionEnabled: false,
			DatabaseFlags: []*pbgcp.DatabaseFlag{
				{
					Name:  "flag-name",
					Value: "flag-value",
				},
			},
		},
	}
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func genSQLInstance(modifiers ...func(sqlinstance *gccsql.SQLInstance)) *gccsql.SQLInstance {
	spec, err := conversion.FromSQLInstanceSpecProto(generateSpec())
	if err != nil {
		panic(err)
	}
	sqlinstance := &gccsql.SQLInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-sqlinstance",
			Namespace: "test-namespace",
		},
		Spec: *spec,
	}
	for _, m := range modifiers {
		m(sqlinstance)
	}
	return sqlinstance
}

func TestProvider_CreateSQLInstance(t *testing.T) {
	tests := []struct {
		name          string
		option        CreateSQLInstanceOption
		initObjs      []k8sclient.Object
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "normal case",
			option: CreateSQLInstanceOption{
				ResourceID: "test-sqlinstance",
				Namespace:  "test-namespace",
				Spec:       generateSpec(),
				Region:     "region1",
				Labels: map[string]string{
					"key1": "value1",
					"key2": "value2",
				},
			},
			wantErr: false,
		},
		{
			name: "already exist",
			initObjs: []k8sclient.Object{
				genSQLInstance(),
			},
			option: CreateSQLInstanceOption{
				ResourceID: "test-sqlinstance",
				Namespace:  "test-namespace",
				Spec:       generateSpec(),
				Region:     "region1",
				Labels: map[string]string{
					"key1": "value1",
					"key2": "value2",
				},
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.CreateSQLInstance(ctx, tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)

			psql, err := k8s.GetResource[gccsql.SQLInstance](ctx, c, "test-sqlinstance", "test-namespace")
			require.NoError(t, err)
			assert.EqualValues(t, "test-sqlinstance", psql.Name)
			assert.EqualValues(t, "test-namespace", psql.Namespace)
			assert.EqualValues(t, map[string]string{"key1": "value1", "key2": "value2"}, psql.Labels)

			expectSpec, err := conversion.FromSQLInstanceSpecProto(tt.option.Spec)
			require.NoError(t, err)
			expectedJSON := prettyJSON(t, expectSpec)
			actualJSON := prettyJSON(t, psql.Spec)
			assert.JSONEq(t, expectedJSON, actualJSON)

			err = p.CreateSQLInstance(ctx, tt.option)
			assert.True(t, utils.IsErrAlreadyExists(err))
		})
	}
}

func TestProvider_DeleteSQLInstance(t *testing.T) {
	ctx := context.Background()
	SQLInstance := genSQLInstance()
	c := fake.NewClient(SQLInstance)
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	err := p.DeleteSQLInstance(ctx, "test-sqlinstance", "test-namespace")
	require.NoError(t, err)
	err = p.DeleteSQLInstance(ctx, "test-sqlinstance", "test-namespace")
	assert.True(t, utils.IsErrNotFound(err))

	_, err = k8s.GetResource[gccsql.SQLInstance](ctx, c, "test-sqlinstance", "test-namespace")
	require.True(t, k8sErrors.IsNotFound(err))
}

func prettyJSON(t *testing.T, x any) string {
	j, err := json.MarshalIndent(x, "", "  ")
	assert.NoError(t, err)
	return string(j)
}

func TestProvider_StartSQLInstance(t *testing.T) {
	tests := []struct {
		name          string
		initObjs      []k8sclient.Object
		mockSQLClient func(client *MockSQLClient)
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "not found",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "already exists (starting)",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(&gapisql.DatabaseInstance{
					Settings: &gapisql.Settings{ActivationPolicy: "ALWAYS"},
				}, nil)
				c.EXPECT().ListOperations(gomock.Any(), "test-sqlinstance", int32(5)).Return([]*gapisql.Operation{
					{Status: "RUNNING", OperationType: "UPDATE"},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "already exists (available)",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(&gapisql.DatabaseInstance{
					Settings: &gapisql.Settings{ActivationPolicy: "ALWAYS"},
				}, nil)
				c.EXPECT().ListOperations(gomock.Any(), "test-sqlinstance", int32(5)).Return([]*gapisql.Operation{
					{Status: "DONE", OperationType: "UPDATE"},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "scheduled",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(&gapisql.DatabaseInstance{
					Settings: &gapisql.Settings{ActivationPolicy: "NEVER"},
				}, nil)
				c.EXPECT().ListOperations(gomock.Any(), "test-sqlinstance", int32(5)).Return([]*gapisql.Operation{
					{Status: "DONE", OperationType: "UPDATE"},
				}, nil)
				c.EXPECT().StartSQLInstance(gomock.Any(), "test-sqlinstance").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			sqlClient := NewMockSQLClient(ctrl)
			p := &Provider{
				kc:        &k8s.KubernetesClient{Client: c},
				sqlClient: sqlClient,
			}

			if tt.mockSQLClient != nil {
				tt.mockSQLClient(sqlClient)
			}
			err := p.StartSQLInstance(ctx, "test-sqlinstance", "test-namespace")
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_StopSQLInstance(t *testing.T) {
	tests := []struct {
		name          string
		option        CreateSQLInstanceOption
		initObjs      []k8sclient.Object
		mockSQLClient func(client *MockSQLClient)
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "not found",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name:          "already exists (stopping)",
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(&gapisql.DatabaseInstance{
					Settings: &gapisql.Settings{ActivationPolicy: "NEVER"},
				}, nil)
				c.EXPECT().ListOperations(gomock.Any(), "test-sqlinstance", int32(5)).Return([]*gapisql.Operation{
					{Status: "RUNNING", OperationType: "UPDATE"},
				}, nil)
			},
		},
		{
			name: "already exists (stopped)",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(&gapisql.DatabaseInstance{
					Settings: &gapisql.Settings{ActivationPolicy: "NEVER"},
				}, nil)
				c.EXPECT().ListOperations(gomock.Any(), "test-sqlinstance", int32(5)).Return([]*gapisql.Operation{
					{Status: "DONE", OperationType: "UPDATE"},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "scheduled",
			mockSQLClient: func(c *MockSQLClient) {
				c.EXPECT().DescribeSQLInstance(gomock.Any(), "test-sqlinstance").Return(&gapisql.DatabaseInstance{
					Settings: &gapisql.Settings{ActivationPolicy: "ALWAYS"},
				}, nil)
				c.EXPECT().ListOperations(gomock.Any(), "test-sqlinstance", int32(5)).Return([]*gapisql.Operation{
					{Status: "DONE", OperationType: "UPDATE"},
				}, nil)
				c.EXPECT().StopSQLInstance(gomock.Any(), "test-sqlinstance").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			sqlClient := NewMockSQLClient(ctrl)
			p := &Provider{
				kc:        &k8s.KubernetesClient{Client: c},
				sqlClient: sqlClient,
			}

			if tt.mockSQLClient != nil {
				tt.mockSQLClient(sqlClient)
			}
			err := p.StopSQLInstance(ctx, "test-sqlinstance", "test-namespace")
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)
		})
	}
}
