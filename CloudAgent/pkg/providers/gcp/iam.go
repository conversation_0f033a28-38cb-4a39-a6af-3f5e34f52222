package gcp

import (
	"context"
	"fmt"
	"strings"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	gcciamg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/iam/v1beta1"
	gcciamgalpha "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbgcpsvc "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	utilsgcp "github.com/risingwavelabs/cloudagent/pkg/utils/gcp"
)

type IAMPolicyMeta struct {
	Status *pbresource.Status
}

type IAMServiceAccountMeta struct {
	Status       *pbresource.Status
	AccountEmail string
}

type AccessOption interface{}

type GCSAccessOption struct {
	Bucket              string
	Dirs                []string
	Role                string
	ConditionExpression string
}

type KubernetesServiceAccount struct {
	Name      string
	Namespace string
}

type CreateIAMPolicyMemberOption struct {
	Namespace         string
	ResourceID        string
	IAMServiceAccount string
	ProjectID         string
	AccessOption      AccessOption
}

type CreateIAMPolicyKSABindingOption struct {
	Namespace         string
	ResourceID        string
	IAMServiceAccount string
	ProjectID         string
	KSA               *KubernetesServiceAccount
}

type CreateIAMServiceAccountOption struct {
	Namespace  string
	ResourceID string
	ProjectID  string
}

const cnrmProjectIDAnnotation = "cnrm.cloud.google.com/project-id"

func (provider *Provider) CreateIAMPolicyMember(ctx context.Context, option CreateIAMPolicyMemberOption) error {
	gcsAccessOption, ok := option.AccessOption.(GCSAccessOption)
	if !ok {
		return eris.Errorf("invalid access option %v", option.AccessOption).WithCode(eris.CodeInvalidArgument)
	}
	member := fmt.Sprintf("serviceAccount:%s@%s.iam.gserviceaccount.com", option.IAMServiceAccount, option.ProjectID)
	iamPolicyMember := &gcciamg.IAMPolicyMember{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				cnrmProjectIDAnnotation: option.ProjectID,
			},
		},
		Spec: gcciamg.IAMPolicyMemberSpec{
			ResourceRef: gcciamgalpha.IAMResourceRef{
				Kind:     "StorageBucket",
				External: gcsAccessOption.Bucket,
			},
			Member: &member,
			Role:   gcsAccessOption.Role,
			Condition: &gcciamg.PolicymemberCondition{
				Title:      "gcs-folder-access",
				Expression: gcsAccessOption.ConditionExpression,
			},
		},
	}

	err := provider.kc.Create(ctx, iamPolicyMember)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("IAM policy %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create IAMPolicyMember")
	}

	return nil
}

// bind kubernetes account to GSA account.
func (provider *Provider) CreateIAMPolicyKSABinding(ctx context.Context, option CreateIAMPolicyKSABindingOption) error {
	ksaName := option.KSA.Name
	ksaNamespace := option.KSA.Namespace

	if ksaName == "" || ksaNamespace == "" {
		return eris.New("Name and namespace cannot be empty").WithCode(eris.CodeInvalidArgument)
	}

	iamSAName := option.IAMServiceAccount

	iamPolicyRoleBinding := &gcciamg.IAMPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				cnrmProjectIDAnnotation: option.ProjectID,
			},
		},
		Spec: gcciamg.IAMPolicySpec{
			ResourceRef: gcciamgalpha.IAMResourceRef{
				Kind:     "IAMServiceAccount",
				External: fmt.Sprintf("projects/%s/serviceAccounts/%s@%s.iam.gserviceaccount.com", option.ProjectID, iamSAName, option.ProjectID),
			},
			Bindings: []gcciamg.PolicyBindings{
				{
					Members: []string{
						fmt.Sprintf("serviceAccount:%s.svc.id.goog[%s/%s]", option.ProjectID, ksaNamespace, ksaName),
					},
					Role: "roles/iam.workloadIdentityUser",
				},
			},
		},
	}

	err := provider.kc.Create(ctx, iamPolicyRoleBinding)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("KSA binding %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create IAMPolicy")
	}

	return nil
}

func (provider *Provider) DeleteIAMPolicyMember(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[gcciamg.IAMPolicyMember](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("IAM policy %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete IAM policy %s", resourceID)
	}
	return nil
}

func (provider *Provider) GetIAMPolicyMember(ctx context.Context, namespace, resourceID string) (*IAMPolicyMeta, error) {
	policy, err := k8s.GetResource[gcciamg.IAMPolicyMember](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get IAM role %s", resourceID)
	}
	status := utilsgcp.KccConditionToResourceStatus(policy.Status.Conditions)
	return &IAMPolicyMeta{
		Status: status,
	}, nil
}

func (provider *Provider) DeleteIAMPolicy(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[gcciamg.IAMPolicy](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("IAM policy %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete IAM policy %s", resourceID)
	}
	return nil
}

func (provider *Provider) GetIAMPolicy(ctx context.Context, namespace, resourceID string) (*IAMPolicyMeta, error) {
	policy, err := k8s.GetResource[gcciamg.IAMPolicy](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, eris.Wrapf(err, "failed to get IAM role %s", resourceID)
	}
	status := utilsgcp.KccConditionToResourceStatus(policy.Status.Conditions)
	return &IAMPolicyMeta{
		Status: status,
	}, nil
}

func (provider *Provider) CreateIAMServiceAccount(ctx context.Context, option CreateIAMServiceAccountOption) error {
	desc := "created and managed by config connector"
	iamServiceAccount := &gcciamg.IAMServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				cnrmProjectIDAnnotation: option.ProjectID,
			},
		},
		Spec: gcciamg.IAMServiceAccountSpec{
			Description: &desc,
		},
	}

	err := provider.kc.Create(ctx, iamServiceAccount)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("IAM service account %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create IAMServiceAccount")
	}

	return nil
}

func (provider *Provider) DeleteIAMServiceAccount(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[gcciamg.IAMServiceAccount](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("IAM service account %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete IAM service account %s", resourceID)
	}
	return nil
}

func (provider *Provider) GetIAMServiceAccount(ctx context.Context, namespace, resourceID string) (*IAMServiceAccountMeta, error) {
	sa, err := k8s.GetResource[gcciamg.IAMServiceAccount](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, eris.Wrapf(err, "failed to get IAM service account %s", resourceID)
	}
	status := utilsgcp.KccConditionToResourceStatus(sa.Status.Conditions)
	var email string

	if status.GetCode() == pbresource.StatusCode_READY {
		if sa.Status.Email == nil {
			logger.FromCtx(ctx).Warnf("email is nil in a ready service account %v", sa)
			return &IAMServiceAccountMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: fmt.Sprintf("email is nil in a ready service account %v", sa),
				},
				AccountEmail: email,
			}, nil
		}
		email = *sa.Status.Email
	}

	return &IAMServiceAccountMeta{
		Status:       status,
		AccountEmail: email,
	}, nil
}

func FromGCSAccessOptionProto(g *pbgcpsvc.GCSAccessOption) (GCSAccessOption, error) {
	bucket := g.GetBucket()
	role := g.GetRole()
	//nolint:staticcheck
	if bucket == "" || (g.GetDir() == "" && len(g.GetDirs()) == 0) {
		return GCSAccessOption{}, eris.New("bucket and directory cannot be empty").WithCode(eris.CodeInvalidArgument)
	}

	var dirs []string
	dirs = append(dirs, g.GetDirs()...)
	//nolint:staticcheck
	if g.GetDir() != "" {
		//nolint:staticcheck
		dirs = append(dirs, g.GetDir())
	}

	option := GCSAccessOption{
		Bucket: bucket,
		Dirs:   dirs,
	}

	switch role {
	case pbgcpsvc.GCSRole_OBJECT_ADMIN:
		var dirConditions []string
		for _, dir := range dirs {
			cond := fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/objects/%s/\")", bucket, dir)
			dirConditions = append(dirConditions, cond)
		}
		option.ConditionExpression = strings.Join(dirConditions, " || ")
		option.Role = "roles/storage.objectAdmin"
	case pbgcpsvc.GCSRole_OBJECT_VIEWER:
		option.ConditionExpression = fmt.Sprintf("resource.name.startsWith(\"projects/_/buckets/%s/\") || resource.name == \"projects/_/buckets/%s\"", bucket, bucket)
		option.Role = "roles/storage.objectViewer"
	case pbgcpsvc.GCSRole_UNKNOWN:
		return GCSAccessOption{}, eris.Errorf("unknown gcs role %v", role).WithCode(eris.CodeInvalidArgument)
	}

	return option, nil
}
