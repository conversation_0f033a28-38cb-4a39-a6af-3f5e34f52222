package gcp

import (
	"context"
	"encoding/base64"
	"fmt"
	"io"
	"net/http"

	gcs "cloud.google.com/go/storage"
	"github.com/risingwavelabs/eris"
	"golang.org/x/oauth2/google"
	"google.golang.org/api/googleapi"
	"google.golang.org/api/iterator"
	gapioption "google.golang.org/api/option"
	gapisql "google.golang.org/api/sqladmin/v1"

	pbcfggcp "github.com/risingwavelabs/cloudagent/pbgen/config/gcp"
)

type ObjectIterator interface {
	Next() (*gcs.ObjectAttrs, error)
}

type BucketHandle interface {
	Attrs(ctx context.Context) (*gcs.BucketAttrs, error)
	Create(ctx context.Context, bucketName string, attrs *gcs.BucketAttrs) error
	Object(name string) *gcs.ObjectHandle
}

type ObjectHandle interface {
	Attrs(ctx context.Context) (*gcs.ObjectAttrs, error)
	NewWriter(ctx context.Context) *gcs.Writer
	NewReader(ctx context.Context) (*gcs.Reader, error)
	CopierFrom(*gcs.ObjectHandle) *gcs.Copier
}

type GCSCopyRequest struct {
	SourceBucket string
	SourceKey    string
	SinkBucket   string
	SinkKey      string
}

type GCSListRequest struct {
	Bucket    string
	Directory string
	Marker    string
	MaxSize   int
}

type GcsClient interface {
	GetObjectIterator(ctx context.Context, bucketName, dataDirectory string) ObjectIterator
	DeleteObject(ctx context.Context, bucketName, objectName string) error
	GetBucket(bucketName string) BucketHandle
	HeadObject(ctx context.Context, bucketName, objectName string) (*gcs.ObjectAttrs, error)
	GetObject(ctx context.Context, bucketName, objectName string) ([]byte, error)
	ListObjects(ctx context.Context, bucketName, objectName string) ([]*gcs.ObjectAttrs, error)
	ListObjectsWithMarker(ctx context.Context, req GCSListRequest) ([]string, string, error)
	CopyObject(ctx context.Context, req GCSCopyRequest) error
}

type SQLClient interface {
	StartSQLInstance(ctx context.Context, id string) error
	StopSQLInstance(ctx context.Context, id string) error
	DescribeSQLInstance(ctx context.Context, id string) (*gapisql.DatabaseInstance, error)
	ListOperations(ctx context.Context, id string, maxResults int32) ([]*gapisql.Operation, error)
}

func newGcsClient(ctx context.Context, gcpCfg *pbcfggcp.Config) (*gcs.Client, error) {
	var (
		client    *gcs.Client
		clientErr error
	)
	switch authType := gcpCfg.GetAuth().(type) {
	case *pbcfggcp.Config_StaticCreds:
		jsonCreds, err := base64.StdEncoding.DecodeString(gcpCfg.GetStaticCreds().GetGoogleApplicationCredentials())
		if err != nil {
			return nil, eris.Wrap(err, "unable to decode gcp static creds")
		}
		client, clientErr = gcs.NewClient(ctx, gapioption.WithCredentialsJSON(jsonCreds))
	case *pbcfggcp.Config_GkeWebIdentity:
		scopes := []string{"https://www.googleapis.com/auth/cloud-platform"}
		sdkCreds, err := google.FindDefaultCredentials(ctx, scopes...)
		if err != nil {
			return nil, eris.Wrap(err, "unable to load GCP SDK config")
		}
		client, clientErr = gcs.NewClient(ctx, gapioption.WithCredentials(sdkCreds))
	default:
		return nil, eris.Errorf("invalid GCP auth type: %v", authType)
	}
	if clientErr != nil {
		return nil, eris.Wrap(clientErr, "failed to create new gcs client")
	}
	return client, nil
}

type GcsClientImpl struct {
	*gcs.Client
}

func (client *GcsClientImpl) GetObjectIterator(ctx context.Context, bucketName, dataDirectory string) ObjectIterator {
	return client.Bucket(bucketName).Objects(ctx, &gcs.Query{
		Prefix: dataDirectory,
	})
}

func (client *GcsClientImpl) GetObjectIteratorWithCursor(ctx context.Context, bucketName, dataDirectory, cursor string) ObjectIterator {
	return client.Bucket(bucketName).Objects(ctx, &gcs.Query{
		Prefix:      dataDirectory,
		StartOffset: cursor,
	})
}

func (client *GcsClientImpl) DeleteObject(ctx context.Context, bucketName, objectName string) error {
	return client.Bucket(bucketName).Object(objectName).Delete(ctx)
}

func (client *GcsClientImpl) GetBucket(bucketName string) BucketHandle {
	return client.Bucket(bucketName)
}

func (client *GcsClientImpl) HeadObject(ctx context.Context, bucketName, objectName string) (*gcs.ObjectAttrs, error) {
	return client.Bucket(bucketName).Object(objectName).Attrs(ctx)
}

func (client *GcsClientImpl) GetObject(ctx context.Context, bucketName, objectName string) ([]byte, error) {
	reader, err := client.Bucket(bucketName).Object(objectName).NewReader(ctx)
	if err != nil {
		return nil, eris.Wrap(err, "failed to get object")
	}
	return io.ReadAll(reader)
}

func (client *GcsClientImpl) ListObjects(ctx context.Context, bucketName, objectName string) ([]*gcs.ObjectAttrs, error) {
	iter := client.Bucket(bucketName).Objects(ctx, &gcs.Query{
		Prefix: objectName,
	})
	objs := []*gcs.ObjectAttrs{}
	for {
		attrs, err := iter.Next()
		if err == iterator.Done {
			break
		} else if err != nil {
			return nil, eris.Wrap(err, "failed to get gcs object")
		}
		objs = append(objs, attrs)
	}
	return objs, nil
}

// iteration size. Returns cursor to be used for the following iteration.
func (client *GcsClientImpl) ListObjectsWithMarker(ctx context.Context, req GCSListRequest) ([]string, string, error) {
	var resCursor string
	count := 0
	iter := client.Bucket(req.Bucket).Objects(ctx, &gcs.Query{
		Prefix:      req.Directory,
		StartOffset: req.Marker,
	})
	var objs []string
	for {
		attrs, err := iter.Next()
		if err == iterator.Done {
			break
		}
		if count == req.MaxSize {
			resCursor = attrs.Name
			break
		}
		if err != nil {
			return nil, "", eris.Wrap(err, "failed to get gcs object")
		}
		count++
		objs = append(objs, attrs.Name)
	}
	return objs, resCursor, nil
}

func (client *GcsClientImpl) CopyObject(ctx context.Context, req GCSCopyRequest) error {
	srcObject := client.Bucket(req.SourceBucket).Object(req.SourceKey)
	dstObject := client.Bucket(req.SinkBucket).Object(req.SinkKey)
	_, err := dstObject.CopierFrom(srcObject).Run(ctx)
	if err != nil {
		return eris.Wrapf(err, "%v", fmt.Sprintf("failed to copy object %v from source", srcObject.ObjectName()))
	}
	return nil
}

type SQLClientImpl struct {
	*gapisql.Service
	projectID string
}

func newSQLClient(ctx context.Context, gcpCfg *pbcfggcp.Config) (*SQLClientImpl, error) {
	var (
		service    *gapisql.Service
		serviceErr error
	)
	switch authType := gcpCfg.GetAuth().(type) {
	case *pbcfggcp.Config_StaticCreds:
		jsonCreds, err := base64.StdEncoding.DecodeString(gcpCfg.GetStaticCreds().GetGoogleApplicationCredentials())
		if err != nil {
			return nil, eris.Wrap(err, "unable to decode gcp static creds")
		}
		service, serviceErr = gapisql.NewService(ctx, gapioption.WithCredentialsJSON(jsonCreds))
	case *pbcfggcp.Config_GkeWebIdentity:
		scopes := []string{"https://www.googleapis.com/auth/cloud-platform"}
		sdkCreds, err := google.FindDefaultCredentials(ctx, scopes...)
		if err != nil {
			return nil, eris.Wrap(err, "unable to load GCP SDK config")
		}
		service, serviceErr = gapisql.NewService(ctx, gapioption.WithCredentials(sdkCreds))
	default:
		return nil, eris.Errorf("invalid GCP auth type: %v", authType)
	}
	if serviceErr != nil {
		return nil, eris.Wrap(serviceErr, "failed to create new gcp client")
	}
	return &SQLClientImpl{
		Service:   service,
		projectID: gcpCfg.GetProjectId(),
	}, nil
}

func (s *SQLClientImpl) StartSQLInstance(ctx context.Context, id string) error {
	_, err := s.Instances.Patch(s.projectID, id, &gapisql.DatabaseInstance{
		Settings: &gapisql.Settings{
			ActivationPolicy: "ALWAYS",
		},
	}).Context(ctx).Do()
	if err != nil {
		return eris.Wrap(err, "failed to start sql instance")
	}
	return nil
}

func (s *SQLClientImpl) StopSQLInstance(ctx context.Context, id string) error {
	_, err := s.Instances.Patch(s.projectID, id, &gapisql.DatabaseInstance{
		Settings: &gapisql.Settings{
			ActivationPolicy: "NEVER",
		},
	}).Context(ctx).Do()
	if err != nil {
		return eris.Wrap(err, "failed to stop sql instance")
	}
	return nil
}

func (s *SQLClientImpl) DescribeSQLInstance(ctx context.Context, id string) (*gapisql.DatabaseInstance, error) {
	res, err := s.Instances.Get(s.projectID, id).Context(ctx).Do()
	if err != nil && isError(err, http.StatusNotFound) {
		return nil, eris.WithCode(eris.Wrap(err, "sql instance not found"), eris.CodeNotFound)
	}
	if err != nil {
		return nil, eris.Wrap(err, "failed to get sql instance")
	}
	return res, nil
}

func isError(err error, code int) bool {
	if err == nil {
		return false
	}
	var ae *googleapi.Error
	ok := eris.As(err, &ae)
	return ok && ae.Code == code
}

func (s *SQLClientImpl) ListOperations(ctx context.Context, instanceID string, maxResults int32) ([]*gapisql.Operation, error) {
	res, err := s.Operations.List(s.projectID).Instance(instanceID).MaxResults(int64(maxResults)).Context(ctx).Do()
	if err != nil {
		return nil, eris.Wrap(err, "failed to list operations for sql instance")
	}
	return res.Items, nil
}
