package gcp

import (
	"context"
	"testing"

	gcs "cloud.google.com/go/storage"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"
	"google.golang.org/api/iterator"

	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"
)

func TestDeleteDataDirectoryTask_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)
	mockObjectIterator := NewMockObjectIterator(ctrl)

	provider := &Provider{
		gcsClient: mockGCSClient,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := DeleteDirectoryOptions{
		TaskID:     "existing-directory",
		BucketName: "my-bucket",
	}

	mockGCSClient.EXPECT().GetObjectIterator(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockObjectIterator).AnyTimes()
	mockGCSClient.EXPECT().DeleteObject(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	ref := mockObjectIterator.EXPECT().Next().Return(&gcs.ObjectAttrs{Name: "existing-directory"}, nil).Times(2)
	mockObjectIterator.EXPECT().Next().Return(nil, iterator.Done).After(ref).AnyTimes()

	err := provider.DeleteDataDirectory(context.Background(), options)
	assert.NoError(t, err, "Expected no error")
}

func TestDeleteDataDirectoryTask_Fail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)
	mockObjectIterator := NewMockObjectIterator(ctrl)

	provider := &Provider{
		gcsClient: mockGCSClient,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := DeleteDirectoryOptions{
		TaskID:     "existing-directory",
		BucketName: "my-bucket",
	}

	mockGCSClient.EXPECT().GetObjectIterator(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockObjectIterator).AnyTimes()
	mockGCSClient.EXPECT().DeleteObject(gomock.Any(), gomock.Any(), gomock.Any()).Return(eris.New("failed to delete 1 objects")).AnyTimes()

	ref := mockObjectIterator.EXPECT().Next().Return(&gcs.ObjectAttrs{Name: "existing-directory"}, nil).Times(2)
	mockObjectIterator.EXPECT().Next().Return(nil, iterator.Done).After(ref).AnyTimes()

	err := provider.deleteDataDirectoryInner(context.Background(), options)
	assert.ErrorIs(t, err, eris.New("failed to delete 1 objects"))
}

func TestCloneDataDirectoryTask_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)
	provider := &Provider{
		gcsClient: mockGCSClient,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := clone.Options{
		TaskID:                   "existing-directory",
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
		Cursor:                   "start",
		CloneSize:                10,
	}

	objs := []string{"my-dir/test1", "my-dir/test2"}
	mockGCSClient.EXPECT().
		ListObjectsWithMarker(
			gomock.Any(),
			GCSListRequest{
				Bucket:    options.SourceBucketName,
				Directory: options.SourceDirectoryName,
				Marker:    options.Cursor,
				MaxSize:   int(options.CloneSize),
			}).Return(objs, "", nil)

	mockGCSClient.EXPECT().CopyObject(gomock.Any(), GCSCopyRequest{SourceKey: "my-dir/test1", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test1", SinkBucket: options.DestinationBucketName}).Return(nil)
	mockGCSClient.EXPECT().CopyObject(gomock.Any(), GCSCopyRequest{SourceKey: "my-dir/test2", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test2", SinkBucket: options.DestinationBucketName}).Return(nil)

	err := provider.CloneDataDirectory(context.Background(), options)
	assert.NoError(t, err, "Expected no error")
}

func TestCloneDataDirectoryTask_Fail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)
	provider := &Provider{
		gcsClient: mockGCSClient,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := clone.Options{
		TaskID:                   "existing-directory",
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
		Cursor:                   "start",
		CloneSize:                10,
	}

	objs := []string{"my-dir/test1", "my-dir/test2"}
	mockGCSClient.EXPECT().
		ListObjectsWithMarker(
			gomock.Any(),
			GCSListRequest{
				Bucket:    options.SourceBucketName,
				Directory: options.SourceDirectoryName,
				Marker:    options.Cursor,
				MaxSize:   int(options.CloneSize),
			}).Return(objs, "", nil).Times(cloneTaskRetry)

	mockGCSClient.EXPECT().CopyObject(gomock.Any(), GCSCopyRequest{SourceKey: "my-dir/test1", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test1", SinkBucket: options.DestinationBucketName}).Return(nil).Times(3)
	mockGCSClient.EXPECT().CopyObject(gomock.Any(), GCSCopyRequest{SourceKey: "my-dir/test2", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test2", SinkBucket: options.DestinationBucketName}).Return(eris.New("copy error")).Times(15)

	err := provider.CloneDataDirectory(context.Background(), options)
	assert.Error(t, err)
}

func TestGetDataDirectory_Exists(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)
	mockObjectIterator := NewMockObjectIterator(ctrl)

	provider := &Provider{
		gcsClient: mockGCSClient,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := DeleteDirectoryOptions{
		TaskID:        "existing-directory",
		DirectoryName: "existing-directory",
		BucketName:    "my-bucket",
	}

	mockGCSClient.EXPECT().GetObjectIterator(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockObjectIterator)
	mockObjectIterator.EXPECT().Next().Return(&gcs.ObjectAttrs{Name: "existing-directory"}, nil).AnyTimes()

	err := provider.GetDataDirectory(context.Background(), options)
	assert.NoError(t, err, "Expected no error")
}

func TestGetDataDirectory_NotExists(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockGCSClient := NewMockGcsClient(ctrl)
	mockObjectIterator := NewMockObjectIterator(ctrl)

	provider := &Provider{
		gcsClient: mockGCSClient,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := DeleteDirectoryOptions{
		TaskID:        "nonexistent-directory",
		DirectoryName: "nonexistent-directory",
		BucketName:    "my-bucket",
	}

	mockGCSClient.EXPECT().GetObjectIterator(gomock.Any(), gomock.Any(), gomock.Any()).Return(mockObjectIterator)
	mockObjectIterator.EXPECT().Next().Return(nil, iterator.Done).AnyTimes()

	err := provider.GetDataDirectory(context.Background(), options)
	assert.ErrorIs(t, err, eris.Errorf("data directory %s is not found", options.TaskID).WithCode(eris.CodeNotFound))
}

func TestCreateDstObjKey(t *testing.T) {
	tests := []struct {
		name       string
		srcObjKey  string
		srcDir     string
		dstDir     string
		want       string
		expectErr  bool
		errMessage string
	}{
		{
			name:      "valid replacement with slashes",
			srcObjKey: "my-folder/file1.txt",
			srcDir:    "my-folder/",
			dstDir:    "new-folder/",
			want:      "new-folder/file1.txt",
		},
		{
			name:      "valid nested path",
			srcObjKey: "backup/my-folder/data.csv",
			srcDir:    "my-folder",
			dstDir:    "final-data",
			want:      "backup/final-data/data.csv",
		},
		{
			name:      "empty dstDir",
			srcObjKey: "my-folder/subdir/file.txt",
			srcDir:    "my-folder",
			dstDir:    "",
			want:      "/subdir/file.txt",
		},
		{
			name:       "srcDir not in srcObjKey",
			srcObjKey:  "my-folder/data.json",
			srcDir:     "nonexistent",
			dstDir:     "target",
			expectErr:  true,
			errMessage: "code(unknown) invalid input, source directory nonexistent must be part of object path my-folder/data.json",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := createDstObjKey(tt.srcObjKey, tt.srcDir, tt.dstDir)

			if tt.expectErr {
				if err == nil {
					t.Fatalf("expected error but got nil")
				}
				if err.Error() != tt.errMessage {
					t.Errorf("expected error %q, got %q", tt.errMessage, err.Error())
				}
				return
			}

			if err != nil {
				t.Fatalf("unexpected error: %v", err)
			}
			if got != tt.want {
				t.Errorf("got %q, want %q", got, tt.want)
			}
		})
	}
}
