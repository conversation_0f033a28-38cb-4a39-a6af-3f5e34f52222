package postgres

import (
	"context"
	"fmt"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/risingwavelabs/eris"
)

type ProviderImpl struct {
}

func NewProvider() Provider {
	return &ProviderImpl{}
}

type ConnectOption struct {
	ConnectTimeout         int
	Options                string
	ExecModeSimpleProtocol bool
}

func (p *ProviderImpl) Connect(ctx context.Context, host string, port int, database, username, password string, option ConnectOption) (Conn, error) {
	var builder strings.Builder
	builder.WriteString(fmt.Sprintf(`host=%s port=%d dbname=%s user=%s password=%s`,
		host,
		port,
		database,
		username,
		password,
	))
	if option.ConnectTimeout == 0 {
		option.ConnectTimeout = 10
	}
	builder.WriteString(fmt.Sprintf(` connect_timeout=%d`, option.ConnectTimeout))

	if option.Options != "" {
		builder.WriteString(fmt.Sprintf(` options="%s"`, option.Options))
	}

	cfg, err := pgx.ParseConfig(builder.String())
	if err != nil {
		return nil, eris.Wrap(err, "failed to parse pgx config")
	}
	if option.ExecModeSimpleProtocol {
		cfg.DefaultQueryExecMode = pgx.QueryExecModeSimpleProtocol
	}

	conn, err := pgx.ConnectConfig(ctx, cfg)
	if err != nil {
		return nil, eris.Wrap(err, "pgx failed to connect")
	}
	return conn, nil
}

var _ Provider = (*ProviderImpl)(nil)
