package postgres

import (
	"context"

	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgconn"
)

type Conn interface {
	Exec(context.Context, string, ...interface{}) (pgconn.CommandTag, error)
	Query(context.Context, string, ...interface{}) (pgx.Rows, error)
	QueryRow(context.Context, string, ...interface{}) pgx.Row
	Close(ctx context.Context) error
	Ping(ctx context.Context) error
}

type Provider interface {
	Connect(ctx context.Context, host string, port int, database, username, password string, option ConnectOption) (Conn, error)
}
