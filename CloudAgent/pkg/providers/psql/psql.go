package psql

import (
	"context"
	"fmt"
	"strings"

	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/providers/psql/postgres"
)

type Provider interface {
	CreateDatabase(ctx context.Context, connection Connection, database string) error
	DeleteDatabase(ctx context.Context, connection Connection, database string) error
	CreateUser(ctx context.Context, connection Connection, option UserOption) error
	DeleteUser(ctx context.Context, connection Connection, username string) error
	TruncateTables(ctx context.Context, connection Connection) error
	UpdateSystemParameters(ctx context.Context, connection Connection, params map[string]string) error
	CloneDatabase(ctx context.Context, option CloneDatabaseOption) error
}

type ProviderImpl struct {
	postgres postgres.Provider
	kc       k8s.KubernetesClientInterface
}

func NewProvider(postgres postgres.Provider, kc k8s.KubernetesClientInterface) *ProviderImpl {
	return &ProviderImpl{
		postgres: postgres,
		kc:       kc,
	}
}

type Connection struct {
	Host     string
	Port     uint32
	Database string
	Username string
	Password string
}

func (provider *ProviderImpl) CreateDatabase(ctx context.Context, connection Connection, database string) error {
	conn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		connection.Database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	exist := false
	row := conn.QueryRow(ctx, "SELECT EXISTS (SELECT FROM pg_catalog.pg_database WHERE datname = $1)", database)
	err = row.Scan(&exist)
	if err != nil {
		return eris.Wrapf(err, "failed to check if the database exists")
	}
	if exist {
		return eris.New("database already exists").WithCode(eris.CodeAlreadyExists)
	}
	_, err = conn.Exec(ctx, fmt.Sprintf("CREATE DATABASE %s", database))
	if err != nil {
		return eris.Wrapf(err, "failed to create database %s", database)
	}
	return nil
}

func (provider *ProviderImpl) DeleteDatabase(ctx context.Context, connection Connection, database string) error {
	conn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		connection.Database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	_, err = conn.Exec(ctx, fmt.Sprintf("DROP DATABASE IF EXISTS %s", database))
	if err != nil {
		return eris.Wrapf(err, "failed to drop database %s", database)
	}

	return nil
}

type CloneDatabaseOption struct {
	TaskID           string
	TaskNamespace    string
	SourceConnection Connection
	TargetConnection Connection
}

const (
	PgDumpFormatCustom    = "custom"
	PgDumpImagePostgres14 = "postgres:14"
)

func (provider *ProviderImpl) CloneDatabase(ctx context.Context, option CloneDatabaseOption) error {
	taskOption := CloneDatabaseTaskOption{
		TaskID:           option.TaskID,
		TaskNamespace:    option.TaskNamespace,
		SourceConnection: option.SourceConnection,
		TargetConnection: option.TargetConnection,
		Image:            PgDumpImagePostgres14,
		Format:           PgDumpFormatCustom,
		OutputPath:       k8s.TaskTmpVolumePath + "/pg_clone.dump", // RunTask mount an empty dir volume to /tmp
		CleanFirst:       true,
		Verbose:          true,
		NoOwner:          true,
		NoACL:            true,
	}
	return provider.kc.RunTask(ctx, taskOption.getTaskOption())
}

type CloneDatabaseTaskOption struct {
	TaskID        string
	TaskNamespace string

	SourceConnection Connection
	TargetConnection Connection

	// Clone options
	OutputPath string // intermediate file
	Format     string // default "custom"
	CleanFirst bool
	Verbose    bool
	NoOwner    bool // skip dumping/restoring object ownership
	NoACL      bool // skip dumping/restoring access privileges

	Image string // e.g. "postgres:17"
}

func (o *CloneDatabaseTaskOption) getCommand() []string {
	format := o.Format
	if format == "" {
		format = PgDumpFormatCustom // custom is compressed by default
	}

	// build dump command (using environment variable)
	dumpCmd := fmt.Sprintf(`PGPASSWORD="$PGPASSWORD_SOURCE" pg_dump --host %s --port %d --username %s --dbname %s --format %s --file %s --no-password`,
		o.SourceConnection.Host,
		o.SourceConnection.Port,
		o.SourceConnection.Username,
		o.SourceConnection.Database,
		format,
		o.OutputPath,
	)

	if o.NoOwner {
		dumpCmd += " --no-owner"
	}
	if o.NoACL {
		dumpCmd += " --no-acl"
	}
	if o.Verbose {
		dumpCmd += " --verbose"
	}

	// build restore command (using environment variable)
	restoreCmd := fmt.Sprintf(`PGPASSWORD="$PGPASSWORD_TARGET" pg_restore --host %s --port %d --username %s --dbname %s --no-password`,
		o.TargetConnection.Host,
		o.TargetConnection.Port,
		o.TargetConnection.Username,
		o.TargetConnection.Database,
	)

	if o.NoOwner {
		restoreCmd += " --no-owner"
	}
	if o.NoACL {
		restoreCmd += " --no-acl"
	}
	if o.CleanFirst {
		restoreCmd += " --clean"
	}
	if o.Verbose {
		restoreCmd += " --verbose"
	}

	restoreCmd += fmt.Sprintf(" %s", o.OutputPath)

	// compose commands by bash -c
	bashCommand := fmt.Sprintf("%s && %s", dumpCmd, restoreCmd)

	return []string{"bash", "-c", bashCommand}
}

func (o *CloneDatabaseTaskOption) getTaskOption() k8s.RunTaskOption {
	return k8s.RunTaskOption{
		Name:            o.TaskID,
		Namespace:       o.TaskNamespace,
		Image:           o.Image,
		Command:         o.getCommand(),
		ImagePullPolicy: corev1.PullIfNotPresent,
		Envs: map[string]string{
			"PGPASSWORD_SOURCE": o.SourceConnection.Password,
			"PGPASSWORD_TARGET": o.TargetConnection.Password,
		},
	}
}

type UserOption struct {
	Username            string
	Password            string
	PrivilegedDatabases []string
}

func (provider *ProviderImpl) CreateUser(ctx context.Context, connection Connection, option UserOption) error {
	conn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		connection.Database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	exist := false
	row := conn.QueryRow(ctx, "SELECT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = $1)", option.Username)
	err = row.Scan(&exist)
	if err != nil {
		return eris.Wrapf(err, "failed to check if the user exists")
	}
	if !exist {
		_, err = conn.Exec(ctx, fmt.Sprintf("CREATE ROLE %s WITH LOGIN PASSWORD '%s'", option.Username, option.Password))
		if err != nil {
			return eris.Wrapf(err, "failed to create user %s", option.Username)
		}
	}
	for _, database := range option.PrivilegedDatabases {
		err = provider.grantPermission(ctx, connection, option, conn, database)
		if err != nil {
			return err
		}
	}
	return nil
}

func (provider *ProviderImpl) grantPermission(ctx context.Context, connection Connection, option UserOption, conn postgres.Conn, database string) error {
	_, err := conn.Exec(ctx, fmt.Sprintf("GRANT ALL PRIVILEGES ON DATABASE %s TO %s", database, option.Username))
	if err != nil {
		return eris.Wrapf(err, "failed to grant privileges for user %s", option.Username)
	}

	dbConn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = dbConn.Close(context.WithoutCancel(ctx)) }()

	_, err = dbConn.Exec(ctx, fmt.Sprintf("GRANT %s TO %s", option.Username, connection.Username))
	if err != nil {
		// ignore the error because `GRANT X TO Y` is not idempotent when rerun.
		logger.FromCtx(ctx).Warnf("failed to grant %s to %s", option.Username, connection.Username)
	}

	_, err = dbConn.Exec(ctx, fmt.Sprintf("ALTER SCHEMA public OWNER TO %s", option.Username))
	if err != nil {
		return eris.Wrapf(err, "failed to alter public schema for user %s", option.Username)
	}
	return nil
}

func (provider *ProviderImpl) DeleteUser(ctx context.Context, connection Connection, username string) error {
	conn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		connection.Database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	_, err = conn.Exec(ctx, fmt.Sprintf("DROP ROLE IF EXISTS %s", username))
	if err != nil {
		return eris.Wrapf(err, "failed to drop user %s", username)
	}
	return nil
}

func (provider *ProviderImpl) TruncateTables(ctx context.Context, connection Connection) error {
	conn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		connection.Database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	// Get all table names excluding system schemas
	rows, err := conn.Query(ctx, `
		SELECT schemaname, tablename
		FROM pg_tables
		WHERE schemaname NOT IN ('pg_catalog', 'information_schema', 'cron')
	`)
	if err != nil {
		return eris.Wrapf(err, "failed to query table names")
	}
	defer rows.Close()

	var statements []string
	for rows.Next() {
		var schema, table string
		if err := rows.Scan(&schema, &table); err != nil {
			return eris.Wrapf(err, "failed to scan row")
		}
		statements = append(statements, fmt.Sprintf(`TRUNCATE TABLE "%s"."%s" CASCADE;`, schema, table))
	}
	if err := rows.Err(); err != nil {
		return eris.Wrapf(err, "row iteration error")
	}

	if len(statements) > 0 {
		_, err := conn.Exec(ctx, strings.Join(statements, "\n"))
		if err != nil {
			return eris.Wrapf(err, "failed to truncate tables")
		}
	}
	return nil
}

func validateSystemParameters(params map[string]string) error {
	validParams := map[string]bool{
		"state_store":              true,
		"data_directory":           true,
		"backup_storage_url":       true,
		"backup_storage_directory": true,
	}
	for k, v := range params {
		if _, ok := validParams[k]; !ok {
			return eris.Errorf("invalid parameter: %s", k)
		}
		if v == "" {
			return eris.Errorf("parameter %s cannot be empty parameter", k)
		}
	}
	return nil
}

func (provider *ProviderImpl) UpdateSystemParameters(ctx context.Context, connection Connection, params map[string]string) error {
	err := validateSystemParameters(params)
	if err != nil {
		return err
	}

	conn, err := provider.postgres.Connect(ctx,
		connection.Host, int(connection.Port),
		connection.Database, connection.Username, connection.Password,
		postgres.ConnectOption{},
	)
	if err != nil {
		return err
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	for k, v := range params {
		_, err := conn.Exec(ctx, "UPDATE system_parameter SET value=$1 WHERE name=$2", v, k)
		if err != nil {
			return eris.Wrapf(err, "failed to update %v", k)
		}
	}
	return nil
}
