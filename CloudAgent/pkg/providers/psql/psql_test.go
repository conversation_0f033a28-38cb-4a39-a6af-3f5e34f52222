package psql

import (
	"context"
	"fmt"
	"testing"

	"github.com/pashagolub/pgxmock/v4"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	corev1 "k8s.io/api/core/v1"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/psql/postgres"
)

func TestProvider_CreateDatabase(t *testing.T) {
	connection := Connection{
		Host:     "instance_1_host",
		Port:     5432,
		Database: "instance_1_db",
		Username: "instance_1_root",
		Password: "instance_1_pass",
	}
	tests := []struct {
		name     string
		database string
		mockConn func(conn pgxmock.PgxConnIface)
		wantErr  bool
	}{
		{
			name:     "regular",
			database: "test-database",
			mockConn: func(conn pgxmock.PgxConnIface) {
				database := "test-database"
				conn.ExpectQuery("SELECT EXISTS \\(SELECT FROM pg_catalog.pg_database WHERE datname = \\$1\\)").WithArgs(database).
					WillReturnRows(pgxmock.NewRows([]string{"exists"}).AddRow(false))
				conn.ExpectExec(fmt.Sprintf("CREATE DATABASE %s", database)).
					WillReturnResult(pgxmock.NewResult("UPDATE", 0))
			},
		},
		{
			name:     "already exist",
			database: "test-database",
			mockConn: func(conn pgxmock.PgxConnIface) {
				database := "test-database"
				conn.ExpectQuery("SELECT EXISTS \\(SELECT FROM pg_catalog.pg_database WHERE datname = \\$1\\)").WithArgs(database).
					WillReturnRows(pgxmock.NewRows([]string{"exists"}).AddRow(true))
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conn, err := pgxmock.NewConn()
			require.NoError(t, err)
			fakePg := postgres.NewFakeProvider(conn)
			provider := ProviderImpl{
				postgres: fakePg,
			}
			if tt.mockConn != nil {
				tt.mockConn(conn)
			}

			err = provider.CreateDatabase(context.Background(), connection, tt.database)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_DeleteDatabase(t *testing.T) {
	connection := Connection{
		Host:     "instance_1_host",
		Port:     5432,
		Database: "instance_1_db",
		Username: "instance_1_root",
		Password: "instance_1_pass",
	}
	tests := []struct {
		name     string
		database string
		mockConn func(conn pgxmock.PgxConnIface)
		wantErr  bool
	}{
		{
			name:     "regular",
			database: "test-database",
			mockConn: func(conn pgxmock.PgxConnIface) {
				database := "test-database"
				conn.ExpectExec(fmt.Sprintf("DROP DATABASE IF EXISTS %s", database)).
					WillReturnResult(pgxmock.NewResult("UPDATE", 0))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conn, err := pgxmock.NewConn()
			require.NoError(t, err)
			fakePg := postgres.NewFakeProvider(conn)
			provider := ProviderImpl{
				postgres: fakePg,
			}
			if tt.mockConn != nil {
				tt.mockConn(conn)
			}

			err = provider.DeleteDatabase(context.Background(), connection, tt.database)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_CreateUser(t *testing.T) {
	connection := Connection{
		Host:     "instance_1_host",
		Port:     5432,
		Database: "instance_1_db",
		Username: "instance_1_root",
		Password: "instance_1_pass",
	}
	database := "test-database"
	user := UserOption{
		Username:            "test-user",
		Password:            "test-password",
		PrivilegedDatabases: []string{database},
	}
	tests := []struct {
		name     string
		mockConn func(conn pgxmock.PgxConnIface)
		wantErr  bool
	}{
		{
			name: "regular",
			mockConn: func(conn pgxmock.PgxConnIface) {
				conn.ExpectQuery("SELECT EXISTS \\(SELECT FROM pg_catalog.pg_roles WHERE rolname = \\$1\\)").WithArgs(user.Username).
					WillReturnRows(pgxmock.NewRows([]string{"exists"}).AddRow(false))
				conn.ExpectExec(fmt.Sprintf("CREATE ROLE %s WITH LOGIN PASSWORD '%s'", user.Username, user.Password)).
					WillReturnResult(pgxmock.NewResult("UPDATE", 0))
				conn.ExpectExec(fmt.Sprintf("GRANT ALL PRIVILEGES ON DATABASE %s TO %s", database, user.Username)).
					WillReturnResult(pgxmock.NewResult("UPDATE", 0))
				conn.ExpectExec(fmt.Sprintf("GRANT %s TO %s", user.Username, connection.Username)).
					WillReturnResult(pgxmock.NewResult("UPDATE", 0))
				conn.ExpectExec(fmt.Sprintf("ALTER SCHEMA public OWNER TO %s", user.Username)).
					WillReturnResult(pgxmock.NewResult("ALTER SCHEMA", 0))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conn, err := pgxmock.NewConn()
			require.NoError(t, err)
			fakePg := postgres.NewFakeProvider(conn)
			provider := ProviderImpl{
				postgres: fakePg,
			}
			if tt.mockConn != nil {
				tt.mockConn(conn)
			}

			err = provider.CreateUser(context.Background(), connection, user)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_DeleteUser(t *testing.T) {
	connection := Connection{
		Host:     "instance_1_host",
		Port:     5432,
		Database: "instance_1_db",
		Username: "instance_1_root",
		Password: "instance_1_pass",
	}
	username := "test-user"
	tests := []struct {
		name     string
		mockConn func(conn pgxmock.PgxConnIface)
		wantErr  bool
	}{
		{
			name: "regular",
			mockConn: func(conn pgxmock.PgxConnIface) {
				conn.ExpectExec(fmt.Sprintf("DROP ROLE IF EXISTS %s", username)).
					WillReturnResult(pgxmock.NewResult("UPDATE", 0))
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			conn, err := pgxmock.NewConn()
			require.NoError(t, err)
			fakePg := postgres.NewFakeProvider(conn)
			provider := ProviderImpl{
				postgres: fakePg,
			}
			if tt.mockConn != nil {
				tt.mockConn(conn)
			}

			err = provider.DeleteUser(context.Background(), connection, username)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestTruncateTables(t *testing.T) {
	connection := Connection{
		Host:     "instance_1_host",
		Port:     5432,
		Database: "instance_1_db",
		Username: "instance_1_root",
		Password: "instance_1_pass",
	}

	tests := []struct {
		name     string
		mockConn func(conn pgxmock.PgxConnIface)
		wantErr  bool
	}{
		{
			name: "truncate success with multiple tables",
			mockConn: func(conn pgxmock.PgxConnIface) {
				conn.ExpectQuery("SELECT schemaname, tablename FROM pg_tables WHERE schemaname NOT IN").
					WillReturnRows(pgxmock.NewRows([]string{"schemaname", "tablename"}).
						AddRow("public", "users").
						AddRow("public", "orders"))

				conn.ExpectExec(`TRUNCATE TABLE "public"."users" CASCADE;
TRUNCATE TABLE "public"."orders" CASCADE;`).
					WillReturnResult(pgxmock.NewResult("TRUNCATE", 0))
			},
			wantErr: false,
		},
		{
			name: "query fails",
			mockConn: func(conn pgxmock.PgxConnIface) {
				conn.ExpectQuery("SELECT schemaname, tablename FROM pg_tables WHERE schemaname NOT IN").
					WillReturnError(fmt.Errorf("db error"))
			},
			wantErr: true,
		},
		{
			name: "exec fails",
			mockConn: func(conn pgxmock.PgxConnIface) {
				conn.ExpectQuery("SELECT schemaname, tablename FROM pg_tables WHERE schemaname NOT IN").
					WillReturnRows(pgxmock.NewRows([]string{"schemaname", "tablename"}).
						AddRow("public", "onlytable"))

				conn.ExpectExec(`TRUNCATE TABLE "public"."onlytable" CASCADE;`).
					WillReturnError(fmt.Errorf("truncate failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockConn, err := pgxmock.NewConn()
			require.NoError(t, err)

			if tt.mockConn != nil {
				tt.mockConn(mockConn)
			}

			fakePg := postgres.NewFakeProvider(mockConn)
			provider := ProviderImpl{
				postgres: fakePg,
			}

			err = provider.TruncateTables(context.Background(), connection)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestUpdateSystemParameters(t *testing.T) {
	connection := Connection{
		Host:     "localhost",
		Port:     5432,
		Database: "test_db",
		Username: "user",
		Password: "pass",
	}

	tests := []struct {
		name     string
		params   map[string]string
		mockConn func(conn pgxmock.PgxConnIface, params map[string]string)
		wantErr  bool
	}{
		{
			name: "successful update",
			params: map[string]string{
				"state_store": "s3://new_state_bucket",
			},
			mockConn: func(conn pgxmock.PgxConnIface, params map[string]string) {
				for k, v := range params {
					conn.ExpectExec(`UPDATE system_parameter SET value=\$1 WHERE name=\$2`).
						WithArgs(v, k).
						WillReturnResult(pgxmock.NewResult("UPDATE", 1)).Times(1)
				}
			},
			wantErr: false,
		},
		{
			name: "invalid parameter - empty value",
			params: map[string]string{
				"state_store":        "s3://partial_state_bucket",
				"data_directory":     "", // invalid
				"backup_storage_url": "s3://some_backup",
			},
			mockConn: nil,
			wantErr:  true,
		},
		{
			name: "update query fails",
			params: map[string]string{
				"state_store": "bad_value",
			},
			mockConn: func(conn pgxmock.PgxConnIface, _ map[string]string) {
				conn.ExpectExec(`UPDATE system_parameter SET value=\$1 WHERE name=\$2`).
					WithArgs("bad_value", "state_store").
					WillReturnError(fmt.Errorf("update failed"))
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockConn, err := pgxmock.NewConn()
			require.NoError(t, err)

			if tt.mockConn != nil {
				tt.mockConn(mockConn, tt.params)
			}

			fakePg := postgres.NewFakeProvider(mockConn)
			provider := ProviderImpl{
				postgres: fakePg,
			}

			err = provider.UpdateSystemParameters(context.Background(), connection, tt.params)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			require.NoError(t, mockConn.ExpectationsWereMet())
		})
	}
}

func TestProvider_CloneDatabase(t *testing.T) {
	sourceConnection := Connection{
		Host:     "source-host",
		Port:     5432,
		Database: "source-db",
		Username: "source-user",
		Password: "source-pass",
	}

	targetConnection := Connection{
		Host:     "target-host",
		Port:     5433,
		Database: "target-db",
		Username: "target-user",
		Password: "target-pass",
	}

	tests := []struct {
		name      string
		option    CloneDatabaseOption
		wantErr   bool
		setupMock func(*k8s.MockKubernetesClientInterface)
	}{
		{
			name: "successful clone with default options",
			option: CloneDatabaseOption{
				TaskID:           "test-clone-task",
				TaskNamespace:    "default",
				SourceConnection: sourceConnection,
				TargetConnection: targetConnection,
			},
			setupMock: func(mockKc *k8s.MockKubernetesClientInterface) {
				expectedCommand := []string{
					"bash", "-c",
					`PGPASSWORD="$PGPASSWORD_SOURCE" pg_dump --host source-host --port 5432 --username source-user --dbname source-db --format custom --file /tmp/pg_clone.dump --no-password --no-owner --no-acl --verbose && PGPASSWORD="$PGPASSWORD_TARGET" pg_restore --host target-host --port 5433 --username target-user --dbname target-db --no-password --no-owner --no-acl --clean --verbose /tmp/pg_clone.dump`,
				}
				expectedRunTaskOption := k8s.RunTaskOption{
					Name:            "test-clone-task",
					Namespace:       "default",
					Image:           "postgres:14",
					Command:         expectedCommand,
					ImagePullPolicy: corev1.PullIfNotPresent,
					Envs: map[string]string{
						"PGPASSWORD_SOURCE": "source-pass",
						"PGPASSWORD_TARGET": "target-pass",
					},
				}
				mockKc.EXPECT().RunTask(gomock.Any(), expectedRunTaskOption).Return(nil)
			},
		},
		{
			name: "clone with special characters in password",
			option: CloneDatabaseOption{
				TaskID:        "test-clone-special",
				TaskNamespace: "default",
				SourceConnection: Connection{
					Host:     "source-host",
					Port:     5432,
					Database: "source-db",
					Username: "source-user",
					Password: "p@ss$w0rd'\"\\&<>|",
				},
				TargetConnection: Connection{
					Host:     "target-host",
					Port:     5433,
					Database: "target-db",
					Username: "target-user",
					Password: "t@rget$p@ss'\"\\&<>|",
				},
			},
			setupMock: func(mockKc *k8s.MockKubernetesClientInterface) {
				expectedCommand := []string{
					"bash", "-c",
					`PGPASSWORD="$PGPASSWORD_SOURCE" pg_dump --host source-host --port 5432 --username source-user --dbname source-db --format custom --file /tmp/pg_clone.dump --no-password --no-owner --no-acl --verbose && PGPASSWORD="$PGPASSWORD_TARGET" pg_restore --host target-host --port 5433 --username target-user --dbname target-db --no-password --no-owner --no-acl --clean --verbose /tmp/pg_clone.dump`,
				}
				expectedRunTaskOption := k8s.RunTaskOption{
					Name:            "test-clone-special",
					Namespace:       "default",
					Image:           "postgres:14",
					Command:         expectedCommand,
					ImagePullPolicy: corev1.PullIfNotPresent,
					Envs: map[string]string{
						"PGPASSWORD_SOURCE": "p@ss$w0rd'\"\\&<>|",
						"PGPASSWORD_TARGET": "t@rget$p@ss'\"\\&<>|",
					},
				}
				mockKc.EXPECT().RunTask(gomock.Any(), expectedRunTaskOption).Return(nil)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			mockKc := k8s.NewMockKubernetesClientInterface(ctrl)

			conn, err := pgxmock.NewConn()
			require.NoError(t, err)
			fakePg := postgres.NewFakeProvider(conn)

			provider := &ProviderImpl{
				postgres: fakePg,
				kc:       mockKc,
			}

			if tt.setupMock != nil {
				tt.setupMock(mockKc)
			}

			err = provider.CloneDatabase(context.Background(), tt.option)
			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}
