// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/psql (interfaces: Provider)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/psql -package=psql -destination=pkg/providers/psql/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/psql Provider
//

// Package psql is a generated GoMock package.
package psql

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
)

// MockProvider is a mock of Provider interface.
type MockProvider struct {
	ctrl     *gomock.Controller
	recorder *MockProviderMockRecorder
	isgomock struct{}
}

// MockProviderMockRecorder is the mock recorder for MockProvider.
type MockProviderMockRecorder struct {
	mock *MockProvider
}

// NewMockProvider creates a new mock instance.
func NewMockProvider(ctrl *gomock.Controller) *MockProvider {
	mock := &MockProvider{ctrl: ctrl}
	mock.recorder = &MockProviderMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProvider) EXPECT() *MockProviderMockRecorder {
	return m.recorder
}

// CloneDatabase mocks base method.
func (m *MockProvider) CloneDatabase(ctx context.Context, option CloneDatabaseOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CloneDatabase", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CloneDatabase indicates an expected call of CloneDatabase.
func (mr *MockProviderMockRecorder) CloneDatabase(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CloneDatabase", reflect.TypeOf((*MockProvider)(nil).CloneDatabase), ctx, option)
}

// CreateDatabase mocks base method.
func (m *MockProvider) CreateDatabase(ctx context.Context, connection Connection, database string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateDatabase", ctx, connection, database)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateDatabase indicates an expected call of CreateDatabase.
func (mr *MockProviderMockRecorder) CreateDatabase(ctx, connection, database any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateDatabase", reflect.TypeOf((*MockProvider)(nil).CreateDatabase), ctx, connection, database)
}

// CreateUser mocks base method.
func (m *MockProvider) CreateUser(ctx context.Context, connection Connection, option UserOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateUser", ctx, connection, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateUser indicates an expected call of CreateUser.
func (mr *MockProviderMockRecorder) CreateUser(ctx, connection, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateUser", reflect.TypeOf((*MockProvider)(nil).CreateUser), ctx, connection, option)
}

// DeleteDatabase mocks base method.
func (m *MockProvider) DeleteDatabase(ctx context.Context, connection Connection, database string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteDatabase", ctx, connection, database)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteDatabase indicates an expected call of DeleteDatabase.
func (mr *MockProviderMockRecorder) DeleteDatabase(ctx, connection, database any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteDatabase", reflect.TypeOf((*MockProvider)(nil).DeleteDatabase), ctx, connection, database)
}

// DeleteUser mocks base method.
func (m *MockProvider) DeleteUser(ctx context.Context, connection Connection, username string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteUser", ctx, connection, username)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteUser indicates an expected call of DeleteUser.
func (mr *MockProviderMockRecorder) DeleteUser(ctx, connection, username any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteUser", reflect.TypeOf((*MockProvider)(nil).DeleteUser), ctx, connection, username)
}

// TruncateTables mocks base method.
func (m *MockProvider) TruncateTables(ctx context.Context, connection Connection) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "TruncateTables", ctx, connection)
	ret0, _ := ret[0].(error)
	return ret0
}

// TruncateTables indicates an expected call of TruncateTables.
func (mr *MockProviderMockRecorder) TruncateTables(ctx, connection any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "TruncateTables", reflect.TypeOf((*MockProvider)(nil).TruncateTables), ctx, connection)
}

// UpdateSystemParameters mocks base method.
func (m *MockProvider) UpdateSystemParameters(ctx context.Context, connection Connection, params map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSystemParameters", ctx, connection, params)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSystemParameters indicates an expected call of UpdateSystemParameters.
func (mr *MockProviderMockRecorder) UpdateSystemParameters(ctx, connection, params any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSystemParameters", reflect.TypeOf((*MockProvider)(nil).UpdateSystemParameters), ctx, connection, params)
}
