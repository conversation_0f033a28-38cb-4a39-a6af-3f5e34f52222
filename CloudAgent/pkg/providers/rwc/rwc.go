package rwc

import (
	"bytes"
	"compress/gzip"
	"context"
	"fmt"
	"io"
	"net/http"
	"strings"

	"github.com/jackc/pgx/v5"
	"github.com/risingwavelabs/eris"
	"golang.org/x/mod/semver"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbrwcsvc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskrwc "github.com/risingwavelabs/cloudagent/pbgen/task/rwc"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/providers/rwc/schemaregistry"
	"github.com/risingwavelabs/cloudagent/pkg/risectl"
	"github.com/risingwavelabs/cloudagent/pkg/shared/cloud/aws/credentials"
	"github.com/risingwavelabs/cloudagent/pkg/utils"

	corev1 "k8s.io/api/core/v1"
)

type ProviderInterface interface {
	StartMetaNodeBackupTask(ctx context.Context, option MetaNodeBackupOption) error
	ValidateSource(ctx context.Context, option ValidateSourceOption) error
	GetClusterInfo(ctx context.Context, name, ns string) (string, error)
	CordonWorkers(ctx context.Context, name, ns string, workers []string) error
	DeleteWorkers(ctx context.Context, name, ns string, workers []string) error
	ResizeWorkers(ctx context.Context, name, ns string, addedWorkers []string, deletingWorkers []string) error
	DeleteSnapshot(ctx context.Context, rwversion string, name, ns string, snapshotID int64) error
	RestoreMeta(ctx context.Context, option RestoreMetaOption) error
	EtcdctlCommand(ctx context.Context, option EtcdctlOption) error
	GetDiagnosisReportURL(svc string, ns string) string
	GenDiagnosisReport(ctx context.Context, svc string, ns string) (string, error)
	GenDiagnosisReportBody(ctx context.Context, svc string, ns string) (io.ReadCloser, error)
	GenDiagnosisReportBodyCompressed(ctx context.Context, svc string, ns string) (io.ReadCloser, error)
	MetaMigration(ctx context.Context, option MetaMigrationOption) error
	FetchPostgresTable(ctx context.Context, req *pbrwcsvc.FetchPostgresTableRequest) ([]string, error)
	FetchSourceSchema(ctx context.Context, req *pbrwcsvc.FetchSourceSchemaRequest) ([]*pbrwcsvc.RawSchemaFile, error)
	FetchKafkaTopic(ctx context.Context, req *pbrwcsvc.FetchKafkaTopicRequest) ([]string, error)
	FetchKafkaMessage(ctx context.Context, req *pbrwcsvc.FetchKafkaMessageRequest) (*KafkaMessage, error)
}

type Provider struct {
	kc              k8s.KubernetesClientInterface
	risectlExecutor risectl.Executor
}

type NewProviderOption struct {
	Kc k8s.KubernetesClientInterface
}

func NewProvider(option NewProviderOption) (*Provider, error) {
	if option.Kc == nil {
		return nil, eris.New("Kubernetes client cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	return &Provider{
		kc:              option.Kc,
		risectlExecutor: risectl.NewMetaNodeExecutor(option.Kc),
	}, nil
}

type MetaNodeBackupOption struct {
	RwNamespace   string
	RwName        string
	TaskID        string
	TaskNamespace string
}

func (p *Provider) StartMetaNodeBackupTask(ctx context.Context, option MetaNodeBackupOption) error {
	return p.kc.StartTaskRunner(ctx, option.TaskID, option.TaskNamespace, &pbtask.Task{
		Task: &pbtask.Task_RisectlTask{
			RisectlTask: &pbtaskrwc.RisectlTask{
				RisingwaveName:      option.RwName,
				RisingwaveNamespace: option.RwNamespace,
				// risectl command args
				Args: []string{"meta", "backup-meta"},
			},
		},
	})
}

type ValidateSourceOption struct {
	RwNamespace string
	RwName      string
	Props       string
}

func (p *Provider) ValidateSource(ctx context.Context, option ValidateSourceOption) error {
	if len(option.RwNamespace) == 0 {
		return eris.WithCode(eris.New("rw namespace cannot be empty"), eris.CodeInvalidArgument)
	}
	if len(option.RwName) == 0 {
		return eris.WithCode(eris.New("rw name cannot be empty"), eris.CodeInvalidArgument)
	}
	if len(option.Props) == 0 {
		return eris.WithCode(eris.New("props cannot be empty"), eris.CodeInvalidArgument)
	}
	_, err := p.risectlExecutor.RunRisectlCommand(ctx, risectl.CommandOption{
		RwNamespace: option.RwNamespace,
		RwName:      option.RwName,
		Cmd:         []string{"meta", "validate-source", "--props", option.Props},
	})

	if err != nil {
		if eris.Is(err, k8s.ErrFailedCommand) {
			return eris.WithCode(err, eris.CodeFailedPrecondition)
		}
		return eris.Wrapf(err, "failed to run risectl command validate-source on %s %s", option.RwNamespace, option.RwName)
	}
	return nil
}

func (p *Provider) GetClusterInfo(ctx context.Context, name, ns string) (string, error) {
	output, err := p.risectlExecutor.RunRisectlCommand(ctx, risectl.CommandOption{
		RwNamespace: ns,
		RwName:      name,
		Cmd:         []string{"meta", "cluster-info"},
	})
	if err != nil {
		return "", eris.Wrapf(err, "failed to run risectl command meta cluster-info on RW %s %s", name, ns)
	}
	return output, nil
}

func (p *Provider) CordonWorkers(ctx context.Context, name, ns string, workers []string) error {
	_, err := p.risectlExecutor.RunRisectlCommand(ctx, risectl.CommandOption{
		RwNamespace: ns,
		RwName:      name,
		Cmd:         []string{"scale", "cordon", "--workers", strings.Join(workers, ",")},
	})
	if err != nil {
		return eris.Wrapf(err, "failed to run risectl command cordon on RW %s %s, workder ids: %v", name, ns, workers)
	}
	return nil
}

func (p *Provider) DeleteWorkers(ctx context.Context, name, ns string, workers []string) error {
	_, err := p.risectlExecutor.RunRisectlCommand(ctx, risectl.CommandOption{
		RwNamespace: ns,
		RwName:      name,
		Cmd:         []string{"meta", "unregister-workers", "--workers", strings.Join(workers, ","), "--ignore-not-found", "--yes"},
	})
	if err != nil {
		return eris.Wrapf(err, "failed to run risectl command unregister-workers on RW %s %s, workder ids: %v", name, ns, workers)
	}
	return nil
}

func (p *Provider) ResizeWorkers(ctx context.Context, name, ns string, addedWorkers []string, deletingWorkers []string) error {
	var workerOpts []string
	if len(addedWorkers) > 0 {
		workerOpts = append(workerOpts, "--include-workers", strings.Join(addedWorkers, ","))
	}
	if len(deletingWorkers) > 0 {
		workerOpts = append(workerOpts, "--exclude-workers", strings.Join(deletingWorkers, ","))
	}
	_, err := p.risectlExecutor.RunRisectlCommand(ctx, risectl.CommandOption{
		RwNamespace: ns,
		RwName:      name,
		Cmd:         append([]string{"scale", "resize", "--yes"}, workerOpts...),
	})
	if err != nil {
		return eris.Wrapf(err, "failed to run risectl command unregister-workers on RW %s %s, added workder ids: %v, deleting worker ids: %v", name, ns, addedWorkers, deletingWorkers)
	}
	return nil
}

func (p *Provider) DeleteSnapshot(ctx context.Context, rwversion string, name, ns string, snapshotID int64) error {
	_, err := p.risectlExecutor.RunRisectlCommand(ctx, risectl.CommandOption{
		RwNamespace: ns,
		RwName:      name,
		Cmd: utils.IfElse(
			semver.Compare(rwversion, "v2.0.1") >= 0,
			[]string{"meta", "delete-meta-snapshots", "--snapshot-ids", fmt.Sprintf("%d", snapshotID)},
			[]string{"meta", "delete-meta-snapshots", fmt.Sprintf("%d", snapshotID)},
		),
	})
	if err != nil {
		return eris.Wrapf(err, "failed to run risectl command meta delete-meta-snapshots, name: %s, namespace: %s, snapshotID: %d", name, ns, snapshotID)
	}
	return nil
}

type RestoreMetaOption struct {
	TaskID        string
	TaskNamespace string
	// can be empty
	ServiceAccount string
	ImageTag       string

	MetaStoreType     string
	MetaSnapshotID    int64
	BackupStorageURL  string
	BackupStorageDir  string
	HummockStorageURL string
	HummockStorageDir string
	EtcdEndpoints     string
	EtcdAuth          bool
	EtcdUsername      string
	EtcdPassword      string

	SQLEndpoint string

	Envs map[string]string
}

func (o *RestoreMetaOption) validate() error {
	switch o.MetaStoreType {
	case "etcd":
		if len(o.EtcdEndpoints) == 0 {
			return eris.New("etcd endpoints cannot be empty").WithCode(eris.CodeInvalidArgument)
		}
	case "sql":
		if len(o.SQLEndpoint) == 0 {
			return eris.New("sql endpoint cannot be empty").WithCode(eris.CodeInvalidArgument)
		}
	case "":
		return eris.New("meta store type cannot be empty").WithCode(eris.CodeInvalidArgument)
	default:
		return eris.Errorf("unsupported meta store type: %s", o.MetaStoreType).WithCode(eris.CodeInvalidArgument)
	}
	if len(o.TaskID) == 0 {
		return eris.New("task ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(o.TaskNamespace) == 0 {
		return eris.New("namespace cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(o.ImageTag) == 0 {
		return eris.New("image tag cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if o.MetaSnapshotID == 0 {
		return eris.New("meta snapshot id cannot be 0").WithCode(eris.CodeInvalidArgument)
	}
	if len(o.BackupStorageURL) == 0 {
		return eris.New("backup storage url cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(o.BackupStorageDir) == 0 {
		return eris.New("backup storage dir cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(o.HummockStorageURL) == 0 {
		return eris.New("hummock storage url cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(o.HummockStorageDir) == 0 {
		return eris.New("hummock storage dir cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	return nil
}

func (o *RestoreMetaOption) getCommand() []string {
	command := []string{
		"/risingwave/bin/risingwave",
		"ctl",
		"meta",
		"restore-meta",
	}

	switch o.MetaStoreType {
	case "etcd":
		command = append(command,
			"--etcd-endpoints",
			o.EtcdEndpoints,
		)
		if o.EtcdAuth {
			command = append(command, "--etcd-auth")
			if len(o.EtcdUsername) != 0 {
				command = append(command, "--etcd-username", o.EtcdUsername)
			}
			if len(o.EtcdPassword) != 0 {
				command = append(command, "--etcd-password", o.EtcdPassword)
			}
		}
	case "sql":
		command = append(command,
			"--sql-endpoint",
			o.SQLEndpoint,
		)
	}

	command = append(command,
		"--meta-store-type",
		o.MetaStoreType,
		"--meta-snapshot-id",
		fmt.Sprint(o.MetaSnapshotID),
		"--backup-storage-url",
		o.BackupStorageURL,
		"--backup-storage-directory",
		o.BackupStorageDir,
		"--hummock-storage-url",
		o.HummockStorageURL,
		"--hummock-storage-directory",
		o.HummockStorageDir,
	)

	return command
}

func (p *Provider) RestoreMeta(ctx context.Context, option RestoreMetaOption) error {
	if err := option.validate(); err != nil {
		return eris.Wrap(err, "invalid restore meta option")
	}

	runTaskOption := k8s.RunTaskOption{
		Name:            option.TaskID,
		Namespace:       option.TaskNamespace,
		Image:           fmt.Sprintf("risingwavelabs/risingwave:%s", option.ImageTag),
		Command:         option.getCommand(),
		ImagePullPolicy: corev1.PullIfNotPresent,
		Envs:            option.Envs,
	}

	if len(option.ServiceAccount) != 0 {
		runTaskOption.ServiceAccountName = option.ServiceAccount
	}

	return p.kc.RunTask(ctx, runTaskOption)
}

type EtcdctlOption struct {
	EtcdPodName      string
	EtcdPodNamespace string
	EtcdUsername     string
	EtcdPassword     string
	EtcdCommand      []string
}

func (p *Provider) EtcdctlCommand(ctx context.Context, option EtcdctlOption) error {
	if len(option.EtcdPodName) == 0 {
		return eris.New("etcd pod name cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.EtcdPodNamespace) == 0 {
		option.EtcdPodNamespace = "default"
	}
	if len(option.EtcdUsername) == 0 {
		return eris.New("etcd username cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.EtcdCommand) == 0 {
		return eris.New("etcd command cannot be empty").WithCode(eris.CodeInvalidArgument)
	}

	cmd := []string{
		"env",
		"ETCDCTL_API=3",
		"etcdctl",
		fmt.Sprintf("--user=%s", option.EtcdUsername),
		fmt.Sprintf("--password=%s", option.EtcdPassword),
	}
	cmd = append(cmd, option.EtcdCommand...)
	o, e, err := p.kc.ExecPod(ctx, option.EtcdPodName, option.EtcdPodNamespace, cmd)
	if err != nil {
		return eris.Wrapf(err, "failed to run etcdctl command %v on %s %s", option.EtcdCommand, option.EtcdPodNamespace, option.EtcdPodName)
	}
	logger.FromCtx(ctx).Infof("finished etcd command %v in %s/%s, stdout: %s, stderr: %s", option.EtcdCommand, option.EtcdPodNamespace, option.EtcdPodName, o, e)
	return nil
}

// RW Diagnosis Report.
const (
	RwEndpointTemplate  string = "%s.%s.svc"
	DiagnosisReportPath string = "/api/monitor/diagnose/"
)

var (
	RwEndpointPort = 5691
)

func (p *Provider) GetDiagnosisReportURL(svc string, ns string) string {
	rwEndpoint := fmt.Sprintf(RwEndpointTemplate, svc, ns)
	return fmt.Sprintf("http://%s:%v%s", rwEndpoint, RwEndpointPort, DiagnosisReportPath)
}

func (p *Provider) getGenDiagnosisReportHTTPResponse(ctx context.Context, svc string, ns string) (*http.Response, error) {
	servURL := p.GetDiagnosisReportURL(svc, ns)
	req, err := http.NewRequestWithContext(ctx, http.MethodGet, servURL, bytes.NewReader([]byte("")))
	if err != nil {
		return nil, eris.Wrapf(err, "failed to construct request to the risingwave cluster provided service_name = %s and namespace = %s", svc, ns)
	}
	client := &http.Client{}
	resp, err := client.Do(req)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to request the diagnosis report generation api, req: %v", req)
	}
	return resp, nil
}

func (p *Provider) GenDiagnosisReport(ctx context.Context, svc string, ns string) (string, error) {
	resp, err := p.getGenDiagnosisReportHTTPResponse(ctx, svc, ns)
	if err != nil {
		return "", err
	}
	defer func() { _ = resp.Body.Close() }()
	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", eris.Wrapf(err, "failed to read response body from the diagnosis report generation api, resp: %v", resp)
	}
	if 200 <= resp.StatusCode && resp.StatusCode <= 299 {
		return string(body), nil
	}
	return string(body), eris.Errorf("abnormal diagnosis report generation response, status code: %d, body: %s", resp.StatusCode, body)
}

func (p *Provider) GenDiagnosisReportBody(ctx context.Context, svc string, ns string) (io.ReadCloser, error) {
	resp, err := p.getGenDiagnosisReportHTTPResponse(ctx, svc, ns)
	if err != nil {
		return nil, err
	}
	return resp.Body, nil
}

func (p *Provider) GenDiagnosisReportBodyCompressed(ctx context.Context, svc string, ns string) (io.ReadCloser, error) {
	// The body will be closed inside compressStream below.
	resp, err := p.getGenDiagnosisReportHTTPResponse(ctx, svc, ns) // nolint:bodyclose
	if err != nil {
		return nil, err
	}
	return compressStream(resp.Body), nil
}

func compressStream(source io.ReadCloser) io.ReadCloser {
	pipeReader, pipeWriter := io.Pipe()

	go func() {
		// It's important to close the pipeWriter, eventually.
		// If there's an error during compression, CloseWithError propagates it.
		// Otherwise, a normal Close signals EOF to the pipeReader.
		var err error
		defer func() {
			source.Close()
			if err != nil {
				pipeWriter.CloseWithError(err)
			} else {
				pipeWriter.Close()
			}
		}()

		gzipWriter := gzip.NewWriter(pipeWriter)
		defer func() {
			if e := gzipWriter.Close(); e != nil && err == nil {
				// If gzip.Close() errors and io.Copy didn't, capture this error.
				err = eris.Errorf("failed to close gzip writer: %v", e)
			}
		}() // Must close gzipWriter to flush compressed data and write footer

		// Copy data from the source to the gzip writer
		_, err = io.Copy(gzipWriter, source)
		if err != nil {
			err = eris.Errorf("failed during io.Copy to gzip writer: %v", err)
			return
		}
	}()

	return pipeReader
}

type MetaMigrationOption struct {
	TaskID          string
	TaskNamespace   string
	RwName          string
	RwNamespace     string
	EtcdEndpoints   string
	SQLEndpoint     string
	TaskImage       string
	TaskResources   *pbk8s.ResourceRequirements
	TaskTolerations []*pbk8s.Toleration
	TaskAffinity    *pbk8s.Affinity
}

func (p *Provider) MetaMigration(ctx context.Context, option MetaMigrationOption) error {
	resource, err := conversion.FromResourceRequirementsProto(option.TaskResources)
	if err != nil {
		return eris.WithCode(eris.Wrap(err, "failed to convert resource requirements"), eris.CodeInvalidArgument)
	}

	var tolerations []corev1.Toleration
	for _, tp := range option.TaskTolerations {
		t, err := conversion.FromTolerationProto(tp)
		if err != nil {
			return eris.WithCode(eris.Wrap(err, "failed to convert task toleration proto"), eris.CodeInvalidArgument)
		}
		tolerations = append(tolerations, t)
	}

	var affinity *corev1.Affinity
	if option.TaskAffinity != nil {
		affinity, err = conversion.FromAffinityProto(option.TaskAffinity)
		if err != nil {
			return eris.WithCode(eris.Wrap(err, "failed to convert task affinity proto"), eris.CodeInvalidArgument)
		}
	}

	runTaskOption := k8s.RunTaskOption{
		Name:      option.TaskID,
		Namespace: option.TaskNamespace,
		Image:     option.TaskImage,
		Command: []string{
			"/risingwave/bin/risingwave", "ctl",
			// risectl command args, -f will clean the data if previous failed
			"meta", "migration", "--etcd-endpoints", option.EtcdEndpoints, "--sql-endpoint", option.SQLEndpoint, "-f",
		},
		ImagePullPolicy: corev1.PullIfNotPresent,
		Resources:       resource,
		Tolerations:     tolerations,
		Affinity:        affinity,
	}
	return p.kc.RunTask(ctx, runTaskOption)
}

func (p *Provider) FetchPostgresTable(ctx context.Context, req *pbrwcsvc.FetchPostgresTableRequest) ([]string, error) {
	connStr := fmt.Sprintf(
		"postgres://%s:%s@%s:%d/%s",
		req.GetPostgres().GetUsername(),
		req.GetPostgres().GetPassword(),
		req.GetPostgres().GetHostname(),
		req.GetPostgres().GetPort(),
		req.GetPostgres().GetDatabase(),
	)
	switch req.GetPostgres().GetSslMode() {
	case pbrwcsvc.PostgresSslMode_DISABLED:
		connStr += "?sslmode=disable"
	case pbrwcsvc.PostgresSslMode_PREFERRED:
		connStr += "?sslmode=prefer"
	case pbrwcsvc.PostgresSslMode_REQUIRED:
		connStr += "?sslmode=require"
	case pbrwcsvc.PostgresSslMode_VERIFY_CA:
		connStr += "?sslmode=verify-ca"
	case pbrwcsvc.PostgresSslMode_VERIFY_FULL:
		connStr += "?sslmode=verify-full"
	case pbrwcsvc.PostgresSslMode_SSL_MODE_UNSPECIFIED:
	}

	conn, err := pgx.Connect(ctx, connStr)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to connect to postgres"), eris.CodeFailedPrecondition)
	}
	defer func() { _ = conn.Close(context.WithoutCancel(ctx)) }()

	tables, err := conn.Query(context.Background(), "SELECT CONCAT(schemaname,'.',tablename) AS table_name FROM pg_catalog.pg_tables")
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to list tables from postgres"), eris.CodeFailedPrecondition)
	}
	defer tables.Close()

	var tableNames []string
	for tables.Next() {
		var tableName string
		if err := tables.Scan(&tableName); err != nil {
			return nil, eris.WithCode(eris.Wrap(err, "failed to scan table name"), eris.CodeFailedPrecondition)
		}
		tableNames = append(tableNames, tableName)
	}
	return tableNames, nil
}

func (p *Provider) FetchSourceSchema(ctx context.Context, req *pbrwcsvc.FetchSourceSchemaRequest) ([]*pbrwcsvc.RawSchemaFile, error) {
	switch req.GetLocation() {
	case pbrwcsvc.SchemaLocation_WEB_LOCATION:
		return GetFromWeb(ctx, req.GetUrl())
	case pbrwcsvc.SchemaLocation_S3:
		s3Config := req.GetS3()
		if s3Config == nil {
			return nil, eris.New("missing S3 config").WithCode(eris.CodeInvalidArgument)
		}
		accessKey := credentials.AnonymousAccessKey
		if s3Config.AccessKeyId != nil {
			accessKey = credentials.AccessKey{
				AccessKeyID:     s3Config.GetAccessKeyId(),
				SecretAccessKey: s3Config.GetSecretAccessKey(),
			}
		}
		return GetFromS3(accessKey, req.GetUrl(), s3Config.GetRegion())
	case pbrwcsvc.SchemaLocation_SCHEMA_REGISTRY:
		regConfig := req.GetSchemaRegistry()
		if regConfig == nil {
			return nil, eris.New("missing schema registry config").WithCode(eris.CodeInvalidArgument)
		}
		var credential *schemaregistry.Credential
		if regConfig.Username != nil {
			credential = &schemaregistry.Credential{
				Username: regConfig.GetUsername(),
				Password: regConfig.GetPassword(),
			}
		}
		return GetFromSchemaRegistry(ctx, req.GetUrl(), regConfig.GetTopic(), credential)
	case pbrwcsvc.SchemaLocation_LOCATION_UNSPECIFIED:
		return nil, eris.Errorf("Unsupported location %s", req.GetLocation()).WithCode(eris.CodeInvalidArgument)
	}
	return nil, eris.New("unreachable")
}
