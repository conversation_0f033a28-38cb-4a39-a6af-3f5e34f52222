package rwc

import (
	"context"
	"fmt"
	"net/url"
	"path"

	"github.com/risingwavelabs/eris"

	pbrwcsvc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
	"github.com/risingwavelabs/cloudagent/pkg/providers/rwc/schemaregistry"
	"github.com/risingwavelabs/cloudagent/pkg/shared/cloud/aws/credentials"
	"github.com/risingwavelabs/cloudagent/pkg/shared/cloud/aws/s3"
	"github.com/risingwavelabs/cloudagent/pkg/shared/http/httpreq"
)

func GetFromWeb(ctx context.Context, urlStr string) ([]*pbrwcsvc.RawSchemaFile, error) {
	bodyBytes, err := httpreq.Get(ctx, urlStr)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to get web locution"), eris.CodeFailedPrecondition)
	}
	urlParse, err := url.Parse(urlStr)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to parse url"), eris.CodeFailedPrecondition)
	}
	return []*pbrwcsvc.RawSchemaFile{{Content: bodyBytes, Name: path.Base(urlParse.Path)}}, nil
}

func GetFromS3(accessKey credentials.AccessKey, urlStr string, region string) ([]*pbrwcsvc.RawSchemaFile, error) {
	bucket, err := s3.ParseS3Url(urlStr)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to parse s3 url"), eris.CodeInvalidArgument)
	}
	bucket.Region = region
	bodyBytes, err := s3.ReadObject(accessKey, bucket)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to read s3 object"), eris.CodeFailedPrecondition)
	}
	urlParse, err := url.Parse(urlStr)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to parse url"), eris.CodeFailedPrecondition)
	}
	return []*pbrwcsvc.RawSchemaFile{{Content: bodyBytes, Name: path.Base(urlParse.Path)}}, nil
}

func GetFromSchemaRegistry(ctx context.Context, url string, topic string, credential *schemaregistry.Credential) ([]*pbrwcsvc.RawSchemaFile, error) {
	reg := schemaregistry.SchemaRegistry{
		URL:        url,
		Credential: credential,
	}
	subjects, err := reg.GetSubjectWithReferences(ctx, fmt.Sprintf("%s-value", topic), "latest")
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to get schema from schema registry"), eris.CodeFailedPrecondition)
	}
	files := make([]*pbrwcsvc.RawSchemaFile, len(subjects))
	for i, subject := range subjects {
		files[i] = &pbrwcsvc.RawSchemaFile{
			Content: []byte(subject.Schema),
			Name:    subject.Subject,
		}
	}
	return files, nil
}
