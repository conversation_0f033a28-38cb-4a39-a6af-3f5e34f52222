// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/rwc (interfaces: ProviderInterface)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/rwc -package=rwc -destination=pkg/providers/rwc/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/rwc ProviderInterface
//

// Package rwc is a generated GoMock package.
package rwc

import (
	context "context"
	io "io"
	reflect "reflect"

	rwc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
	gomock "go.uber.org/mock/gomock"
)

// MockProviderInterface is a mock of ProviderInterface interface.
type MockProviderInterface struct {
	ctrl     *gomock.Controller
	recorder *MockProviderInterfaceMockRecorder
	isgomock struct{}
}

// MockProviderInterfaceMockRecorder is the mock recorder for MockProviderInterface.
type MockProviderInterfaceMockRecorder struct {
	mock *MockProviderInterface
}

// NewMockProviderInterface creates a new mock instance.
func NewMockProviderInterface(ctrl *gomock.Controller) *MockProviderInterface {
	mock := &MockProviderInterface{ctrl: ctrl}
	mock.recorder = &MockProviderInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProviderInterface) EXPECT() *MockProviderInterfaceMockRecorder {
	return m.recorder
}

// CordonWorkers mocks base method.
func (m *MockProviderInterface) CordonWorkers(ctx context.Context, name, ns string, workers []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CordonWorkers", ctx, name, ns, workers)
	ret0, _ := ret[0].(error)
	return ret0
}

// CordonWorkers indicates an expected call of CordonWorkers.
func (mr *MockProviderInterfaceMockRecorder) CordonWorkers(ctx, name, ns, workers any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CordonWorkers", reflect.TypeOf((*MockProviderInterface)(nil).CordonWorkers), ctx, name, ns, workers)
}

// DeleteSnapshot mocks base method.
func (m *MockProviderInterface) DeleteSnapshot(ctx context.Context, rwversion, name, ns string, snapshotID int64) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSnapshot", ctx, rwversion, name, ns, snapshotID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSnapshot indicates an expected call of DeleteSnapshot.
func (mr *MockProviderInterfaceMockRecorder) DeleteSnapshot(ctx, rwversion, name, ns, snapshotID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSnapshot", reflect.TypeOf((*MockProviderInterface)(nil).DeleteSnapshot), ctx, rwversion, name, ns, snapshotID)
}

// DeleteWorkers mocks base method.
func (m *MockProviderInterface) DeleteWorkers(ctx context.Context, name, ns string, workers []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteWorkers", ctx, name, ns, workers)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteWorkers indicates an expected call of DeleteWorkers.
func (mr *MockProviderInterfaceMockRecorder) DeleteWorkers(ctx, name, ns, workers any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteWorkers", reflect.TypeOf((*MockProviderInterface)(nil).DeleteWorkers), ctx, name, ns, workers)
}

// EtcdctlCommand mocks base method.
func (m *MockProviderInterface) EtcdctlCommand(ctx context.Context, option EtcdctlOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "EtcdctlCommand", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// EtcdctlCommand indicates an expected call of EtcdctlCommand.
func (mr *MockProviderInterfaceMockRecorder) EtcdctlCommand(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "EtcdctlCommand", reflect.TypeOf((*MockProviderInterface)(nil).EtcdctlCommand), ctx, option)
}

// FetchKafkaMessage mocks base method.
func (m *MockProviderInterface) FetchKafkaMessage(ctx context.Context, req *rwc.FetchKafkaMessageRequest) (*KafkaMessage, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchKafkaMessage", ctx, req)
	ret0, _ := ret[0].(*KafkaMessage)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchKafkaMessage indicates an expected call of FetchKafkaMessage.
func (mr *MockProviderInterfaceMockRecorder) FetchKafkaMessage(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchKafkaMessage", reflect.TypeOf((*MockProviderInterface)(nil).FetchKafkaMessage), ctx, req)
}

// FetchKafkaTopic mocks base method.
func (m *MockProviderInterface) FetchKafkaTopic(ctx context.Context, req *rwc.FetchKafkaTopicRequest) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchKafkaTopic", ctx, req)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchKafkaTopic indicates an expected call of FetchKafkaTopic.
func (mr *MockProviderInterfaceMockRecorder) FetchKafkaTopic(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchKafkaTopic", reflect.TypeOf((*MockProviderInterface)(nil).FetchKafkaTopic), ctx, req)
}

// FetchPostgresTable mocks base method.
func (m *MockProviderInterface) FetchPostgresTable(ctx context.Context, req *rwc.FetchPostgresTableRequest) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchPostgresTable", ctx, req)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchPostgresTable indicates an expected call of FetchPostgresTable.
func (mr *MockProviderInterfaceMockRecorder) FetchPostgresTable(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchPostgresTable", reflect.TypeOf((*MockProviderInterface)(nil).FetchPostgresTable), ctx, req)
}

// FetchSourceSchema mocks base method.
func (m *MockProviderInterface) FetchSourceSchema(ctx context.Context, req *rwc.FetchSourceSchemaRequest) ([]*rwc.RawSchemaFile, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FetchSourceSchema", ctx, req)
	ret0, _ := ret[0].([]*rwc.RawSchemaFile)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FetchSourceSchema indicates an expected call of FetchSourceSchema.
func (mr *MockProviderInterfaceMockRecorder) FetchSourceSchema(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FetchSourceSchema", reflect.TypeOf((*MockProviderInterface)(nil).FetchSourceSchema), ctx, req)
}

// GenDiagnosisReport mocks base method.
func (m *MockProviderInterface) GenDiagnosisReport(ctx context.Context, svc, ns string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenDiagnosisReport", ctx, svc, ns)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenDiagnosisReport indicates an expected call of GenDiagnosisReport.
func (mr *MockProviderInterfaceMockRecorder) GenDiagnosisReport(ctx, svc, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenDiagnosisReport", reflect.TypeOf((*MockProviderInterface)(nil).GenDiagnosisReport), ctx, svc, ns)
}

// GenDiagnosisReportBody mocks base method.
func (m *MockProviderInterface) GenDiagnosisReportBody(ctx context.Context, svc, ns string) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenDiagnosisReportBody", ctx, svc, ns)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenDiagnosisReportBody indicates an expected call of GenDiagnosisReportBody.
func (mr *MockProviderInterfaceMockRecorder) GenDiagnosisReportBody(ctx, svc, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenDiagnosisReportBody", reflect.TypeOf((*MockProviderInterface)(nil).GenDiagnosisReportBody), ctx, svc, ns)
}

// GenDiagnosisReportBodyCompressed mocks base method.
func (m *MockProviderInterface) GenDiagnosisReportBodyCompressed(ctx context.Context, svc, ns string) (io.ReadCloser, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GenDiagnosisReportBodyCompressed", ctx, svc, ns)
	ret0, _ := ret[0].(io.ReadCloser)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GenDiagnosisReportBodyCompressed indicates an expected call of GenDiagnosisReportBodyCompressed.
func (mr *MockProviderInterfaceMockRecorder) GenDiagnosisReportBodyCompressed(ctx, svc, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GenDiagnosisReportBodyCompressed", reflect.TypeOf((*MockProviderInterface)(nil).GenDiagnosisReportBodyCompressed), ctx, svc, ns)
}

// GetClusterInfo mocks base method.
func (m *MockProviderInterface) GetClusterInfo(ctx context.Context, name, ns string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterInfo", ctx, name, ns)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterInfo indicates an expected call of GetClusterInfo.
func (mr *MockProviderInterfaceMockRecorder) GetClusterInfo(ctx, name, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterInfo", reflect.TypeOf((*MockProviderInterface)(nil).GetClusterInfo), ctx, name, ns)
}

// GetDiagnosisReportURL mocks base method.
func (m *MockProviderInterface) GetDiagnosisReportURL(svc, ns string) string {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDiagnosisReportURL", svc, ns)
	ret0, _ := ret[0].(string)
	return ret0
}

// GetDiagnosisReportURL indicates an expected call of GetDiagnosisReportURL.
func (mr *MockProviderInterfaceMockRecorder) GetDiagnosisReportURL(svc, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDiagnosisReportURL", reflect.TypeOf((*MockProviderInterface)(nil).GetDiagnosisReportURL), svc, ns)
}

// MetaMigration mocks base method.
func (m *MockProviderInterface) MetaMigration(ctx context.Context, option MetaMigrationOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "MetaMigration", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// MetaMigration indicates an expected call of MetaMigration.
func (mr *MockProviderInterfaceMockRecorder) MetaMigration(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "MetaMigration", reflect.TypeOf((*MockProviderInterface)(nil).MetaMigration), ctx, option)
}

// ResizeWorkers mocks base method.
func (m *MockProviderInterface) ResizeWorkers(ctx context.Context, name, ns string, addedWorkers, deletingWorkers []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ResizeWorkers", ctx, name, ns, addedWorkers, deletingWorkers)
	ret0, _ := ret[0].(error)
	return ret0
}

// ResizeWorkers indicates an expected call of ResizeWorkers.
func (mr *MockProviderInterfaceMockRecorder) ResizeWorkers(ctx, name, ns, addedWorkers, deletingWorkers any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ResizeWorkers", reflect.TypeOf((*MockProviderInterface)(nil).ResizeWorkers), ctx, name, ns, addedWorkers, deletingWorkers)
}

// RestoreMeta mocks base method.
func (m *MockProviderInterface) RestoreMeta(ctx context.Context, option RestoreMetaOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestoreMeta", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestoreMeta indicates an expected call of RestoreMeta.
func (mr *MockProviderInterfaceMockRecorder) RestoreMeta(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestoreMeta", reflect.TypeOf((*MockProviderInterface)(nil).RestoreMeta), ctx, option)
}

// StartMetaNodeBackupTask mocks base method.
func (m *MockProviderInterface) StartMetaNodeBackupTask(ctx context.Context, option MetaNodeBackupOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartMetaNodeBackupTask", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartMetaNodeBackupTask indicates an expected call of StartMetaNodeBackupTask.
func (mr *MockProviderInterfaceMockRecorder) StartMetaNodeBackupTask(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartMetaNodeBackupTask", reflect.TypeOf((*MockProviderInterface)(nil).StartMetaNodeBackupTask), ctx, option)
}

// ValidateSource mocks base method.
func (m *MockProviderInterface) ValidateSource(ctx context.Context, option ValidateSourceOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ValidateSource", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ValidateSource indicates an expected call of ValidateSource.
func (mr *MockProviderInterfaceMockRecorder) ValidateSource(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ValidateSource", reflect.TypeOf((*MockProviderInterface)(nil).ValidateSource), ctx, option)
}
