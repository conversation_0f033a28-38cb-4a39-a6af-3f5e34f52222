//go:build !ut

package schemaregistry_test

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"

	"github.com/risingwavelabs/cloudagent/pkg/providers/rwc/schemaregistry"
)

// This test package requires to set up schema registry firstly.
//
// Usage:
// ```bash
//
// docker compose up
//
// jq '. | {schema: tostring}' movies-raw.avsc | \
//   curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" --data @- http://localhost:8085/subjects/movies-raw-value/versions
//
// jq --raw-input --slurp '{schemaType: "PROTOBUF", schema: .}' user.proto | \
//   curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" --data @- http://localhost:8085/subjects/user.proto/versions
//
// jq --raw-input --slurp '{schemaType: "PROTOBUF", schema: ., references: [{name: "user.proto", subject: "user.proto", version: 1}]}' order.proto | \
//   curl -X POST -H "Content-Type: application/vnd.schemaregistry.v1+json" --data @- http://localhost:8085/subjects/order-value/versions
//
// ```
// Reference: https://github.com/confluentinc/schema-registry-workshop/

func TestGetAvroSchema(t *testing.T) {
	ctx := context.Background()
	registry := &schemaregistry.SchemaRegistry{
		URL: "http://localhost:8085",
	}
	subjects, err := registry.GetSubjectWithReferences(ctx, "movies-raw-value", "latest")
	require.NoError(t, err)
	require.EqualValues(t, 1, len(subjects))
	schema := subjects[0]
	require.EqualValues(t,
		schemaregistry.Subject{
			Subject: "movies-raw-value",
			Schema:  "{\"type\":\"record\",\"name\":\"movie\",\"fields\":[{\"name\":\"movie_id\",\"type\":\"long\"},{\"name\":\"title\",\"type\":\"string\"},{\"name\":\"release_year\",\"type\":\"long\"}]}",
			// ignored field
			Version: schema.Version,
			ID:      schema.ID,
		},
		schema,
	)
}

func TestGetProtobufSchema(t *testing.T) {
	ctx := context.Background()
	registry := &schemaregistry.SchemaRegistry{
		URL: "http://localhost:8085",
	}
	subjects, err := registry.GetSubjectWithReferences(ctx, "order-value", "latest")
	require.NoError(t, err)
	require.NotEmpty(t, subjects)
	primarySubject, dependencySubjects := subjects[0], subjects[1:]
	require.EqualValues(t,
		schemaregistry.Subject{
			Subject:    "order-value",
			Schema:     "syntax = \"proto3\";\npackage basic;\n\nimport \"user.proto\";\n\nmessage Order {\n  User user = 1;\n  string msg = 2;\n}\n",
			SchemaType: "PROTOBUF",
			References: []schemaregistry.Reference{
				{
					Name:    "user.proto",
					Subject: "user.proto",
					Version: primarySubject.References[0].Version,
				},
			},
			// ignored field
			Version: primarySubject.Version,
			ID:      primarySubject.ID,
		},
		primarySubject,
	)
	require.EqualValues(t, 1, len(dependencySubjects))
	dependencySubject := dependencySubjects[0]
	require.EqualValues(t,
		schemaregistry.Subject{
			Subject:    "user.proto",
			Schema:     "syntax = \"proto3\";\npackage basic;\n\nmessage User {\n  uint64 id = 1;\n  string name = 2;\n}\n",
			SchemaType: "PROTOBUF",
			References: nil,
			// ignored field
			Version: dependencySubject.Version,
			ID:      dependencySubject.ID,
		},
		dependencySubject,
	)
}
