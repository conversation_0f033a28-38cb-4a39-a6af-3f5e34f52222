package schemaregistry

import (
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/shared/http/httpreq"
)

type Credential struct {
	Username string
	Password string
}

type SchemaRegistry struct {
	URL        string
	Credential *Credential
}

type Subject struct {
	Subject    string      `json:"subject"`
	Version    int         `json:"version"`
	ID         int         `json:"id"`
	Schema     string      `json:"schema"`
	SchemaType string      `json:"schemaType,omitempty"` // Could be AVRO, PROTOBUF, JSON. default value AVRO
	References []Reference `json:"references,omitempty"` // PROTOBUF (import) and JSON ($ref) may have references
}

type Reference struct {
	Name    string `json:"name"`
	Subject string `json:"subject"`
	Version int    `json:"version"`
}

func (reg *SchemaRegistry) GetSubject(ctx context.Context, subject string, version string) (Subject, error) {
	raw, err := reg.getSubjectRaw(ctx, subject, version)
	if err != nil {
		return Subject{}, err
	}
	sub, err := reg.parseSubject(raw)
	if err != nil {
		return Subject{}, err
	}
	return sub, nil
}

// GetSubjectWithReferences get a subject and collect its dependency.
// Avro schema doesn't have dependency.
// Reference: https://docs.confluent.io/platform/current/schema-registry/develop/api.html#get--subjects-(string-%20subject)-versions-(versionId-%20version) .
func (reg *SchemaRegistry) GetSubjectWithReferences(ctx context.Context, subject string, version string) ([]Subject, error) {
	type subjectVersion struct {
		sub string
		ver string
	}
	var subjects []Subject

	visited := map[string]bool{}
	visited[subject] = true
	var queue = []subjectVersion{{sub: subject, ver: version}}

	for len(queue) != 0 {
		sv := queue[0]
		queue = queue[1:]

		sub, err := reg.GetSubject(ctx, sv.sub, sv.ver)
		if err != nil {
			return nil, err
		}
		subjects = append(subjects, sub)

		for _, ref := range sub.References {
			if !visited[ref.Subject] {
				visited[ref.Subject] = true
				queue = append(queue, subjectVersion{sub: ref.Subject, ver: strconv.Itoa(ref.Version)})
			}
		}
	}

	return subjects, nil
}

func (reg *SchemaRegistry) getSubjectRaw(ctx context.Context, subject string, version string) ([]byte, error) {
	serverURL, err := url.Parse(reg.URL)
	if err != nil {
		return nil, eris.Wrap(err, "failed to parse base url")
	}
	queryURL, err := serverURL.Parse(fmt.Sprintf("./subjects/%s/versions/%s", subject, version))
	if err != nil {
		return nil, eris.Wrap(err, "failed to parse query url")
	}

	bodyBytes, err := httpreq.Get(ctx, queryURL.String(), func(request *http.Request) {
		if reg.Credential != nil {
			cred := reg.Credential.Username + ":" + reg.Credential.Password
			base64Str := base64.StdEncoding.EncodeToString([]byte(cred))
			request.Header.Set("Authorization", "Basic "+base64Str)
		}
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to get subject")
	}
	return bodyBytes, nil
}

func (reg *SchemaRegistry) parseSubject(raw []byte) (Subject, error) {
	subject := Subject{}
	err := json.Unmarshal(raw, &subject)
	return subject, eris.Wrap(err, "failed to unmarshal subject")
}
