package rwc

import (
	"context"
	"fmt"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/risectl"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestStartNewMetaBackupTask(t *testing.T) {
	p, err := NewProvider(NewProviderOption{
		Kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
			TaskConfig: taskconfig.Config{
				Image:          "image",
				ServiceAccount: "sa",
				Namespace:      "ns",
				PullPolicy:     corev1.PullAlways,
			},
		},
	})
	require.NoError(t, err)

	err = p.StartMetaNodeBackupTask(context.Background(), MetaNodeBackupOption{
		RwNamespace: "ns",
		RwName:      "rw",
		TaskID:      "id",
	})
	require.NoError(t, err)
}

func TestValidateSource(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	var (
		name      = "rw"
		namespace = "ns"
		props     = "test"
	)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: namespace,
			RwName:      name,
			Cmd:         []string{"meta", "validate-source", "--props", props},
		},
	).Return("ok", nil)

	err := p.ValidateSource(context.Background(), ValidateSourceOption{
		RwNamespace: namespace,
		RwName:      name,
		Props:       props,
	})
	assert.NoError(t, err)
}

func TestValidateSource_failed(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	var (
		name      = "rw"
		namespace = "ns"
		props     = "error"
	)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: namespace,
			RwName:      name,
			Cmd:         []string{"meta", "validate-source", "--props", props},
		},
	).Return("", eris.Wrap(k8s.ErrFailedCommand, "failed"))

	err := p.ValidateSource(context.Background(), ValidateSourceOption{
		RwNamespace: namespace,
		RwName:      name,
		Props:       "error",
	})
	assert.True(t, utils.IsFailedPrecondition(err))
}

func TestGetClusterInfoSuccess(t *testing.T) {
	var (
		name      = "rw"
		namespace = "ns"
	)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: namespace,
			RwName:      name,
			Cmd:         []string{"meta", "cluster-info"},
		},
	).Return("ok", nil)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	output, err := p.GetClusterInfo(context.Background(), name, namespace)
	assert.NoError(t, err)
	assert.Equal(t, output, "ok")
}

func TestGetClusterInfoFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"meta", "cluster-info"},
		},
	).Return("", eris.Wrap(k8s.ErrFailedCommand, "failed"))

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	_, err := p.GetClusterInfo(context.Background(), "rw", "ns")
	assert.Error(t, err)
}

func TestDeleteWorkersSuccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"meta", "unregister-workers", "--workers", "a,b,c", "--ignore-not-found", "--yes"},
		},
	).Return("ok", nil)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.DeleteWorkers(context.Background(), "rw", "ns", []string{"a", "b", "c"})
	assert.NoError(t, err)
}

func TestDeleteWorkersFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"meta", "unregister-workers", "--workers", "a,b,c", "--ignore-not-found", "--yes"},
		},
	).Return("", eris.Wrap(k8s.ErrFailedCommand, "failed"))

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.DeleteWorkers(context.Background(), "rw", "ns", []string{"a", "b", "c"})
	assert.Error(t, err)
}

func TestCordonWorkersSuccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"scale", "cordon", "--workers", "a,b,c"},
		},
	).Return("ok", nil)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.CordonWorkers(context.Background(), "rw", "ns", []string{"a", "b", "c"})
	assert.NoError(t, err)
}

func TestCordonWorkersFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"scale", "cordon", "--workers", "a,b,c"},
		},
	).Return("", eris.Wrap(k8s.ErrFailedCommand, "failed"))

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.CordonWorkers(context.Background(), "rw", "ns", []string{"a", "b", "c"})
	assert.Error(t, err)
}

func TestResizeWorkersSuccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"scale", "resize", "--yes", "--include-workers", "a,b,c", "--exclude-workers", "e,f"},
		},
	).Return("ok", nil)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.ResizeWorkers(context.Background(), "rw", "ns", []string{"a", "b", "c"}, []string{"e", "f"})
	assert.NoError(t, err)
}

func TestResizeWorkersFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"scale", "resize", "--yes", "--include-workers", "a,b,c", "--exclude-workers", "e,f"},
		},
	).Return("", eris.Wrap(k8s.ErrFailedCommand, "failed"))

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.ResizeWorkers(context.Background(), "rw", "ns", []string{"a", "b", "c"}, []string{"e", "f"})
	assert.Error(t, err)
}

func TestDeleteSnapshot_version_compatibility(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	type TestCase struct {
		version string
		cmd     []string
	}

	var tests = []TestCase{
		{
			version: "v1.9.0-rc1+e2e",
			cmd:     []string{"meta", "delete-meta-snapshots", "1"},
		},
		{
			version: "v1.9.0-rc1",
			cmd:     []string{"meta", "delete-meta-snapshots", "1"},
		},
		{
			version: "v2.0.0",
			cmd:     []string{"meta", "delete-meta-snapshots", "1"},
		},
		{
			version: "v2.0.1",
			cmd:     []string{"meta", "delete-meta-snapshots", "--snapshot-ids", "1"},
		},
		{
			version: "v2.0.2",
			cmd:     []string{"meta", "delete-meta-snapshots", "--snapshot-ids", "1"},
		},
		{
			version: "v2.0.3-rc2",
			cmd:     []string{"meta", "delete-meta-snapshots", "--snapshot-ids", "1"},
		},
		{
			version: "v2.0.3-rc2+test",
			cmd:     []string{"meta", "delete-meta-snapshots", "--snapshot-ids", "1"},
		},
	}

	for _, test := range tests {
		t.Logf("running test case: %s", test.version)

		risectlExecutor := risectl.NewMockExecutor(ctrl)
		risectlExecutor.EXPECT().RunRisectlCommand(
			gomock.Any(),
			risectl.CommandOption{
				RwNamespace: "ns",
				RwName:      "rw",
				Cmd:         test.cmd,
			},
		).Return("ok", nil)

		p := &Provider{
			risectlExecutor: risectlExecutor,
		}

		err := p.DeleteSnapshot(context.Background(), test.version, "rw", "ns", 1)
		assert.NoError(t, err)
	}
}

func TestDeleteSnapshotSuccess(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"meta", "delete-meta-snapshots", "1"},
		},
	).Return("ok", nil)

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.DeleteSnapshot(context.Background(), "v1.9.0", "rw", "ns", 1)
	assert.NoError(t, err)
}

func TestDeleteSnapshotFailure(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	risectlExecutor := risectl.NewMockExecutor(ctrl)

	risectlExecutor.EXPECT().RunRisectlCommand(
		gomock.Any(),
		risectl.CommandOption{
			RwNamespace: "ns",
			RwName:      "rw",
			Cmd:         []string{"meta", "delete-meta-snapshots", "1"},
		},
	).Return("", eris.Wrap(k8s.ErrFailedCommand, "failed"))

	p := &Provider{
		risectlExecutor: risectlExecutor,
	}

	err := p.DeleteSnapshot(context.Background(), "v1.9.0", "rw", "ns", 1)
	assert.Error(t, err)
}

func TestGetCommand(t *testing.T) {
	type testCase struct {
		option   RestoreMetaOption
		expected []string
	}

	var tests = []testCase{
		{
			option: RestoreMetaOption{
				MetaStoreType:     "etcd",
				EtcdEndpoints:     "my-etcd:2379",
				MetaSnapshotID:    2,
				BackupStorageURL:  "s3://bucket/mob",
				BackupStorageDir:  "backup-dir",
				HummockStorageURL: "s3://bucket/hummock",
				HummockStorageDir: "data-dir",
				EtcdAuth:          true,
				EtcdUsername:      "root",
				EtcdPassword:      "pwd",
			},
			expected: []string{
				"/risingwave/bin/risingwave",
				"ctl",
				"meta",
				"restore-meta",
				"--etcd-endpoints",
				"my-etcd:2379",
				"--etcd-auth",
				"--etcd-username",
				"root",
				"--etcd-password",
				"pwd",
				"--meta-store-type",
				"etcd",
				"--meta-snapshot-id",
				"2",
				"--backup-storage-url",
				"s3://bucket/mob",
				"--backup-storage-directory",
				"backup-dir",
				"--hummock-storage-url",
				"s3://bucket/hummock",
				"--hummock-storage-directory",
				"data-dir",
			},
		},
		{
			option: RestoreMetaOption{
				MetaStoreType:     "etcd",
				EtcdEndpoints:     "my-etcd:2379",
				MetaSnapshotID:    2,
				BackupStorageURL:  "s3://bucket/mob",
				BackupStorageDir:  "backup-dir",
				HummockStorageURL: "s3://bucket/hummock",
				HummockStorageDir: "data-dir",
			},
			expected: []string{
				"/risingwave/bin/risingwave",
				"ctl",
				"meta",
				"restore-meta",
				"--etcd-endpoints",
				"my-etcd:2379",
				"--meta-store-type",
				"etcd",
				"--meta-snapshot-id",
				"2",
				"--backup-storage-url",
				"s3://bucket/mob",
				"--backup-storage-directory",
				"backup-dir",
				"--hummock-storage-url",
				"s3://bucket/hummock",
				"--hummock-storage-directory",
				"data-dir",
			},
		},
		{
			option: RestoreMetaOption{
				MetaStoreType:     "sql",
				SQLEndpoint:       "postgresql://username:password@host:port/database",
				MetaSnapshotID:    2,
				BackupStorageURL:  "s3://bucket/mob",
				BackupStorageDir:  "backup-dir",
				HummockStorageURL: "s3://bucket/hummock",
				HummockStorageDir: "data-dir",
			},
			expected: []string{
				"/risingwave/bin/risingwave",
				"ctl",
				"meta",
				"restore-meta",
				"--sql-endpoint",
				"postgresql://username:password@host:port/database",
				"--meta-store-type",
				"sql",
				"--meta-snapshot-id",
				"2",
				"--backup-storage-url",
				"s3://bucket/mob",
				"--backup-storage-directory",
				"backup-dir",
				"--hummock-storage-url",
				"s3://bucket/hummock",
				"--hummock-storage-directory",
				"data-dir",
			},
		},
	}

	for i, test := range tests {
		t.Logf("running test case: %d", i)
		assert.Equal(t, test.expected, test.option.getCommand())
	}
}

func TestEtcdctlCommand(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	var (
		ctx          = context.Background()
		podName      = "pod"
		podNamespace = "namespace"
		etcdUsername = "user"
		etcdPassword = "password"
		etcdCommand  = []string{"get", "--prefix=true", "/"}
	)

	cmd := []string{
		"env",
		"ETCDCTL_API=3",
		"etcdctl",
		fmt.Sprintf("--user=%s", etcdUsername),
		fmt.Sprintf("--password=%s", etcdPassword),
	}
	cmd = append(cmd, etcdCommand...)

	kc := k8s.NewMockKubernetesClientInterface(ctrl)
	kc.
		EXPECT().
		ExecPod(ctx, podName, podNamespace, cmd).
		Return("2", "", nil)

	p := &Provider{
		kc: kc,
	}

	err := p.EtcdctlCommand(ctx, EtcdctlOption{
		EtcdPodName:      podName,
		EtcdPodNamespace: podNamespace,
		EtcdUsername:     etcdUsername,
		EtcdPassword:     etcdPassword,
		EtcdCommand:      etcdCommand,
	})
	assert.NoError(t, err)
}

func TestMetaMigration(t *testing.T) {
	taskID := "task"
	taskNamespace := "task"
	rwName := "rw"
	rwNamespace := "ns"
	tests := []struct {
		name          string
		mockK8sClient func(*k8s.MockKubernetesClientInterface)
		wantErr       bool
	}{
		{
			name: "regular",
			mockK8sClient: func(kc *k8s.MockKubernetesClientInterface) {
				resources := corev1.ResourceRequirements{
					Requests: corev1.ResourceList{
						corev1.ResourceCPU:    resource.MustParse("1"),
						corev1.ResourceMemory: resource.MustParse("1Gi"),
					},
					Limits: corev1.ResourceList{
						corev1.ResourceCPU:    resource.MustParse("1"),
						corev1.ResourceMemory: resource.MustParse("1Gi"),
					},
				}
				kc.EXPECT().RunTask(context.Background(), k8s.RunTaskOption{
					Name:      taskID,
					Namespace: taskNamespace,
					Image:     "test_rw_image",
					Command: []string{
						"/risingwave/bin/risingwave", "ctl",
						"meta", "migration", "--etcd-endpoints", "etcd_endpoints", "--sql-endpoint", "sql_endpoint", "-f",
					},
					ImagePullPolicy: corev1.PullIfNotPresent,
					Resources:       &resources,
				}).Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			k8sclient := k8s.NewMockKubernetesClientInterface(ctrl)
			if tt.mockK8sClient != nil {
				tt.mockK8sClient(k8sclient)
			}

			p := &Provider{
				kc: k8sclient,
			}

			err := p.MetaMigration(context.Background(), MetaMigrationOption{
				TaskID:        taskID,
				TaskNamespace: taskNamespace,
				RwName:        rwName,
				RwNamespace:   rwNamespace,
				EtcdEndpoints: "etcd_endpoints",
				SQLEndpoint:   "sql_endpoint",
				TaskImage:     "test_rw_image",
				TaskResources: &pbk8s.ResourceRequirements{
					CpuRequest:    "1",
					CpuLimit:      "1",
					MemoryRequest: "1Gi",
					MemoryLimit:   "1Gi",
				},
			})
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
		})
	}
}
