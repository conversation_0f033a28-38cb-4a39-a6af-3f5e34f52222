package rwc

import (
	"context"
	"crypto/tls"
	"crypto/x509"
	"slices"
	"time"

	"maps"

	"github.com/risingwavelabs/eris"
	"github.com/segmentio/kafka-go"
	"github.com/segmentio/kafka-go/sasl"
	"github.com/segmentio/kafka-go/sasl/plain"
	"github.com/segmentio/kafka-go/sasl/scram"

	pbrwcsvc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
)

const (
	KafkaConnectionTimeout = 10 * time.Second
	KafkaMessageMaxBytes   = 10 * 1024 * 1024
)

func (p *Provider) FetchKafkaTopic(ctx context.Context, req *pbrwcsvc.FetchKafkaTopicRequest) ([]string, error) {
	dialer, err := createKafkaDialer(req.GetKafka())
	if err != nil {
		return nil, err
	}
	ctx, cancel := context.WithTimeout(ctx, KafkaConnectionTimeout)
	defer cancel()

	conn, err := dialer.DialContext(ctx, "tcp", req.Get<PERSON>afka().GetServer())
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to create dial to kafka"), eris.CodeFailedPrecondition)
	}
	defer func() { _ = conn.Close() }()

	partitions, err := conn.ReadPartitions()
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to read topics from kafka"), eris.CodeFailedPrecondition)
	}
	m := map[string]struct{}{}
	for _, p := range partitions {
		m[p.Topic] = struct{}{}
	}
	return slices.Collect(maps.Keys(m)), nil
}

type KafkaMessage struct {
	Key   []byte
	Value []byte
}

func (p *Provider) FetchKafkaMessage(ctx context.Context, req *pbrwcsvc.FetchKafkaMessageRequest) (*KafkaMessage, error) {
	dialer, err := createKafkaDialer(req.GetKafka())
	if err != nil {
		return nil, err
	}
	ctx, cancel := context.WithTimeout(ctx, KafkaConnectionTimeout)
	defer cancel()

	conn, err := dialer.DialLeader(ctx, "tcp", req.GetKafka().GetServer(), req.GetTopic(), 0)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to create dial to kafka"), eris.CodeFailedPrecondition)
	}
	defer func() { _ = conn.Close() }()

	offset, err := conn.ReadFirstOffset()
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to read first offset"), eris.CodeFailedPrecondition)
	}
	_, err = conn.Seek(offset, kafka.SeekAbsolute)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to seek offset"), eris.CodeFailedPrecondition)
	}

	msg, err := conn.ReadMessage(KafkaMessageMaxBytes)
	if err != nil {
		return nil, eris.WithCode(eris.Wrap(err, "failed to read message"), eris.CodeFailedPrecondition)
	}

	return &KafkaMessage{
		Key:   msg.Key,
		Value: msg.Value,
	}, nil
}

func createKafkaDialer(config *pbrwcsvc.KafkaConfig) (*kafka.Dialer, error) {
	dialer := &kafka.Dialer{}

	if config.SecurityProtocol != nil {
		switch config.GetSecurityProtocol() {
		case pbrwcsvc.KafkaSecurityProtocol_SASL_SSL:
			if config.GetSaslMechanism() == pbrwcsvc.KafkaSaslMechanism_MECHANISM_UNSPECIFIED {
				return nil, eris.New("Missing kafka SASL Mechanism").WithCode(eris.CodeInvalidArgument)
			}
			if config.SaslUsername == nil || config.SaslPassword == nil {
				return nil, eris.New("Missing kafka SASL username or password").WithCode(eris.CodeInvalidArgument)
			}
			mechanism, err := parseSaslMechanism(config.GetSaslMechanism(), config.GetSaslUsername(), config.GetSaslPassword())
			if err != nil {
				return nil, err
			}
			dialer.TLS = &tls.Config{}
			if config.CaCertificate != nil {
				caCertPool := x509.NewCertPool()
				caCertPool.AppendCertsFromPEM([]byte(config.GetCaCertificate()))
				dialer.TLS.RootCAs = caCertPool
			}
			dialer.SASLMechanism = mechanism
		case pbrwcsvc.KafkaSecurityProtocol_SASL_PLAINTEXT:
			if config.GetSaslMechanism() == pbrwcsvc.KafkaSaslMechanism_MECHANISM_UNSPECIFIED {
				return nil, eris.New("Missing kafka SASL Mechanism").WithCode(eris.CodeInvalidArgument)
			}
			if config.SaslUsername == nil || config.SaslPassword == nil {
				return nil, eris.New("Missing kafka SASL username or password").WithCode(eris.CodeInvalidArgument)
			}
			mechanism, err := parseSaslMechanism(config.GetSaslMechanism(), config.GetSaslUsername(), config.GetSaslPassword())
			if err != nil {
				return nil, err
			}
			dialer.SASLMechanism = mechanism
		case pbrwcsvc.KafkaSecurityProtocol_SSL:
			dialer.TLS = &tls.Config{}
			if config.CaCertificate != nil {
				caCertPool := x509.NewCertPool()
				caCertPool.AppendCertsFromPEM([]byte(config.GetCaCertificate()))
				dialer.TLS.RootCAs = caCertPool
			}
		case pbrwcsvc.KafkaSecurityProtocol_PROTOCOL_UNSPECIFIED:
			return nil, eris.Errorf("Unsupported Security Protocol %s", config.GetSecurityProtocol()).WithCode(eris.CodeInvalidArgument)
		}
	}

	return dialer, nil
}

func parseSaslMechanism(mechanism pbrwcsvc.KafkaSaslMechanism, username string, password string) (sasl.Mechanism, error) {
	switch mechanism {
	case pbrwcsvc.KafkaSaslMechanism_PLAIN:
		return plain.Mechanism{
			Username: username,
			Password: password,
		}, nil
	case pbrwcsvc.KafkaSaslMechanism_SCRAM_SHA_256:
		mechanism, err := scram.Mechanism(scram.SHA256, username, password)
		if err != nil {
			return nil, eris.Wrap(err, "failed to apply SASL mechanism")
		}
		return mechanism, nil
	case pbrwcsvc.KafkaSaslMechanism_SCRAM_SHA_512:
		mechanism, err := scram.Mechanism(scram.SHA512, username, password)
		if err != nil {
			return nil, eris.Wrap(err, "failed to apply SASL mechanism")
		}
		return mechanism, nil
	case pbrwcsvc.KafkaSaslMechanism_MECHANISM_UNSPECIFIED:
		return nil, eris.Errorf("Unsupported SASL Mechanism %s", mechanism).WithCode(eris.CodeInvalidArgument)
	}
	return nil, eris.New("unreachable")
}
