package byoc

import (
	"context"
	"encoding/base64"
	"fmt"
	"maps"

	"github.com/risingwavelabs/cloudagent/pkg/helmx"
	"github.com/risingwavelabs/cloudagent/pkg/logger"

	"github.com/risingwavelabs/byoc-runtime/pkg/terraform"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"

	pbcommonbyoc "github.com/risingwavelabs/cloudagent/pbgen/common/byoc"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskbyoc "github.com/risingwavelabs/cloudagent/pbgen/task/byoc"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type ApplyModuleOptions struct {
	TaskID          string
	TaskNamespace   string
	ApplyOptions    *pbcommonbyoc.ApplyOptions
	ModuleOptions   *pbcommonbyoc.ModuleOptions
	PackageOptions  *pbcommonbyoc.PackageOptions
	TaskTolerations []*pbk8s.Toleration
	TaskAffinity    *pbk8s.Affinity
}

type RetrieveModuleOutputOptions struct {
	TaskID          string
	TaskNamespace   string
	OutputKey       string
	ApplyOptions    *pbcommonbyoc.ApplyOptions
	ModuleOptions   *pbcommonbyoc.ModuleOptions
	PackageOptions  *pbcommonbyoc.PackageOptions
	OutputOptions   *pbcommonbyoc.OutputOptions
	TaskTolerations []*pbk8s.Toleration
	TaskAffinity    *pbk8s.Affinity
}

// creates a task in taskrunner for the async application of terraform module.
func (provider *Provider) RunApplyByocModuleTask(ctx context.Context, options *ApplyModuleOptions) error {
	var err error
	var tolerations []corev1.Toleration
	for _, tp := range options.TaskTolerations {
		t, err := conversion.FromTolerationProto(tp)
		if err != nil {
			return eris.WithCode(eris.Wrap(err, "failed to convert task toleration proto"), eris.CodeInvalidArgument)
		}
		tolerations = append(tolerations, t)
	}

	var affinity *corev1.Affinity
	if options.TaskAffinity != nil {
		affinity, err = conversion.FromAffinityProto(options.TaskAffinity)
		if err != nil {
			return eris.WithCode(eris.Wrap(err, "failed to convert task affinity proto"), eris.CodeInvalidArgument)
		}
	}

	envs := helmx.GetDirectoriesEnvVars(k8s.TaskEphemeralVolumePath)
	maps.Copy(envs, options.ModuleOptions.GetTaskrunnerEnvOverrides())

	err = provider.kc.StartTaskRunnerWithOverrides(
		ctx,
		options.TaskID,
		options.TaskNamespace,
		&pbtask.Task{
			Task: &pbtask.Task_ApplyByocModuleTask{
				ApplyByocModuleTask: &pbtaskbyoc.ApplyByocModuleTask{
					ApplyOptions:   options.ApplyOptions,
					ModuleOptions:  options.ModuleOptions,
					PackageOptions: options.PackageOptions,
				},
			},
		},
		k8s.TaskRunnerOverrides{
			Tolerations: &tolerations,
			Affinity:    &affinity,
			Envs:        &envs,
		},
	)

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to start apply byoc module task")
	}
	return nil
}

func (provider *Provider) ApplyByocModule(ctx context.Context, options *ApplyModuleOptions) error {
	tf, err := terraform.New(ctx, fromProtoPackageOptionToTerraform(options.PackageOptions))
	if err != nil {
		return eris.Wrap(err, "failed to init terraform client")
	}

	err = tf.ApplyModule(ctx, fromProtoModuleOptionToTerraform(options.ModuleOptions), fromProtoApplyOptionToTerraform(options.ApplyOptions))
	if err != nil {
		return eris.Wrap(err, "failed to apply byoc module")
	}

	return nil
}

// creates a task in taskrunner for the async retrieval of a terraform module
// output.
func (provider *Provider) RunRetrieveByocModuleOutputTask(ctx context.Context, options *RetrieveModuleOutputOptions) error {
	var err error
	var tolerations []corev1.Toleration
	for _, tp := range options.TaskTolerations {
		t, err := conversion.FromTolerationProto(tp)
		if err != nil {
			return eris.WithCode(eris.Wrap(err, "failed to convert task toleration proto"), eris.CodeInvalidArgument)
		}
		tolerations = append(tolerations, t)
	}

	var affinity *corev1.Affinity
	if options.TaskAffinity != nil {
		affinity, err = conversion.FromAffinityProto(options.TaskAffinity)
		if err != nil {
			return eris.WithCode(eris.Wrap(err, "failed to convert task affinity proto"), eris.CodeInvalidArgument)
		}
	}
	err = provider.kc.StartTaskRunnerWithOverrides(
		ctx,
		options.TaskID,
		options.TaskNamespace,
		&pbtask.Task{
			Task: &pbtask.Task_RetrieveByocModuleOutputTask{
				RetrieveByocModuleOutputTask: &pbtaskbyoc.RetrieveByocModuleOutputTask{
					OutputKey:      options.OutputKey,
					ModuleOptions:  options.ModuleOptions,
					OutputOptions:  options.OutputOptions,
					PackageOptions: options.PackageOptions,
				},
			},
		},
		k8s.TaskRunnerOverrides{
			Tolerations: &tolerations,
			Affinity:    &affinity,
		},
	)

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to start retrieve byoc module output task")
	}
	return nil
}

func (provider *Provider) RetrieveByocModuleOutput(ctx context.Context, options *RetrieveModuleOutputOptions) error {
	tf, err := terraform.New(ctx, fromProtoPackageOptionToTerraform(options.PackageOptions))
	if err != nil {
		return eris.Wrap(err, "failed to init terraform client")
	}

	res, err := tf.RetrieveModuleOutput(
		ctx,
		options.OutputKey,
		fromProtoModuleOptionToTerraform(options.ModuleOptions),
		fromProtoOutputOptionToTerraform(options.OutputOptions),
	)

	if err != nil {
		return eris.Wrap(err, "failed to retrieve byoc module output")
	}

	logger.FromCtx(ctx).Info(fmt.Sprintf("Retrieve BYOC module output successful: \n%v", string(res)))
	logger.FromCtx(ctx).Info(fmt.Sprintf("Encoded result: %s", base64.StdEncoding.EncodeToString(res)))

	return nil
}
