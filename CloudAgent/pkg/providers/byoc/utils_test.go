package byoc

import (
	"os"
	"testing"
	"time"

	"github.com/golang/protobuf/ptypes/duration"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbcommonbyoc "github.com/risingwavelabs/cloudagent/pbgen/common/byoc"
)

func TestFromProtoApplyOptionToTerraform(t *testing.T) {
	opt := &pbcommonbyoc.ApplyOptions{
		Retry:                  3,
		RetryInterval:          &duration.Duration{Seconds: 10},
		GracefulShutdownPeriod: &duration.Duration{Seconds: 20},
		LockExpirationDuration: &duration.Duration{Seconds: 30},
		InitOptions: &pbcommonbyoc.TFInitOptions{
			Retry:         2,
			RetryInterval: &duration.Duration{Seconds: 5},
		},
	}

	result := fromProtoApplyOptionToTerraform(opt)

	assert.Equal(t, 3, result.Retry)
	assert.Equal(t, 10*time.Second, result.RetryInterval)
	assert.Equal(t, 20*time.Second, result.GracefulShutdownPeriod)
	assert.Equal(t, 30*time.Second, result.LockExpirationDuration)
	assert.Equal(t, 2, result.InitOptions.Retry)
	assert.Equal(t, 5*time.Second, result.InitOptions.RetryInterval)
}

func TestFromProtoPackageOptionToTerraform(t *testing.T) {
	opt := &pbcommonbyoc.PackageOptions{
		TfVersionFilePath: "version/path",
		RootPath:          "root/path",
		PackageDestName:   "dest/name",
		PackageUrl:        "http://package.url",
	}

	result := fromProtoPackageOptionToTerraform(opt)

	assert.Equal(t, "version/path", result.TFVersionFilePath)
	assert.Equal(t, "root/path", result.RootPath)
	assert.Equal(t, "dest/name", result.PackageDestName)
	assert.Equal(t, "http://package.url", result.PackageURL)
}

func TestFromProtoModuleOptionToTerraform(t *testing.T) {
	require.NoError(t, os.Setenv("TEST_AZURE_FEDERATED_TOKEN_FILE", "test"))
	require.NoError(t, os.Setenv("TEST_AZURE_RANDOM_PARAM", "random"))

	opt := &pbcommonbyoc.ModuleOptions{
		ModulePath:            "module/path",
		BackendConfigFileName: "backend/config",
		BackendConfig:         []byte("a = \"$TEST_AZURE_FEDERATED_TOKEN_FILE\" b = \"$TEST_AZURE_RANDOM_PARAM\""),
		SensitiveVariables:    map[string]string{"sensitveKey": "sensitveVal"},
		VariableFileName:      "variable/file",
		VariablePayload:       []byte("variable/payload"),
		BackendConfigEnvVarOverrides: map[string]string{
			"$TEST_AZURE_FEDERATED_TOKEN_FILE": "TEST_AZURE_FEDERATED_TOKEN_FILE",
			"$TEST_AZURE_RANDOM_PARAM":         "TEST_AZURE_RANDOM_PARAM",
		},
	}

	result := fromProtoModuleOptionToTerraform(opt)

	assert.Equal(t, "module/path", result.ModulePath)
	assert.Equal(t, "backend/config", result.BackendConfigFileName)
	assert.Equal(t, []uint8([]byte("a = \"test\" b = \"random\"")), result.BackendConfig)
	assert.Equal(t, map[string]string{"sensitveKey": "sensitveVal"}, result.SensitiveVariables)
	assert.Equal(t, "variable/file", result.VariableFileName)
	assert.Equal(t, []uint8([]byte("variable/payload")), result.VariablePayload)
}

func TestFromProtoOutputOptionToTerraform(t *testing.T) {
	opt := &pbcommonbyoc.OutputOptions{
		Retry:         3,
		RetryInterval: &duration.Duration{Seconds: 15},
		InitOptions: &pbcommonbyoc.TFInitOptions{
			Retry:         1,
			RetryInterval: &duration.Duration{Seconds: 7},
		},
	}

	result := fromProtoOutputOptionToTerraform(opt)

	assert.Equal(t, 3, result.Retry)
	assert.Equal(t, 15*time.Second, result.RetryInterval)
	assert.Equal(t, 1, result.InitOptions.Retry)
	assert.Equal(t, 7*time.Second, result.InitOptions.RetryInterval)
}
