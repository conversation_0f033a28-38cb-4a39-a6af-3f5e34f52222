package byoc

import (
	"os"
	"slices"
	"strings"

	"github.com/risingwavelabs/byoc-runtime/pkg/terraform"

	pbcommonbyoc "github.com/risingwavelabs/cloudagent/pbgen/common/byoc"
)

func fromProtoApplyOptionToTerraform(opt *pbcommonbyoc.ApplyOptions) terraform.ApplyOptions {
	return terraform.ApplyOptions{
		Retry:                  int(opt.GetRetry()),
		RetryInterval:          opt.GetRetryInterval().AsDuration(),
		GracefulShutdownPeriod: opt.GetGracefulShutdownPeriod().AsDuration(),
		LockExpirationDuration: opt.GetLockExpirationDuration().AsDuration(),
		InitOptions: terraform.TFInitOptions{
			Retry:         int(opt.GetInitOptions().GetRetry()),
			RetryInterval: opt.GetInitOptions().GetRetryInterval().AsDuration(),
		},
	}
}

func fromProtoPackageOptionToTerraform(opt *pbcommonbyoc.PackageOptions) terraform.NewTerraformOptions {
	return terraform.NewTerraformOptions{
		TFVersionFilePath: opt.GetTfVersionFilePath(),
		RootPath:          opt.GetRootPath(),
		PackageDestName:   opt.GetPackageDestName(),
		PackageURL:        opt.GetPackageUrl(),
	}
}

func fromProtoModuleOptionToTerraform(opt *pbcommonbyoc.ModuleOptions) terraform.ModuleOptions {
	// Sort the key before iteration so that the result is deterministic (in case
	// where one key is another key's substring)
	overridingKeys := make([]string, 0, len(opt.GetBackendConfigEnvVarOverrides()))
	for k := range opt.GetBackendConfigEnvVarOverrides() {
		overridingKeys = append(overridingKeys, k)
	}
	slices.Sort(overridingKeys)

	backendCfgStr := string(opt.GetBackendConfig())
	for _, key := range overridingKeys {
		envVarVal, _ := os.LookupEnv(opt.GetBackendConfigEnvVarOverrides()[key])
		backendCfgStr = strings.ReplaceAll(backendCfgStr, key, envVarVal)
	}

	return terraform.ModuleOptions{
		ModulePath:            opt.GetModulePath(),
		BackendConfigFileName: opt.GetBackendConfigFileName(),
		BackendConfig:         []byte(backendCfgStr),
		SensitiveVariables:    opt.GetSensitiveVariables(),
		VariableFileName:      opt.GetVariableFileName(),
		VariablePayload:       opt.GetVariablePayload(),
	}
}

func fromProtoOutputOptionToTerraform(opt *pbcommonbyoc.OutputOptions) terraform.OutputOptions {
	return terraform.OutputOptions{
		Retry:         int(opt.GetRetry()),
		RetryInterval: opt.GetRetryInterval().AsDuration(),
		InitOptions: terraform.TFInitOptions{
			Retry:         int(opt.GetInitOptions().GetRetry()),
			RetryInterval: opt.GetInitOptions().GetRetryInterval().AsDuration(),
		},
	}
}
