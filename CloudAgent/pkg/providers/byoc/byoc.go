package byoc

import (
	rbacv1 "k8s.io/api/rbac/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
)

type Provider struct {
	kc *k8s.KubernetesClient
}

func NewProvider(kc *k8s.KubernetesClient) (*Provider, error) {
	if kc == nil {
		return nil, eris.New("Kubernetes client cannot be nil")
	}

	return &Provider{
		kc: kc,
	}, nil
}

// for testing purposes only.
func CreateFakeProvider(client k8sclient.Client) *Provider {
	k8sClient := &k8s.KubernetesClient{
		Client:    client,
		Interface: fake.NewClientset(&rbacv1.ClusterRole{}),

		TaskConfig: taskconfig.Config{
			Image:          "image",
			ServiceAccount: "serviceAccount",
			PullPolicy:     "Always",
		},
	}
	return &Provider{
		kc: k8sClient,
	}
}
