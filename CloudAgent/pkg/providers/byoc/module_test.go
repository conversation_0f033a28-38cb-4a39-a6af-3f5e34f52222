package byoc

import (
	"context"
	"sort"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"

	pbbyoc "github.com/risingwavelabs/cloudagent/pbgen/common/byoc"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	taskName      = "task-id"
	taskNamespace = "ns"
)

func TestRunApplyBYOCModuleTask(t *testing.T) {
	ctx := context.Background()
	k8Client := fake.NewClient()
	p := CreateFakeProvider(k8Client)

	options := ApplyModuleOptions{
		TaskID:        taskName,
		TaskNamespace: taskNamespace,
		ModuleOptions: &pbbyoc.ModuleOptions{
			TaskrunnerEnvOverrides: map[string]string{
				"ARM_USE_AKS_WORKLOAD_IDENTITY": "true",
				"ARM_USE_AZUREAD":               "true",
				"ARM_USE_CLI":                   "false",
			},
		},
		TaskAffinity: &pbk8s.Affinity{
			NodeAffinity: &pbk8s.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
					NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
						{
							MatchExpressions: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key1",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									Values:   []string{"val1", "val2"},
								},
							},
							MatchFields: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key2",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
									Values:   []string{"val3", "val4"},
								},
							},
						},
					},
				},
			},
		},
		TaskTolerations: []*pbk8s.Toleration{
			{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
				TolerationSecs: utils.Ptr(int64(10)),
			},
		},
	}

	err := p.RunApplyByocModuleTask(ctx, &options)
	require.NoError(t, err)

	job, err := k8s.GetResource[batchv1.Job](context.Background(), p.kc.Client, taskName, taskNamespace)
	require.NoError(t, err)
	assert.Equal(t, job.Spec.Template.Spec.Affinity, &corev1.Affinity{
		NodeAffinity: &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      "key1",
								Operator: corev1.NodeSelectorOpDoesNotExist,
								Values:   []string{"val1", "val2"},
							},
						},
						MatchFields: []corev1.NodeSelectorRequirement{
							{
								Key:      "key2",
								Operator: corev1.NodeSelectorOpNotIn,
								Values:   []string{"val3", "val4"},
							},
						},
					},
				},
			},
		},
	})
	assert.Equal(t, job.Spec.Template.Spec.Tolerations, []corev1.Toleration{
		{
			Key:               "testkey",
			Value:             "testvalue",
			Effect:            corev1.TaintEffectNoExecute,
			Operator:          corev1.TolerationOpEqual,
			TolerationSeconds: utils.Ptr(int64(10)),
		},
	})
	containerEnvs := job.Spec.Template.Spec.Containers[0].Env
	sort.Slice(containerEnvs, func(a, b int) bool {
		return containerEnvs[a].Name < containerEnvs[b].Name
	})
	assert.Equal(t, containerEnvs, []corev1.EnvVar{
		{Name: "ARM_USE_AKS_WORKLOAD_IDENTITY", Value: "true"},
		{Name: "ARM_USE_AZUREAD", Value: "true"},
		{Name: "ARM_USE_CLI", Value: "false"},
		{Name: "XDG_CACHE_HOME", Value: "/ephemeral/.cache"},
		{Name: "XDG_CONFIG_HOME", Value: "/ephemeral/.config"},
		{Name: "XDG_DATA_HOME", Value: "/ephemeral/.local/share"},
	})
}

func TestRunRetrieveBYOCModuleOutputTask(t *testing.T) {
	ctx := context.Background()
	k8Client := fake.NewClient()
	p := CreateFakeProvider(k8Client)

	options := RetrieveModuleOutputOptions{
		TaskID:        taskName,
		TaskNamespace: taskNamespace,

		TaskAffinity: &pbk8s.Affinity{
			NodeAffinity: &pbk8s.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
					NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
						{
							MatchExpressions: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key1",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									Values:   []string{"val1", "val2"},
								},
							},
							MatchFields: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key2",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
									Values:   []string{"val3", "val4"},
								},
							},
						},
					},
				},
			},
		},
		TaskTolerations: []*pbk8s.Toleration{
			{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
				TolerationSecs: utils.Ptr(int64(10)),
			},
		},
	}

	err := p.RunRetrieveByocModuleOutputTask(ctx, &options)
	require.NoError(t, err)

	job, err := k8s.GetResource[batchv1.Job](context.Background(), p.kc.Client, taskName, taskNamespace)
	require.NoError(t, err)

	assert.Equal(t, job.Spec.Template.Spec.Affinity, &corev1.Affinity{
		NodeAffinity: &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      "key1",
								Operator: corev1.NodeSelectorOpDoesNotExist,
								Values:   []string{"val1", "val2"},
							},
						},
						MatchFields: []corev1.NodeSelectorRequirement{
							{
								Key:      "key2",
								Operator: corev1.NodeSelectorOpNotIn,
								Values:   []string{"val3", "val4"},
							},
						},
					},
				},
			},
		},
	})
	assert.Equal(t, job.Spec.Template.Spec.Tolerations, []corev1.Toleration{
		{
			Key:               "testkey",
			Value:             "testvalue",
			Effect:            corev1.TaintEffectNoExecute,
			Operator:          corev1.TolerationOpEqual,
			TolerationSeconds: utils.Ptr(int64(10)),
		},
	})
}
