package aws

import (
	"context"
	"encoding/json"
	"testing"
	"time"

	ackrdsv1alpha1 "github.com/aws-controllers-k8s/rds-controller/apis/v1alpha1"
	"github.com/aws-controllers-k8s/rds-controller/pkg/resource/db_instance"
	rdsTypes "github.com/aws/aws-sdk-go-v2/service/rds/types"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbaws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func getDBInstanceSpecProto() *pbaws.DBInstanceSpec {
	return &pbaws.DBInstanceSpec{
		DbInstanceIdentifier: "test-id",
		DbInstanceClass:      "db.t4g.micro",
		AllocatedStorage:     20,
		Engine:               "postgres",
		EngineVersion:        "14",
		DbName:               "database1",
		MasterUsername:       "user1",
		MasterUserPassword: &pbaws.PasswordSecretRef{
			Namespace: "test-namespace",
			Name:      "test-name",
			Key:       "password",
		},
		DbSubnetGroupName:   "test-subnet",
		VpcSecurityGroupIds: []string{"sg-test"},
		Tags: map[string]string{
			"tag1": "value1",
		},
	}
}

func generateSpec(modifiers ...func(*pbaws.DBInstanceSpec)) *pbaws.DBInstanceSpec {
	proto := getDBInstanceSpecProto()
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func genDbinstance(modifiers ...func(*ackrdsv1alpha1.DBInstance)) *ackrdsv1alpha1.DBInstance {
	spec, err := conversion.FromDBInstanceSpecProto(generateSpec())
	if err != nil {
		panic(err)
	}
	dbinstance := &ackrdsv1alpha1.DBInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rds",
			Namespace: "namespace",
		},
		Spec: *spec,
		Status: ackrdsv1alpha1.DBInstanceStatus{
			DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
		},
	}
	for _, m := range modifiers {
		m(dbinstance)
	}
	return dbinstance
}

func TestProvider_CreateDBInstance(t *testing.T) {
	tests := []struct {
		name          string
		option        CreateDBInstanceOption
		initObjs      []k8sclient.Object
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "normal case",
			option: CreateDBInstanceOption{
				ResourceID: "test-rds",
				Namespace:  "namespace",
				Spec:       generateSpec(),
			},
			wantErr: false,
		},
		{
			name: "already exist",
			initObjs: []k8sclient.Object{
				genDbinstance(func(dbinstance *ackrdsv1alpha1.DBInstance) {
					dbinstance.Status.DBInstanceStatus = utils.Ptr(db_instance.StatusCreating)
				}),
			},
			option: CreateDBInstanceOption{
				ResourceID: "test-rds",
				Namespace:  "namespace",
				Spec:       generateSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.CreateDBInstance(ctx, tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)

			psql, err := k8s.GetResource[ackrdsv1alpha1.DBInstance](ctx, c, "test-rds", "namespace")
			require.NoError(t, err)
			assert.EqualValues(t, "test-rds", psql.Name)
			assert.EqualValues(t, "namespace", psql.Namespace)

			expectSpec, err := conversion.FromDBInstanceSpecProto(tt.option.Spec)
			require.NoError(t, err)
			expectedJSON := getPrettyJSON(t, expectSpec)
			actualJSON := getPrettyJSON(t, psql.Spec)
			assert.JSONEq(t, expectedJSON, actualJSON)

			err = p.CreateDBInstance(ctx, tt.option)
			assert.True(t, utils.IsErrAlreadyExists(err))
		})
	}
}

func TestProvider_DeleteDBInstance(t *testing.T) {
	ctx := context.Background()
	dbinstance := genDbinstance(func(dbinstance *ackrdsv1alpha1.DBInstance) {
		dbinstance.Status.DBInstanceStatus = utils.Ptr(db_instance.StatusAvailable)
	})
	c := fake.NewClient(dbinstance)
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	err := p.DeleteDBInstance(ctx, "test-rds", "namespace")
	require.NoError(t, err)
	err = p.DeleteDBInstance(ctx, "test-rds", "namespace")
	assert.True(t, utils.IsErrNotFound(err))

	_, err = k8s.GetResource[ackrdsv1alpha1.DBInstance](ctx, c, "test-rds", "namespace")
	require.True(t, k8sErrors.IsNotFound(err))
}

func TestProvider_StartDBInstance(t *testing.T) {
	tests := []struct {
		name          string
		option        CreateDBInstanceOption
		initObjs      []k8sclient.Object
		mockRDS       func(*MockRDSClientInterface)
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "not found",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "already exists (starting)",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStarting),
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "already exists (available)",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "scheduled",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopped),
				}, nil)
				c.EXPECT().StartDBInstance(gomock.Any(), "test-rds").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			rdsClient := NewMockRDSClientInterface(ctrl)
			p := &Provider{
				kc:        &k8s.KubernetesClient{Client: c},
				rdsClient: rdsClient,
			}

			if tt.mockRDS != nil {
				tt.mockRDS(rdsClient)
			}
			err := p.StartDBInstance(ctx, "test-rds", "namespace")
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_StopDBInstance(t *testing.T) {
	tests := []struct {
		name          string
		option        CreateDBInstanceOption
		initObjs      []k8sclient.Object
		mockRDS       func(*MockRDSClientInterface)
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "not found",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name:          "already exists (stopping)",
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopping),
				}, nil)
			},
		},
		{
			name: "already exists (stopped)",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopped),
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "scheduled",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
				}, nil)
				c.EXPECT().StopDBInstance(gomock.Any(), "test-rds").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			rdsClient := NewMockRDSClientInterface(ctrl)
			p := &Provider{
				kc:        &k8s.KubernetesClient{Client: c},
				rdsClient: rdsClient,
			}

			if tt.mockRDS != nil {
				tt.mockRDS(rdsClient)
			}
			err := p.StopDBInstance(ctx, "test-rds", "namespace")
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_GetDBInstance(t *testing.T) {
	tests := []struct {
		name     string
		initObjs []k8sclient.Object
		mockRDS  func(*MockRDSClientInterface)
		want     *DBInstanceMeta
		wantErr  bool
	}{
		{
			name: "not found",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_FOUND},
			},
		},
		{
			name: "not ready (just created)",
			initObjs: []k8sclient.Object{
				genDbinstance(func(dbinstace *ackrdsv1alpha1.DBInstance) {
					dbinstace.CreationTimestamp = metav1.Time{Time: time.Now()}
				}),
			},
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
			},
		},
		{
			name: "not synced for a while",
			initObjs: []k8sclient.Object{
				genDbinstance(func(dbinstace *ackrdsv1alpha1.DBInstance) {
					dbinstace.CreationTimestamp = metav1.Time{Time: time.Now().Add(-5 * time.Minute)}
				}),
			},
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_UNKNOWN},
			},
		},
		{
			name: "available",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
					Endpoint: &rdsTypes.Endpoint{
						Address: utils.Ptr("test-rds.something.ap-southeast-1.rds.amazonaws.com"),
					},
				}, nil)
			},
			want: &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_READY},
				Endpoint: &pbaws.DBInstanceEndpoint{
					Address: "test-rds.something.ap-southeast-1.rds.amazonaws.com",
				},
				InstanceStatus: pbaws.DBInstanceStatus_AVAILABLE,
			},
		},
		{
			name: "creating",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusCreating),
				}, nil)
			},
			want: &DBInstanceMeta{
				Status:         &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				InstanceStatus: pbaws.DBInstanceStatus_NOT_READY,
			},
		},
		{
			name: "stopped",
			mockRDS: func(c *MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopped),
					Endpoint: &rdsTypes.Endpoint{
						Address: utils.Ptr("test-rds.something.ap-southeast-1.rds.amazonaws.com"),
					},
				}, nil)
			},
			want: &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				Endpoint: &pbaws.DBInstanceEndpoint{
					Address: "test-rds.something.ap-southeast-1.rds.amazonaws.com",
				},
				InstanceStatus: pbaws.DBInstanceStatus_STOPPED,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			rdsClient := NewMockRDSClientInterface(ctrl)
			p := &Provider{
				kc:        &k8s.KubernetesClient{Client: c},
				rdsClient: rdsClient,
			}

			if tt.mockRDS != nil {
				tt.mockRDS(rdsClient)
			}
			m, err := p.GetDBInstance(ctx, "test-rds", "namespace")
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, m)
		})
	}
}

func getPrettyJSON(t *testing.T, x any) string {
	j, err := json.MarshalIndent(x, "", "  ")
	assert.NoError(t, err)
	return string(j)
}
