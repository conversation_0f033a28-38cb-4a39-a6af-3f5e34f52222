package aws

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	ack_v1alpha1 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestSecurityGroup_normal(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
	defer cancel()
	var (
		resourceID = "resource1"
		vpcID      = "vpc1"
	)

	// create sg the first time
	option := CreateSecurityGroupOption{
		Region:     "fake-region",
		Namespace:  "fake-ns",
		ResourceID: resourceID,
		VpcID:      vpcID,
	}
	err := provider.CreateSecurityGroup(ctx, option)
	require.NoError(t, err)

	// create sg the second time
	err = provider.CreateSecurityGroup(ctx, option)
	assert.True(t, utils.IsErrAlreadyExists(err))

	// Get sg
	_, err = provider.GetSecurityGroup(ctx, option.Namespace, option.ResourceID)
	assert.NoError(t, err)

	// delete sg the first time
	err = provider.DeleteSecurityGroup(ctx, option.Namespace, option.ResourceID)
	require.NoError(t, err)

	// delete sg the second time
	err = provider.DeleteSecurityGroup(ctx, option.Namespace, option.ResourceID)
	assert.True(t, utils.IsErrNotFound(err))

	// Get sg
	m, err := provider.GetSecurityGroup(ctx, option.Namespace, option.ResourceID)
	require.NoError(t, err)
	assert.Equal(t, pbresource.StatusCode_NOT_FOUND, m.Status.GetCode())
}

func TestSecurityGroupPolicy_normal(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	ctx, cancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
	defer cancel()
	var (
		resourceID        = "resource1"
		namespace         = "fake-ns"
		securityGroupIDs  = []string{"sg-1", "sg-2"}
		podLabelsSelector = map[string]string{
			"k1": "v1",
			"k2": "v2",
		}
	)

	// create sgp the first time
	option := CreateSecurityGroupPolicyOption{
		Namespace:         namespace,
		ResourceID:        resourceID,
		SecurityGroupIDs:  securityGroupIDs,
		PodLabelsSelector: podLabelsSelector,
	}
	err := provider.CreateSecurityGroupPolicy(ctx, option)
	require.NoError(t, err)

	// create sgp the second time
	err = provider.CreateSecurityGroupPolicy(ctx, option)
	assert.True(t, utils.IsErrAlreadyExists(err))

	// Get sgp
	sgp, err := provider.GetSecurityGroupPolicy(ctx, option.Namespace, option.ResourceID)
	assert.NoError(t, err)
	assert.Equal(t, securityGroupIDs, sgp.Spec.SecurityGroups.Groups)
	assert.Equal(t, podLabelsSelector, sgp.Spec.PodSelector.MatchLabels)

	// delete sgp the first time
	err = provider.DeleteSecurityGroupPolicy(ctx, option.Namespace, option.ResourceID)
	require.NoError(t, err)

	// delete sgp the second time
	err = provider.DeleteSecurityGroupPolicy(ctx, option.Namespace, option.ResourceID)
	assert.True(t, utils.IsErrNotFound(err))

	// Get sgp
	sgp, err = provider.GetSecurityGroupPolicy(ctx, option.Namespace, option.ResourceID)
	assert.Equal(t, eris.CodeNotFound, eris.GetCode(err))
	assert.Nil(t, sgp)
}

func TestAttachIPPermissions(t *testing.T) {
	var (
		inboundCIDRs    = []string{"***********/16", "***********/16"}
		inboundSgIDs    = []string{"inbound-sg-1", "inbound-sg-2"}
		inboundDesc     = "test inbound"
		inboundProtocol = "TCP"
		inboundFromPort = int32(2)
		inboundToPort   = int32(3)

		outboundCIDRs    = []string{"*********/16", "*********/16"}
		outboundSgIDs    = []string{"outbound-sg-1", "outbound-sg-2"}
		outboundDesc     = "test outbound"
		outboundProtocol = "UDP"
		outboundFromPort = int32(5)
		outboundToPort   = int32(6)
	)
	inbound := []*pbsvcaws.IPPermission{
		{
			Protocol:               inboundProtocol,
			FromPort:               inboundFromPort,
			ToPort:                 inboundToPort,
			Description:            inboundDesc,
			Cidrs:                  inboundCIDRs,
			SourceSecurityGroupIds: inboundSgIDs,
		},
	}

	outbound := []*pbsvcaws.IPPermission{
		{
			Protocol:               outboundProtocol,
			FromPort:               outboundFromPort,
			ToPort:                 outboundToPort,
			Description:            outboundDesc,
			Cidrs:                  outboundCIDRs,
			SourceSecurityGroupIds: outboundSgIDs,
		},
	}

	sg := &ack_v1alpha1.SecurityGroup{Spec: ack_v1alpha1.SecurityGroupSpec{}}

	err := attachIPPermissions(inbound, outbound, sg)
	require.NoError(t, err)

	// validate inbound rules
	require.Equal(t, 1, len(sg.Spec.IngressRules))
	assert.Equal(t, sg.Spec.IngressRules, []*ack_v1alpha1.IPPermission{
		{
			IPProtocol: &inboundProtocol,
			FromPort:   utils.Ptr(int64(inboundFromPort)),
			ToPort:     utils.Ptr(int64(inboundToPort)),
			IPRanges: []*ack_v1alpha1.IPRange{
				{
					CIDRIP:      &inboundCIDRs[0],
					Description: &inboundDesc,
				},
				{
					CIDRIP:      &inboundCIDRs[1],
					Description: &inboundDesc,
				},
			},
			UserIDGroupPairs: []*ack_v1alpha1.UserIDGroupPair{
				{
					GroupID:     &inboundSgIDs[0],
					Description: &inboundDesc,
				},
				{
					GroupID:     &inboundSgIDs[1],
					Description: &inboundDesc,
				},
			},
		},
	})
	assert.Equal(t, sg.Spec.EgressRules, []*ack_v1alpha1.IPPermission{
		{
			IPProtocol: &outboundProtocol,
			FromPort:   utils.Ptr(int64(outboundFromPort)),
			ToPort:     utils.Ptr(int64(outboundToPort)),
			IPRanges: []*ack_v1alpha1.IPRange{
				{
					CIDRIP:      &outboundCIDRs[0],
					Description: &outboundDesc,
				},
				{
					CIDRIP:      &outboundCIDRs[1],
					Description: &outboundDesc,
				},
			},
			UserIDGroupPairs: []*ack_v1alpha1.UserIDGroupPair{
				{
					GroupID:     &outboundSgIDs[0],
					Description: &outboundDesc,
				},
				{
					GroupID:     &outboundSgIDs[1],
					Description: &outboundDesc,
				},
			},
		},
	})
}
