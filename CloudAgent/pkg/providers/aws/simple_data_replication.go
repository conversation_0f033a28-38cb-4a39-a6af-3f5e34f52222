package aws

import (
	"context"
	"strings"
	"time"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	"github.com/risingwavelabs/eris"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskaws "github.com/risingwavelabs/cloudagent/pbgen/task/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	simpleReplicationCopyRetries            = 8
	simpleReplicationCopyRetryStartInterval = 500 * time.Millisecond
)

func (provider *Provider) ListAllObjects(ctx context.Context, bucket, directory string) ([]string, error) {
	var marker *string
	var rtn []string
	for {
		items, nextMarker, err := provider.s3client.ListObjectsWithMarker(ctx, S3ListRequest{
			Bucket:    bucket,
			Directory: directory,
			Marker:    marker,
		})
		if err != nil {
			return nil, eris.Wrap(err, "failed to list s3 objects")
		}
		rtn = append(rtn, items...)
		if nextMarker != "" {
			marker = &nextMarker
		} else {
			break
		}
	}
	return rtn, nil
}

func (provider *Provider) RunSimpleDataReplication(ctx context.Context, taskID, taskNamespace, sourceBucket, sourceDir, sinkBucket, sinkDir string) error {
	if err := provider.kc.StartTaskRunner(ctx, taskID, taskNamespace, &pbtask.Task{
		Task: &pbtask.Task_AwsSimpleDataReplicationTask{
			AwsSimpleDataReplicationTask: &pbtaskaws.AWSSimpleDataReplicationTask{
				SourceBucket:    sourceBucket,
				SourceDirectory: sourceDir,
				SinkBucket:      sinkBucket,
				SinkDirectory:   sinkDir,
			},
		},
	}); err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to run aws simple data replication task")
	}
	return nil
}

func (provider *Provider) SimpleDataReplication(ctx context.Context, sourceBucket, sourceDir, sinkBucket, sinkDir string) error {
	if !strings.HasSuffix(sourceDir, "/") {
		sourceDir += "/"
	}
	if !strings.HasSuffix(sinkDir, "/") {
		sinkDir += "/"
	}

	objectKeys, err := provider.ListAllObjects(ctx, sourceBucket, sourceDir)
	if err != nil {
		return err
	}

	logger.FromCtx(ctx).Infof("replicating data from %s/%s to %s/%s", sourceBucket, sourceDir, sinkBucket, sinkDir)

	for _, key := range objectKeys {
		if err := utils.Retry(
			ctx,
			func(fctx context.Context) error {
				req := S3CopyRequest{
					SourceBucket: sourceBucket,
					SourceKey:    key,
					SinkBucket:   sinkBucket,
					SinkKey:      strings.Replace(key, sourceDir, sinkDir, 1),
				}
				if err := provider.s3client.CopyObject(fctx, req); err != nil {
					logger.FromCtx(ctx).Warn(err.Error())
					return err
				}
				return nil
			},
			simpleReplicationCopyRetries,
			simpleReplicationCopyRetryStartInterval,
			true, /* exponential  */
		); err != nil {
			return eris.Wrap(err, "failed to copy object")
		}
	}
	return nil
}
