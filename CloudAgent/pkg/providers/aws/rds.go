package aws

import (
	"context"
	"time"

	ackrdsv1alpha1 "github.com/aws-controllers-k8s/rds-controller/apis/v1alpha1"
	"github.com/aws-controllers-k8s/rds-controller/pkg/resource/db_instance"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/eris"

	pbaws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type CreateDBInstanceOption struct {
	ResourceID string
	Namespace  string
	Spec       *pbaws.DBInstanceSpec
}

func (provider *Provider) CreateDBInstance(ctx context.Context, option CreateDBInstanceOption) error {
	spec, err := conversion.FromDBInstanceSpecProto(option.Spec)
	if err != nil {
		return eris.Wrapf(err, "failed to convert dbinstance spec proto: %v, error: %s", option.Spec, err)
	}
	err = provider.kc.Create(ctx, &ackrdsv1alpha1.DBInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: *spec,
	})
	if err != nil && k8sErrors.IsAlreadyExists(err) {
		return eris.Errorf("dbinstance already exists: %v", option).WithCode(eris.CodeAlreadyExists)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to create dbinstance: %v", option)
	}
	return nil
}

func (provider *Provider) DeleteDBInstance(ctx context.Context, resourceID string, namespace string) error {
	err := k8s.DeleteResource[ackrdsv1alpha1.DBInstance](ctx, provider.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("dbinstance %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to delete dbinstance %s", resourceID)
	}
	return nil
}

func (provider *Provider) StartDBInstance(ctx context.Context, resourceID string, _ string) error {
	dbinstance, err := provider.rdsClient.DescribeDBInstance(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		return eris.Errorf("dbinstance %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return err
	}
	status := utils.Unwrap(dbinstance.DBInstanceStatus)
	if status == db_instance.StatusStarting || status == db_instance.StatusAvailable {
		return eris.Errorf("dbinstance %s is already in starting", resourceID).WithCode(eris.CodeAlreadyExists)
	}
	return provider.rdsClient.StartDBInstance(ctx, resourceID)
}

func (provider *Provider) StopDBInstance(ctx context.Context, resourceID string, _ string) error {
	dbinstance, err := provider.rdsClient.DescribeDBInstance(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		return eris.Errorf("dbinstance %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return err
	}
	status := utils.Unwrap(dbinstance.DBInstanceStatus)
	if status == db_instance.StatusStopping || status == db_instance.StatusStopped {
		return eris.Errorf("dbinstance %s is already in stopping", resourceID).WithCode(eris.CodeAlreadyExists)
	}
	return provider.rdsClient.StopDBInstance(ctx, resourceID)
}

type DBInstanceMeta struct {
	Status         *pbresource.Status
	Endpoint       *pbaws.DBInstanceEndpoint
	InstanceStatus pbaws.DBInstanceStatus
}

func (provider *Provider) GetDBInstance(ctx context.Context, resourceID string, namespace string) (*DBInstanceMeta, error) {
	dbinstance, err := provider.rdsClient.DescribeDBInstance(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		crd, err := k8s.GetResource[ackrdsv1alpha1.DBInstance](ctx, provider.kc, resourceID, namespace)
		if err != nil && k8sErrors.IsNotFound(err) {
			return &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_FOUND},
			}, nil
		}
		if err != nil {
			return nil, err
		}
		// just created, may not sync to aws.
		const SyncTimeout = -1 * time.Minute
		justCreated := crd.GetCreationTimestamp().After(time.Now().Add(SyncTimeout))
		if justCreated {
			return &DBInstanceMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
			}, nil
		}

		return &DBInstanceMeta{
			Status: &pbresource.Status{Code: pbresource.StatusCode_UNKNOWN},
		}, nil
	}
	if err != nil {
		return nil, err
	}
	meta := &DBInstanceMeta{}
	switch utils.Unwrap(dbinstance.DBInstanceStatus) {
	case db_instance.StatusAvailable:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_READY}
		meta.InstanceStatus = pbaws.DBInstanceStatus_AVAILABLE
	case db_instance.StatusStopped:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
		meta.InstanceStatus = pbaws.DBInstanceStatus_STOPPED
	default:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
		meta.InstanceStatus = pbaws.DBInstanceStatus_NOT_READY
	}
	if dbinstance.Endpoint != nil && dbinstance.Endpoint.Address != nil {
		meta.Endpoint = &pbaws.DBInstanceEndpoint{
			Address: utils.Unwrap(dbinstance.Endpoint.Address),
		}
	}
	return meta, nil
}
