package aws

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"strings"

	"github.com/aws/aws-sdk-go-v2/aws"
	"github.com/aws/aws-sdk-go-v2/config"
	"github.com/aws/aws-sdk-go-v2/credentials"
	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/aws-sdk-go-v2/service/rds"
	rdsTypes "github.com/aws/aws-sdk-go-v2/service/rds/types"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	s3Types "github.com/aws/aws-sdk-go-v2/service/s3/types"
	"github.com/aws/smithy-go"
	"github.com/pkg/errors"

	"github.com/risingwavelabs/eris"

	pbcfgaws "github.com/risingwavelabs/cloudagent/pbgen/config/aws"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type S3ClientInterface interface {
	DeleteObjects(ctx context.Context, bucket string, objectKeys []string) error
	ListObjects(ctx context.Context, bucket, directory string) ([]string, error)
	ListObjectsWithMarker(ctx context.Context, req S3ListRequest) ([]string, string, error)
	CopyObject(ctx context.Context, req S3CopyRequest) error
	GetObject(ctx context.Context, bucket, key string) ([]byte, error)
	GetObjectEtag(ctx context.Context, bucket, key string) (string, error)
}

type EC2ClientInterface interface {
	FilterVPCEndpointsIDByTag(ctx context.Context, tagKey string, tagValues []string) ([]string, error)
	DeleteVPCEndpointsByID(ctx context.Context, ids []string) error
	DescribeVPCEndpoint(ctx context.Context, id string) (*types.VpcEndpoint, error)
	DescribeVPCEndpointService(ctx context.Context, serviceName string) (*types.ServiceDetail, error)
	DescribeSubnets(ctx context.Context, subnets []string) (*[]types.Subnet, error)
}

type RDSClientInterface interface {
	StartDBInstance(ctx context.Context, id string) error
	StopDBInstance(ctx context.Context, id string) error
	DescribeDBInstance(ctx context.Context, id string) (*rdsTypes.DBInstance, error)
}

type EC2ClientImpl struct {
	ec2Svc *ec2.Client
}

func newEC2Client(ctx context.Context, awsCfg *pbcfgaws.Config) (*EC2ClientImpl, error) {
	switch authType := awsCfg.GetAuth().(type) {
	case *pbcfgaws.Config_StaticCreds:
		ec2Svc := ec2.New(ec2.Options{
			Credentials: credentials.NewStaticCredentialsProvider(awsCfg.GetStaticCreds().GetAccessKeyId(), awsCfg.GetStaticCreds().GetSecretAccessKey(), ""),
			Region:      awsCfg.GetRegion(),
		})
		return &EC2ClientImpl{ec2Svc: ec2Svc}, nil
	case *pbcfgaws.Config_EksWebIdentity:
		sdkcfg, err := config.LoadDefaultConfig(ctx, func(lo *config.LoadOptions) error {
			lo.Region = awsCfg.GetRegion()
			return nil
		})
		if err != nil {
			return nil, eris.Wrap(err, "unable to load AWS SDK config")
		}
		sdkcfg.Region = awsCfg.GetRegion()
		return &EC2ClientImpl{ec2Svc: ec2.NewFromConfig(sdkcfg)}, nil
	default:
		return nil, fmt.Errorf("invalid AWS auth type: %v", authType)
	}
}

func (c *EC2ClientImpl) FilterVPCEndpointsIDByTag(ctx context.Context, tagKey string, tagValues []string) ([]string, error) {
	out, err := c.ec2Svc.DescribeVpcEndpoints(ctx, &ec2.DescribeVpcEndpointsInput{
		Filters: []types.Filter{
			{
				Name:   aws.String(fmt.Sprintf("tag:%s", tagKey)),
				Values: tagValues,
			},
		},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "failed to descrive VPC Endpoints by tag %s: %v", tagKey, tagValues)
	}
	ids := []string{}
	for _, e := range out.VpcEndpoints {
		if e.VpcEndpointId == nil {
			var endpointInfo string
			endpointJSON, err := json.Marshal(e)
			if err != nil {
				endpointInfo = fmt.Sprintf("cannot get endpoint info: %s", err.Error())
			} else {
				endpointInfo = string(endpointJSON)
			}
			return nil, errors.Errorf("got VPC endpoint without ID, tag %s:%v, endpoint info: %s", tagKey, tagValues, endpointInfo)
		}
		ids = append(ids, *e.VpcEndpointId)
	}
	return ids, nil
}

func (c *EC2ClientImpl) DeleteVPCEndpointsByID(ctx context.Context, ids []string) error {
	if len(ids) == 0 {
		return nil
	}
	res, err := c.ec2Svc.DeleteVpcEndpoints(ctx, &ec2.DeleteVpcEndpointsInput{
		VpcEndpointIds: ids,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to delete VPC endpoints %v", ids)
	}
	if len(res.Unsuccessful) != 0 {
		return eris.Wrapf(unsuccessfulToError(res.Unsuccessful), "failed to delete VPC endpoints: %v", ids)
	}
	logger.FromCtx(ctx).Infof("VPC endpoints deleted: %v", ids)
	return nil
}

func (c *EC2ClientImpl) DescribeVPCEndpoint(ctx context.Context, endpointID string) (*types.VpcEndpoint, error) {
	res, err := c.ec2Svc.DescribeVpcEndpoints(ctx, &ec2.DescribeVpcEndpointsInput{
		VpcEndpointIds: []string{endpointID},
	})
	if err != nil {
		return nil, eris.Wrapf(err, "failed to describe VPC endpoint %v", endpointID)
	}
	if len(res.VpcEndpoints) == 0 {
		return nil, eris.New("vpc endpoint not found").WithCode(eris.CodeNotFound)
	}
	return &res.VpcEndpoints[0], nil
}

func (c *EC2ClientImpl) DescribeVPCEndpointService(ctx context.Context, serviceName string) (*types.ServiceDetail, error) {
	res, err := c.ec2Svc.DescribeVpcEndpointServices(ctx, &ec2.DescribeVpcEndpointServicesInput{
		ServiceNames: []string{serviceName},
	})
	return extractVPCEndpointServiceDetail(res, err, serviceName)
}

func extractVPCEndpointServiceDetail(res *ec2.DescribeVpcEndpointServicesOutput, err error, serviceName string) (*types.ServiceDetail, error) {
	if err != nil {
		var apiErr smithy.APIError
		if errors.As(err, &apiErr) {
			if apiErr.ErrorCode() == "InvalidServiceName" {
				return nil, eris.New("vpc endpoint service not found").WithCode(eris.CodeNotFound)
			}
		}
		return nil, eris.Wrapf(err, "failed to describe VPC endpoint service %v", serviceName)
	}

	if len(res.ServiceDetails) == 0 {
		return nil, eris.New("vpc endpoint service not found").WithCode(eris.CodeNotFound)
	}
	return &res.ServiceDetails[0], nil
}

func (c *EC2ClientImpl) DescribeSubnets(ctx context.Context, subnets []string) (*[]types.Subnet, error) {
	res, err := c.ec2Svc.DescribeSubnets(ctx, &ec2.DescribeSubnetsInput{
		SubnetIds: subnets,
	})
	if err != nil {
		return nil, eris.Wrapf(err, "failed to describe subnets %v", subnets)
	}
	if len(res.Subnets) != len(subnets) {
		return nil, eris.New("some or all subnets not found").WithCode(eris.CodeNotFound)
	}
	return &res.Subnets, nil
}

type S3ClientImpl struct {
	s3svc *s3.Client
}

func newS3Client(ctx context.Context, awsCfg *pbcfgaws.Config) (*S3ClientImpl, error) {
	switch authType := awsCfg.GetAuth().(type) {
	case *pbcfgaws.Config_StaticCreds:
		s3svc := s3.New(s3.Options{
			Credentials: credentials.NewStaticCredentialsProvider(awsCfg.GetStaticCreds().GetAccessKeyId(), awsCfg.GetStaticCreds().GetSecretAccessKey(), ""),
			Region:      awsCfg.GetRegion(),
		})
		return &S3ClientImpl{s3svc: s3svc}, nil
	case *pbcfgaws.Config_EksWebIdentity:
		sdkcfg, err := config.LoadDefaultConfig(ctx, func(lo *config.LoadOptions) error {
			lo.Region = awsCfg.GetRegion()
			return nil
		})
		if err != nil {
			return nil, eris.Wrap(err, "unable to load AWS SDK config")
		}
		sdkcfg.Region = awsCfg.GetRegion()
		return &S3ClientImpl{s3svc: s3.NewFromConfig(sdkcfg)}, nil
	default:
		return nil, fmt.Errorf("invalid AWS auth type: %v", authType)
	}
}

type S3ListRequest struct {
	Bucket    string
	Directory string
	Marker    *string
	MaxSize   int32
}

func (s *S3ClientImpl) ListObjects(ctx context.Context, bucket, directory string) ([]string, error) {
	rtn, _, err := s.ListObjectsWithMarker(ctx, S3ListRequest{
		Bucket:    bucket,
		Directory: directory,
	})
	return rtn, err
}

// Uses a marker or cursor as the starting point for listing the objects.
func (s *S3ClientImpl) ListObjectsWithMarker(ctx context.Context, req S3ListRequest) ([]string, string, error) {
	var (
		maxKeys    = 1000 // Aws default max keys
		res        []string
		nextMarker string
	)
	if req.MaxSize != 0 {
		maxKeys = int(req.MaxSize)
	}

	listArgs := s3.ListObjectsInput{
		Bucket:  &req.Bucket,
		Prefix:  &req.Directory,
		Marker:  req.Marker,
		MaxKeys: utils.Ptr(int32(maxKeys + 1)), // Request one extra to detect if there's a next key
	}
	output, err := s.s3svc.ListObjects(ctx, &listArgs)
	if err != nil {
		return nil, "", eris.Wrap(err, "failed to list s3 objects")
	}

	retCount := len(output.Contents)
	if retCount > maxKeys {
		retCount = maxKeys
		nextMarker = *output.Contents[retCount].Key // Last element in the result
	}

	for i := 0; i < retCount; i++ {
		obj := output.Contents[i]
		res = append(res, *obj.Key)
	}

	return res, nextMarker, nil
}

func (s *S3ClientImpl) DeleteObjects(ctx context.Context, bucket string, objectKeys []string) error {
	var objectIdentifiers = make([]s3Types.ObjectIdentifier, len(objectKeys))
	for i, key := range objectKeys {
		var k = key
		objectIdentifiers[i] = s3Types.ObjectIdentifier{
			Key: &k,
		}
	}
	deleteResp, err := s.s3svc.DeleteObjects(ctx, &s3.DeleteObjectsInput{
		Bucket: &bucket,
		Delete: &s3Types.Delete{
			Objects: objectIdentifiers,
		},
	})
	if err != nil {
		return eris.Wrap(err, "failed to delete s3 objects")
	}
	sb := strings.Builder{}
	if len(deleteResp.Errors) != 0 {
		sb.WriteString("failed to delete s3 objects: ")
		for _, e := range deleteResp.Errors {
			sb.WriteString(fmt.Sprintf("object: %s, code: %s, message: %s ",
				utils.Unwrap(e.Key),
				utils.Unwrap(e.Code),
				utils.Unwrap(e.Message)))
		}
		return eris.New(sb.String())
	}
	return nil
}

type S3CopyRequest struct {
	SourceBucket string
	SourceKey    string
	SinkBucket   string
	SinkKey      string
}

func (s *S3ClientImpl) CopyObject(ctx context.Context, req S3CopyRequest) error {
	getRes, err := s.s3svc.GetObject(ctx, &s3.GetObjectInput{
		Bucket: &req.SourceBucket,
		Key:    &req.SourceKey,
	})
	if err != nil {
		return eris.Wrapf(err, "failed to get object %s/%s", req.SinkBucket, req.SinkKey)
	}
	// Read the body into a byte array first.
	// Cannot just place the getRes.Body to the PutObjectInput.Body
	// This is needed to avoid the NotImplemeted error
	// The error looks like:
	// > [aws-service] code(internal) failed to put objectt cloudagent-e2e-test-mike replication-test-sinkdir/sink/data/source-3.data:
	// > operation error S3: PutObject, https response error StatusCode: 501, RequestID: YHV1Q952PYVB6JXJ, HostID:
	// > PMAVgSZrTjL82qtWdkJ9G13qzadvTCcLuhApItGZ2kOQ4BhZCV4VUCASg8nD63qzlGqDDAtH+vxH+4cx0DQ8JQ==, api error NotImplemented:
	// > A header you provided implies functionality that is not implemented
	raw, err := io.ReadAll(getRes.Body)
	if err != nil {
		return err
	}
	_, err = s.s3svc.PutObject(ctx, &s3.PutObjectInput{
		Bucket: &req.SinkBucket,
		Key:    &req.SinkKey,
		Body:   bytes.NewReader(raw),
	})
	if err != nil {
		return eris.Wrapf(err, "failed to put object %s/%s", req.SinkBucket, req.SinkKey)
	}
	return nil
}

func (s *S3ClientImpl) GetObject(ctx context.Context, bucket, key string) ([]byte, error) {
	res, err := s.s3svc.GetObject(ctx, &s3.GetObjectInput{
		Bucket: &bucket,
		Key:    &key,
	})
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get object %s/%s", bucket, key)
	}
	raw, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to read object %s/%s", bucket, key)
	}
	return raw, nil
}

func (s *S3ClientImpl) GetObjectEtag(ctx context.Context, bucket, key string) (string, error) {
	res, err := s.s3svc.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: &bucket,
		Key:    &key,
	})
	if err != nil {
		return "", eris.Wrapf(err, "failed to head object %s/%s", bucket, key)
	}
	if res.ETag == nil {
		return "", eris.Errorf("etag not found for object %s/%s", bucket, key)
	}
	return *res.ETag, nil
}

type RDSClientImpl struct {
	rdssvc *rds.Client
}

func newRDSClient(ctx context.Context, awsCfg *pbcfgaws.Config) (*RDSClientImpl, error) {
	switch authType := awsCfg.GetAuth().(type) {
	case *pbcfgaws.Config_StaticCreds:
		rdssvc := rds.New(rds.Options{
			Credentials: credentials.NewStaticCredentialsProvider(awsCfg.GetStaticCreds().GetAccessKeyId(), awsCfg.GetStaticCreds().GetSecretAccessKey(), ""),
			Region:      awsCfg.GetRegion(),
		})
		return &RDSClientImpl{rdssvc: rdssvc}, nil
	case *pbcfgaws.Config_EksWebIdentity:
		sdkcfg, err := config.LoadDefaultConfig(ctx, func(lo *config.LoadOptions) error {
			lo.Region = awsCfg.GetRegion()
			return nil
		})
		if err != nil {
			return nil, eris.Wrap(err, "unable to load AWS SDK config")
		}
		sdkcfg.Region = awsCfg.GetRegion()
		return &RDSClientImpl{rdssvc: rds.NewFromConfig(sdkcfg)}, nil
	default:
		return nil, fmt.Errorf("invalid AWS auth type: %v", authType)
	}
}

func (c *RDSClientImpl) StartDBInstance(ctx context.Context, id string) error {
	_, err := c.rdssvc.StartDBInstance(ctx, &rds.StartDBInstanceInput{
		DBInstanceIdentifier: &id,
	})
	if err != nil {
		return eris.Wrap(err, "failed to start rds db instance")
	}
	return nil
}

func (c *RDSClientImpl) StopDBInstance(ctx context.Context, id string) error {
	_, err := c.rdssvc.StopDBInstance(ctx, &rds.StopDBInstanceInput{
		DBInstanceIdentifier: &id,
	})
	if err != nil {
		return eris.Wrap(err, "failed to stop rds db instance")
	}
	return nil
}

func (c *RDSClientImpl) DescribeDBInstance(ctx context.Context, id string) (*rdsTypes.DBInstance, error) {
	res, err := c.rdssvc.DescribeDBInstances(ctx, &rds.DescribeDBInstancesInput{
		DBInstanceIdentifier: &id,
	})
	if err != nil && strings.Contains(err.Error(), "DBInstanceNotFound") {
		return nil, eris.WithCode(eris.Wrap(err, "db instance not found"), eris.CodeNotFound)
	}
	if err != nil {
		return nil, eris.Wrapf(err, "failed to describe db instance %v", id)
	}
	if len(res.DBInstances) == 0 {
		return nil, eris.New("db instance not found").WithCode(eris.CodeNotFound)
	}
	return &res.DBInstances[0], nil
}

func unsuccessfulToError(unsuccessful []types.UnsuccessfulItem) error {
	if len(unsuccessful) == 0 {
		return nil
	}
	var sb strings.Builder
	for _, item := range unsuccessful {
		sb.WriteString(fmt.Sprintf("resource: %s, code: %s, error: %s ",
			utils.Unwrap(item.ResourceId),
			utils.Unwrap(utils.Unwrap(item.Error).Code),
			utils.Unwrap(utils.Unwrap(item.Error).Message),
		))
	}
	return eris.New(sb.String())
}
