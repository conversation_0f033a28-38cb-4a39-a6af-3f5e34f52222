package aws

import (
	"context"
	"fmt"
	"math"
	"strings"
	"time"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskaws "github.com/risingwavelabs/cloudagent/pbgen/task/aws"
	"github.com/risingwavelabs/cloudagent/pkg/logger"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"
	"github.com/risingwavelabs/cloudagent/pkg/utils"

	"github.com/risingwavelabs/eris"
)

const (
	samplingRate     = 5
	deletionRetry    = 3
	cloneParallelism = 10
	cloneTaskRetry   = 3
)

func (provider *Provider) RunDeleteDataDirectoryTask(ctx context.Context, taskID, taskNamespace, bucket, directory string) error {
	if err := provider.kc.StartTaskRunner(ctx, taskID, taskNamespace, &pbtask.Task{
		Task: &pbtask.Task_AwsDirectoryCleanUpTask{
			AwsDirectoryCleanUpTask: &pbtaskaws.AWSDirectoryCleanUpTask{
				Bucket:    bucket,
				Directory: directory,
			},
		},
	}); err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to run aws delete data directory task")
	}
	return nil
}

func (provider *Provider) DeleteDataDirectory(ctx context.Context, bucket, directory string) error {
	var err error
	for i := 0; i < deletionRetry; i++ {
		err = provider.deleteDataDirectoryInner(ctx, bucket, directory)
		if err == nil {
			return nil
		}
	}
	return err
}

func (provider *Provider) deleteDataDirectoryInner(ctx context.Context, bucket, directory string) error {
	if !strings.HasSuffix(directory, "/") {
		directory += "/"
	}
	objectKeys, err := provider.s3client.ListObjects(ctx, bucket, directory)
	if err != nil {
		return err
	}
	crashTimes := 0
	for len(objectKeys) != 0 {
		err := provider.s3client.DeleteObjects(ctx, bucket, objectKeys)

		if err != nil {
			logger.FromCtx(ctx).Warn(err.Error())
			// error might be caused by the throttled API
			crashTimes++
			if crashTimes > 10 {
				return eris.New(fmt.Sprintf("failed to delete s3 objects, abort after trying %d times", crashTimes))
			}
			if crashTimes > 6 {
				time.Sleep(30 * time.Second)
			} else {
				time.Sleep(time.Duration(int32(math.Pow(2, float64(crashTimes)))*500) * time.Millisecond)
			}
		}

		objectKeys, err = provider.s3client.ListObjects(ctx, bucket, directory)
		if err != nil {
			return err
		}
	}
	return nil
}

func (provider *Provider) RunDataDirectoryCloneTask(ctx context.Context, options clone.Options) error {
	if err := provider.kc.StartTaskRunner(ctx, options.TaskID, options.TaskNamespace, &pbtask.Task{
		Task: &pbtask.Task_AwsDirectoryCloneTask{
			AwsDirectoryCloneTask: &pbtaskaws.AWSDirectoryCloneTask{
				SourceDirectoryName:      options.SourceDirectoryName,
				SourceBucketName:         options.SourceBucketName,
				DestinationDirectoryName: options.DestinationDirectoryName,
				DestinationBucketName:    options.DestinationBucketName,
				Cursor:                   options.Cursor,
				CloneSize:                options.CloneSize,
			},
		},
	}); err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to run aws delete data directory task")
	}
	return nil
}

func (provider *Provider) CloneDataDirectory(ctx context.Context, options clone.Options) error {
	var err error
	buildCopyReq := func(srcObj, dstObj string) S3CopyRequest {
		return S3CopyRequest{
			SourceBucket: options.SourceBucketName,
			SourceKey:    srcObj,
			SinkBucket:   options.DestinationBucketName,
			SinkKey:      dstObj,
		}
	}

	buildListReq := func() S3ListRequest {
		return S3ListRequest{
			Bucket:    options.SourceBucketName,
			Directory: options.SourceDirectoryName,
			Marker:    &options.Cursor,
			MaxSize:   options.CloneSize,
		}
	}
	for i := 0; i < cloneTaskRetry; i++ {
		err = clone.DataDirectoryInner[S3CopyRequest, S3ListRequest](ctx, provider.s3client, options, buildCopyReq, buildListReq)
		if err == nil {
			return nil
		}
	}
	return err
}
