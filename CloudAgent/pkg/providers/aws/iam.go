package aws

import (
	"context"
	"fmt"
	"strings"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	ackiamv1alpha1 "github.com/aws-controllers-k8s/iam-controller/apis/v1alpha1"
	ackv1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsaws "github.com/risingwavelabs/cloudagent/pkg/utils/aws"
)

type IAMPolicyMeta struct {
	Status *pbresource.Status
	ARN    string
}

type IAMRoleMeta struct {
	Status *pbresource.Status
	ARN    string
}

type AccessOption interface{}

type S3AccessOption struct {
	Bucket string
	Dirs   []string
}

type K8sServiceAccountOption struct {
	Name      string
	Namespace string
}

type CreateIAMPolicyOption struct {
	ResourceID    string
	Namespace     string
	AccessOptions []AccessOption
}

type CreateIAMRoleOption struct {
	ResourceID   string
	Namespace    string
	SA           K8sServiceAccountOption
	AccountID    string
	OIDCPrivider string
	PolicyRefs   []*pbresource.Meta
}

func (provider *Provider) CreateIAMPoicy(ctx context.Context, option CreateIAMPolicyOption) error {
	// Construct statemtnets based on access options.
	var statements []string
	for _, o := range option.AccessOptions {
		if s3Option, ok := o.(S3AccessOption); ok {
			statements = append(statements, createS3PolicyStatement(s3Option))
		}
	}

	if len(statements) == 0 {
		return eris.New("emty policy is not allowed").WithCode(eris.CodeInvalidArgument)
	}

	document := createPolicyDocument(statements)

	tags, err := utilsaws.AckTagsFromTags[ackiamv1alpha1.Tag](
		provider.DefaultTags(option.ResourceID, option.Namespace),
	)
	if err != nil {
		return eris.Wrapf(err, "failed to generate resource tags")
	}

	// Create policy CRD
	policy := &ackiamv1alpha1.Policy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: ackiamv1alpha1.PolicySpec{
			Name:           &option.ResourceID,
			PolicyDocument: &document,
			// Description must be set: https://github.com/aws-controllers-k8s/community/issues/1490
			// https://github.com/aws-controllers-k8s/community/issues/1610#issuecomment-**********
			Description: utils.Ptr("Grant tenant access to AWS resources"),
			Tags:        tags,
		},
	}
	if err := provider.kc.Create(ctx, policy); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("IAM policy %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create IAM policy %v", option)
	}

	return nil
}

func (provider *Provider) DeleteIAMPolicy(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[ackiamv1alpha1.Policy](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("IAM policy %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete IAM policy %s", resourceID)
	}
	return nil
}

func (provider *Provider) CreateIAMRole(ctx context.Context, option CreateIAMRoleOption) error {
	assumeRolePolicyDocument := createAssumeRolePolicyDocument(
		option.AccountID,
		option.OIDCPrivider,
		option.SA.Namespace,
		option.SA.Name,
	)

	policyRefs, err := policyRefsToResourceRefWrappers(option.Namespace, option.PolicyRefs)
	if err != nil {
		return err
	}

	tags, err := utilsaws.AckTagsFromTags[ackiamv1alpha1.Tag](
		provider.DefaultTags(option.ResourceID, option.Namespace),
	)
	if err != nil {
		return eris.Wrapf(err, "failed to generate resource tags")
	}

	role := &ackiamv1alpha1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: ackiamv1alpha1.RoleSpec{
			// Description must be set: https://github.com/aws-controllers-k8s/community/issues/1490
			// https://github.com/aws-controllers-k8s/community/issues/1610#issuecomment-**********
			Description:              utils.Ptr("tenant iam role"),
			AssumeRolePolicyDocument: &assumeRolePolicyDocument,
			Name:                     &option.ResourceID,
			PolicyRefs:               policyRefs,
			Tags:                     tags,
		},
	}
	if err := provider.kc.Create(ctx, role); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("IAM role %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create IAM role %v", option)
	}

	return nil
}

func (provider *Provider) DeleteIAMRole(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[ackiamv1alpha1.Role](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("IAM role %s is not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete IAM role %s", resourceID)
	}
	return nil
}

func (provider *Provider) GetIAMPolicy(ctx context.Context, namespace, resourceID string) (*IAMPolicyMeta, error) {
	policy, err := k8s.GetResource[ackiamv1alpha1.Policy](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, eris.Wrapf(err, "failed to get IAM policy %s", resourceID)
	}
	status := utilsaws.AckConditionToResourceStatus(policy.Status.Conditions)
	if policy.Status.ACKResourceMetadata == nil || policy.Status.ACKResourceMetadata.ARN == nil {
		if status.GetCode() == pbresource.StatusCode_READY {
			logger.FromCtx(ctx).Errorf("arn is nil in a ready iam policy %v", policy)
			return nil, eris.Wrapf(err, "arn is nil in a ready iam policy %v", policy)
		}

		return &IAMPolicyMeta{
			Status: &pbresource.Status{
				Code:    status.GetCode(),
				Message: fmt.Sprintf("policy ARN is nil: %s", status.GetMessage()),
			},
			ARN: "",
		}, nil
	}

	return &IAMPolicyMeta{
		Status: status,
		ARN:    string(*policy.Status.ACKResourceMetadata.ARN),
	}, nil
}

func (provider *Provider) GetIAMRole(ctx context.Context, namespace, resourceID string) (*IAMRoleMeta, error) {
	role, err := k8s.GetResource[ackiamv1alpha1.Role](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &IAMRoleMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, eris.Wrapf(err, "failed to get IAM role %s", resourceID)
	}
	status := utilsaws.AckConditionToResourceStatus(role.Status.Conditions)
	if role.Status.ACKResourceMetadata == nil || role.Status.ACKResourceMetadata.ARN == nil {
		if status.GetCode() == pbresource.StatusCode_READY {
			logger.FromCtx(ctx).Errorf("arn is nil in a ready iam role %v", role)
			return nil, eris.Wrapf(err, "arn is nil in a ready iam role %v", role)
		}
		return &IAMRoleMeta{
			Status: &pbresource.Status{
				Code:    status.GetCode(),
				Message: fmt.Sprintf("role ARN is nil: %s", status.GetMessage()),
			},
			ARN: "",
		}, nil
	}

	return &IAMRoleMeta{
		Status: status,
		ARN:    string(*role.Status.ACKResourceMetadata.ARN),
	}, nil
}

func createS3PolicyStatement(option S3AccessOption) string {
	const s3StatementString = `{
	"Effect": "Allow",
	"Action": ["s3:*"],
	"Resource": [
		"arn:aws:s3:::%s",
		%s
	]
}`

	var dirResources []string
	for _, dir := range option.Dirs {
		dirResources = append(dirResources, fmt.Sprintf("\"arn:aws:s3:::%s/%s/*\"", option.Bucket, dir))
	}

	return fmt.Sprintf(s3StatementString, option.Bucket, strings.Join(dirResources, ",\n\t\t"))
}

func createPolicyDocument(statements []string) string {
	const policyDocumentString = `{
	"Version": "2012-10-17",
	"Statement": [
		%s
	]
}
`
	return fmt.Sprintf(policyDocumentString, strings.Join(statements, ",\n"))
}

func createAssumeRolePolicyDocument(accountID, oidcPrivider, saNamespace, saName string) string {
	const assumeRolePolicyDocumentString = `{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Effect": "Allow",
			"Principal": {
				"Federated": "arn:aws:iam::%[1]s:oidc-provider/%[2]s"
			},
			"Action": "sts:AssumeRoleWithWebIdentity",
			"Condition": {
				"StringEquals": {
					"%[2]s:aud": "sts.amazonaws.com",
					"%[2]s:sub": "system:serviceaccount:%[3]s:%[4]s"
				}
			}
		}
	]
}
`
	return fmt.Sprintf(assumeRolePolicyDocumentString, accountID, oidcPrivider, saNamespace, saName)
}

func policyRefsToResourceRefWrappers(namespace string, policyRefs []*pbresource.Meta) ([]*ackv1alpha1.AWSResourceReferenceWrapper, error) {
	var policyRefWrappers []*ackv1alpha1.AWSResourceReferenceWrapper

	for _, p := range policyRefs {
		if p.GetNamespace() != namespace {
			return nil, eris.Errorf(
				"currently, referred policy must be in the same namespace as the referring resource, got %s, want %s",
				p.GetNamespace(),
				namespace,
			).WithCode(eris.CodeInvalidArgument)
		}
		policyRefWrappers = append(
			policyRefWrappers,
			&ackv1alpha1.AWSResourceReferenceWrapper{
				From: &ackv1alpha1.AWSResourceReference{
					Name: &p.Id,
				},
			},
		)
	}
	return policyRefWrappers, nil
}
