package aws

import (
	"context"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	ack_v1alpha1 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"
	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsaws "github.com/risingwavelabs/cloudagent/pkg/utils/aws"
)

type CreateVPCEndpointOption struct {
	VpcID             string
	ServiceName       string
	Namespace         string
	ExtraTags         map[string]string
	ResourceID        string
	SubnetIDs         []string
	SecurityGroupIDs  []string
	PrivateDNSEnabled bool
}

type VPCEndpointMeta struct {
	Status           *pbresource.Status
	VPCEndpointID    string
	VPCEndpointDNS   string
	VPCEndpointState aws.VPCEndpointStatus
}

var (
	EndpointTypeInterface = "Interface"
)

func (provider *Provider) CheckVPCEndpointServiceReachability(ctx context.Context, serviceName string) error {
	_, err := provider.ec2Client.DescribeVPCEndpointService(ctx, serviceName)
	return err
}

func (provider *Provider) CreateVPCEndpoint(ctx context.Context, option CreateVPCEndpointOption) error {
	logger.FromCtx(ctx).Infof("creating vpc endpoint %s in %s", option.ResourceID, option.Namespace)

	var sgs []*string
	for i := range option.SecurityGroupIDs {
		sgs = append(sgs, &option.SecurityGroupIDs[i])
	}

	validSubnets, err := provider.getValidEndpointSubnets(ctx, option.ServiceName, option.SubnetIDs)
	if err != nil {
		return eris.Wrapf(err, "could not find valid subnets for the vpc endpoint")
	}

	tags := []*ack_v1alpha1.Tag{
		{
			Key:   utils.Ptr(utilsaws.TagProjectKey),
			Value: utils.Ptr(utilsaws.TagProjectValue),
		},
	}

	for k, v := range option.ExtraTags {
		tags = append(tags, &ack_v1alpha1.Tag{Key: utils.Ptr(k), Value: utils.Ptr(v)})
	}

	if err := provider.kc.Create(ctx, &ack_v1alpha1.VPCEndpoint{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: ack_v1alpha1.VPCEndpointSpec{
			ServiceName:       &option.ServiceName,
			VPCID:             &option.VpcID,
			VPCEndpointType:   &EndpointTypeInterface,
			SecurityGroupIDs:  sgs,
			SubnetIDs:         validSubnets,
			PrivateDNSEnabled: utils.Ptr(option.PrivateDNSEnabled),
			Tags:              tags,
		},
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("vpc endpoint already exists: %v", option).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create vpc endpoint: %v", option)
	}
	logger.FromCtx(ctx).Infof("vpc endpoint %s created", option.ResourceID)
	return nil
}

func (provider *Provider) GetVPCEndpoint(ctx context.Context, namespace, resourceID string) (*VPCEndpointMeta, error) {
	endpoint, err := k8s.GetResource[ack_v1alpha1.VPCEndpoint](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &VPCEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get vpc endpoint %s in %s", resourceID, namespace)
	}

	var endpointID, endpointDNS string
	var endpointState aws.VPCEndpointStatus
	status := utilsaws.AckConditionToResourceStatus(endpoint.Status.Conditions)
	if endpoint.Status.VPCEndpointID != nil {
		endpointID = *endpoint.Status.VPCEndpointID
		updatedEndpoint, err := provider.ec2Client.DescribeVPCEndpoint(ctx, endpointID)

		if err != nil {
			return nil, eris.Wrapf(err, "could not describe vpc endpoint %v", endpointID)
		}
		endpointState = utilsaws.VPCEndpointStateToProto(updatedEndpoint.State)
		if len(updatedEndpoint.DnsEntries) > 0 && updatedEndpoint.DnsEntries[0].DnsName != nil {
			endpointDNS = *updatedEndpoint.DnsEntries[0].DnsName
		}
	}

	return &VPCEndpointMeta{
		Status:           status,
		VPCEndpointID:    endpointID,
		VPCEndpointState: endpointState,
		VPCEndpointDNS:   endpointDNS,
	}, nil
}

func (provider *Provider) DeleteVPCEndpoint(ctx context.Context, namespace, resourceID string) error {
	logger.FromCtx(ctx).Infof("deleting vpc endpoint %s in %s", resourceID, namespace)
	if err := k8s.DeleteResource[ack_v1alpha1.VPCEndpoint](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("vpc endpoint %s in %s is not found", resourceID, namespace).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete vpc endpoint %s in %s", resourceID, namespace)
	}
	logger.FromCtx(ctx).Infof("vpc endpoint %s deleted", resourceID)
	return nil
}

func (provider *Provider) getValidEndpointSubnets(ctx context.Context, serviceName string, subnets []string) ([]*string, error) {
	serviceDetails, err := provider.ec2Client.DescribeVPCEndpointService(ctx, serviceName)
	if err != nil {
		return nil, eris.Wrapf(err, "could not describe endpoint service")
	}

	serviceAZs := make(map[string]bool)
	for _, s := range serviceDetails.AvailabilityZones {
		serviceAZs[s] = true
	}

	subnetDetails, err := provider.ec2Client.DescribeSubnets(ctx, subnets)
	if err != nil {
		return nil, eris.Wrapf(err, "could not describe subnets")
	}

	var validSubnets []*string
	for _, s := range *subnetDetails {
		if serviceAZs[*s.AvailabilityZone] {
			validSubnets = append(validSubnets, s.SubnetId)
		}
	}

	if len(validSubnets) == 0 {
		return nil, eris.New("no compatible subnets were found for the vpc endpoint").WithCode(eris.CodeFailedPrecondition)
	}

	return validSubnets, nil
}
