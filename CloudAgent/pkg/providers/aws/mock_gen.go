// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/aws (interfaces: S3ClientInterface,EC2ClientInterface,RDSClientInterface)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/aws -package=aws -destination=pkg/providers/aws/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/aws S3ClientInterface,EC2ClientInterface,RDSClientInterface
//

// Package aws is a generated GoMock package.
package aws

import (
	context "context"
	reflect "reflect"

	types "github.com/aws/aws-sdk-go-v2/service/ec2/types"
	types0 "github.com/aws/aws-sdk-go-v2/service/rds/types"
	gomock "go.uber.org/mock/gomock"
)

// MockS3ClientInterface is a mock of S3ClientInterface interface.
type MockS3ClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockS3ClientInterfaceMockRecorder
	isgomock struct{}
}

// MockS3ClientInterfaceMockRecorder is the mock recorder for MockS3ClientInterface.
type MockS3ClientInterfaceMockRecorder struct {
	mock *MockS3ClientInterface
}

// NewMockS3ClientInterface creates a new mock instance.
func NewMockS3ClientInterface(ctrl *gomock.Controller) *MockS3ClientInterface {
	mock := &MockS3ClientInterface{ctrl: ctrl}
	mock.recorder = &MockS3ClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockS3ClientInterface) EXPECT() *MockS3ClientInterfaceMockRecorder {
	return m.recorder
}

// CopyObject mocks base method.
func (m *MockS3ClientInterface) CopyObject(ctx context.Context, req S3CopyRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyObject", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CopyObject indicates an expected call of CopyObject.
func (mr *MockS3ClientInterfaceMockRecorder) CopyObject(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyObject", reflect.TypeOf((*MockS3ClientInterface)(nil).CopyObject), ctx, req)
}

// DeleteObjects mocks base method.
func (m *MockS3ClientInterface) DeleteObjects(ctx context.Context, bucket string, objectKeys []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteObjects", ctx, bucket, objectKeys)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteObjects indicates an expected call of DeleteObjects.
func (mr *MockS3ClientInterfaceMockRecorder) DeleteObjects(ctx, bucket, objectKeys any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteObjects", reflect.TypeOf((*MockS3ClientInterface)(nil).DeleteObjects), ctx, bucket, objectKeys)
}

// GetObject mocks base method.
func (m *MockS3ClientInterface) GetObject(ctx context.Context, bucket, key string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObject", ctx, bucket, key)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObject indicates an expected call of GetObject.
func (mr *MockS3ClientInterfaceMockRecorder) GetObject(ctx, bucket, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObject", reflect.TypeOf((*MockS3ClientInterface)(nil).GetObject), ctx, bucket, key)
}

// GetObjectEtag mocks base method.
func (m *MockS3ClientInterface) GetObjectEtag(ctx context.Context, bucket, key string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetObjectEtag", ctx, bucket, key)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetObjectEtag indicates an expected call of GetObjectEtag.
func (mr *MockS3ClientInterfaceMockRecorder) GetObjectEtag(ctx, bucket, key any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetObjectEtag", reflect.TypeOf((*MockS3ClientInterface)(nil).GetObjectEtag), ctx, bucket, key)
}

// ListObjects mocks base method.
func (m *MockS3ClientInterface) ListObjects(ctx context.Context, bucket, directory string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjects", ctx, bucket, directory)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListObjects indicates an expected call of ListObjects.
func (mr *MockS3ClientInterfaceMockRecorder) ListObjects(ctx, bucket, directory any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjects", reflect.TypeOf((*MockS3ClientInterface)(nil).ListObjects), ctx, bucket, directory)
}

// ListObjectsWithMarker mocks base method.
func (m *MockS3ClientInterface) ListObjectsWithMarker(ctx context.Context, req S3ListRequest) ([]string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjectsWithMarker", ctx, req)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListObjectsWithMarker indicates an expected call of ListObjectsWithMarker.
func (mr *MockS3ClientInterfaceMockRecorder) ListObjectsWithMarker(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjectsWithMarker", reflect.TypeOf((*MockS3ClientInterface)(nil).ListObjectsWithMarker), ctx, req)
}

// MockEC2ClientInterface is a mock of EC2ClientInterface interface.
type MockEC2ClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockEC2ClientInterfaceMockRecorder
	isgomock struct{}
}

// MockEC2ClientInterfaceMockRecorder is the mock recorder for MockEC2ClientInterface.
type MockEC2ClientInterfaceMockRecorder struct {
	mock *MockEC2ClientInterface
}

// NewMockEC2ClientInterface creates a new mock instance.
func NewMockEC2ClientInterface(ctrl *gomock.Controller) *MockEC2ClientInterface {
	mock := &MockEC2ClientInterface{ctrl: ctrl}
	mock.recorder = &MockEC2ClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockEC2ClientInterface) EXPECT() *MockEC2ClientInterfaceMockRecorder {
	return m.recorder
}

// DeleteVPCEndpointsByID mocks base method.
func (m *MockEC2ClientInterface) DeleteVPCEndpointsByID(ctx context.Context, ids []string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteVPCEndpointsByID", ctx, ids)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteVPCEndpointsByID indicates an expected call of DeleteVPCEndpointsByID.
func (mr *MockEC2ClientInterfaceMockRecorder) DeleteVPCEndpointsByID(ctx, ids any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteVPCEndpointsByID", reflect.TypeOf((*MockEC2ClientInterface)(nil).DeleteVPCEndpointsByID), ctx, ids)
}

// DescribeSubnets mocks base method.
func (m *MockEC2ClientInterface) DescribeSubnets(ctx context.Context, subnets []string) (*[]types.Subnet, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeSubnets", ctx, subnets)
	ret0, _ := ret[0].(*[]types.Subnet)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeSubnets indicates an expected call of DescribeSubnets.
func (mr *MockEC2ClientInterfaceMockRecorder) DescribeSubnets(ctx, subnets any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeSubnets", reflect.TypeOf((*MockEC2ClientInterface)(nil).DescribeSubnets), ctx, subnets)
}

// DescribeVPCEndpoint mocks base method.
func (m *MockEC2ClientInterface) DescribeVPCEndpoint(ctx context.Context, id string) (*types.VpcEndpoint, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeVPCEndpoint", ctx, id)
	ret0, _ := ret[0].(*types.VpcEndpoint)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeVPCEndpoint indicates an expected call of DescribeVPCEndpoint.
func (mr *MockEC2ClientInterfaceMockRecorder) DescribeVPCEndpoint(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeVPCEndpoint", reflect.TypeOf((*MockEC2ClientInterface)(nil).DescribeVPCEndpoint), ctx, id)
}

// DescribeVPCEndpointService mocks base method.
func (m *MockEC2ClientInterface) DescribeVPCEndpointService(ctx context.Context, serviceName string) (*types.ServiceDetail, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeVPCEndpointService", ctx, serviceName)
	ret0, _ := ret[0].(*types.ServiceDetail)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeVPCEndpointService indicates an expected call of DescribeVPCEndpointService.
func (mr *MockEC2ClientInterfaceMockRecorder) DescribeVPCEndpointService(ctx, serviceName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeVPCEndpointService", reflect.TypeOf((*MockEC2ClientInterface)(nil).DescribeVPCEndpointService), ctx, serviceName)
}

// FilterVPCEndpointsIDByTag mocks base method.
func (m *MockEC2ClientInterface) FilterVPCEndpointsIDByTag(ctx context.Context, tagKey string, tagValues []string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "FilterVPCEndpointsIDByTag", ctx, tagKey, tagValues)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// FilterVPCEndpointsIDByTag indicates an expected call of FilterVPCEndpointsIDByTag.
func (mr *MockEC2ClientInterfaceMockRecorder) FilterVPCEndpointsIDByTag(ctx, tagKey, tagValues any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "FilterVPCEndpointsIDByTag", reflect.TypeOf((*MockEC2ClientInterface)(nil).FilterVPCEndpointsIDByTag), ctx, tagKey, tagValues)
}

// MockRDSClientInterface is a mock of RDSClientInterface interface.
type MockRDSClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockRDSClientInterfaceMockRecorder
	isgomock struct{}
}

// MockRDSClientInterfaceMockRecorder is the mock recorder for MockRDSClientInterface.
type MockRDSClientInterfaceMockRecorder struct {
	mock *MockRDSClientInterface
}

// NewMockRDSClientInterface creates a new mock instance.
func NewMockRDSClientInterface(ctrl *gomock.Controller) *MockRDSClientInterface {
	mock := &MockRDSClientInterface{ctrl: ctrl}
	mock.recorder = &MockRDSClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockRDSClientInterface) EXPECT() *MockRDSClientInterfaceMockRecorder {
	return m.recorder
}

// DescribeDBInstance mocks base method.
func (m *MockRDSClientInterface) DescribeDBInstance(ctx context.Context, id string) (*types0.DBInstance, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribeDBInstance", ctx, id)
	ret0, _ := ret[0].(*types0.DBInstance)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribeDBInstance indicates an expected call of DescribeDBInstance.
func (mr *MockRDSClientInterfaceMockRecorder) DescribeDBInstance(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribeDBInstance", reflect.TypeOf((*MockRDSClientInterface)(nil).DescribeDBInstance), ctx, id)
}

// StartDBInstance mocks base method.
func (m *MockRDSClientInterface) StartDBInstance(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartDBInstance", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartDBInstance indicates an expected call of StartDBInstance.
func (mr *MockRDSClientInterfaceMockRecorder) StartDBInstance(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartDBInstance", reflect.TypeOf((*MockRDSClientInterface)(nil).StartDBInstance), ctx, id)
}

// StopDBInstance mocks base method.
func (m *MockRDSClientInterface) StopDBInstance(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopDBInstance", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopDBInstance indicates an expected call of StopDBInstance.
func (mr *MockRDSClientInterfaceMockRecorder) StopDBInstance(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopDBInstance", reflect.TypeOf((*MockRDSClientInterface)(nil).StopDBInstance), ctx, id)
}
