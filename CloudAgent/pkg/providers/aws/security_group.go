package aws

import (
	"context"
	"fmt"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	ack_v1alpha1 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"
	vpcresources_v1beta1 "github.com/aws/amazon-vpc-resource-controller-k8s/apis/vpcresources/v1beta1"
	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsaws "github.com/risingwavelabs/cloudagent/pkg/utils/aws"
)

type SecurityGroupMeta struct {
	Status          *pbresource.Status
	SecurityGroupID string
}

type CreateSecurityGroupOption struct {
	VpcID                 string
	Region                string
	Namespace             string
	ResourceID            string
	InboundIPPermissions  []*pbsvcaws.IPPermission
	OutboundIPPermissions []*pbsvcaws.IPPermission
}

type CreateSecurityGroupPolicyOption struct {
	ResourceID        string
	Namespace         string
	SecurityGroupIDs  []string
	PodLabelsSelector map[string]string
}

func (provider *Provider) CreateSecurityGroupPolicy(ctx context.Context, option CreateSecurityGroupPolicyOption) error {
	logger.FromCtx(ctx).Infof("deleting security group policy %s in %s", option.ResourceID, option.Namespace)
	if err := provider.kc.Create(ctx, &vpcresources_v1beta1.SecurityGroupPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: vpcresources_v1beta1.SecurityGroupPolicySpec{
			SecurityGroups: vpcresources_v1beta1.GroupIds{
				Groups: option.SecurityGroupIDs,
			},
			PodSelector: &metav1.LabelSelector{
				MatchLabels: option.PodLabelsSelector,
			},
		},
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("security group policy already exists: %v", option).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create security group policy: %v", option)
	}
	logger.FromCtx(ctx).Infof("security group policy %s deleted", option.ResourceID)
	return nil
}

func (provider *Provider) GetSecurityGroupPolicy(ctx context.Context, namespace, resourceID string) (*vpcresources_v1beta1.SecurityGroupPolicy, error) {
	sgp, err := k8s.GetResource[vpcresources_v1beta1.SecurityGroupPolicy](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return nil, eris.Errorf("security group policy %s in %s is not found", resourceID, namespace).WithCode(eris.CodeNotFound)
		}
		return nil, errors.Wrapf(err, "failed to get security group policy %s in %s", resourceID, namespace)
	}
	return sgp, nil
}

func (provider *Provider) DeleteSecurityGroupPolicy(ctx context.Context, namespace, resourceID string) error {
	logger.FromCtx(ctx).Infof("deleting security group policy %s in %s", resourceID, namespace)
	if err := k8s.DeleteResource[vpcresources_v1beta1.SecurityGroupPolicy](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("security group policy %s in %s is not found", resourceID, namespace).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete security group policy %s in %s", resourceID, namespace)
	}
	logger.FromCtx(ctx).Infof("security group policy %s deleted", resourceID)
	return nil
}

// this operation is idempotent (ignore already exist).
func (provider *Provider) CreateSecurityGroup(ctx context.Context, option CreateSecurityGroupOption) error {
	logger.FromCtx(ctx).Infof("creating security group: %v", option)
	tags, err := utilsaws.AckTagsFromTags[ack_v1alpha1.Tag](
		provider.DefaultTags(option.ResourceID, option.Namespace),
	)
	if err != nil {
		return errors.Wrapf(err, "failed to generate resource tags")
	}

	sg := &ack_v1alpha1.SecurityGroup{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Annotations: map[string]string{
				"services.k8s.aws/region": option.Region,
			},
			Labels: k8s.DefaultTags(option.ResourceID, option.Namespace),
		},
		Spec: ack_v1alpha1.SecurityGroupSpec{
			Name:        utils.Ptr(option.ResourceID),
			VPCID:       utils.Ptr(option.VpcID),
			Description: utils.Ptr("Security group for RisingWave cluster pods"),
			Tags:        tags,
		},
	}

	if err := attachIPPermissions(option.InboundIPPermissions, option.OutboundIPPermissions, sg); err != nil {
		return eris.Wrap(err, "failed to attach IP permissions")
	}

	if err := provider.kc.Create(ctx, sg); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("security group %s in %s already exists", option.ResourceID, option.Namespace).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create security group %v", option)
	}
	logger.FromCtx(ctx).Infof("security group %s created", option.ResourceID)
	return nil
}

// this operation is idempotent (ignore not found).
func (provider *Provider) DeleteSecurityGroup(ctx context.Context, namespace, resourceID string) error {
	logger.FromCtx(ctx).Infof("deleting security group %s in %s", resourceID, namespace)
	if err := k8s.DeleteResource[ack_v1alpha1.SecurityGroup](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("security group %s is not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete security group %s", resourceID)
	}
	logger.FromCtx(ctx).Infof("security group %s deleted", resourceID)
	return nil
}

func (provider *Provider) GetSecurityGroup(ctx context.Context, namespace, resourceID string) (*SecurityGroupMeta, error) {
	sg, err := k8s.GetResource[ack_v1alpha1.SecurityGroup](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &SecurityGroupMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get security group %s", resourceID)
	}
	if sg.Spec.Name == nil {
		return nil, errors.Errorf(
			"failed to get resource name in spec, security group resource id: %s", resourceID)
	}
	status := utilsaws.AckConditionToResourceStatus(sg.Status.Conditions)
	if sg.Status.ID == nil {
		return &SecurityGroupMeta{
			Status: &pbresource.Status{
				Code:    status.GetCode(),
				Message: fmt.Sprintf("security group id is nil: %s", status.GetMessage()),
			},
			SecurityGroupID: "",
		}, nil
	}
	return &SecurityGroupMeta{
		Status:          status,
		SecurityGroupID: *sg.Status.ID,
	}, nil
}

func attachIPPermissions(inbound []*pbsvcaws.IPPermission, outbound []*pbsvcaws.IPPermission, sg *ack_v1alpha1.SecurityGroup) error {
	for _, p := range inbound {
		converted, err := convertIPPermissions(p)
		if err != nil {
			return eris.Wrapf(err, "failed to attach inbound IP permission: %v", inbound)
		}
		sg.Spec.IngressRules = append(sg.Spec.IngressRules, converted)
	}
	for _, p := range outbound {
		converted, err := convertIPPermissions(p)
		if err != nil {
			return eris.Wrapf(err, "failed to attach outbound IP permission: %v", inbound)
		}
		sg.Spec.EgressRules = append(sg.Spec.EgressRules, converted)
	}
	return nil
}

func convertIPPermissions(p *pbsvcaws.IPPermission) (*ack_v1alpha1.IPPermission, error) {
	if p == nil {
		return nil, eris.New("failed to convert IP Permission").WithCode(eris.CodeInvalidArgument)
	}
	var ipRange []*ack_v1alpha1.IPRange
	for _, r := range p.GetCidrs() {
		ipRange = append(ipRange, &ack_v1alpha1.IPRange{
			CIDRIP:      utils.Ptr(r),
			Description: utils.Ptr(p.GetDescription()),
		})
	}
	var userGroups []*ack_v1alpha1.UserIDGroupPair
	for _, id := range p.GetSourceSecurityGroupIds() {
		userGroups = append(userGroups, &ack_v1alpha1.UserIDGroupPair{
			GroupID:     utils.Ptr(id),
			Description: utils.Ptr(p.GetDescription()),
		})
	}
	return &ack_v1alpha1.IPPermission{
		IPProtocol:       utils.Ptr(p.GetProtocol()),
		FromPort:         utils.Ptr(int64(p.GetFromPort())),
		ToPort:           utils.Ptr(int64(p.GetToPort())),
		IPRanges:         ipRange,
		UserIDGroupPairs: userGroups,
	}, nil
}
