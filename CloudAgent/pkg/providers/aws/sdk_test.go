package aws

import (
	"fmt"
	"testing"

	"github.com/aws/aws-sdk-go-v2/service/ec2"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/aws/smithy-go"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
)

type smithyGenericAPIError struct {
	error
	code, msg string
}

func (e *smithyGenericAPIError) ErrorCode() string             { return e.code }
func (e *smithyGenericAPIError) ErrorMessage() string          { return e.msg }
func (e *smithyGenericAPIError) ErrorFault() smithy.ErrorFault { return 1 }

func TestExtractVPCEndpointServiceDetail(t *testing.T) {
	serviceName := "com.amazonaws.vpce.fake-service"

	tests := []struct {
		name        string
		res         *ec2.DescribeVpcEndpointServicesOutput
		err         error
		wantErrCode *eris.Code
		wantErrText string
		wantNil     bool
	}{
		{
			name: "InvalidServiceName error",
			err: &smithyGenericAPIError{
				code: "InvalidServiceName",
				msg:  "service not found",
			},
			wantErrCode: utils.Ptr(eris.CodeNotFound),
			wantErrText: "vpc endpoint service not found",
			wantNil:     true,
		},
		{
			name: "Other AWS error",
			err: &smithyGenericAPIError{
				code: "Throttling",
				msg:  "rate limit exceeded",
			},
			wantErrCode: utils.Ptr(eris.CodeInternal),
			wantErrText: "failed to describe VPC endpoint service",
			wantNil:     true,
		},
		{
			name:        "Generic error",
			err:         fmt.Errorf("something failed"),
			wantErrText: "failed to describe VPC endpoint service",
			wantNil:     true,
		},
		{
			name: "Empty result returns not found",
			res: &ec2.DescribeVpcEndpointServicesOutput{
				ServiceDetails: []types.ServiceDetail{},
			},
			wantErrCode: utils.Ptr(eris.CodeNotFound),
			wantErrText: "vpc endpoint service not found",
			wantNil:     true,
		},
		{
			name: "Success with one service detail",
			res: &ec2.DescribeVpcEndpointServicesOutput{
				ServiceDetails: []types.ServiceDetail{
					{ServiceName: &serviceName},
				},
			},
			wantNil: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			detail, err := extractVPCEndpointServiceDetail(tt.res, tt.err, serviceName)

			if tt.wantNil {
				assert.Nil(t, detail)
				assert.Error(t, err)
				if tt.wantErrCode != nil {
					assert.Equal(t, *tt.wantErrCode, eris.GetCode(err))
				}
				assert.Contains(t, err.Error(), tt.wantErrText)
			} else {
				assert.NoError(t, err)
				assert.NotNil(t, detail)
				assert.Equal(t, serviceName, *detail.ServiceName)
			}
		})
	}
}
