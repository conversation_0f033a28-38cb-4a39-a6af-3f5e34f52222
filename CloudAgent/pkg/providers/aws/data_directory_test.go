package aws

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestRunDeleteDataDirectoryTask(t *testing.T) {
	ctx := context.Background()

	var (
		taskName      = "task-id"
		taskNamespace = "ns"
	)

	p := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
			TaskConfig: taskconfig.Config{
				Image:          "image",
				ServiceAccount: "sa",
				Namespace:      taskNamespace,
				PullPolicy:     corev1.PullAlways,
			},
		},
	}

	err := p.RunDeleteDataDirectoryTask(ctx, taskName, "", "bucket", "data-directory")
	require.NoError(t, err)

	_, err = k8s.GetResource[batchv1.Job](context.Background(), p.kc.Client, taskName, taskNamespace)
	require.NoError(t, err)
}

func TestDeleteDataDirectory(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	p := &Provider{
		s3client: s3svc,
	}

	var (
		ctx           = context.Background()
		total         = 100
		page          = 10
		bucket        = "bucket"
		dataDirectory = "data-directory"
	)

	mockObjectKeys := map[*string]struct{}{}
	for i := 0; i < total; i++ {
		k := utils.Ptr(fmt.Sprintf("%d.data", i))
		mockObjectKeys[k] = struct{}{}
	}

	s3svc.
		EXPECT().
		ListObjects(gomock.Any(), gomock.Eq(bucket), gomock.Eq(dataDirectory+"/")).
		DoAndReturn(func(any, any, any) ([]*string, error) { // list only <page> keys
			rtn := []*string{}
			cnt := 0
			for k := range mockObjectKeys {
				rtn = append(rtn, k)
				cnt++
				if cnt == page {
					break
				}
			}
			return rtn, nil
		}).
		AnyTimes()

	s3svc.
		EXPECT().
		DeleteObjects(gomock.Any(), gomock.Eq(bucket), gomock.Any()).
		DoAndReturn(func(_ any, _ any, keys []*string) error { // delete keys listed from `mockObjectKeys`
			assert.Equal(t, page, len(keys), "should delete %d objects listed", page)
			for _, k := range keys {
				delete(mockObjectKeys, k)
			}
			return nil
		}).
		AnyTimes()

	err := p.DeleteDataDirectory(ctx, bucket, dataDirectory)

	assert.NoError(t, err)
}

func TestDeleteDataDirectory_delete_error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	p := &Provider{
		s3client: s3svc,
	}

	var (
		ctx           = context.Background()
		customErr     = errors.New("custom error")
		listCnt       = 5
		bucket        = "bucket"
		dataDirectory = "data-directory"
	)

	s3svc.
		EXPECT().
		ListObjects(gomock.Any(), gomock.Eq(bucket), gomock.Eq(dataDirectory+"/")).
		DoAndReturn(func(any, any, any) ([]*string, error) {
			listCnt--
			if listCnt <= 0 { // finish all deletions
				return []*string{}, nil
			}
			return []*string{utils.Ptr("some-key")}, nil
		}).
		AnyTimes()

	deleteCnt := 0
	s3svc.
		EXPECT().
		DeleteObjects(gomock.Any(), gomock.Eq(bucket), gomock.Any()).
		DoAndReturn(func(any, any, any) error { // return error
			deleteCnt++
			if deleteCnt&1 == 0 {
				return customErr
			}
			return nil
		}).
		AnyTimes()

	err := p.DeleteDataDirectory(ctx, "bucket", "data-directory")

	assert.NoError(t, err)
}

func TestDeleteDataDirectory_list_error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	var (
		customErr     = errors.New("custom error")
		bucket        = "bucket"
		dataDirectory = "data-directory"
	)

	p := &Provider{
		s3client: s3svc,
	}

	s3svc.
		EXPECT().
		ListObjects(gomock.Any(), gomock.Eq(bucket), gomock.Eq(dataDirectory+"/")).
		Return(nil, customErr).
		AnyTimes()

	err := p.DeleteDataDirectory(context.Background(), bucket, dataDirectory)

	assert.True(t, errors.Is(err, customErr))
}

func TestCloneDataDirectoryTask_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := NewMockS3ClientInterface(ctrl)
	provider := &Provider{
		s3client: mockS3Client,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := clone.Options{
		TaskID:                   "existing-directory",
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
		Cursor:                   "start",
		CloneSize:                10,
	}

	objs := []string{"my-dir/test1", "my-dir/test2"}
	mockS3Client.EXPECT().
		ListObjectsWithMarker(
			gomock.Any(),
			S3ListRequest{
				Bucket:    options.SourceBucketName,
				Directory: options.SourceDirectoryName,
				Marker:    &options.Cursor,
				MaxSize:   options.CloneSize,
			}).Return(objs, "", nil)

	mockS3Client.EXPECT().CopyObject(gomock.Any(), S3CopyRequest{SourceKey: "my-dir/test1", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test1", SinkBucket: options.DestinationBucketName}).Return(nil)
	mockS3Client.EXPECT().CopyObject(gomock.Any(), S3CopyRequest{SourceKey: "my-dir/test2", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test2", SinkBucket: options.DestinationBucketName}).Return(nil)

	err := provider.CloneDataDirectory(context.Background(), options)
	assert.NoError(t, err, "Expected no error")
}

func TestCloneDataDirectoryTask_Fail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockS3Client := NewMockS3ClientInterface(ctrl)
	provider := &Provider{
		s3client: mockS3Client,
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := clone.Options{
		TaskID:                   "existing-directory",
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
		Cursor:                   "start",
		CloneSize:                10,
	}

	objs := []string{"my-dir/test1", "my-dir/test2"}
	mockS3Client.EXPECT().
		ListObjectsWithMarker(
			gomock.Any(),
			S3ListRequest{
				Bucket:    options.SourceBucketName,
				Directory: options.SourceDirectoryName,
				Marker:    &options.Cursor,
				MaxSize:   options.CloneSize,
			}).Return(objs, "", nil).Times(cloneTaskRetry)

	mockS3Client.EXPECT().CopyObject(gomock.Any(), S3CopyRequest{SourceKey: "my-dir/test1", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test1", SinkBucket: options.DestinationBucketName}).Return(nil).Times(3)
	mockS3Client.EXPECT().CopyObject(gomock.Any(), S3CopyRequest{SourceKey: "my-dir/test2", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test2", SinkBucket: options.DestinationBucketName}).Return(eris.New("copy error")).Times(15)

	err := provider.CloneDataDirectory(context.Background(), options)
	assert.Error(t, err)
}
