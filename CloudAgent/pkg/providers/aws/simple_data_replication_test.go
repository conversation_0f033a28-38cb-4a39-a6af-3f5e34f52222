package aws

import (
	"context"
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
)

func TestListAllObjects(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	var (
		ctx       = context.Background()
		bucket    = "bucket"
		directory = "directory"
	)

	s3svc.
		EXPECT().
		ListObjectsWithMarker(ctx, gomock.Any()).
		DoAndReturn(func(_ context.Context, req S3ListRequest) ([]string, string, error) {
			if req.Marker == nil {
				return []string{"1", "2", "3"}, "test-marker", nil
			}
			return []string{"4", "5", "6"}, "", nil
		}).
		AnyTimes()

	p := &Provider{
		s3client: s3svc,
	}
	rtn, err := p.ListAllObjects(ctx, bucket, directory)
	require.NoError(t, err)

	assert.Equal(t, []string{"1", "2", "3", "4", "5", "6"}, rtn)
}

func TestSimpleDataReplication(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	var (
		ctx          = context.Background()
		sourceBucket = "source-bucket"
		sourceDir    = "source-directory/p1/p2/p3"
		sinkBucket   = "sink-bucket"
		sinkDir      = "sink-directory"
		filename     = "test.txt"
		keys         = []string{fmt.Sprintf("%s/%s", sourceDir, filename)}
	)

	s3svc.
		EXPECT().
		ListObjectsWithMarker(ctx, S3ListRequest{Bucket: sourceBucket, Directory: fmt.Sprintf("%s/", sourceDir)}).
		Return(keys, "", nil)

	s3svc.
		EXPECT().
		CopyObject(ctx,
			S3CopyRequest{
				SourceBucket: sourceBucket,
				SourceKey:    fmt.Sprintf("%s/%s", sourceDir, filename),
				SinkBucket:   sinkBucket,
				SinkKey:      fmt.Sprintf("%s/%s", sinkDir, filename),
			}).Return(nil)

	p := &Provider{
		s3client: s3svc,
	}

	err := p.SimpleDataReplication(ctx, sourceBucket, sourceDir, sinkBucket, sinkDir)
	require.NoError(t, err)
}
