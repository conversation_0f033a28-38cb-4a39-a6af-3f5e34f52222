package aws

import (
	"context"
	"testing"

	ackiamv1alpha1 "github.com/aws-controllers-k8s/iam-controller/apis/v1alpha1"
	ackv1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8sapi "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	testClusterID       = "test-cluster"
	testAWSAccountID    = "1234"
	testAWSRegion       = "us-east-1"
	testVPCID           = "test-vpc"
	testSecurityGroupID = "test-sg"
	testOIDCIssuer      = "test-oidc"

	testBucket          = "test-bucket"
	testDir1            = "test-dir1"
	testDir2            = "test-dir2"
	testTenantNamespace = "rwc-g1ha9k6gcketqpkut5bcmlm8nq-test"
	testResourceTag     = "risingwave.cloud/private-endpoint-default-tag/tenant-resource-namespace"

	expectedResourcePolicySingleDir = `
{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Action": ["s3:*"],
				"Resource": [
					"arn:aws:s3:::test-bucket",
					"arn:aws:s3:::test-bucket/test-dir1/*"
				]
			}
		]
}`

	expectedResourcePolicy = `
{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Action": ["s3:*"],
				"Resource": [
					"arn:aws:s3:::test-bucket",
					"arn:aws:s3:::test-bucket/test-dir1/*",
					"arn:aws:s3:::test-bucket/test-dir2/*"
				]
			}
		]
}`

	expectedRolePolicy = `
{
	"Version": "2012-10-17",
	"Statement": [
		{
			"Effect": "Allow",
			"Principal": {
				"Federated": "arn:aws:iam::1234:oidc-provider/test-oidc"
			},
			"Action": "sts:AssumeRoleWithWebIdentity",
			"Condition": {
				"StringEquals": {
					"test-oidc:aud": "sts.amazonaws.com",
					"test-oidc:sub": "system:serviceaccount:ns:sa"
				}
			}
		}
	]
}
`
)

func TestCreateIAMPolicySingleDir(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	ctx := context.Background()
	option := CreateIAMPolicyOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		AccessOptions: []AccessOption{
			S3AccessOption{
				Bucket: testBucket,
				Dirs:   []string{testDir1},
			},
		},
	}
	require.NoError(t, provider.CreateIAMPoicy(ctx, option))

	policy := ackiamv1alpha1.Policy{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &policy))
	assert.JSONEq(t, expectedResourcePolicySingleDir, *policy.Spec.PolicyDocument)
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMPoicy(ctx, option)))
}

func TestCreateIAMPolicy(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	ctx := context.Background()
	option := CreateIAMPolicyOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		AccessOptions: []AccessOption{
			S3AccessOption{
				Bucket: testBucket,
				Dirs:   []string{testDir1, testDir2},
			},
		},
	}
	require.NoError(t, provider.CreateIAMPoicy(ctx, option))

	policy := ackiamv1alpha1.Policy{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &policy))
	assert.JSONEq(t, expectedResourcePolicy, *policy.Spec.PolicyDocument)
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMPoicy(ctx, option)))
}

func TestCreateIAMPolicyInvalid(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	ctx := context.Background()
	option := CreateIAMPolicyOption{
		ResourceID: resourceID,
		Namespace:  namespace,
	}
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(provider.CreateIAMPoicy(ctx, option)))
}

func TestCreateIAMRole(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	const (
		resourceID      = "resource"
		namespace       = "ns"
		policyID        = "policy"
		policyNamespace = "ns"
		saName          = "sa"
		saNamespace     = "ns"
	)
	ctx := context.Background()
	option := CreateIAMRoleOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		SA: K8sServiceAccountOption{
			Name:      saName,
			Namespace: saNamespace,
		},
		AccountID:    testAWSAccountID,
		OIDCPrivider: testOIDCIssuer,
		PolicyRefs: []*pbresource.Meta{
			{
				Id:        policyID,
				Namespace: policyNamespace,
			},
		},
	}
	require.NoError(t, provider.CreateIAMRole(ctx, option))

	role := ackiamv1alpha1.Role{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &role))
	assert.JSONEq(t, expectedRolePolicy, *role.Spec.AssumeRolePolicyDocument)
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateIAMRole(ctx, option)))
}

func TestCreateIAMRoleInvalidPolicyRef(t *testing.T) {
	client := fake.NewClient()
	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	const (
		resourceID      = "resource"
		namespace       = "ns"
		policyID        = "policy"
		policyNamespace = "ns1"
		saName          = "sa"
		saNamespace     = "ns"
	)
	ctx := context.Background()
	option := CreateIAMRoleOption{
		ResourceID: resourceID,
		Namespace:  namespace,
		SA: K8sServiceAccountOption{
			Name:      saName,
			Namespace: saNamespace,
		},
		AccountID:    testAWSAccountID,
		OIDCPrivider: testOIDCIssuer,
		PolicyRefs: []*pbresource.Meta{
			{
				Id:        policyID,
				Namespace: policyNamespace,
			},
		},
	}
	assert.Equal(t, eris.CodeInvalidArgument, eris.GetCode(provider.CreateIAMRole(ctx, option)))
}

func TestDeleteIAMPolicy(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	policy := ackiamv1alpha1.Policy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&policy)

	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	ctx := context.Background()
	require.NoError(t, provider.DeleteIAMPolicy(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	policy = ackiamv1alpha1.Policy{}
	assert.True(t, k8sErrors.IsNotFound(client.Get(ctx, objKey, &policy)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteIAMPolicy(ctx, namespace, resourceID)))
}

func TestDeleteIAMRole(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	role := ackiamv1alpha1.Role{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&role)

	provider := &Provider{
		kc: &k8s.KubernetesClient{Client: client},
	}
	ctx := context.Background()
	require.NoError(t, provider.DeleteIAMRole(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	role = ackiamv1alpha1.Role{}
	assert.True(t, k8sErrors.IsNotFound(client.Get(ctx, objKey, &role)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteIAMRole(ctx, namespace, resourceID)))
}

func TestGetIAMPolicy(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
		testARN    = "arn"
	)
	tests := []struct {
		description  string
		policy       *ackiamv1alpha1.Policy
		arn          *string
		expectedMeta *IAMPolicyMeta
		errCode      *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			policy: &ackiamv1alpha1.Policy{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ackiamv1alpha1.PolicyStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: utils.Ptr(ackv1alpha1.AWSResourceName(testARN)),
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:   ackv1alpha1.ConditionTypeResourceSynced,
							Status: k8sapi.ConditionTrue,
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				ARN: testARN,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			policy: &ackiamv1alpha1.Policy{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ackiamv1alpha1.PolicyStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: utils.Ptr(ackv1alpha1.AWSResourceName(testARN)),
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:    ackv1alpha1.ConditionTypeResourceSynced,
							Status:  k8sapi.ConditionFalse,
							Message: utils.Ptr("resource not ready"),
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"type\":\"ACK.ResourceSynced\",\"status\":\"False\",\"message\":\"resource not ready\"}]",
				},
				ARN: testARN,
			},
		},
		{
			description: "Normal case, resource is in error state",
			policy: &ackiamv1alpha1.Policy{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ackiamv1alpha1.PolicyStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: nil,
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:    ackv1alpha1.ConditionTypeTerminal,
							Status:  k8sapi.ConditionTrue,
							Message: utils.Ptr("resource in error state"),
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_ERROR,
					Message: "policy ARN is nil: [{\"type\":\"ACK.Terminal\",\"status\":\"True\",\"message\":\"resource in error state\"}]",
				},
				ARN: "",
			},
		},
		{
			description: "Normal case, resource is not found",
			policy: &ackiamv1alpha1.Policy{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "randomresource",
				},
				Status: ackiamv1alpha1.PolicyStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: utils.Ptr(ackv1alpha1.AWSResourceName(testARN)),
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:    ackv1alpha1.ConditionTypeTerminal,
							Status:  k8sapi.ConditionTrue,
							Message: utils.Ptr("resource in error state"),
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMPolicyMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
				ARN: "",
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.policy)

		provider := &Provider{
			kc: &k8s.KubernetesClient{Client: client},
		}

		meta, err := provider.GetIAMPolicy(context.Background(), namespace, resourceID)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, meta, tt.expectedMeta, "unexpect result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
		}
	}
}

func TestGetIAMRole(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
		testARN    = "arn"
	)
	tests := []struct {
		description  string
		role         *ackiamv1alpha1.Role
		arn          *string
		expectedMeta *IAMRoleMeta
		errCode      *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			role: &ackiamv1alpha1.Role{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ackiamv1alpha1.RoleStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: utils.Ptr(ackv1alpha1.AWSResourceName(testARN)),
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:   ackv1alpha1.ConditionTypeResourceSynced,
							Status: k8sapi.ConditionTrue,
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMRoleMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				ARN: testARN,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			role: &ackiamv1alpha1.Role{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ackiamv1alpha1.RoleStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: utils.Ptr(ackv1alpha1.AWSResourceName(testARN)),
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:    ackv1alpha1.ConditionTypeResourceSynced,
							Status:  k8sapi.ConditionFalse,
							Message: utils.Ptr("resource not ready"),
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMRoleMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"type\":\"ACK.ResourceSynced\",\"status\":\"False\",\"message\":\"resource not ready\"}]",
				},
				ARN: testARN,
			},
		},
		{
			description: "Normal case, resource is in error state",
			role: &ackiamv1alpha1.Role{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ackiamv1alpha1.RoleStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: nil,
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:    ackv1alpha1.ConditionTypeTerminal,
							Status:  k8sapi.ConditionTrue,
							Message: utils.Ptr("resource in error state"),
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMRoleMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_ERROR,
					Message: "role ARN is nil: [{\"type\":\"ACK.Terminal\",\"status\":\"True\",\"message\":\"resource in error state\"}]",
				},
				ARN: "",
			},
		},
		{
			description: "Normal case, resource is not found",
			role: &ackiamv1alpha1.Role{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "randomresource",
				},
				Status: ackiamv1alpha1.RoleStatus{
					ACKResourceMetadata: &ackv1alpha1.ResourceMetadata{
						ARN: utils.Ptr(ackv1alpha1.AWSResourceName(testARN)),
					},
					Conditions: []*ackv1alpha1.Condition{
						{
							Type:    ackv1alpha1.ConditionTypeTerminal,
							Status:  k8sapi.ConditionTrue,
							Message: utils.Ptr("resource in error state"),
						},
					},
				},
			},
			arn: utils.Ptr(testARN),
			expectedMeta: &IAMRoleMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
				ARN: "",
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.role)

		provider := &Provider{
			kc: &k8s.KubernetesClient{Client: client},
		}

		meta, err := provider.GetIAMRole(context.Background(), namespace, resourceID)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, meta, tt.expectedMeta, "unexpect result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
		}
	}
}
