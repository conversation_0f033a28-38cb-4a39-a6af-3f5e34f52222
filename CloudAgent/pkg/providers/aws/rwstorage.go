package aws

import (
	"context"

	"github.com/risingwavelabs/eris"
)

func (provider *Provider) GetFile(ctx context.Context, bucket, key string) ([]byte, error) {
	objs, err := provider.s3client.ListObjects(ctx, bucket, key)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to list objects for %s/%s", bucket, key)
	}
	if len(objs) == 0 {
		return nil, eris.WithCode(eris.New("file not found"), eris.CodeNotFound)
	}

	etag, err := provider.s3client.GetObjectEtag(ctx, bucket, key)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get etag for %s/%s", bucket, key)
	}

	val, ok := provider.cache.Get(key, etag)
	if ok {
		return val, nil
	}

	manifest, err := provider.s3client.GetObject(ctx, bucket, key)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get manifest for %s/%s", bucket, key)
	}

	provider.cache.Set(key, manifest, etag)
	return manifest, nil
}
