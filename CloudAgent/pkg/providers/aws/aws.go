package aws

import (
	"context"

	"github.com/pkg/errors"
	rbacv1 "k8s.io/api/rbac/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbcfgaws "github.com/risingwavelabs/cloudagent/pbgen/config/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	utilsaws "github.com/risingwavelabs/cloudagent/pkg/utils/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils/etagcache"
)

type Provider struct {
	kc          *k8s.KubernetesClient
	s3client    S3ClientInterface
	ec2Client   EC2ClientInterface
	rdsClient   RDSClientInterface
	defaultTags utilsaws.Tags
	cache       *etagcache.Cache
}

type NewProviderOption struct {
	Kc        *k8s.KubernetesClient
	AWSConfig *pbcfgaws.Config
}

func NewProvider(ctx context.Context, option NewProviderOption) (*Provider, error) {
	if option.Kc == nil {
		return nil, eris.New("kubernetes client cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	if option.AWSConfig == nil {
		return nil, eris.New("AWS config cannot be nil").WithCode(eris.CodeInvalidArgument)
	}

	s3client, err := newS3Client(ctx, option.AWSConfig)
	if err != nil {
		return nil, errors.Wrap(err, "failed to init S3 client")
	}

	ec2Client, err := newEC2Client(ctx, option.AWSConfig)
	if err != nil {
		return nil, errors.Wrap(err, "failed to init ec2 client")
	}

	rdsClient, err := newRDSClient(ctx, option.AWSConfig)
	if err != nil {
		return nil, errors.Wrap(err, "failed to init rds client")
	}

	defaultTags, err := utilsaws.ParseTags(option.AWSConfig.GetDefaultTags())
	if err != nil {
		return nil, errors.Wrapf(err, "failed to parse tags: %s", option.AWSConfig.GetDefaultTags())
	}

	return &Provider{
		kc:          option.Kc,
		s3client:    s3client,
		ec2Client:   ec2Client,
		rdsClient:   rdsClient,
		defaultTags: defaultTags,
		cache:       etagcache.NewCache(),
	}, nil
}

func (provider *Provider) DefaultTags(resourceID string, namespace string) utilsaws.Tags {
	return provider.defaultTags.Clone().With(utilsaws.BasicDefaultTags(resourceID, namespace))
}

// for testing purposes only.
func CreateFakeProvider(client k8sclient.Client, ec2svc EC2ClientInterface, s3svc S3ClientInterface, rdssvc RDSClientInterface) *Provider {
	k8sClient := &k8s.KubernetesClient{
		Client:    client,
		Interface: fake.NewClientset(&rbacv1.ClusterRole{}),
	}
	res := &Provider{
		kc: k8sClient,
	}

	if ec2svc != nil {
		res.ec2Client = ec2svc
	}
	if s3svc != nil {
		res.s3client = s3svc
	}
	if rdssvc != nil {
		res.rdsClient = rdssvc
	}

	return res
}
