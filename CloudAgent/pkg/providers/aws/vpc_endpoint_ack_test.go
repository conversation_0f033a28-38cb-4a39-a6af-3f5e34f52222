package aws

import (
	"context"
	"fmt"
	"testing"

	ack_v1alpha1 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"
	ack_core_v1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	v1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

var (
	testStatusAccepted       = "AVAILABLE"
	testVpcID                = "testVPCID"
	testVPCEndpointDNS       = "testDNS"
	testStatusProtoAvailable = aws.VPCEndpointStatus_AVAILABLE
	testStatusProtoRejected  = aws.VPCEndpointStatus_REJECTED
	codeFailedPrecondition   = eris.Code(9)
	errorStateMsg            = "resource is error state"
	testSubnets              = []string{"subnet1", "subnet2", "subnet3"}
	testSecurityGroups       = []string{"sg1", "sg2", "sg3"}
	testTags                 = []ack_v1alpha1.Tag{{Key: utils.Ptr("Project"), Value: utils.Ptr("RisingWave")}, {Key: utils.Ptr("EnvID"), Value: utils.Ptr("env")}}
)

func TestCheckVPCEndpointServiceReachability(t *testing.T) {
	ctx := context.Background()

	var (
		testService = "testService"
	)

	serviceDetail := types.ServiceDetail{
		AvailabilityZones: []string{"az1", "az2", "az3"},
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ec2svc := NewMockEC2ClientInterface(ctrl)

	p := CreateFakeProvider(fake.NewClient(), ec2svc, nil, nil)

	ec2svc.
		EXPECT().
		DescribeVPCEndpointService(gomock.Any(), testService).
		Return(&serviceDetail, nil).
		AnyTimes()

	require.NoError(t, p.CheckVPCEndpointServiceReachability(ctx, testService))
}

func TestCreateVPCEndpoint(t *testing.T) {
	ctx := context.Background()

	var (
		testName              = "task-id"
		testNamespace         = "ns"
		testService           = "testService"
		testPrivateDNSEnabled = true
	)

	serviceDetail := types.ServiceDetail{
		AvailabilityZones: []string{"az1", "az2", "az3"},
	}

	var subnetDetails []types.Subnet
	for i := range 3 {
		az := fmt.Sprintf("az%d", i+1)
		subnet := fmt.Sprintf("subnet%d", i+1)
		subnetDetails = append(
			subnetDetails,
			types.Subnet{
				AvailabilityZone: &az,
				SubnetId:         &subnet,
			},
		)
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ec2svc := NewMockEC2ClientInterface(ctrl)

	p := CreateFakeProvider(fake.NewClient(), ec2svc, nil, nil)

	ec2svc.
		EXPECT().
		DescribeVPCEndpointService(gomock.Any(), gomock.Any()).
		Return(&serviceDetail, nil).
		AnyTimes()

	ec2svc.
		EXPECT().
		DescribeSubnets(gomock.Any(), gomock.Any()).
		Return(&subnetDetails, nil).
		AnyTimes()

	option := CreateVPCEndpointOption{
		VpcID:             testVpcID,
		Namespace:         testNamespace,
		ExtraTags:         map[string]string{"EnvID": "env"},
		ResourceID:        testName,
		ServiceName:       testService,
		SubnetIDs:         testSubnets,
		SecurityGroupIDs:  testSecurityGroups,
		PrivateDNSEnabled: testPrivateDNSEnabled,
	}
	require.NoError(t, p.CreateVPCEndpoint(ctx, option))

	vpcEndpoint := ack_v1alpha1.VPCEndpoint{}
	objKey := k8sclient.ObjectKey{
		Name:      testName,
		Namespace: testNamespace,
	}

	require.NoError(t, p.kc.Get(ctx, objKey, &vpcEndpoint))
	assert.NotEmpty(t, vpcEndpoint.Spec.SubnetIDs)
	assert.NotEmpty(t, vpcEndpoint.Spec.SecurityGroupIDs)
	assert.NotEmpty(t, vpcEndpoint.Spec.Tags)
	for i, s := range vpcEndpoint.Spec.SubnetIDs {
		assert.Equal(t, testSubnets[i], *s)
	}
	for i, sg := range vpcEndpoint.Spec.SecurityGroupIDs {
		assert.Equal(t, testSecurityGroups[i], *sg)
	}
	for i, tag := range vpcEndpoint.Spec.Tags {
		assert.Equal(t, testTags[i], *tag)
	}

	assert.True(t, utils.IsErrAlreadyExists(p.CreateVPCEndpoint(ctx, option)))
	assert.Equal(t, testPrivateDNSEnabled, *vpcEndpoint.Spec.PrivateDNSEnabled)
}

func TestDeleteVPCEndpoint(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	vpcEndpoint := ack_v1alpha1.VPCEndpoint{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}

	client := fake.NewClient(&vpcEndpoint)
	provider := CreateFakeProvider(client, nil, nil, nil)
	ctx := context.Background()
	require.NoError(t, provider.DeleteVPCEndpoint(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	vpcEndpoint = ack_v1alpha1.VPCEndpoint{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &vpcEndpoint)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteVPCEndpoint(ctx, namespace, resourceID)))
}

func TestGetVPCEndpoint(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ec2svc := NewMockEC2ClientInterface(ctrl)

	type VPCEndpointDescribeRes struct {
		endpoint *types.VpcEndpoint
		err      error
	}

	tests := []struct {
		description                 string
		vpcEndpoint                 *ack_v1alpha1.VPCEndpoint
		expectedMeta                *VPCEndpointMeta
		expectedVPCEndpointDescribe *VPCEndpointDescribeRes
	}{
		{
			description: "Normal case, resource is ready",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:   ack_core_v1alpha1.ConditionTypeResourceSynced,
							Status: v1.ConditionTrue,
						},
					},
					State:         &testStatusAccepted,
					VPCEndpointID: &testVpcID,
				},
				Spec: ack_v1alpha1.VPCEndpointSpec{
					VPCID: &testVpcID,
				},
			},
			expectedVPCEndpointDescribe: &VPCEndpointDescribeRes{
				endpoint: &types.VpcEndpoint{
					State: "available",
					DnsEntries: []types.DnsEntry{
						{
							DnsName: &testVPCEndpointDNS,
						},
					},
				},
			},
			expectedMeta: &VPCEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				VPCEndpointState: testStatusProtoAvailable,
				VPCEndpointID:    testVpcID,
				VPCEndpointDNS:   testVPCEndpointDNS,
			},
		},
		{
			description: "Normal case, resource is rejected",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:   ack_core_v1alpha1.ConditionTypeResourceSynced,
							Status: v1.ConditionTrue,
						},
					},
					State:         &testStatusAccepted,
					VPCEndpointID: &testVpcID,
				},
				Spec: ack_v1alpha1.VPCEndpointSpec{
					VPCID: &testVpcID,
				},
			},
			expectedVPCEndpointDescribe: &VPCEndpointDescribeRes{
				endpoint: &types.VpcEndpoint{
					State: "rejected",
				},
			},
			expectedMeta: &VPCEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				VPCEndpointState: testStatusProtoRejected,
				VPCEndpointID:    testVpcID,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:   ack_core_v1alpha1.ConditionTypeResourceSynced,
							Status: v1.ConditionFalse,
						},
					},
				},
			},
			expectedMeta: &VPCEndpointMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"type\":\"ACK.ResourceSynced\",\"status\":\"False\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is in error state",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:    ack_core_v1alpha1.ConditionTypeTerminal,
							Status:  v1.ConditionFalse,
							Message: &errorStateMsg,
						},
					},
				},
			},
			expectedMeta: &VPCEndpointMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"type\":\"ACK.Terminal\",\"status\":\"False\",\"message\":\"resource is error state\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "random namespace",
					Name:      "random id",
				},
			},
			expectedMeta: &VPCEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.vpcEndpoint)
		provider := CreateFakeProvider(client, ec2svc, nil, nil)
		const (
			resourceID = "resource"
			namespace  = "ns"
		)

		if tt.expectedVPCEndpointDescribe != nil {
			ec2svc.EXPECT().
				DescribeVPCEndpoint(gomock.Any(), gomock.Any()).
				Return(tt.expectedVPCEndpointDescribe.endpoint, tt.expectedVPCEndpointDescribe.err).
				Times(1)
		}

		meta, err := provider.GetVPCEndpoint(context.Background(), namespace, resourceID)
		require.NoError(t, err, "expect no error for test %v", tt)
		assert.Equal(t, meta, tt.expectedMeta, "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
	}
}

func TestGetValidSubnets(t *testing.T) {
	const (
		resourceID  = "resource"
		namespace   = "ns"
		serviceName = "serviceName"
	)

	var (
		az1 = "az1"
		az2 = "az2"

		s1 = "subnet1"
		s2 = "subnet2"
	)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ec2svc := NewMockEC2ClientInterface(ctrl)

	tests := []struct {
		description          string
		subnetsInput         []string
		serviceNameInput     string
		serviceDetails       *types.ServiceDetail
		getServiceDetailsErr error
		subnetDetails        *[]types.Subnet
		getSubnetErr         error
		expectedResult       []*string
		errCode              *eris.Code
	}{
		{
			description: "Normal case, complete intersection",
			serviceDetails: &types.ServiceDetail{
				AvailabilityZones: []string{az1, az2},
			},
			subnetsInput: []string{s1, s2},
			subnetDetails: &[]types.Subnet{
				{
					SubnetId:         &s1,
					AvailabilityZone: &az1,
				},
				{
					SubnetId:         &s2,
					AvailabilityZone: &az2,
				},
			},
			expectedResult: []*string{&s1, &s2},
		},
		{
			description: "Normal case, partial intersection 1",
			serviceDetails: &types.ServiceDetail{
				AvailabilityZones: []string{az1, az2},
			},
			subnetsInput: []string{s1},
			subnetDetails: &[]types.Subnet{
				{
					SubnetId:         &s1,
					AvailabilityZone: &az1,
				},
			},
			expectedResult: []*string{&s1},
		},
		{
			description: "Normal case, partial intersection 2",
			serviceDetails: &types.ServiceDetail{
				AvailabilityZones: []string{az1},
			},
			subnetsInput: []string{s1, s2},
			subnetDetails: &[]types.Subnet{
				{
					SubnetId:         &s1,
					AvailabilityZone: &az1,
				},
				{
					SubnetId:         &s2,
					AvailabilityZone: &az2,
				},
			},
			expectedResult: []*string{&s1},
		},
		{
			description: "Normal case, no intersection",
			serviceDetails: &types.ServiceDetail{
				AvailabilityZones: []string{az1},
			},
			subnetsInput: []string{s2},
			subnetDetails: &[]types.Subnet{
				{
					SubnetId:         &s2,
					AvailabilityZone: &az2,
				},
			},
			errCode: &codeFailedPrecondition,
		},
	}

	for _, tt := range tests {
		client := fake.NewClient()
		provider := CreateFakeProvider(client, ec2svc, nil, nil)

		if tt.serviceDetails != nil {
			ec2svc.EXPECT().
				DescribeVPCEndpointService(gomock.Any(), serviceName).
				Return(tt.serviceDetails, tt.getServiceDetailsErr).
				Times(1)
		}
		if tt.serviceDetails != nil {
			ec2svc.EXPECT().
				DescribeSubnets(gomock.Any(), tt.subnetsInput).
				Return(tt.subnetDetails, tt.getSubnetErr).
				Times(1)
		}

		res, err := provider.getValidEndpointSubnets(context.Background(), serviceName, tt.subnetsInput)
		if tt.errCode != nil {
			require.Error(t, err, "expect error to be thrown for test %v", tt)
			assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
		} else {
			require.NoError(t, err, "expect no error for test %v", tt)
			assert.Equal(t, res, tt.expectedResult, "unexpected result for test %v, get %v, want %v", tt, res, tt.expectedResult)
		}
	}
}
