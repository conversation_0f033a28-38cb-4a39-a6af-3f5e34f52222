package aws

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/utils/etagcache"
)

func TestGetFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	p := &Provider{
		s3client: s3svc,
		cache:    etagcache.NewCache(),
	}

	bucketName := "my-bucket"
	key := "my-key"
	content := []byte("test")

	s3svc.EXPECT().ListObjects(gomock.Any(), bucketName, key).Return([]string{key}, nil)
	s3svc.EXPECT().GetObjectEtag(gomock.Any(), bucketName, key).Return("test", nil)
	s3svc.EXPECT().GetObject(gomock.Any(), bucketName, key).Return(content, nil)

	rtn, err := p.GetFile(context.Background(), bucketName, key)
	assert.NoError(t, err)
	assert.Equal(t, content, rtn)
}

func TestGetFile_not_found(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	s3svc := NewMockS3ClientInterface(ctrl)

	p := &Provider{
		s3client: s3svc,
		cache:    etagcache.NewCache(),
	}

	bucketName := "my-bucket"
	key := "my-key"

	s3svc.EXPECT().ListObjects(gomock.Any(), bucketName, key).Return([]string{}, nil)

	_, err := p.GetFile(context.Background(), bucketName, key)
	assert.Error(t, err)
	assert.Equal(t, eris.CodeNotFound, eris.GetCode(err))
}
