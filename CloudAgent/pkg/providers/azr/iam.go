package azr

import (
	"context"
	"fmt"
	"strings"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	asoauthorization "github.com/Azure/azure-service-operator/v2/api/authorization/v1api20220401"
	asomanagedidentity "github.com/Azure/azure-service-operator/v2/api/managedidentity/v1api20230131"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime"
	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsazr "github.com/risingwavelabs/cloudagent/pkg/utils/azr"
)

const (
	FederatedIdentityCredentialAudience = "api://AzureADTokenExchange"
)

type CreateUserAssignedIdentityOption struct {
	Namespace      string
	ResourceID     string
	SubscriptionID string
	ResourceGroup  string
	Location       string
}

type CreateFederatedIdentityCredentialOption struct {
	Namespace                string
	ResourceID               string
	UserAssignedIdentityName string
	OIDCIssuer               string
	ServiceAccount           KubernetesServiceAccount
}

type AccessOption interface{}

type CreateRoleAssignmentOption struct {
	Namespace      string
	ResourceID     string
	SubscriptionID string
	PrincipleID    string
	AccessOption   AccessOption
}

type BlobAccessOption struct {
	StorageAccount string
	Container      string
	Dirs           []string
}

type UserAssignedIdentityMeta struct {
	Status      *pbresource.Status
	PrincipalID string
	ClientID    string
}

type FederatedIdentityCredentialMeta struct {
	Status *pbresource.Status
}

type RoleAssignmentMeta struct {
	Status *pbresource.Status
}

type KubernetesServiceAccount struct {
	Name      string
	Namespace string
}

func (provider *Provider) CreateUserAssignedIdentity(ctx context.Context, opt CreateUserAssignedIdentityOption) error {
	uai := &asomanagedidentity.UserAssignedIdentity{
		ObjectMeta: metav1.ObjectMeta{
			Name:      opt.ResourceID,
			Namespace: opt.Namespace,
		},
		Spec: asomanagedidentity.UserAssignedIdentity_Spec{
			Location: &opt.Location,
			Owner: &genruntime.KnownResourceReference{
				ARMID: getResourceGroupARMID(opt.SubscriptionID, opt.ResourceGroup),
			},
		},
	}

	err := provider.kc.Create(ctx, uai)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("user assigned identity %s already exists", opt.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create UserAssignedIdentity")
	}

	return nil
}

func (provider *Provider) GetUserAssignedIdentity(ctx context.Context, namespace, resourceID string) (*UserAssignedIdentityMeta, error) {
	uai, err := k8s.GetResource[asomanagedidentity.UserAssignedIdentity](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &UserAssignedIdentityMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get UserAssignedIdentity %s, %s", resourceID, namespace)
	}
	status := utilsazr.ASOConditionsToResourceStatus(uai)

	var principalID string
	var clientID string
	if status.GetCode() == pbresource.StatusCode_READY {
		if uai.Status.PrincipalId == nil {
			logger.FromCtx(ctx).Errorf("principle id is nil in a ready user assigned identity %v", uai)
			return nil, eris.Wrapf(err, "principle id is nil in a ready user assigned identity %v", uai)
		}
		principalID = *uai.Status.PrincipalId
		if uai.Status.ClientId == nil {
			logger.FromCtx(ctx).Errorf("client id is nil in a ready user assigned identity %v", uai)
			return nil, eris.Wrapf(err, "client id is nil in a ready user assigned identity %v", uai)
		}
		clientID = *uai.Status.ClientId
	}

	return &UserAssignedIdentityMeta{
		Status:      status,
		PrincipalID: principalID,
		ClientID:    clientID,
	}, nil
}

func (provider *Provider) DeleteUserAssignedIdentity(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[asomanagedidentity.UserAssignedIdentity](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("user assigned identity %s not found, ns: %s", resourceID, namespace).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete user assigned identity %s, ns: %s", resourceID, namespace)
	}
	return nil
}

func (provider *Provider) CreateFederatedIdentityCredential(ctx context.Context, opt CreateFederatedIdentityCredentialOption) error {
	fic := &asomanagedidentity.FederatedIdentityCredential{
		ObjectMeta: metav1.ObjectMeta{
			Name:      opt.ResourceID,
			Namespace: opt.Namespace,
		},
		Spec: asomanagedidentity.FederatedIdentityCredential_Spec{
			Owner: &genruntime.KnownResourceReference{
				Name: opt.UserAssignedIdentityName,
			},
			Audiences: []string{FederatedIdentityCredentialAudience},
			Issuer:    &opt.OIDCIssuer,
			Subject:   utils.Ptr(fmt.Sprintf("system:serviceaccount:%s:%s", opt.ServiceAccount.Namespace, opt.ServiceAccount.Name)),
		},
	}

	err := provider.kc.Create(ctx, fic)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("federated identity credential %s already exists", opt.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create FederatedIdentityCredential")
	}

	return nil
}

func (provider *Provider) GetFederatedIdentityCredential(ctx context.Context, namespace, resourceID string) (*FederatedIdentityCredentialMeta, error) {
	fic, err := k8s.GetResource[asomanagedidentity.FederatedIdentityCredential](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &FederatedIdentityCredentialMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get FederatedIdentityCredential %s, %s", resourceID, namespace)
	}

	return &FederatedIdentityCredentialMeta{
		Status: utilsazr.ASOConditionsToResourceStatus(fic),
	}, nil
}

func (provider *Provider) DeleteFederatedIdentityCredential(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[asomanagedidentity.FederatedIdentityCredential](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("federated identity credential %s not found, ns: %s", resourceID, namespace).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete federated identity credential %s, ns: %s", resourceID, namespace)
	}
	return nil
}

func (provider *Provider) CreateRoleAssignment(ctx context.Context, opt CreateRoleAssignmentOption) error {
	blobAccessOption, ok := opt.AccessOption.(BlobAccessOption)
	if !ok {
		return eris.Errorf("invalid access option %v", opt.AccessOption).WithCode(eris.CodeInvalidArgument)
	}
	roleSpec, err := fromBlobAccessOption(blobAccessOption, opt.SubscriptionID)
	if err != nil {
		return eris.Errorf("failed to generate role spec from blob access option %v", blobAccessOption).WithCode(eris.CodeInvalidArgument)
	}
	rs := &asoauthorization.RoleAssignment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      opt.ResourceID,
			Namespace: opt.Namespace,
		},
		Spec: asoauthorization.RoleAssignment_Spec{
			Owner: &genruntime.ArbitraryOwnerReference{
				ARMID: getSubsriptionGroupARMID(opt.SubscriptionID),
			},
			PrincipalId: &opt.PrincipleID,
			// We currently only support rol assignment for user assigned identity.
			PrincipalType: utils.Ptr(asoauthorization.RoleAssignmentProperties_PrincipalType_ServicePrincipal),
			RoleDefinitionReference: &genruntime.ResourceReference{
				ARMID: roleSpec.armID,
			},
			Condition:        &roleSpec.condition,
			ConditionVersion: utils.Ptr(roleAssigmentConditionVersion),
		},
	}

	if err = provider.kc.Create(ctx, rs); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("role assignment %s already exists", opt.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create role assignment, option %v", opt)
	}

	return nil
}

func (provider *Provider) GetRoleAssignment(ctx context.Context, namespace, resourceID string) (*RoleAssignmentMeta, error) {
	rs, err := k8s.GetResource[asoauthorization.RoleAssignment](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &RoleAssignmentMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get role assignment %s, %s", resourceID, namespace)
	}

	return &RoleAssignmentMeta{
		Status: utilsazr.ASOConditionsToResourceStatus(rs),
	}, nil
}

func (provider *Provider) DeleteRoleAssignment(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[asoauthorization.RoleAssignment](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("role assignment %s not found, ns: %s", resourceID, namespace).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete role assignment %s, ns: %s", resourceID, namespace)
	}
	return nil
}

type roleSpec struct {
	armID     string
	condition string
}

const (
	// https://azure.github.io/azure-service-operator/reference/authorization/v1api20220401/#authorization.azure.com/v1api20220401.RoleAssignment
	roleAssigmentConditionVersion = "2.0"
	// https://learn.microsoft.com/en-us/azure/role-based-access-control/built-in-roles/storage#storage-blob-data-owner
	blobDataOwnerARMIDTemplate = "/subscriptions/%s/providers/Microsoft.Authorization/roleDefinitions/b7e6dc6d-f1e8-4753-8033-0f276bb0955b"
	blobPathCondTemplate       = "@Resource[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:path] StringStartsWith '%s/'"
	blobPrefixCondTemplate     = "@Request[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:prefix] StringStartsWith '%s/'"
	// Reference: https://learn.microsoft.com/en-us/azure/storage/blobs/storage-auth-abac-attributes
	blobDataOwnerCondTemplate = `(
	(
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/filter/action'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/tags/read'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/delete'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'} AND NOT SubOperationMatches{'Blob.List'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/write'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/add/action'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/runAsSuperUser/action'})
	)
	OR
	(
		@Resource[Microsoft.Storage/storageAccounts:name] StringEquals '%s'
		AND
		@Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringEquals '%s'
		AND
		(
%s
		)
	)
)
AND
(
	(
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'} AND SubOperationMatches{'Blob.List'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/runAsSuperUser/action'})
	)
	OR
	(
		@Resource[Microsoft.Storage/storageAccounts:name] StringEquals '%s'
		AND
		@Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringEquals '%s'
		AND
		(
%s
		)
	)
)`
)

func fromBlobAccessOption(opt BlobAccessOption, subscriptionID string) (roleSpec, error) {
	if len(opt.Dirs) == 0 {
		return roleSpec{}, eris.Errorf("invalid blob access option, empty dir list %v", opt)
	}
	var pathConditions []string
	var prefixConditions []string
	for _, dir := range opt.Dirs {
		pathConditions = append(pathConditions, fmt.Sprintf(blobPathCondTemplate, dir))
		prefixConditions = append(prefixConditions, fmt.Sprintf(blobPrefixCondTemplate, dir))
	}
	return roleSpec{
		armID:     fmt.Sprintf(blobDataOwnerARMIDTemplate, subscriptionID),
		condition: fmt.Sprintf(blobDataOwnerCondTemplate, opt.StorageAccount, opt.Container, strings.Join(pathConditions, "\nOR\n"), opt.StorageAccount, opt.Container, strings.Join(prefixConditions, "\nOR\n")),
	}, nil
}
