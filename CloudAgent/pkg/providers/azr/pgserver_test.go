package azr

import (
	"context"
	"encoding/json"
	"testing"

	azrpg "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresqlflexibleservers/v4"
	asodb4pg "github.com/Azure/azure-service-operator/v2/api/dbforpostgresql/v1api20221201"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbazr "github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func generateSpec(modifiers ...func(spec *pbazr.PGServerSpec)) *pbazr.PGServerSpec {
	proto := &pbazr.PGServerSpec{
		AzureName: "test-pgserver",
		Owner: &pbazr.ResourceReference{
			ArmId: "/subscriptions/test-subscription/resourceGroups/test-rg",
		},
		Version:  "14",
		Location: "eastus",
		Sku: &pbazr.PGServerSku{
			Name: "Standard_B2s",
			Tier: "Burstable",
		},
		Storage: &pbazr.PGServerStorage{
			StorageSizeGb: 32,
		},
		AdministratorLogin: "testuser",
		AdministratorLoginPassword: &pbazr.SecretKeyRef{
			Key:  "password",
			Name: "test-secret",
		},
		Network: &pbazr.PGServerNetwork{
			DelegatedSubnet: &pbazr.ResourceReference{
				ArmId: "/subscriptions/test-subscription/resourceGroups/test-rg/providers/Microsoft.Network/virtualNetworks/test-vnet/subnets/test-subnet",
			},
			PrivateDnsZone: &pbazr.ResourceReference{
				ArmId: "/subscriptions/test-subscription/resourceGroups/test-rg/providers/Microsoft.Network/privateDnsZones/test-private-dns-zone",
			},
		},
		Tags: map[string]string{
			"key1": "value1",
			"key2": "value2",
		},
	}
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func genPGServer(modifiers ...func(pgServer *asodb4pg.FlexibleServer)) *asodb4pg.FlexibleServer {
	spec, err := conversion.FromPGServerSpecProto(generateSpec())
	if err != nil {
		panic(err)
	}
	pgServer := &asodb4pg.FlexibleServer{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pgserver",
			Namespace: "test-namespace",
		},
		Spec: spec,
	}
	for _, m := range modifiers {
		m(pgServer)
	}
	return pgServer
}

func TestProvider_CreatePGServer(t *testing.T) {
	tests := []struct {
		name          string
		option        CreatePGServerOption
		initObjs      []k8sclient.Object
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "normal case",
			option: CreatePGServerOption{
				ResourceID:     "test-pgserver",
				Namespace:      "test-namespace",
				Spec:           generateSpec(),
				Location:       "eastus",
				SubscriptionID: "test-subscription",
				ResourceGroup:  "test-rg",
			},
			wantErr: false,
		},
		{
			name: "already exist",
			initObjs: []k8sclient.Object{
				genPGServer(),
			},
			option: CreatePGServerOption{
				ResourceID:     "test-pgserver",
				Namespace:      "test-namespace",
				Spec:           generateSpec(),
				Location:       "eastus",
				SubscriptionID: "test-subscription",
				ResourceGroup:  "test-rg",
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}

			err := p.CreatePGServer(ctx, tt.option)
			if tt.wantErr {
				require.Error(t, err)
				assert.Equal(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}

			require.NoError(t, err)
			psql, err := k8s.GetResource[asodb4pg.FlexibleServer](ctx, c, "test-pgserver", "test-namespace")
			require.NoError(t, err)

			assert.Equal(t, "test-pgserver", psql.Name)
			assert.Equal(t, "test-namespace", psql.Namespace)

			expectSpec, err := conversion.FromPGServerSpecProto(tt.option.Spec)
			require.NoError(t, err)
			assert.Equal(t, expectSpec, psql.Spec)
			expectedJSON := prettyJSON(t, expectSpec)
			actualJSON := prettyJSON(t, psql.Spec)
			assert.JSONEq(t, expectedJSON, actualJSON)

			err = p.CreatePGServer(ctx, tt.option)
			assert.True(t, utils.IsErrAlreadyExists(err))
		})
	}
}

func TestProvider_DeletePGServer(t *testing.T) {
	ctx := context.Background()
	pgServer := genPGServer()
	c := fake.NewClient(pgServer)
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	err := p.DeletePGServer(ctx, "test-pgserver", "test-namespace")
	require.NoError(t, err)
	err = p.DeletePGServer(ctx, "test-pgserver", "test-namespace")
	assert.True(t, utils.IsErrNotFound(err))

	_, err = k8s.GetResource[asodb4pg.FlexibleServer](ctx, c, "test-pgserver", "test-namespace")
	require.True(t, k8sErrors.IsNotFound(err))
}

func TestProvider_StartPGServer(t *testing.T) {
	tests := []struct {
		name               string
		initObjs           []k8sclient.Object
		mockPGServerClient func(client *MockPGServerClient)
		wantErr            bool
		wantErrorCode      eris.Code
	}{
		{
			name: "not found",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "already exists (starting)",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStarting),
					},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "already exists (ready)",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateReady),
					},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "scheduled",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStopped),
					},
				}, nil)
				c.EXPECT().StartPGServer(gomock.Any(), "test-pgserver").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			pgServerClient := NewMockPGServerClient(ctrl)
			p := &Provider{
				kc:       &k8s.KubernetesClient{Client: c},
				pgClient: pgServerClient,
			}

			if tt.mockPGServerClient != nil {
				tt.mockPGServerClient(pgServerClient)
			}
			err := p.StartPGServer(ctx, "test-pgserver", "test-namespace")
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_StopGServer(t *testing.T) {
	tests := []struct {
		name               string
		initObjs           []k8sclient.Object
		mockPGServerClient func(client *MockPGServerClient)
		wantErr            bool
		wantErrorCode      eris.Code
	}{
		{
			name: "not found",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "already exists (stopping)",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStopping),
					},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "already exists (stopped)",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStopped),
					},
				}, nil)
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "scheduled",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateReady),
					},
				}, nil)
				c.EXPECT().StopPGServer(gomock.Any(), "test-pgserver").Return(nil)
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			pgServerClient := NewMockPGServerClient(ctrl)
			p := &Provider{
				kc:       &k8s.KubernetesClient{Client: c},
				pgClient: pgServerClient,
			}

			if tt.mockPGServerClient != nil {
				tt.mockPGServerClient(pgServerClient)
			}
			err := p.StopPGServer(ctx, "test-pgserver", "test-namespace")
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)
		})
	}
}

func TestProvider_GetPGServer(t *testing.T) {
	tests := []struct {
		name               string
		initObjs           []k8sclient.Object
		mockPGServerClient func(client *MockPGServerClient)
		want               *PGServerMeta
		wantErr            bool
	}{
		{
			name: "not found",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &PGServerMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_FOUND},
			},
		},
		{
			name: "ready",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State:                    utils.Ptr(azrpg.ServerStateReady),
						FullyQualifiedDomainName: utils.Ptr("test.domain"),
					},
				}, nil)
			},
			want: &PGServerMeta{
				Status:      &pbresource.Status{Code: pbresource.StatusCode_READY},
				DomainName:  utils.Ptr("test.domain"),
				ServerState: pbazr.PGServerState_READY,
			},
		},
		{
			name: "stopped",
			mockPGServerClient: func(c *MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State:                    utils.Ptr(azrpg.ServerStateStopped),
						FullyQualifiedDomainName: utils.Ptr("test.domain"),
					},
				}, nil)
			},
			want: &PGServerMeta{
				Status:      &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				DomainName:  utils.Ptr("test.domain"),
				ServerState: pbazr.PGServerState_STOPPED,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			pgServerClient := NewMockPGServerClient(ctrl)
			p := &Provider{
				kc:       &k8s.KubernetesClient{Client: c},
				pgClient: pgServerClient,
			}

			if tt.mockPGServerClient != nil {
				tt.mockPGServerClient(pgServerClient)
			}
			m, err := p.GetPGServer(ctx, "test-pgserver", "test-namespace")
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, m)
		})
	}
}

func prettyJSON(t *testing.T, x any) string {
	j, err := json.MarshalIndent(x, "", "  ")
	assert.NoError(t, err)
	return string(j)
}
