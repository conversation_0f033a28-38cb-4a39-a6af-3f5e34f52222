package azr

import (
	"context"
	"testing"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/cloudagent/pkg/utils/etagcache"
)

func TestGetFile(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	bucketName := "my-bucket"
	directoryName := "existing-directory"
	storageAccountName := "storage-account"

	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != storageAccountName {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", storageAccountName, got)
		}
		return mockAzBlobClient, nil
	}

	blobs := []string{directoryName}
	mockAzBlobClient.EXPECT().ListBlobs(gomock.Any(), bucketName, directoryName).Return(blobs, nil)
	mockAzBlobClient.EXPECT().GetBlobEtag(gomock.Any(), bucketName, directoryName).Return("test", nil)
	mockAzBlobClient.EXPECT().GetBlob(gomock.Any(), bucketName, directoryName).Return([]byte("test"), nil)

	provider := &Provider{
		cache: etagcache.NewCache(),
	}

	content, err := provider.GetFile(context.Background(), storageAccountName, bucketName, directoryName)
	assert.NoError(t, err)
	assert.Equal(t, content, []byte("test"))
}

func TestGetFile_not_found(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	bucketName := "my-bucket"
	directoryName := "existing-directory"
	storageAccountName := "storage-account"

	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != storageAccountName {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", storageAccountName, got)
		}
		return mockAzBlobClient, nil
	}

	mockAzBlobClient.EXPECT().ListBlobs(gomock.Any(), bucketName, directoryName).Return([]string{}, nil)

	provider := &Provider{
		cache: etagcache.NewCache(),
	}

	_, err := provider.GetFile(context.Background(), storageAccountName, bucketName, directoryName)
	assert.Error(t, err)
	assert.Equal(t, eris.CodeNotFound, eris.GetCode(err))
}
