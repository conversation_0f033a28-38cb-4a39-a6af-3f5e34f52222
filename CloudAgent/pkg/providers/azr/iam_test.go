package azr

import (
	"context"
	"fmt"
	"testing"

	asoauthorization "github.com/Azure/azure-service-operator/v2/api/authorization/v1api20220401"
	asomanagedidentity "github.com/Azure/azure-service-operator/v2/api/managedidentity/v1api20230131"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime/conditions"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateUserAssignedIdentity(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client, nil)
	const (
		resourceID         = "resource"
		namespace          = "ns"
		testSubscriptionID = "sid"
		testResourceGroup  = "rg"
		testLocation       = "loc"
	)
	ctx := context.Background()
	option := CreateUserAssignedIdentityOption{
		ResourceID:     resourceID,
		Namespace:      namespace,
		SubscriptionID: testSubscriptionID,
		ResourceGroup:  testResourceGroup,
		Location:       testLocation,
	}
	require.NoError(t, provider.CreateUserAssignedIdentity(ctx, option))

	uai := asomanagedidentity.UserAssignedIdentity{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, provider.kc.Get(ctx, objKey, &uai))
	assert.Equal(t, *uai.Spec.Location, testLocation)
	assert.Equal(t, uai.Spec.Owner.ARMID, fmt.Sprintf("/subscriptions/%s/resourceGroups/%s", testSubscriptionID, testResourceGroup))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateUserAssignedIdentity(ctx, option)))
}

func TestDeleteUserAssignedIdentity(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	uai := asomanagedidentity.UserAssignedIdentity{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&uai)
	provider := CreateFakeProvider(client, nil)
	ctx := context.Background()
	require.NoError(t, provider.DeleteUserAssignedIdentity(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	uai = asomanagedidentity.UserAssignedIdentity{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &uai)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteUserAssignedIdentity(ctx, namespace, resourceID)))
}

func TestGetUserAssignedIdentity(t *testing.T) {
	const (
		resourceID  = "resource"
		namespace   = "ns"
		principalID = "pid"
		clientID    = "cid"
	)
	tests := map[string]struct {
		uai          *asomanagedidentity.UserAssignedIdentity
		expectedMeta *UserAssignedIdentityMeta
		errCode      *eris.Code
	}{
		"Normal case, resource is ready": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.UserAssignedIdentity_STATUS{
					PrincipalId: utils.Ptr(principalID),
					ClientId:    utils.Ptr(clientID),
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &UserAssignedIdentityMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PrincipalID: principalID,
				ClientID:    clientID,
			},
		},
		"Normal case, resource is NOT ready": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.UserAssignedIdentity_STATUS{},
			},
			expectedMeta: &UserAssignedIdentityMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
			},
		},
		"Normal case, resource is in error state": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.UserAssignedIdentity_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			expectedMeta: &UserAssignedIdentityMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_ERROR,
				},
			},
		},
		"Normal case, resource is not found": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			expectedMeta: &UserAssignedIdentityMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			client := fake.NewClient(tt.uai)
			provider := CreateFakeProvider(client, nil)

			meta, err := provider.GetUserAssignedIdentity(context.Background(), namespace, resourceID)
			if tt.errCode != nil {
				require.Error(t, err, "expect error to be thrown for test %v", tt)
				assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
			} else {
				require.NoError(t, err, "expect no error for test %v", tt)
				assert.Equal(t, meta.Status.GetCode(), tt.expectedMeta.Status.GetCode(), "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
				assert.Equal(t, meta.PrincipalID, tt.expectedMeta.PrincipalID)
				assert.Equal(t, meta.ClientID, tt.expectedMeta.ClientID)
			}
		})
	}
}

func TestCreateFederatedIdentityCredential(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client, nil)
	const (
		resourceID            = "resource"
		namespace             = "ns"
		testSubscriptionID    = "sid"
		testResourceGroup     = "rg"
		testLocation          = "loc"
		uaiName               = "uai"
		oidcIssuer            = "oidc"
		k8sServiceAccountName = "sa"
	)
	ctx := context.Background()
	option := CreateFederatedIdentityCredentialOption{
		ResourceID:               resourceID,
		Namespace:                namespace,
		UserAssignedIdentityName: uaiName,
		OIDCIssuer:               oidcIssuer,
		ServiceAccount: KubernetesServiceAccount{
			Name:      k8sServiceAccountName,
			Namespace: namespace,
		},
	}
	require.NoError(t, provider.CreateFederatedIdentityCredential(ctx, option))

	fic := asomanagedidentity.FederatedIdentityCredential{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, provider.kc.Get(ctx, objKey, &fic))
	assert.Equal(t, fic.Spec.Owner.Name, uaiName)
	assert.Equal(t, fic.Spec.Audiences, []string{FederatedIdentityCredentialAudience})
	assert.Equal(t, *fic.Spec.Issuer, oidcIssuer)
	assert.Equal(t, *fic.Spec.Subject, fmt.Sprintf("system:serviceaccount:%s:%s", namespace, k8sServiceAccountName))
	assert.True(t, utils.IsErrAlreadyExists(provider.CreateFederatedIdentityCredential(ctx, option)))
}

func TestDeleteFederatedIdentityCredential(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	fic := asomanagedidentity.FederatedIdentityCredential{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&fic)
	provider := CreateFakeProvider(client, nil)
	ctx := context.Background()
	require.NoError(t, provider.DeleteFederatedIdentityCredential(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	fic = asomanagedidentity.FederatedIdentityCredential{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &fic)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteFederatedIdentityCredential(ctx, namespace, resourceID)))
}

func TestGetFederatedIdentityCredential(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	tests := map[string]struct {
		fic          *asomanagedidentity.FederatedIdentityCredential
		expectedMeta *FederatedIdentityCredentialMeta
		errCode      *eris.Code
	}{
		"Normal case, resource is ready": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.FederatedIdentityCredential_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &FederatedIdentityCredentialMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
			},
		},
		"Normal case, resource is NOT ready": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.FederatedIdentityCredential_STATUS{},
			},
			expectedMeta: &FederatedIdentityCredentialMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
			},
		},
		"Normal case, resource is in error state": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.FederatedIdentityCredential_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			expectedMeta: &FederatedIdentityCredentialMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_ERROR,
				},
			},
		},
		"Normal case, resource is not found": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			expectedMeta: &FederatedIdentityCredentialMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			client := fake.NewClient(tt.fic)
			provider := CreateFakeProvider(client, nil)

			meta, err := provider.GetFederatedIdentityCredential(context.Background(), namespace, resourceID)
			if tt.errCode != nil {
				require.Error(t, err, "expect error to be thrown for test %v", tt)
				assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
			} else {
				require.NoError(t, err, "expect no error for test %v", tt)
				assert.Equal(t, meta.Status.GetCode(), tt.expectedMeta.Status.GetCode(), "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
			}
		})
	}
}

func TestCreateRoleAssignment(t *testing.T) {
	const (
		resourceID     = "resource"
		namespace      = "ns"
		subscriptionID = "sid"
		principleID    = "pid"
		storageAccount = "sa"
		container      = "container"
		dir1           = "dir1"
		dir2           = "dir2"
	)
	opt := CreateRoleAssignmentOption{
		Namespace:      namespace,
		ResourceID:     resourceID,
		SubscriptionID: subscriptionID,
		PrincipleID:    principleID,
		AccessOption: BlobAccessOption{
			StorageAccount: storageAccount,
			Container:      container,
			Dirs:           []string{dir1, dir2},
		},
	}

	expectedOwnerARMID := "/subscriptions/sid"
	expectedPrincipalID := principleID
	expectedRoleARMID := "/subscriptions/sid/providers/Microsoft.Authorization/roleDefinitions/b7e6dc6d-f1e8-4753-8033-0f276bb0955b"
	expectedCondition := `(
	(
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/filter/action'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/tags/read'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/delete'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'} AND NOT SubOperationMatches{'Blob.List'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/write'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/add/action'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/runAsSuperUser/action'})
	)
	OR
	(
		@Resource[Microsoft.Storage/storageAccounts:name] StringEquals 'sa'
		AND
		@Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringEquals 'container'
		AND
		(
@Resource[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:path] StringStartsWith 'dir1/'
OR
@Resource[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:path] StringStartsWith 'dir2/'
		)
	)
)
AND
(
	(
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'} AND SubOperationMatches{'Blob.List'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/runAsSuperUser/action'})
	)
	OR
	(
		@Resource[Microsoft.Storage/storageAccounts:name] StringEquals 'sa'
		AND
		@Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringEquals 'container'
		AND
		(
@Request[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:prefix] StringStartsWith 'dir1/'
OR
@Request[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:prefix] StringStartsWith 'dir2/'
		)
	)
)`
	client := fake.NewClient()
	provider := CreateFakeProvider(client, nil)

	err := provider.CreateRoleAssignment(context.Background(), opt)
	require.NoError(t, err, "expect no error for test %v")

	rs := asoauthorization.RoleAssignment{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	ctx := context.Background()
	require.NoError(t, provider.kc.Get(ctx, objKey, &rs))

	assert.Equal(t, rs.Spec.Owner.ARMID, expectedOwnerARMID)
	assert.Equal(t, *rs.Spec.PrincipalId, expectedPrincipalID)
	assert.Equal(t, *rs.Spec.PrincipalType, asoauthorization.RoleAssignmentProperties_PrincipalType_ServicePrincipal)
	assert.Equal(t, rs.Spec.RoleDefinitionReference.ARMID, expectedRoleARMID)
	assert.Equal(t, *rs.Spec.Condition, expectedCondition)
	assert.Equal(t, *rs.Spec.ConditionVersion, "2.0")
}

func TestDeleteRoleAssignment(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	fic := asoauthorization.RoleAssignment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&fic)
	provider := CreateFakeProvider(client, nil)
	ctx := context.Background()
	require.NoError(t, provider.DeleteRoleAssignment(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	fic = asoauthorization.RoleAssignment{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &fic)))
	assert.True(t, utils.IsErrNotFound(provider.DeleteFederatedIdentityCredential(ctx, namespace, resourceID)))
}

func TestGetRoleAssignment(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	tests := map[string]struct {
		rs           *asoauthorization.RoleAssignment
		expectedMeta *RoleAssignmentMeta
		errCode      *eris.Code
	}{
		"Normal case, resource is ready": {
			rs: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asoauthorization.RoleAssignment_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
				},
			},
			expectedMeta: &RoleAssignmentMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
			},
		},
		"Normal case, resource is NOT ready": {
			rs: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asoauthorization.RoleAssignment_STATUS{},
			},
			expectedMeta: &RoleAssignmentMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
			},
		},
		"Normal case, resource is in error state": {
			rs: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asoauthorization.RoleAssignment_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			expectedMeta: &RoleAssignmentMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_ERROR,
				},
			},
		},
		"Normal case, resource is not found": {
			rs: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			expectedMeta: &RoleAssignmentMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			client := fake.NewClient(tt.rs)
			provider := CreateFakeProvider(client, nil)

			meta, err := provider.GetRoleAssignment(context.Background(), namespace, resourceID)
			if tt.errCode != nil {
				require.Error(t, err, "expect error to be thrown for test %v", tt)
				assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
			} else {
				require.NoError(t, err, "expect no error for test %v", tt)
				assert.Equal(t, meta.Status.GetCode(), tt.expectedMeta.Status.GetCode(), "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
			}
		})
	}
}
