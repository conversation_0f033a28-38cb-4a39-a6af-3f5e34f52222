package azr

import (
	"context"
	"fmt"
	"maps"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	asonetwork "github.com/Azure/azure-service-operator/v2/api/network/v1api20220701"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbazrsvc "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilsazr "github.com/risingwavelabs/cloudagent/pkg/utils/azr"
)

type CreatePrivateEndpointOption struct {
	Namespace            string
	ExtraTags            map[string]string
	ResourceID           string
	PrivateLinkServiceID string
	PrivateLinkSubnetID  string
	SubscriptionID       string
	ResourceGroup        string
	PrivateIP            string
	Location             string
}

type PrivateEndpointMeta struct {
	Status                *pbresource.Status
	PrivateEndpointStatus pbazrsvc.PrivateEndpointStatus
	PrivateEndpointIP     string
}

const (
	privateEndpointConfigMapNamePrefix = "peip-"
	privateEndpointConfigMapKey        = "ip"
)

func (provider *Provider) CreatePrivateEndpoint(ctx context.Context, option CreatePrivateEndpointOption) error {
	tags := map[string]string{
		utils.TagProjectKey: utils.TagProjectValue,
	}
	maps.Copy(tags, option.ExtraTags)
	privateEndpoint := &asonetwork.PrivateEndpoint{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: asonetwork.PrivateEndpoint_Spec{
			AzureName: option.ResourceID,
			Subnet: &asonetwork.Subnet_PrivateEndpoint_SubResourceEmbedded{
				Reference: &genruntime.ResourceReference{
					ARMID: option.PrivateLinkSubnetID,
				},
			},
			Owner: &genruntime.KnownResourceReference{
				ARMID: getResourceGroupARMID(option.SubscriptionID, option.ResourceGroup),
			},
			ManualPrivateLinkServiceConnections: []asonetwork.PrivateLinkServiceConnection{
				{
					Name: &option.ResourceID,
					PrivateLinkServiceReference: &genruntime.ResourceReference{
						ARMID: option.PrivateLinkServiceID,
					},
				},
			},
			Location: utils.Ptr(option.Location),
			OperatorSpec: &asonetwork.PrivateEndpointOperatorSpec{
				ConfigMaps: &asonetwork.PrivateEndpointOperatorConfigMaps{
					PrimaryNicPrivateIpAddress: &genruntime.ConfigMapDestination{
						Name: fmt.Sprintf("%s%s", privateEndpointConfigMapNamePrefix, option.ResourceID),
						Key:  privateEndpointConfigMapKey,
					},
				},
			},
			Tags: tags,
		},
	}

	err := provider.kc.Create(ctx, privateEndpoint)
	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("private endpoint %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrap(err, "failed to create private endpoint")
	}

	return nil
}

func (provider *Provider) DeletePrivateEndpoint(ctx context.Context, namespace, resourceID string) error {
	if err := k8s.DeleteResource[asonetwork.PrivateEndpoint](ctx, provider.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("private endpoint %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete private endpoint %s", resourceID)
	}
	return nil
}

func (provider *Provider) GetPrivateEndpoint(ctx context.Context, namespace, resourceID string) (*PrivateEndpointMeta, error) {
	privateEndpoint, err := k8s.GetResource[asonetwork.PrivateEndpoint](ctx, provider.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &PrivateEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to get private endpoint %s", resourceID)
	}

	status := utilsazr.ASOConditionsToResourceStatus(privateEndpoint)
	if status.GetCode() == pbresource.StatusCode_ERROR {
		return &PrivateEndpointMeta{
			Status: &pbresource.Status{
				Code:    status.GetCode(),
				Message: fmt.Sprintf("resource in error state %v", status.GetMessage()),
			},
		}, nil
	}

	if privateEndpoint.Status.ProvisioningState == nil {
		if status.GetCode() == pbresource.StatusCode_READY {
			logger.FromCtx(ctx).Errorf("provisioning state is nil in a ready private endpoint %v", privateEndpoint)
			return nil, eris.New(fmt.Sprintf("provisioning state is nil in a ready private endpoint %v", privateEndpoint))
		}

		return &PrivateEndpointMeta{
			Status: &pbresource.Status{
				Code:    status.GetCode(),
				Message: fmt.Sprintf("provisioning state is nil: %s", status.GetMessage()),
			},
		}, nil
	}

	meta := &PrivateEndpointMeta{
		Status:                status,
		PrivateEndpointStatus: utilsazr.PrivateEndpointStatusToProto(*privateEndpoint.Status.ProvisioningState),
	}

	cmName := fmt.Sprintf("%s%s", privateEndpointConfigMapNamePrefix, resourceID)
	ipConfigMap, err := k8s.GetResource[corev1.ConfigMap](ctx, provider.kc, cmName, namespace)

	if err != nil {
		if k8sErrors.IsNotFound(err) && status.GetCode() != pbresource.StatusCode_READY {
			return meta, nil
		}
		return nil, eris.Wrapf(err, "failed to retrieve ip config map %s in a ready private endpoint %v", cmName, privateEndpoint)
	}
	ip, ok := ipConfigMap.Data[privateEndpointConfigMapKey]
	if !ok {
		return nil, eris.Errorf("key %s is missing in  config map %s", privateEndpointConfigMapKey, cmName)
	}

	meta.PrivateEndpointIP = ip

	return meta, nil
}
