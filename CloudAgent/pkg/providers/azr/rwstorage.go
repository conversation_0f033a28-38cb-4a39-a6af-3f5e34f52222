package azr

import (
	"context"

	"github.com/risingwavelabs/eris"
)

func (provider *Provider) GetFile(ctx context.Context, storageAccount, containerName, blobName string) ([]byte, error) {
	azBlobClient, err := createAzBlobClient(provider.sdkCred, storageAccount)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to create az blob client for %s/%s", storageAccount, containerName)
	}
	blobs, err := azBlobClient.ListBlobs(ctx, containerName, blobName)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to list blobs for %s/%s", containerName, blobName)
	}
	if len(blobs) == 0 {
		return nil, eris.WithCode(eris.New("file not found"), eris.CodeNotFound)
	}

	etag, err := azBlobClient.GetBlobEtag(ctx, containerName, blobName)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get etag for %s/%s", containerName, blobName)
	}

	val, ok := provider.cache.Get(blobName, etag)
	if ok {
		return val, nil
	}

	manifest, err := azBlobClient.GetBlob(ctx, containerName, blobName)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get manifest for %s/%s", containerName, blobName)
	}

	provider.cache.Set(blobName, manifest, etag)
	return manifest, nil
}
