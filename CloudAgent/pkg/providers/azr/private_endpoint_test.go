package azr

import (
	"context"
	"fmt"
	"testing"

	asonetwork "github.com/Azure/azure-service-operator/v2/api/network/v1api20220701"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime"
	"go.uber.org/mock/gomock"

	"github.com/Azure/azure-service-operator/v2/pkg/genruntime/conditions"
	"github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/k8s/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbazrsvc "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreatePrivateEndpoint(t *testing.T) {
	client := fake.NewClient()
	provider := CreateFakeProvider(client, nil)
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	ctx := context.Background()
	option := CreatePrivateEndpointOption{
		ResourceID:           resourceID,
		Namespace:            namespace,
		ExtraTags:            map[string]string{"envid": "env"},
		SubscriptionID:       "subscription-id",
		PrivateLinkServiceID: "test-target",
		PrivateLinkSubnetID:  "Private-Endpoint-ip",
	}
	require.NoError(t, provider.CreatePrivateEndpoint(ctx, option))
	psc := asonetwork.PrivateEndpoint{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, provider.kc.Get(ctx, objKey, &psc))
	assert.Equal(t, psc.Spec, asonetwork.PrivateEndpoint_Spec{
		AzureName: option.ResourceID,
		Subnet: &asonetwork.Subnet_PrivateEndpoint_SubResourceEmbedded{
			Reference: &genruntime.ResourceReference{
				ARMID: option.PrivateLinkSubnetID,
			},
		},
		Owner: &genruntime.KnownResourceReference{
			ARMID: getResourceGroupARMID(option.SubscriptionID, option.ResourceGroup),
		},
		ManualPrivateLinkServiceConnections: []asonetwork.PrivateLinkServiceConnection{
			{
				Name: &option.ResourceID,
				PrivateLinkServiceReference: &genruntime.ResourceReference{
					ARMID: option.PrivateLinkServiceID,
				},
			},
		},
		Location: utils.Ptr(option.Location),
		OperatorSpec: &asonetwork.PrivateEndpointOperatorSpec{
			ConfigMaps: &asonetwork.PrivateEndpointOperatorConfigMaps{
				PrimaryNicPrivateIpAddress: &genruntime.ConfigMapDestination{
					Name: fmt.Sprintf("%s%s", privateEndpointConfigMapNamePrefix, option.ResourceID),
					Key:  privateEndpointConfigMapKey,
				},
			},
		},
		Tags: map[string]string{
			"project": "risingwave",
			"envid":   "env",
		},
	})
	assert.True(t, utils.IsErrAlreadyExists(provider.CreatePrivateEndpoint(ctx, option)))
}

func TestDeletePrivateEndpoint(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	psc := asonetwork.PrivateEndpoint{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&psc)
	provider := CreateFakeProvider(client, nil)
	ctx := context.Background()
	require.NoError(t, provider.DeletePrivateEndpoint(ctx, namespace, resourceID))
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	psc = asonetwork.PrivateEndpoint{}
	assert.True(t, k8sErrors.IsNotFound(provider.kc.Get(ctx, objKey, &psc)))
	assert.True(t, utils.IsErrNotFound(provider.DeletePrivateEndpoint(ctx, namespace, resourceID)))
}

func TestGetPrivateEndpoint(t *testing.T) {
	const (
		resourceID           = "resource"
		namespace            = "ns"
		nicID                = "nicID"
		resourceGroup        = "resourceGroup"
		privateIP            = "10.0.0.0"
		SubscriptionID       = "11111111-1111-1111-1111-111111111111"
		PrivateLinkServiceID = "test-target"
		PrivateLinkSubnetID  = "Private-Endpoint-ip"
	)
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	tests := []struct {
		description     string
		privateEndpoint *asonetwork.PrivateEndpoint
		configMap       *corev1.ConfigMap
		expectedMeta    *PrivateEndpointMeta
		errCode         *eris.Code
	}{
		{
			description: "Normal case, resource is ready",
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{
					Conditions: []conditions.Condition{
						{
							Type:   v1alpha1.ReadyConditionType,
							Status: metav1.ConditionTrue,
						},
					},
					NetworkInterfaces: []asonetwork.NetworkInterface_STATUS_PrivateEndpoint_SubResourceEmbedded{
						{
							Id: utils.Ptr(nicID),
						},
					},
					ProvisioningState: utils.Ptr(asonetwork.ApplicationGatewayProvisioningState_STATUS_Succeeded)},
			},
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      fmt.Sprintf("%s%s", privateEndpointConfigMapNamePrefix, resourceID),
				},
				Data: map[string]string{
					privateEndpointConfigMapKey: privateIP,
				},
			},
			expectedMeta: &PrivateEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PrivateEndpointStatus: pbazrsvc.PrivateEndpointStatus_SUCCEEDED,
				PrivateEndpointIP:     privateIP,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{
					Conditions: []conditions.Condition{
						{
							Type:   "NotReady",
							Status: metav1.ConditionTrue,
						},
					},
					NetworkInterfaces: []asonetwork.NetworkInterface_STATUS_PrivateEndpoint_SubResourceEmbedded{
						{
							Id: utils.Ptr(nicID),
						},
					},
					ProvisioningState: utils.Ptr(asonetwork.ApplicationGatewayProvisioningState_STATUS_Succeeded)},
			},
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      fmt.Sprintf("%s%s", privateEndpointConfigMapNamePrefix, resourceID),
				},
				Data: map[string]string{
					privateEndpointConfigMapKey: privateIP,
				},
			},
			expectedMeta: &PrivateEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				PrivateEndpointStatus: pbazrsvc.PrivateEndpointStatus_SUCCEEDED,
				PrivateEndpointIP:     privateIP,
			},
		},
		{
			description: "Normal case, resource is in failed connection state",
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{
					Conditions: []conditions.Condition{
						{
							Type:   "NotReady",
							Status: metav1.ConditionTrue,
						},
					},
					NetworkInterfaces: []asonetwork.NetworkInterface_STATUS_PrivateEndpoint_SubResourceEmbedded{
						{
							Id: utils.Ptr(nicID),
						},
					},
					ProvisioningState: utils.Ptr(asonetwork.ApplicationGatewayProvisioningState_STATUS_Failed)},
			},
			expectedMeta: &PrivateEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				PrivateEndpointStatus: pbazrsvc.PrivateEndpointStatus_FAILED,
			},
		},
		{
			description: "Normal case, resource is in error state",
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			expectedMeta: &PrivateEndpointMeta{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_ERROR,
					Message: fmt.Sprintf("resource in error state %v", "Condition [Ready], Status = \"False\", ObservedGeneration = 0, Severity = \"Error\", Reason = \"\", Message = \"\", LastTransitionTime = \"0001-01-01 00:00:00 +0000 UTC\""),
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "random namespace",
					Name:      "random id",
				},
			},
			expectedMeta: &PrivateEndpointMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.description, func(t *testing.T) {
			objs := []k8sclient.Object{tt.privateEndpoint}
			if tt.configMap != nil {
				objs = append(objs, tt.configMap)
			}
			client := fake.NewClient(objs...)
			provider := CreateFakeProvider(client, nil)
			meta, err := provider.GetPrivateEndpoint(context.Background(), namespace, resourceID)
			if tt.errCode != nil {
				require.Error(t, err, "expect error to be thrown for test %v", tt)
				assert.Equal(t, eris.GetCode(err), *tt.errCode, "unexpected err code, get %v, want %v", eris.GetCode(err), *tt.errCode)
			} else {
				require.NoError(t, err, "expect no error for test %v", tt)
				assert.Equal(t, meta, tt.expectedMeta, "unexpected result for test %v, get %v, want %v", tt, meta, tt.expectedMeta)
			}
		})
	}
}
