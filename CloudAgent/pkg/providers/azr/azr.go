package azr

import (
	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/risingwavelabs/eris"

	pbcfgazr "github.com/risingwavelabs/cloudagent/pbgen/config/azr"

	rbacv1 "k8s.io/api/rbac/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/utils/etagcache"
)

type Provider struct {
	kc *k8s.KubernetesClient

	sdkCred  azcore.TokenCredential
	pgClient PGServerClient

	cache *etagcache.Cache
}

type NewProviderOption struct {
	Kc        *k8s.KubernetesClient
	AZRConfig *pbcfgazr.Config
	PGClient  PGServerClient
}

func NewProvider(option NewProviderOption) (*Provider, error) {
	if option.Kc == nil {
		return nil, eris.New("Kubernetes client cannot be nil")
	}

	sdkCred, err := initSDKCred(option.AZRConfig)
	if err != nil {
		return nil, eris.Wrap(err, "unable to load AZR SDK credentials")
	}
	client, err := newPGServerClient(sdkCred, option.AZRConfig.GetSubscriptionId(), option.AZRConfig.GetResourceGroup())
	if err != nil {
		return nil, eris.Wrap(err, "failed to create PGServerClient")
	}

	return &Provider{
		kc:       option.Kc,
		sdkCred:  sdkCred,
		pgClient: client,
		cache:    etagcache.NewCache(),
	}, nil
}

// for testing purposes only.
func CreateFakeProvider(client k8sclient.Client, pgServerClient PGServerClient) *Provider {
	k8sClient := &k8s.KubernetesClient{
		Client:    client,
		Interface: fake.NewClientset(&rbacv1.ClusterRole{}),

		TaskConfig: taskconfig.Config{
			Image:          "image",
			ServiceAccount: "serviceAccount",
			PullPolicy:     "Always",
		},
	}
	return &Provider{
		kc:       k8sClient,
		pgClient: pgServerClient,
	}
}
