// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/azr (interfaces: AzBlobClientInterface,AzNicClientInterface,PGServerClient)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/azr -package=azr -destination=pkg/providers/azr/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/azr AzBlobClientInterface,AzNicClientInterface,PGServerClient
//

// Package azr is a generated GoMock package.
package azr

import (
	context "context"
	reflect "reflect"

	armpostgresqlflexibleservers "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresqlflexibleservers/v4"
	gomock "go.uber.org/mock/gomock"
)

// MockAzBlobClientInterface is a mock of AzBlobClientInterface interface.
type MockAzBlobClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockAzBlobClientInterfaceMockRecorder
	isgomock struct{}
}

// MockAzBlobClientInterfaceMockRecorder is the mock recorder for MockAzBlobClientInterface.
type MockAzBlobClientInterfaceMockRecorder struct {
	mock *MockAzBlobClientInterface
}

// NewMockAzBlobClientInterface creates a new mock instance.
func NewMockAzBlobClientInterface(ctrl *gomock.Controller) *MockAzBlobClientInterface {
	mock := &MockAzBlobClientInterface{ctrl: ctrl}
	mock.recorder = &MockAzBlobClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAzBlobClientInterface) EXPECT() *MockAzBlobClientInterfaceMockRecorder {
	return m.recorder
}

// CopyObject mocks base method.
func (m *MockAzBlobClientInterface) CopyObject(ctx context.Context, req AZBlobCopyRequest) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CopyObject", ctx, req)
	ret0, _ := ret[0].(error)
	return ret0
}

// CopyObject indicates an expected call of CopyObject.
func (mr *MockAzBlobClientInterfaceMockRecorder) CopyObject(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CopyObject", reflect.TypeOf((*MockAzBlobClientInterface)(nil).CopyObject), ctx, req)
}

// DeleteBlobs mocks base method.
func (m *MockAzBlobClientInterface) DeleteBlobs(ctx context.Context, containerName, blobName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteBlobs", ctx, containerName, blobName)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteBlobs indicates an expected call of DeleteBlobs.
func (mr *MockAzBlobClientInterfaceMockRecorder) DeleteBlobs(ctx, containerName, blobName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteBlobs", reflect.TypeOf((*MockAzBlobClientInterface)(nil).DeleteBlobs), ctx, containerName, blobName)
}

// GetBlob mocks base method.
func (m *MockAzBlobClientInterface) GetBlob(ctx context.Context, containerName, blobName string) ([]byte, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlob", ctx, containerName, blobName)
	ret0, _ := ret[0].([]byte)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlob indicates an expected call of GetBlob.
func (mr *MockAzBlobClientInterfaceMockRecorder) GetBlob(ctx, containerName, blobName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlob", reflect.TypeOf((*MockAzBlobClientInterface)(nil).GetBlob), ctx, containerName, blobName)
}

// GetBlobEtag mocks base method.
func (m *MockAzBlobClientInterface) GetBlobEtag(ctx context.Context, containerName, blobName string) (string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetBlobEtag", ctx, containerName, blobName)
	ret0, _ := ret[0].(string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetBlobEtag indicates an expected call of GetBlobEtag.
func (mr *MockAzBlobClientInterfaceMockRecorder) GetBlobEtag(ctx, containerName, blobName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetBlobEtag", reflect.TypeOf((*MockAzBlobClientInterface)(nil).GetBlobEtag), ctx, containerName, blobName)
}

// ListBlobs mocks base method.
func (m *MockAzBlobClientInterface) ListBlobs(ctx context.Context, containerName, dataDirectory string) ([]string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListBlobs", ctx, containerName, dataDirectory)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListBlobs indicates an expected call of ListBlobs.
func (mr *MockAzBlobClientInterfaceMockRecorder) ListBlobs(ctx, containerName, dataDirectory any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListBlobs", reflect.TypeOf((*MockAzBlobClientInterface)(nil).ListBlobs), ctx, containerName, dataDirectory)
}

// ListObjectsWithMarker mocks base method.
func (m *MockAzBlobClientInterface) ListObjectsWithMarker(ctx context.Context, req AZBlobListRequest) ([]string, string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListObjectsWithMarker", ctx, req)
	ret0, _ := ret[0].([]string)
	ret1, _ := ret[1].(string)
	ret2, _ := ret[2].(error)
	return ret0, ret1, ret2
}

// ListObjectsWithMarker indicates an expected call of ListObjectsWithMarker.
func (mr *MockAzBlobClientInterfaceMockRecorder) ListObjectsWithMarker(ctx, req any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListObjectsWithMarker", reflect.TypeOf((*MockAzBlobClientInterface)(nil).ListObjectsWithMarker), ctx, req)
}

// MockAzNicClientInterface is a mock of AzNicClientInterface interface.
type MockAzNicClientInterface struct {
	ctrl     *gomock.Controller
	recorder *MockAzNicClientInterfaceMockRecorder
	isgomock struct{}
}

// MockAzNicClientInterfaceMockRecorder is the mock recorder for MockAzNicClientInterface.
type MockAzNicClientInterfaceMockRecorder struct {
	mock *MockAzNicClientInterface
}

// NewMockAzNicClientInterface creates a new mock instance.
func NewMockAzNicClientInterface(ctrl *gomock.Controller) *MockAzNicClientInterface {
	mock := &MockAzNicClientInterface{ctrl: ctrl}
	mock.recorder = &MockAzNicClientInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockAzNicClientInterface) EXPECT() *MockAzNicClientInterfaceMockRecorder {
	return m.recorder
}

// getPrivateIPFromNic mocks base method.
func (m *MockAzNicClientInterface) getPrivateIPFromNic(ctx context.Context, nicID, resourceGroup string) (*string, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "getPrivateIPFromNic", ctx, nicID, resourceGroup)
	ret0, _ := ret[0].(*string)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// getPrivateIPFromNic indicates an expected call of getPrivateIPFromNic.
func (mr *MockAzNicClientInterfaceMockRecorder) getPrivateIPFromNic(ctx, nicID, resourceGroup any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "getPrivateIPFromNic", reflect.TypeOf((*MockAzNicClientInterface)(nil).getPrivateIPFromNic), ctx, nicID, resourceGroup)
}

// MockPGServerClient is a mock of PGServerClient interface.
type MockPGServerClient struct {
	ctrl     *gomock.Controller
	recorder *MockPGServerClientMockRecorder
	isgomock struct{}
}

// MockPGServerClientMockRecorder is the mock recorder for MockPGServerClient.
type MockPGServerClientMockRecorder struct {
	mock *MockPGServerClient
}

// NewMockPGServerClient creates a new mock instance.
func NewMockPGServerClient(ctrl *gomock.Controller) *MockPGServerClient {
	mock := &MockPGServerClient{ctrl: ctrl}
	mock.recorder = &MockPGServerClientMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockPGServerClient) EXPECT() *MockPGServerClientMockRecorder {
	return m.recorder
}

// DescribePGServer mocks base method.
func (m *MockPGServerClient) DescribePGServer(ctx context.Context, id string) (*armpostgresqlflexibleservers.Server, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DescribePGServer", ctx, id)
	ret0, _ := ret[0].(*armpostgresqlflexibleservers.Server)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DescribePGServer indicates an expected call of DescribePGServer.
func (mr *MockPGServerClientMockRecorder) DescribePGServer(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DescribePGServer", reflect.TypeOf((*MockPGServerClient)(nil).DescribePGServer), ctx, id)
}

// StartPGServer mocks base method.
func (m *MockPGServerClient) StartPGServer(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartPGServer", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartPGServer indicates an expected call of StartPGServer.
func (mr *MockPGServerClientMockRecorder) StartPGServer(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartPGServer", reflect.TypeOf((*MockPGServerClient)(nil).StartPGServer), ctx, id)
}

// StopPGServer mocks base method.
func (m *MockPGServerClient) StopPGServer(ctx context.Context, id string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopPGServer", ctx, id)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopPGServer indicates an expected call of StopPGServer.
func (mr *MockPGServerClientMockRecorder) StopPGServer(ctx, id any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopPGServer", reflect.TypeOf((*MockPGServerClient)(nil).StopPGServer), ctx, id)
}
