package azr

import (
	"context"
	"testing"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"
)

func TestDeleteDataDirectoryTask_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	provider := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	opt := DirectoryOptions{
		TaskID:             "existing-directory",
		BucketName:         "my-bucket",
		DirectoryName:      "existing-directory",
		StorageAccountName: "storage-account",
	}
	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != opt.StorageAccountName {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", opt.StorageAccountName, got)
		}
		return mockAzBlobClient, nil
	}

	dirSuffix := opt.DirectoryName + "/"
	mockAzBlobClient.EXPECT().ListBlobs(gomock.Any(), opt.BucketName, dirSuffix).Return([]string{opt.DirectoryName, opt.DirectoryName}, nil).AnyTimes()
	mockAzBlobClient.EXPECT().DeleteBlobs(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := provider.DeleteDataDirectory(context.Background(), opt.StorageAccountName, opt.BucketName, opt.DirectoryName)
	assert.NoError(t, err, "Expected no error")
}

func TestDeleteEmptyDataDirectoryTask_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	provider := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	opt := DirectoryOptions{
		TaskID:             "empty-directory",
		BucketName:         "my-bucket",
		DirectoryName:      "emtpy-directory",
		StorageAccountName: "storage-account",
	}

	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != opt.StorageAccountName {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", opt.StorageAccountName, got)
		}
		return mockAzBlobClient, nil
	}

	dirSuffix := opt.DirectoryName + "/"
	mockAzBlobClient.EXPECT().ListBlobs(gomock.Any(), opt.BucketName, dirSuffix).Return(nil, nil).AnyTimes()
	mockAzBlobClient.EXPECT().DeleteBlobs(gomock.Any(), gomock.Any(), gomock.Any()).Return(nil).AnyTimes()

	err := provider.DeleteDataDirectory(context.Background(), opt.StorageAccountName, opt.BucketName, opt.DirectoryName)
	assert.NoError(t, err, "Expected no error")
}

func TestDeleteDataDirectoryTask_Delete_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	provider := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	opt := DirectoryOptions{
		TaskID:             "delete-error",
		BucketName:         "my-bucket",
		DirectoryName:      "existing-directory",
		StorageAccountName: "storage-account",
	}
	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != opt.StorageAccountName {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", opt.StorageAccountName, got)
		}
		return mockAzBlobClient, nil
	}

	dirSuffix := opt.DirectoryName + "/"
	mockAzBlobClient.EXPECT().ListBlobs(gomock.Any(), opt.BucketName, dirSuffix).Return([]string{opt.DirectoryName, opt.DirectoryName}, nil).AnyTimes()
	mockAzBlobClient.EXPECT().DeleteBlobs(gomock.Any(), gomock.Any(), gomock.Any()).Return(eris.New("failed to delete 1 objects")).AnyTimes()

	err := provider.DeleteDataDirectory(context.Background(), opt.StorageAccountName, opt.BucketName, opt.DirectoryName)
	assert.ErrorIs(t, err, eris.New("failed to delete 1 objects"))
}

func TestDeleteDataDirectory_List_Error(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	provider := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	opt := DirectoryOptions{
		TaskID:             "list-error",
		BucketName:         "my-bucket",
		DirectoryName:      "existing-directory",
		StorageAccountName: "storage-account",
	}
	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != opt.StorageAccountName {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", opt.StorageAccountName, got)
		}
		return mockAzBlobClient, nil
	}

	dirSuffix := opt.DirectoryName + "/"
	mockAzBlobClient.EXPECT().ListBlobs(gomock.Any(), opt.BucketName, dirSuffix).Return(nil, eris.New("list error")).AnyTimes()
	err := provider.DeleteDataDirectory(context.Background(), opt.StorageAccountName, opt.BucketName, opt.DirectoryName)
	assert.ErrorIs(t, err, eris.New("list error"))
}

func TestCloneDataDirectoryTask_Success(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	provider := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := clone.Options{
		TaskID:                   "existing-directory",
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
		Cursor:                   "start",
		CloneSize:                10,
	}

	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != "storage-account" {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", "storage-account", got)
		}
		return mockAzBlobClient, nil
	}

	objs := []string{"my-dir/test1", "my-dir/test2"}
	mockAzBlobClient.EXPECT().
		ListObjectsWithMarker(
			gomock.Any(),
			AZBlobListRequest{
				Bucket:    options.SourceBucketName,
				Directory: options.SourceDirectoryName,
				Marker:    options.Cursor,
				MaxSize:   int(options.CloneSize),
			}).Return(objs, "", nil)

	mockAzBlobClient.EXPECT().CopyObject(gomock.Any(), AZBlobCopyRequest{SourceKey: "my-dir/test1", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test1", SinkBucket: options.DestinationBucketName, SourceAccount: "storage-account", SinkAccount: "storage-account"}).Return(nil)
	mockAzBlobClient.EXPECT().CopyObject(gomock.Any(), AZBlobCopyRequest{SourceKey: "my-dir/test2", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test2", SinkBucket: options.DestinationBucketName, SourceAccount: "storage-account", SinkAccount: "storage-account"}).Return(nil)

	err := provider.CloneDataDirectory(context.Background(), "storage-account", "storage-account", options)
	assert.NoError(t, err, "Expected no error")
}

func TestCloneDataDirectoryTask_Fail(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockAzBlobClient := NewMockAzBlobClientInterface(ctrl)

	provider := &Provider{
		kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
		},
	}

	options := clone.Options{
		TaskID:                   "existing-directory",
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
		Cursor:                   "start",
		CloneSize:                10,
	}

	createAzBlobClient = func(_ azcore.TokenCredential, got string) (AzBlobClientInterface, error) {
		if got != "storage-account" {
			return nil, eris.Errorf("invalid storage account name, expect %v, got %v", "storage-account", got)
		}
		return mockAzBlobClient, nil
	}

	objs := []string{"my-dir/test1", "my-dir/test2"}
	mockAzBlobClient.EXPECT().
		ListObjectsWithMarker(
			gomock.Any(),
			AZBlobListRequest{
				Bucket:    options.SourceBucketName,
				Directory: options.SourceDirectoryName,
				Marker:    options.Cursor,
				MaxSize:   int(options.CloneSize),
			}).Return(objs, "", nil).Times(cloneRetry)

	mockAzBlobClient.EXPECT().CopyObject(gomock.Any(), AZBlobCopyRequest{SourceKey: "my-dir/test1", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test1", SinkBucket: options.DestinationBucketName, SourceAccount: "storage-account", SinkAccount: "storage-account"}).Return(nil).Times(3)
	mockAzBlobClient.EXPECT().CopyObject(gomock.Any(), AZBlobCopyRequest{SourceKey: "my-dir/test2", SourceBucket: options.SourceBucketName, SinkKey: "their-dir/test2", SinkBucket: options.DestinationBucketName, SourceAccount: "storage-account", SinkAccount: "storage-account"}).Return(eris.New("copy error")).Times(15)

	err := provider.CloneDataDirectory(context.Background(), "storage-account", "storage-account", options)
	assert.Error(t, err)
}
