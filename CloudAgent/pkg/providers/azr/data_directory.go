package azr

import (
	"context"
	"strings"
	"sync"

	"github.com/risingwavelabs/eris"

	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskazr "github.com/risingwavelabs/cloudagent/pbgen/task/azr"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	deletionParallelism = 100
	samplingRate        = 5
	deletionRetry       = 3
	cloneRetry          = 3
)

type DirectoryOptions struct {
	TaskID             string
	TaskNamespace      string
	BucketName         string
	DirectoryName      string
	StorageAccountName string
}

// creates a task in taskrunner for the async deletion of data directories.
func (provider *Provider) RunDataDirectoryDeletionTask(ctx context.Context, options DirectoryOptions) error {
	err := provider.kc.StartTaskRunner(ctx, options.TaskID, options.TaskNamespace, &pbtask.Task{
		Task: &pbtask.Task_AzrDirectoryCleanUpTask{
			AzrDirectoryCleanUpTask: &pbtaskazr.AZRDirectoryCleanUpTask{
				Container:      options.BucketName,
				Directory:      options.DirectoryName,
				StorageAccount: options.StorageAccountName,
			},
		},
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to start directory deletion task")
	}
	return nil
}

func (provider *Provider) DeleteDataDirectory(ctx context.Context, storageAccount, container, directory string) error {
	azBlobClient, err := createAzBlobClient(provider.sdkCred, storageAccount)
	if err != nil {
		return eris.Wrapf(err, "unable to create azblob client, storage account: %v", storageAccount)
	}
	for i := 0; i < deletionRetry; i++ {
		err = provider.deleteDataDirectoryInner(ctx, azBlobClient, container, directory)
		if err == nil {
			return nil
		}
	}
	return err
}

func (provider *Provider) deleteDataDirectoryInner(ctx context.Context, azBlobClient AzBlobClientInterface, containerName, directory string) error {
	if !strings.HasSuffix(directory, "/") {
		directory += "/"
	}
	blobKeys, err := azBlobClient.ListBlobs(ctx, containerName, directory)
	if err != nil {
		return eris.Wrapf(err, "error listing blobs with prefix %v", directory)
	}

	if len(blobKeys) == 0 {
		return nil
	}

	// use parallelism to speed up deletion. Most of the time is spent waiting for the API
	var delErrors []error
	var mu sync.Mutex
	workerPool := make(chan bool, deletionParallelism)
	failedToDelete := 0
	for i := range blobKeys {
		workerPool <- true
		go func(failedToDelete *int, blobName string) {
			defer func() { <-workerPool }()
			if err := azBlobClient.DeleteBlobs(ctx, containerName, blobName); err != nil {
				mu.Lock()
				if *failedToDelete%samplingRate == 0 {
					delErrors = append(delErrors, err)
				}

				*failedToDelete++
				mu.Unlock()
			}
		}(&failedToDelete, blobKeys[i])
	}

	// wait for all workers to finish
	for i := 0; i < cap(workerPool); i++ {
		workerPool <- true
	}

	// delete channel
	close(workerPool)

	if failedToDelete > 0 {
		return eris.Wrapf(eris.Join(delErrors...), "failed to delete %d objects", failedToDelete)
	}

	return nil
}

// creates a task in taskrunner for the async cloning of data directories.
func (provider *Provider) RunDataDirectoryCloneTask(ctx context.Context, sourceAcc, destAcc string, options clone.Options) error {
	if !strings.HasSuffix(options.SourceDirectoryName, "/") {
		options.SourceDirectoryName += "/"
	}
	err := provider.kc.StartTaskRunner(ctx, options.TaskID, options.TaskNamespace, &pbtask.Task{
		Task: &pbtask.Task_AzrDirectoryCloneTask{
			AzrDirectoryCloneTask: &pbtaskazr.AZRDirectoryCloneTask{
				SourceDirectoryName:       options.SourceDirectoryName,
				SourceContainerName:       options.SourceBucketName,
				SourceStorageAccount:      sourceAcc,
				DestinationDirectoryName:  options.DestinationDirectoryName,
				DestinationContainerName:  options.DestinationBucketName,
				DestinationStorageAccount: destAcc,
				Cursor:                    options.Cursor,
				CloneSize:                 options.CloneSize,
			},
		},
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return err
		}
		return eris.Wrap(err, "failed to start directory deletion task")
	}
	return nil
}

func (provider *Provider) CloneDataDirectory(ctx context.Context, sourceAcc, destAcc string, options clone.Options) error {
	var err error
	buildCopyReq := func(srcObj, dstObj string) AZBlobCopyRequest {
		return AZBlobCopyRequest{
			SourceBucket:  options.SourceBucketName,
			SourceKey:     srcObj,
			SourceAccount: sourceAcc,
			SinkBucket:    options.DestinationBucketName,
			SinkKey:       dstObj,
			SinkAccount:   destAcc,
		}
	}

	buildListReq := func() AZBlobListRequest {
		return AZBlobListRequest{
			Bucket:    options.SourceBucketName,
			Directory: options.SourceDirectoryName,
			Marker:    options.Cursor,
			MaxSize:   int(options.CloneSize),
		}
	}
	azBlobClient, err := createAzBlobClient(provider.sdkCred, sourceAcc)
	if err != nil {
		return eris.Wrapf(err, "unable to create azblob client, storage account: %v", sourceAcc)
	}
	for i := 0; i < deletionRetry; i++ {
		err = clone.DataDirectoryInner[AZBlobCopyRequest, AZBlobListRequest](ctx, azBlobClient, options, buildCopyReq, buildListReq)
		if err == nil {
			return nil
		}
	}
	return err
}
