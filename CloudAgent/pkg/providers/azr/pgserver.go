package azr

import (
	"context"

	azrpg "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresqlflexibleservers/v4"
	asodb4pg "github.com/Azure/azure-service-operator/v2/api/dbforpostgresql/v1api20221201"
	asoruntime "github.com/Azure/azure-service-operator/v2/pkg/genruntime"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbazr "github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/utils"

	"github.com/risingwavelabs/eris"
)

type CreatePGServerOption struct {
	ResourceID     string
	Namespace      string
	Location       string
	Spec           *pbazr.PGServerSpec
	SubscriptionID string
	ResourceGroup  string
}

func (provider *Provider) CreatePGServer(ctx context.Context, option CreatePGServerOption) error {
	spec, err := conversion.FromPGServerSpecProto(option.Spec)
	if err != nil {
		return eris.Wrapf(err, "failed to convert pgserver spec proto: %v, error: %s", option.Spec, err)
	}
	spec.Location = utils.Ptr(option.Location)
	spec.Owner = &asoruntime.KnownResourceReference{
		ARMID: getResourceGroupARMID(option.SubscriptionID, option.ResourceGroup),
	}
	err = provider.kc.Create(ctx, &asodb4pg.FlexibleServer{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: spec,
	})
	if err != nil && k8sErrors.IsAlreadyExists(err) {
		return eris.Errorf("pgserver already exists: %v", option).WithCode(eris.CodeAlreadyExists)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to create pgserver: %v", option)
	}
	return nil
}

func (provider *Provider) DeletePGServer(ctx context.Context, resourceID string, namespace string) error {
	err := k8s.DeleteResource[asodb4pg.FlexibleServer](ctx, provider.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("pgserver %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to delete pgserver %s", resourceID)
	}
	return nil
}

func (provider *Provider) StartPGServer(ctx context.Context, resourceID string, _ string) error {
	pgServer, err := provider.pgClient.DescribePGServer(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		return eris.Errorf("pg server %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return err
	}
	state := utils.Unwrap(pgServer.Properties.State)
	if state == azrpg.ServerStateStarting || state == azrpg.ServerStateReady {
		return eris.Errorf("pg server %s is already in starting", resourceID).WithCode(eris.CodeAlreadyExists)
	}
	return provider.pgClient.StartPGServer(ctx, resourceID)
}

func (provider *Provider) StopPGServer(ctx context.Context, resourceID string, _ string) error {
	pgServer, err := provider.pgClient.DescribePGServer(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		return eris.Errorf("pg server %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return err
	}
	state := utils.Unwrap(pgServer.Properties.State)
	if state == azrpg.ServerStateStopping || state == azrpg.ServerStateStopped {
		return eris.Errorf("pg server %s is already in stopping", resourceID).WithCode(eris.CodeAlreadyExists)
	}
	return provider.pgClient.StopPGServer(ctx, resourceID)
}

type PGServerMeta struct {
	Status      *pbresource.Status
	DomainName  *string
	ServerState pbazr.PGServerState
}

func (provider *Provider) GetPGServer(ctx context.Context, resourceID string, namespace string) (*PGServerMeta, error) {
	pgServer, err := provider.pgClient.DescribePGServer(ctx, resourceID)
	if err != nil && utils.IsErrNotFound(err) {
		_, err := k8s.GetResource[asodb4pg.FlexibleServer](ctx, provider.kc, resourceID, namespace)
		if err != nil && k8sErrors.IsNotFound(err) {
			return &PGServerMeta{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_FOUND},
			}, nil
		}
		if err != nil {
			return nil, eris.Wrapf(err, "failed to pg server %s", resourceID)
		}
		// resource is changed but not synced between k8s and azr
		return &PGServerMeta{
			Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
		}, nil
	}
	if err != nil {
		return nil, err
	}
	meta := &PGServerMeta{}
	switch utils.Unwrap(pgServer.Properties.State) {
	case azrpg.ServerStateReady:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_READY}
		meta.ServerState = pbazr.PGServerState_READY
	case azrpg.ServerStateStopped:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
		meta.ServerState = pbazr.PGServerState_STOPPED
	case "Provisioning", azrpg.ServerStateStarting, azrpg.ServerStateStopping, azrpg.ServerStateUpdating:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
		meta.ServerState = pbazr.PGServerState_UPDATING
	case azrpg.ServerStateDisabled, azrpg.ServerStateDropping:
		meta.Status = &pbresource.Status{Code: pbresource.StatusCode_NOT_READY}
		meta.ServerState = pbazr.PGServerState_UNKNOWN
	}
	if pgServer.Properties.FullyQualifiedDomainName != nil {
		meta.DomainName = pgServer.Properties.FullyQualifiedDomainName
	}
	return meta, nil
}
