package azr

import (
	"context"
	"fmt"
	"io"
	"net/http"

	"github.com/Azure/azure-sdk-for-go/sdk/azcore"
	"github.com/Azure/azure-sdk-for-go/sdk/azidentity"
	azrpg "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresqlflexibleservers/v4"
	"github.com/Azure/azure-sdk-for-go/sdk/storage/azblob"
	"github.com/risingwavelabs/eris"

	pbcfgazr "github.com/risingwavelabs/cloudagent/pbgen/config/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

var createAzBlobClient = newAzBlobClient

const (
	listBlobMaxResults = 5000
)

type AzBlobClientInterface interface {
	ListBlobs(ctx context.Context, containerName, dataDirectory string) ([]string, error)
	ListObjectsWithMarker(ctx context.Context, req AZBlobListRequest) ([]string, string, error)
	CopyObject(ctx context.Context, req AZBlobCopyRequest) error
	DeleteBlobs(ctx context.Context, containerName, blobName string) error
	GetBlob(ctx context.Context, containerName, blobName string) ([]byte, error)
	GetBlobEtag(ctx context.Context, containerName, blobName string) (string, error)
}

type AZBlobCopyRequest struct {
	SourceAccount string
	SourceBucket  string
	SourceKey     string
	SinkAccount   string
	SinkBucket    string
	SinkKey       string
}

type AZBlobListRequest struct {
	Bucket    string
	Directory string
	Marker    string
	MaxSize   int
}

type PGServerClient interface {
	StartPGServer(ctx context.Context, id string) error
	StopPGServer(ctx context.Context, id string) error
	DescribePGServer(ctx context.Context, id string) (*azrpg.Server, error)
}

type AzBlobClientImpl struct {
	AzBlobClient *azblob.Client
}

type AzNicClientInterface interface {
	getPrivateIPFromNic(ctx context.Context, nicID, resourceGroup string) (*string, error)
}

func newAzBlobClient(sdkCred azcore.TokenCredential, storageAccount string) (AzBlobClientInterface, error) {
	storageAccountURL := fmt.Sprintf("https://%s.blob.core.windows.net/", storageAccount)
	client, err := azblob.NewClient(storageAccountURL, sdkCred, nil)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create new azr client")
	}
	return &AzBlobClientImpl{AzBlobClient: client}, nil
}

func (az *AzBlobClientImpl) ListBlobs(ctx context.Context, containerName, prefix string) ([]string, error) {
	res, _, err := az.ListObjectsWithMarker(ctx, AZBlobListRequest{Directory: prefix, Bucket: containerName})
	return res, err
}

func (az *AzBlobClientImpl) ListObjectsWithMarker(ctx context.Context, req AZBlobListRequest) ([]string, string, error) {
	numResults := listBlobMaxResults
	if req.MaxSize != 0 {
		numResults = req.MaxSize
	}
	listBlobsOpts := azblob.ListBlobsFlatOptions{
		Prefix:     &req.Directory,
		MaxResults: utils.Ptr(int32(numResults)),
	}
	if req.Marker != "" {
		listBlobsOpts.Marker = &req.Marker
	}
	pager := az.AzBlobClient.NewListBlobsFlatPager(req.Bucket, &listBlobsOpts)

	var blobNames []string
	var resMarker string
	for pager.More() && (req.MaxSize == 0 || len(blobNames) < req.MaxSize) {
		resp, err := pager.NextPage(ctx)
		if err != nil {
			return nil, "", eris.Wrapf(err, "failed to list blobs")
		}

		for _, blob := range resp.Segment.BlobItems {
			blobNames = append(blobNames, *blob.Name)
		}
		if resp.NextMarker != nil {
			resMarker = *resp.NextMarker
		}
	}

	return blobNames, resMarker, nil
}

func (az *AzBlobClientImpl) CopyObject(ctx context.Context, req AZBlobCopyRequest) error {
	sourceBlobURL := fmt.Sprintf("https://%s.blob.core.windows.net/%s/%s", req.SourceAccount, req.SourceBucket, req.SourceKey)
	destinationBlobClient := az.AzBlobClient.ServiceClient().NewContainerClient(req.SinkBucket).NewBlobClient(req.SinkKey)
	_, err := destinationBlobClient.StartCopyFromURL(
		ctx,
		sourceBlobURL,
		nil,
	)
	if err != nil {
		return eris.Wrapf(err, "failed to copy objects")
	}

	return nil
}

func (az *AzBlobClientImpl) DeleteBlobs(ctx context.Context, containerName, blobName string) error {
	_, err := az.AzBlobClient.DeleteBlob(ctx, containerName, blobName, nil)
	if err != nil {
		return eris.Wrapf(err, "failed to delete blob")
	}
	return nil
}

func (az *AzBlobClientImpl) GetBlob(ctx context.Context, containerName, blobName string) ([]byte, error) {
	res, err := az.AzBlobClient.DownloadStream(ctx, containerName, blobName, nil)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get blob from %s/%s", containerName, blobName)
	}
	data, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to read blob from %s/%s", containerName, blobName)
	}
	if res.ETag == nil {
		return nil, eris.Errorf("blob etag is nil for %s/%s", containerName, blobName)
	}
	return data, nil
}

func (az *AzBlobClientImpl) GetBlobEtag(ctx context.Context, containerName, blobName string) (string, error) {
	res, err := az.AzBlobClient.ServiceClient().NewContainerClient(containerName).NewBlobClient(blobName).GetProperties(ctx, nil)
	if err != nil {
		return "", eris.Wrapf(err, "failed to get blob properties from %s/%s", containerName, blobName)
	}
	if res.ETag == nil {
		return "", eris.Errorf("blob etag is nil for %s/%s", containerName, blobName)
	}
	return string(*res.ETag), nil
}

type PGServerClientImpl struct {
	server        *azrpg.ServersClient
	resourceGroup string
}

func newPGServerClient(sdkCred azcore.TokenCredential, subscriptionID string, resourceGroup string) (PGServerClient, error) {
	clientFactory, err := azrpg.NewClientFactory(subscriptionID, sdkCred, nil)
	if err != nil {
		return nil, eris.Wrap(err, "failed to create new azr client")
	}
	return &PGServerClientImpl{
		server:        clientFactory.NewServersClient(),
		resourceGroup: resourceGroup,
	}, nil
}

func (p *PGServerClientImpl) StartPGServer(ctx context.Context, id string) error {
	_, err := p.server.BeginStart(ctx, p.resourceGroup, id, &azrpg.ServersClientBeginStartOptions{})
	if err != nil {
		return eris.Wrap(err, "failed to start pg server")
	}
	return nil
}

func (p *PGServerClientImpl) StopPGServer(ctx context.Context, id string) error {
	_, err := p.server.BeginStop(ctx, p.resourceGroup, id, &azrpg.ServersClientBeginStopOptions{})
	if err != nil {
		return eris.Wrap(err, "failed to stop pg server")
	}
	return nil
}

func (p *PGServerClientImpl) DescribePGServer(ctx context.Context, id string) (*azrpg.Server, error) {
	res, err := p.server.Get(ctx, p.resourceGroup, id, &azrpg.ServersClientGetOptions{})
	respErr := &azcore.ResponseError{}
	if err != nil && eris.As(err, &respErr) && respErr.StatusCode == http.StatusNotFound {
		return nil, eris.WithCode(eris.Wrap(err, "pg server not found"), eris.CodeNotFound)
	}
	if err != nil {
		return nil, eris.Wrap(err, "failed to get pg server")
	}
	return &res.Server, nil
}

func initSDKCred(azrConfig *pbcfgazr.Config) (azcore.TokenCredential, error) {
	switch authType := azrConfig.GetAuth().(type) {
	case *pbcfgazr.Config_StaticCreds:
		staticCreds := azrConfig.GetStaticCreds()
		creds, err := azidentity.NewClientSecretCredential(staticCreds.GetTenantId(), staticCreds.GetClientId(), staticCreds.GetClientSecret(), nil)
		if err != nil {
			return nil, eris.Wrap(err, "unable to load AZR static config")
		}
		return creds, nil
	case *pbcfgazr.Config_AksWorkloadIdentity:
		sdkCreds, err := azidentity.NewDefaultAzureCredential(nil)
		if err != nil {
			return nil, eris.Wrap(err, "unable to load AZR SDK config")
		}
		return sdkCreds, nil
	default:
		return nil, eris.New(fmt.Sprintf("invalid AZR auth type: %v", authType))
	}
}
