package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateConfigMap(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreateConfigMap(ctx, CreateConfigMapOption{
		ResourceID: "name",
		Namespace:  "ns",
		ConfigMap: ConfigMap{
			Immutable: true,
			Data: map[string]string{
				"k1": "v1",
				"k2": "v2",
			},
		},
	})
	require.NoError(t, err)

	cm, err := k8s.GetResource[corev1.ConfigMap](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", cm.Name)
	assert.Equal(t, "ns", cm.Namespace)
	assert.Equal(t, map[string]string{
		"k1": "v1",
		"k2": "v2",
	}, cm.Data)

	err = p.CreateConfigMap(ctx, CreateConfigMapOption{
		ResourceID: "name",
		Namespace:  "ns",
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestGetConfigMap(t *testing.T) {
	c := fake.NewClient(&corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Data: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	cmMeta, err := p.GetConfigMap(ctx, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, cmMeta.Status)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, cmMeta.Status)
	assert.Equal(t, &ConfigMap{
		Data: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	}, cmMeta.ConfigMap)

	cmMeta, err = p.GetConfigMap(ctx, "name-non-existent", "ns")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_NOT_FOUND,
	}, cmMeta.Status)
}

func TestDeleteConfigMap(t *testing.T) {
	c := fake.NewClient(&corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.DeleteConfigMap(ctx, "name", "ns")
	require.NoError(t, err)
	err = p.DeleteConfigMap(ctx, "name", "ns")
	assert.True(t, utils.IsErrNotFound(err))
}

func TestUpdateConfigMap(t *testing.T) {
	c := fake.NewClient(&corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Data: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()

	err := p.UpdateConfigMap(ctx, UpdateConfigMapOption{
		ResourceID: "name",
		Namespace:  "ns",
		To: ConfigMap{
			Data: map[string]string{
				"k3": "v3",
				"k4": "v4",
			},
		},
	})
	require.NoError(t, err)

	cmMeta, err := p.GetConfigMap(ctx, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, cmMeta.Status)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, cmMeta.Status)
	assert.Equal(t, &ConfigMap{
		Data: map[string]string{
			"k3": "v3",
			"k4": "v4",
		},
	}, cmMeta.ConfigMap)
}
