package k8s

import (
	"context"
	"encoding/json"
	"fmt"
	"slices"

	"github.com/risingwavelabs/eris"
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	rwconsts "github.com/risingwavelabs/risingwave-operator/pkg/consts"
	"google.golang.org/protobuf/proto"
	pbtimestamp "google.golang.org/protobuf/types/known/timestamppb"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"

	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

type CreateRisingWaveOption struct {
	ResourceID     string
	Namespace      string
	Labels         map[string]string
	Annotations    map[string]string
	RisingWaveSpec *pbrw.RisingWaveSpec
}

type ScaleRisingWaveOption struct {
	ResourceID          string
	Namespace           string
	MetaScaleSepc       *pbrw.ScaleSpec
	FrontendScaleSepc   *pbrw.ScaleSpec
	ComputeScaleSepc    *pbrw.ScaleSpec
	CompactorScaleSepc  *pbrw.ScaleSpec
	StandaloneScaleSepc *pbrw.ScaleSpec
}

func (p *Provider) CreateRisingWave(ctx context.Context, option CreateRisingWaveOption) error {
	rwSpec, err := conversion.FromRisingWaveSpecProto(option.RisingWaveSpec)
	if err != nil {
		return eris.Errorf("failed to convert rw spec proto: %v, error: %s", option.RisingWaveSpec, err).WithCode(eris.CodeInvalidArgument)
	}

	if err := p.kc.Create(ctx, &rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:        option.ResourceID,
			Namespace:   option.Namespace,
			Labels:      option.Labels,
			Annotations: option.Annotations,
		},
		Spec: *rwSpec,
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("rw %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create rw %s", option.ResourceID)
	}
	return nil
}

type RisingWaveMeta struct {
	RisingWaveSpec *pbrw.RisingWaveSpec
	RWStatus       *pbrw.RisingWaveStatus
}

func (p *Provider) GetRisingWave(ctx context.Context, resourceID, namespace string) (*RisingWaveMeta, error) {
	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return nil, eris.Errorf("failed to find rw %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return nil, eris.Wrapf(err, "failed to retrieve rw %s", resourceID)
	}
	rwSpecProto, err := conversion.ToRisingWaveSpecProto(&rw.Spec)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to covert RW to proto %v", rw.Spec)
	}
	return &RisingWaveMeta{
		RWStatus: &pbrw.RisingWaveStatus{
			StatusCode:         p.getRisingWaveStatusCode(rw),
			StateStoreRootPath: rw.Status.Internal.StateStoreRootPath,
		},
		RisingWaveSpec: rwSpecProto,
	}, nil
}

func (p *Provider) DeleteRisingWave(ctx context.Context, resourceID string, namespace string) error {
	if err := k8s.DeleteResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to find rw %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete rw %s", resourceID)
	}
	return nil
}

func (p *Provider) getRisingWaveStatusCode(rw *rwv1alpha1.RisingWave) pbrw.RisingWaveStatusCode {
	if rw.Generation != rw.Status.ObservedGeneration {
		return pbrw.RisingWaveStatusCode_RW_WAIT_FOR_OBSERVATION
	}
	status := pbrw.RisingWaveStatusCode_RW_NOT_READY
	for _, cond := range rw.Status.Conditions {
		if cond.Type == "Running" {
			if runningStatus := cond.Status; runningStatus == metav1.ConditionTrue {
				status = pbrw.RisingWaveStatusCode_RW_READY
			}
		} else if cond.Type == rwv1alpha1.RisingWaveConditionUpgrading && cond.Status == metav1.ConditionTrue {
			return pbrw.RisingWaveStatusCode_RW_UPGRADING
		}
	}
	return status
}

func (p *Provider) updateRisingWaveImage(spec *rwv1alpha1.RisingWaveSpec, resourceID, namespace, tag string) error {
	spec.Image = tag

	if ptr.Deref(spec.EnableStandaloneMode, false) {
		if spec.Components.Standalone != nil && len(spec.Components.Standalone.Template.Spec.Image) > 0 {
			spec.Components.Standalone.Template.Spec.Image = tag
		}
	} else {
		nodegroups := [][]rwv1alpha1.RisingWaveNodeGroup{
			spec.Components.Meta.NodeGroups,
			spec.Components.Compute.NodeGroups,
		}

		for _, nodegroup := range nodegroups {
			if len(nodegroup) == 0 {
				return eris.Errorf("find empty nodegroup, name  %s, namespace %s", resourceID, namespace)
			}
			for i := 0; i < len(nodegroup); i++ {
				nodegroup[i].UpgradeStrategy.Type = rwv1alpha1.RisingWaveUpgradeStrategyTypeRecreate
			}
		}
	}

	return nil
}

func (p *Provider) UpdateRisingWaveImage(ctx context.Context, resourceID, namespace, imageTag string) error {
	from, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to find rw %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve rw %s", resourceID)
	}

	to := from.DeepCopy()
	err = p.updateRisingWaveImage(&to.Spec, resourceID, namespace, imageTag)
	if err != nil {
		return err
	}

	err = k8s.MergePatchResource(ctx, p.kc, from, to)
	if k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to get RW, id: %v, ns: %v", resourceID, namespace).WithCode(eris.CodeNotFound)
	}
	return err
}

// scaleCluster scales a rw instance in cluster mode or switches a standalone instance over to cluster mode.
func (p *Provider) scaleCluster(option ScaleRisingWaveOption, out *rwv1alpha1.RisingWave) error {
	if option.StandaloneScaleSepc != nil {
		return fmt.Errorf("standalone scale spec has to be nil when scaling a cluster instance")
	}
	if ptr.Deref(out.Spec.EnableStandaloneMode, false) {
		return fmt.Errorf("function should only be used to scale cluster instances")
	}

	if option.MetaScaleSepc != nil {
		err := applyScaleSpec(option.MetaScaleSepc, out.Spec.Components.Meta.NodeGroups)
		if err != nil {
			return eris.Wrap(err, "failed to apply meta scale spec")
		}
	}
	if option.FrontendScaleSepc != nil {
		err := applyScaleSpec(option.FrontendScaleSepc, out.Spec.Components.Frontend.NodeGroups)
		if err != nil {
			return eris.Wrap(err, "failed to apply frontend scale spec")
		}
	}
	if option.ComputeScaleSepc != nil {
		err := applyScaleSpec(option.ComputeScaleSepc, out.Spec.Components.Compute.NodeGroups)
		if err != nil {
			return eris.Wrap(err, "failed to apply compute scale spec")
		}
	}
	if option.CompactorScaleSepc != nil {
		err := applyScaleSpec(option.CompactorScaleSepc, out.Spec.Components.Compactor.NodeGroups)
		if err != nil {
			return eris.Wrap(err, "failed to apply compactor scale spec")
		}
	}

	return nil
}

func (p *Provider) scaleStandalone(option ScaleRisingWaveOption, out *rwv1alpha1.RisingWave) error {
	if option.StandaloneScaleSepc == nil {
		return fmt.Errorf("standalone scale spec cannot be nil when scaling standalone instance")
	}
	if !ptr.Deref(out.Spec.EnableStandaloneMode, false) {
		return fmt.Errorf("function should only be used to scale standalone instances")
	}
	if option.StandaloneScaleSepc.GetReplicas() > 1 {
		return fmt.Errorf("scaling standalone instance to more than 1 replicas is not allowed")
	}
	if err := applyStandaloneScaleSpec(option.StandaloneScaleSepc, out.Spec.Components.Standalone); err != nil {
		return eris.Wrap(err, "failed to apply standalone scale spec")
	}
	return nil
}

func (p *Provider) applyScaleOperation(option ScaleRisingWaveOption, from *rwv1alpha1.RisingWave, to *rwv1alpha1.RisingWave) error {
	toStandalone := option.StandaloneScaleSepc != nil
	fromStandalone := ptr.Deref(from.Spec.EnableStandaloneMode, false)

	if fromStandalone && toStandalone {
		return p.scaleStandalone(option, to)
	} else if !fromStandalone && !toStandalone {
		return p.scaleCluster(option, to)
	}
	return eris.Errorf("switching between standalone and cluster is currently not supported")
}

func (p *Provider) ScaleRisingWave(ctx context.Context, option ScaleRisingWaveOption) error {
	from, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to find rw %s", option.ResourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve rw %s, %s", option.ResourceID, option.Namespace)
	}

	to := from.DeepCopy()
	if err := p.applyScaleOperation(option, from, to); err != nil {
		return eris.Wrap(err, "failed to pick scale operation")
	}

	err = k8s.MergePatchResource(ctx, p.kc, from, to)
	if k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to get RW, id: %v, ns: %v", option.ResourceID, option.Namespace).WithCode(eris.CodeNotFound)
	}
	return err
}

const (
	DefaultNodeGroup = "default"
)

func applyScaleSpec(spec *pbrw.ScaleSpec, ngs []rwv1alpha1.RisingWaveNodeGroup) error {
	if len(ngs) == 0 {
		return nil
	}
	idx := slices.IndexFunc(ngs, func(item rwv1alpha1.RisingWaveNodeGroup) bool {
		return item.Name == DefaultNodeGroup
	})
	if idx == -1 {
		return nil
	}
	// We assume there's only one ng
	ngs[idx].Replicas = int32(spec.GetReplicas())
	if spec.GetResources() != nil {
		resources, err := conversion.FromResourceRequirementsProto(spec.GetResources())
		if err != nil {
			return eris.Wrapf(err, "invalid resource requirements %v", spec.GetResources())
		}
		ngs[idx].Template.Spec.Resources = *resources
	}
	if spec.GetAffinity() != nil {
		affinity, err := conversion.FromAffinityProto(spec.GetAffinity())
		if err != nil {
			return eris.Wrapf(err, "invalid affinity to override %v", spec.GetAffinity())
		}
		ngs[idx].Template.Spec.Affinity = affinity
	}

	return nil
}

func applyStandaloneScaleSpec(spec *pbrw.ScaleSpec, standalone *rwv1alpha1.RisingWaveStandaloneComponent) error {
	if standalone == nil {
		return nil
	}
	standalone.Replicas = int32(spec.GetReplicas())
	if spec.GetResources() == nil {
		return nil
	}
	resources, err := conversion.FromResourceRequirementsProto(spec.GetResources())
	if err != nil {
		return eris.Wrapf(err, "invalid resource requirements %v", spec.GetResources())
	}
	standalone.Template.Spec.Resources = *resources
	return nil
}

const (
	RwLastRecordReplica = "risingwave.cloud/last-record-replica"
)

func (p *Provider) StartRisingWave(ctx context.Context, resourceID string, namespace string, overrides []*pbk8ssvc.RisingWaveReplicaOverride) error {
	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", namespace, resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", namespace, resourceID)
	}

	// read replica from annotation
	replicaMap := make(map[string]int32)
	if replicaStr, ok := rw.Annotations[RwLastRecordReplica]; ok {
		err = json.Unmarshal([]byte(replicaStr), &replicaMap)
		if err != nil {
			return eris.Wrapf(err, "failed to unmarshal replicaMap %s", replicaStr)
		}
		delete(rw.Annotations, RwLastRecordReplica)
	}
	for _, override := range overrides {
		replicaMap[fmt.Sprintf("%s/%s", override.GetComponent(), override.GetNodeGroup())] = int32(override.GetReplicas())
	}
	patchReplica(rw, func(component string, nodeGroup string, originalReplica int32) int32 {
		if replica, ok := replicaMap[fmt.Sprintf("%s/%s", component, nodeGroup)]; ok {
			return replica
		}
		return originalReplica
	})

	err = p.kc.Update(ctx, rw)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s/%s", namespace, resourceID)
	}
	return nil
}

func (p *Provider) StopRisingWave(ctx context.Context, resourceID string, namespace string) error {
	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", namespace, resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", namespace, resourceID)
	}
	// record current replica and set to 0
	replicaMap := make(map[string]int32)
	updated := false
	patchReplica(rw, func(component string, nodeGroup string, replica int32) int32 {
		if replica != 0 {
			updated = true
		}
		replicaMap[fmt.Sprintf("%s/%s", component, nodeGroup)] = replica
		return 0
	})
	if !updated {
		return nil
	}
	if _, ok := rw.Annotations[RwLastRecordReplica]; ok {
		return eris.New("replicas is inconsistent with the last record").WithCode(eris.CodeFailedPrecondition)
	}
	replicaStr, err := json.Marshal(replicaMap)
	if err != nil {
		return eris.Wrapf(err, "failed to marshal replicaMap %#v", replicaMap)
	}
	// put replica annotation
	if rw.Annotations == nil {
		rw.Annotations = make(map[string]string)
	}
	rw.Annotations[RwLastRecordReplica] = string(replicaStr)

	err = p.kc.Update(ctx, rw)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s/%s", namespace, resourceID)
	}
	return nil
}

func patchReplica(rw *rwv1alpha1.RisingWave, replicaFunc func(component string, nodeGroup string, replica int32) int32) {
	isStandalone := ptr.Deref(rw.Spec.EnableStandaloneMode, false)
	components := &rw.Spec.Components
	if isStandalone {
		components.Standalone.Replicas = replicaFunc("standalone", "default", components.Standalone.Replicas)
	} else {
		for i, nodeGroup := range components.Meta.NodeGroups {
			components.Meta.NodeGroups[i].Replicas = replicaFunc("meta", nodeGroup.Name, nodeGroup.Replicas)
		}
		for i, nodeGroup := range components.Compute.NodeGroups {
			components.Compute.NodeGroups[i].Replicas = replicaFunc("compute", nodeGroup.Name, nodeGroup.Replicas)
		}
		for i, nodeGroup := range components.Compactor.NodeGroups {
			components.Compactor.NodeGroups[i].Replicas = replicaFunc("compactor", nodeGroup.Name, nodeGroup.Replicas)
		}
		for i, nodeGroup := range components.Frontend.NodeGroups {
			components.Frontend.NodeGroups[i].Replicas = replicaFunc("frontend", nodeGroup.Name, nodeGroup.Replicas)
		}
	}
}

type UpdateRisingWaveComponentsOption struct {
	ResourceID           string
	Namespace            string
	MetaSpec             *pbrw.ComponentSpec
	FrontendSpec         *pbrw.ComponentSpec
	ComputeSpec          *pbrw.ComponentSpec
	CompactorSpec        *pbrw.ComponentSpec
	StandaloneSpec       *pbrw.StandaloneSpec
	EnableStandaloneMode *bool
}

func (p *Provider) UpdateRisingWaveComponents(ctx context.Context, option UpdateRisingWaveComponentsOption) error {
	from, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s", option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s, %s", option.ResourceID, option.Namespace)
	}

	to := from.DeepCopy()
	err = p.updateComponents(option, to)
	if err != nil {
		return err
	}

	delete(to.Annotations, RwLastRecordReplica)
	err = p.kc.Update(ctx, to)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s, %s", option.ResourceID, option.Namespace)
	}
	return err
}

func (p *Provider) updateComponents(option UpdateRisingWaveComponentsOption, to *rwv1alpha1.RisingWave) error {
	if option.MetaSpec != nil {
		spec, err := conversion.FromComponentSpecProto(option.MetaSpec)
		if err != nil {
			return eris.WithCode(eris.Wrapf(err, "failed to covert meta component spec %v", option.MetaSpec), eris.CodeInvalidArgument)
		}
		to.Spec.Components.Meta = spec
	}
	if option.FrontendSpec != nil {
		spec, err := conversion.FromComponentSpecProto(option.FrontendSpec)
		if err != nil {
			return eris.WithCode(eris.Wrapf(err, "failed to covert frontend component spec %v", option.FrontendSpec), eris.CodeInvalidArgument)
		}
		to.Spec.Components.Frontend = spec
	}
	if option.ComputeSpec != nil {
		spec, err := conversion.FromComponentSpecProto(option.ComputeSpec)
		if err != nil {
			return eris.WithCode(eris.Wrapf(err, "failed to covert compute component spec %v", option.ComputeSpec), eris.CodeInvalidArgument)
		}
		to.Spec.Components.Compute = spec
	}
	if option.CompactorSpec != nil {
		spec, err := conversion.FromComponentSpecProto(option.CompactorSpec)
		if err != nil {
			return eris.WithCode(eris.Wrapf(err, "failed to covert compactor component spec %v", option.CompactorSpec), eris.CodeInvalidArgument)
		}
		to.Spec.Components.Compactor = spec
	}
	if option.StandaloneSpec != nil {
		spec, err := conversion.FromStandaloneSpecProto(option.StandaloneSpec)
		if err != nil {
			return eris.WithCode(eris.Wrapf(err, "failed to covert standalone component spec %v", option.CompactorSpec), eris.CodeInvalidArgument)
		}
		to.Spec.Components.Standalone = spec
	}
	if option.EnableStandaloneMode != nil {
		to.Spec.EnableStandaloneMode = option.EnableStandaloneMode
	}
	return nil
}

func (p *Provider) UpdateRisingWaveMetaStore(ctx context.Context, resourceID string, namespace string, spec *pbrw.MetaStoreSpec) error {
	metaStore, err := conversion.FromRisingWaveMetaStoreProto(spec)
	if err != nil {
		return eris.WithCode(eris.Wrapf(err, "failed to covert metastore spec to proto %v, error: %s", spec, err), eris.CodeInvalidArgument)
	}

	rwOriginal, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("rw not found %s %s", resourceID, namespace).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s, %s", resourceID, namespace)
	}

	rwPrePatch := rwOriginal.DeepCopy()
	if rwPrePatch.Annotations == nil {
		rwPrePatch.Annotations = make(map[string]string)
	}
	rwPrePatch.Annotations[rwconsts.AnnotationBypassValidatingWebhook] = "true"
	rwPrePatch.Spec.MetaStore = metaStore

	err = k8s.MergePatchResource(ctx, p.kc, rwOriginal, rwPrePatch)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("rw not found %s %s", resourceID, namespace).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s, %s", resourceID, namespace)
	}

	rwPostPatch := rwPrePatch.DeepCopy()
	rwPostPatch.Annotations[rwconsts.AnnotationBypassValidatingWebhook] = "false"
	err = k8s.MergePatchResource(ctx, p.kc, rwPrePatch, rwPostPatch)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("rw not found %s %s", resourceID, namespace).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s, %s", resourceID, namespace)
	}
	return nil
}

func (p *Provider) UpdateRisingWaveLicenseKey(ctx context.Context, resourceID, namespace, secretName string) error {
	from, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to find rw %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve rw %s", resourceID)
	}

	to := from.DeepCopy()
	to.Spec.LicenseKey = &rwv1alpha1.RisingWaveLicenseKey{
		SecretName: secretName,
	}

	err = k8s.MergePatchResource(ctx, p.kc, from, to)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to get RW, id: %v, ns: %v", resourceID, namespace).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s, %s", resourceID, namespace)
	}

	return nil
}

func (p *Provider) UpdateRisingWaveSecretStore(ctx context.Context, resourceID, namespace, secretName, secretKey string) error {
	from, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to find rw %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve rw %s", resourceID)
	}

	// For now the secret store private key doesn't support rotation,
	// thus any update to private key is forbidden.
	fmt.Println(from.Spec.SecretStore)
	if from.Spec.SecretStore.PrivateKey.SecretRef != nil || from.Spec.SecretStore.PrivateKey.Value != nil {
		return eris.Errorf("failed to update rw secret store, id: %s, ns: %s, existent secret store is found", resourceID, namespace)
	}

	to := from.DeepCopy()
	to.Spec.SecretStore = rwv1alpha1.RisingWaveSecretStore{
		PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
			SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
				Name: secretName,
				Key:  secretKey,
			},
		},
	}

	err = k8s.MergePatchResource(ctx, p.kc, from, to)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to get RW, id: %v, ns: %v", resourceID, namespace).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s, %s", resourceID, namespace)
	}

	return nil
}

type CreateRisingWaveNodeGroupOption struct {
	ResourceID    string
	Namespace     string
	ComponentType pbrw.ComponentType
	Spec          *pbrw.NodeGroupSpec
}

type UpdateRisingWaveNodeGroupOption struct {
	ResourceID    string
	Namespace     string
	ComponentType pbrw.ComponentType
	Spec          *pbrw.NodeGroupSpec
}

type DeleteRisingWaveNodeGroupOption struct {
	ResourceID    string
	Namespace     string
	ComponentType pbrw.ComponentType
	NodeGroup     string
}

func (p *Provider) CreateRisingWaveNodeGroup(ctx context.Context, option CreateRisingWaveNodeGroupOption) error {
	nodeGroup, err := conversion.FromNodeGroupSpecProto(option.Spec)
	if err != nil {
		return eris.WithCode(eris.Wrapf(err, "failed to convert node group spec"), eris.CodeInvalidArgument)
	}
	if option.ComponentType == pbrw.ComponentType_STANDALONE {
		return eris.Errorf("standalone does not support node group").WithCode(eris.CodeInvalidArgument)
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", option.Namespace, option.ResourceID)
	}

	component, err := getComponentPointer(rw, option.ComponentType)
	if err != nil {
		return err
	}
	for _, it := range component.NodeGroups {
		if it.Name == nodeGroup.Name {
			return eris.Errorf("node group %s/%s already exists", option.ComponentType, nodeGroup.Name).WithCode(eris.CodeAlreadyExists)
		}
	}
	component.NodeGroups = append(component.NodeGroups, nodeGroup)

	err = p.kc.Update(ctx, rw)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s/%s", option.Namespace, option.ResourceID)
	}
	return nil
}

func (p *Provider) UpdateRisingWaveNodeGroup(ctx context.Context, option UpdateRisingWaveNodeGroupOption) error {
	nodeGroup, err := conversion.FromNodeGroupSpecProto(option.Spec)
	if err != nil {
		return eris.WithCode(eris.Wrapf(err, "failed to convert node group spec"), eris.CodeInvalidArgument)
	}
	if option.ComponentType == pbrw.ComponentType_STANDALONE {
		return eris.Errorf("standalone does not support node group").WithCode(eris.CodeInvalidArgument)
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", option.Namespace, option.ResourceID)
	}

	var target *rwv1alpha1.RisingWaveNodeGroup

	component, err := getComponentPointer(rw, option.ComponentType)
	if err != nil {
		return err
	}
	for i, it := range component.NodeGroups {
		if it.Name == nodeGroup.Name {
			target = &component.NodeGroups[i]
		}
	}
	if target == nil {
		return eris.Errorf("node group %s/%s not found", option.ComponentType, nodeGroup.Name).WithCode(eris.CodeNotFound)
	}

	targetProto, err := conversion.ToNodeGroupSpecProto(*target)
	if err != nil {
		return eris.Wrapf(err, "failed to convert node group spec")
	}
	if proto.Equal(targetProto, option.Spec) {
		return eris.Errorf("node group %s/%s already updated", option.ComponentType, nodeGroup.Name).WithCode(eris.CodeAlreadyExists)
	}

	*target = nodeGroup
	err = p.kc.Update(ctx, rw)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s/%s", option.Namespace, option.ResourceID)
	}
	return nil
}

func (p *Provider) DeleteRisingWaveNodeGroup(ctx context.Context, option DeleteRisingWaveNodeGroupOption) error {
	if option.ComponentType == pbrw.ComponentType_STANDALONE {
		return eris.Errorf("standalone does not support node group").WithCode(eris.CodeInvalidArgument)
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", option.Namespace, option.ResourceID)
	}

	component, err := getComponentPointer(rw, option.ComponentType)
	if err != nil {
		return err
	}
	index := -1
	for i, it := range component.NodeGroups {
		if it.Name == option.NodeGroup {
			index = i
			break
		}
	}

	if index == -1 {
		return eris.Errorf("node group %s/%s not found", option.ComponentType, option.NodeGroup).WithCode(eris.CodeNotFound)
	}
	newNodeGroups := make([]rwv1alpha1.RisingWaveNodeGroup, 0, len(component.NodeGroups)-1)
	newNodeGroups = append(newNodeGroups, component.NodeGroups[:index]...)
	newNodeGroups = append(newNodeGroups, component.NodeGroups[index+1:]...)

	component.NodeGroups = newNodeGroups
	err = p.kc.Update(ctx, rw)
	if err != nil {
		return eris.Wrapf(err, "failed to update rw %s/%s", option.Namespace, option.ResourceID)
	}
	return nil
}

type UpdateRisingWaveNodeGroupConfigurationOption struct {
	ResourceID    string
	Namespace     string
	ComponentType pbrw.ComponentType
	NodeGroup     string
	Spec          *pbrw.NodeConfig
}

func (p *Provider) UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, option UpdateRisingWaveNodeGroupConfigurationOption) error {
	var nodeConfig *rwv1alpha1.RisingWaveNodeConfiguration
	if option.Spec != nil {
		nodeConfig = conversion.FromRisingWaveNodeConfigProto(option.Spec)
	}
	if option.ComponentType == pbrw.ComponentType_STANDALONE {
		return eris.Errorf("standalone does not support node group configuration").WithCode(eris.CodeInvalidArgument)
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", option.Namespace, option.ResourceID)
	}

	nodeGroup, err := getNodeGroupPointer(rw, option.ComponentType, option.NodeGroup)
	if err != nil {
		return err
	}
	nodeGroup.Configuration = nodeConfig
	err = p.kc.Update(ctx, rw)
	// handle the error: the object has been modified
	if err != nil && k8sErrors.IsConflict(err) {
		return eris.WithCode(eris.Wrapf(err, "conflict to update node group %s/%s of %s/%s", option.ComponentType, option.NodeGroup, option.Namespace, option.ResourceID), eris.CodeAborted)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to update node group %s/%s of %s/%s", option.ComponentType, option.NodeGroup, option.Namespace, option.ResourceID)
	}
	return nil
}

type UpdateRisingWaveNodeGroupRestartAtOption struct {
	ResourceID    string
	Namespace     string
	ComponentType pbrw.ComponentType
	NodeGroup     string
	RestartAt     *pbtimestamp.Timestamp
}

func (p *Provider) UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, option UpdateRisingWaveNodeGroupRestartAtOption) error {
	var restartAt *metav1.Time
	if option.RestartAt != nil {
		restartAt = &metav1.Time{Time: option.RestartAt.AsTime()}
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("failed to find rw %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to retrieve rw %s/%s", option.Namespace, option.ResourceID)
	}

	if option.ComponentType == pbrw.ComponentType_STANDALONE {
		if rw.Spec.Components.Standalone == nil {
			return eris.Errorf("standalone component not found").WithCode(eris.CodeInvalidArgument)
		}
		rw.Spec.Components.Standalone.RestartAt = restartAt
	} else {
		nodeGroup, err := getNodeGroupPointer(rw, option.ComponentType, option.NodeGroup)
		if err != nil {
			return err
		}
		nodeGroup.RestartAt = restartAt
	}
	err = p.kc.Update(ctx, rw)
	// handle the error: the object has been modified
	if err != nil && k8sErrors.IsConflict(err) {
		return eris.WithCode(eris.Wrapf(err, "conflict to update node group %s/%s of %s/%s", option.ComponentType, option.NodeGroup, option.Namespace, option.ResourceID), eris.CodeAborted)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to update node group %s/%s of %s/%s", option.ComponentType, option.NodeGroup, option.Namespace, option.ResourceID)
	}
	return nil
}

func getComponentPointer(rw *rwv1alpha1.RisingWave, componentType pbrw.ComponentType) (*rwv1alpha1.RisingWaveComponent, error) {
	switch componentType {
	case pbrw.ComponentType_UNKNOWN_COMPONENT:
		return nil, eris.New("unknown component type").WithCode(eris.CodeInvalidArgument)
	case pbrw.ComponentType_STANDALONE:
		return nil, eris.New("don't support for standalone component").WithCode(eris.CodeInvalidArgument)
	case pbrw.ComponentType_META:
		return &rw.Spec.Components.Meta, nil
	case pbrw.ComponentType_FRONTEND:
		return &rw.Spec.Components.Frontend, nil
	case pbrw.ComponentType_COMPUTE:
		return &rw.Spec.Components.Compute, nil
	case pbrw.ComponentType_COMPACTOR:
		return &rw.Spec.Components.Compactor, nil
	}
	return nil, eris.New("unknown component type").WithCode(eris.CodeInvalidArgument)
}

func getNodeGroupPointer(rw *rwv1alpha1.RisingWave, componentType pbrw.ComponentType, nodeGroup string) (*rwv1alpha1.RisingWaveNodeGroup, error) {
	component, err := getComponentPointer(rw, componentType)
	if err != nil {
		return nil, err
	}
	for i, it := range component.NodeGroups {
		if it.Name == nodeGroup {
			return &component.NodeGroups[i], nil
		}
	}
	return nil, eris.Errorf("node group %s/%s not found", componentType, nodeGroup).WithCode(eris.CodeNotFound)
}
