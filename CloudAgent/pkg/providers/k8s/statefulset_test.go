package k8s

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	appv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	testNamespace       = "test-namespace"
	testStatefulsetName = "test-statefulset"
)

func TestRestartStatefulSet(t *testing.T) {
	currTime := time.Now().Add(time.Duration(-1) * time.Minute).Format(time.RFC3339)
	c := fake.NewClient(&appv1.StatefulSet{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testStatefulsetName,
			Namespace: testNamespace,
		},
		Spec: appv1.StatefulSetSpec{
			Template: v1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{"kubectl.kubernetes.io/restartedAt": currTime},
				},
			},
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.RestartStatefulSet(ctx, testStatefulsetName, testNamespace)
	require.NoError(t, err)

	statefulSet, err := k8s.GetResource[appv1.StatefulSet](ctx, c, testStatefulsetName, testNamespace)
	require.NoError(t, err)

	restartTime, ok := statefulSet.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"]
	assert.True(t, ok)
	assert.Greater(t, restartTime, currTime)
}

func TestGetStatefulSetReplicaStatus(t *testing.T) {
	tests := []struct {
		name         string
		expectStatus pbresource.StatusCode
		rs           *appv1.StatefulSet
	}{
		{
			name:         "wrong ns",
			expectStatus: pbresource.StatusCode_NOT_FOUND,
			rs: &appv1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testStatefulsetName,
					Namespace:  "other",
					Generation: 2,
				},
				Status: appv1.StatefulSetStatus{
					Replicas:           3,
					AvailableReplicas:  1,
					ObservedGeneration: 2,
				},
			},
		},
		{
			name:         "wrong name",
			expectStatus: pbresource.StatusCode_NOT_FOUND,
			rs: &appv1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:       "other",
					Namespace:  testNamespace,
					Generation: 2,
				},
				Status: appv1.StatefulSetStatus{
					Replicas:           3,
					AvailableReplicas:  1,
					ObservedGeneration: 2,
				},
			},
		},
		{
			name:         "no set",
			expectStatus: pbresource.StatusCode_NOT_FOUND,
			rs:           nil,
		},
		{
			name:         "ObservedGeneration < Generation",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testStatefulsetName,
					Namespace:  testNamespace,
					Generation: 2,
				},
				Spec: appv1.StatefulSetSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.StatefulSetStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 1,
					UpdatedReplicas:    3,
				},
			},
		},
		{
			name:         "UpdatedReplicas < Spec.replicas",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testStatefulsetName,
					Namespace:  testNamespace,
					Generation: 2,
				},
				Spec: appv1.StatefulSetSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.StatefulSetStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    2,
				},
			},
		},
		{
			name:         "AvailableReplicas < UpdatedReplicas",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testStatefulsetName,
					Namespace:  testNamespace,
					Generation: 2,
				},
				Spec: appv1.StatefulSetSpec{
					Replicas: utils.Ptr[int32](2),
				},
				Status: appv1.StatefulSetStatus{
					Replicas:           3,
					AvailableReplicas:  2,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
				},
			},
		},
		{
			name:         "Ready",
			expectStatus: pbresource.StatusCode_READY,
			rs: &appv1.StatefulSet{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testStatefulsetName,
					Namespace:  testNamespace,
					Generation: 2,
				},
				Spec: appv1.StatefulSetSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.StatefulSetStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			t.Parallel()
			c := fake.NewClient()
			if test.rs != nil {
				c = fake.NewClient(test.rs)
			}
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}

			ctx := context.Background()
			status, err := p.GetStatefulSetReplicaStatus(ctx, testStatefulsetName, testNamespace)
			require.NoError(t, err)
			assert.Equal(t, test.expectStatus.String(), status.GetCode().String())
		})
	}
}
