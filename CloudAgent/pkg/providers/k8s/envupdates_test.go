package k8s

import (
	"context"
	"testing"

	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/controller-runtime/pkg/client"
	"sigs.k8s.io/controller-runtime/pkg/client/fake"

	utilruntime "k8s.io/apimachinery/pkg/util/runtime"
	clientgoscheme "k8s.io/client-go/kubernetes/scheme"

	pbcommonk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

var (
	scheme = runtime.NewScheme()
)

func init() {
	utilruntime.Must(clientgoscheme.AddToScheme(scheme))
	utilruntime.Must(rwv1alpha1.AddToScheme(scheme))
}

const (
	testRWNamespace = "rwc-namespace"
	testRWName      = "risingwave"
	dummyEnvName    = "envName"
	dummyEnvValue   = "envVal"
)

func getNodeGroup(ngName string) rwv1alpha1.RisingWaveNodeGroup {
	return rwv1alpha1.RisingWaveNodeGroup{
		Name: ngName,
		Template: rwv1alpha1.RisingWaveNodePodTemplate{
			Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
				RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
					Env: []v1.EnvVar{{
						Name:  "RISINGWAVE_CLOUD_UUID",
						Value: "123-123-123",
					}, {
						Name:  "MALLOC_CONF",
						Value: "prof:true,lg_prof_interval:-1,lg_prof_sample:20,prof_prefix:/var/mnt/coredump/heap",
					}, {
						Name:  "RW_PROMETHEUS_ENDPOINT",
						Value: "http://promproxy.promproxy/$(POD_NAMESPACE)",
					}, {
						Name:  "RW_TELEMETRY_TYPE",
						Value: "hosted",
					}, {
						Name:  "RISINGWAVE_CLOUD",
						Value: "1",
					}, {
						Name:  "RW_METRICS_LEVEL",
						Value: "1",
					}, {
						Name:  "ENABLE_TELEMETRY",
						Value: "true",
					}, {
						Name:  "RW_HEAP_PROFILING_DIR",
						Value: "/var/mnt/coredump/",
					}, {
						Name:  "DISABLE_DEFAULT_CREDENTIAL",
						Value: "true",
					}},
				},
			},
		},
	}
}

func getCli(isStandalone bool) client.WithWatch {
	rw := rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testRWName,
			Namespace: testRWNamespace,
		},
		Spec: rwv1alpha1.RisingWaveSpec{
			Components: rwv1alpha1.RisingWaveComponentsSpec{
				Frontend: rwv1alpha1.RisingWaveComponent{
					NodeGroups: []rwv1alpha1.RisingWaveNodeGroup{
						getNodeGroup("first"),
						getNodeGroup("second"),
						getNodeGroup("third"),
					},
				},
			},
		},
	}

	if isStandalone {
		rw = rwv1alpha1.RisingWave{
			ObjectMeta: metav1.ObjectMeta{
				Name:      testRWName,
				Namespace: testRWNamespace,
			},
			Spec: rwv1alpha1.RisingWaveSpec{
				Components: rwv1alpha1.RisingWaveComponentsSpec{
					Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
						Template: rwv1alpha1.RisingWaveNodePodTemplate{
							Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
								RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
									Env: []v1.EnvVar{{
										Name:  "RISINGWAVE_CLOUD_UUID",
										Value: "123-123-123-",
									}, {
										Name:  "MALLOC_CONF",
										Value: "prof:true,lg_prof_interval:-1,lg_prof_sample:20,prof_prefix:/var/mnt/coredump/heap",
									}, {
										Name:  "RW_PROMETHEUS_ENDPOINT",
										Value: "http://promproxy.promproxy/$(POD_NAMESPACE)",
									}, {
										Name:  "RW_TELEMETRY_TYPE",
										Value: "hosted",
									}, {
										Name:  "RISINGWAVE_CLOUD",
										Value: "1",
									}, {
										Name:  "RW_METRICS_LEVEL",
										Value: "1",
									}, {
										Name:  "ENABLE_TELEMETRY",
										Value: "true",
									}, {
										Name:  "RW_HEAP_PROFILING_DIR",
										Value: "/var/mnt/coredump/",
									}, {
										Name:  "DISABLE_DEFAULT_CREDENTIAL",
										Value: "true",
									}},
								},
							},
						},
					},
				},
			},
		}
	}

	c := fake.NewClientBuilder().
		WithObjects(&rw).
		WithScheme(scheme).
		Build()
	return c
}

func envsToKeys(envs []v1.EnvVar) []string {
	result := []string{}
	for _, env := range envs {
		result = append(result, env.Name)
	}
	return result
}

func TestUpdateAllEnvVars(t *testing.T) {
	t.Run("delete standalone", func(t *testing.T) {
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(true)}}
		envs := []string{"other-var", dummyEnvName}
		gotRW, err := p.DeleteRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nil, envs, nil, nil, nil, nil)
		assert.NoError(t, err)
		gotEnv := gotRW.Spec.Components.Standalone.Template.Spec.Env
		assert.NotContains(t, envsToKeys(gotEnv), []string{"other-var", dummyEnvName})
	})

	t.Run("add standalone", func(t *testing.T) {
		envs := []*pbcommonk8s.EnvVar{
			{Name: dummyEnvName, Value: "new-value"},
			{Name: "other-var", Value: "other-value"},
		}
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(true)}}
		gotRW, err := p.PutRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nil, envs, nil, nil, nil, nil)
		require.NoError(t, err)

		gotEnv := gotRW.Spec.Components.Standalone.Template.Spec.Env
		assert.Contains(t, gotEnv, v1.EnvVar{Name: dummyEnvName, Value: "new-value"})
		assert.Contains(t, gotEnv, v1.EnvVar{Name: "other-var", Value: "other-value"})
	})

	t.Run("delete frontend", func(t *testing.T) {
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(false)}}
		envs := []string{dummyEnvName}
		gotRW, err := p.DeleteRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nil, nil, nil, envs, nil, nil)
		require.NoError(t, err)

		for _, ng := range gotRW.Spec.Components.Frontend.NodeGroups {
			gotEnv := ng.Template.Spec.Env
			assert.NotContains(t, envsToKeys(gotEnv), dummyEnvName)
		}
	})

	t.Run("delete frontend other", func(t *testing.T) {
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(false)}}
		otherVarName := "other-var"
		envs := []string{otherVarName, dummyEnvName}
		gotRW, err := p.DeleteRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nil, nil, nil, envs, nil, nil)
		require.NoError(t, err)

		for _, ng := range gotRW.Spec.Components.Frontend.NodeGroups {
			gotEnv := ng.Template.Spec.Env
			assert.NotContains(t, envsToKeys(gotEnv), dummyEnvName)
			assert.NotContains(t, envsToKeys(gotEnv), otherVarName)
		}
	})

	t.Run("add frontend", func(t *testing.T) {
		envs := []*pbcommonk8s.EnvVar{
			{Name: dummyEnvName, Value: "new-value"},
			{Name: "other-var", Value: "other-value"},
		}
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(false)}}
		gotRW, err := p.PutRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nil, nil, nil, envs, nil, nil)
		require.NoError(t, err)
		for _, ng := range gotRW.Spec.Components.Frontend.NodeGroups {
			gotEnv := ng.Template.Spec.Env
			assert.Contains(t, gotEnv, v1.EnvVar{Name: dummyEnvName, Value: "new-value"})
			assert.Contains(t, gotEnv, v1.EnvVar{Name: "other-var", Value: "other-value"})
		}
	})
}

func TestUpdateSomeEnvVars(t *testing.T) {
	nodeGroups := []string{"first", "third"}

	t.Run("delete standalone", func(t *testing.T) {
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(true)}}
		envs := []string{"other-var", dummyEnvName}
		_, err := p.DeleteRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nodeGroups, envs, nil, nil, nil, nil)
		assert.Error(t, err)
	})

	t.Run("add standalone", func(t *testing.T) {
		envs := []*pbcommonk8s.EnvVar{
			{Name: dummyEnvName, Value: "new-value"},
			{Name: "other-var", Value: "other-value"},
		}
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(true)}}
		_, err := p.PutRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nodeGroups, envs, nil, nil, nil, nil)
		require.Error(t, err)
	})

	t.Run("delete frontend", func(t *testing.T) {
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(false)}}
		varName := "RISINGWAVE_CLOUD_UUID"
		envs := []string{varName}
		gotRW, err := p.DeleteRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nodeGroups, nil, nil, envs, nil, nil)
		require.NoError(t, err)

		gotEnv := gotRW.Spec.Components.Frontend.NodeGroups[0].Template.Spec.Env
		assert.NotContains(t, envsToKeys(gotEnv), varName)
		gotEnv = gotRW.Spec.Components.Frontend.NodeGroups[2].Template.Spec.Env
		assert.NotContains(t, envsToKeys(gotEnv), varName)

		gotEnv = gotRW.Spec.Components.Frontend.NodeGroups[1].Template.Spec.Env
		assert.Contains(t, gotEnv, v1.EnvVar{Name: varName, Value: "123-123-123"})
	})

	t.Run("delete frontend RISINGWAVE_CLOUD_UUID", func(t *testing.T) {
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(false)}}
		varName := "RISINGWAVE_CLOUD_UUID"
		envs := []string{varName, dummyEnvName}
		gotRW, err := p.DeleteRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nodeGroups, nil, nil, envs, nil, nil)
		require.NoError(t, err)

		gotEnv := gotRW.Spec.Components.Frontend.NodeGroups[0].Template.Spec.Env
		assert.NotContains(t, envsToKeys(gotEnv), dummyEnvName)
		assert.NotContains(t, envsToKeys(gotEnv), varName)

		gotEnv = gotRW.Spec.Components.Frontend.NodeGroups[2].Template.Spec.Env
		assert.NotContains(t, envsToKeys(gotEnv), dummyEnvName)
		assert.NotContains(t, envsToKeys(gotEnv), varName)

		gotEnv = gotRW.Spec.Components.Frontend.NodeGroups[1].Template.Spec.Env
		assert.Contains(t, envsToKeys(gotEnv), varName)
	})

	t.Run("add frontend", func(t *testing.T) {
		varName := "RISINGWAVE_CLOUD_UUID"
		envs := []*pbcommonk8s.EnvVar{
			{Name: varName, Value: "new-value"},
			{Name: "other-var", Value: "other-value"},
		}
		p := &Provider{kc: &k8s.KubernetesClient{Client: getCli(false)}}
		gotRW, err := p.PutRisingWaveEnvVar(context.Background(), testRWNamespace, testRWName, nodeGroups, nil, nil, envs, nil, nil)
		require.NoError(t, err)

		gotEnv := gotRW.Spec.Components.Frontend.NodeGroups[0].Template.Spec.Env
		assert.Contains(t, gotEnv, v1.EnvVar{Name: varName, Value: "new-value"})
		assert.Contains(t, gotEnv, v1.EnvVar{Name: "other-var", Value: "other-value"})
		gotEnv = gotRW.Spec.Components.Frontend.NodeGroups[2].Template.Spec.Env
		assert.Contains(t, gotEnv, v1.EnvVar{Name: varName, Value: "new-value"})
		assert.Contains(t, gotEnv, v1.EnvVar{Name: "other-var", Value: "other-value"})

		gotEnv = gotRW.Spec.Components.Frontend.NodeGroups[1].Template.Spec.Env
		assert.NotContains(t, gotEnv, v1.EnvVar{Name: "other-var", Value: "other-value"})
	})
}
