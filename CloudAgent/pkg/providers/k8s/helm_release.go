package k8s

import (
	"context"
	"time"

	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/release"

	pbcommonk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbtask "github.com/risingwavelabs/cloudagent/pbgen/task"
	pbtaskhelm "github.com/risingwavelabs/cloudagent/pbgen/task/helm"
	"github.com/risingwavelabs/cloudagent/pkg/helmx"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	installReleaseTimeout   = 1 * time.Minute
	upgradeReleaseTimeout   = 1 * time.Minute
	getReleaseTimeout       = 1 * time.Minute
	uninstallReleaseTimeout = 1 * time.Minute
)

var (
	ErrInvalidChartURL = eris.New("invalid chart URL")
)

type InstallHelmReleaseOption struct {
	TaskID        string
	TaskNamespace string
	ResourceID    string
	Namespace     string
	ChartURL      string
	ValuesJSON    string
}

func (p *Provider) InstallHelmRelease(ctx context.Context, option InstallHelmReleaseOption) error {
	if len(option.ChartURL) == 0 {
		return eris.Errorf("chartURL cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.Namespace) == 0 {
		return eris.Errorf("namespace cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.ResourceID) == 0 {
		return eris.Errorf("resource ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.ValuesJSON) == 0 {
		return eris.Errorf("values cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.TaskID) == 0 {
		return eris.Errorf("task ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	return p.kc.StartTaskRunnerWithOverrides(
		ctx,
		option.TaskID,
		option.TaskNamespace,
		&pbtask.Task{
			Task: &pbtask.Task_InstallHelmReleaseTask{
				InstallHelmReleaseTask: &pbtaskhelm.InstallReleaseTask{
					ReleaseName:      option.ResourceID,
					ReleaseNamespace: option.Namespace,
					ValuesJson:       option.ValuesJSON,
					ChartUrl:         option.ChartURL,
				},
			},
		},
		k8s.TaskRunnerOverrides{
			Envs: utils.Ptr(helmx.GetDirectoriesEnvVars(k8s.TaskEphemeralVolumePath)),
		},
	)
}

type UpgradeHelmReleaseOption struct {
	TaskID        string
	TaskNamespace string
	ResourceID    string
	Namespace     string
	ChartURL      string
	ValuesJSON    string
	Install       bool
}

func (p *Provider) UpgradeHelmRelease(ctx context.Context, option UpgradeHelmReleaseOption) error {
	if len(option.Namespace) == 0 {
		return eris.Errorf("chartURL cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.ResourceID) == 0 {
		return eris.Errorf("resource ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.ValuesJSON) == 0 {
		return eris.Errorf("values cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.TaskID) == 0 {
		return eris.Errorf("task ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.ChartURL) == 0 {
		return eris.Errorf("chart URL cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	return p.kc.StartTaskRunnerWithOverrides(
		ctx,
		option.TaskID,
		option.TaskNamespace,
		&pbtask.Task{
			Task: &pbtask.Task_UpgradeHelmReleaseTask{
				UpgradeHelmReleaseTask: &pbtaskhelm.UpgradeReleaseTask{
					ReleaseName:      option.ResourceID,
					ReleaseNamespace: option.Namespace,
					ValuesJson:       option.ValuesJSON,
					ChartUrl:         option.ChartURL,
					Install:          option.Install,
				},
			},
		},
		k8s.TaskRunnerOverrides{
			Envs: utils.Ptr(helmx.GetDirectoriesEnvVars(k8s.TaskEphemeralVolumePath)),
		},
	)
}

type UninstallHelmReleaseOption struct {
	TaskID        string
	TaskNamespace string
	ResourceID    string
	Namespace     string
}

func (p *Provider) UninstallHelmRelease(ctx context.Context, option UninstallHelmReleaseOption) error {
	if len(option.Namespace) == 0 {
		return eris.Errorf("chartURL cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.ResourceID) == 0 {
		return eris.Errorf("resource ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	if len(option.TaskID) == 0 {
		return eris.Errorf("task ID cannot be empty").WithCode(eris.CodeInvalidArgument)
	}
	return p.kc.StartTaskRunnerWithOverrides(
		ctx,
		option.TaskID,
		option.TaskNamespace,
		&pbtask.Task{
			Task: &pbtask.Task_UninstallHelmReleaseTask{
				UninstallHelmReleaseTask: &pbtaskhelm.UninstallReleaseTask{
					ReleaseName:      option.ResourceID,
					ReleaseNamespace: option.Namespace,
				},
			},
		},
		k8s.TaskRunnerOverrides{
			Envs: utils.Ptr(helmx.GetDirectoriesEnvVars(k8s.TaskEphemeralVolumePath)),
		},
	)
}

func (p *Provider) GetHelmRelease(ctx context.Context, resourceID, namespace string) (*pbcommonk8s.HelmRelease, error) {
	meta, err := p.helmSvc.Get(ctx, helmx.GetOption{
		ReleaseName: resourceID,
		Namespace:   namespace,
		Timeout:     utils.Ptr(uninstallReleaseTimeout),
	})
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get helm release, resource ID: %s, namespace: %s", resourceID, namespace)
	}
	s := pbcommonk8s.HelmReleaseStatus_UNKNOWN
	if meta.Info != nil {
		switch meta.Info.Status {
		case release.StatusUninstalling:
			s = pbcommonk8s.HelmReleaseStatus_UNINSTALLING
		case release.StatusDeployed:
			s = pbcommonk8s.HelmReleaseStatus_DEPLOYED
		case release.StatusFailed:
			s = pbcommonk8s.HelmReleaseStatus_FAILED
		case release.StatusUninstalled:
			s = pbcommonk8s.HelmReleaseStatus_UNINSTALLED
		case release.StatusPendingUpgrade:
			s = pbcommonk8s.HelmReleaseStatus_UPGRADING
		case release.StatusPendingInstall:
			s = pbcommonk8s.HelmReleaseStatus_INSTALLING
		case release.StatusSuperseded, release.StatusPendingRollback, release.StatusUnknown:
		}
	}
	return &pbcommonk8s.HelmRelease{
		ReleaseName: meta.Name,
		Namespace:   meta.Namespace,
		Status:      s,
		Version:     utils.Unwrap(utils.Unwrap(meta.Chart).Metadata).Version,
	}, nil
}
