package k8s

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	appv1 "k8s.io/api/apps/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	testDeplNamespace = "test-namespace"
	testDeplName      = "test-deployment"
)

func TestRestartDeployment(t *testing.T) {
	currTime := time.Now().Add(time.Duration(-1) * time.Minute).Format(time.RFC3339)
	c := fake.NewClient(&appv1.Deployment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testDeplName,
			Namespace: testDeplNamespace,
		},
		Spec: appv1.DeploymentSpec{
			Template: v1.PodTemplateSpec{
				ObjectMeta: metav1.ObjectMeta{
					Annotations: map[string]string{"kubectl.kubernetes.io/restartedAt": currTime},
				},
			},
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.RestartDeployment(ctx, testDeplName, testDeplNamespace)
	require.NoError(t, err)

	depl, err := k8s.GetResource[appv1.Deployment](ctx, c, testDeplName, testDeplNamespace)
	require.NoError(t, err)

	restartTime, ok := depl.Spec.Template.Annotations["kubectl.kubernetes.io/restartedAt"]
	assert.True(t, ok)
	assert.Greater(t, restartTime, currTime)
}

func TestGetDeploymentReplicasStatus(t *testing.T) {
	tests := []struct {
		name         string
		expectStatus pbresource.StatusCode
		rs           *appv1.Deployment
	}{
		{
			name:         "No deployment",
			expectStatus: pbresource.StatusCode_NOT_FOUND,
			rs:           nil,
		},
		{
			name:         "Wrong ns",
			expectStatus: pbresource.StatusCode_NOT_FOUND,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  "other",
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "Wrong name",
			expectStatus: pbresource.StatusCode_NOT_FOUND,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       "other",
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "ProgressDeadlineExceeded",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentProgressing,
							Reason: "ProgressDeadlineExceeded",
						}, {
							Type:   appv1.DeploymentReplicaFailure,
							Reason: "ProgressDeadlineExceeded",
						}, {
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "Observed generation < generation",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 1,
					UpdatedReplicas:    3,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "UpdatedReplicas < Spec.Replicas",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           2,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    2,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "UpdatedReplicas < Status.Replicas",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](2),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    2,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "AvailableReplicas < UpdatedReplicas",
			expectStatus: pbresource.StatusCode_NOT_READY,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](2),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  2,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
		{
			name:         "Ready",
			expectStatus: pbresource.StatusCode_READY,
			rs: &appv1.Deployment{
				ObjectMeta: metav1.ObjectMeta{
					Name:       testDeplName,
					Namespace:  testDeplNamespace,
					Generation: 2,
				},
				Spec: appv1.DeploymentSpec{
					Replicas: utils.Ptr[int32](3),
				},
				Status: appv1.DeploymentStatus{
					Replicas:           3,
					AvailableReplicas:  3,
					ObservedGeneration: 2,
					UpdatedReplicas:    3,
					Conditions: []appv1.DeploymentCondition{
						{
							Type:   appv1.DeploymentAvailable,
							Reason: "SomeHappyMessage",
						},
					},
				},
			},
		},
	}

	for _, test := range tests {
		t.Run(test.name, func(t *testing.T) {
			c := fake.NewClient()
			if test.rs != nil {
				c = fake.NewClient(test.rs)
			}
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}

			ctx := context.Background()
			status, err := p.GetDeploymentReplicasStatus(ctx, testDeplName, testDeplNamespace)
			require.NoError(t, err)
			assert.Equal(t, test.expectStatus.String(), status.GetCode().String())
		})
	}
}
