package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateNamespace(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreateNamespace(ctx, "name", nil)
	require.NoError(t, err)

	secret, err := k8s.GetResource[corev1.Namespace](ctx, c, "name", "")
	require.NoError(t, err)
	assert.Equal(t, "name", secret.Name)

	err = p.CreateNamespace(ctx, "name", nil)
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestCreateNamespace_with_labels(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	var (
		ns     = "name"
		labels = map[string]string{
			"k1": "v1",
			"k2": "v2",
		}
	)

	ctx := context.Background()
	err := p.CreateNamespace(ctx, ns, labels)
	require.NoError(t, err)

	secret, err := k8s.GetResource[corev1.Namespace](ctx, c, ns, "")
	require.NoError(t, err)
	assert.Equal(t, ns, secret.Name)
	assert.Equal(t, labels, secret.Labels)
}

func TestGetNamespace(t *testing.T) {
	c := fake.NewClient(&corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "name",
		},
		Status: corev1.NamespaceStatus{
			Phase: corev1.NamespaceActive,
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	status, err := p.GetNamespace(ctx, "name")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, status)

	status, err = p.GetNamespace(ctx, "name-non-existent")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_NOT_FOUND,
	}, status)
}

func TestGetNamespaceNotReady(t *testing.T) {
	c := fake.NewClient(&corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "name",
		},
		Status: corev1.NamespaceStatus{
			Phase: corev1.NamespaceTerminating,
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	status, err := p.GetNamespace(ctx, "name")
	require.NoError(t, err)
	assert.Equal(t, pbresource.StatusCode_NOT_READY, status.GetCode())
}

func TestGetNamespaceNotFound(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	status, err := p.GetNamespace(ctx, "name")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_NOT_FOUND,
	}, status)
}

func TestDeleteNamespace(t *testing.T) {
	c := fake.NewClient(&corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "name",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.DeleteNamespace(ctx, "name")
	require.NoError(t, err)
	err = p.DeleteNamespace(ctx, "name")
	assert.True(t, utils.IsErrNotFound(err))
}

func TestLabelNamespace(t *testing.T) {
	c := fake.NewClient(&corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "name",
			Labels: map[string]string{
				"foo": "bar",
				"bar": "foo",
			},
		},
		Status: corev1.NamespaceStatus{
			Phase: corev1.NamespaceActive,
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.LabelNamespace(ctx, "name", map[string]string{
		"foo": "baz",
		"baz": "bar",
	})
	require.NoError(t, err)

	ns, err := k8s.GetResource[corev1.Namespace](ctx, c, "name", "")
	require.NoError(t, err)
	assert.Equal(t, map[string]string{
		"foo": "baz",
		"bar": "foo",
		"baz": "bar",
	}, ns.Labels)

	err = p.LabelNamespace(ctx, "namenonexistent", map[string]string{
		"foo": "baz",
		"baz": "bar",
	})
	assert.Equal(t, eris.CodeNotFound, eris.GetCode(err))
}
