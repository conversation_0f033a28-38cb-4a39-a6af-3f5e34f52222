package k8s

import (
	"context"

	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"
)

func (p *Provider) GetPodPhases(ctx context.Context, ns string) (map[string]corev1.PodPhase, error) {
	pods, err := p.ListPods(ctx, ns)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to list pods in namespace %v", ns)
	}
	var phases = make(map[string]corev1.PodPhase)
	for _, pod := range pods {
		phases[pod.Name] = pod.Status.Phase
	}
	return phases, nil
}

func (p *Provider) ListPods(ctx context.Context, ns string) ([]corev1.Pod, error) {
	var podList = &corev1.PodList{}
	err := p.kc.List(ctx, podList, client.InNamespace(ns))
	if err != nil {
		return nil, err
	}
	return podList.Items, nil
}
