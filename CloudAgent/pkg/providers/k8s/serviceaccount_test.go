package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateServiceAccount(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreateServiceAccount(ctx, "name", "ns")
	require.NoError(t, err)

	secret, err := k8s.GetResource[corev1.ServiceAccount](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", secret.Name)
	assert.Equal(t, "ns", secret.Namespace)

	err = p.CreateServiceAccount(ctx, "name", "ns")
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestGetServiceAccount(t *testing.T) {
	c := fake.NewClient(&corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	status, err := p.GetServiceAccount(ctx, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, status)

	status, err = p.GetServiceAccount(ctx, "name-non-existent", "ns")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_NOT_FOUND,
	}, status)
}

func TestDeleteServiceAccount(t *testing.T) {
	c := fake.NewClient(&corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.DeleteServiceAccount(ctx, "name", "ns")
	require.NoError(t, err)
	err = p.DeleteServiceAccount(ctx, "name", "ns")
	assert.True(t, utils.IsErrNotFound(err))
}

func TestAnnotateServiceAccount(t *testing.T) {
	c := fake.NewClient(&corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.AnnotateServiceAccount(ctx, "name", "ns", map[string]string{
		"key": "val",
	})
	require.NoError(t, err)
	saObj := corev1.ServiceAccount{}
	saKey := client.ObjectKey{
		Name:      "name",
		Namespace: "ns",
	}
	require.NoError(t, c.Get(ctx, saKey, &saObj))
	val, ok := saObj.Annotations["key"]
	require.True(t, ok)
	assert.Equal(t, "val", val)
}
