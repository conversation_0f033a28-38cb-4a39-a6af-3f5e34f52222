package k8s

import (
	"context"

	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"

	pbgmp "github.com/risingwavelabs/cloudagent/pbgen/common/gmp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

type CreatePodMonitoringOption struct {
	ResourceID        string
	Namespace         string
	Labels            map[string]string
	Annotations       map[string]string
	PodMonitoringSpec *pbgmp.PodMonitoringSpec
}

func toMapStringInterface(from map[string]string) map[string]interface{} {
	to := make(map[string]interface{}, len(from))
	for k, v := range from {
		to[k] = v
	}
	return to
}

func (p *Provider) CreatePodMonitoring(ctx context.Context, option CreatePodMonitoringOption) error {
	if option.PodMonitoringSpec == nil {
		return eris.Errorf("PodMonitoringSpec should not be nil").WithCode(eris.CodeInvalidArgument)
	}
	pmSpec, err := conversion.FromPodMonitoringSpecProto(option.PodMonitoringSpec)
	if err != nil {
		return eris.Errorf("failed to convert service monitor spec proto: %v", option.PodMonitoringSpec).WithCode(eris.CodeInvalidArgument)
	}
	obj := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "monitoring.googleapis.com/v1",
			"kind":       "PodMonitoring",
			"metadata": map[string]interface{}{
				"name":        option.ResourceID,
				"namespace":   option.Namespace,
				"labels":      toMapStringInterface(option.Labels),
				"annotations": toMapStringInterface(option.Annotations),
			},
			"spec": pmSpec,
		},
	}
	gvr := schema.GroupVersionKind{
		Group:   "monitoring.googleapis.com",
		Version: "v1",
		Kind:    "PodMonitoring",
	}
	obj.SetGroupVersionKind(gvr)
	_, err = p.kc.DynamicInterface.Resource(schema.GroupVersionResource{
		Group:    "monitoring.googleapis.com",
		Version:  "v1",
		Resource: "podmonitorings",
	}).Namespace(option.Namespace).Create(ctx, obj, metav1.CreateOptions{})

	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("service monitor %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create service monitor %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) DeletePodMonitoring(ctx context.Context, resourceID, namespace string) error {
	err := p.kc.DynamicInterface.Resource(schema.GroupVersionResource{
		Group:    "monitoring.googleapis.com",
		Version:  "v1",
		Resource: "podmonitorings",
	}).Namespace(namespace).Delete(ctx, resourceID, metav1.DeleteOptions{})
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("service monitor %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete service monitor %s", resourceID)
	}
	return nil
}
