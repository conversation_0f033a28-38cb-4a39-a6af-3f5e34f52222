// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/providers/k8s (interfaces: ProviderInterface)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/k8s -package=k8s -destination=pkg/providers/k8s/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/k8s ProviderInterface
//

// Package k8s is a generated GoMock package.
package k8s

import (
	context "context"
	reflect "reflect"
	time "time"

	k8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	resource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	rw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	k8s0 "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	v1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	gomock "go.uber.org/mock/gomock"
	v1 "k8s.io/api/core/v1"
)

// MockProviderInterface is a mock of ProviderInterface interface.
type MockProviderInterface struct {
	ctrl     *gomock.Controller
	recorder *MockProviderInterfaceMockRecorder
	isgomock struct{}
}

// MockProviderInterfaceMockRecorder is the mock recorder for MockProviderInterface.
type MockProviderInterfaceMockRecorder struct {
	mock *MockProviderInterface
}

// NewMockProviderInterface creates a new mock instance.
func NewMockProviderInterface(ctrl *gomock.Controller) *MockProviderInterface {
	mock := &MockProviderInterface{ctrl: ctrl}
	mock.recorder = &MockProviderInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockProviderInterface) EXPECT() *MockProviderInterfaceMockRecorder {
	return m.recorder
}

// AnnotateServiceAccount mocks base method.
func (m *MockProviderInterface) AnnotateServiceAccount(ctx context.Context, resourceID, namespace string, labels map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "AnnotateServiceAccount", ctx, resourceID, namespace, labels)
	ret0, _ := ret[0].(error)
	return ret0
}

// AnnotateServiceAccount indicates an expected call of AnnotateServiceAccount.
func (mr *MockProviderInterfaceMockRecorder) AnnotateServiceAccount(ctx, resourceID, namespace, labels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "AnnotateServiceAccount", reflect.TypeOf((*MockProviderInterface)(nil).AnnotateServiceAccount), ctx, resourceID, namespace, labels)
}

// CreateAzureServiceMonitor mocks base method.
func (m *MockProviderInterface) CreateAzureServiceMonitor(ctx context.Context, option CreateServiceMonitorOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateAzureServiceMonitor", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateAzureServiceMonitor indicates an expected call of CreateAzureServiceMonitor.
func (mr *MockProviderInterfaceMockRecorder) CreateAzureServiceMonitor(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateAzureServiceMonitor", reflect.TypeOf((*MockProviderInterface)(nil).CreateAzureServiceMonitor), ctx, option)
}

// CreateConfigMap mocks base method.
func (m *MockProviderInterface) CreateConfigMap(ctx context.Context, option CreateConfigMapOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateConfigMap", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateConfigMap indicates an expected call of CreateConfigMap.
func (mr *MockProviderInterfaceMockRecorder) CreateConfigMap(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateConfigMap", reflect.TypeOf((*MockProviderInterface)(nil).CreateConfigMap), ctx, option)
}

// CreateNamespace mocks base method.
func (m *MockProviderInterface) CreateNamespace(ctx context.Context, resourceID string, labels map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNamespace", ctx, resourceID, labels)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNamespace indicates an expected call of CreateNamespace.
func (mr *MockProviderInterfaceMockRecorder) CreateNamespace(ctx, resourceID, labels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNamespace", reflect.TypeOf((*MockProviderInterface)(nil).CreateNamespace), ctx, resourceID, labels)
}

// CreateNetworkPolicy mocks base method.
func (m *MockProviderInterface) CreateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateNetworkPolicy", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateNetworkPolicy indicates an expected call of CreateNetworkPolicy.
func (mr *MockProviderInterfaceMockRecorder) CreateNetworkPolicy(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateNetworkPolicy", reflect.TypeOf((*MockProviderInterface)(nil).CreateNetworkPolicy), ctx, option)
}

// CreateOrUpdateNetworkPolicy mocks base method.
func (m *MockProviderInterface) CreateOrUpdateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateOrUpdateNetworkPolicy", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateOrUpdateNetworkPolicy indicates an expected call of CreateOrUpdateNetworkPolicy.
func (mr *MockProviderInterfaceMockRecorder) CreateOrUpdateNetworkPolicy(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateOrUpdateNetworkPolicy", reflect.TypeOf((*MockProviderInterface)(nil).CreateOrUpdateNetworkPolicy), ctx, option)
}

// CreatePersistentVolumeClaim mocks base method.
func (m *MockProviderInterface) CreatePersistentVolumeClaim(ctx context.Context, option CreatePVCOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePersistentVolumeClaim", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePersistentVolumeClaim indicates an expected call of CreatePersistentVolumeClaim.
func (mr *MockProviderInterfaceMockRecorder) CreatePersistentVolumeClaim(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePersistentVolumeClaim", reflect.TypeOf((*MockProviderInterface)(nil).CreatePersistentVolumeClaim), ctx, option)
}

// CreatePodMonitoring mocks base method.
func (m *MockProviderInterface) CreatePodMonitoring(ctx context.Context, option CreatePodMonitoringOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePodMonitoring", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePodMonitoring indicates an expected call of CreatePodMonitoring.
func (mr *MockProviderInterfaceMockRecorder) CreatePodMonitoring(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePodMonitoring", reflect.TypeOf((*MockProviderInterface)(nil).CreatePodMonitoring), ctx, option)
}

// CreatePostgreSQL mocks base method.
func (m *MockProviderInterface) CreatePostgreSQL(ctx context.Context, option CreatePostgreSQLOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreatePostgreSQL", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreatePostgreSQL indicates an expected call of CreatePostgreSQL.
func (mr *MockProviderInterfaceMockRecorder) CreatePostgreSQL(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreatePostgreSQL", reflect.TypeOf((*MockProviderInterface)(nil).CreatePostgreSQL), ctx, option)
}

// CreateRisingWave mocks base method.
func (m *MockProviderInterface) CreateRisingWave(ctx context.Context, option CreateRisingWaveOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRisingWave", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRisingWave indicates an expected call of CreateRisingWave.
func (mr *MockProviderInterfaceMockRecorder) CreateRisingWave(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRisingWave", reflect.TypeOf((*MockProviderInterface)(nil).CreateRisingWave), ctx, option)
}

// CreateRisingWaveNodeGroup mocks base method.
func (m *MockProviderInterface) CreateRisingWaveNodeGroup(ctx context.Context, option CreateRisingWaveNodeGroupOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateRisingWaveNodeGroup", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateRisingWaveNodeGroup indicates an expected call of CreateRisingWaveNodeGroup.
func (mr *MockProviderInterfaceMockRecorder) CreateRisingWaveNodeGroup(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateRisingWaveNodeGroup", reflect.TypeOf((*MockProviderInterface)(nil).CreateRisingWaveNodeGroup), ctx, option)
}

// CreateSecret mocks base method.
func (m *MockProviderInterface) CreateSecret(ctx context.Context, option CreateSecretOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateSecret", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateSecret indicates an expected call of CreateSecret.
func (mr *MockProviderInterfaceMockRecorder) CreateSecret(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateSecret", reflect.TypeOf((*MockProviderInterface)(nil).CreateSecret), ctx, option)
}

// CreateService mocks base method.
func (m *MockProviderInterface) CreateService(ctx context.Context, option CreateServiceOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateService", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateService indicates an expected call of CreateService.
func (mr *MockProviderInterfaceMockRecorder) CreateService(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateService", reflect.TypeOf((*MockProviderInterface)(nil).CreateService), ctx, option)
}

// CreateServiceAccount mocks base method.
func (m *MockProviderInterface) CreateServiceAccount(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServiceAccount", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateServiceAccount indicates an expected call of CreateServiceAccount.
func (mr *MockProviderInterfaceMockRecorder) CreateServiceAccount(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceAccount", reflect.TypeOf((*MockProviderInterface)(nil).CreateServiceAccount), ctx, resourceID, namespace)
}

// CreateServiceMonitor mocks base method.
func (m *MockProviderInterface) CreateServiceMonitor(ctx context.Context, option CreateServiceMonitorOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "CreateServiceMonitor", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// CreateServiceMonitor indicates an expected call of CreateServiceMonitor.
func (mr *MockProviderInterfaceMockRecorder) CreateServiceMonitor(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "CreateServiceMonitor", reflect.TypeOf((*MockProviderInterface)(nil).CreateServiceMonitor), ctx, option)
}

// DeleteAzureServiceMonitor mocks base method.
func (m *MockProviderInterface) DeleteAzureServiceMonitor(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteAzureServiceMonitor", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteAzureServiceMonitor indicates an expected call of DeleteAzureServiceMonitor.
func (mr *MockProviderInterfaceMockRecorder) DeleteAzureServiceMonitor(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteAzureServiceMonitor", reflect.TypeOf((*MockProviderInterface)(nil).DeleteAzureServiceMonitor), ctx, resourceID, namespace)
}

// DeleteConfigMap mocks base method.
func (m *MockProviderInterface) DeleteConfigMap(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteConfigMap", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteConfigMap indicates an expected call of DeleteConfigMap.
func (mr *MockProviderInterfaceMockRecorder) DeleteConfigMap(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteConfigMap", reflect.TypeOf((*MockProviderInterface)(nil).DeleteConfigMap), ctx, resourceID, namespace)
}

// DeleteNamespace mocks base method.
func (m *MockProviderInterface) DeleteNamespace(ctx context.Context, resourceID string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNamespace", ctx, resourceID)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNamespace indicates an expected call of DeleteNamespace.
func (mr *MockProviderInterfaceMockRecorder) DeleteNamespace(ctx, resourceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNamespace", reflect.TypeOf((*MockProviderInterface)(nil).DeleteNamespace), ctx, resourceID)
}

// DeleteNetworkPolicy mocks base method.
func (m *MockProviderInterface) DeleteNetworkPolicy(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteNetworkPolicy", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteNetworkPolicy indicates an expected call of DeleteNetworkPolicy.
func (mr *MockProviderInterfaceMockRecorder) DeleteNetworkPolicy(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteNetworkPolicy", reflect.TypeOf((*MockProviderInterface)(nil).DeleteNetworkPolicy), ctx, resourceID, namespace)
}

// DeletePersistentVolumeClaims mocks base method.
func (m *MockProviderInterface) DeletePersistentVolumeClaims(ctx context.Context, ns string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePersistentVolumeClaims", ctx, ns)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePersistentVolumeClaims indicates an expected call of DeletePersistentVolumeClaims.
func (mr *MockProviderInterfaceMockRecorder) DeletePersistentVolumeClaims(ctx, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePersistentVolumeClaims", reflect.TypeOf((*MockProviderInterface)(nil).DeletePersistentVolumeClaims), ctx, ns)
}

// DeletePodMonitoring mocks base method.
func (m *MockProviderInterface) DeletePodMonitoring(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePodMonitoring", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePodMonitoring indicates an expected call of DeletePodMonitoring.
func (mr *MockProviderInterfaceMockRecorder) DeletePodMonitoring(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePodMonitoring", reflect.TypeOf((*MockProviderInterface)(nil).DeletePodMonitoring), ctx, resourceID, namespace)
}

// DeletePostgreSQL mocks base method.
func (m *MockProviderInterface) DeletePostgreSQL(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeletePostgreSQL", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeletePostgreSQL indicates an expected call of DeletePostgreSQL.
func (mr *MockProviderInterfaceMockRecorder) DeletePostgreSQL(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeletePostgreSQL", reflect.TypeOf((*MockProviderInterface)(nil).DeletePostgreSQL), ctx, resourceID, namespace)
}

// DeleteRisingWave mocks base method.
func (m *MockProviderInterface) DeleteRisingWave(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRisingWave", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRisingWave indicates an expected call of DeleteRisingWave.
func (mr *MockProviderInterfaceMockRecorder) DeleteRisingWave(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWave", reflect.TypeOf((*MockProviderInterface)(nil).DeleteRisingWave), ctx, resourceID, namespace)
}

// DeleteRisingWaveEnvVar mocks base method.
func (m *MockProviderInterface) DeleteRisingWaveEnvVar(ctx context.Context, namespace, name string, groups, standalone, meta, frontend, compute, compactor []string) (*v1alpha1.RisingWave, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRisingWaveEnvVar", ctx, namespace, name, groups, standalone, meta, frontend, compute, compactor)
	ret0, _ := ret[0].(*v1alpha1.RisingWave)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// DeleteRisingWaveEnvVar indicates an expected call of DeleteRisingWaveEnvVar.
func (mr *MockProviderInterfaceMockRecorder) DeleteRisingWaveEnvVar(ctx, namespace, name, groups, standalone, meta, frontend, compute, compactor any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWaveEnvVar", reflect.TypeOf((*MockProviderInterface)(nil).DeleteRisingWaveEnvVar), ctx, namespace, name, groups, standalone, meta, frontend, compute, compactor)
}

// DeleteRisingWaveNodeGroup mocks base method.
func (m *MockProviderInterface) DeleteRisingWaveNodeGroup(ctx context.Context, option DeleteRisingWaveNodeGroupOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteRisingWaveNodeGroup", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteRisingWaveNodeGroup indicates an expected call of DeleteRisingWaveNodeGroup.
func (mr *MockProviderInterfaceMockRecorder) DeleteRisingWaveNodeGroup(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteRisingWaveNodeGroup", reflect.TypeOf((*MockProviderInterface)(nil).DeleteRisingWaveNodeGroup), ctx, option)
}

// DeleteSecret mocks base method.
func (m *MockProviderInterface) DeleteSecret(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteSecret", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteSecret indicates an expected call of DeleteSecret.
func (mr *MockProviderInterfaceMockRecorder) DeleteSecret(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteSecret", reflect.TypeOf((*MockProviderInterface)(nil).DeleteSecret), ctx, resourceID, namespace)
}

// DeleteServiceAccount mocks base method.
func (m *MockProviderInterface) DeleteServiceAccount(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceAccount", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceAccount indicates an expected call of DeleteServiceAccount.
func (mr *MockProviderInterfaceMockRecorder) DeleteServiceAccount(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceAccount", reflect.TypeOf((*MockProviderInterface)(nil).DeleteServiceAccount), ctx, resourceID, namespace)
}

// DeleteServiceMonitor mocks base method.
func (m *MockProviderInterface) DeleteServiceMonitor(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "DeleteServiceMonitor", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// DeleteServiceMonitor indicates an expected call of DeleteServiceMonitor.
func (mr *MockProviderInterfaceMockRecorder) DeleteServiceMonitor(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "DeleteServiceMonitor", reflect.TypeOf((*MockProviderInterface)(nil).DeleteServiceMonitor), ctx, resourceID, namespace)
}

// GetClusterAccess mocks base method.
func (m *MockProviderInterface) GetClusterAccess(ctx context.Context, timeout time.Duration) (ClusterAccessInfo, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetClusterAccess", ctx, timeout)
	ret0, _ := ret[0].(ClusterAccessInfo)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetClusterAccess indicates an expected call of GetClusterAccess.
func (mr *MockProviderInterfaceMockRecorder) GetClusterAccess(ctx, timeout any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetClusterAccess", reflect.TypeOf((*MockProviderInterface)(nil).GetClusterAccess), ctx, timeout)
}

// GetConfigMap mocks base method.
func (m *MockProviderInterface) GetConfigMap(ctx context.Context, resourceID, namespace string) (ConfigMapMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetConfigMap", ctx, resourceID, namespace)
	ret0, _ := ret[0].(ConfigMapMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetConfigMap indicates an expected call of GetConfigMap.
func (mr *MockProviderInterfaceMockRecorder) GetConfigMap(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetConfigMap", reflect.TypeOf((*MockProviderInterface)(nil).GetConfigMap), ctx, resourceID, namespace)
}

// GetDeploymentReplicasStatus mocks base method.
func (m *MockProviderInterface) GetDeploymentReplicasStatus(ctx context.Context, resourceID, namespace string) (*resource.Status, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetDeploymentReplicasStatus", ctx, resourceID, namespace)
	ret0, _ := ret[0].(*resource.Status)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetDeploymentReplicasStatus indicates an expected call of GetDeploymentReplicasStatus.
func (mr *MockProviderInterfaceMockRecorder) GetDeploymentReplicasStatus(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetDeploymentReplicasStatus", reflect.TypeOf((*MockProviderInterface)(nil).GetDeploymentReplicasStatus), ctx, resourceID, namespace)
}

// GetHelmRelease mocks base method.
func (m *MockProviderInterface) GetHelmRelease(arg0 context.Context, resourceID, namespace string) (*k8s.HelmRelease, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetHelmRelease", arg0, resourceID, namespace)
	ret0, _ := ret[0].(*k8s.HelmRelease)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetHelmRelease indicates an expected call of GetHelmRelease.
func (mr *MockProviderInterfaceMockRecorder) GetHelmRelease(arg0, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetHelmRelease", reflect.TypeOf((*MockProviderInterface)(nil).GetHelmRelease), arg0, resourceID, namespace)
}

// GetNamespace mocks base method.
func (m *MockProviderInterface) GetNamespace(ctx context.Context, resourceID string) (*resource.Status, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetNamespace", ctx, resourceID)
	ret0, _ := ret[0].(*resource.Status)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetNamespace indicates an expected call of GetNamespace.
func (mr *MockProviderInterfaceMockRecorder) GetNamespace(ctx, resourceID any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetNamespace", reflect.TypeOf((*MockProviderInterface)(nil).GetNamespace), ctx, resourceID)
}

// GetPersistenVolumeClaims mocks base method.
func (m *MockProviderInterface) GetPersistenVolumeClaims(ctx context.Context, ns string) (*PersistentVolumeClaimsMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPersistenVolumeClaims", ctx, ns)
	ret0, _ := ret[0].(*PersistentVolumeClaimsMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPersistenVolumeClaims indicates an expected call of GetPersistenVolumeClaims.
func (mr *MockProviderInterfaceMockRecorder) GetPersistenVolumeClaims(ctx, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPersistenVolumeClaims", reflect.TypeOf((*MockProviderInterface)(nil).GetPersistenVolumeClaims), ctx, ns)
}

// GetPodPhases mocks base method.
func (m *MockProviderInterface) GetPodPhases(ctx context.Context, ns string) (map[string]v1.PodPhase, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPodPhases", ctx, ns)
	ret0, _ := ret[0].(map[string]v1.PodPhase)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPodPhases indicates an expected call of GetPodPhases.
func (mr *MockProviderInterfaceMockRecorder) GetPodPhases(ctx, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPodPhases", reflect.TypeOf((*MockProviderInterface)(nil).GetPodPhases), ctx, ns)
}

// GetPostgreSQL mocks base method.
func (m *MockProviderInterface) GetPostgreSQL(ctx context.Context, resourceID, namespace string) (*PostgreSQLMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetPostgreSQL", ctx, resourceID, namespace)
	ret0, _ := ret[0].(*PostgreSQLMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetPostgreSQL indicates an expected call of GetPostgreSQL.
func (mr *MockProviderInterfaceMockRecorder) GetPostgreSQL(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetPostgreSQL", reflect.TypeOf((*MockProviderInterface)(nil).GetPostgreSQL), ctx, resourceID, namespace)
}

// GetRisingWave mocks base method.
func (m *MockProviderInterface) GetRisingWave(ctx context.Context, resourceID, namespace string) (*RisingWaveMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetRisingWave", ctx, resourceID, namespace)
	ret0, _ := ret[0].(*RisingWaveMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetRisingWave indicates an expected call of GetRisingWave.
func (mr *MockProviderInterfaceMockRecorder) GetRisingWave(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetRisingWave", reflect.TypeOf((*MockProviderInterface)(nil).GetRisingWave), ctx, resourceID, namespace)
}

// GetSecret mocks base method.
func (m *MockProviderInterface) GetSecret(ctx context.Context, resourceID, namespace string) (SecretMeta, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetSecret", ctx, resourceID, namespace)
	ret0, _ := ret[0].(SecretMeta)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetSecret indicates an expected call of GetSecret.
func (mr *MockProviderInterfaceMockRecorder) GetSecret(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetSecret", reflect.TypeOf((*MockProviderInterface)(nil).GetSecret), ctx, resourceID, namespace)
}

// GetServiceAccount mocks base method.
func (m *MockProviderInterface) GetServiceAccount(ctx context.Context, resourceID, namespace string) (*resource.Status, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetServiceAccount", ctx, resourceID, namespace)
	ret0, _ := ret[0].(*resource.Status)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetServiceAccount indicates an expected call of GetServiceAccount.
func (mr *MockProviderInterfaceMockRecorder) GetServiceAccount(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetServiceAccount", reflect.TypeOf((*MockProviderInterface)(nil).GetServiceAccount), ctx, resourceID, namespace)
}

// GetStatefulSetReplicaStatus mocks base method.
func (m *MockProviderInterface) GetStatefulSetReplicaStatus(ctx context.Context, resourceID, namespace string) (*resource.Status, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "GetStatefulSetReplicaStatus", ctx, resourceID, namespace)
	ret0, _ := ret[0].(*resource.Status)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// GetStatefulSetReplicaStatus indicates an expected call of GetStatefulSetReplicaStatus.
func (mr *MockProviderInterfaceMockRecorder) GetStatefulSetReplicaStatus(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "GetStatefulSetReplicaStatus", reflect.TypeOf((*MockProviderInterface)(nil).GetStatefulSetReplicaStatus), ctx, resourceID, namespace)
}

// InstallHelmRelease mocks base method.
func (m *MockProviderInterface) InstallHelmRelease(ctx context.Context, option InstallHelmReleaseOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "InstallHelmRelease", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// InstallHelmRelease indicates an expected call of InstallHelmRelease.
func (mr *MockProviderInterfaceMockRecorder) InstallHelmRelease(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "InstallHelmRelease", reflect.TypeOf((*MockProviderInterface)(nil).InstallHelmRelease), ctx, option)
}

// LabelNamespace mocks base method.
func (m *MockProviderInterface) LabelNamespace(ctx context.Context, resourceID string, labels map[string]string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "LabelNamespace", ctx, resourceID, labels)
	ret0, _ := ret[0].(error)
	return ret0
}

// LabelNamespace indicates an expected call of LabelNamespace.
func (mr *MockProviderInterfaceMockRecorder) LabelNamespace(ctx, resourceID, labels any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "LabelNamespace", reflect.TypeOf((*MockProviderInterface)(nil).LabelNamespace), ctx, resourceID, labels)
}

// ListPods mocks base method.
func (m *MockProviderInterface) ListPods(ctx context.Context, ns string) ([]v1.Pod, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ListPods", ctx, ns)
	ret0, _ := ret[0].([]v1.Pod)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// ListPods indicates an expected call of ListPods.
func (mr *MockProviderInterfaceMockRecorder) ListPods(ctx, ns any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ListPods", reflect.TypeOf((*MockProviderInterface)(nil).ListPods), ctx, ns)
}

// PutRisingWaveEnvVar mocks base method.
func (m *MockProviderInterface) PutRisingWaveEnvVar(ctx context.Context, namespace, name string, groups []string, standalone, meta, frontend, compute, compactor []*k8s.EnvVar) (*v1alpha1.RisingWave, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "PutRisingWaveEnvVar", ctx, namespace, name, groups, standalone, meta, frontend, compute, compactor)
	ret0, _ := ret[0].(*v1alpha1.RisingWave)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// PutRisingWaveEnvVar indicates an expected call of PutRisingWaveEnvVar.
func (mr *MockProviderInterfaceMockRecorder) PutRisingWaveEnvVar(ctx, namespace, name, groups, standalone, meta, frontend, compute, compactor any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "PutRisingWaveEnvVar", reflect.TypeOf((*MockProviderInterface)(nil).PutRisingWaveEnvVar), ctx, namespace, name, groups, standalone, meta, frontend, compute, compactor)
}

// RestartDeployment mocks base method.
func (m *MockProviderInterface) RestartDeployment(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestartDeployment", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestartDeployment indicates an expected call of RestartDeployment.
func (mr *MockProviderInterfaceMockRecorder) RestartDeployment(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartDeployment", reflect.TypeOf((*MockProviderInterface)(nil).RestartDeployment), ctx, resourceID, namespace)
}

// RestartStatefulSet mocks base method.
func (m *MockProviderInterface) RestartStatefulSet(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "RestartStatefulSet", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// RestartStatefulSet indicates an expected call of RestartStatefulSet.
func (mr *MockProviderInterfaceMockRecorder) RestartStatefulSet(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "RestartStatefulSet", reflect.TypeOf((*MockProviderInterface)(nil).RestartStatefulSet), ctx, resourceID, namespace)
}

// ScaleRisingWave mocks base method.
func (m *MockProviderInterface) ScaleRisingWave(ctx context.Context, option ScaleRisingWaveOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "ScaleRisingWave", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// ScaleRisingWave indicates an expected call of ScaleRisingWave.
func (mr *MockProviderInterfaceMockRecorder) ScaleRisingWave(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "ScaleRisingWave", reflect.TypeOf((*MockProviderInterface)(nil).ScaleRisingWave), ctx, option)
}

// StartRisingWave mocks base method.
func (m *MockProviderInterface) StartRisingWave(ctx context.Context, resourceID, namespace string, overrides []*k8s0.RisingWaveReplicaOverride) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StartRisingWave", ctx, resourceID, namespace, overrides)
	ret0, _ := ret[0].(error)
	return ret0
}

// StartRisingWave indicates an expected call of StartRisingWave.
func (mr *MockProviderInterfaceMockRecorder) StartRisingWave(ctx, resourceID, namespace, overrides any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StartRisingWave", reflect.TypeOf((*MockProviderInterface)(nil).StartRisingWave), ctx, resourceID, namespace, overrides)
}

// StopRisingWave mocks base method.
func (m *MockProviderInterface) StopRisingWave(ctx context.Context, resourceID, namespace string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "StopRisingWave", ctx, resourceID, namespace)
	ret0, _ := ret[0].(error)
	return ret0
}

// StopRisingWave indicates an expected call of StopRisingWave.
func (mr *MockProviderInterfaceMockRecorder) StopRisingWave(ctx, resourceID, namespace any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "StopRisingWave", reflect.TypeOf((*MockProviderInterface)(nil).StopRisingWave), ctx, resourceID, namespace)
}

// UninstallHelmRelease mocks base method.
func (m *MockProviderInterface) UninstallHelmRelease(ctx context.Context, option UninstallHelmReleaseOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UninstallHelmRelease", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UninstallHelmRelease indicates an expected call of UninstallHelmRelease.
func (mr *MockProviderInterfaceMockRecorder) UninstallHelmRelease(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UninstallHelmRelease", reflect.TypeOf((*MockProviderInterface)(nil).UninstallHelmRelease), ctx, option)
}

// UpdateConfigMap mocks base method.
func (m *MockProviderInterface) UpdateConfigMap(ctx context.Context, option UpdateConfigMapOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateConfigMap", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateConfigMap indicates an expected call of UpdateConfigMap.
func (mr *MockProviderInterfaceMockRecorder) UpdateConfigMap(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateConfigMap", reflect.TypeOf((*MockProviderInterface)(nil).UpdateConfigMap), ctx, option)
}

// UpdatePostgreSQL mocks base method.
func (m *MockProviderInterface) UpdatePostgreSQL(ctx context.Context, option UpdatePostgreSQLOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdatePostgreSQL", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdatePostgreSQL indicates an expected call of UpdatePostgreSQL.
func (mr *MockProviderInterfaceMockRecorder) UpdatePostgreSQL(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdatePostgreSQL", reflect.TypeOf((*MockProviderInterface)(nil).UpdatePostgreSQL), ctx, option)
}

// UpdateRisingWaveComponents mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveComponents(ctx context.Context, option UpdateRisingWaveComponentsOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveComponents", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveComponents indicates an expected call of UpdateRisingWaveComponents.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveComponents(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveComponents", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveComponents), ctx, option)
}

// UpdateRisingWaveImage mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveImage(ctx context.Context, resourceID, namespace, imageTag string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveImage", ctx, resourceID, namespace, imageTag)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveImage indicates an expected call of UpdateRisingWaveImage.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveImage(ctx, resourceID, namespace, imageTag any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveImage", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveImage), ctx, resourceID, namespace, imageTag)
}

// UpdateRisingWaveLicenseKey mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveLicenseKey(ctx context.Context, resourceID, namespace, secretName string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveLicenseKey", ctx, resourceID, namespace, secretName)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveLicenseKey indicates an expected call of UpdateRisingWaveLicenseKey.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveLicenseKey(ctx, resourceID, namespace, secretName any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveLicenseKey", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveLicenseKey), ctx, resourceID, namespace, secretName)
}

// UpdateRisingWaveMetaStore mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveMetaStore(ctx context.Context, resourceID, namespace string, spec *rw.MetaStoreSpec) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveMetaStore", ctx, resourceID, namespace, spec)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveMetaStore indicates an expected call of UpdateRisingWaveMetaStore.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveMetaStore(ctx, resourceID, namespace, spec any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveMetaStore", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveMetaStore), ctx, resourceID, namespace, spec)
}

// UpdateRisingWaveNodeGroup mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveNodeGroup(ctx context.Context, option UpdateRisingWaveNodeGroupOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveNodeGroup", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveNodeGroup indicates an expected call of UpdateRisingWaveNodeGroup.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveNodeGroup(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveNodeGroup", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveNodeGroup), ctx, option)
}

// UpdateRisingWaveNodeGroupConfiguration mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, option UpdateRisingWaveNodeGroupConfigurationOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveNodeGroupConfiguration", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveNodeGroupConfiguration indicates an expected call of UpdateRisingWaveNodeGroupConfiguration.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveNodeGroupConfiguration(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveNodeGroupConfiguration", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveNodeGroupConfiguration), ctx, option)
}

// UpdateRisingWaveNodeGroupRestartAt mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, option UpdateRisingWaveNodeGroupRestartAtOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveNodeGroupRestartAt", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveNodeGroupRestartAt indicates an expected call of UpdateRisingWaveNodeGroupRestartAt.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveNodeGroupRestartAt(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveNodeGroupRestartAt", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveNodeGroupRestartAt), ctx, option)
}

// UpdateRisingWaveSecretStore mocks base method.
func (m *MockProviderInterface) UpdateRisingWaveSecretStore(ctx context.Context, resourceID, namespace, secretName, secretKey string) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateRisingWaveSecretStore", ctx, resourceID, namespace, secretName, secretKey)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateRisingWaveSecretStore indicates an expected call of UpdateRisingWaveSecretStore.
func (mr *MockProviderInterfaceMockRecorder) UpdateRisingWaveSecretStore(ctx, resourceID, namespace, secretName, secretKey any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateRisingWaveSecretStore", reflect.TypeOf((*MockProviderInterface)(nil).UpdateRisingWaveSecretStore), ctx, resourceID, namespace, secretName, secretKey)
}

// UpdateSecret mocks base method.
func (m *MockProviderInterface) UpdateSecret(ctx context.Context, option UpdateSecretOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpdateSecret", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpdateSecret indicates an expected call of UpdateSecret.
func (mr *MockProviderInterfaceMockRecorder) UpdateSecret(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpdateSecret", reflect.TypeOf((*MockProviderInterface)(nil).UpdateSecret), ctx, option)
}

// UpgradeHelmRelease mocks base method.
func (m *MockProviderInterface) UpgradeHelmRelease(ctx context.Context, option UpgradeHelmReleaseOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "UpgradeHelmRelease", ctx, option)
	ret0, _ := ret[0].(error)
	return ret0
}

// UpgradeHelmRelease indicates an expected call of UpgradeHelmRelease.
func (mr *MockProviderInterfaceMockRecorder) UpgradeHelmRelease(ctx, option any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "UpgradeHelmRelease", reflect.TypeOf((*MockProviderInterface)(nil).UpgradeHelmRelease), ctx, option)
}
