package k8s

import (
	"context"

	prometheusv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

type CreateServiceMonitorOption struct {
	ResourceID         string
	Namespace          string
	Labels             map[string]string
	Annotations        map[string]string
	ServiceMonitorSpec *pbprometheus.ServiceMonitorSpec
}

func (p *Provider) CreateServiceMonitor(ctx context.Context, option CreateServiceMonitorOption) error {
	if option.ServiceMonitorSpec == nil {
		return eris.Errorf("ServiceMonitorSpec should not be nil").WithCode(eris.CodeInvalidArgument)
	}
	smSpec, err := conversion.FromServiceMonitorSpecProto(option.ServiceMonitorSpec)
	if err != nil {
		return eris.Errorf("failed to convert service monitor spec proto: %v", option.ServiceMonitorSpec).WithCode(eris.CodeInvalidArgument)
	}
	if err := p.kc.Create(ctx, &prometheusv1.ServiceMonitor{
		ObjectMeta: metav1.ObjectMeta{
			Name:        option.ResourceID,
			Namespace:   option.Namespace,
			Labels:      option.Labels,
			Annotations: option.Annotations,
		},
		Spec: smSpec,
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("service monitor %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create service monitor %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) DeleteServiceMonitor(ctx context.Context, resourceID, namespace string) error {
	if err := p.kc.Delete(ctx, &prometheusv1.ServiceMonitor{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("service monitor %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete service monitor %s", resourceID)
	}
	return nil
}
