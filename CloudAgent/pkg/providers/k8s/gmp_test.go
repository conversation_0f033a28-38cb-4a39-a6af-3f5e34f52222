package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	pbgmp "github.com/risingwavelabs/cloudagent/pbgen/common/gmp"
	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreatePodMonitoring(t *testing.T) {
	c := fake.NewDynamiceInterface()
	p := &Provider{
		kc: &k8s.KubernetesClient{DynamicInterface: c},
	}
	pmProto := &pbgmp.PodMonitoringSpec{
		TargetLabels: &pbgmp.TargetLabels{
			FromPod: []*pbgmp.LabelMapping{
				{
					From: "tl1",
					To:   "tl1",
				},
				{
					From: "tl2",
					To:   "tl2",
				},
			},
		},
		Endpoints: []*pbgmp.Endpoint{
			{
				Port:     "p1",
				Interval: "1m",
				Timeout:  "1s",
				MetricRelabeling: []*pbgmp.RelabelingRule{
					{
						SourceLabels: []string{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []string{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:     "p2",
				Interval: "2m",
				Timeout:  "2s",
				MetricRelabeling: []*pbgmp.RelabelingRule{
					{
						SourceLabels: []string{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: &pbk8s.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []*pbk8s.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
				},
			},
		},
	}

	ctx := context.Background()
	err := p.CreatePodMonitoring(ctx, CreatePodMonitoringOption{
		ResourceID:        "name",
		Namespace:         "ns",
		Labels:            map[string]string{"key1": "val1"},
		Annotations:       map[string]string{"key2": "val2"},
		PodMonitoringSpec: pmProto,
	})

	require.NoError(t, err)
	obj, err := c.Resource(schema.GroupVersionResource{
		Group:    "monitoring.googleapis.com",
		Version:  "v1",
		Resource: "podmonitorings",
	}).Namespace("ns").Get(ctx, "name", metav1.GetOptions{})

	require.NoError(t, err)
	assert.Equal(t, obj.Object, map[string]interface{}{
		"apiVersion": "monitoring.googleapis.com/v1",
		"kind":       "PodMonitoring",
		"metadata": map[string]interface{}{
			"annotations": map[string]interface{}{"key2": "val2"},
			"labels":      map[string]interface{}{"key1": "val1"},
			"name":        "name",
			"namespace":   "ns",
		},
		"spec": map[string]interface{}{
			"endpoints": []interface{}{
				map[string]interface{}{
					"interval": "1m",
					"metricRelabeling": []interface{}{
						map[string]interface{}{
							"action":       "action1",
							"regex":        "re1",
							"sourceLabels": []interface{}{"sl1", "sl2"},
						},
						map[string]interface{}{
							"action":       "action2",
							"regex":        "re2",
							"sourceLabels": []interface{}{"sl3", "sl4"},
						},
					},
					"port":    "p1",
					"timeout": "1s",
				},
				map[string]interface{}{
					"interval": "2m",
					"metricRelabeling": []interface{}{
						map[string]interface{}{
							"action":       "action1",
							"regex":        "re1",
							"sourceLabels": []interface{}{"sl5", "sl6"}}},
					"port":    "p2",
					"timeout": "2s",
				},
			},
			"selector": map[string]interface{}{
				"matchExpressions": []interface{}{
					map[string]interface{}{
						"key":      "key1",
						"operator": "In",
						"values":   []interface{}{"val1", "val2"},
					},
					map[string]interface{}{
						"key":      "key2",
						"operator": "NotIn",
						"values":   []interface{}{"val3", "val4"},
					},
				},
				"matchLabels": map[string]interface{}{"k1": "v1", "k2": "v2"}},
			"targetLabels": map[string]interface{}{
				"fromPod": []interface{}{
					map[string]interface{}{
						"from": "tl1",
						"to":   "tl1",
					},
					map[string]interface{}{
						"from": "tl2",
						"to":   "tl2"},
				},
			},
		},
	})

	err = p.CreatePodMonitoring(ctx, CreatePodMonitoringOption{
		ResourceID:        "name",
		Namespace:         "ns",
		Labels:            map[string]string{"key1": "val1"},
		Annotations:       map[string]string{"key2": "val2"},
		PodMonitoringSpec: pmProto,
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestDeletePodMonitoring(t *testing.T) {
	c := fake.NewDynamiceInterface()

	var (
		ctx        = context.Background()
		resourceID = "resource-ID"
		namespace  = "ns"
	)

	p := &Provider{
		kc: &k8s.KubernetesClient{DynamicInterface: c},
	}

	err := p.DeletePodMonitoring(ctx, resourceID, namespace)
	assert.True(t, utils.IsErrNotFound(err))

	err = p.CreatePodMonitoring(ctx, CreatePodMonitoringOption{
		ResourceID:        resourceID,
		Namespace:         namespace,
		PodMonitoringSpec: &pbgmp.PodMonitoringSpec{},
	})
	require.NoError(t, err)

	err = p.DeletePodMonitoring(ctx, resourceID, namespace)
	require.NoError(t, err)

	err = p.DeletePodMonitoring(ctx, resourceID, namespace)
	assert.True(t, utils.IsErrNotFound(err))
}
