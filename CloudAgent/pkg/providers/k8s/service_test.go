package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateService(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreateService(ctx, CreateServiceOption{
		ResourceID: "name",
		Namespace:  "ns",
		ServiceSpec: &pbk8s.ServiceSpec{
			Ports: []*pbk8s.ServicePort{
				{
					Name: "p1",
					Port: 1,
				},
				{
					Name: "p2",
					Port: 2,
				},
			},
			Selector: map[string]string{
				"k1": "v1",
				"k2": "v2",
			},
		},
	})
	require.NoError(t, err)

	service, err := k8s.GetResource[corev1.Service](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", service.Name)
	assert.Equal(t, "ns", service.Namespace)
	assert.Equal(t, map[string]string{
		"k1": "v1",
		"k2": "v2",
	}, service.Spec.Selector)
	assert.Equal(t, []corev1.ServicePort{
		{
			Name: "p1",
			Port: 1,
		},
		{
			Name: "p2",
			Port: 2,
		},
	}, service.Spec.Ports)

	err = p.CreateService(ctx, CreateServiceOption{
		ResourceID: "name",
		Namespace:  "ns",
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}
