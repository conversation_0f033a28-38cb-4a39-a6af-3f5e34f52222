package k8s

import (
	"context"
	"slices"

	"github.com/risingwavelabs/eris"
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	v1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"

	pbcommonk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

func (p *Provider) PutRisingWaveEnvVar(ctx context.Context, namespace string, name string, groups []string,
	standalone, meta, frontend, compute, compactor []*pbcommonk8s.EnvVar) (*rwv1alpha1.RisingWave, error) {
	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, name, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return nil, eris.Errorf("failed to find risingwave %s/%s", namespace, name).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return nil, eris.Wrap(err, "failed to get risingwave")
	}

	if rw.Spec.Components.Standalone != nil {
		if groups != nil {
			return nil, eris.New("node groups on standalone not supported").WithCode(eris.CodeInvalidArgument)
		}
		putEnvsStandalone(rw.Spec.Components.Standalone, standalone)
	} else {
		rw.Spec.Components.Meta.NodeGroups = putEnvs(rw.Spec.Components.Meta.NodeGroups, meta, groups)
		rw.Spec.Components.Frontend.NodeGroups = putEnvs(rw.Spec.Components.Frontend.NodeGroups, frontend, groups)
		rw.Spec.Components.Compute.NodeGroups = putEnvs(rw.Spec.Components.Compute.NodeGroups, compute, groups)
		rw.Spec.Components.Compactor.NodeGroups = putEnvs(rw.Spec.Components.Compactor.NodeGroups, compactor, groups)
	}

	err = p.kc.Update(ctx, rw)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return nil, eris.Errorf("failed to get RW, namespace %v", namespace).WithCode(eris.CodeNotFound)
		}
		return nil, eris.Wrapf(err, "failed to update RW, namespace %s", namespace)
	}
	return rw, nil
}

func (p *Provider) DeleteRisingWaveEnvVar(ctx context.Context, namespace string, name string, groups []string,
	standalone, meta, frontend, compute, compactor []string) (*rwv1alpha1.RisingWave, error) {
	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, name, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return nil, eris.Errorf("failed to find rw %s/%s", namespace, name).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return nil, eris.Wrap(err, "failed to get risingwave")
	}

	if rw.Spec.Components.Standalone != nil {
		if groups != nil {
			return nil, eris.New("node groups on standalone not supported").WithCode(eris.CodeInvalidArgument)
		}
		deleteEnvsStandalone(rw.Spec.Components.Standalone, standalone)
	} else {
		rw.Spec.Components.Meta.NodeGroups = deleteEnvs(rw.Spec.Components.Meta.NodeGroups, meta, groups)
		rw.Spec.Components.Frontend.NodeGroups = deleteEnvs(rw.Spec.Components.Frontend.NodeGroups, frontend, groups)
		rw.Spec.Components.Compute.NodeGroups = deleteEnvs(rw.Spec.Components.Compute.NodeGroups, compute, groups)
		rw.Spec.Components.Compactor.NodeGroups = deleteEnvs(rw.Spec.Components.Compactor.NodeGroups, compactor, groups)
	}

	err = p.kc.Update(ctx, rw)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return nil, eris.Errorf("failed to get RW, namespace %s", namespace).WithCode(eris.CodeNotFound)
		}
		return nil, eris.Wrapf(err, "failed to update RW, namespace %s", namespace)
	}
	return rw, nil
}

func deleteEnvsStandalone(standalone *rwv1alpha1.RisingWaveStandaloneComponent, toEnvVars []string) {
	toEnvVarsInner := make([]*pbcommonk8s.EnvVar, len(toEnvVars))
	for i, envName := range toEnvVars {
		toEnvVarsInner[i] = &pbcommonk8s.EnvVar{Name: envName}
	}
	updateEnvVars(&standalone.Template.Spec.Env, toEnvVarsInner, true)
}

func putEnvsStandalone(standalone *rwv1alpha1.RisingWaveStandaloneComponent, toEnvVars []*pbcommonk8s.EnvVar) {
	updateEnvVars(&standalone.Template.Spec.Env, toEnvVars, false)
}

func putEnvs(nodeGroups []rwv1alpha1.RisingWaveNodeGroup, toEnvVars []*pbcommonk8s.EnvVar, groups []string) []rwv1alpha1.RisingWaveNodeGroup {
	for i := range nodeGroups {
		ngName := nodeGroups[i].Name
		if groups == nil || slices.Contains(groups, ngName) {
			updateEnvVars(&nodeGroups[i].Template.Spec.Env, toEnvVars, false)
		}
	}
	return nodeGroups
}

func deleteEnvs(nodeGroups []rwv1alpha1.RisingWaveNodeGroup, toEnvVars []string, groups []string) []rwv1alpha1.RisingWaveNodeGroup {
	toEnvVarsInner := make([]*pbcommonk8s.EnvVar, len(toEnvVars))
	for i, envName := range toEnvVars {
		toEnvVarsInner[i] = &pbcommonk8s.EnvVar{Name: envName}
	}

	for i := range nodeGroups {
		ngName := nodeGroups[i].Name
		if groups == nil || slices.Contains(groups, ngName) {
			updateEnvVars(&nodeGroups[i].Template.Spec.Env, toEnvVarsInner, true)
		}
	}
	return nodeGroups
}

func updateEnvVars(base *[]v1.EnvVar, diff []*pbcommonk8s.EnvVar, remove bool) {
	envSet := make(map[string]bool)
	// Convert input variables to a set for quick lookup
	for _, e := range diff {
		if e != nil {
			envSet[e.GetName()] = true
		}
	}

	if remove {
		// Filter out variables that should be removed
		newEnvVars := []v1.EnvVar{}
		for _, env := range *base {
			if !envSet[env.Name] { // Keep only variables not marked for removal
				newEnvVars = append(newEnvVars, env)
			}
		}
		*base = newEnvVars
	} else {
		// Create a map for quick lookups of existing environment variables
		envMap := make(map[string]int)
		for i, env := range *base {
			envMap[env.Name] = i
		}

		// Update existing variables and add new ones if needed
		for _, e := range diff {
			if index, exists := envMap[e.GetName()]; exists {
				if (*base)[index].ValueFrom != nil {
					(*base)[index].ValueFrom = nil // Clean ValueFrom if it exists
				}
				(*base)[index].Value = e.GetValue() // Update existing value
			} else {
				*base = append(*base, v1.EnvVar{Name: e.GetName(), Value: e.GetValue()}) // Add new variable
			}
		}
	}
}
