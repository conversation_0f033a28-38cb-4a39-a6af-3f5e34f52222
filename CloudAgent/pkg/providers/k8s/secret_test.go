package k8s

import (
	"context"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateSecret(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreateSecret(ctx, CreateSecretOption{
		ResourceID: "name",
		Namespace:  "ns",
		Immutable:  true,
		StringData: map[string]string{
			"k1": "v1",
			"k2": "v2",
		},
	})
	require.NoError(t, err)

	secret, err := k8s.GetResource[corev1.Secret](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", secret.Name)
	assert.Equal(t, "ns", secret.Namespace)
	assert.Equal(t, map[string]string{
		"k1": "v1",
		"k2": "v2",
	}, secret.StringData)

	err = p.CreateSecret(ctx, CreateSecretOption{
		ResourceID: "name",
		Namespace:  "ns",
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestGetSecret(t *testing.T) {
	c := fake.NewClient(&corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Immutable: ptr.To(true),
		StringData: map[string]string{
			"k1": "v1",
		},
		Data: map[string][]byte{
			"k1": []byte("v1"),
		},
		Type: corev1.SecretTypeOpaque,
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	secretMeta, err := p.GetSecret(ctx, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, SecretMeta{
		Status: &pbresource.Status{
			Code: pbresource.StatusCode_READY,
		},
		Secret: &Secret{
			StringData: map[string]string{
				"k1": "v1",
			},
			Data: map[string][]byte{
				"k1": []byte("v1"),
			},
			Type:      string(corev1.SecretTypeOpaque),
			Immutable: true,
		},
	}, secretMeta)

	secretMeta, err = p.GetSecret(ctx, "name-non-existent", "ns")
	require.NoError(t, err)
	assert.Equal(t, &pbresource.Status{
		Code: pbresource.StatusCode_NOT_FOUND,
	}, secretMeta.Status)
}

func TestDeleteSecret(t *testing.T) {
	c := fake.NewClient(&corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.DeleteSecret(ctx, "name", "ns")
	require.NoError(t, err)
	err = p.DeleteSecret(ctx, "name", "ns")
	assert.True(t, utils.IsErrNotFound(err))
}

func TestUpdateSecret(t *testing.T) {
	c := fake.NewClient(&corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Immutable: ptr.To(false),
		Data: map[string][]byte{
			"old-key": []byte("old-value"),
		},
		Type: corev1.SecretTypeOpaque,
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.UpdateSecret(ctx, UpdateSecretOption{
		ResourceID: "name",
		Namespace:  "ns",
		To: Secret{
			Immutable: true,
			Data: map[string][]byte{
				"new-key": []byte("new-value"),
			},
			Type: string(corev1.SecretTypeTLS),
		},
	})
	require.NoError(t, err)

	// Verify the secret was updated
	secret, err := k8s.GetResource[corev1.Secret](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", secret.Name)
	assert.Equal(t, "ns", secret.Namespace)
	assert.True(t, *secret.Immutable)
	assert.Equal(t, map[string][]byte{
		"new-key": []byte("new-value"),
	}, secret.Data)
	assert.Equal(t, corev1.SecretTypeTLS, secret.Type)

	// Forbid when the secret is immutable
	err = p.UpdateSecret(ctx, UpdateSecretOption{
		ResourceID: "name",
		Namespace:  "ns",
		To: Secret{
			Immutable: true,
			Data: map[string][]byte{
				"new-key": []byte("new-value-2"),
			},
			Type: string(corev1.SecretTypeTLS),
		},
	})
	require.Error(t, err)
	require.Equal(t, eris.CodePermissionDenied, eris.GetCode(err))
}
