package k8s

import (
	"context"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

type CreateServiceOption struct {
	ResourceID  string
	Namespace   string
	ServiceSpec *pbk8s.ServiceSpec
	Labels      map[string]string
}

func (p *Provider) CreateService(ctx context.Context, option CreateServiceOption) error {
	service := &corev1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Labels:    option.Labels,
		},
		Spec: conversion.FromServiceSpecProto(option.ServiceSpec),
	}
	if err := p.kc.Create(ctx, service); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("service %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create service %s", option.ResourceID)
	}
	return nil
}
