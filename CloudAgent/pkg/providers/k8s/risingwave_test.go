package k8s

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"
	"time"

	"github.com/risingwavelabs/eris"
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	rwconsts "github.com/risingwavelabs/risingwave-operator/pkg/consts"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	pbtimestamp "google.golang.org/protobuf/types/known/timestamppb"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/equality"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func getRwSpecProto(isStandalone bool) pbrw.RisingWaveSpec {
	standaloneSpec := &pbrw.StandaloneSpec{
		LogLevel: "info",
		Replicas: 1,
		UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
			Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
		},
		NodePodSpec: &pbrw.NodePodSpec{
			ContainerSpec: &pbrw.NodePodContainerSpec{
				Envs: map[string]string{
					"key1": "val1",
				},
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
			},
			ServiceAccount: "test_sa",
			Labels:         map[string]string{"k1": "v1", "k2": "v2"},
			Annotations:    map[string]string{"ak1": "av1", "ak2": "av2"},
		},
	}

	frontendSpec := &pbrw.ComponentSpec{
		NodeGroups: []*pbrw.NodeGroupSpec{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
					Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
				},
				NodePodSpec: &pbrw.NodePodSpec{
					ServiceAccount: "test_sa",
					Labels:         map[string]string{"k1": "v1", "k2": "v2"},
					ContainerSpec: &pbrw.NodePodContainerSpec{
						Envs: map[string]string{
							"key1": "val1",
						},
						Resources: &pbk8s.ResourceRequirements{
							CpuRequest:    "250m",
							CpuLimit:      "500m",
							MemoryRequest: "64Mi",
							MemoryLimit:   "128Mi",
						},
					},
				},
			},
		},
	}

	computeSpec := &pbrw.ComponentSpec{
		NodeGroups: []*pbrw.NodeGroupSpec{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
					Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
				},
				NodePodSpec: &pbrw.NodePodSpec{
					ServiceAccount: "test_sa",
					Labels:         map[string]string{"k1": "v1", "k2": "v2"},
					ContainerSpec: &pbrw.NodePodContainerSpec{
						Envs: map[string]string{
							"key1": "val1",
						},
						Resources: &pbk8s.ResourceRequirements{
							CpuRequest:    "250m",
							CpuLimit:      "500m",
							MemoryRequest: "64Mi",
							MemoryLimit:   "128Mi",
						},
					},
				},
			},
		},
	}

	compactorSpec := &pbrw.ComponentSpec{
		NodeGroups: []*pbrw.NodeGroupSpec{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
					Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
				},
				NodePodSpec: &pbrw.NodePodSpec{
					ServiceAccount: "test_sa",
					Labels:         map[string]string{"k1": "v1", "k2": "v2"},
					ContainerSpec: &pbrw.NodePodContainerSpec{
						Envs: map[string]string{
							"key1": "val1",
						},
						Resources: &pbk8s.ResourceRequirements{
							CpuRequest:    "250m",
							CpuLimit:      "500m",
							MemoryRequest: "64Mi",
							MemoryLimit:   "128Mi",
						},
					},
				},
			},
		},
	}

	metaSpec := &pbrw.ComponentSpec{
		LogLevel: "info",
		NodeGroups: []*pbrw.NodeGroupSpec{
			{
				Name:     "default",
				Replicas: 1,
				UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
					Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
				},
				NodePodSpec: &pbrw.NodePodSpec{
					ContainerSpec: &pbrw.NodePodContainerSpec{
						Envs: map[string]string{
							"key1": "val1",
						},
						Resources: &pbk8s.ResourceRequirements{
							CpuRequest:    "250m",
							CpuLimit:      "500m",
							MemoryRequest: "64Mi",
							MemoryLimit:   "128Mi",
						},
					},
					ServiceAccount: "test_sa",
					Labels:         map[string]string{"k1": "v1", "k2": "v2"},
				},
			},
		},
	}

	return pbrw.RisingWaveSpec{
		Image:                       "test_image",
		EnableDefaultServiceMonitor: true,
		FrontendServiceType:         pbk8s.ServiceType_SERVICE_TYPE_CLUSTER_IP,
		EnableFullKubernetesAddr:    true,
		EnableEmbeddedServingMode:   true,
		EnableStandaloneMode:        isStandalone,
		Config: &pbrw.Config{
			NodeConfig: &pbrw.NodeConfig{
				NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
					Name: "test_cm",
					Key:  "test_key",
				},
			},
		},
		StateStore: &pbrw.StateStoreSpec{
			DataDirectory: "test_dir",
			Backend: &pbrw.StateStoreSpec_GcsStateStore{
				GcsStateStore: &pbrw.StateStoreBackendGCS{
					Bucket: "test_bucket",
				},
			},
		},
		MetaStoreSpec: &pbrw.MetaStoreSpec{
			EtcdBackend: &pbrw.MetaStoreBackendEtcd{
				Endpoint: "test_etcd",
			},
		},
		Components: &pbrw.ComponentsSpec{
			StandaloneComponent: standaloneSpec,
			MetaSpec:            metaSpec,
			FrontendSpec:        frontendSpec,
			ComputeSpec:         computeSpec,
			CompactorSpec:       compactorSpec,
		},
		LicenseKey: &pbrw.LicenseKey{
			SecretName: "test-secret-name",
		},
		SecretStore: &pbrw.SecretStore{},
	}
}

func conversionHelper(t *testing.T, p *pbrw.RisingWaveSpec) rwv1alpha1.RisingWaveSpec {
	val, err := conversion.FromRisingWaveSpecProto(p)
	assert.NoError(t, err)
	return *val
}

func TestCreateRisingWave(t *testing.T) {
	tests := []struct {
		createOpts CreateRisingWaveOption

		description string

		expected rwv1alpha1.RisingWaveSpec
	}{
		{
			description: "create cluster instance",
			createOpts: CreateRisingWaveOption{
				ResourceID:     "name",
				Namespace:      "ns",
				Labels:         map[string]string{"key1": "val1"},
				Annotations:    map[string]string{"key2": "val2"},
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
			},
			expected: conversionHelper(t, utils.Ptr(getRwSpecProto(false))),
		},
		{
			description: "create standalone instance",
			createOpts: CreateRisingWaveOption{
				ResourceID:     "name",
				Namespace:      "ns",
				Labels:         map[string]string{"key1": "val1"},
				Annotations:    map[string]string{"key2": "val2"},
				RisingWaveSpec: utils.Ptr(getRwSpecProto(true)),
			},
			expected: conversionHelper(t, utils.Ptr(getRwSpecProto(true))),
		},
	}

	for _, test := range tests {
		c := fake.NewClient()
		p := &Provider{
			kc: &k8s.KubernetesClient{Client: c},
		}
		ctx := context.Background()
		err := p.CreateRisingWave(ctx, test.createOpts)
		require.NoError(t, err)
		rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
		require.NoError(t, err)
		assert.Equalf(t, "name", rw.Name, test.description)
		assert.Equalf(t, "ns", rw.Namespace, test.description)
		assert.Equalf(t, map[string]string{"key1": "val1"}, rw.Labels, test.description)
		assert.Equalf(t, map[string]string{"key2": "val2"}, rw.Annotations, test.description)

		expectedJSON := getPrettyJSON(t, test.expected)
		actualJSON := getPrettyJSON(t, rw.Spec)
		failMsg := fmt.Sprintf("%s.\n\nExpected: %s\n\nActual: %s", test.description, expectedJSON, actualJSON)
		assert.Equalf(t, expectedJSON, actualJSON, failMsg)

		err = p.CreateRisingWave(ctx, test.createOpts)
		assert.Truef(t, utils.IsErrAlreadyExists(err), test.description)
	}

	for _, isStandalone := range []bool{true, false} {
		c := fake.NewClient()
		p := &Provider{
			kc: &k8s.KubernetesClient{Client: c},
		}

		ctx := context.Background()
		err := p.CreateRisingWave(ctx, CreateRisingWaveOption{
			ResourceID:     "name",
			Namespace:      "ns",
			Labels:         map[string]string{"key1": "val1"},
			Annotations:    map[string]string{"key2": "val2"},
			RisingWaveSpec: utils.Ptr(getRwSpecProto(isStandalone)),
		})

		require.NoError(t, err)

		rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
		require.NoError(t, err)
		assert.Equal(t, "name", rw.Name)
		assert.Equal(t, "ns", rw.Namespace)
		assert.Equal(t, map[string]string{"key1": "val1"}, rw.Labels)
		assert.Equal(t, map[string]string{"key2": "val2"}, rw.Annotations)

		// we expect that the StandaloneComponent is not present, since we created a non-standalone instance
		assert.Equal(t, *utils.GetDummyRwSpec(isStandalone), rw.Spec)

		err = p.CreateRisingWave(ctx, CreateRisingWaveOption{
			ResourceID:     "name",
			Namespace:      "ns",
			RisingWaveSpec: utils.Ptr(getRwSpecProto(isStandalone)),
		})
		assert.True(t, utils.IsErrAlreadyExists(err))
	}
}

func TestGetRisingWave(t *testing.T) {
	tests := []struct {
		generation     int64
		rwStatus       rwv1alpha1.RisingWaveStatus
		expectedResult RisingWaveMeta
		isStandalone   bool
	}{
		{
			// retrieve standalone instance
			isStandalone: true,
			generation:   1,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
				Conditions: []rwv1alpha1.RisingWaveCondition{
					{
						Type:   rwv1alpha1.RisingWaveConditionRunning,
						Status: metav1.ConditionTrue,
					},
				},
			},
			expectedResult: RisingWaveMeta{
				// TODO: error is that we create RW in standalone false
				// Standalone component gets dropped
				// Solution: Do these tests with and without standalone mode
				RisingWaveSpec: utils.Ptr(getRwSpecProto(true)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode: pbrw.RisingWaveStatusCode_RW_READY,
				},
			},
		},
		{
			isStandalone: false,
			generation:   1,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
				Conditions: []rwv1alpha1.RisingWaveCondition{
					{
						Type:   rwv1alpha1.RisingWaveConditionRunning,
						Status: metav1.ConditionTrue,
					},
				},
				Internal: rwv1alpha1.RisingWaveInternalStatus{
					StateStoreRootPath: "rootpath",
				},
			},
			expectedResult: RisingWaveMeta{
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode:         pbrw.RisingWaveStatusCode_RW_READY,
					StateStoreRootPath: "rootpath",
				},
			},
		},
		{
			isStandalone: false,
			generation:   1,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
				Conditions: []rwv1alpha1.RisingWaveCondition{
					{
						Type:   rwv1alpha1.RisingWaveConditionRunning,
						Status: metav1.ConditionTrue,
					},
				},
			},
			expectedResult: RisingWaveMeta{
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode: pbrw.RisingWaveStatusCode_RW_READY,
				},
			},
		},
		{
			isStandalone: false,
			generation:   1,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
				Conditions: []rwv1alpha1.RisingWaveCondition{
					{
						Type:   rwv1alpha1.RisingWaveConditionRunning,
						Status: metav1.ConditionTrue,
					},
					{
						Type:   rwv1alpha1.RisingWaveConditionUpgrading,
						Status: metav1.ConditionTrue,
					},
				},
			},
			expectedResult: RisingWaveMeta{
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode: pbrw.RisingWaveStatusCode_RW_UPGRADING,
				},
			},
		},
		{
			isStandalone: false,
			generation:   1,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
				Conditions: []rwv1alpha1.RisingWaveCondition{
					{
						Type:   rwv1alpha1.RisingWaveConditionRunning,
						Status: metav1.ConditionFalse,
					},
				},
			},
			expectedResult: RisingWaveMeta{
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode: pbrw.RisingWaveStatusCode_RW_NOT_READY,
				},
			},
		},
		{
			isStandalone: false,
			generation:   1,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
			},
			expectedResult: RisingWaveMeta{
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode: pbrw.RisingWaveStatusCode_RW_NOT_READY,
				},
			},
		},
		{
			isStandalone: false,
			generation:   2,
			rwStatus: rwv1alpha1.RisingWaveStatus{
				ObservedGeneration: 1,
				Conditions: []rwv1alpha1.RisingWaveCondition{
					{
						Type:   rwv1alpha1.RisingWaveConditionRunning,
						Status: metav1.ConditionTrue,
					},
				},
			},
			expectedResult: RisingWaveMeta{
				RisingWaveSpec: utils.Ptr(getRwSpecProto(false)),
				RWStatus: &pbrw.RisingWaveStatus{
					StatusCode: pbrw.RisingWaveStatusCode_RW_WAIT_FOR_OBSERVATION,
				},
			},
		},
	}

	for _, tt := range tests {
		c := fake.NewClient(&rwv1alpha1.RisingWave{
			ObjectMeta: metav1.ObjectMeta{
				Name:       "name",
				Namespace:  "ns",
				Generation: tt.generation,
			},
			Spec:   *utils.GetDummyRwSpec(tt.isStandalone),
			Status: tt.rwStatus,
		})
		p := &Provider{
			kc: &k8s.KubernetesClient{Client: c},
		}
		ctx := context.Background()
		rwMeta, err := p.GetRisingWave(ctx, "name", "ns")
		require.NoError(t, err)

		expectedJSON := getPrettyJSON(t, tt.expectedResult)
		actualJSON := getPrettyJSON(t, rwMeta)
		failMsg := fmt.Sprintf("\n\nExpected: %s\n\nActual: %s", expectedJSON, actualJSON)
		assert.Equalf(t, expectedJSON, actualJSON, failMsg)
	}
}

func TestGetRisingWaveNotFound(t *testing.T) {
	c := fake.NewClient(&rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	_, err := p.GetRisingWave(ctx, "randomname", "ns")
	assert.True(t, utils.IsErrNotFound(err))
}

func TestDeleteRisingWave(t *testing.T) {
	c := fake.NewClient(&rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.DeleteRisingWave(ctx, "name", "ns")
	require.NoError(t, err)
	err = p.DeleteRisingWave(ctx, "name", "ns")
	assert.True(t, utils.IsErrNotFound(err))

	_, err = k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
	require.True(t, k8sErrors.IsNotFound(err))
}

func TestUpdateRisingWaveImageTag(t *testing.T) {
	c := fake.NewClient(&rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.UpdateRisingWaveImage(ctx, "name", "ns", "new_test_image")
	require.NoError(t, err)

	expected := utils.GetDummyRwSpec(false)
	expected.Image = "new_test_image"
	expected.Components.Meta.NodeGroups[0].UpgradeStrategy.Type = rwv1alpha1.RisingWaveUpgradeStrategyTypeRecreate
	expected.Components.Compute.NodeGroups[0].UpgradeStrategy.Type = rwv1alpha1.RisingWaveUpgradeStrategyTypeRecreate

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
	require.NoError(t, err)

	// Generate `s` fields
	assert.Equal(t, *expected, rw.Spec)
}

func TestUpdateRisingWaveImageTag_Standalone(t *testing.T) {
	objectMeta := metav1.ObjectMeta{
		Namespace: "tns",
		Name:      "t",
	}
	testcases := map[string]struct {
		in  *rwv1alpha1.RisingWave
		tag string
		out *rwv1alpha1.RisingWave
	}{
		"standalone-global": {
			in: &rwv1alpha1.RisingWave{
				ObjectMeta: objectMeta,
				Spec: rwv1alpha1.RisingWaveSpec{
					EnableStandaloneMode: utils.Ptr(true),
					Image:                "v1",
				},
			},
			tag: "v2",
			out: &rwv1alpha1.RisingWave{
				ObjectMeta: objectMeta,
				Spec: rwv1alpha1.RisingWaveSpec{
					EnableStandaloneMode: utils.Ptr(true),
					Image:                "v2",
				},
			},
		},
		"standalone-component": {
			in: &rwv1alpha1.RisingWave{
				ObjectMeta: objectMeta,
				Spec: rwv1alpha1.RisingWaveSpec{
					EnableStandaloneMode: utils.Ptr(true),
					Image:                "v1",
					Components: rwv1alpha1.RisingWaveComponentsSpec{
						Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
							Template: rwv1alpha1.RisingWaveNodePodTemplate{
								Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
									RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
										Image: "v3",
									},
								},
							},
						},
					},
				},
			},
			tag: "v2",
			out: &rwv1alpha1.RisingWave{
				ObjectMeta: objectMeta,
				Spec: rwv1alpha1.RisingWaveSpec{
					EnableStandaloneMode: utils.Ptr(true),
					Image:                "v2",
					Components: rwv1alpha1.RisingWaveComponentsSpec{
						Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
							Template: rwv1alpha1.RisingWaveNodePodTemplate{
								Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
									RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
										Image: "v2",
									},
								},
							},
						},
					},
				},
			},
		},
	}

	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			c := fake.NewClient(tc.in)
			p, err := NewProvider(NewProviderOption{
				Kc: &k8s.KubernetesClient{Client: c},
			})
			assert.NoError(t, err)

			err = p.UpdateRisingWaveImage(context.Background(), tc.in.Name, tc.in.Namespace, tc.tag)
			assert.NoError(t, err)

			// Get object after scaling.
			var out rwv1alpha1.RisingWave
			assert.NoError(t,
				c.Get(context.Background(), client.ObjectKey{
					Namespace: tc.in.Namespace,
					Name:      tc.in.Name,
				}, &out))

			assert.True(t, equality.Semantic.DeepEqual(tc.out.Spec, out.Spec))
		})
	}
}

func getExpectedStandaloneRW() *rwv1alpha1.RisingWaveSpec {
	expectedCPULimit := resource.NewScaledQuantity(500, resource.Milli)
	// Generate `s`` field
	_ = expectedCPULimit.String()
	expectedCPURequest := resource.NewScaledQuantity(250, resource.Milli)
	_ = expectedCPURequest.String()

	return &rwv1alpha1.RisingWaveSpec{
		Image:                       "test_image",
		EnableDefaultServiceMonitor: utils.Ptr(true),
		EnableFullKubernetesAddr:    utils.Ptr(true),
		FrontendServiceType:         corev1.ServiceTypeClusterIP,
		EnableStandaloneMode:        utils.Ptr(true),
		Configuration: rwv1alpha1.RisingWaveConfigurationSpec{
			RisingWaveNodeConfiguration: rwv1alpha1.RisingWaveNodeConfiguration{
				ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
					Name: "test_cm",
					Key:  "test_key",
				},
			},
		},
		StateStore: rwv1alpha1.RisingWaveStateStoreBackend{
			DataDirectory: "test_dir",
			GCS: &rwv1alpha1.RisingWaveStateStoreBackendGCS{
				RisingWaveGCSCredentials: rwv1alpha1.RisingWaveGCSCredentials{
					UseWorkloadIdentity: utils.Ptr(true),
				},
				Bucket: "test_bucket",
				Root:   "test_dir",
			},
		},
		MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
			Etcd: &rwv1alpha1.RisingWaveMetaStoreBackendEtcd{
				Endpoint: "test_etcd",
			},
		},
		Components: rwv1alpha1.RisingWaveComponentsSpec{
			Standalone: &rwv1alpha1.RisingWaveStandaloneComponent{
				Replicas:        1,
				LogLevel:        "info",
				UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRollingUpdate},
				Template: rwv1alpha1.RisingWaveNodePodTemplate{
					Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
						RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
							Resources: corev1.ResourceRequirements{
								Limits: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("500m"),
									corev1.ResourceMemory: resource.MustParse("128Mi"),
								},
								Requests: corev1.ResourceList{
									corev1.ResourceCPU:    resource.MustParse("250m"),
									corev1.ResourceMemory: resource.MustParse("64Mi"),
								},
							},
						},
					},
				},
			},
			Meta:      rwv1alpha1.RisingWaveComponent{},
			Frontend:  rwv1alpha1.RisingWaveComponent{},
			Compute:   rwv1alpha1.RisingWaveComponent{},
			Compactor: rwv1alpha1.RisingWaveComponent{},
		},
	}
}

func getExpectedRWInstanceWithAffinityOverridden(affinity *corev1.Affinity) *rwv1alpha1.RisingWaveSpec {
	rw := getExpectedRWInstance()
	rw.Components.Meta.NodeGroups[0].Template.Spec.Affinity = affinity
	rw.Components.Compute.NodeGroups[0].Template.Spec.Affinity = affinity
	rw.Components.Compactor.NodeGroups[0].Template.Spec.Affinity = affinity
	rw.Components.Frontend.NodeGroups[0].Template.Spec.Affinity = affinity
	return rw
}

func getExpectedRWInstance() *rwv1alpha1.RisingWaveSpec {
	expected := utils.GetDummyRwSpec(false)
	expected.Components.Meta.NodeGroups[0].Replicas = 2
	expectedMetaCPU := resource.NewScaledQuantity(9, resource.Milli)
	expectedMetaRAM := resource.NewQuantity(9*1024*1024, "BinarySI")
	_ = expectedMetaCPU.String()
	_ = expectedMetaRAM.String()
	expected.Components.Meta.NodeGroups[0].Template.Spec.Resources = corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedMetaCPU,
			corev1.ResourceMemory: *expectedMetaRAM,
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedMetaCPU,
			corev1.ResourceMemory: *expectedMetaRAM,
		},
	}

	expected.Components.Frontend.NodeGroups[0].Replicas = 3
	expectedFrontendCPU := resource.NewScaledQuantity(99, resource.Milli)
	expectedFrontendRAM := resource.NewQuantity(99*1024*1024, "BinarySI")
	_ = expectedFrontendCPU.String()
	_ = expectedFrontendRAM.String()
	expected.Components.Frontend.NodeGroups[0].Template.Spec.Resources = corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedFrontendCPU,
			corev1.ResourceMemory: *expectedFrontendRAM,
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedFrontendCPU,
			corev1.ResourceMemory: *expectedFrontendRAM,
		},
	}

	expected.Components.Compute.NodeGroups[0].Replicas = 4
	expectedComputeCPU := resource.NewScaledQuantity(999, resource.Milli)
	expectedComputeRAM := resource.NewQuantity(999*1024*1024, "BinarySI")
	_ = expectedComputeCPU.String()
	_ = expectedComputeRAM.String()
	expected.Components.Compute.NodeGroups[0].Template.Spec.Resources = corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedComputeCPU,
			corev1.ResourceMemory: *expectedComputeRAM,
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedComputeCPU,
			corev1.ResourceMemory: *expectedComputeRAM,
		},
	}

	expected.Components.Compactor.NodeGroups[0].Replicas = 5
	expectedCompactorCPU := resource.NewScaledQuantity(9999, resource.Milli)
	expectedCompactorRAM := resource.NewQuantity(9999*1024*1024, "BinarySI")
	_ = expectedCompactorCPU.String()
	_ = expectedCompactorRAM.String()
	expected.Components.Compactor.NodeGroups[0].Template.Spec.Resources = corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedCompactorCPU,
			corev1.ResourceMemory: *expectedCompactorRAM,
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedCompactorCPU,
			corev1.ResourceMemory: *expectedCompactorRAM,
		},
	}
	return expected
}

func TestScaleRisingWave(t *testing.T) {
	newAffinityPB := &pbk8s.Affinity{
		NodeAffinity: &pbk8s.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
				NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
					{
						MatchExpressions: []*pbk8s.NodeSelectorRequirement{
							{
								Key:      "foo",
								Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_IN,
								Values:   []string{"bar"},
							},
						},
					},
				},
			},
		},
	}

	newAffinity := &corev1.Affinity{
		NodeAffinity: &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      "foo",
								Operator: corev1.NodeSelectorOpIn,
								Values:   []string{"bar"},
							},
						},
					},
				},
			},
		},
	}
	tests := []struct {

		// Switching between cluster and standalone mode is currently not supported. Skip those tests
		// https://linear.app/risingwave-labs/issue/CLOUD-2212/[devtiercontrolplane]-support-scaling-from-dev-tier
		skip bool

		// purpose of test
		description string

		// if True, then originally created RW is standalone
		isStandalone bool

		// scales originally created RW
		scalingOptions ScaleRisingWaveOption

		// expected rw spec after scaling
		expected *rwv1alpha1.RisingWaveSpec

		// If true, then expected scaling operation to fail
		shouldFail bool
	}{
		{
			description:  "Scaling cluster to 0 replicas",
			isStandalone: false,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 0,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
				FrontendScaleSepc: &pbrw.ScaleSpec{
					Replicas: 0,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "99m",
						CpuLimit:      "99m",
						MemoryRequest: "99Mi",
						MemoryLimit:   "99Mi",
					},
				},
				ComputeScaleSepc: &pbrw.ScaleSpec{
					Replicas: 0,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "999m",
						CpuLimit:      "999m",
						MemoryRequest: "999Mi",
						MemoryLimit:   "999Mi",
					},
				},
				CompactorScaleSepc: &pbrw.ScaleSpec{
					Replicas: 0,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9999m",
						CpuLimit:      "9999m",
						MemoryRequest: "9999Mi",
						MemoryLimit:   "9999Mi",
					},
				},
			},
			expected: func() *rwv1alpha1.RisingWaveSpec {
				i := getExpectedRWInstance()
				i.Components.Meta.NodeGroups[0].Replicas = 0
				i.Components.Compactor.NodeGroups[0].Replicas = 0
				i.Components.Compute.NodeGroups[0].Replicas = 0
				i.Components.Frontend.NodeGroups[0].Replicas = 0
				i.EnableStandaloneMode = utils.Ptr(false)
				return i
			}(),
			shouldFail: false,
		},
		{
			description:  "Scaling standalone to 0 replicas",
			shouldFail:   false,
			isStandalone: true,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 0,
				},
			},
			// We expect that Standalone has 0 replicas. The other values should not get changed
			expected: func() *rwv1alpha1.RisingWaveSpec {
				i := utils.GetDummyRwSpec(true)
				i.Components.Standalone.Replicas = 0
				i.Components.Meta.NodeGroups[0].Replicas = 1
				i.Components.Frontend.NodeGroups[0].Replicas = 1
				i.Components.Compute.NodeGroups[0].Replicas = 1
				i.Components.Compactor.NodeGroups[0].Replicas = 1
				i.EnableStandaloneMode = utils.Ptr(true)
				return i
			}(),
		},
		{
			description:  "Passing non-standalone to scale should switch to cluster mode",
			skip:         true,
			shouldFail:   false,
			isStandalone: true,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
			},
		},
		{
			description:  "Passing standalone to scale cluster should fail",
			shouldFail:   true,
			isStandalone: false,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 1,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
			},
		},
		{
			description:  "Scaling up cluster instance",
			isStandalone: false,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
				FrontendScaleSepc: &pbrw.ScaleSpec{
					Replicas: 3,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "99m",
						CpuLimit:      "99m",
						MemoryRequest: "99Mi",
						MemoryLimit:   "99Mi",
					},
				},
				ComputeScaleSepc: &pbrw.ScaleSpec{
					Replicas: 4,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "999m",
						CpuLimit:      "999m",
						MemoryRequest: "999Mi",
						MemoryLimit:   "999Mi",
					},
				},
				CompactorScaleSepc: &pbrw.ScaleSpec{
					Replicas: 5,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9999m",
						CpuLimit:      "9999m",
						MemoryRequest: "9999Mi",
						MemoryLimit:   "9999Mi",
					},
				},
			},
			expected:   getExpectedRWInstance(),
			shouldFail: false,
		},
		{
			description:  "Scaling up cluster instance with affinity override",
			isStandalone: false,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
					Affinity: newAffinityPB,
				},
				FrontendScaleSepc: &pbrw.ScaleSpec{
					Replicas: 3,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "99m",
						CpuLimit:      "99m",
						MemoryRequest: "99Mi",
						MemoryLimit:   "99Mi",
					},
					Affinity: newAffinityPB,
				},
				ComputeScaleSepc: &pbrw.ScaleSpec{
					Replicas: 4,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "999m",
						CpuLimit:      "999m",
						MemoryRequest: "999Mi",
						MemoryLimit:   "999Mi",
					},
					Affinity: newAffinityPB,
				},
				CompactorScaleSepc: &pbrw.ScaleSpec{
					Replicas: 5,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9999m",
						CpuLimit:      "9999m",
						MemoryRequest: "9999Mi",
						MemoryLimit:   "9999Mi",
					},
					Affinity: newAffinityPB,
				},
			},
			expected:   getExpectedRWInstanceWithAffinityOverridden(newAffinity),
			shouldFail: false,
		},
		{
			description:  "Scaling up standalone instance should fail",
			isStandalone: true,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
			},
			shouldFail: true,
		},
		{
			skip:         true,
			description:  "Switch from cluster instance to standalone. Pass not allowed components should fail",
			isStandalone: true,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
			},
			shouldFail: true,
		},
		{
			skip:         true,
			description:  "Switch from standalone to cluster instance",
			expected:     getExpectedRWInstance(),
			isStandalone: true,
			shouldFail:   false,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				MetaScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9m",
						CpuLimit:      "9m",
						MemoryRequest: "9Mi",
						MemoryLimit:   "9Mi",
					},
				},
				FrontendScaleSepc: &pbrw.ScaleSpec{
					Replicas: 3,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "99m",
						CpuLimit:      "99m",
						MemoryRequest: "99Mi",
						MemoryLimit:   "99Mi",
					},
				},
				ComputeScaleSepc: &pbrw.ScaleSpec{
					Replicas: 4,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "999m",
						CpuLimit:      "999m",
						MemoryRequest: "999Mi",
						MemoryLimit:   "999Mi",
					},
				},
				CompactorScaleSepc: &pbrw.ScaleSpec{
					Replicas: 5,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "9999m",
						CpuLimit:      "9999m",
						MemoryRequest: "9999Mi",
						MemoryLimit:   "9999Mi",
					},
				},
			},
		},
		{
			skip:         true,
			description:  "Switch from cluster instance to standalone",
			isStandalone: false,
			expected:     getExpectedStandaloneRW(),
			shouldFail:   false,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 1,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "250m",
						CpuLimit:      "500m",
						MemoryRequest: "64Mi",
						MemoryLimit:   "128Mi",
					},
				},
			},
		},
		{
			skip:         true,
			description:  "Switching cluster to standalone with > 1 replica should fail",
			isStandalone: false,
			shouldFail:   true,
			scalingOptions: ScaleRisingWaveOption{
				ResourceID: "name",
				Namespace:  "ns",
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 2,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "250m",
						CpuLimit:      "500m",
						MemoryRequest: "64Mi",
						MemoryLimit:   "128Mi",
					},
				},
			},
		},
	}

	for _, test := range tests {
		if test.skip {
			continue
		}

		c := fake.NewClient(&rwv1alpha1.RisingWave{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "name",
				Namespace: "ns",
			},
			Spec: *utils.GetDummyRwSpec(test.isStandalone),
		})
		p := &Provider{
			kc: &k8s.KubernetesClient{Client: c},
		}
		ctx := context.Background()
		err := p.ScaleRisingWave(ctx, test.scalingOptions)
		if test.shouldFail {
			require.Errorf(t, err, test.description)
			continue
		}
		require.NoErrorf(t, err, test.description)

		rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
		require.NoErrorf(t, err, test.description)

		// printing Spec, since auto print only shows mem addr of fields and not their values
		expectedJSON := getPrettyJSON(t, test.expected)
		actualJSON := getPrettyJSON(t, rw.Spec)
		failMsg := fmt.Sprintf("%s.\n\nExpected: %s\n\nActual: %s", test.description, expectedJSON, actualJSON)
		assert.Equalf(t, expectedJSON, actualJSON, failMsg)
	}
}

// getPrettyJSON converts any to json and asserts that there is no error.
func getPrettyJSON(t *testing.T, x any) string {
	j, err := json.MarshalIndent(x, "", "  ")
	assert.NoError(t, err)
	return string(j)
}

func TestScaleRisingWaveReplicaOnly(t *testing.T) {
	c := fake.NewClient(&rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.ScaleRisingWave(ctx, ScaleRisingWaveOption{
		ResourceID: "name",
		Namespace:  "ns",
		MetaScaleSepc: &pbrw.ScaleSpec{
			Replicas: 2,
		},
		FrontendScaleSepc: &pbrw.ScaleSpec{
			Replicas: 3,
		},
		ComputeScaleSepc: &pbrw.ScaleSpec{
			Replicas: 4,
		},
		CompactorScaleSepc: &pbrw.ScaleSpec{
			Replicas: 5,
		},
	})
	require.NoError(t, err)

	expected := utils.GetDummyRwSpec(false)

	expected.Components.Meta.NodeGroups[0].Replicas = 2
	expected.Components.Frontend.NodeGroups[0].Replicas = 3
	expected.Components.Compute.NodeGroups[0].Replicas = 4
	expected.Components.Compactor.NodeGroups[0].Replicas = 5

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, *expected, rw.Spec)
}

func generateRisingWaveStandalone(modifiers ...func(*rwv1alpha1.RisingWave, *rwv1alpha1.RisingWaveStandaloneComponent)) *rwv1alpha1.RisingWave {
	standalone := &rwv1alpha1.RisingWaveStandaloneComponent{
		Replicas: 1,
		Template: rwv1alpha1.RisingWaveNodePodTemplate{
			Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
				RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
					Resources: corev1.ResourceRequirements{
						Limits: corev1.ResourceList{
							corev1.ResourceCPU:    resource.MustParse("1"),
							corev1.ResourceMemory: resource.MustParse("1Gi"),
						},
					},
				},
			},
		},
	}
	rw := &rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: rwv1alpha1.RisingWaveSpec{
			EnableStandaloneMode: utils.Ptr(true),
			Components: rwv1alpha1.RisingWaveComponentsSpec{
				Standalone: standalone,
			},
		},
	}
	for _, modifier := range modifiers {
		modifier(rw, standalone)
	}
	return rw
}

func TestScaleRisingWave_Standalone(t *testing.T) {
	testcases := map[string]struct {
		in  *rwv1alpha1.RisingWave
		opt ScaleRisingWaveOption
		out *rwv1alpha1.RisingWave
	}{
		"standalone-replicas": {
			in: generateRisingWaveStandalone(),
			opt: ScaleRisingWaveOption{
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 0,
				},
			},
			out: generateRisingWaveStandalone(func(rw *rwv1alpha1.RisingWave, standalone *rwv1alpha1.RisingWaveStandaloneComponent) {
				rw.Generation = 1
				standalone.Replicas = 0
			}),
		},
		"standalone-resources": {
			in: generateRisingWaveStandalone(),
			opt: ScaleRisingWaveOption{
				StandaloneScaleSepc: &pbrw.ScaleSpec{
					Replicas: 1,
					Resources: &pbk8s.ResourceRequirements{
						CpuRequest:    "1",
						CpuLimit:      "2",
						MemoryRequest: "1Gi",
						MemoryLimit:   "2Gi",
					},
				},
			},
			out: generateRisingWaveStandalone(func(rw *rwv1alpha1.RisingWave, standalone *rwv1alpha1.RisingWaveStandaloneComponent) {
				rw.Generation = 1
				standalone.Template.Spec.Resources.Limits = corev1.ResourceList{
					corev1.ResourceCPU:    resource.MustParse("2"),
					corev1.ResourceMemory: resource.MustParse("2Gi"),
				}
				standalone.Template.Spec.Resources.Requests = corev1.ResourceList{
					corev1.ResourceCPU:    resource.MustParse("1"),
					corev1.ResourceMemory: resource.MustParse("1Gi"),
				}
			}),
		},
	}

	for name, tc := range testcases {
		t.Run(name, func(t *testing.T) {
			c := fake.NewClient(tc.in)
			p, err := NewProvider(NewProviderOption{
				Kc: &k8s.KubernetesClient{Client: c},
			})
			assert.NoError(t, err)

			// Set options metadata.
			opt := tc.opt
			opt.Namespace, opt.ResourceID = tc.in.Namespace, tc.in.Name

			err = p.ScaleRisingWave(context.Background(), opt)
			assert.NoError(t, err)

			// Get object after scaling.
			var out rwv1alpha1.RisingWave
			assert.NoError(t,
				c.Get(context.Background(), client.ObjectKey{
					Namespace: opt.Namespace,
					Name:      opt.ResourceID,
				}, &out))

			assert.EqualValues(t, tc.out.Spec, out.Spec)
		})
	}
}

func TestStopStartRisingWave(t *testing.T) {
	resourceID := "name"
	namespace := "ns"
	tests := []struct {
		name              string
		initRisingWave    *rwv1alpha1.RisingWave
		wantErr           bool
		stoppedRisingWave *rwv1alpha1.RisingWave
		stoppedAnnotation string
		startOverrides    []*pbk8ssvc.RisingWaveReplicaOverride
		startedRisingWave *rwv1alpha1.RisingWave
	}{
		{
			name:    "not found",
			wantErr: true,
		},
		{
			name: "standalone",
			initRisingWave: generateRisingWaveStandalone(func(rw *rwv1alpha1.RisingWave, _ *rwv1alpha1.RisingWaveStandaloneComponent) {
				rw.ResourceVersion = "1000"
			}),
			stoppedRisingWave: generateRisingWaveStandalone(func(rw *rwv1alpha1.RisingWave, standalone *rwv1alpha1.RisingWaveStandaloneComponent) {
				rw.ResourceVersion = "1000"
				standalone.Replicas = 0
			}),
			stoppedAnnotation: `{"standalone/default":1}`,
		},
		{
			name: "regular",
			initRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			stoppedRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				(*nodeGroups)[0].Replicas = 0
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			stoppedAnnotation: `{"compactor/default":1,"compute/default":1,"frontend/default":1,"meta/default":1}`,
			startOverrides: []*pbk8ssvc.RisingWaveReplicaOverride{
				{Component: "meta", NodeGroup: "default", Replicas: 1},
				{Component: "compute", NodeGroup: "default", Replicas: 1},
				{Component: "frontend", NodeGroup: "default", Replicas: 1},
				{Component: "compactor", NodeGroup: "default", Replicas: 1},
			},
		},
		{
			name: "regular with node group",
			initRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				// groups: [default, rg1, rg2, backfill]
				rw.ResourceVersion = "1000"
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
					spec.Replicas = 2
				}))
				require.NoError(t, err)
				rg2, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
					spec.Replicas = 0
				}))
				require.NoError(t, err)
				backfill, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "backfill"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
				*nodeGroups = append(*nodeGroups, rg2)
				*nodeGroups = append(*nodeGroups, backfill)
			}),
			stoppedRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				// groups: [default, rg1, rg2, backfill]
				rw.ResourceVersion = "1000"
				(*nodeGroups)[0].Replicas = 0
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
					spec.Replicas = 0
				}))
				require.NoError(t, err)
				rg2, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
					spec.Replicas = 0
				}))
				require.NoError(t, err)
				backfill, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "backfill"
					spec.Replicas = 0
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
				*nodeGroups = append(*nodeGroups, rg2)
				*nodeGroups = append(*nodeGroups, backfill)
			}),
			stoppedAnnotation: `{"compute/backfill":1,"compute/default":1,"compute/rg1":2,"compute/rg2":0}`,
			startOverrides: []*pbk8ssvc.RisingWaveReplicaOverride{
				{Component: "meta", NodeGroup: "default", Replicas: 1},
				{Component: "compute", NodeGroup: "default", Replicas: 1},
				{Component: "frontend", NodeGroup: "default", Replicas: 1},
				{Component: "compactor", NodeGroup: "default", Replicas: 1},
			},
		},
		{
			name: "already stopped",
			initRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Annotations = map[string]string{RwLastRecordReplica: `{"compactor/default":1,"compute/default":1,"frontend/default":1,"meta/default":1}`}
				(*nodeGroups)[0].Replicas = 0
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			stoppedRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				(*nodeGroups)[0].Replicas = 0
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			startedRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			stoppedAnnotation: `{"compactor/default":1,"compute/default":1,"frontend/default":1,"meta/default":1}`,
		},
		{
			name: "already stopped but be modified",
			initRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Annotations = map[string]string{RwLastRecordReplica: `{"compactor/default":1,"compute/default":1,"frontend/default":1,"meta/default":1}`}
				(*nodeGroups)[0].Replicas = 2
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			wantErr: true,
		},
		{
			name: "override start",
			initRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			stoppedRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				(*nodeGroups)[0].Replicas = 0
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			startedRisingWave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				(*nodeGroups)[0].Replicas = 2
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			stoppedAnnotation: `{"compactor/default":1,"compute/default":1,"frontend/default":1,"meta/default":1}`,
			startOverrides: []*pbk8ssvc.RisingWaveReplicaOverride{
				{Component: "meta", NodeGroup: "default", Replicas: 2},
				{Component: "compute", NodeGroup: "default", Replicas: 2},
				{Component: "frontend", NodeGroup: "default", Replicas: 2},
				{Component: "compactor", NodeGroup: "default", Replicas: 2},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var initObjs []client.Object
			if tt.initRisingWave != nil {
				initObjs = append(initObjs, tt.initRisingWave)
			}
			c := fake.NewClient(initObjs...)

			p, err := NewProvider(NewProviderOption{
				Kc: &k8s.KubernetesClient{Client: c},
			})
			require.NoError(t, err)
			ctx := context.Background()

			err = p.StopRisingWave(ctx, resourceID, namespace)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
			require.NoError(t, err)
			require.EqualValues(t, tt.stoppedAnnotation, rw.Annotations[RwLastRecordReplica])

			// compare ignore annotations
			rw.Annotations = nil
			rw.ResourceVersion = "1000"
			require.EqualValues(t, tt.stoppedRisingWave, rw)

			err = p.StartRisingWave(ctx, resourceID, namespace, tt.startOverrides)
			require.NoError(t, err)
			rw, err = k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
			require.NoError(t, err)

			// compare ignore annotations
			rw.Annotations = nil
			rw.ResourceVersion = "1000"
			if tt.startedRisingWave == nil {
				tt.startedRisingWave = tt.initRisingWave
			}
			require.EqualValues(t, tt.startedRisingWave, rw)
		})
	}
}

func TestProvider_UpdateRisingWaveComponents(t *testing.T) {
	resourceID := "risingwave"
	namespace := "test-namespacw"
	emptyRw := &rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:            resourceID,
			Namespace:       namespace,
			ResourceVersion: "1000",
		},
	}
	generateRw := func(modifiers ...func(*rwv1alpha1.RisingWave)) *rwv1alpha1.RisingWave {
		rw := &rwv1alpha1.RisingWave{
			ObjectMeta: metav1.ObjectMeta{
				Name:            resourceID,
				Namespace:       namespace,
				ResourceVersion: "1001",
			},
		}
		for _, m := range modifiers {
			m(rw)
		}
		return rw
	}
	componentSpec := &pbrw.ComponentSpec{
		LogLevel: "info",
		NodeGroups: []*pbrw.NodeGroupSpec{
			{
				Name:     "meta",
				Replicas: 1,
				UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
					Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
				},
				NodePodSpec: &pbrw.NodePodSpec{
					ContainerSpec: &pbrw.NodePodContainerSpec{
						Envs: map[string]string{
							"key1": "val1",
						},
						Resources: &pbk8s.ResourceRequirements{
							CpuRequest:    "250m",
							CpuLimit:      "500m",
							MemoryRequest: "64Mi",
							MemoryLimit:   "128Mi",
						},
					},
					ServiceAccount: "test_sa",
					Labels:         map[string]string{"k1": "v1", "k2": "v2"},
				},
			},
		},
	}
	rwComponent, err := conversion.FromComponentSpecProto(componentSpec)
	require.NoError(t, err)

	standaloneSpec := &pbrw.StandaloneSpec{
		LogLevel: "info",
		Replicas: 1,
		UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{
			Type: pbrw.UpgradeStrategyType_ROLLING_UPDATE,
		},
		NodePodSpec: &pbrw.NodePodSpec{
			ContainerSpec: &pbrw.NodePodContainerSpec{
				Envs: map[string]string{
					"key1": "val1",
				},
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "250m",
					CpuLimit:      "500m",
					MemoryRequest: "64Mi",
					MemoryLimit:   "128Mi",
				},
			},
			ServiceAccount: "test_sa",
			Labels:         map[string]string{"k1": "v1", "k2": "v2"},
		},
	}
	rwStandalone, err := conversion.FromStandaloneSpecProto(standaloneSpec)
	require.NoError(t, err)

	tests := []struct {
		name           string
		initObjs       []client.Object
		option         UpdateRisingWaveComponentsOption
		wantErr        bool
		wantErrorCode  eris.Code
		wantRisingWave *rwv1alpha1.RisingWave
	}{
		{
			name: "update meta",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				MetaSpec: componentSpec,
			},
			wantRisingWave: generateRw(func(rw *rwv1alpha1.RisingWave) {
				rw.Spec.Components.Meta = rwComponent
			}),
		},
		{
			name: "update frontend",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				FrontendSpec: componentSpec,
			},
			wantRisingWave: generateRw(func(rw *rwv1alpha1.RisingWave) {
				rw.Spec.Components.Frontend = rwComponent
			}),
		},
		{
			name: "update compute",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				ComputeSpec: componentSpec,
			},
			wantRisingWave: generateRw(func(rw *rwv1alpha1.RisingWave) {
				rw.Spec.Components.Compute = rwComponent
			}),
		},
		{
			name: "update compactor",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				CompactorSpec: componentSpec,
			},
			wantRisingWave: generateRw(func(rw *rwv1alpha1.RisingWave) {
				rw.Spec.Components.Compactor = rwComponent
			}),
		},
		{
			name: "update standalone",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				StandaloneSpec: standaloneSpec,
			},
			wantRisingWave: generateRw(func(rw *rwv1alpha1.RisingWave) {
				rw.Spec.Components.Standalone = rwStandalone
			}),
		},
		{
			name: "update EnableStandaloneMode",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				EnableStandaloneMode: utils.Ptr(true),
			},
			wantRisingWave: generateRw(func(rw *rwv1alpha1.RisingWave) {
				rw.Spec.EnableStandaloneMode = utils.Ptr(true)
			}),
		},
		{
			name:          "not found",
			initObjs:      []client.Object{},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "invalid argument",
			initObjs: []client.Object{
				emptyRw,
			},
			option: UpdateRisingWaveComponentsOption{
				MetaSpec: &pbrw.ComponentSpec{
					NodeGroups: []*pbrw.NodeGroupSpec{
						{},
					},
				},
			},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := fake.NewClient(tt.initObjs...)
			p, err := NewProvider(NewProviderOption{
				Kc: &k8s.KubernetesClient{Client: c},
			})
			require.NoError(t, err)
			ctx := context.Background()

			tt.option.ResourceID = resourceID
			tt.option.Namespace = namespace
			err = p.UpdateRisingWaveComponents(ctx, tt.option)
			if tt.wantErr {
				require.Error(t, err)
				assert.Equal(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
			require.NoError(t, err)
			require.EqualValues(t, tt.wantRisingWave, rw)
		})
	}
}

func TestProvider_UpdateRisingWaveMetaStore(t *testing.T) {
	resourceID := "risingwave"
	namespace := "test-namespacw"
	postgresqlSpec := &pbrw.MetaStoreBackendPostgreSql{
		Host:     "localhost",
		Port:     5432,
		Database: "rw_meta",
		Credentials: &pbrw.PostgreSqlCredentials{
			SecretName: "risingwave.credential",
		},
	}
	tests := []struct {
		name           string
		spec           *pbrw.MetaStoreSpec
		initObjs       []client.Object
		wantErr        bool
		wantErrorCode  eris.Code
		wantRisingWave *rwv1alpha1.RisingWave
	}{
		{
			name: "regular case",
			spec: &pbrw.MetaStoreSpec{
				PostgresqlBackend: postgresqlSpec,
			},
			initObjs: []client.Object{
				&rwv1alpha1.RisingWave{
					ObjectMeta: metav1.ObjectMeta{
						Name:            resourceID,
						Namespace:       namespace,
						ResourceVersion: "1000",
					},
				},
			},
			wantRisingWave: &rwv1alpha1.RisingWave{
				ObjectMeta: metav1.ObjectMeta{
					Name:      resourceID,
					Namespace: namespace,
					Annotations: map[string]string{
						rwconsts.AnnotationBypassValidatingWebhook: "false",
					},
					ResourceVersion: "1002",
				},
				Spec: rwv1alpha1.RisingWaveSpec{
					MetaStore: rwv1alpha1.RisingWaveMetaStoreBackend{
						PostgreSQL: &rwv1alpha1.RisingWaveMetaStoreBackendPostgreSQL{
							Host:     postgresqlSpec.GetHost(),
							Port:     postgresqlSpec.GetPort(),
							Database: postgresqlSpec.GetDatabase(),
							RisingWaveDBCredentials: rwv1alpha1.RisingWaveDBCredentials{
								SecretName: postgresqlSpec.GetCredentials().GetSecretName(),
							},
						},
					},
				},
			},
		},
		{
			name:          "illegal arguments",
			spec:          &pbrw.MetaStoreSpec{},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
		{
			name: "not found",
			spec: &pbrw.MetaStoreSpec{
				PostgresqlBackend: postgresqlSpec,
			},
			initObjs:      []client.Object{},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			c := fake.NewClient(tt.initObjs...)
			p, err := NewProvider(NewProviderOption{
				Kc: &k8s.KubernetesClient{Client: c},
			})
			require.NoError(t, err)
			ctx := context.Background()

			err = p.UpdateRisingWaveMetaStore(ctx, resourceID, namespace, tt.spec)
			if tt.wantErr {
				require.Error(t, err)
				assert.Equal(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, p.kc, resourceID, namespace)
			require.NoError(t, err)
			require.EqualValues(t, tt.wantRisingWave, rw)
		})
	}
}

func TestProvider_UpdateRisingWaveMetaStoreInternalBypass(t *testing.T) {
	resourceID := "risingwave"
	namespace := "test-namespacw"
	postgresqlSpec := &pbrw.MetaStoreBackendPostgreSql{
		Host:     "localhost",
		Port:     5432,
		Database: "rw_meta",
		Credentials: &pbrw.PostgreSqlCredentials{
			SecretName: "risingwave.credential",
		},
	}
	spec := &pbrw.MetaStoreSpec{
		PostgresqlBackend: postgresqlSpec,
	}
	generateRw := func(modifiers ...func(*rwv1alpha1.RisingWave)) *rwv1alpha1.RisingWave {
		rw := &rwv1alpha1.RisingWave{
			ObjectMeta: metav1.ObjectMeta{
				Name:            resourceID,
				Namespace:       namespace,
				ResourceVersion: "1000",
			},
		}
		for _, modifier := range modifiers {
			modifier(rw)
		}
		return rw
	}

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ctx := context.Background()

	c := k8s.NewMockKubernetesClientInterface(ctrl)
	p, err := NewProvider(NewProviderOption{
		Kc: &k8s.KubernetesClient{Client: c},
	})
	require.NoError(t, err)

	initRw := generateRw()
	c.EXPECT().Get(ctx, client.ObjectKey{Namespace: namespace, Name: resourceID}, gomock.Any()).
		DoAndReturn(func(_ context.Context, _ client.ObjectKey, obj *rwv1alpha1.RisingWave, _ ...client.GetOption) error {
			*obj = *initRw
			return nil
		})

	// assert the first patch bypass the validating
	patchRw := generateRw(func(rw *rwv1alpha1.RisingWave) {
		rw.Annotations = map[string]string{
			rwconsts.AnnotationBypassValidatingWebhook: "true",
		}
		rw.Spec.MetaStore = rwv1alpha1.RisingWaveMetaStoreBackend{
			PostgreSQL: &rwv1alpha1.RisingWaveMetaStoreBackendPostgreSQL{
				Host:     postgresqlSpec.GetHost(),
				Port:     postgresqlSpec.GetPort(),
				Database: postgresqlSpec.GetDatabase(),
				RisingWaveDBCredentials: rwv1alpha1.RisingWaveDBCredentials{
					SecretName: postgresqlSpec.GetCredentials().GetSecretName(),
				},
			},
		}
	})
	c.EXPECT().Patch(ctx, patchRw, gomock.Any()).Return(nil)

	// assert the second patch set back the validating
	finalRw := generateRw(func(rw *rwv1alpha1.RisingWave) {
		rw.Annotations = map[string]string{
			rwconsts.AnnotationBypassValidatingWebhook: "false",
		}
		rw.Spec.MetaStore = rwv1alpha1.RisingWaveMetaStoreBackend{
			PostgreSQL: &rwv1alpha1.RisingWaveMetaStoreBackendPostgreSQL{
				Host:     postgresqlSpec.GetHost(),
				Port:     postgresqlSpec.GetPort(),
				Database: postgresqlSpec.GetDatabase(),
				RisingWaveDBCredentials: rwv1alpha1.RisingWaveDBCredentials{
					SecretName: postgresqlSpec.GetCredentials().GetSecretName(),
				},
			},
		}
	})
	c.EXPECT().Patch(ctx, finalRw, gomock.Any()).Return(nil)

	err = p.UpdateRisingWaveMetaStore(ctx, resourceID, namespace, spec)
	require.NoError(t, err)
}

func TestUpdateRisingWaveLicenseKey(t *testing.T) {
	c := fake.NewClient(&rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.UpdateRisingWaveLicenseKey(ctx, "name", "ns", "test-secret-name")
	require.NoError(t, err)

	expected := utils.GetDummyRwSpec(false)
	expected.LicenseKey.SecretName = "test-secret-name"

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
	require.NoError(t, err)

	assert.Equal(t, *expected, rw.Spec)
}

func TestUpdateRisingWaveSecretStore(t *testing.T) {
	c := fake.NewClient(&rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.UpdateRisingWaveSecretStore(ctx, "name", "ns", "test-secret-name", "test-secret-key")
	require.NoError(t, err)

	expected := utils.GetDummyRwSpec(false)
	expected.SecretStore = rwv1alpha1.RisingWaveSecretStore{
		PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
			SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
				Name: "test-secret-name",
				Key:  "test-secret-key",
			},
		},
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
	require.NoError(t, err)

	assert.Equal(t, *expected, rw.Spec)
}

func TestUpdateRisingWaveSecretStoreAlreadyExists(t *testing.T) {
	risingwaveSpec := &rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: *utils.GetDummyRwSpec(false),
	}
	risingwaveSpec.Spec.SecretStore = rwv1alpha1.RisingWaveSecretStore{
		PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
			SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
				Name: "test-secret-name",
				Key:  "test-secret-key",
			},
		},
	}

	c := fake.NewClient(risingwaveSpec)
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	ctx := context.Background()
	err := p.UpdateRisingWaveSecretStore(ctx, "name", "ns", "test-secret-name-1", "test-secret-key-1")
	require.Error(t, err)

	expected := utils.GetDummyRwSpec(false)
	expected.SecretStore = rwv1alpha1.RisingWaveSecretStore{
		PrivateKey: rwv1alpha1.RisingWaveSecretStorePrivateKey{
			SecretRef: &rwv1alpha1.RisingWaveSecretStorePrivateKeySecretReference{
				Name: "test-secret-name",
				Key:  "test-secret-key",
			},
		},
	}

	rw, err := k8s.GetResource[rwv1alpha1.RisingWave](ctx, c, "name", "ns")
	require.NoError(t, err)

	assert.Equal(t, *expected, rw.Spec)
}

func generateNodeGroupSpec(modifiers ...func(*pbrw.NodeGroupSpec)) *pbrw.NodeGroupSpec {
	spec := &pbrw.NodeGroupSpec{
		Name:            "test-rg",
		Replicas:        1,
		UpgradeStrategy: &pbrw.NodeGroupUpgradeStrategy{Type: pbrw.UpgradeStrategyType_RECREATE},
		NodePodSpec: &pbrw.NodePodSpec{
			ContainerSpec: &pbrw.NodePodContainerSpec{
				Resources: &pbk8s.ResourceRequirements{
					CpuRequest:    "1",
					CpuLimit:      "1",
					MemoryRequest: "1Gi",
					MemoryLimit:   "1Gi",
				},
				Envs: map[string]string{},
			},
		},
	}
	for _, modifier := range modifiers {
		modifier(spec)
	}
	return spec
}

func generateRisingWaveWithNodeGroup(modifiers ...func(*rwv1alpha1.RisingWave, *[]rwv1alpha1.RisingWaveNodeGroup)) *rwv1alpha1.RisingWave {
	nodeGroups := []rwv1alpha1.RisingWaveNodeGroup{
		{
			Name:     "default",
			Replicas: 1,
			UpgradeStrategy: rwv1alpha1.RisingWaveNodeGroupUpgradeStrategy{
				Type: rwv1alpha1.RisingWaveUpgradeStrategyTypeRecreate,
			},
			Template: rwv1alpha1.RisingWaveNodePodTemplate{
				Spec: rwv1alpha1.RisingWaveNodePodTemplateSpec{
					RisingWaveNodeContainer: rwv1alpha1.RisingWaveNodeContainer{
						Resources: corev1.ResourceRequirements{
							Limits: corev1.ResourceList{
								corev1.ResourceCPU:    resource.MustParse("1"),
								corev1.ResourceMemory: resource.MustParse("1Gi"),
							},
							Requests: corev1.ResourceList{
								corev1.ResourceCPU:    resource.MustParse("1"),
								corev1.ResourceMemory: resource.MustParse("1Gi"),
							},
						},
					},
				},
			},
		},
	}
	rw := &rwv1alpha1.RisingWave{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "name",
			Namespace: "ns",
		},
		Spec: rwv1alpha1.RisingWaveSpec{
			Components: rwv1alpha1.RisingWaveComponentsSpec{
				Compute: rwv1alpha1.RisingWaveComponent{
					NodeGroups: nodeGroups,
				},
			},
		},
	}
	for _, modifier := range modifiers {
		modifier(rw, &nodeGroups)
	}
	rw.Spec.Components.Compute.NodeGroups = nodeGroups
	return rw
}

func TestProvider_CreateRisingWaveComputeNodeGroup(t *testing.T) {
	tests := []struct {
		name          string
		risingwave    *rwv1alpha1.RisingWave
		option        CreateRisingWaveNodeGroupOption
		wantErr       bool
		wantErrorCode eris.Code
		want          []*pbrw.NodeGroupSpec
	}{
		{
			name:       "invalid argument",
			risingwave: generateRisingWaveWithNodeGroup(),
			option: CreateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec: generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.NodePodSpec.ContainerSpec.Resources.CpuLimit = "xyz"
				}),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
		{
			name:       "not found",
			risingwave: nil,
			option: CreateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec:          generateNodeGroupSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name:       "already exist",
			risingwave: generateRisingWaveWithNodeGroup(),
			option: CreateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec: generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "default"
				}),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "normal",
			// groups: [default]
			risingwave: generateRisingWaveWithNodeGroup(),
			option: CreateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				// add: test-rg
				Spec: generateNodeGroupSpec(),
			},
			wantErr: false,
			want: []*pbrw.NodeGroupSpec{
				// groups: [default, test-rg]
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "default"
				}),
				generateNodeGroupSpec(),
			},
		},
		{
			name: "normal more rg",
			risingwave: generateRisingWaveWithNodeGroup(func(_ *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				// groups: [default, rg1]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
			}),
			option: CreateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				// add: rg2
				Spec: generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
				}),
			},
			wantErr: false,
			want: []*pbrw.NodeGroupSpec{
				// groups: [default, rg1, rg2]
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "default"
				}),
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}),
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
				}),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var objs []client.Object
			if tt.risingwave != nil {
				objs = append(objs, tt.risingwave)
			}
			c := fake.NewClient(objs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.CreateRisingWaveNodeGroup(context.Background(), tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, eris.GetCode(err), tt.wantErrorCode)
				return
			}
			require.NoError(t, err)

			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](context.Background(), c, "name", "ns")
			require.NoError(t, err)

			var result []*pbrw.NodeGroupSpec
			for _, it := range rw.Spec.Components.Compute.NodeGroups {
				proto, err := conversion.ToNodeGroupSpecProto(it)
				require.NoError(t, err)
				result = append(result, proto)
			}

			require.EqualValues(t, tt.want, result)
		})
	}
}

func TestProvider_UpdateRisingWaveComputeNodeGroup(t *testing.T) {
	tests := []struct {
		name          string
		risingwave    *rwv1alpha1.RisingWave
		option        UpdateRisingWaveNodeGroupOption
		wantErr       bool
		wantErrorCode eris.Code
		want          []*pbrw.NodeGroupSpec
	}{
		{
			name:       "invalid argument",
			risingwave: generateRisingWaveWithNodeGroup(),
			option: UpdateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec: generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.NodePodSpec.ContainerSpec.Resources.CpuLimit = "xyz"
				}),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
		{
			name:       "not found rw",
			risingwave: nil,
			option: UpdateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec:          generateNodeGroupSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name:       "not found node group",
			risingwave: generateRisingWaveWithNodeGroup(),
			option: UpdateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec:          generateNodeGroupSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "already exist",
			risingwave: generateRisingWaveWithNodeGroup(func(_ *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				ng, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec())
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, ng)
			}),
			option: UpdateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				Spec:          generateNodeGroupSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name: "normal",
			risingwave: generateRisingWaveWithNodeGroup(func(_ *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				// groups: [default, test-rg]
				ng, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec())
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, ng)
			}),
			option: UpdateRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				// update: test-rg
				Spec: generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Replicas = 2
				}),
			},
			wantErr: false,
			want: []*pbrw.NodeGroupSpec{
				// groups: [default, test-rg]
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "default"
				}),
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Replicas = 2
				}),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var objs []client.Object
			if tt.risingwave != nil {
				objs = append(objs, tt.risingwave)
			}
			c := fake.NewClient(objs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.UpdateRisingWaveNodeGroup(context.Background(), tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, eris.GetCode(err), tt.wantErrorCode)
				return
			}
			require.NoError(t, err)

			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](context.Background(), c, "name", "ns")
			require.NoError(t, err)

			var result []*pbrw.NodeGroupSpec
			for _, it := range rw.Spec.Components.Compute.NodeGroups {
				proto, err := conversion.ToNodeGroupSpecProto(it)
				require.NoError(t, err)
				result = append(result, proto)
			}

			require.EqualValues(t, tt.want, result)
		})
	}
}

func TestProvider_DeleteRisingWaveComputeNodeGroup(t *testing.T) {
	tests := []struct {
		name          string
		risingwave    *rwv1alpha1.RisingWave
		option        DeleteRisingWaveNodeGroupOption
		wantErr       bool
		wantErrorCode eris.Code
		want          []*pbrw.NodeGroupSpec
	}{
		{
			name:       "not found rw",
			risingwave: nil,
			option: DeleteRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				NodeGroup:     "test-rg",
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name:       "not found node group",
			risingwave: generateRisingWaveWithNodeGroup(),
			option: DeleteRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				NodeGroup:     "test-rg",
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
		{
			name: "normal",
			risingwave: generateRisingWaveWithNodeGroup(func(_ *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				// groups: [default, rg1, rg2]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
				rg2, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg2)
			}),
			option: DeleteRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				// remove rg1
				NodeGroup: "rg1",
			},
			wantErr: false,
			want: []*pbrw.NodeGroupSpec{
				// groups: [default, rg2]
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "default"
				}),
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
				}),
			},
		},
		{
			name: "normal delete last",
			risingwave: generateRisingWaveWithNodeGroup(func(_ *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				// groups: [default, rg1, rg2]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
				rg2, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg2"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg2)
			}),
			option: DeleteRisingWaveNodeGroupOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				// remove rg2
				NodeGroup: "rg2",
			},
			wantErr: false,
			want: []*pbrw.NodeGroupSpec{
				// groups: [default, rg2]
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "default"
				}),
				generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}),
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var objs []client.Object
			if tt.risingwave != nil {
				objs = append(objs, tt.risingwave)
			}
			c := fake.NewClient(objs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.DeleteRisingWaveNodeGroup(context.Background(), tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, eris.GetCode(err), tt.wantErrorCode)
				return
			}
			require.NoError(t, err)

			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](context.Background(), c, "name", "ns")
			require.NoError(t, err)

			var result []*pbrw.NodeGroupSpec
			for _, it := range rw.Spec.Components.Compute.NodeGroups {
				proto, err := conversion.ToNodeGroupSpecProto(it)
				require.NoError(t, err)
				result = append(result, proto)
			}

			require.EqualValues(t, tt.want, result)
		})
	}
}

func TestProvider_UpdateRisingWaveNodeGroupConfiguration(t *testing.T) {
	tests := []struct {
		name       string
		risingwave *rwv1alpha1.RisingWave
		option     UpdateRisingWaveNodeGroupConfigurationOption
		wantErr    bool
		want       *rwv1alpha1.RisingWave
	}{
		{
			name: "normal",
			risingwave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			option: UpdateRisingWaveNodeGroupConfigurationOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_FRONTEND,
				NodeGroup:     "default",
				Spec: &pbrw.NodeConfig{
					NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
						Name: "frontend-config",
					},
				},
			},
			wantErr: false,
			want: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups

				frontend := (*nodeGroups)[0]
				frontend.Configuration = &rwv1alpha1.RisingWaveNodeConfiguration{
					ConfigMap: &rwv1alpha1.RisingWaveNodeConfigurationConfigMapSource{
						Name: "frontend-config",
					},
				}
				rw.Spec.Components.Frontend.NodeGroups = []rwv1alpha1.RisingWaveNodeGroup{frontend}
			}),
		},
		{
			name: "node group",
			risingwave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				// groups: [default, rg1]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
					spec.NodeConfig = &pbrw.NodeConfig{
						NodeConfigurationConfigMap: &pbrw.NodeConfigurationConfigMap{
							Name: "rg1-config",
						},
					}
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
			}),
			option: UpdateRisingWaveNodeGroupConfigurationOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				NodeGroup:     "rg1",
				Spec:          nil,
			},
			wantErr: false,
			want: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				// groups: [default, rg1]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
			}),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var objs []client.Object
			if tt.risingwave != nil {
				objs = append(objs, tt.risingwave)
			}
			c := fake.NewClient(objs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.UpdateRisingWaveNodeGroupConfiguration(context.Background(), tt.option)
			if tt.wantErr {
				require.Error(t, err)
			}
			require.NoError(t, err)

			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](context.Background(), c, "name", "ns")
			require.NoError(t, err)

			rw.ResourceVersion = "1000"
			require.EqualValues(t, tt.want, rw)
		})
	}
}

func TestProvider_UpdateRisingWaveNodeGroupRestartAt(t *testing.T) {
	restartAt, err := time.Parse(time.RFC3339, "2022-01-01T00:00:00Z")
	require.NoError(t, err)

	tests := []struct {
		name       string
		risingwave *rwv1alpha1.RisingWave
		option     UpdateRisingWaveNodeGroupRestartAtOption
		wantErr    bool
		want       *rwv1alpha1.RisingWave
	}{
		{
			name: "normal",
			risingwave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Frontend.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups
			}),
			option: UpdateRisingWaveNodeGroupRestartAtOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_FRONTEND,
				NodeGroup:     "default",
				RestartAt:     pbtimestamp.New(restartAt),
			},
			wantErr: false,
			want: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				rw.Spec.Components.Meta.NodeGroups = *nodeGroups
				rw.Spec.Components.Compactor.NodeGroups = *nodeGroups

				frontend := (*nodeGroups)[0]
				frontend.RestartAt = &metav1.Time{Time: restartAt.Local()}
				rw.Spec.Components.Frontend.NodeGroups = []rwv1alpha1.RisingWaveNodeGroup{frontend}
			}),
		},
		{
			name: "node group",
			risingwave: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				// groups: [default, rg1]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}))
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
			}),
			option: UpdateRisingWaveNodeGroupRestartAtOption{
				ResourceID:    "name",
				Namespace:     "ns",
				ComponentType: pbrw.ComponentType_COMPUTE,
				NodeGroup:     "rg1",
				RestartAt:     pbtimestamp.New(restartAt),
			},
			wantErr: false,
			want: generateRisingWaveWithNodeGroup(func(rw *rwv1alpha1.RisingWave, nodeGroups *[]rwv1alpha1.RisingWaveNodeGroup) {
				rw.ResourceVersion = "1000"
				// groups: [default, rg1]
				rg1, err := conversion.FromNodeGroupSpecProto(generateNodeGroupSpec(func(spec *pbrw.NodeGroupSpec) {
					spec.Name = "rg1"
				}))
				rg1.RestartAt = &metav1.Time{Time: restartAt.Local()}
				require.NoError(t, err)
				*nodeGroups = append(*nodeGroups, rg1)
			}),
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var objs []client.Object
			if tt.risingwave != nil {
				objs = append(objs, tt.risingwave)
			}
			c := fake.NewClient(objs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.UpdateRisingWaveNodeGroupRestartAt(context.Background(), tt.option)
			if tt.wantErr {
				require.Error(t, err)
			}
			require.NoError(t, err)

			rw, err := k8s.GetResource[rwv1alpha1.RisingWave](context.Background(), c, "name", "ns")
			require.NoError(t, err)

			rw.ResourceVersion = "1000"
			require.EqualValues(t, tt.want, rw)
		})
	}
}
