package k8s

import (
	"context"
	"errors"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"helm.sh/helm/v3/pkg/chart"
	"helm.sh/helm/v3/pkg/release"
	batchv1 "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"
	rbacv1 "k8s.io/api/rbac/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	pbcommonk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/helmx"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/taskconfig"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func prepareProviderForHelmReleaseTests(t *testing.T) *Provider {
	t.Helper()

	p, err := NewProvider(NewProviderOption{
		Kc: &k8s.KubernetesClient{
			Client: fake.NewClient(),
			Interface: fake.NewClientset(
				&rbacv1.ClusterRole{
					ObjectMeta: metav1.ObjectMeta{
						Name: "role-name",
					},
				},
			),
			TaskConfig: taskconfig.Config{
				Image:          "image",
				ServiceAccount: "sa",
				Namespace:      "ns",
				PullPolicy:     corev1.PullAlways,
			},
		},
	})
	require.NoError(t, err)
	return p
}

func TestInstallHelmReleaseTask(t *testing.T) {
	var (
		ctx           = context.Background()
		installTaskID = "install-task"
		ResourceID    = "resource-id"
		namespace     = "ns"
		chartURL      = "https://registry.risingwave.cloud/etcd-9.0.4.tgz"
		valuesJSON    = "{}"
	)
	p := prepareProviderForHelmReleaseTests(t)

	// install
	err := p.InstallHelmRelease(ctx, InstallHelmReleaseOption{
		TaskID:     installTaskID,
		ResourceID: ResourceID,
		Namespace:  namespace,
		ChartURL:   chartURL,
		ValuesJSON: valuesJSON,
	})
	require.NoError(t, err)

	installJob := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      installTaskID,
			Namespace: namespace,
		},
	}

	err = p.kc.Get(ctx, client.ObjectKeyFromObject(installJob), installJob)
	require.NoError(t, err)
	require.Len(t, installJob.Spec.Template.Spec.Containers, 1)
	require.Len(t, installJob.Spec.Template.Spec.Containers[0].Env, 3)
	assert.Contains(t,
		installJob.Spec.Template.Spec.Containers[0].Env,
		corev1.EnvVar{Name: "XDG_DATA_HOME", Value: "/ephemeral/.local/share"},
		corev1.EnvVar{Name: "XDG_CACHE_HOME", Value: "/ephemeral/.cache"},
		corev1.EnvVar{Name: "XDG_CONFIG_HOME", Value: "/ephemeral/.config"},
	)
}

func TestUpgradeHelmReleaseTask(t *testing.T) {
	var (
		ctx           = context.Background()
		upgradeTaskID = "upgrade-task"
		ResourceID    = "resource-id"
		namespace     = "ns"
		chartURL      = "https://registry.risingwave.cloud/etcd-9.0.4.tgz"
		valuesJSON    = "{}"
	)
	p := prepareProviderForHelmReleaseTests(t)

	err := p.UpgradeHelmRelease(ctx, UpgradeHelmReleaseOption{
		TaskID:     upgradeTaskID,
		ResourceID: ResourceID,
		Namespace:  namespace,
		ChartURL:   chartURL,
		ValuesJSON: valuesJSON,
	})
	require.NoError(t, err)

	upgradeJob := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      upgradeTaskID,
			Namespace: namespace,
		},
	}

	err = p.kc.Get(ctx, client.ObjectKeyFromObject(upgradeJob), upgradeJob)
	require.NoError(t, err)
	require.Len(t, upgradeJob.Spec.Template.Spec.Containers, 1)
	require.Len(t, upgradeJob.Spec.Template.Spec.Containers[0].Env, 3)
	assert.Contains(t,
		upgradeJob.Spec.Template.Spec.Containers[0].Env,
		corev1.EnvVar{Name: "XDG_DATA_HOME", Value: "/ephemeral/.local/share"},
		corev1.EnvVar{Name: "XDG_CACHE_HOME", Value: "/ephemeral/.cache"},
		corev1.EnvVar{Name: "XDG_CONFIG_HOME", Value: "/ephemeral/.config"},
	)
}

func TestUninstallHelmReleaseTask(t *testing.T) {
	var (
		ctx             = context.Background()
		uninstallTaskID = "uninstall-tasl"
		ResourceID      = "resource-id"
		namespace       = "ns"
	)
	p := prepareProviderForHelmReleaseTests(t)

	err := p.UninstallHelmRelease(ctx, UninstallHelmReleaseOption{
		TaskID:     uninstallTaskID,
		ResourceID: ResourceID,
		Namespace:  namespace,
	})
	require.NoError(t, err)

	uninstallJob := &batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uninstallTaskID,
			Namespace: namespace,
		},
	}

	err = p.kc.Get(ctx, client.ObjectKeyFromObject(uninstallJob), uninstallJob)
	require.NoError(t, err)
	require.Len(t, uninstallJob.Spec.Template.Spec.Containers, 1)
	require.Len(t, uninstallJob.Spec.Template.Spec.Containers[0].Env, 3)
	assert.Contains(t,
		uninstallJob.Spec.Template.Spec.Containers[0].Env,
		corev1.EnvVar{Name: "XDG_DATA_HOME", Value: "/ephemeral/.local/share"},
		corev1.EnvVar{Name: "XDG_CACHE_HOME", Value: "/ephemeral/.cache"},
		corev1.EnvVar{Name: "XDG_CONFIG_HOME", Value: "/ephemeral/.config"},
	)
}

func TestGetHelmRelease(t *testing.T) {
	var (
		ctx           = context.Background()
		namespace     = "ns"
		customTestErr = errors.New("custom test error")
		version       = "1.0.0"
	)

	ctrl := gomock.NewController(t)
	svc := helmx.NewMockServiceInterface(ctrl)

	p := &Provider{
		helmSvc: svc,
	}

	svc.
		EXPECT().
		Get(gomock.Any(), gomock.Any()).
		AnyTimes().
		DoAndReturn(func(_ context.Context, option helmx.GetOption) (*release.Release, error) {
			rls := &release.Release{Info: &release.Info{}, Chart: &chart.Chart{Metadata: &chart.Metadata{Version: version}}}
			switch option.ReleaseName {
			case "uninstalling":
				rls.Info.Status = release.StatusUninstalling
			case "deployed":
				rls.Info.Status = release.StatusDeployed
			case "failed":
				rls.Info.Status = release.StatusFailed
			case "uninstalled":
				rls.Info.Status = release.StatusUninstalled
			case "pendingUpgrade":
				rls.Info.Status = release.StatusPendingUpgrade
			case "pendingInstall":
				rls.Info.Status = release.StatusPendingInstall
			case "error":
				return nil, customTestErr
			}
			return rls, nil
		})

	rls, err := p.GetHelmRelease(ctx, "uninstalling", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_UNINSTALLING, rls.GetStatus())
	assert.Equal(t, version, rls.GetVersion())

	rls, err = p.GetHelmRelease(ctx, "deployed", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_DEPLOYED, rls.GetStatus())

	rls, err = p.GetHelmRelease(ctx, "failed", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_FAILED, rls.GetStatus())

	rls, err = p.GetHelmRelease(ctx, "uninstalled", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_UNINSTALLED, rls.GetStatus())

	rls, err = p.GetHelmRelease(ctx, "pendingUpgrade", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_UPGRADING, rls.GetStatus())

	rls, err = p.GetHelmRelease(ctx, "pendingInstall", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_INSTALLING, rls.GetStatus())

	rls, err = p.GetHelmRelease(ctx, "error", namespace)
	assert.True(t, eris.Is(err, customTestErr))
	assert.Nil(t, rls)

	rls, err = p.GetHelmRelease(ctx, "unknown", namespace)
	require.NoError(t, err)
	assert.Equal(t, pbcommonk8s.HelmReleaseStatus_UNKNOWN, rls.GetStatus())
}

func TestInstallHelmReleaseTask_already_exists(t *testing.T) {
	var (
		ctx           = context.Background()
		installTaskID = "install-task"
		ResourceID    = "resource-id"
		namespace     = "ns"
		chartURL      = "https://registry.risingwave.cloud/etcd-9.0.4.tgz"
		valuesJSON    = "{}"
	)
	p := prepareProviderForHelmReleaseTests(t)
	p.kc.Client = fake.NewClient(&batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      installTaskID,
			Namespace: namespace,
		},
	})

	// install
	err := p.InstallHelmRelease(ctx, InstallHelmReleaseOption{
		TaskID:     installTaskID,
		ResourceID: ResourceID,
		Namespace:  namespace,
		ChartURL:   chartURL,
		ValuesJSON: valuesJSON,
	})
	require.True(t, utils.IsErrAlreadyExists(err))
}

func TestUpgradeHelmReleaseTask_already_exists(t *testing.T) {
	var (
		ctx           = context.Background()
		upgradeTaskID = "upgrade-task"
		ResourceID    = "resource-id"
		namespace     = "ns"
		chartURL      = "https://registry.risingwave.cloud/etcd-9.0.4.tgz"
		valuesJSON    = "{}"
	)
	p := prepareProviderForHelmReleaseTests(t)
	p.kc.Client = fake.NewClient(&batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      upgradeTaskID,
			Namespace: namespace,
		},
	})

	err := p.UpgradeHelmRelease(ctx, UpgradeHelmReleaseOption{
		TaskID:     upgradeTaskID,
		ResourceID: ResourceID,
		Namespace:  namespace,
		ChartURL:   chartURL,
		ValuesJSON: valuesJSON,
	})
	require.True(t, utils.IsErrAlreadyExists(err))
}

func TestUninstallHelmReleaseTask_already_exists(t *testing.T) {
	var (
		ctx             = context.Background()
		uninstallTaskID = "uninstall-tasl"
		ResourceID      = "resource-id"
		namespace       = "ns"
	)
	p := prepareProviderForHelmReleaseTests(t)
	p.kc.Client = fake.NewClient(&batchv1.Job{
		ObjectMeta: metav1.ObjectMeta{
			Name:      uninstallTaskID,
			Namespace: namespace,
		},
	})

	err := p.UninstallHelmRelease(ctx, UninstallHelmReleaseOption{
		TaskID:     uninstallTaskID,
		ResourceID: ResourceID,
		Namespace:  namespace,
	})
	require.True(t, utils.IsErrAlreadyExists(err))
}
