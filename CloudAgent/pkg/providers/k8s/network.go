package k8s

import (
	"context"
	"encoding/json"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	networkingv1 "k8s.io/api/networking/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

type CreateNetworkPolicyOption struct {
	ResourceID        string
	Namespace         string
	NetworkPolicySpec *pbk8s.NetworkPolicySpec
	Labels            map[string]string
}

func (p *Provider) CreateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error {
	spec, err := conversion.FromNetworkPolicySpecProto(option.NetworkPolicySpec)
	if err != nil {
		return eris.Wrapf(err, "invalid spec %v", option.NetworkPolicySpec)
	}
	policy := &networkingv1.NetworkPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Labels:    option.Labels,
		},
		Spec: spec,
	}
	if err := p.kc.Create(ctx, policy); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("network policy %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create network policy %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) DeleteNetworkPolicy(ctx context.Context, resourceID, namespace string) error {
	if err := k8s.DeleteResource[networkingv1.NetworkPolicy](ctx, p.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get network policy %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete network policy %s", resourceID)
	}
	return nil
}

func (p *Provider) CreateOrUpdateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error {
	spec, err := conversion.FromNetworkPolicySpecProto(option.NetworkPolicySpec)
	if err != nil {
		return eris.Wrapf(err, "invalid spec %v", option.NetworkPolicySpec)
	}
	policy := &networkingv1.NetworkPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
			Labels:    option.Labels,
		},
		Spec: spec,
	}
	if err := p.kc.Create(ctx, policy); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return p.updateNetworkPolicy(ctx, option)
		}
		return errors.Wrapf(err, "failed to create network policy %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) updateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error {
	spec, err := conversion.FromNetworkPolicySpecProto(option.NetworkPolicySpec)
	if err != nil {
		return eris.Wrapf(err, "invalid spec %v", option.NetworkPolicySpec)
	}

	newSpecBytes, err := json.Marshal(spec)
	if err != nil {
		return err
	}
	patchOps := []map[string]interface{}{
		{
			"op":    "replace",
			"path":  "/spec",
			"value": json.RawMessage(newSpecBytes), // Embed raw spec as value
		},
	}
	patchBytes, err := json.Marshal(patchOps)
	if err != nil {
		return err
	}
	obj := &networkingv1.NetworkPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
	}
	if err := p.kc.Patch(ctx, obj, client.RawPatch(types.JSONPatchType, patchBytes)); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get network policy name %s, ns %s", option.ResourceID, option.Namespace).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to update network policy %s", option.ResourceID)
	}
	return nil
}
