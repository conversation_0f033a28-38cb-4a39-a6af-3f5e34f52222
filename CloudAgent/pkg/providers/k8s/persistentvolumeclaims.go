package k8s

import (
	"context"
	"fmt"

	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

type CreatePVCOption struct {
	ResourceID string
	Namespace  string
	Spec       *pbk8s.PersistentVolumeClaimSpec
}

type PersistentVolumeClaimsMeta struct {
	Status *pbresource.Status
}

func (p *Provider) CreatePersistentVolumeClaim(ctx context.Context, option CreatePVCOption) error {
	spec, err := conversion.FromPersistentVolumeClaimSpecProto(option.Spec)
	if err != nil {
		return eris.Errorf("failed to convert PVC spec proto: %v", option.Spec).WithCode(eris.CodeInvalidArgument)
	}
	if err := p.kc.Create(ctx, &corev1.PersistentVolumeClaim{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Spec: spec,
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("rw %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create rw %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) DeletePersistentVolumeClaims(ctx context.Context, ns string) error {
	err := p.kc.DeleteAllOf(ctx, &corev1.PersistentVolumeClaim{}, client.InNamespace(ns))
	if err != nil {
		return eris.Wrap(err, fmt.Sprintf("failed to delete all PVCs by namespace %s", ns))
	}
	return nil
}

func (p *Provider) GetPersistenVolumeClaims(ctx context.Context, ns string) (*PersistentVolumeClaimsMeta, error) {
	pvcs, err := p.listPersistenVolumeClaims(ctx, ns)
	if err != nil {
		return nil, eris.Wrap(err, "failed to list pvcs")
	}

	code := pbresource.StatusCode_READY
	if len(pvcs) == 0 {
		code = pbresource.StatusCode_NOT_FOUND
	}

	return &PersistentVolumeClaimsMeta{
		Status: &pbresource.Status{
			Code: code,
		},
	}, nil
}

func (p *Provider) listPersistenVolumeClaims(ctx context.Context, ns string) ([]corev1.PersistentVolumeClaim, error) {
	var pvcList = &corev1.PersistentVolumeClaimList{}

	opts := []client.ListOption{
		client.InNamespace(ns),
	}

	err := p.kc.List(ctx, pvcList, opts...)
	if err != nil {
		return nil, err
	}
	return pvcList.Items, nil
}
