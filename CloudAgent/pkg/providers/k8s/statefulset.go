package k8s

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	appsv1 "k8s.io/api/apps/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"

	"sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
)

func (p *Provider) RestartStatefulSet(ctx context.Context, resourceID, namespace string) error {
	statefulSet, err := p.getStatefulSet(ctx, namespace, resourceID)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.New("stateful set not found").WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve statefulset  %s", resourceID)
	}

	patch := client.MergeFrom(statefulSet.DeepCopy())
	annotations := statefulSet.Spec.Template.Annotations
	annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)
	statefulSet.Spec.Template.Annotations = annotations

	if err := p.kc.Patch(context.Background(), statefulSet, patch); err != nil {
		return eris.Wrapf(err, "failed to patch statefulset %s", resourceID)
	}
	return nil
}

func (p *Provider) GetStatefulSetReplicaStatus(ctx context.Context, resourceID, namespace string) (*pbresource.Status, error) {
	statefulSet, err := p.getStatefulSet(ctx, namespace, resourceID)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to retrieve statefulset  %s", resourceID)
	}

	status := pbresource.StatusCode_NOT_READY
	if IsStatefulSetRolledOut(statefulSet) {
		status = pbresource.StatusCode_READY
	}

	return &pbresource.Status{
		Code: status,
	}, nil
}

// IsStatefulSetRolledOut returns true when the StatefulSet is rolled out.
func IsStatefulSetRolledOut(statefulSet *appsv1.StatefulSet) bool {
	if statefulSet == nil {
		return false
	}
	if statefulSet.Status.ObservedGeneration < statefulSet.Generation {
		return false
	}
	if statefulSet.Spec.Replicas != nil && statefulSet.Status.UpdatedReplicas < *statefulSet.Spec.Replicas {
		return false
	}
	if statefulSet.Status.Replicas > statefulSet.Status.UpdatedReplicas {
		return false
	}
	if statefulSet.Status.AvailableReplicas < statefulSet.Status.UpdatedReplicas {
		return false
	}
	return true
}

func (p *Provider) getStatefulSet(ctx context.Context, ns string, statefulsetName string) (*appsv1.StatefulSet, error) {
	rtn := &appsv1.StatefulSet{}
	err := p.kc.Get(ctx, client.ObjectKey{
		Name:      statefulsetName,
		Namespace: ns,
	}, rtn)
	if err != nil {
		return nil, err
	}
	return rtn, nil
}
