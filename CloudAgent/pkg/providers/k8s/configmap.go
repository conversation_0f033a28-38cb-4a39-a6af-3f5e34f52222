package k8s

import (
	"context"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type ConfigMap struct {
	Immutable  bool
	Data       map[string]string
	BinaryData map[string][]byte
}

type CreateConfigMapOption struct {
	ResourceID string
	Namespace  string
	ConfigMap  ConfigMap
}

type UpdateConfigMapOption struct {
	ResourceID string
	Namespace  string
	To         ConfigMap
}

type ConfigMapMeta struct {
	Status    *pbresource.Status
	ConfigMap *ConfigMap
}

func FromConfigMapProto(p *pbk8s.ConfigMap) ConfigMap {
	return ConfigMap{
		Immutable:  p.GetImmutable(),
		Data:       p.GetData(),
		BinaryData: p.GetBinaryData(),
	}
}

func toConfigMapResource(resourceID, namespace string, cm ConfigMap) *corev1.ConfigMap {
	return &corev1.ConfigMap{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
		Immutable:  utils.Ptr(cm.Immutable),
		Data:       cm.Data,
		BinaryData: cm.BinaryData,
	}
}

func (p *Provider) CreateConfigMap(ctx context.Context, option CreateConfigMapOption) error {
	if err := p.kc.Create(ctx, toConfigMapResource(
		option.ResourceID,
		option.Namespace,
		option.ConfigMap,
	)); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("config map %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create config map %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) GetConfigMap(ctx context.Context, resourceID, namespace string) (ConfigMapMeta, error) {
	cm, err := k8s.GetResource[corev1.ConfigMap](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return ConfigMapMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return ConfigMapMeta{}, errors.Wrapf(err, "failed to retrieve config map %s", resourceID)
	}

	immutable := false
	if cm.Immutable != nil {
		immutable = *cm.Immutable
	}
	return ConfigMapMeta{
		Status: &pbresource.Status{
			Code: pbresource.StatusCode_READY,
		},
		ConfigMap: &ConfigMap{
			Immutable:  immutable,
			Data:       cm.Data,
			BinaryData: cm.BinaryData,
		},
	}, nil
}

func (p *Provider) DeleteConfigMap(ctx context.Context, resourceID string, namespace string) error {
	if err := k8s.DeleteResource[corev1.ConfigMap](ctx, p.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get config map %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete config map %s", resourceID)
	}
	return nil
}

func (p *Provider) UpdateConfigMap(ctx context.Context, option UpdateConfigMapOption) error {
	from, err := k8s.GetResource[corev1.ConfigMap](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get config map %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
		}
		return err
	}
	if utils.Unwrap(from.Immutable) {
		return eris.Errorf("failed to update config map %s/%s, config map is immutable", option.Namespace, option.ResourceID).WithCode(eris.CodePermissionDenied)
	}

	to := toConfigMapResource(option.ResourceID, option.Namespace, option.To)
	err = k8s.MergePatchResource(ctx, p.kc, from, to)
	if err != nil {
		return err
	}
	return nil
}
