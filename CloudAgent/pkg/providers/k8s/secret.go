package k8s

import (
	"context"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type CreateSecretOption struct {
	ResourceID string
	Namespace  string
	Immutable  bool
	Data       map[string][]byte
	StringData map[string]string
	Type       string
}

type UpdateSecretOption struct {
	ResourceID string
	Namespace  string
	To         Secret
}

func toSecretResource(resourceID, namespace string, secret Secret) *corev1.Secret {
	return &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
		Immutable:  utils.Ptr(secret.Immutable),
		Data:       secret.Data,
		StringData: secret.StringData,
		Type:       corev1.SecretType(secret.Type),
	}
}

func FromSecretProto(p *pbk8s.Secret) Secret {
	return Secret{
		Immutable:  p.GetImmutable(),
		Data:       p.GetData(),
		StringData: p.GetStringData(),
		Type:       p.GetType(),
	}
}

type SecretMeta struct {
	Status *pbresource.Status
	Secret *Secret
}

type Secret struct {
	Immutable  bool
	StringData map[string]string
	Data       map[string][]byte
	Type       string
}

func (p *Provider) CreateSecret(ctx context.Context, option CreateSecretOption) error {
	if err := p.kc.Create(ctx, &corev1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      option.ResourceID,
			Namespace: option.Namespace,
		},
		Immutable:  &option.Immutable,
		Data:       option.Data,
		StringData: option.StringData,
		Type:       corev1.SecretType(option.Type),
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("secret %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create secret %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) GetSecret(ctx context.Context, resourceID, namespace string) (SecretMeta, error) {
	secret, err := k8s.GetResource[corev1.Secret](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return SecretMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return SecretMeta{}, errors.Wrapf(err, "failed to retrieve secret %s", resourceID)
	}

	return SecretMeta{
		Status: &pbresource.Status{
			Code: pbresource.StatusCode_READY,
		},
		Secret: &Secret{
			Immutable:  utils.Unwrap(secret.Immutable),
			StringData: secret.StringData,
			Data:       secret.Data,
			Type:       string(secret.Type),
		},
	}, nil
}

func (p *Provider) DeleteSecret(ctx context.Context, resourceID string, namespace string) error {
	if err := k8s.DeleteResource[corev1.Secret](ctx, p.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get secret %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete secret %s", resourceID)
	}
	return nil
}

func (p *Provider) UpdateSecret(ctx context.Context, option UpdateSecretOption) error {
	from, err := k8s.GetResource[corev1.Secret](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get secret %s/%s", option.Namespace, option.ResourceID).WithCode(eris.CodeNotFound)
		}
		return err
	}

	if utils.Unwrap(from.Immutable) {
		return eris.Errorf("failed to update secret %s/%s, secret is immuntable", option.Namespace, option.ResourceID).WithCode(eris.CodePermissionDenied)
	}

	to := toSecretResource(option.ResourceID, option.Namespace, option.To)
	err = k8s.MergePatchResource(ctx, p.kc, from, to)
	if err != nil {
		return err
	}
	return nil
}
