package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateNetworkPolicy(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreateNetworkPolicy(ctx, CreateNetworkPolicyOption{
		ResourceID: "name",
		Namespace:  "ns",
		NetworkPolicySpec: &pbk8s.NetworkPolicySpec{
			PodSelector: &pbk8s.LabelSelector{
				MatchLabels: map[string]string{"k1": "v1"},
			},
			Ingress: []*pbk8s.NetworkPolicyIngressRule{
				{
					Ports: []*pbk8s.NetworkPolicyPort{
						{
							Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
							Port: &pbk8s.IntOrString{
								IntVal: utils.Ptr(int32(8080)),
							},
							EndPort: 9090,
						},
						{
							Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
							Port: &pbk8s.IntOrString{
								IntVal: utils.Ptr(int32(8081)),
							},
						},
					},
					From: []*pbk8s.NetworkPolicyPeer{
						{
							PodSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k2": "v2"},
							},
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3"},
							},
							IpBlock: &pbk8s.IPBlock{
								Cidr:   "0.0.0.0/0",
								Except: []string{"10.0.0.0"},
							},
						},
					},
				},
			},
			Egress: []*pbk8s.NetworkPolicyEgressRule{
				{
					Ports: []*pbk8s.NetworkPolicyPort{
						{
							Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
							Port: &pbk8s.IntOrString{
								IntVal: utils.Ptr(int32(8080)),
							},
							EndPort: 9090,
						},
					},
					To: []*pbk8s.NetworkPolicyPeer{
						{
							IpBlock: &pbk8s.IPBlock{
								Cidr:   "0.0.0.0/0",
								Except: []string{"10.0.0.0"},
							},
						},
					},
				},
				{
					Ports: []*pbk8s.NetworkPolicyPort{
						{
							Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_UDP,
							Port: &pbk8s.IntOrString{
								StrVal: utils.Ptr("test"),
							},
						},
					},
					To: []*pbk8s.NetworkPolicyPeer{
						{
							IpBlock: &pbk8s.IPBlock{
								Cidr:   "0.0.0.0/0",
								Except: []string{"10.0.0.0"},
							},
						},
					},
				},
			},
			PolicyTypes: []pbk8s.NetworkPolicyType{
				pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
				pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
			},
		},
	})
	require.NoError(t, err)

	policy, err := k8s.GetResource[networkingv1.NetworkPolicy](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", policy.Name)
	assert.Equal(t, "ns", policy.Namespace)
	assert.Equal(t, networkingv1.NetworkPolicySpec{
		PodSelector: metav1.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1"},
		},
		Ingress: []networkingv1.NetworkPolicyIngressRule{
			{
				Ports: []networkingv1.NetworkPolicyPort{
					{
						Protocol: utils.Ptr(corev1.ProtocolTCP),
						Port:     utils.Ptr(intstr.FromInt(8080)),
						EndPort:  utils.Ptr(int32(9090)),
					},
					{
						Protocol: utils.Ptr(corev1.ProtocolTCP),
						Port:     utils.Ptr(intstr.FromInt(8081)),
					},
				},
				From: []networkingv1.NetworkPolicyPeer{
					{
						PodSelector: &metav1.LabelSelector{
							MatchLabels: map[string]string{"k2": "v2"},
						},
						NamespaceSelector: &metav1.LabelSelector{
							MatchLabels: map[string]string{"k3": "v3"},
						},
						IPBlock: &networkingv1.IPBlock{
							CIDR:   "0.0.0.0/0",
							Except: []string{"10.0.0.0"},
						},
					},
				},
			},
		},
		Egress: []networkingv1.NetworkPolicyEgressRule{
			{
				Ports: []networkingv1.NetworkPolicyPort{
					{
						Protocol: utils.Ptr(corev1.ProtocolTCP),
						Port:     utils.Ptr(intstr.FromInt(8080)),
						EndPort:  utils.Ptr(int32(9090)),
					},
				},
				To: []networkingv1.NetworkPolicyPeer{
					{
						IPBlock: &networkingv1.IPBlock{
							CIDR:   "0.0.0.0/0",
							Except: []string{"10.0.0.0"},
						},
					},
				},
			},
			{
				Ports: []networkingv1.NetworkPolicyPort{
					{
						Protocol: utils.Ptr(corev1.ProtocolUDP),
						Port:     utils.Ptr(intstr.FromString("test")),
					},
				},
				To: []networkingv1.NetworkPolicyPeer{
					{
						IPBlock: &networkingv1.IPBlock{
							CIDR:   "0.0.0.0/0",
							Except: []string{"10.0.0.0"},
						},
					},
				},
			},
		},
		PolicyTypes: []networkingv1.PolicyType{
			networkingv1.PolicyTypeIngress,
			networkingv1.PolicyTypeEgress,
		},
	}, policy.Spec)

	err = p.CreateNetworkPolicy(ctx, CreateNetworkPolicyOption{
		ResourceID: "name",
		Namespace:  "ns",
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestDeleteNetworkPolicy(t *testing.T) {
	var (
		testID        = "testID"
		testNamespace = "testNamespace"
	)

	c := fake.NewClient(
		&networkingv1.NetworkPolicy{
			ObjectMeta: metav1.ObjectMeta{
				Name:      testID,
				Namespace: testNamespace,
			},
		})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.DeleteNetworkPolicy(ctx, testID, testNamespace)
	require.NoError(t, err)
	err = p.DeleteNetworkPolicy(ctx, testID, testNamespace)
	assert.True(t, utils.IsErrNotFound(err))
}

func TestCreateOrUpdateNetworkPolicy(t *testing.T) {
	tests := map[string]struct {
		originalObj      []k8sclient.Object
		newNetworkPolicy *pbk8s.NetworkPolicySpec

		expectedNetworkPolicy networkingv1.NetworkPolicySpec
	}{
		"create new policy": {
			newNetworkPolicy: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Ingress: []*pbk8s.NetworkPolicyIngressRule{
					{
						From: []*pbk8s.NetworkPolicyPeer{
							{
								PodSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k2": "v2"},
								},
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3"},
								},
							},
						},
					},
				},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{
					pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
					pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
				},
			},
			expectedNetworkPolicy: networkingv1.NetworkPolicySpec{
				PodSelector: metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Ingress: []networkingv1.NetworkPolicyIngressRule{
					{
						From: []networkingv1.NetworkPolicyPeer{
							{
								PodSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k2": "v2"},
								},
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3"},
								},
							},
						},
					},
				},
				Egress: []networkingv1.NetworkPolicyEgressRule{
					{
						To: []networkingv1.NetworkPolicyPeer{
							{
								IPBlock: &networkingv1.IPBlock{
									CIDR:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				PolicyTypes: []networkingv1.PolicyType{
					networkingv1.PolicyTypeIngress,
					networkingv1.PolicyTypeEgress,
				},
			},
		},

		"update existing policy": {
			originalObj: []k8sclient.Object{
				&networkingv1.NetworkPolicy{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "name",
						Namespace: "ns",
					},
					Spec: networkingv1.NetworkPolicySpec{
						PodSelector: metav1.LabelSelector{
							MatchLabels: map[string]string{"k1": "v1"},
						},
						Egress: []networkingv1.NetworkPolicyEgressRule{
							{
								To: []networkingv1.NetworkPolicyPeer{
									{
										IPBlock: &networkingv1.IPBlock{
											CIDR:   "0.0.0.0/0",
											Except: []string{"********"},
										},
									},
								},
							},
						},
						PolicyTypes: []networkingv1.PolicyType{
							networkingv1.PolicyTypeEgress,
						},
					},
				},
			},
			newNetworkPolicy: &pbk8s.NetworkPolicySpec{
				PodSelector: &pbk8s.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Ingress: []*pbk8s.NetworkPolicyIngressRule{
					{
						From: []*pbk8s.NetworkPolicyPeer{
							{
								PodSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k2": "v2"},
								},
								NamespaceSelector: &pbk8s.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3"},
								},
							},
						},
					},
				},
				Egress: []*pbk8s.NetworkPolicyEgressRule{
					{
						To: []*pbk8s.NetworkPolicyPeer{
							{
								IpBlock: &pbk8s.IPBlock{
									Cidr:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				PolicyTypes: []pbk8s.NetworkPolicyType{
					pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
					pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
				},
			},
			expectedNetworkPolicy: networkingv1.NetworkPolicySpec{
				PodSelector: metav1.LabelSelector{
					MatchLabels: map[string]string{"k1": "v1"},
				},
				Ingress: []networkingv1.NetworkPolicyIngressRule{
					{
						From: []networkingv1.NetworkPolicyPeer{
							{
								PodSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k2": "v2"},
								},
								NamespaceSelector: &metav1.LabelSelector{
									MatchLabels: map[string]string{"k3": "v3"},
								},
							},
						},
					},
				},
				Egress: []networkingv1.NetworkPolicyEgressRule{
					{
						To: []networkingv1.NetworkPolicyPeer{
							{
								IPBlock: &networkingv1.IPBlock{
									CIDR:   "0.0.0.0/0",
									Except: []string{"10.0.0.0"},
								},
							},
						},
					},
				},
				PolicyTypes: []networkingv1.PolicyType{
					networkingv1.PolicyTypeIngress,
					networkingv1.PolicyTypeEgress,
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			c := fake.NewClient(tt.originalObj...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}

			ctx := context.Background()
			err := p.CreateOrUpdateNetworkPolicy(ctx, CreateNetworkPolicyOption{
				ResourceID:        "name",
				Namespace:         "ns",
				NetworkPolicySpec: tt.newNetworkPolicy,
			})
			require.NoError(t, err)

			policy, err := k8s.GetResource[networkingv1.NetworkPolicy](ctx, c, "name", "ns")
			require.NoError(t, err)
			assert.Equal(t, "name", policy.Name)
			assert.Equal(t, "ns", policy.Namespace)
			assert.Equal(t, tt.expectedNetworkPolicy, policy.Spec)
		})
	}
}
