package k8s

import (
	"context"
	"fmt"
	"time"

	"github.com/pkg/errors"
	appsv1 "k8s.io/api/apps/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"

	"sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
)

func (p *Provider) GetDeploymentReplicasStatus(ctx context.Context, resourceID, namespace string) (*pbresource.Status, error) {
	deployment, err := p.GetDeployment(ctx, namespace, resourceID)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to retrieve deployment  %s/%s", namespace, resourceID)
	}

	status := pbresource.StatusCode_NOT_READY
	if IsDeploymentRolledOut(deployment) {
		status = pbresource.StatusCode_READY
	}

	return &pbresource.Status{
		Code: status,
	}, nil
}

// IsDeploymentRolledOut returns true when the Deployment is rolled out.
func IsDeploymentRolledOut(deploy *appsv1.Deployment) bool {
	if deploy == nil {
		return false
	}
	if deploy.Status.ObservedGeneration < deploy.Generation {
		return false
	}
	for _, cond := range deploy.Status.Conditions {
		if cond.Type == appsv1.DeploymentProgressing {
			if cond.Reason == "ProgressDeadlineExceeded" {
				return false
			}
		}
	}
	if deploy.Spec.Replicas != nil && deploy.Status.UpdatedReplicas < *deploy.Spec.Replicas {
		return false
	}
	if deploy.Status.Replicas > deploy.Status.UpdatedReplicas {
		return false
	}
	if deploy.Status.AvailableReplicas < deploy.Status.UpdatedReplicas {
		return false
	}
	return true
}

func (p *Provider) GetDeployment(ctx context.Context, ns string, deploymentName string) (*appsv1.Deployment, error) {
	depl := &appsv1.Deployment{}
	err := p.kc.Get(ctx, client.ObjectKey{
		Name:      deploymentName,
		Namespace: ns,
	}, depl)
	if err != nil {
		return nil, err
	}
	return depl, nil
}

func (p *Provider) RestartDeployment(ctx context.Context, resourceID, namespace string) error {
	deployment, err := p.GetDeployment(ctx, namespace, resourceID)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.New(
				fmt.Sprintf("deployment %s/%s set not found", namespace, resourceID),
			).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve deployment %s/%s", namespace, resourceID)
	}

	// see https://kubernetes.io/docs/reference/labels-annotations-taints/#kubectl-k8s-io-restart-at
	patch := client.MergeFrom(deployment.DeepCopy())
	annotations := deployment.Spec.Template.Annotations
	annotations["kubectl.kubernetes.io/restartedAt"] = time.Now().Format(time.RFC3339)

	if err := p.kc.Patch(ctx, deployment, patch); err != nil {
		return eris.Wrapf(err, "failed to patch deployment %s/%s", namespace, resourceID)
	}
	return nil
}
