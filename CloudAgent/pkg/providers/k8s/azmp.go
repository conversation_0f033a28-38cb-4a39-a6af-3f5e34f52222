package k8s

import (
	"context"

	"github.com/risingwavelabs/eris"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

func (p *Provider) CreateAzureServiceMonitor(ctx context.Context, option CreateServiceMonitorOption) error {
	if option.ServiceMonitorSpec == nil {
		return eris.Errorf("ServiceMonitorSpec should not be nil").WithCode(eris.CodeInvalidArgument)
	}
	smSpec, err := conversion.FromServiceMonitorK8SpecToUnstructured(option.ServiceMonitorSpec)
	if err != nil {
		return eris.Errorf("failed to convert service monitor k8 spec to unstructured: %v", option.ServiceMonitorSpec).WithCode(eris.CodeInvalidArgument)
	}
	obj := &unstructured.Unstructured{
		Object: map[string]interface{}{
			"apiVersion": "azmonitoring.coreos.com/v1",
			"kind":       "ServiceMonitor",
			"metadata": map[string]interface{}{
				"name":        option.ResourceID,
				"namespace":   option.Namespace,
				"labels":      toMapStringInterface(option.Labels),
				"annotations": toMapStringInterface(option.Annotations),
			},
			"spec": smSpec,
		},
	}
	gvr := schema.GroupVersionKind{
		Group:   "azmonitoring.coreos.com",
		Version: "v1",
		Kind:    "ServiceMonitor",
	}
	obj.SetGroupVersionKind(gvr)
	_, err = p.kc.DynamicInterface.Resource(schema.GroupVersionResource{
		Group:    "azmonitoring.coreos.com",
		Version:  "v1",
		Resource: "servicemonitors",
	}).Namespace(option.Namespace).Create(ctx, obj, metav1.CreateOptions{})

	if err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("service monitor %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create service monitor %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) DeleteAzureServiceMonitor(ctx context.Context, resourceID, namespace string) error {
	err := p.kc.DynamicInterface.Resource(schema.GroupVersionResource{
		Group:    "azmonitoring.coreos.com",
		Version:  "v1",
		Resource: "servicemonitors",
	}).Namespace(namespace).Delete(ctx, resourceID, metav1.DeleteOptions{})
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("service monitor %s not found", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete service monitor %s", resourceID)
	}
	return nil
}
