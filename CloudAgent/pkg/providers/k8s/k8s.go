package k8s

import (
	"context"
	"time"

	"github.com/risingwavelabs/eris"
	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"
	v1 "k8s.io/api/core/v1"

	pbcommonk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	"github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbk8ssvc "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/helmx"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

type ProviderInterface interface {
	AnnotateServiceAccount(ctx context.Context, resourceID string, namespace string, labels map[string]string) error
	CreateAzureServiceMonitor(ctx context.Context, option CreateServiceMonitorOption) error
	CreateConfigMap(ctx context.Context, option CreateConfigMapOption) error
	CreateNamespace(ctx context.Context, resourceID string, labels map[string]string) error
	CreateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error
	CreateOrUpdateNetworkPolicy(ctx context.Context, option CreateNetworkPolicyOption) error
	CreatePersistentVolumeClaim(ctx context.Context, option CreatePVCOption) error
	CreatePodMonitoring(ctx context.Context, option CreatePodMonitoringOption) error
	CreateRisingWave(ctx context.Context, option CreateRisingWaveOption) error
	CreateSecret(ctx context.Context, option CreateSecretOption) error
	CreateService(ctx context.Context, option CreateServiceOption) error
	CreateServiceAccount(ctx context.Context, resourceID string, namespace string) error
	CreatePostgreSQL(ctx context.Context, option CreatePostgreSQLOption) error
	CreateServiceMonitor(ctx context.Context, option CreateServiceMonitorOption) error
	CreateRisingWaveNodeGroup(ctx context.Context, option CreateRisingWaveNodeGroupOption) error
	DeleteAzureServiceMonitor(ctx context.Context, resourceID string, namespace string) error
	DeleteConfigMap(ctx context.Context, resourceID string, namespace string) error
	DeleteNamespace(ctx context.Context, resourceID string) error
	DeleteNetworkPolicy(ctx context.Context, resourceID string, namespace string) error
	DeletePersistentVolumeClaims(ctx context.Context, ns string) error
	DeletePodMonitoring(ctx context.Context, resourceID string, namespace string) error
	DeleteRisingWave(ctx context.Context, resourceID string, namespace string) error
	DeleteSecret(ctx context.Context, resourceID string, namespace string) error
	DeleteServiceAccount(ctx context.Context, resourceID string, namespace string) error
	DeleteServiceMonitor(ctx context.Context, resourceID string, namespace string) error
	DeletePostgreSQL(ctx context.Context, resourceID string, namespace string) error
	DeleteRisingWaveNodeGroup(ctx context.Context, option DeleteRisingWaveNodeGroupOption) error
	GetConfigMap(ctx context.Context, resourceID string, namespace string) (ConfigMapMeta, error)
	GetDeploymentReplicasStatus(ctx context.Context, resourceID string, namespace string) (*resource.Status, error)
	GetHelmRelease(_ context.Context, resourceID string, namespace string) (*pbcommonk8s.HelmRelease, error)
	GetNamespace(ctx context.Context, resourceID string) (*resource.Status, error)
	GetPersistenVolumeClaims(ctx context.Context, ns string) (*PersistentVolumeClaimsMeta, error)
	GetPodPhases(ctx context.Context, ns string) (map[string]v1.PodPhase, error)
	GetRisingWave(ctx context.Context, resourceID string, namespace string) (*RisingWaveMeta, error)
	GetSecret(ctx context.Context, resourceID string, namespace string) (SecretMeta, error)
	GetServiceAccount(ctx context.Context, resourceID string, namespace string) (*resource.Status, error)
	GetStatefulSetReplicaStatus(ctx context.Context, resourceID string, namespace string) (*resource.Status, error)
	GetPostgreSQL(ctx context.Context, resourceID string, namespace string) (*PostgreSQLMeta, error)
	InstallHelmRelease(ctx context.Context, option InstallHelmReleaseOption) error
	RestartStatefulSet(ctx context.Context, resourceID string, namespace string) error
	RestartDeployment(ctx context.Context, resourceID string, namespace string) error
	ScaleRisingWave(ctx context.Context, option ScaleRisingWaveOption) error
	StartRisingWave(ctx context.Context, resourceID string, namespace string, overrides []*pbk8ssvc.RisingWaveReplicaOverride) error
	StopRisingWave(ctx context.Context, resourceID string, namespace string) error
	UpdateRisingWaveComponents(ctx context.Context, option UpdateRisingWaveComponentsOption) error
	UpdateRisingWaveNodeGroup(ctx context.Context, option UpdateRisingWaveNodeGroupOption) error
	UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, option UpdateRisingWaveNodeGroupConfigurationOption) error
	UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, option UpdateRisingWaveNodeGroupRestartAtOption) error
	UninstallHelmRelease(ctx context.Context, option UninstallHelmReleaseOption) error
	UpdateConfigMap(ctx context.Context, option UpdateConfigMapOption) error
	UpdatePostgreSQL(ctx context.Context, option UpdatePostgreSQLOption) error
	UpdateRisingWaveImage(ctx context.Context, resourceID string, namespace string, imageTag string) error
	UpdateRisingWaveLicenseKey(ctx context.Context, resourceID string, namespace string, secretName string) error
	UpdateRisingWaveSecretStore(ctx context.Context, resourceID string, namespace string, secretName, secretKey string) error
	UpdateRisingWaveMetaStore(ctx context.Context, resourceID string, namespace string, spec *pbrw.MetaStoreSpec) error
	UpdateSecret(ctx context.Context, option UpdateSecretOption) error
	PutRisingWaveEnvVar(ctx context.Context, namespace string, name string, groups []string, standalone []*pbcommonk8s.EnvVar, meta []*pbcommonk8s.EnvVar, frontend []*pbcommonk8s.EnvVar, compute []*pbcommonk8s.EnvVar, compactor []*pbcommonk8s.EnvVar) (*rwv1alpha1.RisingWave, error)
	DeleteRisingWaveEnvVar(ctx context.Context, namespace string, name string, groups []string, standalone []string, meta []string, frontend []string, compute []string, compactor []string) (*rwv1alpha1.RisingWave, error)
	UpgradeHelmRelease(ctx context.Context, option UpgradeHelmReleaseOption) error
	ListPods(ctx context.Context, ns string) ([]v1.Pod, error)
	LabelNamespace(ctx context.Context, resourceID string, labels map[string]string) error

	GetClusterAccess(ctx context.Context, timeout time.Duration) (ClusterAccessInfo, error)
}

type Provider struct {
	kc      *k8s.KubernetesClient
	helmSvc helmx.ServiceInterface

	endpoint     string
	caCertBase64 string
}

type NewProviderOption struct {
	Kc           *k8s.KubernetesClient
	Endpoint     string
	CACertBase64 string
}

func NewProvider(option NewProviderOption) (*Provider, error) {
	if option.Kc == nil {
		return nil, eris.Errorf("kubernetes client cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	helmSvc, err := helmx.NewService(option.Kc.GetRestCfg(), []string{} /* Only Get method is used here */)
	if err != nil {
		return nil, eris.Wrap(err, "failed to initialize helm service")
	}
	return &Provider{
		kc:      option.Kc,
		helmSvc: helmSvc,

		endpoint:     option.Endpoint,
		caCertBase64: option.CACertBase64,
	}, nil
}
