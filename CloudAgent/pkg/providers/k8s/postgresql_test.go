package k8s

import (
	"context"
	"testing"
	"time"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	acidv1 "github.com/zalando/postgres-operator/pkg/apis/acid.zalan.do/v1"
	v1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbpostgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func getPostgresqlSpecProto() *pbpostgresql.PostgreSqlSpec {
	return &pbpostgresql.PostgreSqlSpec{
		TeamId: "test_team",
		Resources: &pbk8s.ResourceRequirements{
			CpuRequest:    "250m",
			CpuLimit:      "500m",
			MemoryRequest: "64Mi",
			MemoryLimit:   "128Mi",
		},
		NumberOfInstances: 1,
		Volume: &pbpostgresql.PostgreSqlVolume{
			Size: "10Gi",
		},
		Users: map[string]*pbpostgresql.StringArray{
			"risingwave": {Value: []string{"rw_meta"}},
		},
		Postgresql: &pbpostgresql.PostgreSqlParam{
			Version: "16",
		},
	}
}

func generateSpec(modifiers ...func(*pbpostgresql.PostgreSqlSpec)) *pbpostgresql.PostgreSqlSpec {
	proto := getPostgresqlSpecProto()
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func TestProvider_CreatePostgreSQL(t *testing.T) {
	tests := []struct {
		name          string
		option        CreatePostgreSQLOption
		wantErr       bool
		wantErrorCode eris.Code
	}{
		{
			name: "normal case",
			option: CreatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec:       generateSpec(),
			},
			wantErr: false,
		},
		{
			name: "invalid argument (empty team id)",
			option: CreatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.TeamId = ""
				}),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
		{
			name: "invalid argument (no risingwave user)",
			option: CreatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Users = map[string]*pbpostgresql.StringArray{
						"user1": {Value: []string{"database1"}},
					}
				}),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient()
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.CreatePostgreSQL(ctx, tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)

			psql, err := k8s.GetResource[acidv1.Postgresql](ctx, c, "postgresql", "namespace")
			require.NoError(t, err)
			assert.EqualValues(t, "postgresql", psql.Name)
			assert.EqualValues(t, "namespace", psql.Namespace)

			expectSpec, err := conversion.FromPostgreSQLSpecProto(tt.option.Spec)
			require.NoError(t, err)
			expectedJSON := getPrettyJSON(t, expectSpec)
			actualJSON := getPrettyJSON(t, psql.Spec)
			assert.JSONEq(t, expectedJSON, actualJSON)

			err = p.CreatePostgreSQL(ctx, tt.option)
			assert.True(t, utils.IsErrAlreadyExists(err))
		})
	}
}

func TestProvider_DeletePostgreSQL(t *testing.T) {
	ctx := context.Background()
	proto := getPostgresqlSpecProto()
	spec, err := conversion.FromPostgreSQLSpecProto(proto)
	require.NoError(t, err)
	c := fake.NewClient(&acidv1.Postgresql{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "postgresql",
			Namespace: "namespace",
		},
		Spec: *spec,
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}
	err = p.DeletePostgreSQL(ctx, "postgresql", "namespace")
	require.NoError(t, err)
	err = p.DeletePostgreSQL(ctx, "postgresql", "namespace")
	assert.True(t, utils.IsErrNotFound(err))

	_, err = k8s.GetResource[acidv1.Postgresql](ctx, c, "postgresql", "namespace")
	require.True(t, k8sErrors.IsNotFound(err))
}

func TestProvider_UpdatePostgreSQL(t *testing.T) {
	generatePsql := func(generation string, modifier ...func(*acidv1.Postgresql)) *acidv1.Postgresql {
		proto := getPostgresqlSpecProto()
		spec, err := conversion.FromPostgreSQLSpecProto(proto)
		require.NoError(t, err)
		psql := &acidv1.Postgresql{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "postgresql",
				Namespace: "namespace",
				Annotations: map[string]string{
					RwGenerationAnnotation: generation,
				},
			},
			Spec: *spec,
		}
		for _, m := range modifier {
			m(psql)
		}
		return psql
	}
	tests := []struct {
		name             string
		option           UpdatePostgreSQLOption
		initObjs         []k8sclient.Object
		skipAlreadyExist bool
		wantErr          bool
		wantErrorCode    eris.Code
		wantGeneration   string
	}{
		{
			name: "update NumberOfInstances",
			initObjs: []k8sclient.Object{
				generatePsql("1"),
			},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.NumberOfInstances = 0
				}),
			},
			wantErr:        false,
			wantGeneration: "2",
		},
		{
			name: "update resources",
			initObjs: []k8sclient.Object{
				generatePsql("123"),
			},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Resources = &pbk8s.ResourceRequirements{
						CpuRequest:    "1",
						CpuLimit:      "1",
						MemoryRequest: "2Gi",
						MemoryLimit:   "2Gi",
					}
				}),
			},
			wantErr:        false,
			wantGeneration: "124",
		},
		{
			name: "update volume",
			initObjs: []k8sclient.Object{
				generatePsql("100000"),
			},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Volume = &pbpostgresql.PostgreSqlVolume{
						Size: "20Gi",
					}
				}),
			},
			wantErr:        false,
			wantGeneration: "100001",
		},
		{
			name: "invalid argument",
			initObjs: []k8sclient.Object{
				generatePsql("1"),
			},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Volume = &pbpostgresql.PostgreSqlVolume{
						Size: "",
					}
				}),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeInvalidArgument,
		},
		{
			name: "last update failed",
			initObjs: []k8sclient.Object{
				generatePsql("100001", func(psql *acidv1.Postgresql) {
					psql.Status.PostgresClusterStatus = acidv1.ClusterStatusUpdateFailed
				}),
			},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec:       generateSpec(),
			},
			skipAlreadyExist: true,
			wantErr:          false,
			wantGeneration:   "100002",
		},
		{
			name: "already updated",
			initObjs: []k8sclient.Object{
				generatePsql("1"),
			},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec:       generateSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeAlreadyExists,
		},
		{
			name:     "not found",
			initObjs: []k8sclient.Object{},
			option: UpdatePostgreSQLOption{
				ResourceID: "postgresql",
				Namespace:  "namespace",
				Spec:       generateSpec(),
			},
			wantErr:       true,
			wantErrorCode: eris.CodeNotFound,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			err := p.UpdatePostgreSQL(ctx, tt.option)
			if tt.wantErr {
				require.Error(t, err)
				require.EqualValues(t, tt.wantErrorCode, eris.GetCode(err))
				return
			}
			require.NoError(t, err)

			psql, err := k8s.GetResource[acidv1.Postgresql](ctx, c, "postgresql", "namespace")
			require.NoError(t, err)
			assert.EqualValues(t, tt.wantGeneration, psql.Annotations[RwGenerationAnnotation])

			if !tt.skipAlreadyExist {
				err = p.UpdatePostgreSQL(ctx, tt.option)
				assert.True(t, utils.IsErrAlreadyExists(err))
			}
		})
	}
}

func TestProvider_GetPostgreSQL(t *testing.T) {
	proto := getPostgresqlSpecProto()
	spec, err := conversion.FromPostgreSQLSpecProto(proto)
	require.NoError(t, err)
	psqlWithStatus := func(status string, generation string, modifier ...func(*acidv1.Postgresql)) *acidv1.Postgresql {
		psql := &acidv1.Postgresql{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "postgresql",
				Namespace: "namespace",
				Annotations: map[string]string{
					RwGenerationAnnotation: generation,
				},
			},
			Spec: *spec,
			Status: acidv1.PostgresStatus{
				PostgresClusterStatus: status,
			},
		}
		for _, m := range modifier {
			m(psql)
		}
		return psql
	}
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "risingwave.postgresql.credentials",
			Namespace: "namespace",
		},
		Data: map[string][]byte{
			SecretPasswordKey: []byte("test-password"),
		},
	}
	service := &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "postgresql",
			Namespace: "namespace",
			Annotations: map[string]string{
				ObservedRwGenerationAnnotation: "1",
			},
		},
	}

	tests := []struct {
		name     string
		initObjs []k8sclient.Object
		want     *PostgreSQLMeta
		wantErr  bool
	}{
		{
			name: "running",
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusRunning, "1"),
				secret,
				service,
			},
			want: &PostgreSQLMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				Spec: proto,
				SecretRef: &pbresource.Meta{
					Namespace: "namespace",
					Id:        "risingwave.postgresql.credentials",
				},
				Credentials: &pbpostgresql.Credentials{
					Username: "risingwave",
					Password: "test-password",
				},
			},
		},
		{
			name: "updating",
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusRunning, "2"),
				service,
			},
			want: &PostgreSQLMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				Spec: proto,
			},
		},
		{
			name:     "not found",
			initObjs: []k8sclient.Object{},
			want: &PostgreSQLMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
		{
			name: "creating",
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusCreating, "1"),
			},
			want: &PostgreSQLMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				Spec: proto,
			},
		},
		{
			name: "no secret",
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusRunning, "1"),
			},
			wantErr: true,
		},
		{
			name: "unknown",
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusUnknown, "1", func(psql *acidv1.Postgresql) {
					psql.CreationTimestamp = metav1.NewTime(time.Now().Add(-5 * time.Minute))
				}),
			},
			want: &PostgreSQLMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_UNKNOWN,
				},
				Spec: proto,
			},
		},
		{
			name: "just created",
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusUnknown, "1", func(psql *acidv1.Postgresql) {
					psql.CreationTimestamp = metav1.NewTime(time.Now().Add(-5 * time.Second))
				}),
			},
			want: &PostgreSQLMeta{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				Spec: proto,
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			p := &Provider{
				kc: &k8s.KubernetesClient{Client: c},
			}
			meta, err := p.GetPostgreSQL(ctx, "postgresql", "namespace")
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			assert.EqualValues(t, tt.want, meta)
		})
	}
}

func Test_postgresqlToStatus(t *testing.T) {
	tests := []struct {
		name string
		args acidv1.PostgresStatus
		want *pbresource.Status
	}{
		{
			name: "empty",
			args: acidv1.PostgresStatus{},
			want: &pbresource.Status{Code: pbresource.StatusCode_UNKNOWN},
		},
		{
			name: "unknown",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusUnknown},
			want: &pbresource.Status{Code: pbresource.StatusCode_UNKNOWN},
		},
		{
			name: "running",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusRunning},
			want: &pbresource.Status{Code: pbresource.StatusCode_READY},
		},
		{
			name: "creating",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusCreating},
			want: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
		},
		{
			name: "updating",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusUpdating},
			want: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
		},
		{
			name: "update failed",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusUpdateFailed},
			want: &pbresource.Status{Code: pbresource.StatusCode_ERROR},
		},
		{
			name: "sync failed",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusSyncFailed},
			want: &pbresource.Status{Code: pbresource.StatusCode_ERROR},
		},
		{
			name: "add failed",
			args: acidv1.PostgresStatus{PostgresClusterStatus: acidv1.ClusterStatusAddFailed},
			want: &pbresource.Status{Code: pbresource.StatusCode_ERROR},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			assert.EqualValues(t, tt.want, postgresqlToStatus(&acidv1.Postgresql{Status: tt.args}))
		})
	}
}
