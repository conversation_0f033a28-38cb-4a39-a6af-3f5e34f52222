package k8s

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"maps"

	"github.com/risingwavelabs/eris"
	acidv1 "github.com/zalando/postgres-operator/pkg/apis/acid.zalan.do/v1"
	"google.golang.org/protobuf/proto"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbpostgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

type CreatePostgreSQLOption struct {
	ResourceID string
	Namespace  string
	Spec       *pbpostgresql.PostgreSqlSpec
}

type UpdatePostgreSQLOption struct {
	ResourceID string
	Namespace  string
	Spec       *pbpostgresql.PostgreSqlSpec
}

type PostgreSQLMeta struct {
	Status      *pbresource.Status
	Spec        *pbpostgresql.PostgreSqlSpec
	SecretRef   *pbresource.Meta
	Credentials *pbpostgresql.Credentials
}

const (
	PostgreSQLUsername = "risingwave"

	SecretPasswordKey = "password"

	// RwGenerationAnnotation tag at postgresql.meta.annotation.
	RwGenerationAnnotation = "rwGeneration"
	// ObservedRwGenerationAnnotation tag at postgresql.spec.serviceAnnotation, postgres-operator syncs it to service.meta.annotation.
	ObservedRwGenerationAnnotation = "observedRwGeneration"

	InitialRwGeneration = 1
)

func (p *Provider) CreatePostgreSQL(ctx context.Context, option CreatePostgreSQLOption) error {
	if _, ok := option.Spec.GetUsers()[PostgreSQLUsername]; !ok {
		return eris.Errorf("postgresql user '%s' is required", PostgreSQLUsername).WithCode(eris.CodeInvalidArgument)
	}
	psql, err := patchGeneration(option.ResourceID, option.Namespace, option.Spec, InitialRwGeneration)
	if err != nil {
		return eris.Errorf("failed to convert postgresql spec proto: %v, error: %s", option.Spec, err).WithCode(eris.CodeInvalidArgument)
	}
	err = p.kc.Create(ctx, psql)
	if err != nil && k8sErrors.IsAlreadyExists(err) {
		return eris.Errorf("postgresql %s already exists", option.ResourceID).WithCode(eris.CodeAlreadyExists)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to create postgresql %s", option.ResourceID)
	}
	return nil
}

func (p *Provider) DeletePostgreSQL(ctx context.Context, resourceID string, namespace string) error {
	err := k8s.DeleteResource[acidv1.Postgresql](ctx, p.kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("postgresql %s not found", resourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to delete postgresql %s", resourceID)
	}
	return nil
}

func (p *Provider) UpdatePostgreSQL(ctx context.Context, option UpdatePostgreSQLOption) error {
	psqlRaw, err := k8s.GetResource[acidv1.Postgresql](ctx, p.kc, option.ResourceID, option.Namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return eris.Errorf("postgresql %s not found", option.ResourceID).WithCode(eris.CodeNotFound)
	}
	if err != nil {
		return eris.Wrapf(err, "failed to get postgresql %s", option.ResourceID)
	}

	originPbSpec, originGeneration, err := unpackGeneration(psqlRaw)
	if err != nil {
		return err
	}
	isLastUpdateFailed := psqlRaw.Status.PostgresClusterStatus == acidv1.ClusterStatusUpdateFailed
	pbSpec, isSpecChanged := patchSpec(originPbSpec, option.Spec)
	// update the resource when 1. spec changed or 2. last update failed
	if !isSpecChanged && !isLastUpdateFailed {
		return eris.Errorf("already updated %s", option.ResourceID).WithCode(eris.CodeAlreadyExists)
	}
	// CloudAgent maintains `RwGenerationAnnotation` and `ObservedRwGenerationAnnotation`.
	// They will increase automatically when update postgresql CRD. postgres-operator syncs it to service.meta.annotation.
	// We can compare `postgresql.rwGeneration` and `service.observedRwGeneration` to know if operator has observed the CRD update.
	// reference postgresql-operator: https://github.com/zalando/postgres-operator/blob/2ef7d5857881655e9e3d0df6bd1a0dc0c677e2d1/pkg/cluster/cluster.go#L883
	generation := originGeneration + 1
	psql, err := patchGeneration(option.ResourceID, option.Namespace, pbSpec, generation)
	if err != nil {
		return eris.Errorf("failed to convert postgresql spec proto: %v, error: %s", option.Spec, err).WithCode(eris.CodeInvalidArgument)
	}

	// generate the origin object for patch
	originPsql, err := patchGeneration(option.ResourceID, option.Namespace, originPbSpec, originGeneration)
	if err != nil {
		return eris.Errorf("failed to convert origin postgresql spec proto: %v, error: %s", option.Spec, err)
	}
	// TODO: concurrency control https://github.com/risingwavelabs/CloudAgent/pull/667#discussion_r1652191048
	err = k8s.MergePatchResource(ctx, p.kc, originPsql, psql)
	if err != nil {
		return eris.Wrapf(err, "failed to update postgresql %s", option.ResourceID)
	}

	return nil
}

func (p *Provider) GetPostgreSQL(ctx context.Context, resourceID string, namespace string) (*PostgreSQLMeta, error) {
	status, spec, err := getPostgreSQL(ctx, p.kc, resourceID, namespace)
	if err != nil {
		return nil, err
	}
	if status.GetCode() != pbresource.StatusCode_READY {
		return &PostgreSQLMeta{
			Status: status,
			Spec:   spec,
		}, nil
	}
	// get secret after the postgresql ready
	// format ${username}.${postgresql_name}.credentials
	secretName := fmt.Sprintf("%s.%s.credentials", PostgreSQLUsername, resourceID)
	secret, err := k8s.GetResource[corev1.Secret](ctx, p.kc, secretName, namespace)
	if err != nil {
		return nil, eris.Wrapf(err, "failed to get postgresql secret %s", secretName)
	}
	return &PostgreSQLMeta{
		Status: status,
		Spec:   spec,
		SecretRef: &pbresource.Meta{
			Id:        secretName,
			Namespace: namespace,
		},
		Credentials: &pbpostgresql.Credentials{
			Username: PostgreSQLUsername,
			Password: string(secret.Data[SecretPasswordKey]),
		},
	}, nil
}

func getPostgreSQL(ctx context.Context, kc *k8s.KubernetesClient, resourceID string, namespace string) (*pbresource.Status, *pbpostgresql.PostgreSqlSpec, error) {
	psql, err := k8s.GetResource[acidv1.Postgresql](ctx, kc, resourceID, namespace)
	if err != nil && k8sErrors.IsNotFound(err) {
		return &pbresource.Status{
			Code: pbresource.StatusCode_NOT_FOUND,
		}, nil, nil
	}
	if err != nil {
		return nil, nil, eris.Wrapf(err, "failed to get postgresql %s", resourceID)
	}

	spec, generation, err := unpackGeneration(psql)
	if err != nil {
		return nil, nil, err
	}

	status := postgresqlToStatus(psql)

	// the CRD is just created and not be observed by postgres-operator
	const UnknownCreationTimeout = -1 * time.Minute
	justCreated := psql.GetCreationTimestamp().After(metav1.Now().Add(UnknownCreationTimeout))
	if status.GetCode() == pbresource.StatusCode_UNKNOWN && justCreated {
		return &pbresource.Status{
			Code: pbresource.StatusCode_NOT_READY,
		}, spec, nil
	}

	if status.GetCode() != pbresource.StatusCode_READY {
		return status, spec, nil
	}

	// obtain `observedGeneration` from the service with same name as postgresql
	svc, err := k8s.GetResource[corev1.Service](ctx, kc, resourceID, namespace)
	if err != nil {
		return nil, nil, eris.Wrapf(err, "failed to get service %s", resourceID)
	}
	str, ok := svc.Annotations[ObservedRwGenerationAnnotation]
	if !ok {
		return nil, nil, eris.New("no observedRwGeneration found on postgresql crd")
	}
	observedGeneration, err := strconv.Atoi(str)
	if err != nil {
		return nil, nil, eris.Wrapf(err, "failed to parse observedGeneration %s", str)
	}
	// `observedGeneration != generation` means operator has not observed the update
	if observedGeneration != generation {
		return &pbresource.Status{
			Code: pbresource.StatusCode_NOT_READY,
		}, spec, nil
	}
	return status, spec, nil
}

var psqlStatusMap = map[string]pbresource.StatusCode{
	acidv1.ClusterStatusUnknown:      pbresource.StatusCode_UNKNOWN,
	acidv1.ClusterStatusRunning:      pbresource.StatusCode_READY,
	acidv1.ClusterStatusCreating:     pbresource.StatusCode_NOT_READY,
	acidv1.ClusterStatusUpdating:     pbresource.StatusCode_NOT_READY,
	acidv1.ClusterStatusUpdateFailed: pbresource.StatusCode_ERROR,
	acidv1.ClusterStatusSyncFailed:   pbresource.StatusCode_ERROR,
	acidv1.ClusterStatusAddFailed:    pbresource.StatusCode_ERROR,
}

func postgresqlToStatus(psql *acidv1.Postgresql) *pbresource.Status {
	code, ok := psqlStatusMap[psql.Status.PostgresClusterStatus]
	if !ok {
		code = pbresource.StatusCode_UNKNOWN
	}
	return &pbresource.Status{
		Code: code,
	}
}

func patchGeneration(
	resourceID string,
	namespace string,
	spec *pbpostgresql.PostgreSqlSpec,
	generation int,
) (*acidv1.Postgresql, error) {
	// patch `rwGeneration` in postgresql crd meta
	meta := metav1.ObjectMeta{
		Name:      resourceID,
		Namespace: namespace,
		Annotations: map[string]string{
			RwGenerationAnnotation: strconv.Itoa(generation),
		},
	}

	// patch `observedRwGeneration` in service annotation
	if spec.ServiceAnnotations == nil {
		spec.ServiceAnnotations = make(map[string]string)
	}
	maps.Copy(spec.GetServiceAnnotations(), map[string]string{
		ObservedRwGenerationAnnotation: strconv.Itoa(generation),
	})
	proto, err := conversion.FromPostgreSQLSpecProto(spec)
	if err != nil {
		return nil, err
	}

	return &acidv1.Postgresql{
		ObjectMeta: meta,
		Spec:       *proto,
	}, nil
}

func unpackGeneration(psql *acidv1.Postgresql) (*pbpostgresql.PostgreSqlSpec, int, error) {
	str, ok := psql.Annotations[RwGenerationAnnotation]
	if !ok {
		return nil, 0, eris.New("no rwGeneration found on postgresql crd")
	}
	generation, err := strconv.Atoi(str)
	if err != nil {
		return nil, 0, eris.Wrapf(err, "failed to parse generation %s", str)
	}

	spec, err := conversion.ToPostgreSQLSpecProto(&psql.Spec)
	if err != nil {
		return nil, 0, eris.Wrapf(err, "failed to convert postgresql spec proto: %v, error: %s", psql.Spec, err)
	}
	// unpack `observedRwGeneration` in service annotation
	delete(spec.GetServiceAnnotations(), ObservedRwGenerationAnnotation)
	return spec, generation, nil
}

func patchSpec(dst *pbpostgresql.PostgreSqlSpec, src *pbpostgresql.PostgreSqlSpec) (*pbpostgresql.PostgreSqlSpec, bool) {
	clone := proto.Clone(dst).(*pbpostgresql.PostgreSqlSpec)
	// only these field may be updated
	updated := false
	if clone.GetNumberOfInstances() != src.GetNumberOfInstances() {
		clone.NumberOfInstances = src.GetNumberOfInstances()
		updated = true
	}
	if clone.GetResources().GetCpuRequest() != src.GetResources().GetCpuRequest() || clone.GetResources().GetMemoryRequest() != src.GetResources().GetMemoryRequest() ||
		clone.GetResources().GetCpuLimit() != src.GetResources().GetCpuLimit() || clone.GetResources().GetMemoryLimit() != src.GetResources().GetMemoryLimit() {
		clone.Resources = proto.Clone(src.GetResources()).(*pbk8s.ResourceRequirements)
		updated = true
	}
	if clone.GetVolume().GetSize() != src.GetVolume().GetSize() || clone.GetVolume().GetStorageClass() != src.GetVolume().GetStorageClass() {
		clone.Volume = proto.Clone(src.GetVolume()).(*pbpostgresql.PostgreSqlVolume)
		updated = true
	}
	return clone, updated
}
