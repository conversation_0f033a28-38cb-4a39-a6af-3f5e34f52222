package k8s

import (
	"context"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

func (p *Provider) CreateServiceAccount(ctx context.Context, resourceID, namespace string) error {
	if err := p.kc.Create(ctx, &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("service account %s already exists", resourceID).WithCode(eris.CodeAlreadyExists)
		}
		return errors.Wrapf(err, "failed to create service account %s", resourceID)
	}
	return nil
}

func (p *Provider) GetServiceAccount(ctx context.Context, resourceID, namespace string) (*pbresource.Status, error) {
	_, err := k8s.GetResource[corev1.ServiceAccount](ctx, p.kc, resourceID, namespace)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			}, nil
		}
		return nil, errors.Wrapf(err, "failed to retrieve service account %s", resourceID)
	}

	return &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}, nil
}

func (p *Provider) DeleteServiceAccount(ctx context.Context, resourceID string, namespace string) error {
	if err := k8s.DeleteResource[corev1.ServiceAccount](ctx, p.kc, resourceID, namespace); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get service account %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return errors.Wrapf(err, "failed to delete service account %s", resourceID)
	}
	return nil
}

func (p *Provider) AnnotateServiceAccount(ctx context.Context, resourceID string, namespace string, labels map[string]string) error {
	sa := &corev1.ServiceAccount{
		ObjectMeta: metav1.ObjectMeta{
			Namespace: namespace,
			Name:      resourceID,
		},
	}
	patch := client.MergeFrom(sa.DeepCopy())
	sa.Annotations = labels

	return p.kc.Patch(ctx, sa, patch)
}
