package k8s

import (
	"context"
	"encoding/json"

	"github.com/risingwavelabs/eris"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
)

func (p *Provider) CreateNamespace(ctx context.Context, resourceID string, labels map[string]string) error {
	if err := p.kc.Create(ctx, &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name:   resourceID,
			Labels: labels,
		},
	}); err != nil {
		if k8sErrors.IsAlreadyExists(err) {
			return eris.Errorf("namespace %s already exists", resourceID).WithCode(eris.CodeAlreadyExists)
		}
		return eris.Wrapf(err, "failed to create namespace %s", resourceID)
	}
	return nil
}

func (p *Provider) GetNamespace(ctx context.Context, resourceID string) (*pbresource.Status, error) {
	ns, err := k8s.GetResource[corev1.Namespace](ctx, p.kc, resourceID, "" /*namespace*/)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			}, nil
		}
		return nil, eris.Wrapf(err, "failed to retrieve namespace %s", resourceID)
	}

	if ns.Status.Phase == corev1.NamespaceActive {
		return &pbresource.Status{
			Code: pbresource.StatusCode_READY,
		}, nil
	}
	msgbytes, err := json.Marshal(ns.Status.Conditions)
	var msg string
	if err != nil {
		msg = "unable to get detailed conditions in JSON"
	} else {
		msg = string(msgbytes)
	}
	return &pbresource.Status{
		Code:    pbresource.StatusCode_NOT_READY,
		Message: msg,
	}, nil
}

func (p *Provider) DeleteNamespace(ctx context.Context, resourceID string) error {
	if err := k8s.DeleteResource[corev1.Namespace](ctx, p.kc, resourceID, "" /*namespace*/); err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.Errorf("failed to get namespace %s", resourceID).WithCode(eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to delete namespace %s", resourceID)
	}
	return nil
}

func (p *Provider) LabelNamespace(ctx context.Context, resourceID string, labels map[string]string) error {
	ns, err := k8s.GetResource[corev1.Namespace](ctx, p.kc, resourceID, "" /*namespace*/)
	if err != nil {
		if k8sErrors.IsNotFound(err) {
			return eris.WithCode(eris.Wrapf(err, "namespace %s is not found", resourceID), eris.CodeNotFound)
		}
		return eris.Wrapf(err, "failed to retrieve namespace %s", resourceID)
	}
	patch := ns.DeepCopy()

	// Add or update the label
	if patch.Labels == nil && len(labels) != 0 {
		patch.Labels = map[string]string{}
	}
	for k, v := range labels {
		patch.Labels[k] = v
	}

	return k8s.MergePatchResource(ctx, p.kc, ns, patch)
}
