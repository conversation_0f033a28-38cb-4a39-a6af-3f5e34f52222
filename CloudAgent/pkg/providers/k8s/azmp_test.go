package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

var (
	smSpecProto = &pbprometheus.ServiceMonitorSpec{
		JobLabel:     "jl",
		TargetLabels: []string{"tl1", "tl2"},
		Endpoints: []*pbprometheus.Endpoint{
			{
				Port:          "p1",
				Interval:      "1m",
				ScrapeTimeout: "1s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []string{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:          "p2",
				Interval:      "2m",
				ScrapeTimeout: "2s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: &pbk8s.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []*pbk8s.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
				},
			},
		},
	}
)

func TestCreateAzureServiceMonitor(t *testing.T) {
	c := fake.NewDynamiceInterface()
	p := &Provider{
		kc: &k8s.KubernetesClient{DynamicInterface: c},
	}

	ctx := context.Background()
	err := p.CreateAzureServiceMonitor(ctx, CreateServiceMonitorOption{
		ResourceID:         "name",
		Namespace:          "ns",
		Labels:             map[string]string{"key1": "val1"},
		Annotations:        map[string]string{"key2": "val2"},
		ServiceMonitorSpec: smSpecProto,
	})

	require.NoError(t, err)
	obj, err := c.Resource(schema.GroupVersionResource{
		Group:    "azmonitoring.coreos.com",
		Version:  "v1",
		Resource: "servicemonitors",
	}).Namespace("ns").Get(ctx, "name", metav1.GetOptions{})

	require.NoError(t, err)
	assert.Equal(t, obj.Object, map[string]interface{}{
		"apiVersion": "azmonitoring.coreos.com/v1",
		"kind":       "ServiceMonitor",
		"metadata": map[string]interface{}{
			"annotations": map[string]interface{}{
				"key2": "val2",
			},
			"labels": map[string]interface{}{
				"key1": "val1",
			},
			"name":      "name",
			"namespace": "ns",
		},
		"spec": map[string]interface{}{
			"endpoints": []interface{}{
				map[string]interface{}{
					"interval": "1m",
					"metricRelabelConfigs": []interface{}{
						map[string]interface{}{
							"action": "action1",
							"regex":  "re1",
							"sourceLabels": []interface{}{
								"sl1",
								"sl2",
							},
						},
						map[string]interface{}{
							"action": "action2",
							"regex":  "re2",
							"sourceLabels": []interface{}{
								"sl3",
								"sl4",
							},
						},
					},
					"port":          "p1",
					"scrapeTimeout": "1s",
				},
				map[string]interface{}{
					"interval": "2m",
					"metricRelabelConfigs": []interface{}{
						map[string]interface{}{
							"action": "action1",
							"regex":  "re1",
							"sourceLabels": []interface{}{
								"sl5",
								"sl6",
							},
						},
					},
					"port":          "p2",
					"scrapeTimeout": "2s",
				},
			},
			"jobLabels": "jl",
			"selector": map[string]interface{}{
				"matchExpressions": []interface{}{
					map[string]interface{}{
						"key":      "key1",
						"operator": "In",
						"values": []interface{}{
							"val1",
							"val2",
						},
					},
					map[string]interface{}{
						"key":      "key2",
						"operator": "NotIn",
						"values": []interface{}{
							"val3",
							"val4",
						},
					},
				},
				"matchLabels": map[string]interface{}{
					"k1": "v1",
					"k2": "v2",
				},
			},
			"targetLabels": []interface{}{
				"tl1",
				"tl2",
			},
		},
	})

	err = p.CreateAzureServiceMonitor(ctx, CreateServiceMonitorOption{
		ResourceID:         "name",
		Namespace:          "ns",
		Labels:             map[string]string{"key1": "val1"},
		Annotations:        map[string]string{"key2": "val2"},
		ServiceMonitorSpec: smSpecProto,
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestDeleteAzureServiceMonitor(t *testing.T) {
	c := fake.NewDynamiceInterface()

	var (
		ctx        = context.Background()
		resourceID = "resource-ID"
		namespace  = "ns"
	)

	p := &Provider{
		kc: &k8s.KubernetesClient{DynamicInterface: c},
	}

	err := p.DeleteAzureServiceMonitor(ctx, resourceID, namespace)
	assert.True(t, utils.IsErrNotFound(err))

	err = p.CreateAzureServiceMonitor(ctx, CreateServiceMonitorOption{
		ResourceID:         resourceID,
		Namespace:          namespace,
		ServiceMonitorSpec: &pbprometheus.ServiceMonitorSpec{},
	})
	require.NoError(t, err)

	err = p.DeleteAzureServiceMonitor(ctx, resourceID, namespace)
	require.NoError(t, err)

	err = p.DeleteAzureServiceMonitor(ctx, resourceID, namespace)
	assert.True(t, utils.IsErrNotFound(err))
}
