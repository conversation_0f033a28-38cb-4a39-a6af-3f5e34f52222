package k8s

import (
	"context"
	"testing"

	prometheusv1 "github.com/prometheus-operator/prometheus-operator/pkg/apis/monitoring/v1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbprometheus "github.com/risingwavelabs/cloudagent/pbgen/common/prometheus"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateServiceMonitor(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	smSpesProto := &pbprometheus.ServiceMonitorSpec{
		JobLabel:     "jl",
		TargetLabels: []string{"tl1", "tl2"},
		Endpoints: []*pbprometheus.Endpoint{
			{
				Port:          "p1",
				Interval:      "1m",
				ScrapeTimeout: "1s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []string{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:          "p2",
				Interval:      "2m",
				ScrapeTimeout: "2s",
				MetricRelabelings: []*pbprometheus.RelabelConfig{
					{
						SourceLabels: []string{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: &pbk8s.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []*pbk8s.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_IN,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: pbk8s.LabelSelectorOperator_LABEL_SELECTOR_OPERATOR_NOT_IN,
				},
			},
		},
	}
	ctx := context.Background()
	err := p.CreateServiceMonitor(ctx, CreateServiceMonitorOption{
		ResourceID:         "name",
		Namespace:          "ns",
		Labels:             map[string]string{"key1": "val1"},
		Annotations:        map[string]string{"key2": "val2"},
		ServiceMonitorSpec: smSpesProto,
	})

	require.NoError(t, err)

	sm, err := k8s.GetResource[prometheusv1.ServiceMonitor](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", sm.Name)
	assert.Equal(t, "ns", sm.Namespace)
	assert.Equal(t, map[string]string{"key1": "val1"}, sm.Labels)
	assert.Equal(t, map[string]string{"key2": "val2"}, sm.Annotations)

	assert.Equal(t, prometheusv1.ServiceMonitorSpec{
		JobLabel:     "jl",
		TargetLabels: []string{"tl1", "tl2"},
		Endpoints: []prometheusv1.Endpoint{
			{
				Port:          "p1",
				Interval:      "1m",
				ScrapeTimeout: "1s",
				MetricRelabelConfigs: []prometheusv1.RelabelConfig{
					{
						SourceLabels: []prometheusv1.LabelName{"sl1", "sl2"},
						Regex:        "re1",
						Action:       "action1",
					},
					{
						SourceLabels: []prometheusv1.LabelName{"sl3", "sl4"},
						Regex:        "re2",
						Action:       "action2",
					},
				},
			},
			{
				Port:          "p2",
				Interval:      "2m",
				ScrapeTimeout: "2s",
				MetricRelabelConfigs: []prometheusv1.RelabelConfig{
					{
						SourceLabels: []prometheusv1.LabelName{"sl5", "sl6"},
						Regex:        "re1",
						Action:       "action1",
					},
				},
			},
		},
		Selector: metav1.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1", "k2": "v2"},
			MatchExpressions: []metav1.LabelSelectorRequirement{
				{
					Key:      "key1",
					Values:   []string{"val1", "val2"},
					Operator: metav1.LabelSelectorOpIn,
				},
				{
					Key:      "key2",
					Values:   []string{"val3", "val4"},
					Operator: metav1.LabelSelectorOpNotIn,
				},
			},
		},
	}, sm.Spec)

	err = p.CreateServiceMonitor(ctx, CreateServiceMonitorOption{
		ResourceID:         "name",
		Namespace:          "ns",
		ServiceMonitorSpec: smSpesProto,
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}

func TestDeleteServiceMonitor(t *testing.T) {
	var (
		resourceID = "resource-ID"
		namespace  = "ns"
	)
	c := fake.NewClient(&prometheusv1.ServiceMonitor{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	})
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	err := p.DeleteServiceMonitor(context.Background(), resourceID, namespace)
	assert.NoError(t, err)

	err = p.DeleteServiceMonitor(context.Background(), resourceID, namespace)
	assert.Error(t, err)
	assert.True(t, utils.IsErrNotFound(err))
}
