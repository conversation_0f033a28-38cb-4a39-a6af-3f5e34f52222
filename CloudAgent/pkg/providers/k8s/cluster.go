package k8s

import (
	"context"
	"time"

	"github.com/risingwavelabs/eris"
	authenticationv1 "k8s.io/api/authentication/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type ClusterAccessInfo struct {
	Endpoint     string
	CACertBase64 string
	Token        string
}

func (p *Provider) GetClusterAccess(ctx context.Context, timeout time.Duration) (ClusterAccessInfo, error) {
	const (
		clusterAccessServiceAccountName      = "cloudagent"
		clusterAccessServiceAccountNamespace = "cloudagent"

		defaultTimeout = 30 * time.Minute
		maxTimeout     = 24 * time.Hour
	)

	if timeout == 0 {
		timeout = defaultTimeout
	}
	if timeout > maxTimeout {
		return ClusterAccessInfo{}, eris.Errorf("timeout %s exceeds the max allowed value", timeout).WithCode(eris.CodeInvalidArgument)
	}

	req := &authenticationv1.TokenRequest{
		Spec: authenticationv1.TokenRequestSpec{
			ExpirationSeconds: utils.Ptr(int64(min(timeout, maxTimeout).Seconds())),
		},
	}
	resp, err := p.kc.Interface.CoreV1().ServiceAccounts(clusterAccessServiceAccountNamespace).CreateToken(ctx, clusterAccessServiceAccountName, req, metav1.CreateOptions{})

	if err != nil {
		return ClusterAccessInfo{}, eris.Wrap(err, "failed to create service account token")
	}

	if len(resp.Status.Token) == 0 {
		return ClusterAccessInfo{}, eris.New("no token in server response")
	}
	return ClusterAccessInfo{
		Endpoint:     p.endpoint,
		CACertBase64: p.caCertBase64,
		Token:        resp.Status.Token,
	}, nil
}
