package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	fake "github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestDeletePersistentVolumeAndClaims(t *testing.T) {
	c := fake.NewClient(
		&corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pvc-1",
				Namespace: "ns",
			},
			Spec: corev1.PersistentVolumeClaimSpec{
				VolumeName: "pvname",
			},
		},
		&corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pvc-2",
				Namespace: "ns",
			},
			Spec: corev1.PersistentVolumeClaimSpec{
				VolumeName: "pvname",
			},
		},
	)
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	l, err := p.listPersistenVolumeClaims(ctx, "ns")
	require.NoError(t, err)
	assert.Equal(t, 2, len(l))
	err = p.DeletePersistentVolumeClaims(ctx, "ns")
	require.NoError(t, err)
	l, err = p.listPersistenVolumeClaims(ctx, "ns")
	require.NoError(t, err)
	assert.Equal(t, 0, len(l))
	err = p.DeletePersistentVolumeClaims(ctx, "ns")
	require.NoError(t, err)
}

func TestGetPersistentVolumeAndClaimsReady(t *testing.T) {
	c := fake.NewClient(
		&corev1.PersistentVolumeClaim{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "pvcname",
				Namespace: "ns",
			},
		},
	)
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	m, err := p.GetPersistenVolumeClaims(ctx, "ns")
	require.NoError(t, err)
	assert.Equal(t, m.Status.GetCode(), pbresource.StatusCode_READY)
}

func TestGetPersistentVolumeAndClaimsNotFound(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	m, err := p.GetPersistenVolumeClaims(ctx, "ns")
	require.NoError(t, err)
	assert.Equal(t, m.Status.GetCode(), pbresource.StatusCode_NOT_FOUND)
}

func TestCreatePersistentVolumeClaim(t *testing.T) {
	c := fake.NewClient()
	p := &Provider{
		kc: &k8s.KubernetesClient{Client: c},
	}

	ctx := context.Background()
	err := p.CreatePersistentVolumeClaim(ctx, CreatePVCOption{
		ResourceID: "name",
		Namespace:  "ns",
		Spec: &pbk8s.PersistentVolumeClaimSpec{
			AccessModes: []pbk8s.PVCAccessMode{
				pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
			},
			Selector: &pbk8s.LabelSelector{
				MatchLabels: map[string]string{"k1": "v1"},
			},
			Resources: &pbk8s.VolumeResourceRequirements{
				StorageRequest: "20Gi",
			},
			VolumeName:       "test_volume",
			StorageClassName: "test_class",
		},
	})

	require.NoError(t, err)
	// Verified value of the created PVC.
	pvc, err := k8s.GetResource[corev1.PersistentVolumeClaim](ctx, c, "name", "ns")
	require.NoError(t, err)
	assert.Equal(t, "name", pvc.Name)
	assert.Equal(t, "ns", pvc.Namespace)
	storageResource := resource.NewQuantity(20*1024*1024*1024, "BinarySI")
	// Generate `s` filed
	_ = storageResource.String()
	assert.Equal(t, corev1.PersistentVolumeClaimSpec{
		AccessModes: []corev1.PersistentVolumeAccessMode{
			corev1.ReadWriteOnce,
		},
		Selector: &metav1.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1"},
		},
		Resources: corev1.VolumeResourceRequirements{
			Requests: corev1.ResourceList{
				corev1.ResourceStorage: *storageResource,
			},
		},
		VolumeName:       "test_volume",
		StorageClassName: utils.Ptr("test_class"),
	}, pvc.Spec)

	// Verify creation idempotency
	err = p.CreatePersistentVolumeClaim(ctx, CreatePVCOption{
		ResourceID: "name",
		Namespace:  "ns",
		Spec: &pbk8s.PersistentVolumeClaimSpec{
			AccessModes: []pbk8s.PVCAccessMode{
				pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
			},
			Selector: &pbk8s.LabelSelector{
				MatchLabels: map[string]string{"k1": "v1"},
			},
			Resources: &pbk8s.VolumeResourceRequirements{
				StorageRequest: "20Gi",
			},
			VolumeName:       "test_volume",
			StorageClassName: "test_class",
		},
	})
	assert.True(t, utils.IsErrAlreadyExists(err))
}
