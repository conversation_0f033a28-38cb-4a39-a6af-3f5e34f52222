package mtls

import (
	"crypto/tls"
	"crypto/x509"
	"sync"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestMTLSListener(t *testing.T) {
	t.<PERSON>llel()

	var (
		addr    = "localhost:40211"
		message = "test message"

		serverCert         = "../../certs/tls.crt"
		serverKey          = "../../certs/tls.key"
		serverSideVerifyCa = "../../certs/ca.crt"

		clientCert         = "../../certs/tls2.crt"
		clientKey          = "../../certs/tls2.key"
		clientSideVerifyCa = "../../certs/ca.crt"

		wrongClientCert = "../../certs/group-b/tls.crt"
		wrongClientKey  = "../../certs/group-b/tls.key"

		wg = sync.WaitGroup{}
	)
	listener, err := NewListener(ListenerOption{
		CertPath: serverCert,
		KeyPath:  server<PERSON>ey,
		CaPath:   serverSideVerifyCa,
		Address:  addr,
	})
	require.NoError(t, err)
	defer func() { _ = listener.Close() }()

	// clientSideVerifyCa
	caCert, err := loadx509Cert(clientSideVerifyCa)
	require.NoError(t, err)
	pool := x509.NewCertPool()
	pool.AddCert(caCert)

	// client A: use certificate issued by the CA blessed by the server
	wg.Add(1)
	go func() {
		conn, err := listener.Accept()
		assert.NoError(t, err)
		tlsConn := conn.(*tls.Conn)
		err = tlsConn.Handshake()
		assert.NoError(t, err)
		buf := make([]byte, len(message))
		_, err = conn.Read(buf)
		assert.NoError(t, err)
		assert.Equal(t, message, string(buf))
		wg.Done()
	}()
	cert, err := tls.LoadX509KeyPair(clientCert, clientKey)
	require.NoError(t, err)
	require.NotNil(t, cert)
	conn, err := tls.Dial("tcp", addr, &tls.Config{
		Certificates: []tls.Certificate{cert},
		RootCAs:      pool,
	})
	require.NoError(t, err)
	_, err = conn.Write([]byte(message))
	assert.NoError(t, err)
	_ = conn.Close()
	wg.Wait()

	// client B: use certificate not issued by the server's client CA pool
	wg.Add(1)
	go func() {
		conn, err := listener.Accept()
		assert.NoError(t, err)
		tlsConn := conn.(*tls.Conn)
		err = tlsConn.Handshake()
		assert.Error(t, err)
		wg.Done()
	}()
	cert, err = tls.LoadX509KeyPair(wrongClientCert, wrongClientKey)
	require.NoError(t, err)
	require.NotNil(t, cert)
	_, _ = tls.Dial("tcp", addr, &tls.Config{
		Certificates: []tls.Certificate{cert},
		RootCAs:      pool,
	})
	wg.Wait()
}
