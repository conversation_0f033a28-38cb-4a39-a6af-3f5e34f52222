package mtls

import (
	"crypto/tls"
	"crypto/x509"
	"encoding/pem"
	"net"
	"os"

	"github.com/pkg/errors"
)

func loadx509Cert(filepath string) (*x509.Certificate, error) {
	raw, err := os.ReadFile(filepath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to read file from %s", filepath)
	}
	block, _ := pem.Decode(raw)
	return x509.ParseCertificate(block.Bytes)
}

type ListenerOption struct {
	CertPath string
	KeyPath  string
	CaPath   string
	Address  string
}

func NewListener(option ListenerOption) (net.Listener, error) {
	var (
		certPath = option.CertPath
		keyPath  = option.KeyPath
		caPath   = option.CaPath
		address  = option.Address
	)
	if len(certPath) == 0 {
		return nil, errors.New("certPath cannot be empty")
	}
	if len(keyPath) == 0 {
		return nil, errors.New("keyPath cannot be empty")
	}
	if len(caPath) == 0 {
		return nil, errors.New("caPath cannot be empty")
	}
	if len(address) == 0 {
		return nil, errors.New("address cannot be empty")
	}
	caCert, err := loadx509Cert(caPath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to load CA cert from %s", certPath)
	}
	pool := x509.NewCertPool()
	pool.AddCert(caCert)

	cert, err := tls.LoadX509KeyPair(certPath, keyPath)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to load tls key pair from cert: %s, key: %s", certPath, keyPath)
	}

	tlsCfg := &tls.Config{
		Certificates: []tls.Certificate{cert},
		ClientAuth:   tls.RequireAndVerifyClientCert,
		ClientCAs:    pool,
	}
	// fix `missing selected ALPN property` error during TLS handshake in Python gRPC client.
	tlsCfg.NextProtos = append(tlsCfg.NextProtos, "h2")
	listener, err := tls.Listen("tcp", address, tlsCfg)
	if err != nil {
		return nil, errors.Wrapf(err, "failed to start listener on %s", address)
	}
	return listener, nil
}
