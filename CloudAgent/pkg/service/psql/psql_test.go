package psql

import (
	"context"
	"errors"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbpsqlsvc "github.com/risingwavelabs/cloudagent/pbgen/services/psql"
	providerpsql "github.com/risingwavelabs/cloudagent/pkg/providers/psql"
)

func TestService_CloneDatabase(t *testing.T) {
	taskID := "test-clone-task"
	namespace := "default"

	tests := []struct {
		name          string
		req           *pbpsqlsvc.CloneDatabaseRequest
		mockProvider  func(*providerpsql.MockProvider)
		want          *pbpsqlsvc.CloneDatabaseResponse
		wantErr       bool
		wantErrorCode codes.Code
	}{
		{
			name: "successful clone",
			req: &pbpsqlsvc.CloneDatabaseRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        taskID,
					Namespace: namespace,
				},
				SourceConnection: &pbpsqlsvc.Connection{
					Host:     "source-host",
					Port:     5432,
					Database: "source-db",
					Username: "source-user",
					Password: "source-pass",
				},
				TargetConnection: &pbpsqlsvc.Connection{
					Host:     "target-host",
					Port:     5433,
					Database: "target-db",
					Username: "target-user",
					Password: "target-pass",
				},
			},
			mockProvider: func(provider *providerpsql.MockProvider) {
				provider.EXPECT().CloneDatabase(context.Background(), providerpsql.CloneDatabaseOption{
					TaskID:        taskID,
					TaskNamespace: namespace,
					SourceConnection: providerpsql.Connection{
						Host:     "source-host",
						Port:     5432,
						Database: "source-db",
						Username: "source-user",
						Password: "source-pass",
					},
					TargetConnection: providerpsql.Connection{
						Host:     "target-host",
						Port:     5433,
						Database: "target-db",
						Username: "target-user",
						Password: "target-pass",
					},
				}).Return(nil)
			},
			want: &pbpsqlsvc.CloneDatabaseResponse{
				Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
			},
		},
		{
			name: "clone with special characters in password",
			req: &pbpsqlsvc.CloneDatabaseRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        taskID,
					Namespace: namespace,
				},
				SourceConnection: &pbpsqlsvc.Connection{
					Host:     "source-host",
					Port:     5432,
					Database: "source-db",
					Username: "source-user",
					Password: "p@ss$w0rd'\"\\&<>|",
				},
				TargetConnection: &pbpsqlsvc.Connection{
					Host:     "target-host",
					Port:     5433,
					Database: "target-db",
					Username: "target-user",
					Password: "t@rget$p@ss'\"\\&<>|",
				},
			},
			mockProvider: func(provider *providerpsql.MockProvider) {
				provider.EXPECT().CloneDatabase(context.Background(), providerpsql.CloneDatabaseOption{
					TaskID:        taskID,
					TaskNamespace: namespace,
					SourceConnection: providerpsql.Connection{
						Host:     "source-host",
						Port:     5432,
						Database: "source-db",
						Username: "source-user",
						Password: "p@ss$w0rd'\"\\&<>|",
					},
					TargetConnection: providerpsql.Connection{
						Host:     "target-host",
						Port:     5433,
						Database: "target-db",
						Username: "target-user",
						Password: "t@rget$p@ss'\"\\&<>|",
					},
				}).Return(nil)
			},
			want: &pbpsqlsvc.CloneDatabaseResponse{
				Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
			},
		},
		{
			name: "provider error",
			req: &pbpsqlsvc.CloneDatabaseRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        taskID,
					Namespace: namespace,
				},
				SourceConnection: &pbpsqlsvc.Connection{
					Host:     "source-host",
					Port:     5432,
					Database: "source-db",
					Username: "source-user",
					Password: "source-pass",
				},
				TargetConnection: &pbpsqlsvc.Connection{
					Host:     "target-host",
					Port:     5433,
					Database: "target-db",
					Username: "target-user",
					Password: "target-pass",
				},
			},
			mockProvider: func(provider *providerpsql.MockProvider) {
				provider.EXPECT().CloneDatabase(gomock.Any(), gomock.Any()).Return(errors.New("clone failed"))
			},
			wantErr:       true,
			wantErrorCode: codes.Internal,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			provider := providerpsql.NewMockProvider(ctrl)
			if tt.mockProvider != nil {
				tt.mockProvider(provider)
			}

			service := &Service{
				provider: provider,
			}

			got, err := service.CloneDatabase(context.Background(), tt.req)
			if tt.wantErr {
				require.Error(t, err)
				assert.Equal(t, tt.wantErrorCode, grpcstatus.Code(err))
				return
			}
			require.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})
	}
}
