package psql

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbpsqlsvc "github.com/risingwavelabs/cloudagent/pbgen/services/psql"
	providerpsql "github.com/risingwavelabs/cloudagent/pkg/providers/psql"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type Service struct {
	pbpsqlsvc.UnimplementedPsqlManagerServer
	provider providerpsql.Provider
}

func NewService(psql providerpsql.Provider) (*Service, error) {
	return &Service{
		provider: psql,
	}, nil
}

func (s Service) CreateDatabase(ctx context.Context, req *pbpsqlsvc.CreateDatabaseRequest) (*pbpsqlsvc.CreateDatabaseResponse, error) {
	err := s.provider.CreateDatabase(ctx, toConn(req.GetConnection()), req.GetDatabase())
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbpsqlsvc.CreateDatabaseResponse{
			Status: &pbcreation.Status{Code: pbcreation.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, err
	}
	return &pbpsqlsvc.CreateDatabaseResponse{
		Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
	}, nil
}

func (s Service) DeleteDatabase(ctx context.Context, req *pbpsqlsvc.DeleteDatabaseRequest) (*pbpsqlsvc.DeleteDatabaseResponse, error) {
	err := s.provider.DeleteDatabase(ctx, toConn(req.GetConnection()), req.GetDatabase())
	if err != nil {
		return nil, err
	}
	return &pbpsqlsvc.DeleteDatabaseResponse{
		Status: &pbdeletion.Status{Code: pbdeletion.StatusCode_DELETED},
	}, nil
}

func (s Service) CreateUser(ctx context.Context, req *pbpsqlsvc.CreateUserRequest) (*pbpsqlsvc.CreateUserResponse, error) {
	err := s.provider.CreateUser(ctx, toConn(req.GetConnection()), providerpsql.UserOption{
		Username:            req.GetUsername(),
		Password:            req.GetPassword(),
		PrivilegedDatabases: req.GetPrivilegedDatabases(),
	})
	if err != nil {
		return nil, err
	}
	return &pbpsqlsvc.CreateUserResponse{
		Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
	}, nil
}

func (s Service) DeleteUser(ctx context.Context, req *pbpsqlsvc.DeleteUserRequest) (*pbpsqlsvc.DeleteUserResponse, error) {
	err := s.provider.DeleteUser(ctx, toConn(req.GetConnection()), req.GetUsername())
	if err != nil {
		return nil, err
	}
	return &pbpsqlsvc.DeleteUserResponse{
		Status: &pbdeletion.Status{Code: pbdeletion.StatusCode_DELETED},
	}, nil
}

func (s Service) TruncateTables(ctx context.Context, req *pbpsqlsvc.TruncateTablesRequest) (*pbpsqlsvc.TruncateTablesResponse, error) {
	err := s.provider.TruncateTables(ctx, toConn(req.GetConnection()))
	if err != nil {
		return nil, err
	}
	return &pbpsqlsvc.TruncateTablesResponse{}, nil
}

func (s Service) UpdateSystemParameters(ctx context.Context, req *pbpsqlsvc.UpdateSystemParametersRequest) (*pbpsqlsvc.UpdateSystemParametersResponse, error) {
	err := s.provider.UpdateSystemParameters(ctx, toConn(req.GetConnection()), req.GetSystemParameters())
	if err != nil {
		return nil, err
	}
	return &pbpsqlsvc.UpdateSystemParametersResponse{}, nil
}

func (s Service) CloneDatabase(ctx context.Context, req *pbpsqlsvc.CloneDatabaseRequest) (*pbpsqlsvc.CloneDatabaseResponse, error) {
	option := providerpsql.CloneDatabaseOption{
		TaskID:           req.GetResourceMeta().GetId(),
		TaskNamespace:    req.GetResourceMeta().GetNamespace(),
		SourceConnection: toConn(req.GetSourceConnection()),
		TargetConnection: toConn(req.GetTargetConnection()),
	}

	err := s.provider.CloneDatabase(ctx, option)
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbpsqlsvc.CloneDatabaseResponse{
			Status: &pbcreation.Status{Code: pbcreation.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbpsqlsvc.CloneDatabaseResponse{
		Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
	}, nil
}

func toConn(connection *pbpsqlsvc.Connection) providerpsql.Connection {
	return providerpsql.Connection{
		Host:     connection.GetHost(),
		Port:     connection.GetPort(),
		Database: connection.GetDatabase(),
		Username: connection.GetUsername(),
		Password: connection.GetPassword(),
	}
}
