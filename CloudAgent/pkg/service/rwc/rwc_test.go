package rwc

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"

	"github.com/risingwavelabs/eris"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcrwc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
	providerrwc "github.com/risingwavelabs/cloudagent/pkg/providers/rwc"
)

func TestMetaRestore(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockProvider := providerrwc.NewMockProviderInterface(ctrl)
	ctx := context.Background()

	type testCase struct {
		err           error
		restoreOption providerrwc.RestoreMetaOption
		req           *pbsvcrwc.RestoreMetaRequest
	}

	testCases := []testCase{
		{
			req: &pbsvcrwc.RestoreMetaRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "name",
					Namespace: "ns",
				},
				ServiceAccount:    "SA",
				RwImageTag:        "v2.1.1",
				MetaSnapshotId:    1,
				MetaStoreType:     "etcd",
				BackupStorageUrl:  "backupurl",
				BackupStorageDir:  "backupdir",
				HummockStorageUrl: "hummockurl",
				HummockStorageDir: "hummockdir",
				MetastoreConfig: &pbsvcrwc.RestoreMetaRequest_EtcdConfig{
					EtcdConfig: &pbsvcrwc.RestoreMetaRequestEtcd{
						EtcdEndpoints: "127.0.0.1",
						EtcdAuth:      true,
						EtcdUsername:  "user",
						EtcdPassword:  "password",
					},
				},
				Envs: map[string]string{"env": "prod"},
			},
			restoreOption: providerrwc.RestoreMetaOption{
				TaskID:            "name",
				TaskNamespace:     "ns",
				ServiceAccount:    "SA",
				ImageTag:          "v2.1.1",
				MetaSnapshotID:    1,
				MetaStoreType:     "etcd",
				BackupStorageURL:  "backupurl",
				BackupStorageDir:  "backupdir",
				HummockStorageURL: "hummockurl",
				HummockStorageDir: "hummockdir",
				EtcdEndpoints:     "127.0.0.1",
				EtcdAuth:          true,
				EtcdUsername:      "user",
				EtcdPassword:      "password",
				Envs:              map[string]string{"env": "prod"},
			},
		},
		{
			req: &pbsvcrwc.RestoreMetaRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "name",
					Namespace: "ns",
				},
				ServiceAccount:    "SA",
				RwImageTag:        "v2.1.1",
				MetaSnapshotId:    1,
				MetaStoreType:     "sql",
				BackupStorageUrl:  "backupurl",
				BackupStorageDir:  "backupdir",
				HummockStorageUrl: "hummockurl",
				HummockStorageDir: "hummockdir",
				MetastoreConfig: &pbsvcrwc.RestoreMetaRequest_SqlConfig{
					SqlConfig: &pbsvcrwc.RestoreMetaRequestSql{
						SqlEndpoint: "psqlendpoint",
					},
				},
				Envs: map[string]string{"env": "prod"},
			},
			restoreOption: providerrwc.RestoreMetaOption{
				TaskID:            "name",
				TaskNamespace:     "ns",
				ServiceAccount:    "SA",
				ImageTag:          "v2.1.1",
				MetaSnapshotID:    1,
				MetaStoreType:     "sql",
				BackupStorageURL:  "backupurl",
				BackupStorageDir:  "backupdir",
				HummockStorageURL: "hummockurl",
				HummockStorageDir: "hummockdir",
				SQLEndpoint:       "psqlendpoint",
				Envs:              map[string]string{"env": "prod"},
			},
		},
		{
			req: &pbsvcrwc.RestoreMetaRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "name",
					Namespace: "ns",
				},
				ServiceAccount:    "SA",
				RwImageTag:        "v2.1.1",
				MetaSnapshotId:    1,
				MetaStoreType:     "sql",
				BackupStorageDir:  "backupdir",
				HummockStorageUrl: "hummockurl",
				HummockStorageDir: "hummockdir",
				MetastoreConfig: &pbsvcrwc.RestoreMetaRequest_SqlConfig{
					SqlConfig: &pbsvcrwc.RestoreMetaRequestSql{
						SqlEndpoint: "psqlendpoint",
					},
				},
				Envs: map[string]string{"env": "prod"},
			},
			restoreOption: providerrwc.RestoreMetaOption{
				TaskID:            "name",
				TaskNamespace:     "ns",
				ServiceAccount:    "SA",
				ImageTag:          "v2.1.1",
				MetaSnapshotID:    1,
				MetaStoreType:     "sql",
				BackupStorageDir:  "backupdir",
				HummockStorageURL: "hummockurl",
				HummockStorageDir: "hummockdir",
				SQLEndpoint:       "psqlendpoint",
				Envs:              map[string]string{"env": "prod"},
			},
			err: eris.New("invalid arguments"),
		},
	}

	for _, test := range testCases {
		s := Service{
			rwcProvider: mockProvider,
		}
		mockProvider.EXPECT().
			RestoreMeta(gomock.Any(), test.restoreOption).
			Return(test.err)
		res, err := s.RestoreMeta(ctx, test.req)
		if test.err != nil {
			require.Error(t, test.err)
		} else {
			require.NoError(t, err)
			assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
		}
	}
}
