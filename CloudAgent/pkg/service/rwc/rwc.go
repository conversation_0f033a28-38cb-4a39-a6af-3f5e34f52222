package rwc

import (
	"context"
	"fmt"
	"io"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/risingwavelabs/eris"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcrwc "github.com/risingwavelabs/cloudagent/pbgen/services/rwc"
	"github.com/risingwavelabs/cloudagent/pkg/providers/rwc"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

type Service struct {
	pbsvcrwc.UnimplementedRisingwaveControlServer
	rwcProvider rwc.ProviderInterface
}

func NewService(provider *rwc.Provider) *Service {
	return &Service{
		rwcProvider: provider,
	}
}

func (s *Service) MetaNodeBackup(ctx context.Context, req *pbsvcrwc.MetaNodeBackupRequest) (*pbsvcrwc.MetaNodeBackupResponse, error) {
	if err := s.rwcProvider.StartMetaNodeBackupTask(ctx, rwc.MetaNodeBackupOption{
		RwNamespace:   req.GetRwNamespace(),
		RwName:        req.GetRwName(),
		TaskID:        req.GetResourceMeta().GetId(),
		TaskNamespace: req.GetResourceMeta().GetNamespace(),
	}); err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcrwc.MetaNodeBackupResponse{
				Status: &pbcreation.Status{Code: pbcreation.StatusCode_ALREADY_EXISTS},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.MetaNodeBackupResponse{
		Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
	}, nil
}

func (s *Service) ValidateSource(ctx context.Context, req *pbsvcrwc.ValidateSourceRequest) (*pbsvcrwc.ValidateSourceResponse, error) {
	if err := s.rwcProvider.ValidateSource(ctx, rwc.ValidateSourceOption{
		RwNamespace: req.GetRwNamespace(),
		RwName:      req.GetRwName(),
		Props:       req.GetProps(),
	}); err != nil {
		if utils.IsInvalidArgument(err) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		if utils.IsFailedPrecondition(err) {
			return &pbsvcrwc.ValidateSourceResponse{
				ErrorMessage: err.Error(),
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.ValidateSourceResponse{}, nil
}

func (s *Service) GetClusterInfo(ctx context.Context, req *pbsvcrwc.GetClusterInfoRequest) (*pbsvcrwc.GetClusterInfoResponse, error) {
	output, err := s.rwcProvider.GetClusterInfo(ctx, req.GetRwName(), req.GetRwNamespace())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.GetClusterInfoResponse{
		Output: output,
	}, nil
}

func (s *Service) CordonWorkers(ctx context.Context, req *pbsvcrwc.CordonWorkersRequest) (*pbsvcrwc.CordonWorkersResponse, error) {
	err := s.rwcProvider.CordonWorkers(ctx, req.GetRwName(), req.GetRwNamespace(), req.GetWorkIds())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.CordonWorkersResponse{}, nil
}

func (s *Service) ResizeWorkers(ctx context.Context, req *pbsvcrwc.ResizeWorkersRequest) (*pbsvcrwc.ResizeWorkersResponse, error) {
	err := s.rwcProvider.ResizeWorkers(ctx, req.GetRwName(), req.GetRwNamespace(), req.GetAddedWorkIds(), req.GetDeletingWorkIds())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.ResizeWorkersResponse{}, nil
}

func (s *Service) DeleteWorkers(ctx context.Context, req *pbsvcrwc.DeleteWorkersRequest) (*pbsvcrwc.DeleteWorkersResponse, error) {
	err := s.rwcProvider.DeleteWorkers(ctx, req.GetRwName(), req.GetRwNamespace(), req.GetWorkIds())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.DeleteWorkersResponse{}, nil
}

func (s *Service) DeleteSnapshot(ctx context.Context, req *pbsvcrwc.DeleteSnapshotRequest) (*pbsvcrwc.DeleteSnapshotResponse, error) {
	err := s.rwcProvider.DeleteSnapshot(ctx, req.GetRwVersion(), req.GetRwName(), req.GetRwNamespace(), req.GetSnapshotId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.DeleteSnapshotResponse{}, nil
}

func (s *Service) RestoreMeta(ctx context.Context, req *pbsvcrwc.RestoreMetaRequest) (*pbsvcrwc.RestoreMetaResponse, error) {
	option := rwc.RestoreMetaOption{
		TaskID:            req.GetResourceMeta().GetId(),
		TaskNamespace:     req.GetResourceMeta().GetNamespace(),
		ServiceAccount:    req.GetServiceAccount(),
		ImageTag:          req.GetRwImageTag(),
		MetaSnapshotID:    req.GetMetaSnapshotId(),
		MetaStoreType:     req.GetMetaStoreType(),
		BackupStorageURL:  req.GetBackupStorageUrl(),
		BackupStorageDir:  req.GetBackupStorageDir(),
		HummockStorageURL: req.GetHummockStorageUrl(),
		HummockStorageDir: req.GetHummockStorageDir(),
		Envs:              req.GetEnvs(),
	}
	switch req.GetMetastoreConfig().(type) {
	case *pbsvcrwc.RestoreMetaRequest_EtcdConfig:
		option.EtcdEndpoints = req.GetEtcdConfig().GetEtcdEndpoints()
		option.EtcdAuth = req.GetEtcdConfig().GetEtcdAuth()
		option.EtcdUsername = req.GetEtcdConfig().GetEtcdUsername()
		option.EtcdPassword = req.GetEtcdConfig().GetEtcdPassword()
	case *pbsvcrwc.RestoreMetaRequest_SqlConfig:
		option.SQLEndpoint = req.GetSqlConfig().GetSqlEndpoint()
	default:
		return nil, eris.New(fmt.Sprintf("metastore type %s not supported", req.GetMetastoreConfig()))
	}

	err := s.rwcProvider.RestoreMeta(ctx, option)
	if err != nil {
		if utils.IsInvalidArgument(err) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.RestoreMetaResponse{
		Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
	}, nil
}

func (s *Service) VacuumEtcdMeta(ctx context.Context, req *pbsvcrwc.VacuumEtcdMetaRequest) (*pbsvcrwc.VacuumEtcdMetaResponse, error) {
	err := s.rwcProvider.EtcdctlCommand(ctx, rwc.EtcdctlOption{
		EtcdPodName:      req.GetPodName(),
		EtcdPodNamespace: req.GetPodNamespace(),
		EtcdUsername:     req.GetEtcdUsername(),
		EtcdPassword:     req.GetEtcdPassword(),
		EtcdCommand:      []string{"del", "--prefix=true", ""},
	})
	if err != nil {
		if utils.IsInvalidArgument(err) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.VacuumEtcdMetaResponse{}, nil
}

// RW Diagnosis Report.
func (s *Service) GenDiagnosisReport(ctx context.Context, req *pbsvcrwc.GenDiagnosisReportRequest) (*pbsvcrwc.GenDiagnosisReportResponse, error) {
	res, err := s.rwcProvider.GenDiagnosisReport(ctx, req.GetServiceName(), req.GetNamespace())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.GenDiagnosisReportResponse{
		Report: res,
	}, nil
}

var (
	RwReportChunkSize = 32 * 1024 // bytes
)

// diagStreamWriter is a wrapper of the gRPC stream object.
type diagStreamWriter struct {
	stream pbsvcrwc.RisingwaveControl_GenDiagnosisReportStreamServer
}

func (w *diagStreamWriter) Write(data []byte) (int, error) {
	err := w.stream.Send(&pbsvcrwc.GenDiagnosisReportStreamResponse{
		ReportChunk: data,
	})
	if err != nil {
		return 0, err
	}
	return len(data), nil
}

func (s *Service) GenDiagnosisReportStream(req *pbsvcrwc.GenDiagnosisReportStreamRequest, stream pbsvcrwc.RisingwaveControl_GenDiagnosisReportStreamServer) error {
	// In server-side streaming, context is embedded in the stream
	// https://github.com/grpc/grpc-go/issues/156#issuecomment-91364882
	ctx := stream.Context()

	var body io.ReadCloser
	var err error
	// Still retrieve the entire report content here.
	if !req.GetGzipCompressed() {
		body, err = s.rwcProvider.GenDiagnosisReportBody(ctx, req.GetServiceName(), req.GetNamespace())
	} else {
		body, err = s.rwcProvider.GenDiagnosisReportBodyCompressed(ctx, req.GetServiceName(), req.GetNamespace())
	}
	if err != nil {
		return status.Error(codes.Internal, err.Error())
	}
	// Send report content chunks back through gRPC.
	defer func() { _ = body.Close() }()
	buffer := make([]byte, RwReportChunkSize)
	_, err = io.CopyBuffer(&diagStreamWriter{stream: stream}, body, buffer)
	if err != nil {
		return status.Error(codes.Internal, err.Error())
	}
	return nil
}

func (s *Service) MetaMigration(ctx context.Context, req *pbsvcrwc.MetaMigrationRequest) (*pbsvcrwc.MetaMigrationResponse, error) {
	err := s.rwcProvider.MetaMigration(ctx, rwc.MetaMigrationOption{
		TaskID:          req.GetResourceMeta().GetId(),
		TaskNamespace:   req.GetResourceMeta().GetNamespace(),
		RwNamespace:     req.GetRwNamespace(),
		RwName:          req.GetRwName(),
		EtcdEndpoints:   req.GetEtcdEndpoints(),
		SQLEndpoint:     req.GetSqlEndpoint(),
		TaskImage:       req.GetTaskImage(),
		TaskResources:   req.GetTaskResources(),
		TaskTolerations: req.GetTaskTolerations(),
		TaskAffinity:    req.GetTaskAffinity(),
	})
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcrwc.MetaMigrationResponse{
			Status: &pbcreation.Status{Code: pbcreation.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.MetaMigrationResponse{
		Status: &pbcreation.Status{Code: pbcreation.StatusCode_CREATED},
	}, nil
}

func (s *Service) FetchKafkaTopic(ctx context.Context, req *pbsvcrwc.FetchKafkaTopicRequest) (*pbsvcrwc.FetchKafkaTopicResponse, error) {
	topics, err := s.rwcProvider.FetchKafkaTopic(ctx, req)
	if utils.IsInvalidArgument(err) {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	if utils.IsFailedPrecondition(err) {
		return nil, status.Error(codes.FailedPrecondition, err.Error())
	}
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.FetchKafkaTopicResponse{
		Topics: topics,
	}, nil
}

func (s *Service) FetchKafkaMessage(ctx context.Context, req *pbsvcrwc.FetchKafkaMessageRequest) (*pbsvcrwc.FetchKafkaMessageResponse, error) {
	message, err := s.rwcProvider.FetchKafkaMessage(ctx, req)
	if utils.IsInvalidArgument(err) {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	if utils.IsFailedPrecondition(err) {
		return nil, status.Error(codes.FailedPrecondition, err.Error())
	}
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.FetchKafkaMessageResponse{
		Key:   message.Key,
		Value: message.Value,
	}, nil
}

func (s *Service) FetchPostgresTable(ctx context.Context, req *pbsvcrwc.FetchPostgresTableRequest) (*pbsvcrwc.FetchPostgresTableResponse, error) {
	tables, err := s.rwcProvider.FetchPostgresTable(ctx, req)
	if utils.IsInvalidArgument(err) {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	if utils.IsFailedPrecondition(err) {
		return nil, status.Error(codes.FailedPrecondition, err.Error())
	}
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.FetchPostgresTableResponse{
		Tables: tables,
	}, nil
}

func (s *Service) FetchSourceSchema(ctx context.Context, req *pbsvcrwc.FetchSourceSchemaRequest) (*pbsvcrwc.FetchSourceSchemaResponse, error) {
	files, err := s.rwcProvider.FetchSourceSchema(ctx, req)
	if utils.IsInvalidArgument(err) {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	if utils.IsFailedPrecondition(err) {
		return nil, status.Error(codes.FailedPrecondition, err.Error())
	}
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcrwc.FetchSourceSchemaResponse{
		Files: files,
	}, nil
}
