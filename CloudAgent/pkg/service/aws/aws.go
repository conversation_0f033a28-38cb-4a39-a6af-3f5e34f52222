package aws

import (
	"github.com/risingwavelabs/eris"

	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
)

type Service struct {
	pbsvcaws.UnimplementedAwsResourceManagerServer
	provider     *aws.Provider
	accoutID     string
	oidcPrivider string
	region       string
	vpcID        string
}

type NewServiceOption struct {
	Provider     *aws.Provider
	AccoutID     string
	OIDCPrivider string
	Region       string
	VPCID        string
}

func NewService(option NewServiceOption) (*Service, error) {
	// TODO(junfeng): check other inputs -- currently it's blocked by the E2E setup (we need to decide what value to use in E2E, setting a dummy VPC ID will break Security Group creation)
	if option.Provider == nil {
		return nil, eris.Errorf("AWS provider cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	return &Service{
		provider:     option.Provider,
		accoutID:     option.AccoutID,
		oidcPrivider: option.OIDCPrivider,
		region:       option.Region,
		vpcID:        option.VPCID,
	}, nil
}
