package aws

import (
	"context"
	"fmt"
	"testing"

	"google.golang.org/grpc/status"

	ack_v1alpha1 "github.com/aws-controllers-k8s/ec2-controller/apis/v1alpha1"
	ack_core_v1alpha1 "github.com/aws-controllers-k8s/runtime/apis/core/v1alpha1"
	"github.com/aws/aws-sdk-go-v2/service/ec2/types"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	"github.com/risingwavelabs/eris"
)

var (
	testID                   = "testID"
	testNamespace            = "testNamespace"
	testVPCID                = "testVPCID"
	testServiceName          = "testServiceName"
	testStatusAccepted       = "AVAILABLE"
	testVPCEndpointDNS       = "testDNS"
	testStatusProtoAvailable = pbsvcaws.VPCEndpointStatus_AVAILABLE
	testStatusProtoRejected  = pbsvcaws.VPCEndpointStatus_REJECTED
	errorStateMsg            = "resource is error state"
)

func TestCreateVPCEndpoint(t *testing.T) {
	c := fake.NewClient()

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ec2svc := aws.NewMockEC2ClientInterface(ctrl)

	serviceDetail := types.ServiceDetail{
		AvailabilityZones: []string{"az1", "az2", "az3"},
	}

	var subnetDetails []types.Subnet
	for i := range 3 {
		az := fmt.Sprintf("az%d", i+1)
		subnet := fmt.Sprintf("subnet%d", i+1)
		subnetDetails = append(
			subnetDetails,
			types.Subnet{
				AvailabilityZone: &az,
				SubnetId:         &subnet,
			},
		)
	}

	ec2svc.
		EXPECT().
		DescribeSubnets(gomock.Any(), gomock.Any()).
		Return(&subnetDetails, nil).
		AnyTimes()

	ec2svc.
		EXPECT().
		DescribeVPCEndpointService(gomock.Any(), gomock.Any()).
		Return(&serviceDetail, nil).
		AnyTimes()

	p := aws.CreateFakeProvider(c, ec2svc, nil, nil)
	s := Service{
		provider: p,
		vpcID:    testVPCID,
	}

	res, err := s.CreateVPCEndpoint(context.Background(), &pbsvcaws.CreateVPCEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		ServiceName: testServiceName,
		ExtraTags:   map[string]string{"EnvID": "env"},
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)
	vpcEndpoint, err := k8s.GetResource[ack_v1alpha1.VPCEndpoint](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, vpcEndpoint.Name)
	assert.Equal(t, vpcEndpoint.Namespace, testNamespace)
	assert.Equal(t, testVPCID, *vpcEndpoint.Spec.VPCID)
	assert.Equal(t, testServiceName, *vpcEndpoint.Spec.ServiceName)

	res, err = s.CreateVPCEndpoint(context.Background(), &pbsvcaws.CreateVPCEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		ServiceName: testServiceName,
		ExtraTags:   map[string]string{"EnvID": "env"},
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}

func TestGetVPCEndpoint(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)

	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	ec2svc := aws.NewMockEC2ClientInterface(ctrl)

	type VPCEndpointDescribeRes struct {
		endpoint *types.VpcEndpoint
		err      error
	}

	tests := []struct {
		description                 string
		vpcEndpoint                 *ack_v1alpha1.VPCEndpoint
		res                         *pbsvcaws.GetVPCEndpointResponse
		expectedVPCEndpointDescribe *VPCEndpointDescribeRes
	}{
		{
			description: "Normal case, resource is ready",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:   ack_core_v1alpha1.ConditionTypeResourceSynced,
							Status: v1.ConditionTrue,
						},
					},
					State:         &testStatusAccepted,
					VPCEndpointID: &testVPCID,
				},
				Spec: ack_v1alpha1.VPCEndpointSpec{
					VPCID: &testVPCID,
				},
			},
			expectedVPCEndpointDescribe: &VPCEndpointDescribeRes{
				endpoint: &types.VpcEndpoint{
					State: "available",
					DnsEntries: []types.DnsEntry{
						{
							DnsName: &testVPCEndpointDNS,
						},
					},
				},
			},
			res: &pbsvcaws.GetVPCEndpointResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				EndpointState: testStatusProtoAvailable,
				EndpointId:    testVPCID,
				EndpointDns:   testVPCEndpointDNS,
			},
		},
		{
			description: "Normal case, resource is rejected",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:   ack_core_v1alpha1.ConditionTypeResourceSynced,
							Status: v1.ConditionTrue,
						},
					},
					State:         &testStatusAccepted,
					VPCEndpointID: &testVPCID,
				},
				Spec: ack_v1alpha1.VPCEndpointSpec{
					VPCID: &testVPCID,
				},
			},
			expectedVPCEndpointDescribe: &VPCEndpointDescribeRes{
				endpoint: &types.VpcEndpoint{
					State: "rejected",
				},
			},
			res: &pbsvcaws.GetVPCEndpointResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				EndpointState: testStatusProtoRejected,
				EndpointId:    testVPCID,
			},
		},
		{
			description: "Normal case, resource is NOT ready",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:   ack_core_v1alpha1.ConditionTypeResourceSynced,
							Status: v1.ConditionFalse,
						},
					},
				},
			},
			res: &pbsvcaws.GetVPCEndpointResponse{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"type\":\"ACK.ResourceSynced\",\"status\":\"False\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is in error state",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: ack_v1alpha1.VPCEndpointStatus{
					Conditions: []*ack_core_v1alpha1.Condition{
						{
							Type:    ack_core_v1alpha1.ConditionTypeTerminal,
							Status:  v1.ConditionFalse,
							Message: &errorStateMsg,
						},
					},
				},
			},
			res: &pbsvcaws.GetVPCEndpointResponse{
				Status: &pbresource.Status{
					Code:    pbresource.StatusCode_NOT_READY,
					Message: "[{\"type\":\"ACK.Terminal\",\"status\":\"False\",\"message\":\"resource is error state\"}]",
				},
			},
		},
		{
			description: "Normal case, resource is not found",
			vpcEndpoint: &ack_v1alpha1.VPCEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: "random namespace",
					Name:      "random id",
				},
			},
			res: &pbsvcaws.GetVPCEndpointResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for _, tt := range tests {
		client := fake.NewClient(tt.vpcEndpoint)
		provider := aws.CreateFakeProvider(client, ec2svc, nil, nil)
		s := Service{
			provider: provider,
		}
		const (
			resourceID = "resource"
			namespace  = "ns"
		)

		if tt.expectedVPCEndpointDescribe != nil {
			ec2svc.EXPECT().
				DescribeVPCEndpoint(gomock.Any(), gomock.Any()).
				Return(tt.expectedVPCEndpointDescribe.endpoint, tt.expectedVPCEndpointDescribe.err).
				Times(1)
		}

		res, err := s.GetVPCEndpoint(context.Background(), &pbsvcaws.GetVPCEndpointRequest{
			ResourceMeta: &pbresource.Meta{
				Id:        resourceID,
				Namespace: namespace,
			},
		})
		require.NoError(t, err, "expect no error for test %v", tt)
		assert.Equal(t, res, tt.res, "unexpected result for test %v, get %v, want %v", tt, res, tt.res)
	}
}

func TestCheckVPCEndpointServiceReachability(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	testService := "testService"
	testCases := []struct {
		name         string
		describeRes  *types.ServiceDetail
		describeErr  error
		expectedResp *pbsvcaws.CheckVPCEndpointServiceReachabilityResponse
		expectedErr  error
	}{
		{
			name: "success - endpoint reachable",
			describeRes: &types.ServiceDetail{
				AvailabilityZones: []string{"az1", "az2", "az3"},
			},
			expectedResp: &pbsvcaws.CheckVPCEndpointServiceReachabilityResponse{
				Status: pbsvcaws.VPCEndpointServiceReachabilityStatus_SUCCESS,
			},
		},
		{
			name:        "not found - endpoint does not exist",
			describeRes: nil,
			describeErr: eris.New("").WithCode(eris.CodeNotFound),
			expectedResp: &pbsvcaws.CheckVPCEndpointServiceReachabilityResponse{
				Status: pbsvcaws.VPCEndpointServiceReachabilityStatus_NOT_FOUND,
			},
		},
		{
			name:         "error - provider failure",
			describeRes:  nil,
			describeErr:  eris.New("unexpected error"),
			expectedResp: nil,
			expectedErr:  status.Error(codes.Internal, eris.New("unexpected error").Error()),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			ec2svc := aws.NewMockEC2ClientInterface(ctrl)
			ec2svc.EXPECT().
				DescribeVPCEndpointService(gomock.Any(), gomock.Any()).
				Return(tc.describeRes, tc.describeErr).
				Times(1)
			c := fake.NewClient()
			p := aws.CreateFakeProvider(c, ec2svc, nil, nil)
			s := Service{
				provider: p,
			}
			req := &pbsvcaws.CheckVPCEndpointServiceReachabilityRequest{
				ServiceName: testService,
			}
			resp, err := s.CheckVPCEndpointServiceReachability(context.Background(), req)
			require.ErrorIs(t, err, tc.expectedErr)
			require.Equal(t, tc.expectedResp, resp)
		})
	}
}
