package aws

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateSecurityGroup(ctx context.Context, req *pbsvcaws.CreateSecurityGroupRequest) (*pbsvcaws.CreateSecurityGroupResponse, error) {
	err := s.provider.CreateSecurityGroup(ctx, aws.CreateSecurityGroupOption{
		VpcID:                 s.vpcID,
		Region:                s.region,
		Namespace:             req.GetResourceMeta().GetNamespace(),
		ResourceID:            req.GetResourceMeta().GetId(),
		InboundIPPermissions:  req.GetInboundIpPermissions(),
		OutboundIPPermissions: req.GetOutboundIpPermissions(),
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcaws.CreateSecurityGroupResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		if utils.IsInvalidArgument(err) {
			return nil, status.Error(codes.InvalidArgument, err.Error())
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.CreateSecurityGroupResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteSecurityGroup(ctx context.Context, req *pbsvcaws.DeleteSecurityGroupRequest) (*pbsvcaws.DeleteSecurityGroupResponse, error) {
	if err := s.provider.DeleteSecurityGroup(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcaws.DeleteSecurityGroupResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.DeleteSecurityGroupResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetSecurityGroup(ctx context.Context, req *pbsvcaws.GetSecurityGroupRequest) (*pbsvcaws.GetSecurityGroupResponse, error) {
	sg, err := s.provider.GetSecurityGroup(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.GetSecurityGroupResponse{
		Status:          sg.Status,
		SecurityGroupId: sg.SecurityGroupID,
	}, nil
}

func (s *Service) CreateSecurityGroupPolicy(ctx context.Context, req *pbsvcaws.CreateSecurityGroupPolicyRequest) (*pbsvcaws.CreateSecurityGroupPolicyResponse, error) {
	if err := s.provider.CreateSecurityGroupPolicy(ctx, aws.CreateSecurityGroupPolicyOption{
		ResourceID:        req.GetResourceMeta().GetId(),
		Namespace:         req.GetResourceMeta().GetNamespace(),
		SecurityGroupIDs:  req.GetSecurityGroupIds(),
		PodLabelsSelector: req.GetPodLabelsSelector(),
	}); err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcaws.CreateSecurityGroupPolicyResponse{Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			}}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.CreateSecurityGroupPolicyResponse{Status: &pbcreation.Status{
		Code: pbcreation.StatusCode_CREATED,
	}}, nil
}

func (s *Service) DeleteSecurityGroupPolicy(ctx context.Context, req *pbsvcaws.DeleteSecurityGroupPolicyRequest) (*pbsvcaws.DeleteSecurityGroupPolicyResponse, error) {
	if err := s.provider.DeleteSecurityGroupPolicy(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcaws.DeleteSecurityGroupPolicyResponse{Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			}}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.DeleteSecurityGroupPolicyResponse{Status: &pbdeletion.Status{
		Code: pbdeletion.StatusCode_SCHEDULED,
	}}, nil
}

func (s *Service) GetSecurityGroupPolicy(ctx context.Context, req *pbsvcaws.GetSecurityGroupPolicyRequest) (*pbsvcaws.GetSecurityGroupPolicyResponse, error) {
	if _, err := s.provider.GetSecurityGroupPolicy(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcaws.GetSecurityGroupPolicyResponse{Status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			}}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.GetSecurityGroupPolicyResponse{Status: &pbresource.Status{
		Code: pbresource.StatusCode_READY,
	}}, nil
}
