package aws

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateDBInstance(ctx context.Context, req *pbsvcaws.CreateDBInstanceRequest) (*pbsvcaws.CreateDBInstanceResponse, error) {
	err := s.provider.CreateDBInstance(ctx, aws.CreateDBInstanceOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		Spec:       req.GetSpec(),
	})

	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcaws.CreateDBInstanceResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.CreateDBInstanceResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteDBInstance(ctx context.Context, req *pbsvcaws.DeleteDBInstanceRequest) (*pbsvcaws.DeleteDBInstanceResponse, error) {
	err := s.provider.DeleteDBInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcaws.DeleteDBInstanceResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.DeleteDBInstanceResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) StartDBInstance(ctx context.Context, req *pbsvcaws.StartDBInstanceRequest) (*pbsvcaws.StartDBInstanceResponse, error) {
	err := s.provider.StartDBInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcaws.StartDBInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcaws.StartDBInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.StartDBInstanceResponse{
		Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
	}, nil
}

func (s *Service) StopDBInstance(ctx context.Context, req *pbsvcaws.StopDBInstanceRequest) (*pbsvcaws.StopDBInstanceResponse, error) {
	err := s.provider.StopDBInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcaws.StopDBInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcaws.StopDBInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.StopDBInstanceResponse{
		Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
	}, nil
}

func (s *Service) GetDBInstance(ctx context.Context, req *pbsvcaws.GetDBInstanceRequest) (*pbsvcaws.GetDBInstanceResponse, error) {
	m, err := s.provider.GetDBInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.GetDBInstanceResponse{
		Status:         m.Status,
		Endpoint:       m.Endpoint,
		InstanceStatus: m.InstanceStatus,
	}, nil
}
