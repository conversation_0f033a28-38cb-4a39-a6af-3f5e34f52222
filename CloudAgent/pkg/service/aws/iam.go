package aws

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/risingwavelabs/eris"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) toAccessOptions(protos []*pbsvcaws.IAMAccessOption) ([]aws.AccessOption, error) {
	var options []aws.AccessOption
	for _, p := range protos {
		switch p.GetAccessOption().(type) {
		case *pbsvcaws.IAMAccessOption_S3AccessOption:
			var dirs []string
			dirs = append(dirs, p.GetS3AccessOption().GetDirs()...)
			//nolint:staticcheck
			if p.GetS3AccessOption().GetDir() != "" {
				//nolint:staticcheck
				dirs = append(dirs, p.GetS3AccessOption().GetDir())
			}
			if len(dirs) == 0 {
				return nil, eris.New("invalid option: empty dirs in S3 access option")
			}
			options = append(options, aws.S3AccessOption{
				Bucket: p.GetS3AccessOption().GetBucket(),
				Dirs:   dirs,
			})
		default:
			return nil, eris.Errorf("invalid option: %v", p)
		}
	}
	return options, nil
}

func (s *Service) CreateIAMPolicy(ctx context.Context, req *pbsvcaws.CreateIAMPolicyRequest) (*pbsvcaws.CreateIAMPolicyResponse, error) {
	options, err := s.toAccessOptions(req.GetAccessOptions())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	option := aws.CreateIAMPolicyOption{
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ResourceID:    req.GetResourceMeta().GetId(),
		AccessOptions: options,
	}
	err = s.provider.CreateIAMPoicy(ctx, option)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcaws.CreateIAMPolicyResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.CreateIAMPolicyResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) CreateIAMRole(ctx context.Context, req *pbsvcaws.CreateIAMRoleRequest) (*pbsvcaws.CreateIAMRoleResponse, error) {
	err := s.provider.CreateIAMRole(ctx, aws.CreateIAMRoleOption{
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ResourceID: req.GetResourceMeta().GetId(),
		SA: aws.K8sServiceAccountOption{
			Name:      req.GetServiceAccount().GetName(),
			Namespace: req.GetServiceAccount().GetNamespace(),
		},
		AccountID:    s.accoutID,
		OIDCPrivider: s.oidcPrivider,
		PolicyRefs:   req.GetPolicyRefs(),
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcaws.CreateIAMRoleResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.CreateIAMRoleResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetIAMPolicy(ctx context.Context, req *pbsvcaws.GetIAMPolicyRequest) (*pbsvcaws.GetIAMPolicyResponse, error) {
	m, err := s.provider.GetIAMPolicy(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.GetIAMPolicyResponse{
		Status:    m.Status,
		PolicyArn: m.ARN,
	}, nil
}

func (s *Service) GetIAMRole(ctx context.Context, req *pbsvcaws.GetIAMRoleRequest) (*pbsvcaws.GetIAMRoleResponse, error) {
	m, err := s.provider.GetIAMRole(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.GetIAMRoleResponse{
		Status:  m.Status,
		RoleArn: m.ARN,
	}, nil
}

func (s *Service) DeleteIAMPolicy(ctx context.Context, req *pbsvcaws.DeleteIAMPolicyRequest) (*pbsvcaws.DeleteIAMPolicyResponse, error) {
	if err := s.provider.DeleteIAMPolicy(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcaws.DeleteIAMPolicyResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.DeleteIAMPolicyResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteIAMRole(ctx context.Context, req *pbsvcaws.DeleteIAMRoleRequest) (*pbsvcaws.DeleteIAMRoleResponse, error) {
	if err := s.provider.DeleteIAMRole(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcaws.DeleteIAMRoleResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcaws.DeleteIAMRoleResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}
