package aws

import (
	"context"
	"testing"

	ackiamv1alpha1 "github.com/aws-controllers-k8s/iam-controller/apis/v1alpha1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
)

func TestCreateIAMPolicySingleDir(t *testing.T) {
	const (
		resourceID             = "resource"
		namespace              = "ns"
		testBucket             = "test-bucket"
		testDir                = "test-dir"
		expectedResourcePolicy = `
{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Action": ["s3:*"],
				"Resource": [
					"arn:aws:s3:::test-bucket",
					"arn:aws:s3:::test-bucket/test-dir/*"
				]
			}
		]
}`
	)
	client := fake.NewClient()
	provider := aws.CreateFakeProvider(&k8s.KubernetesClient{Client: client}, nil, nil, nil)
	s := Service{
		provider: provider,
	}
	ctx := context.Background()
	req := &pbsvcaws.CreateIAMPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
		AccessOptions: []*pbsvcaws.IAMAccessOption{
			{
				AccessOption: &pbsvcaws.IAMAccessOption_S3AccessOption{
					S3AccessOption: &pbsvcaws.IAMS3AccessOption{
						Bucket: testBucket,
						Dir:    testDir,
					},
				},
			},
		},
	}
	res, err := s.CreateIAMPolicy(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)

	policy := ackiamv1alpha1.Policy{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &policy))
	assert.JSONEq(t, expectedResourcePolicy, *policy.Spec.PolicyDocument)

	res, err = s.CreateIAMPolicy(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}

func TestCreateIAMPolicy(t *testing.T) {
	const (
		resourceID             = "resource"
		namespace              = "ns"
		testBucket             = "test-bucket"
		testDir1               = "test-dir1"
		testDir2               = "test-dir2"
		expectedResourcePolicy = `
{
		"Version": "2012-10-17",
		"Statement": [
			{
				"Effect": "Allow",
				"Action": ["s3:*"],
				"Resource": [
					"arn:aws:s3:::test-bucket",
					"arn:aws:s3:::test-bucket/test-dir1/*",
					"arn:aws:s3:::test-bucket/test-dir2/*"
				]
			}
		]
}`
	)
	client := fake.NewClient()
	provider := aws.CreateFakeProvider(&k8s.KubernetesClient{Client: client}, nil, nil, nil)
	s := Service{
		provider: provider,
	}
	ctx := context.Background()
	req := &pbsvcaws.CreateIAMPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
		AccessOptions: []*pbsvcaws.IAMAccessOption{
			{
				AccessOption: &pbsvcaws.IAMAccessOption_S3AccessOption{
					S3AccessOption: &pbsvcaws.IAMS3AccessOption{
						Bucket: testBucket,
						Dirs:   []string{testDir1, testDir2},
					},
				},
			},
		},
	}
	res, err := s.CreateIAMPolicy(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)

	policy := ackiamv1alpha1.Policy{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &policy))
	assert.JSONEq(t, expectedResourcePolicy, *policy.Spec.PolicyDocument)

	res, err = s.CreateIAMPolicy(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
