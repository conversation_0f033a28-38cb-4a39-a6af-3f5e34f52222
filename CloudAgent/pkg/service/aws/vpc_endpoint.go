package aws

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbawssvc "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateVPCEndpoint(ctx context.Context, req *pbawssvc.CreateVPCEndpointRequest) (*pbawssvc.CreateVPCEndpointResponse, error) {
	option := aws.CreateVPCEndpointOption{
		VpcID:             s.vpcID,
		ResourceID:        req.GetResourceMeta().GetId(),
		Namespace:         req.GetResourceMeta().GetNamespace(),
		ExtraTags:         req.GetExtraTags(),
		ServiceName:       req.GetServiceName(),
		SecurityGroupIDs:  req.GetSecurityGroupIds(),
		SubnetIDs:         req.GetSubnetIds(),
		PrivateDNSEnabled: req.GetPrivateDnsEnabled(),
	}

	err := s.provider.CreateVPCEndpoint(ctx, option)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbawssvc.CreateVPCEndpointResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbawssvc.CreateVPCEndpointResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteVPCEndpoint(ctx context.Context, req *pbawssvc.DeleteVPCEndpointRequest) (*pbawssvc.DeleteVPCEndpointResponse, error) {
	err := s.provider.DeleteVPCEndpoint(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbawssvc.DeleteVPCEndpointResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbawssvc.DeleteVPCEndpointResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetVPCEndpoint(ctx context.Context, req *pbawssvc.GetVPCEndpointRequest) (*pbawssvc.GetVPCEndpointResponse, error) {
	vpcEndpointMeta, err := s.provider.GetVPCEndpoint(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &pbawssvc.GetVPCEndpointResponse{
		Status:        vpcEndpointMeta.Status,
		EndpointId:    vpcEndpointMeta.VPCEndpointID,
		EndpointState: vpcEndpointMeta.VPCEndpointState,
		EndpointDns:   vpcEndpointMeta.VPCEndpointDNS,
	}, nil
}

func (s *Service) CheckVPCEndpointServiceReachability(ctx context.Context, req *pbawssvc.CheckVPCEndpointServiceReachabilityRequest) (*pbawssvc.CheckVPCEndpointServiceReachabilityResponse, error) {
	err := s.provider.CheckVPCEndpointServiceReachability(ctx, req.GetServiceName())
	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbawssvc.CheckVPCEndpointServiceReachabilityResponse{
				Status: pbawssvc.VPCEndpointServiceReachabilityStatus_NOT_FOUND,
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &pbawssvc.CheckVPCEndpointServiceReachabilityResponse{
		Status: pbawssvc.VPCEndpointServiceReachabilityStatus_SUCCESS,
	}, nil
}
