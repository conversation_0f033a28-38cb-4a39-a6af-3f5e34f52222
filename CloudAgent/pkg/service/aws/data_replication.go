package aws

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbcommondata "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateSimpleDataReplicationTask(ctx context.Context, req *pbcommondata.CreateSimpleDataReplicationTaskRequest) (*pbcommondata.CreateSimpleDataReplicationTaskResponse, error) {
	err := s.provider.RunSimpleDataReplication(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
		req.GetSourceBucket(),
		req.GetSourceDirectory(),
		req.GetSinkBucket(),
		req.GetSinkDirectory(),
	)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbcommondata.CreateSimpleDataReplicationTaskResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbcommondata.CreateSimpleDataReplicationTaskResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}
