package aws

import (
	"context"
	"testing"

	ackrdsv1alpha1 "github.com/aws-controllers-k8s/rds-controller/apis/v1alpha1"
	"github.com/aws-controllers-k8s/rds-controller/pkg/resource/db_instance"
	rdsTypes "github.com/aws/aws-sdk-go-v2/service/rds/types"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	"github.com/risingwavelabs/eris"

	pbaws "github.com/risingwavelabs/cloudagent/pbgen/common/aws"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbsvcaws "github.com/risingwavelabs/cloudagent/pbgen/services/aws"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/aws"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func getDBInstanceSpecProto() *pbaws.DBInstanceSpec {
	return &pbaws.DBInstanceSpec{
		DbInstanceIdentifier: "test-id",
		DbInstanceClass:      "db.t4g.micro",
		AllocatedStorage:     20,
		Engine:               "postgres",
		EngineVersion:        "14",
		DbName:               "database1",
		MasterUsername:       "user1",
		MasterUserPassword: &pbaws.PasswordSecretRef{
			Namespace: "test-namespace",
			Name:      "test-name",
			Key:       "password",
		},
		DbSubnetGroupName:   "test-subnet",
		VpcSecurityGroupIds: []string{"sg-test"},
		Tags: map[string]string{
			"tag1": "value1",
		},
	}
}

func generateSpec(modifiers ...func(*pbaws.DBInstanceSpec)) *pbaws.DBInstanceSpec {
	proto := getDBInstanceSpecProto()
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func genDbinstance(modifiers ...func(*ackrdsv1alpha1.DBInstance)) *ackrdsv1alpha1.DBInstance {
	spec, err := conversion.FromDBInstanceSpecProto(generateSpec())
	if err != nil {
		panic(err)
	}
	dbinstance := &ackrdsv1alpha1.DBInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-rds",
			Namespace: "test-namespace",
		},
		Spec: *spec,
		Status: ackrdsv1alpha1.DBInstanceStatus{
			DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
		},
	}
	for _, m := range modifiers {
		m(dbinstance)
	}
	return dbinstance
}

func TestService_CreateDBInstance(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcaws.CreateDBInstanceRequest
		initObjs []k8sclient.Object
		want     *pbsvcaws.CreateDBInstanceResponse
		wantErr  bool
	}{
		{
			name: "regular",
			req: &pbsvcaws.CreateDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
				Spec: generateSpec(),
			},
			want: &pbsvcaws.CreateDBInstanceResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "already exist",
			req: &pbsvcaws.CreateDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
				Spec: generateSpec(),
			},
			initObjs: []k8sclient.Object{
				genDbinstance(),
			},
			want: &pbsvcaws.CreateDBInstanceResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s := &Service{
				provider: aws.CreateFakeProvider(c, nil, nil, nil),
			}
			got, err := s.CreateDBInstance(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_DeleteDBInstance(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcaws.DeleteDBInstanceRequest
		initObjs []k8sclient.Object
		want     *pbsvcaws.DeleteDBInstanceResponse
		wantErr  bool
	}{
		{
			name: "regular",
			req: &pbsvcaws.DeleteDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			},
			initObjs: []k8sclient.Object{
				genDbinstance(),
			},
			want: &pbsvcaws.DeleteDBInstanceResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "not found",
			req: &pbsvcaws.DeleteDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			},
			want: &pbsvcaws.DeleteDBInstanceResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s := &Service{
				provider: aws.CreateFakeProvider(c, nil, nil, nil),
			}

			got, err := s.DeleteDBInstance(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_StartDBInstance(t *testing.T) {
	tests := []struct {
		name     string
		initObjs []k8sclient.Object
		mockRDS  func(*aws.MockRDSClientInterface)
		want     *pbsvcaws.StartDBInstanceResponse
		wantErr  bool
	}{
		{
			name: "not found",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &pbsvcaws.StartDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
			},
		},
		{
			name: "already exists (starting)",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStarting),
				}, nil)
			},
			want: &pbsvcaws.StartDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "already exists (available)",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
				}, nil)
			},
			want: &pbsvcaws.StartDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "scheduled",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopped),
				}, nil)
				c.EXPECT().StartDBInstance(gomock.Any(), "test-rds").Return(nil)
			},
			want: &pbsvcaws.StartDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			rdsClient := aws.NewMockRDSClientInterface(ctrl)
			s := &Service{
				provider: aws.CreateFakeProvider(c, nil, nil, rdsClient),
			}

			if tt.mockRDS != nil {
				tt.mockRDS(rdsClient)
			}
			got, err := s.StartDBInstance(ctx, &pbsvcaws.StartDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			})
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_StopDBInstance(t *testing.T) {
	tests := []struct {
		name     string
		initObjs []k8sclient.Object
		mockRDS  func(*aws.MockRDSClientInterface)
		want     *pbsvcaws.StopDBInstanceResponse
		wantErr  bool
	}{
		{
			name: "not found",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &pbsvcaws.StopDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
			},
		},
		{
			name: "already exists (stopping)",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopping),
				}, nil)
			},
			want: &pbsvcaws.StopDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "already exists (stopped)",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopped),
				}, nil)
			},
			want: &pbsvcaws.StopDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "scheduled",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
				}, nil)
				c.EXPECT().StopDBInstance(gomock.Any(), "test-rds").Return(nil)
			},
			want: &pbsvcaws.StopDBInstanceResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			rdsClient := aws.NewMockRDSClientInterface(ctrl)
			s := &Service{
				provider: aws.CreateFakeProvider(c, nil, nil, rdsClient),
			}

			if tt.mockRDS != nil {
				tt.mockRDS(rdsClient)
			}
			got, err := s.StopDBInstance(ctx, &pbsvcaws.StopDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			})
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_GetDBInstance(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcaws.GetDBInstanceRequest
		initObjs []k8sclient.Object
		mockRDS  func(*aws.MockRDSClientInterface)
		want     *pbsvcaws.GetDBInstanceResponse
		wantErr  bool
	}{
		{
			name: "ready",
			req: &pbsvcaws.GetDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			},
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusAvailable),
					Endpoint: &rdsTypes.Endpoint{
						Address: utils.Ptr("test-rds.something.ap-southeast-1.rds.amazonaws.com"),
					},
				}, nil)
			},
			want: &pbsvcaws.GetDBInstanceResponse{
				Status: &pbresource.Status{Code: pbresource.StatusCode_READY},
				Endpoint: &pbaws.DBInstanceEndpoint{
					Address: "test-rds.something.ap-southeast-1.rds.amazonaws.com",
				},
				InstanceStatus: pbaws.DBInstanceStatus_AVAILABLE,
			},
		},
		{
			name: "not ready",
			req: &pbsvcaws.GetDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			},
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusCreating),
				}, nil)
			},
			want: &pbsvcaws.GetDBInstanceResponse{
				Status:         &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				InstanceStatus: pbaws.DBInstanceStatus_NOT_READY,
			},
		},
		{
			name: "stopped",
			req: &pbsvcaws.GetDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			},
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(&rdsTypes.DBInstance{
					DBInstanceStatus: utils.Ptr(db_instance.StatusStopped),
					Endpoint: &rdsTypes.Endpoint{
						Address: utils.Ptr("test-rds.something.ap-southeast-1.rds.amazonaws.com"),
					},
				}, nil)
			},
			want: &pbsvcaws.GetDBInstanceResponse{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				Endpoint: &pbaws.DBInstanceEndpoint{
					Address: "test-rds.something.ap-southeast-1.rds.amazonaws.com",
				},
				InstanceStatus: pbaws.DBInstanceStatus_STOPPED,
			},
		},
		{
			name: "not found",
			mockRDS: func(c *aws.MockRDSClientInterface) {
				c.EXPECT().DescribeDBInstance(gomock.Any(), "test-rds").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			req: &pbsvcaws.GetDBInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-rds",
					Namespace: "test-namespace",
				},
			},
			want: &pbsvcaws.GetDBInstanceResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			rdsClient := aws.NewMockRDSClientInterface(ctrl)
			s := &Service{
				provider: aws.CreateFakeProvider(c, nil, nil, rdsClient),
			}

			if tt.mockRDS != nil {
				tt.mockRDS(rdsClient)
			}

			got, err := s.GetDBInstance(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}
