package byoc

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batch "k8s.io/api/batch/v1"
	corev1 "k8s.io/api/core/v1"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcbyoc "github.com/risingwavelabs/cloudagent/pbgen/services/byoc"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/byoc"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	testSubnet    = "testSubnet"
	testID        = "testID"
	testNamespace = "testNamespace"
)

func TestCreateApplyByocModuleTask(t *testing.T) {
	c := fake.NewClient()
	p := byoc.CreateFakeProvider(c)

	s := Service{
		provider: p,
	}

	res, err := s.CreateApplyByocModuleTask(context.Background(), &pbsvcbyoc.CreateApplyByocModuleTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		TaskAffinity: &pbk8s.Affinity{
			NodeAffinity: &pbk8s.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
					NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
						{
							MatchExpressions: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key1",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									Values:   []string{"val1", "val2"},
								},
							},
							MatchFields: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key2",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
									Values:   []string{"val3", "val4"},
								},
							},
						},
					},
				},
			},
		},
		TaskTolerations: []*pbk8s.Toleration{
			{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
				TolerationSecs: utils.Ptr(int64(10)),
			},
		},
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	job, err := k8s.GetResource[batch.Job](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, job.Name)
	assert.Equal(t, job.Namespace, testNamespace)
	assert.Equal(t, job.Spec.Template.Spec.Affinity, &corev1.Affinity{
		NodeAffinity: &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      "key1",
								Operator: corev1.NodeSelectorOpDoesNotExist,
								Values:   []string{"val1", "val2"},
							},
						},
						MatchFields: []corev1.NodeSelectorRequirement{
							{
								Key:      "key2",
								Operator: corev1.NodeSelectorOpNotIn,
								Values:   []string{"val3", "val4"},
							},
						},
					},
				},
			},
		},
	})
	assert.Equal(t, job.Spec.Template.Spec.Tolerations, []corev1.Toleration{
		{
			Key:               "testkey",
			Value:             "testvalue",
			Effect:            corev1.TaintEffectNoExecute,
			Operator:          corev1.TolerationOpEqual,
			TolerationSeconds: utils.Ptr(int64(10)),
		},
	})

	res, err = s.CreateApplyByocModuleTask(context.Background(), &pbsvcbyoc.CreateApplyByocModuleTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}

func TestCreateRetrieveByocModuleOutputTask(t *testing.T) {
	c := fake.NewClient()
	p := byoc.CreateFakeProvider(c)

	s := Service{
		provider: p,
	}

	res, err := s.CreateRetrieveByocModuleOutputTask(context.Background(), &pbsvcbyoc.CreateRetrieveByocModuleOutputTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		TaskAffinity: &pbk8s.Affinity{
			NodeAffinity: &pbk8s.NodeAffinity{
				RequiredDuringSchedulingIgnoredDuringExecution: &pbk8s.NodeSelector{
					NodeSelectorTerms: []*pbk8s.NodeSelectorTerm{
						{
							MatchExpressions: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key1",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_DOES_NOT_EXIST,
									Values:   []string{"val1", "val2"},
								},
							},
							MatchFields: []*pbk8s.NodeSelectorRequirement{
								{
									Key:      "key2",
									Operator: pbk8s.NodeSelectorOperator_NODE_SELECTOR_OPERATOR_NOT_IN,
									Values:   []string{"val3", "val4"},
								},
							},
						},
					},
				},
			},
		},
		TaskTolerations: []*pbk8s.Toleration{
			{
				Key:            "testkey",
				Value:          "testvalue",
				Effect:         pbk8s.TaintEffect_TAINT_EFFECT_NO_EXECUTE,
				Operator:       pbk8s.TolerationOperator_TOLERATION_OPERATOR_EQUAL,
				TolerationSecs: utils.Ptr(int64(10)),
			},
		},
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	job, err := k8s.GetResource[batch.Job](context.Background(), c, testID, testNamespace)
	assert.Equal(t, job.Spec.Template.Spec.Affinity, &corev1.Affinity{
		NodeAffinity: &corev1.NodeAffinity{
			RequiredDuringSchedulingIgnoredDuringExecution: &corev1.NodeSelector{
				NodeSelectorTerms: []corev1.NodeSelectorTerm{
					{
						MatchExpressions: []corev1.NodeSelectorRequirement{
							{
								Key:      "key1",
								Operator: corev1.NodeSelectorOpDoesNotExist,
								Values:   []string{"val1", "val2"},
							},
						},
						MatchFields: []corev1.NodeSelectorRequirement{
							{
								Key:      "key2",
								Operator: corev1.NodeSelectorOpNotIn,
								Values:   []string{"val3", "val4"},
							},
						},
					},
				},
			},
		},
	})
	assert.Equal(t, job.Spec.Template.Spec.Tolerations, []corev1.Toleration{
		{
			Key:               "testkey",
			Value:             "testvalue",
			Effect:            corev1.TaintEffectNoExecute,
			Operator:          corev1.TolerationOpEqual,
			TolerationSeconds: utils.Ptr(int64(10)),
		},
	})

	require.NoError(t, err)
	assert.Equal(t, testID, job.Name)
	assert.Equal(t, job.Namespace, testNamespace)

	res, err = s.CreateRetrieveByocModuleOutputTask(context.Background(), &pbsvcbyoc.CreateRetrieveByocModuleOutputTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
