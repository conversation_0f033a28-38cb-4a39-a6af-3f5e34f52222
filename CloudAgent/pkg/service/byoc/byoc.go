package byoc

import (
	pbsvcbyoc "github.com/risingwavelabs/cloudagent/pbgen/services/byoc"
	"github.com/risingwavelabs/cloudagent/pkg/providers/byoc"
)

type Service struct {
	pbsvcbyoc.UnimplementedByocResourceManagerServer
	provider *byoc.Provider
}

type NewServiceOption struct {
	Provider *byoc.Provider
}

func NewService(provider *byoc.Provider) *Service {
	return &Service{
		provider: provider,
	}
}
