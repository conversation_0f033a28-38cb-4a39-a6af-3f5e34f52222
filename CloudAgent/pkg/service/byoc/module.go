package byoc

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcbyoc "github.com/risingwavelabs/cloudagent/pbgen/services/byoc"
	"github.com/risingwavelabs/cloudagent/pkg/providers/byoc"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateApplyByocModuleTask(ctx context.Context, req *pbsvcbyoc.CreateApplyByocModuleTaskRequest) (*pbsvcbyoc.CreateApplyByocModuleTaskResponse, error) {
	option := byoc.ApplyModuleOptions{
		TaskID:          req.GetResourceMeta().GetId(),
		TaskNamespace:   req.GetResourceMeta().GetNamespace(),
		ApplyOptions:    req.GetApplyOptions(),
		ModuleOptions:   req.GetModuleOptions(),
		PackageOptions:  req.GetPackageOptions(),
		TaskTolerations: req.GetTaskTolerations(),
		TaskAffinity:    req.GetTaskAffinity(),
	}

	err := s.provider.RunApplyByocModuleTask(ctx, &option)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcbyoc.CreateApplyByocModuleTaskResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcbyoc.CreateApplyByocModuleTaskResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) CreateRetrieveByocModuleOutputTask(ctx context.Context, req *pbsvcbyoc.CreateRetrieveByocModuleOutputTaskRequest) (*pbsvcbyoc.CreateRetrieveByocModuleOutputTaskResponse, error) {
	option := byoc.RetrieveModuleOutputOptions{
		TaskID:          req.GetResourceMeta().GetId(),
		TaskNamespace:   req.GetResourceMeta().GetNamespace(),
		OutputKey:       req.GetOutputKey(),
		OutputOptions:   req.GetOutputOptions(),
		ModuleOptions:   req.GetModuleOptions(),
		PackageOptions:  req.GetPackageOptions(),
		TaskTolerations: req.GetTaskTolerations(),
		TaskAffinity:    req.GetTaskAffinity(),
	}

	err := s.provider.RunRetrieveByocModuleOutputTask(ctx, &option)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcbyoc.CreateRetrieveByocModuleOutputTaskResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcbyoc.CreateRetrieveByocModuleOutputTaskResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}
