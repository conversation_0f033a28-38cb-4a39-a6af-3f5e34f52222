package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateAzureServiceMonitor(ctx context.Context, req *pbsvck8s.CreateServiceMonitorRequest) (*pbsvck8s.CreateServiceMonitorResponse, error) {
	err := s.provider.CreateAzureServiceMonitor(ctx, k8s.CreateServiceMonitorOption{
		ResourceID:         req.GetResourceMeta().GetId(),
		Namespace:          req.GetResourceMeta().GetNamespace(),
		Labels:             req.GetLabels(),
		Annotations:        req.GetAnnotations(),
		ServiceMonitorSpec: req.GetServiceMonitorSpec(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateServiceMonitorResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateServiceMonitorResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) DeleteAzureServiceMonitor(ctx context.Context, req *pbsvck8s.DeleteServiceMonitorRequest) (*pbsvck8s.DeleteServiceMonitorResponse, error) {
	err := s.provider.DeleteAzureServiceMonitor(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteServiceMonitorResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteServiceMonitorResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}
