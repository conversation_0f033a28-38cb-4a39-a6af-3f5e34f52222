package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreatePodMonitoring(ctx context.Context, req *pbsvck8s.CreatePodMonitoringRequest) (*pbsvck8s.CreatePodMonitoringResponse, error) {
	err := s.provider.CreatePodMonitoring(ctx, k8s.CreatePodMonitoringOption{
		ResourceID:        req.GetResourceMeta().GetId(),
		Namespace:         req.GetResourceMeta().GetNamespace(),
		Labels:            req.GetLabels(),
		Annotations:       req.GetAnnotations(),
		PodMonitoringSpec: req.GetPodMonitoringSpec(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreatePodMonitoringResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreatePodMonitoringResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) DeletePodMonitoring(ctx context.Context, req *pbsvck8s.DeletePodMonitoringRequest) (*pbsvck8s.DeletePodMonitoringResponse, error) {
	err := s.provider.DeletePodMonitoring(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeletePodMonitoringResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeletePodMonitoringResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}
