package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateNetworkPolicy(ctx context.Context, req *pbsvck8s.CreateNetworkPolicyRequest) (*pbsvck8s.CreateNetworkPolicyResponse, error) {
	err := s.provider.CreateNetworkPolicy(ctx, k8s.CreateNetworkPolicyOption{
		ResourceID:        req.GetResourceMeta().GetId(),
		Namespace:         req.GetResourceMeta().GetNamespace(),
		Labels:            req.Get<PERSON>abe<PERSON>(),
		NetworkPolicySpec: req.GetSpec(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateNetworkPolicyResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateNetworkPolicyResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) DeleteNetworkPolicy(ctx context.Context, req *pbsvck8s.DeleteNetworkPolicyRequest) (*pbsvck8s.DeleteNetworkPolicyResponse, error) {
	err := s.provider.DeleteNetworkPolicy(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteNetworkPolicyResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteNetworkPolicyResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}

func (s *Service) CreateOrUpdateNetworkPolicy(ctx context.Context, req *pbsvck8s.CreateOrUpdateNetworkPolicyRequest) (*pbsvck8s.CreateOrUpdateNetworkPolicyResponse, error) {
	err := s.provider.CreateOrUpdateNetworkPolicy(ctx, k8s.CreateNetworkPolicyOption{
		ResourceID:        req.GetResourceMeta().GetId(),
		Namespace:         req.GetResourceMeta().GetNamespace(),
		Labels:            req.GetLabels(),
		NetworkPolicySpec: req.GetSpec(),
	})

	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateOrUpdateNetworkPolicyResponse{}, nil
}
