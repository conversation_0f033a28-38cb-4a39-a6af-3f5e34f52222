package k8s

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	"github.com/risingwavelabs/eris"
	"go.uber.org/mock/gomock"
	grpccodes "google.golang.org/grpc/codes"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	grpcstatus "google.golang.org/grpc/status"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/resource"

	pbgenres "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"

	rwv1alpha1 "github.com/risingwavelabs/risingwave-operator/apis/risingwave/v1alpha1"

	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
)

// Testing if the new message is interpreted exactly as the old one was.
func TestReqToScaleOpts(t *testing.T) {
	rwScaleSpec := &pbrw.ScaleSpec{
		Replicas: uint32(1),
	}

	tests := []struct {
		// message should be equivalent with req
		reqDeprecated *pbsvck8s.ScaleRisingWaveRequest

		// message should be equivalent with deprecatedReq
		req *pbsvck8s.ScaleRisingWaveRequestOneOf

		// both req and deprecatedReq should be converted to this value
		expected providerk8s.ScaleRisingWaveOption

		// for logging only
		description string

		shouldFail bool
	}{
		{
			description:   "empty request, should fail",
			shouldFail:    true,
			reqDeprecated: &pbsvck8s.ScaleRisingWaveRequest{},
			req:           &pbsvck8s.ScaleRisingWaveRequestOneOf{},
			expected:      providerk8s.ScaleRisingWaveOption{},
		},
		{
			description: "standalone and cluster defined, should fail",
			shouldFail:  true,
			reqDeprecated: &pbsvck8s.ScaleRisingWaveRequest{
				FrontendScaleSpec:   rwScaleSpec,
				StandaloneScaleSpec: rwScaleSpec,
			},
			// Not possible in new message, because it uses OneOf
			req: &pbsvck8s.ScaleRisingWaveRequestOneOf{},
		},
		{
			description: "scale cluster (one component), should succeed",
			shouldFail:  false,
			reqDeprecated: &pbsvck8s.ScaleRisingWaveRequest{
				FrontendScaleSpec: rwScaleSpec,
			},
			req: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec: rwScaleSpec,
					},
				},
			},
			expected: providerk8s.ScaleRisingWaveOption{
				FrontendScaleSepc: rwScaleSpec,
			},
		},
		{
			description: "scale cluster (all components), should succeed",
			shouldFail:  false,
			reqDeprecated: &pbsvck8s.ScaleRisingWaveRequest{
				FrontendScaleSpec:  rwScaleSpec,
				ComputeScaleSpec:   rwScaleSpec,
				CompactorScaleSpec: rwScaleSpec,
				MetaScaleSpec:      rwScaleSpec,
			},
			req: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec:  rwScaleSpec,
						ComputeScaleSpec:   rwScaleSpec,
						CompactorScaleSpec: rwScaleSpec,
						MetaScaleSpec:      rwScaleSpec,
					},
				},
			},
			expected: providerk8s.ScaleRisingWaveOption{
				FrontendScaleSepc:  rwScaleSpec,
				ComputeScaleSepc:   rwScaleSpec,
				CompactorScaleSepc: rwScaleSpec,
				MetaScaleSepc:      rwScaleSpec,
			},
		},
	}
	for _, test := range tests {
		failMsg := fmt.Sprintf("Test failed: %s", test.description)

		{ // scoping to re-use same var names
			opts, err := reqToScaleOpts(test.req)
			hasErr := err != nil
			assert.Equal(t, hasErr, test.shouldFail, failMsg)
			assert.Equal(t, test.expected, opts, failMsg)
		}

		opts, err := reqToScaleOptsDeprecated(test.reqDeprecated)
		hasErr := err != nil
		assert.Equal(t, hasErr, test.shouldFail, failMsg)
		assert.Equal(t, test.expected, opts, failMsg)
	}
}

// Testing if the new and the old RPC have the same effect.
func TestScaleRisingWave(t *testing.T) {
	name := "name"
	ns := "ns"
	resourceMeta := &pbgenres.Meta{
		Id:        name,
		Namespace: ns,
	}
	resourceScale := &pbk8s.ResourceRequirements{
		CpuRequest:    "123m",
		CpuLimit:      "456m",
		MemoryRequest: "123Mi",
		MemoryLimit:   "456Mi",
	}
	expectedCPURequest := resource.NewScaledQuantity(123, resource.Milli)
	expectedCPULimit := resource.NewScaledQuantity(456, resource.Milli)
	_ = expectedCPURequest.String()
	_ = expectedCPULimit.String()
	resourceCorev1 := corev1.ResourceRequirements{
		Limits: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedCPULimit,
			corev1.ResourceMemory: *resource.NewQuantity(456*1024*1024, "BinarySI"),
		},
		Requests: corev1.ResourceList{
			corev1.ResourceCPU:    *expectedCPURequest,
			corev1.ResourceMemory: *resource.NewQuantity(123*1024*1024, "BinarySI"),
		},
	}

	tests := []struct {
		// a description of this test. Used for logging only
		description string

		// deprecated scale requests. Should be equivalent with reqOneOf
		req *pbsvck8s.ScaleRisingWaveRequest

		// new scale requests. Should be equivalent with req
		reqOneOf *pbsvck8s.ScaleRisingWaveRequestOneOf

		// If true then original rw instance is in standalone mode. Else cluster mode.
		isStandalone bool

		// expected rw spec after scaling
		// nil is a valid value, if test is expected to fail
		expected *rwv1alpha1.RisingWaveSpec

		// True if scale operation should return an error
		shouldFail bool

		// only needed if shouldFail is true
		expectedErrCode grpccodes.Code
	}{
		{
			description: "rw instance not found",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        name + "other",
					Namespace: ns + "other",
				},
				FrontendScaleSpec: &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: &pbgenres.Meta{
					Id:        name + "other",
					Namespace: ns + "other",
				},
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec: &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
					},
				},
			},
			isStandalone:    true,
			shouldFail:      true,
			expectedErrCode: grpccodes.NotFound,
			expected:        nil,
		}, {
			description: "rw instance not found",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        name + "other",
					Namespace: ns + "other",
				},
				FrontendScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: &pbgenres.Meta{
					Id:        name + "other",
					Namespace: ns + "other",
				},
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
					},
				},
			},
			isStandalone:    false,
			shouldFail:      true,
			expectedErrCode: grpccodes.NotFound,
			expected:        nil,
		}, {
			description:     "empty request should fail",
			req:             &pbsvck8s.ScaleRisingWaveRequest{},
			reqOneOf:        &pbsvck8s.ScaleRisingWaveRequestOneOf{},
			isStandalone:    true,
			shouldFail:      true,
			expectedErrCode: grpccodes.InvalidArgument,
			expected:        nil,
		}, {
			description:     "empty request should fail",
			req:             &pbsvck8s.ScaleRisingWaveRequest{},
			reqOneOf:        &pbsvck8s.ScaleRisingWaveRequestOneOf{},
			isStandalone:    false,
			shouldFail:      true,
			expectedErrCode: grpccodes.InvalidArgument,
			expected:        nil,
		}, {
			description: "scale single component",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:      resourceMeta,
				FrontendScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
					},
				},
			},
			isStandalone: false,
			shouldFail:   false,
			expected: func() *rwv1alpha1.RisingWaveSpec {
				s := utils.GetDummyRwSpec(false)
				s.Components.Frontend.NodeGroups[0].Replicas = 2
				return s
			}(),
		}, {
			description: "change resource requirements + replicas",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:       resourceMeta,
				FrontendScaleSpec:  &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
				MetaScaleSpec:      &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
				ComputeScaleSpec:   &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
				CompactorScaleSpec: &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec:  &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
						MetaScaleSpec:      &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
						ComputeScaleSpec:   &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
						CompactorScaleSpec: &pbrw.ScaleSpec{Resources: resourceScale, Replicas: uint32(2)},
					},
				},
			},
			isStandalone: false,
			shouldFail:   false,
			expected: func() *rwv1alpha1.RisingWaveSpec {
				s := utils.GetDummyRwSpec(false)
				s.Components.Frontend.NodeGroups[0].Replicas = 2
				s.Components.Meta.NodeGroups[0].Replicas = 2
				s.Components.Compute.NodeGroups[0].Replicas = 2
				s.Components.Compactor.NodeGroups[0].Replicas = 2
				s.Components.Frontend.NodeGroups[0].Template.Spec.Resources = resourceCorev1
				s.Components.Meta.NodeGroups[0].Template.Spec.Resources = resourceCorev1
				s.Components.Compute.NodeGroups[0].Template.Spec.Resources = resourceCorev1
				s.Components.Compactor.NodeGroups[0].Template.Spec.Resources = resourceCorev1
				return s
			}(),
		}, {
			description: "reject sending standalone and cluster components",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:        resourceMeta,
				FrontendScaleSpec:   &pbrw.ScaleSpec{Replicas: uint32(2)},
				CompactorScaleSpec:  &pbrw.ScaleSpec{Resources: resourceScale},
				StandaloneScaleSpec: &pbrw.ScaleSpec{Replicas: 1},
			},
			// not possible with new request
			// reqOneOf is nil and will be rejected with 'Please define either the cluster or standalone spec'
			isStandalone:    false,
			expectedErrCode: grpccodes.InvalidArgument,
			shouldFail:      true,
			expected:        nil,
		}, {
			description: "reject sending standalone and cluster components",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:        resourceMeta,
				FrontendScaleSpec:   &pbrw.ScaleSpec{Replicas: uint32(2)},
				CompactorScaleSpec:  &pbrw.ScaleSpec{Resources: resourceScale},
				StandaloneScaleSpec: &pbrw.ScaleSpec{Replicas: 1},
			},
			// not possible with new request
			// reqOneOf is nil and will be rejected with 'Please define either the cluster or standalone spec'
			isStandalone:    true,
			expectedErrCode: grpccodes.InvalidArgument,
			shouldFail:      true,
			expected:        nil,
		}, {
			description: "scale to 0",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:        resourceMeta,
				StandaloneScaleSpec: &pbrw.ScaleSpec{Replicas: 0},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_StandaloneSpec{
					StandaloneSpec: &pbrw.ScaleSpec{Replicas: 0},
				},
			},
			isStandalone: true,
			shouldFail:   false,
			expected: func() *rwv1alpha1.RisingWaveSpec {
				s := utils.GetDummyRwSpec(true)
				s.Components.Standalone.Replicas = 0
				return s
			}(),
		}, {
			description: "scale to 0",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:       resourceMeta,
				FrontendScaleSpec:  &pbrw.ScaleSpec{Replicas: uint32(0)},
				MetaScaleSpec:      &pbrw.ScaleSpec{Replicas: uint32(0)},
				ComputeScaleSpec:   &pbrw.ScaleSpec{Replicas: uint32(0)},
				CompactorScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(0)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec:  &pbrw.ScaleSpec{Replicas: uint32(0)},
						MetaScaleSpec:      &pbrw.ScaleSpec{Replicas: uint32(0)},
						ComputeScaleSpec:   &pbrw.ScaleSpec{Replicas: uint32(0)},
						CompactorScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(0)},
					},
				},
			},
			isStandalone: false,
			shouldFail:   false,
			expected: func() *rwv1alpha1.RisingWaveSpec {
				s := utils.GetDummyRwSpec(false)
				s.Components.Frontend.NodeGroups[0].Replicas = 0
				s.Components.Meta.NodeGroups[0].Replicas = 0
				s.Components.Compute.NodeGroups[0].Replicas = 0
				s.Components.Compactor.NodeGroups[0].Replicas = 0
				return s
			}(),
		}, {
			description: "change resource requirements",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta: resourceMeta,
				// we have to set replicas to 1, otherwise it will be defaulted to 0
				StandaloneScaleSpec: &pbrw.ScaleSpec{Resources: resourceScale, Replicas: 1},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_StandaloneSpec{
					StandaloneSpec: &pbrw.ScaleSpec{Resources: resourceScale, Replicas: 1},
				},
			},
			isStandalone: true,
			shouldFail:   false,
			expected: func() *rwv1alpha1.RisingWaveSpec {
				s := utils.GetDummyRwSpec(true)
				s.Components.Standalone.Template.Spec.Resources = resourceCorev1
				return s
			}(),
		}, {
			description: "scale standalone component",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:        resourceMeta,
				StandaloneScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_StandaloneSpec{
					StandaloneSpec: &pbrw.ScaleSpec{
						Replicas: uint32(2),
					},
				},
			},
			isStandalone:    false,
			expectedErrCode: grpccodes.Internal,
			shouldFail:      true,
			expected:        nil,
		}, {
			// we do not allow scaling standalone to > 1 replica
			description: "scale standalone",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:        resourceMeta,
				StandaloneScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_StandaloneSpec{
					StandaloneSpec: &pbrw.ScaleSpec{
						Replicas: uint32(2),
					},
				},
			},
			isStandalone:    true,
			expectedErrCode: grpccodes.Internal,
			shouldFail:      true,
			expected:        nil,
		}, {
			// currently not enabled
			// https://linear.app/risingwave-labs/issue/CLOUD-2212/[devtiercontrolplane]-support-scaling-from-dev-tier
			description: "switch standalone to cluster",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:      resourceMeta,
				FrontendScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec{
					ClusterSpec: &pbsvck8s.ClusterScaleSpec{
						FrontendScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(2)},
					},
				},
			},
			isStandalone:    true,
			expectedErrCode: grpccodes.Internal,
			shouldFail:      true,
			expected:        nil,
		}, {
			// currently not enabled
			// https://linear.app/risingwave-labs/issue/CLOUD-2212/[devtiercontrolplane]-support-scaling-from-dev-tier
			description: "switch cluster to standalone",
			req: &pbsvck8s.ScaleRisingWaveRequest{
				ResourceMeta:        resourceMeta,
				StandaloneScaleSpec: &pbrw.ScaleSpec{Replicas: uint32(1)},
			},
			reqOneOf: &pbsvck8s.ScaleRisingWaveRequestOneOf{
				ResourceMeta: resourceMeta,
				Mode: &pbsvck8s.ScaleRisingWaveRequestOneOf_StandaloneSpec{
					StandaloneSpec: &pbrw.ScaleSpec{
						Replicas: uint32(1),
					},
				},
			},
			isStandalone:    false,
			expectedErrCode: grpccodes.Internal,
			shouldFail:      true,
			expected:        nil,
		},
	}

	for _, test := range tests {
		for _, deprecatedScale := range []bool{true, false} {
			t.Run(test.description, func(t *testing.T) {
				kc := fake.NewClient()
				k8sCli := &k8s.KubernetesClient{Client: kc}
				s, err := NewService(k8sCli, "" /*endpoint*/, "" /*caCertBase64*/)
				require.NoError(t, err)

				// create the original rw instance
				rwSpec := utils.GetDummyRwSpec(test.isStandalone)
				rwSpecProto, err := conversion.ToRisingWaveSpecProto(rwSpec)
				require.NoErrorf(t, err, test.description)
				_, err = s.CreateRisingWave(context.Background(), &pbsvck8s.CreateRisingWaveRequest{
					ResourceMeta: &pbgenres.Meta{
						Id:        name,
						Namespace: ns,
					},
					RisingwaveSpec: rwSpecProto,
				})
				require.NoErrorf(t, err, test.description)

				// Call new or deprecated scale RPC.
				// response object is an empty message. We can discard it.
				if deprecatedScale {
					_, err = s.ScaleRisingWave(context.Background(), test.req)
				} else {
					_, err = s.ScaleRisingWaveOneOf(context.Background(), test.reqOneOf)
				}
				if test.shouldFail {
					assert.Errorf(t, err, test.description)
					actualCode := grpcstatus.Code(err).String()
					assert.Equal(t, test.expectedErrCode.String(), actualCode, test.description)
					return
				}
				require.NoErrorf(t, err, test.description)

				// scale request should result in expected rw spec
				rw, err := k8s.GetResource[rwv1alpha1.RisingWave](context.Background(), kc, name, ns)
				require.NoErrorf(t, err, test.description)
				require.Equalf(t, name, rw.Name, test.description)
				require.Equalf(t, ns, rw.Namespace, test.description)
				expectedJSON := getPrettyJSON(t, test.expected)
				actualJSON := getPrettyJSON(t, rw.Spec)
				mode := "cluster"
				if test.isStandalone {
					mode = "standalone"
				}
				failMsg := fmt.Sprintf("%s (%s).\n\nExpected: %s\n\nActual: %s", test.description, mode, expectedJSON, actualJSON)
				assert.Equalf(t, expectedJSON, actualJSON, failMsg)
			})
		}
	}
}

// getPrettyJSON converts any to json and requires that there is no error.
func getPrettyJSON(t *testing.T, x any) string {
	j, err := json.MarshalIndent(x, "", "  ")
	require.NoError(t, err)
	return string(j)
}

func TestUpdateRisingWaveComponents(t *testing.T) {
	resourceID := "risingwave"
	namespace := "test-namespacw"
	tests := []struct {
		name          string
		req           *pbsvck8s.UpdateRisingWaveComponentsRequest
		mockProvider  func(*providerk8s.MockProviderInterface)
		want          *pbsvck8s.UpdateRisingWaveComponentsResponse
		wantErr       bool
		wantErrorCode grpccodes.Code
	}{
		{
			name: "regular",
			req: &pbsvck8s.UpdateRisingWaveComponentsRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveComponents(context.Background(), providerk8s.UpdateRisingWaveComponentsOption{
					Namespace:  namespace,
					ResourceID: resourceID,
				}).
					Return(nil)
			},
			want: &pbsvck8s.UpdateRisingWaveComponentsResponse{},
		},
		{
			name: "illegal argument",
			req: &pbsvck8s.UpdateRisingWaveComponentsRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				ComponentsSpec: &pbrw.ComponentsSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveComponents(context.Background(), providerk8s.UpdateRisingWaveComponentsOption{
					Namespace:  namespace,
					ResourceID: resourceID,
				}).
					Return(eris.New("error").WithCode(eris.CodeInvalidArgument))
			},
			wantErr:       true,
			wantErrorCode: grpccodes.InvalidArgument,
		},
		{
			name: "not found",
			req: &pbsvck8s.UpdateRisingWaveComponentsRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				ComponentsSpec: &pbrw.ComponentsSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveComponents(context.Background(), providerk8s.UpdateRisingWaveComponentsOption{
					Namespace:  namespace,
					ResourceID: resourceID,
				}).
					Return(eris.New("error").WithCode(eris.CodeNotFound))
			},
			wantErr:       true,
			wantErrorCode: grpccodes.NotFound,
		},
		{
			name: "internal",
			req: &pbsvck8s.UpdateRisingWaveComponentsRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				ComponentsSpec: &pbrw.ComponentsSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveComponents(context.Background(), providerk8s.UpdateRisingWaveComponentsOption{
					Namespace:  namespace,
					ResourceID: resourceID,
				}).
					Return(eris.New("error"))
			},
			wantErr:       true,
			wantErrorCode: grpccodes.Internal,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			provider := providerk8s.NewMockProviderInterface(ctrl)
			if tt.mockProvider != nil {
				tt.mockProvider(provider)
			}
			s := &Service{
				provider: provider,
			}
			got, err := s.UpdateRisingWaveComponents(context.Background(), tt.req)
			if tt.wantErr {
				require.Error(t, err)
				assert.Equal(t, tt.wantErrorCode, grpcstatus.Code(err))
				return
			}
			require.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})
	}
}

func TestUpdateRisingWaveMetaStore(t *testing.T) {
	resourceID := "risingwave"
	namespace := "test-namespacw"
	tests := []struct {
		name          string
		req           *pbsvck8s.UpdateRisingWaveMetaStoreRequest
		mockProvider  func(*providerk8s.MockProviderInterface)
		want          *pbsvck8s.UpdateRisingWaveMetaStoreResponse
		wantErr       bool
		wantErrorCode grpccodes.Code
	}{
		{
			name: "regular",
			req: &pbsvck8s.UpdateRisingWaveMetaStoreRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveMetaStore(context.Background(), resourceID, namespace, &pbrw.MetaStoreSpec{}).
					Return(nil)
			},
			want: &pbsvck8s.UpdateRisingWaveMetaStoreResponse{},
		},
		{
			name: "illegal argument",
			req: &pbsvck8s.UpdateRisingWaveMetaStoreRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveMetaStore(context.Background(), resourceID, namespace, &pbrw.MetaStoreSpec{}).
					Return(eris.New("error").WithCode(eris.CodeInvalidArgument))
			},
			wantErr:       true,
			wantErrorCode: grpccodes.InvalidArgument,
		},
		{
			name: "not found",
			req: &pbsvck8s.UpdateRisingWaveMetaStoreRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveMetaStore(context.Background(), resourceID, namespace, &pbrw.MetaStoreSpec{}).
					Return(eris.New("error").WithCode(eris.CodeNotFound))
			},
			wantErr:       true,
			wantErrorCode: grpccodes.NotFound,
		},
		{
			name: "internal",
			req: &pbsvck8s.UpdateRisingWaveMetaStoreRequest{
				ResourceMeta: &pbgenres.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
				MetaStoreSpec: &pbrw.MetaStoreSpec{},
			},
			mockProvider: func(provider *providerk8s.MockProviderInterface) {
				provider.EXPECT().UpdateRisingWaveMetaStore(context.Background(), resourceID, namespace, &pbrw.MetaStoreSpec{}).
					Return(eris.New("error"))
			},
			wantErr:       true,
			wantErrorCode: grpccodes.Internal,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			provider := providerk8s.NewMockProviderInterface(ctrl)
			if tt.mockProvider != nil {
				tt.mockProvider(provider)
			}
			s := &Service{
				provider: provider,
			}

			got, err := s.UpdateRisingWaveMetaStore(context.Background(), tt.req)
			if tt.wantErr {
				require.Error(t, err)
				assert.Equal(t, tt.wantErrorCode, grpcstatus.Code(err))
				return
			}
			require.NoError(t, err)
			assert.Equal(t, tt.want, got)
		})
	}
}
