package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateNamespace(ctx context.Context, req *pbsvck8s.CreateNamespaceRequest) (*pbsvck8s.CreateNamespaceResponse, error) {
	err := s.provider.CreateNamespace(ctx, req.GetResourceMeta().GetId(), req.GetLabels())

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateNamespaceResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateNamespaceResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) GetNamespace(ctx context.Context, req *pbsvck8s.GetNamespaceRequest) (*pbsvck8s.GetNamespaceResponse, error) {
	status, err := s.provider.GetNamespace(ctx, req.GetResourceMeta().GetId())

	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.GetNamespaceResponse{
		Status: status,
	}, nil
}

func (s *Service) DeleteNamespace(ctx context.Context, req *pbsvck8s.DeleteNamespaceRequest) (*pbsvck8s.DeleteNamespaceResponse, error) {
	err := s.provider.DeleteNamespace(ctx, req.GetResourceMeta().GetId())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteNamespaceResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteNamespaceResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) LabelNamespace(ctx context.Context, req *pbsvck8s.LabelNamespaceRequest) (*pbsvck8s.LabelNamespaceResponse, error) {
	err := s.provider.LabelNamespace(ctx, req.GetResourceMeta().GetId(), req.GetLabels())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return nil, grpcstatus.Error(codes.NotFound, err.Error())
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.LabelNamespaceResponse{}, nil
}
