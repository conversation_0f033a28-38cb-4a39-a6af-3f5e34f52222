package k8s

import (
	"github.com/risingwavelabs/eris"

	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
)

type Service struct {
	pbsvck8s.UnimplementedK8SResourceManagerServer
	provider providerk8s.ProviderInterface
}

func NewService(kc *k8s.KubernetesClient, endpoint, caCertBase64 string) (*Service, error) {
	provider, err := providerk8s.NewProvider(providerk8s.NewProviderOption{
		Kc: kc,

		Endpoint:     endpoint,
		CACertBase64: caCertBase64,
	})
	if err != nil {
		return nil, eris.Wrap(err, "failed to init aws service")
	}
	return &Service{
		provider: provider,
	}, nil
}
