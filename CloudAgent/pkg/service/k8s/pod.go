package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
)

func (s *Service) GetPodPhases(ctx context.Context, req *pbsvck8s.GetPodPhasesRequest) (*pbsvck8s.GetPodPhasesResponse, error) {
	phases, err := s.provider.GetPodPhases(ctx, req.GetResourceMeta().GetId())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	protoPhases := make(map[string]pbk8s.PodPhase)
	for k, v := range phases {
		p, err := conversion.ToPodPhaseProto(v)
		if err != nil {
			return nil, grpcstatus.Error(codes.Internal, err.Error())
		}
		protoPhases[k] = p
	}
	return &pbsvck8s.GetPodPhasesResponse{
		PodToPhase: protoPhases,
	}, nil
}

func (s *Service) GetPods(ctx context.Context, req *pbsvck8s.GetPodsRequest) (*pbsvck8s.GetPodsResponse, error) {
	pods, err := s.provider.ListPods(ctx, req.GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	protoPods := []*pbk8s.Pod{}
	for _, pod := range pods {
		var p = pod
		protoPod, err := conversion.ToPodProto(&p)
		if err != nil {
			return nil, grpcstatus.Error(codes.Internal, err.Error())
		}
		protoPods = append(protoPods, protoPod)
	}
	return &pbsvck8s.GetPodsResponse{
		Pods: protoPods,
	}, nil
}
