package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbpostgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreatePostgreSql(ctx context.Context, req *pbsvck8s.CreatePostgreSqlRequest) (*pbsvck8s.CreatePostgreSqlResponse, error) { // nolint:all
	err := s.provider.CreatePostgreSQL(ctx, k8s.CreatePostgreSQLOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		Spec:       req.GetPostgresqlSpec(),
	})

	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvck8s.CreatePostgreSqlResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreatePostgreSqlResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeletePostgreSql(ctx context.Context, req *pbsvck8s.DeletePostgreSqlRequest) (*pbsvck8s.DeletePostgreSqlResponse, error) { // nolint:all
	err := s.provider.DeletePostgreSQL(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.DeletePostgreSqlResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeletePostgreSqlResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) UpdatePostgreSql(ctx context.Context, req *pbsvck8s.UpdatePostgreSqlRequest) (*pbsvck8s.UpdatePostgreSqlResponse, error) { // nolint:all
	err := s.provider.UpdatePostgreSQL(ctx, k8s.UpdatePostgreSQLOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		Spec:       req.GetPostgresqlSpec(),
	})
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.UpdatePostgreSqlResponse{
			Status: pbpostgresql.UpdateStatusCode_UPDATE_NOT_FOUND,
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvck8s.UpdatePostgreSqlResponse{
			Status: pbpostgresql.UpdateStatusCode_UPDATE_ALREADY_EXISTS,
		}, nil
	}
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdatePostgreSqlResponse{
		Status: pbpostgresql.UpdateStatusCode_UPDATE_SCHEDULED,
	}, nil
}

func (s *Service) GetPostgreSql(ctx context.Context, req *pbsvck8s.GetPostgreSqlRequest) (*pbsvck8s.GetPostgreSqlResponse, error) { // nolint:all
	m, err := s.provider.GetPostgreSQL(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.GetPostgreSqlResponse{
		Status:         m.Status,
		PostgresqlSpec: m.Spec,
		SecretRef:      m.SecretRef,
		Credentials:    m.Credentials,
	}, nil
}
