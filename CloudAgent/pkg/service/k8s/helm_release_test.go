package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
)

func TestTaskResourceMetaCorrectness(t *testing.T) {
	kc := &k8s.KubernetesClient{Client: fake.NewClient()}
	kc.TaskConfig.Image = "cannot-be-empty"
	kc.TaskConfig.PullPolicy = "cannot-be-empty"

	p, err := providerk8s.NewProvider(providerk8s.NewProviderOption{
		Kc: kc,
	})
	require.NoError(t, err)
	s := Service{
		provider: p,
	}

	var (
		helmNamespace = "helm-ns"
		helmName      = "helm-name"
		chartURL      = "test-char-url"
		valuesJSON    = "{}"
	)

	verify := func(taskName, taskNamespace string) {
		job, err := k8s.GetResource[batchv1.Job](context.Background(), kc, taskName, taskNamespace)
		require.NoError(t, err)

		assert.Equal(t, taskName, job.GetObjectMeta().GetName())
		assert.Equal(t, taskNamespace, job.GetObjectMeta().GetNamespace())
	}

	var (
		installTaskNamespace = "install-task-ns"
		installTaskName      = "install-task-name"
	)

	_, err = s.InstallHelmRelease(context.Background(), &pbsvck8s.InstallHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Namespace: installTaskNamespace,
			Id:        installTaskName,
		},
		ReleaseMeta: &pbresource.Meta{
			Namespace: helmNamespace,
			Id:        helmName,
		},
		ChartUrl:   chartURL,
		ValuesJson: valuesJSON,
	})
	require.NoError(t, err)
	verify(installTaskName, installTaskNamespace)

	var (
		upgradeTaskNamespace = "upgrade-task-ns"
		upgradeTaskName      = "upgrade-task-name"
	)

	_, err = s.UpgradeHelmRelease(context.Background(), &pbsvck8s.UpgradeHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Namespace: upgradeTaskNamespace,
			Id:        upgradeTaskName,
		},
		ReleaseMeta: &pbresource.Meta{
			Namespace: helmNamespace,
			Id:        helmName,
		},
		ChartUrl:   chartURL,
		ValuesJson: valuesJSON,
	})
	require.NoError(t, err)
	verify(upgradeTaskName, upgradeTaskNamespace)

	var (
		uninstallTaskNamespace = "uninstall-task-ns"
		uninstallTaskName      = "uninstall-task-name"
	)

	_, err = s.UninstallHelmRelease(context.Background(), &pbsvck8s.UninstallHelmReleaseRequest{
		ResourceMeta: &pbresource.Meta{
			Namespace: uninstallTaskNamespace,
			Id:        uninstallTaskName,
		},
		ReleaseMeta: &pbresource.Meta{
			Namespace: helmNamespace,
			Id:        helmName,
		},
	})
	require.NoError(t, err)
	verify(uninstallTaskName, uninstallTaskNamespace)
}
