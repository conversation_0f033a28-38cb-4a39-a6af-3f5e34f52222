package k8s

import (
	"context"

	"github.com/risingwavelabs/eris"

	"github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
)

func (s *Service) PutRisingWaveEnvVar(ctx context.Context, req *pbsvck8s.PutRisingWaveEnvRequest) (*pbsvck8s.PutRisingWaveEnvResponse, error) {
	var selectedGroups []string
	switch v := req.GetNodeGroupSelection().(type) {
	case *pbsvck8s.PutRisingWaveEnvRequest_SpecificGroups:
		selectedGroups = v.SpecificGroups.GetGroups()
	case *pbsvck8s.PutRisingWaveEnvRequest_AllGroups:
		selectedGroups = nil
	default:
		return nil, eris.New("received unknown node group type").WithCode(eris.CodeInvalidArgument)
	}

	if selectedGroups != nil && len(selectedGroups) == 0 {
		return nil, eris.New("please select at least one resource group to update").WithCode(eris.CodeInvalidArgument)
	}

	var standalone, meta, frontend, compute, compactor []*k8s.EnvVar
	switch v := req.GetComponent().(type) {
	case *pbsvck8s.PutRisingWaveEnvRequest_StandaloneEnvChange:
		standalone = v.StandaloneEnvChange.GetVars()
	case *pbsvck8s.PutRisingWaveEnvRequest_MetaEnvChange:
		meta = v.MetaEnvChange.GetVars()
	case *pbsvck8s.PutRisingWaveEnvRequest_FrontendEnvChange:
		frontend = v.FrontendEnvChange.GetVars()
	case *pbsvck8s.PutRisingWaveEnvRequest_ComputeEnvChange:
		compute = v.ComputeEnvChange.GetVars()
	case *pbsvck8s.PutRisingWaveEnvRequest_CompactorEnvChange:
		compactor = v.CompactorEnvChange.GetVars()
	default:
		return nil, eris.New("received unknown component").WithCode(eris.CodeInvalidArgument)
	}

	_, err := s.provider.PutRisingWaveEnvVar(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId(), selectedGroups,
		standalone, meta, frontend, compute, compactor)

	return &pbsvck8s.PutRisingWaveEnvResponse{}, err
}

func (s *Service) DeleteRisingWaveEnvVar(ctx context.Context, req *pbsvck8s.DeleteRisingWaveEnvRequest) (*pbsvck8s.DeleteRisingWaveEnvResponse, error) {
	var selectedGroups []string
	switch v := req.GetNodeGroupSelection().(type) {
	case *pbsvck8s.DeleteRisingWaveEnvRequest_SpecificGroups:
		selectedGroups = v.SpecificGroups.GetGroups()
	case *pbsvck8s.DeleteRisingWaveEnvRequest_AllGroups:
		selectedGroups = nil
	default:
		return nil, eris.New("received unknown node group type").WithCode(eris.CodeInvalidArgument)
	}

	if selectedGroups != nil && len(selectedGroups) == 0 {
		return nil, eris.New("please select at least one resource group to update").WithCode(eris.CodeInvalidArgument)
	}

	var standalone, meta, frontend, compute, compactor []string
	switch v := req.GetComponent().(type) {
	case *pbsvck8s.DeleteRisingWaveEnvRequest_StandaloneEnvChange:
		standalone = v.StandaloneEnvChange.GetKeys()
	case *pbsvck8s.DeleteRisingWaveEnvRequest_MetaEnvChange:
		meta = v.MetaEnvChange.GetKeys()
	case *pbsvck8s.DeleteRisingWaveEnvRequest_FrontendEnvChange:
		frontend = v.FrontendEnvChange.GetKeys()
	case *pbsvck8s.DeleteRisingWaveEnvRequest_ComputeEnvChange:
		compute = v.ComputeEnvChange.GetKeys()
	case *pbsvck8s.DeleteRisingWaveEnvRequest_CompactorEnvChange:
		compactor = v.CompactorEnvChange.GetKeys()
	default:
		return nil, eris.New("received unknown component").WithCode(eris.CodeInvalidArgument)
	}

	_, err := s.provider.DeleteRisingWaveEnvVar(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId(), selectedGroups,
		standalone, meta, frontend, compute, compactor)

	return &pbsvck8s.DeleteRisingWaveEnvResponse{}, err
}
