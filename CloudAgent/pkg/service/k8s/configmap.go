package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateConfigMap(ctx context.Context, req *pbsvck8s.CreateConfigMapRequest) (*pbsvck8s.CreateConfigMapResponse, error) {
	err := s.provider.CreateConfigMap(ctx, k8s.CreateConfigMapOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ConfigMap:  k8s.FromConfigMapProto(req.GetConfigMapSpec()),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateConfigMapResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateConfigMapResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) GetConfigMap(ctx context.Context, req *pbsvck8s.GetConfigMapRequest) (*pbsvck8s.GetConfigMapResponse, error) {
	cmMeta, err := s.provider.GetConfigMap(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	var cmSpec *pbk8s.ConfigMap
	if cmMeta.ConfigMap != nil {
		cmSpec = &pbk8s.ConfigMap{
			Immutable:  cmMeta.ConfigMap.Immutable,
			Data:       cmMeta.ConfigMap.Data,
			BinaryData: cmMeta.ConfigMap.BinaryData,
		}
	}
	return &pbsvck8s.GetConfigMapResponse{
		Status:        cmMeta.Status,
		ConfigMapSpec: cmSpec,
	}, nil
}

func (s *Service) DeleteConfigMap(ctx context.Context, req *pbsvck8s.DeleteConfigMapRequest) (*pbsvck8s.DeleteConfigMapResponse, error) {
	err := s.provider.DeleteConfigMap(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteConfigMapResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteConfigMapResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}

func (s *Service) UpdateConfigMap(ctx context.Context, req *pbsvck8s.UpdateConfigMapRequest) (*pbsvck8s.UpdateConfigMapResponse, error) {
	err := s.provider.UpdateConfigMap(ctx, k8s.UpdateConfigMapOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		To:         k8s.FromConfigMapProto(req.GetToConfigMapSpec()),
	})

	if err != nil {
		code := codes.Internal
		if utils.IsErrNotFound(err) {
			code = codes.NotFound
		}
		return nil, grpcstatus.Error(code, err.Error())
	}
	return &pbsvck8s.UpdateConfigMapResponse{}, nil
}
