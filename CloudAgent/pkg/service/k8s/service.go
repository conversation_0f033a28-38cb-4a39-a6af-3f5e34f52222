package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateService(ctx context.Context, req *pbsvck8s.CreateServiceRequest) (*pbsvck8s.CreateServiceResponse, error) {
	err := s.provider.CreateService(ctx, k8s.CreateServiceOption{
		ResourceID:  req.GetResourceMeta().GetId(),
		Namespace:   req.GetResourceMeta().GetNamespace(),
		Labels:      req.GetLabels(),
		ServiceSpec: req.GetServiceSpec(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateServiceResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateServiceResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}
