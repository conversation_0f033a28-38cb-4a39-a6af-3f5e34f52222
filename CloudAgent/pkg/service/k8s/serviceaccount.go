package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateServiceAccount(ctx context.Context, req *pbsvck8s.CreateServiceAccountRequest) (*pbsvck8s.CreateServiceAccountResponse, error) {
	err := s.provider.CreateServiceAccount(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateServiceAccountResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateServiceAccountResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) GetServiceAccount(ctx context.Context, req *pbsvck8s.GetServiceAccountRequest) (*pbsvck8s.GetServiceAccountResponse, error) {
	status, err := s.provider.GetServiceAccount(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.GetServiceAccountResponse{
		Status: status,
	}, nil
}

func (s *Service) DeleteServiceAccount(ctx context.Context, req *pbsvck8s.DeleteServiceAccountRequest) (*pbsvck8s.DeleteServiceAccountResponse, error) {
	err := s.provider.DeleteServiceAccount(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteServiceAccountResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteServiceAccountResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}

func (s *Service) AnnotateServiceAccount(ctx context.Context, req *pbsvck8s.AnnotateServiceAccountRequest) (*pbsvck8s.AnnotateServiceAccountResponse, error) {
	err := s.provider.AnnotateServiceAccount(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace(), req.GetLabels())

	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.AnnotateServiceAccountResponse{}, nil
}
