package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateSecret(ctx context.Context, req *pbsvck8s.CreateSecretRequest) (*pbsvck8s.CreateSecretResponse, error) {
	err := s.provider.CreateSecret(ctx, k8s.CreateSecretOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		Immutable:  req.GetSecretSpec().GetImmutable(),
		Data:       req.GetSecretSpec().GetData(),
		StringData: req.GetSecretSpec().GetStringData(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateSecretResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateSecretResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) GetSecret(ctx context.Context, req *pbsvck8s.GetSecretRequest) (*pbsvck8s.GetSecretResponse, error) {
	secretMeta, err := s.provider.GetSecret(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.GetSecretResponse{
		Status: secretMeta.Status,
		SecretSpec: &pbk8s.Secret{
			Immutable:  secretMeta.Secret.Immutable,
			Data:       secretMeta.Secret.Data,
			StringData: secretMeta.Secret.StringData,
			Type:       secretMeta.Secret.Type,
		},
	}, nil
}

func (s *Service) UpdateSecret(ctx context.Context, req *pbsvck8s.UpdateSecretRequest) (*pbsvck8s.UpdateSecretResponse, error) {
	err := s.provider.UpdateSecret(ctx, k8s.UpdateSecretOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		To:         k8s.FromSecretProto(req.GetSecretSpec()),
	})

	if err != nil {
		code := codes.Internal
		if utils.IsErrNotFound(err) {
			code = codes.NotFound
		}
		return nil, grpcstatus.Error(code, err.Error())
	}
	return &pbsvck8s.UpdateSecretResponse{}, nil
}

func (s *Service) DeleteSecret(ctx context.Context, req *pbsvck8s.DeleteSecretRequest) (*pbsvck8s.DeleteSecretResponse, error) {
	err := s.provider.DeleteSecret(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteSecretResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteSecretResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}
