package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
)

func (s *Service) RestartStatefulSet(ctx context.Context, req *pbsvck8s.RestartStatefulSetRequest) (*pbsvck8s.RestartStatefulSetResponse, error) {
	err := s.provider.RestartStatefulSet(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}

	return &pbsvck8s.RestartStatefulSetResponse{}, nil
}

func (s *Service) GetStatefulSetReplicasStatus(ctx context.Context, req *pbsvck8s.GetStatefulSetReplicasStatusRequest) (*pbsvck8s.GetStatefulSetReplicasStatusResponse, error) {
	status, err := s.provider.GetStatefulSetReplicaStatus(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}

	return &pbsvck8s.GetStatefulSetReplicasStatusResponse{
		Status: status,
	}, nil
}
