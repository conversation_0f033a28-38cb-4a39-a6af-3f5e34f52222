package k8s

import (
	"context"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"go.uber.org/mock/gomock"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
)

func TestDeleteServiceMonitor(t *testing.T) {
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()
	mockProvider := providerk8s.NewMockProviderInterface(ctrl)

	var (
		ctx        = context.Background()
		resourceID = "resource-ID"
		namespace  = "ns"
	)

	type testCase struct {
		err      error
		expected pbdeletion.StatusCode
	}

	var testCases = []testCase{
		{
			err:      nil,
			expected: pbdeletion.StatusCode_DELETED,
		},
		{
			err:      eris.New("").WithCode(eris.CodeNotFound),
			expected: pbdeletion.StatusCode_NOT_FOUND,
		},
	}

	for _, testCase := range testCases {
		mockProvider.EXPECT().
			DeleteServiceMonitor(ctx, resourceID, namespace).
			Return(testCase.err)

		s := Service{
			provider: mockProvider,
		}

		res, err := s.DeleteServiceMonitor(ctx, &pbsvck8s.DeleteServiceMonitorRequest{
			ResourceMeta: &pbresource.Meta{
				Namespace: namespace,
				Id:        resourceID,
			},
		})
		assert.NoError(t, err)
		assert.Equal(t, res.GetStatus().GetCode(), testCase.expected)
	}
}
