package k8s

import (
	"context"
	"encoding/json"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/helmx"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) InstallHelmRelease(ctx context.Context, req *pbsvck8s.InstallHelmReleaseRequest) (*pbsvck8s.InstallHelmReleaseResponse, error) {
	err := s.provider.InstallHelmRelease(ctx, k8s.InstallHelmReleaseOption{
		TaskID:        req.GetResourceMeta().GetId(),
		TaskNamespace: req.GetResourceMeta().GetNamespace(),
		ResourceID:    req.GetReleaseMeta().GetId(),
		Namespace:     req.GetReleaseMeta().GetNamespace(),
		ChartURL:      req.GetChartUrl(),
		ValuesJSON:    req.GetValuesJson(),
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.InstallHelmReleaseResponse{Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			}}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.InstallHelmReleaseResponse{Status: &pbcreation.Status{
		Code: pbcreation.StatusCode_CREATED,
	}}, nil
}

func (s *Service) UpgradeHelmRelease(ctx context.Context, req *pbsvck8s.UpgradeHelmReleaseRequest) (*pbsvck8s.UpgradeHelmReleaseResponse, error) {
	values := map[string]any{}
	if err := json.Unmarshal([]byte(req.GetValuesJson()), &values); err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	err := s.provider.UpgradeHelmRelease(ctx, k8s.UpgradeHelmReleaseOption{
		TaskID:        req.GetResourceMeta().GetId(),
		TaskNamespace: req.GetResourceMeta().GetNamespace(),
		ResourceID:    req.GetReleaseMeta().GetId(),
		Namespace:     req.GetReleaseMeta().GetNamespace(),
		ValuesJSON:    req.GetValuesJson(),
		ChartURL:      req.GetChartUrl(),
		Install:       req.GetInstall(),
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.UpgradeHelmReleaseResponse{Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			}}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpgradeHelmReleaseResponse{Status: &pbcreation.Status{
		Code: pbcreation.StatusCode_CREATED,
	}}, nil
}

func (s *Service) UninstallHelmRelease(ctx context.Context, req *pbsvck8s.UninstallHelmReleaseRequest) (*pbsvck8s.UninstallHelmReleaseResponse, error) {
	err := s.provider.UninstallHelmRelease(ctx, k8s.UninstallHelmReleaseOption{
		TaskID:        req.GetResourceMeta().GetId(),
		TaskNamespace: req.GetResourceMeta().GetNamespace(),
		ResourceID:    req.GetReleaseMeta().GetId(),
		Namespace:     req.GetReleaseMeta().GetNamespace(),
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.UninstallHelmReleaseResponse{Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			}}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UninstallHelmReleaseResponse{Status: &pbcreation.Status{
		Code: pbcreation.StatusCode_CREATED,
	}}, nil
}

func (s *Service) GetHelmRelease(ctx context.Context, req *pbsvck8s.GetHelmReleaseRequest) (*pbsvck8s.GetHelmReleaseResponse, error) {
	rls, err := s.provider.GetHelmRelease(ctx, req.GetReleaseMeta().GetId(), req.GetReleaseMeta().GetNamespace())
	if err != nil {
		if helmx.IsErrNotFound(err) {
			return nil, status.Error(codes.NotFound, err.Error())
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.GetHelmReleaseResponse{HelmRelease: rls}, nil
}
