package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbrw "github.com/risingwavelabs/cloudagent/pbgen/common/rw"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"

	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateRisingWave(ctx context.Context, req *pbsvck8s.CreateRisingWaveRequest) (*pbsvck8s.CreateRisingWaveResponse, error) {
	err := s.provider.CreateRisingWave(ctx, k8s.CreateRisingWaveOption{
		ResourceID:     req.GetResourceMeta().GetId(),
		Namespace:      req.GetResourceMeta().GetNamespace(),
		Labels:         req.GetLabels(),
		Annotations:    req.GetAnnotations(),
		RisingWaveSpec: req.GetRisingwaveSpec(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreateRisingWaveResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateRisingWaveResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetRisingWave(ctx context.Context, req *pbsvck8s.GetRisingWaveRequest) (*pbsvck8s.GetRisingWaveResponse, error) {
	rwMeta, err := s.provider.GetRisingWave(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.GetRisingWaveResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	statusCode := pbresource.StatusCode_NOT_READY
	if rwMeta.RWStatus.GetStatusCode() == pbrw.RisingWaveStatusCode_RW_READY {
		statusCode = pbresource.StatusCode_READY
	}
	return &pbsvck8s.GetRisingWaveResponse{
		RisingwaveSpec:   rwMeta.RisingWaveSpec,
		RisingwaveStatus: rwMeta.RWStatus,
		Status: &pbresource.Status{
			Code: statusCode,
		},
	}, nil
}

func (s *Service) DeleteRisingWave(ctx context.Context, req *pbsvck8s.DeleteRisingWaveRequest) (*pbsvck8s.DeleteRisingWaveResponse, error) {
	err := s.provider.DeleteRisingWave(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeleteRisingWaveResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteRisingWaveResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) UpdateRisingWaveImage(ctx context.Context, req *pbsvck8s.UpdateRisingWaveImageRequest) (*pbsvck8s.UpdateRisingWaveImageResponse, error) {
	err := s.provider.UpdateRisingWaveImage(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
		req.GetImageTag(),
	)

	if err != nil {
		code := codes.Internal
		if utils.IsErrNotFound(err) {
			code = codes.NotFound
		}
		return nil, grpcstatus.Error(code, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveImageResponse{}, nil
}

func (s *Service) UpdateRisingWaveLicenseKey(ctx context.Context, req *pbsvck8s.UpdateRisingWaveLicenseKeyRequest) (*pbsvck8s.UpdateRisingWaveLicenseKeyResponse, error) {
	err := s.provider.UpdateRisingWaveLicenseKey(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
		req.GetSecretName(),
	)

	if err != nil {
		code := codes.Internal
		if utils.IsErrNotFound(err) {
			code = codes.NotFound
		}
		return nil, grpcstatus.Error(code, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveLicenseKeyResponse{}, nil
}

func (s *Service) scaleRisingWaveInner(ctx context.Context, opts k8s.ScaleRisingWaveOption) (*pbsvck8s.ScaleRisingWaveResponse, error) {
	err := s.provider.ScaleRisingWave(ctx, opts)

	if err != nil && utils.IsErrNotFound(err) {
		return nil, grpcstatus.Error(codes.NotFound, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.ScaleRisingWaveResponse{}, nil
}

// reqToScaleOpts converts a scaling request to a ScaleRisingWaveOption.
func reqToScaleOpts(req *pbsvck8s.ScaleRisingWaveRequestOneOf) (k8s.ScaleRisingWaveOption, error) {
	opts := k8s.ScaleRisingWaveOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
	}
	if req == nil {
		return opts, grpcstatus.Error(codes.InvalidArgument, "request cannot be nil")
	}
	switch mode := req.GetMode().(type) {
	case *pbsvck8s.ScaleRisingWaveRequestOneOf_ClusterSpec:
		opts.MetaScaleSepc = mode.ClusterSpec.GetMetaScaleSpec()
		opts.FrontendScaleSepc = mode.ClusterSpec.GetFrontendScaleSpec()
		opts.ComputeScaleSepc = mode.ClusterSpec.GetComputeScaleSpec()
		opts.CompactorScaleSepc = mode.ClusterSpec.GetCompactorScaleSpec()
	case *pbsvck8s.ScaleRisingWaveRequestOneOf_StandaloneSpec:
		opts.StandaloneScaleSepc = mode.StandaloneSpec
	default:
		return opts, grpcstatus.Error(codes.InvalidArgument, "invalid mode. Please define either the cluster or standalone spec")
	}
	return opts, nil
}

// reqToScaleOptsDeprecated is deprecated. Please use reqToScaleOpts instead.
func reqToScaleOptsDeprecated(req *pbsvck8s.ScaleRisingWaveRequest) (k8s.ScaleRisingWaveOption, error) {
	opts := k8s.ScaleRisingWaveOption{
		ResourceID:          req.GetResourceMeta().GetId(),
		Namespace:           req.GetResourceMeta().GetNamespace(),
		MetaScaleSepc:       req.GetMetaScaleSpec(),
		FrontendScaleSepc:   req.GetFrontendScaleSpec(),
		ComputeScaleSepc:    req.GetComputeScaleSpec(),
		CompactorScaleSepc:  req.GetCompactorScaleSpec(),
		StandaloneScaleSepc: req.GetStandaloneScaleSpec(),
	}
	if req == nil {
		return opts, grpcstatus.Error(codes.InvalidArgument, "request cannot be nil")
	}
	// Need to define exactly one: Cluster- or Standalone mode
	clusterDefined := opts.CompactorScaleSepc != nil || opts.ComputeScaleSepc != nil || opts.FrontendScaleSepc != nil || opts.MetaScaleSepc != nil
	standaloneDefined := opts.StandaloneScaleSepc != nil
	if clusterDefined && standaloneDefined || !clusterDefined && !standaloneDefined {
		return k8s.ScaleRisingWaveOption{}, grpcstatus.Error(codes.InvalidArgument, "invalid mode. Please define either the cluster or standalone spec")
	}
	return opts, nil
}

// ScaleRisingWaveOneOf scales the RisingWave instance in standalone or in cluster mode.
// May be used in the future to switch between cluster and standalone mode.
// https://linear.app/risingwave-labs/issue/CLOUD-2212/[devtiercontrolplane]-support-scaling-from-dev-tier
func (s *Service) ScaleRisingWaveOneOf(ctx context.Context, req *pbsvck8s.ScaleRisingWaveRequestOneOf) (*pbsvck8s.ScaleRisingWaveResponse, error) {
	opts, err := reqToScaleOpts(req)
	if err != nil {
		return nil, err
	}
	return s.scaleRisingWaveInner(ctx, opts)
}

// ScaleRisingWave is deprecated. Please use ScaleRisingWaveOneOf instead.
func (s *Service) ScaleRisingWave(ctx context.Context, req *pbsvck8s.ScaleRisingWaveRequest) (*pbsvck8s.ScaleRisingWaveResponse, error) {
	opts, err := reqToScaleOptsDeprecated(req)
	if err != nil {
		return nil, err
	}
	return s.scaleRisingWaveInner(ctx, opts)
}

func (s *Service) StartRisingWave(ctx context.Context, req *pbsvck8s.StartRisingWaveRequest) (*pbsvck8s.StartRisingWaveResponse, error) {
	err := s.provider.StartRisingWave(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
		req.GetOverrides(),
	)
	if err != nil && utils.IsErrNotFound(err) {
		return nil, grpcstatus.Error(codes.NotFound, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.StartRisingWaveResponse{}, nil
}

func (s *Service) StopRisingWave(ctx context.Context, req *pbsvck8s.StopRisingWaveRequest) (*pbsvck8s.StopRisingWaveResponse, error) {
	err := s.provider.StopRisingWave(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
	)
	if err != nil && utils.IsErrNotFound(err) {
		return nil, grpcstatus.Error(codes.NotFound, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.StopRisingWaveResponse{}, nil
}

func (s *Service) UpdateRisingWaveComponents(ctx context.Context, req *pbsvck8s.UpdateRisingWaveComponentsRequest) (*pbsvck8s.UpdateRisingWaveComponentsResponse, error) {
	opts := k8s.UpdateRisingWaveComponentsOption{
		ResourceID:           req.GetResourceMeta().GetId(),
		Namespace:            req.GetResourceMeta().GetNamespace(),
		MetaSpec:             req.GetComponentsSpec().GetMetaSpec(),
		FrontendSpec:         req.GetComponentsSpec().GetFrontendSpec(),
		ComputeSpec:          req.GetComponentsSpec().GetComputeSpec(),
		CompactorSpec:        req.GetComponentsSpec().GetCompactorSpec(),
		StandaloneSpec:       req.GetComponentsSpec().GetStandaloneComponent(),
		EnableStandaloneMode: req.EnableStandaloneMode,
	}
	err := s.provider.UpdateRisingWaveComponents(ctx, opts)
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrNotFound(err) {
		return nil, grpcstatus.Error(codes.NotFound, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveComponentsResponse{}, nil
}

func (s *Service) UpdateRisingWaveMetaStore(ctx context.Context, req *pbsvck8s.UpdateRisingWaveMetaStoreRequest) (*pbsvck8s.UpdateRisingWaveMetaStoreResponse, error) {
	err := s.provider.UpdateRisingWaveMetaStore(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
		req.GetMetaStoreSpec(),
	)
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrNotFound(err) {
		return nil, grpcstatus.Error(codes.NotFound, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveMetaStoreResponse{}, nil
}

func (s *Service) UpdateRisingWaveSecretStore(ctx context.Context, req *pbsvck8s.UpdateRisingWaveSecretStoreRequest) (*pbsvck8s.UpdateRisingWaveSecretStoreResponse, error) {
	err := s.provider.UpdateRisingWaveSecretStore(
		ctx,
		req.GetResourceMeta().GetId(),
		req.GetResourceMeta().GetNamespace(),
		req.GetSecretName(),
		req.GetSecretKey(),
	)

	if err != nil {
		code := codes.Internal
		if utils.IsErrNotFound(err) {
			code = codes.NotFound
		}
		return nil, grpcstatus.Error(code, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveSecretStoreResponse{}, nil
}

func (s *Service) CreateRisingWaveComputeNodeGroup(ctx context.Context, req *pbsvck8s.CreateRisingWaveComputeNodeGroupRequest) (*pbsvck8s.CreateRisingWaveComputeNodeGroupResponse, error) {
	err := s.provider.CreateRisingWaveNodeGroup(ctx, k8s.CreateRisingWaveNodeGroupOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: pbrw.ComponentType_COMPUTE,
		Spec:          req.GetNodeGroup(),
	})

	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvck8s.CreateRisingWaveComputeNodeGroupResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateRisingWaveComputeNodeGroupResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) UpdateRisingWaveComputeNodeGroup(ctx context.Context, req *pbsvck8s.UpdateRisingWaveComputeNodeGroupRequest) (*pbsvck8s.UpdateRisingWaveComputeNodeGroupResponse, error) {
	err := s.provider.UpdateRisingWaveNodeGroup(ctx, k8s.UpdateRisingWaveNodeGroupOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: pbrw.ComponentType_COMPUTE,
		Spec:          req.GetNodeGroup(),
	})

	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvck8s.UpdateRisingWaveComputeNodeGroupResponse{
			Status: &pbupdate.Status{
				Code: pbupdate.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.UpdateRisingWaveComputeNodeGroupResponse{
			Status: &pbupdate.Status{
				Code: pbupdate.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveComputeNodeGroupResponse{
		Status: &pbupdate.Status{
			Code: pbupdate.StatusCode_UPDATED,
		},
	}, nil
}

func (s *Service) DeleteRisingWaveComputeNodeGroup(ctx context.Context, req *pbsvck8s.DeleteRisingWaveComputeNodeGroupRequest) (*pbsvck8s.DeleteRisingWaveComputeNodeGroupResponse, error) {
	err := s.provider.DeleteRisingWaveNodeGroup(ctx, k8s.DeleteRisingWaveNodeGroupOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: pbrw.ComponentType_COMPUTE,
		NodeGroup:     req.GetNodeGroupName(),
	})

	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.DeleteRisingWaveComputeNodeGroupResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteRisingWaveComputeNodeGroupResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}

func (s *Service) CreateRisingWaveNodeGroup(ctx context.Context, req *pbsvck8s.CreateRisingWaveNodeGroupRequest) (*pbsvck8s.CreateRisingWaveNodeGroupResponse, error) {
	err := s.provider.CreateRisingWaveNodeGroup(ctx, k8s.CreateRisingWaveNodeGroupOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: req.GetComponent(),
		Spec:          req.GetNodeGroup(),
	})

	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvck8s.CreateRisingWaveNodeGroupResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreateRisingWaveNodeGroupResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) UpdateRisingWaveNodeGroup(ctx context.Context, req *pbsvck8s.UpdateRisingWaveNodeGroupRequest) (*pbsvck8s.UpdateRisingWaveNodeGroupResponse, error) {
	err := s.provider.UpdateRisingWaveNodeGroup(ctx, k8s.UpdateRisingWaveNodeGroupOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: req.GetComponent(),
		Spec:          req.GetNodeGroup(),
	})

	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvck8s.UpdateRisingWaveNodeGroupResponse{
			Status: &pbupdate.Status{
				Code: pbupdate.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.UpdateRisingWaveNodeGroupResponse{
			Status: &pbupdate.Status{
				Code: pbupdate.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveNodeGroupResponse{
		Status: &pbupdate.Status{
			Code: pbupdate.StatusCode_UPDATED,
		},
	}, nil
}

func (s *Service) DeleteRisingWaveNodeGroup(ctx context.Context, req *pbsvck8s.DeleteRisingWaveNodeGroupRequest) (*pbsvck8s.DeleteRisingWaveNodeGroupResponse, error) {
	err := s.provider.DeleteRisingWaveNodeGroup(ctx, k8s.DeleteRisingWaveNodeGroupOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: req.GetComponent(),
		NodeGroup:     req.GetNodeGroupName(),
	})

	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.DeleteRisingWaveNodeGroupResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeleteRisingWaveNodeGroupResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_DELETED,
		},
	}, nil
}

func (s *Service) UpdateRisingWaveNodeGroupConfiguration(ctx context.Context, req *pbsvck8s.UpdateRisingWaveNodeGroupConfigurationRequest) (*pbsvck8s.UpdateRisingWaveNodeGroupConfigurationResponse, error) {
	err := s.provider.UpdateRisingWaveNodeGroupConfiguration(ctx, k8s.UpdateRisingWaveNodeGroupConfigurationOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: req.GetComponent(),
		NodeGroup:     req.GetNodeGroup(),
		Spec:          req.GetNodeConfig(),
	})

	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.UpdateRisingWaveNodeGroupConfigurationResponse{
			Status: &pbupdate.Status{
				Code: pbupdate.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil && utils.IsErrAborted(err) {
		return nil, grpcstatus.Error(codes.Aborted, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveNodeGroupConfigurationResponse{
		Status: &pbupdate.Status{
			Code: pbupdate.StatusCode_UPDATED,
		},
	}, nil
}

func (s *Service) UpdateRisingWaveNodeGroupRestartAt(ctx context.Context, req *pbsvck8s.UpdateRisingWaveNodeGroupRestartAtRequest) (*pbsvck8s.UpdateRisingWaveNodeGroupRestartAtResponse, error) {
	err := s.provider.UpdateRisingWaveNodeGroupRestartAt(ctx, k8s.UpdateRisingWaveNodeGroupRestartAtOption{
		ResourceID:    req.GetResourceMeta().GetId(),
		Namespace:     req.GetResourceMeta().GetNamespace(),
		ComponentType: req.GetComponent(),
		NodeGroup:     req.GetNodeGroup(),
		RestartAt:     req.GetRestartAt(),
	})
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvck8s.UpdateRisingWaveNodeGroupRestartAtResponse{
			Status: &pbupdate.Status{
				Code: pbupdate.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil && utils.IsErrAborted(err) {
		return nil, grpcstatus.Error(codes.Aborted, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.UpdateRisingWaveNodeGroupRestartAtResponse{
		Status: &pbupdate.Status{
			Code: pbupdate.StatusCode_UPDATED,
		},
	}, nil
}
