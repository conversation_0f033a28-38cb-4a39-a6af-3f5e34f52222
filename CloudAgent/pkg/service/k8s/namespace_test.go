package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
)

func TestLabelNamespace(t *testing.T) {
	c := fake.NewClient(&corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "name",
			Labels: map[string]string{
				"foo": "bar",
				"bar": "foo",
			},
		},
		Status: corev1.NamespaceStatus{
			Phase: corev1.NamespaceActive,
		},
	})
	p, err := providerk8s.NewProvider(providerk8s.NewProviderOption{
		Kc: &k8s.KubernetesClient{Client: c},
	})
	require.NoError(t, err)
	s := Service{
		provider: p,
	}

	ctx := context.Background()
	_, err = s.LabelNamespace(ctx, &pbsvck8s.LabelNamespaceRequest{
		ResourceMeta: &pbresource.Meta{
			Id: "name",
		},
		Labels: map[string]string{
			"foo": "baz",
			"baz": "bar",
		},
	})

	require.NoError(t, err)

	_, err = s.LabelNamespace(ctx, &pbsvck8s.LabelNamespaceRequest{
		ResourceMeta: &pbresource.Meta{
			Id: "nonexistentname",
		},
		Labels: map[string]string{
			"foo": "baz",
			"baz": "bar",
		},
	})
	assert.Equal(t, status.Code(err), codes.NotFound)
}
