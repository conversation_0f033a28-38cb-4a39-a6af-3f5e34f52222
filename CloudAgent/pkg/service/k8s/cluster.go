package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) GetClusterAccess(ctx context.Context, req *pbsvck8s.GetClusterAccessRequest) (*pbsvck8s.GetClusterAccessResponse, error) {
	access, err := s.provider.GetClusterAccess(ctx, req.GetTimeout().AsDuration())
	if err != nil {
		if utils.IsInvalidArgument(err) {
			return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}

	return &pbsvck8s.GetClusterAccessResponse{
		ClusterEndpoint:            access.Endpoint,
		ClusterCaCertificateBase64: access.CACertBase64,
		Token:                      access.Token,
	}, nil
}
