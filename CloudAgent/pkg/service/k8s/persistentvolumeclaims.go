package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) DeletePersistentVolumeClaims(ctx context.Context, req *pbsvck8s.DeletePersistentVolumeClaimsRequest) (*pbsvck8s.DeletePersistentVolumeClaimsResponse, error) {
	err := s.provider.DeletePersistentVolumeClaims(ctx, req.GetResourceMeta().GetId())

	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvck8s.DeletePersistentVolumeClaimsResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.DeletePersistentVolumeClaimsResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetPersistentVolumeClaims(ctx context.Context, req *pbsvck8s.GetPersistentVolumeClaimsRequest) (*pbsvck8s.GetPersistentVolumeClaimsResponse, error) {
	m, err := s.provider.GetPersistenVolumeClaims(ctx, req.GetResourceMeta().GetId())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.GetPersistentVolumeClaimsResponse{
		Status: m.Status,
	}, nil
}

func (s *Service) CreatePersistentVolumeClaim(ctx context.Context, req *pbsvck8s.CreatePersistentVolumeClaimRequest) (*pbsvck8s.CreatePersistentVolumeClaimResponse, error) {
	err := s.provider.CreatePersistentVolumeClaim(ctx, k8s.CreatePVCOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		Spec:       req.GetSpec(),
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvck8s.CreatePersistentVolumeClaimResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvck8s.CreatePersistentVolumeClaimResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}
