package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	networkingv1 "k8s.io/api/networking/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateNetworkPolicy(t *testing.T) {
	c := fake.NewClient()
	p, err := providerk8s.NewProvider(providerk8s.NewProviderOption{
		Kc: &k8s.KubernetesClient{Client: c},
	})
	require.NoError(t, err)
	s := Service{
		provider: p,
	}
	res, err := s.CreateNetworkPolicy(context.Background(), &pbsvck8s.CreateNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        "id",
			Namespace: "ns",
		},
		Spec: &pbk8s.NetworkPolicySpec{
			PodSelector: &pbk8s.LabelSelector{
				MatchLabels: map[string]string{"k1": "v1"},
			},
			Ingress: []*pbk8s.NetworkPolicyIngressRule{
				{
					Ports: []*pbk8s.NetworkPolicyPort{
						{
							Protocol: pbk8s.NetworkProtocol_NETWORK_PROTOCOL_TCP,
							Port: &pbk8s.IntOrString{
								IntVal: utils.Ptr(int32(8080)),
							},
							EndPort: 9090,
						},
					},
					From: []*pbk8s.NetworkPolicyPeer{
						{
							PodSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k2": "v2"},
							},
							NamespaceSelector: &pbk8s.LabelSelector{
								MatchLabels: map[string]string{"k3": "v3"},
							},
							IpBlock: &pbk8s.IPBlock{
								Cidr:   "0.0.0.0/0",
								Except: []string{"10.0.0.0"},
							},
						},
					},
				},
			},
			PolicyTypes: []pbk8s.NetworkPolicyType{
				pbk8s.NetworkPolicyType_NETWORK_POLICY_INGRESS,
				pbk8s.NetworkPolicyType_NETWORK_POLICY_EGRESS,
			},
		},
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	policy, err := k8s.GetResource[networkingv1.NetworkPolicy](context.Background(), c, "id", "ns")
	require.NoError(t, err)
	assert.Equal(t, "id", policy.Name)
	assert.Equal(t, "ns", policy.Namespace)
	assert.Equal(t, networkingv1.NetworkPolicySpec{
		PodSelector: metav1.LabelSelector{
			MatchLabels: map[string]string{"k1": "v1"},
		},
		Ingress: []networkingv1.NetworkPolicyIngressRule{
			{
				Ports: []networkingv1.NetworkPolicyPort{
					{
						Protocol: utils.Ptr(corev1.ProtocolTCP),
						Port:     utils.Ptr(intstr.FromInt(8080)),
						EndPort:  utils.Ptr(int32(9090)),
					},
				},
				From: []networkingv1.NetworkPolicyPeer{
					{
						PodSelector: &metav1.LabelSelector{
							MatchLabels: map[string]string{"k2": "v2"},
						},
						NamespaceSelector: &metav1.LabelSelector{
							MatchLabels: map[string]string{"k3": "v3"},
						},
						IPBlock: &networkingv1.IPBlock{
							CIDR:   "0.0.0.0/0",
							Except: []string{"10.0.0.0"},
						},
					},
				},
			},
		},
		PolicyTypes: []networkingv1.PolicyType{
			networkingv1.PolicyTypeIngress,
			networkingv1.PolicyTypeEgress,
		},
	}, policy.Spec)

	res, err = s.CreateNetworkPolicy(context.Background(), &pbsvck8s.CreateNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        "id",
			Namespace: "ns",
		},
		Spec: &pbk8s.NetworkPolicySpec{},
	})
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_ALREADY_EXISTS)
}

func TestDeleteNetworkPolicy(t *testing.T) {
	var (
		testID        = "testID"
		testNamespace = "testNamespace"
	)

	c := fake.NewClient(&networkingv1.NetworkPolicy{
		ObjectMeta: metav1.ObjectMeta{
			Name:      testID,
			Namespace: testNamespace,
		}})
	p, err := providerk8s.NewProvider(providerk8s.NewProviderOption{
		Kc: &k8s.KubernetesClient{Client: c},
	})
	require.NoError(t, err)
	s := Service{
		provider: p,
	}

	res, err := s.DeleteNetworkPolicy(context.Background(), &pbsvck8s.DeleteNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
	})
	assert.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_DELETED)
	res, err = s.DeleteNetworkPolicy(context.Background(), &pbsvck8s.DeleteNetworkPolicyRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
	})
	assert.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_NOT_FOUND)
}
