package k8s

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
)

func (s *Service) RestartDeployment(ctx context.Context, req *pbsvck8s.RestartDeploymentRequest) (*pbsvck8s.RestartDeploymentResponse, error) {
	err := s.provider.RestartDeployment(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}

	return &pbsvck8s.RestartDeploymentResponse{}, nil
}

func (s *Service) GetDeploymentReplicasStatus(ctx context.Context, req *pbsvck8s.GetDeploymentReplicasStatusRequest) (*pbsvck8s.GetDeploymentReplicasStatusResponse, error) {
	status, err := s.provider.GetDeploymentReplicasStatus(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}

	return &pbsvck8s.GetDeploymentReplicasStatusResponse{
		Status: status,
	}, nil
}
