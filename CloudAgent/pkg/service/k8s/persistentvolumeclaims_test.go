package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
)

func TestCreatePersistentVolumeClaim(t *testing.T) {
	c := fake.NewClient()
	p, err := providerk8s.NewProvider(providerk8s.NewProviderOption{
		Kc: &k8s.KubernetesClient{Client: c},
	})
	require.NoError(t, err)
	s := Service{
		provider: p,
	}

	ctx := context.Background()
	res, err := s.CreatePersistentVolumeClaim(ctx, &pbsvck8s.CreatePersistentVolumeClaimRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        "id",
			Namespace: "ns",
		},
		Spec: &pbk8s.PersistentVolumeClaimSpec{
			AccessModes: []pbk8s.PVCAccessMode{
				pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
			},
			Selector: &pbk8s.LabelSelector{
				MatchLabels: map[string]string{"k1": "v1"},
			},
			Resources: &pbk8s.VolumeResourceRequirements{
				StorageRequest: "20Gi",
			},
			VolumeName:       "test_volume",
			StorageClassName: "test_class",
		},
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)

	res, err = s.CreatePersistentVolumeClaim(ctx, &pbsvck8s.CreatePersistentVolumeClaimRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        "id",
			Namespace: "ns",
		},
		Spec: &pbk8s.PersistentVolumeClaimSpec{
			AccessModes: []pbk8s.PVCAccessMode{
				pbk8s.PVCAccessMode_PVC_ACCESS_MODE_READ_WRITE_ONCE,
			},
			Selector: &pbk8s.LabelSelector{
				MatchLabels: map[string]string{"k1": "v1"},
			},
			Resources: &pbk8s.VolumeResourceRequirements{
				StorageRequest: "20Gi",
			},
			VolumeName:       "test_volume",
			StorageClassName: "test_class",
		},
	})
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_ALREADY_EXISTS)
}
