package k8s

import (
	"context"
	"testing"

	"github.com/stretchr/testify/require"
	acidv1 "github.com/zalando/postgres-operator/pkg/apis/acid.zalan.do/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbpostgresql "github.com/risingwavelabs/cloudagent/pbgen/common/postgresql"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvck8s "github.com/risingwavelabs/cloudagent/pbgen/services/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	providerk8s "github.com/risingwavelabs/cloudagent/pkg/providers/k8s"
)

func getPostgresqlSpecProto() *pbpostgresql.PostgreSqlSpec {
	return &pbpostgresql.PostgreSqlSpec{
		TeamId: "test_team",
		Resources: &pbk8s.ResourceRequirements{
			CpuRequest:    "250m",
			CpuLimit:      "500m",
			MemoryRequest: "64Mi",
			MemoryLimit:   "128Mi",
		},
		NumberOfInstances: 1,
		Volume: &pbpostgresql.PostgreSqlVolume{
			Size: "10Gi",
		},
		Users: map[string]*pbpostgresql.StringArray{
			"risingwave": {Value: []string{"rw_meta"}},
		},
		Postgresql: &pbpostgresql.PostgreSqlParam{
			Version: "16",
		},
	}
}

func generateSpec(modifiers ...func(*pbpostgresql.PostgreSqlSpec)) *pbpostgresql.PostgreSqlSpec {
	proto := getPostgresqlSpecProto()
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func TestService_CreatePostgreSql(t *testing.T) {
	proto := getPostgresqlSpecProto()
	spec, err := conversion.FromPostgreSQLSpecProto(proto)
	require.NoError(t, err)
	psqlWithStatus := func(status string) *acidv1.Postgresql {
		return &acidv1.Postgresql{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "postgresql",
				Namespace: "namespace",
			},
			Spec: *spec,
			Status: acidv1.PostgresStatus{
				PostgresClusterStatus: status,
			},
		}
	}
	resourceMeta := &pbresource.Meta{
		Namespace: "namespace",
		Id:        "postgresql",
	}

	tests := []struct {
		name     string
		req      *pbsvck8s.CreatePostgreSqlRequest
		initObjs []k8sclient.Object
		want     *pbsvck8s.CreatePostgreSqlResponse
		wantErr  bool
	}{
		{
			name: "create postgresql",
			req: &pbsvck8s.CreatePostgreSqlRequest{
				ResourceMeta:   resourceMeta,
				PostgresqlSpec: proto,
			},
			want: &pbsvck8s.CreatePostgreSqlResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "already exist",
			req: &pbsvck8s.CreatePostgreSqlRequest{
				ResourceMeta:   resourceMeta,
				PostgresqlSpec: proto,
			},
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusCreating),
			},
			want: &pbsvck8s.CreatePostgreSqlResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s, err := NewService(&k8s.KubernetesClient{Client: c}, "" /*endpoint*/, "" /*caCertBase64*/)
			require.NoError(t, err)

			got, err := s.CreatePostgreSql(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_DeletePostgreSql(t *testing.T) {
	proto := getPostgresqlSpecProto()
	spec, err := conversion.FromPostgreSQLSpecProto(proto)
	require.NoError(t, err)
	psqlWithStatus := func(status string) *acidv1.Postgresql {
		return &acidv1.Postgresql{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "postgresql",
				Namespace: "namespace",
			},
			Spec: *spec,
			Status: acidv1.PostgresStatus{
				PostgresClusterStatus: status,
			},
		}
	}
	resourceMeta := &pbresource.Meta{
		Namespace: "namespace",
		Id:        "postgresql",
	}
	tests := []struct {
		name     string
		req      *pbsvck8s.DeletePostgreSqlRequest
		initObjs []k8sclient.Object
		want     *pbsvck8s.DeletePostgreSqlResponse
		wantErr  bool
	}{
		{
			name: "delete postgresql",
			req: &pbsvck8s.DeletePostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusRunning),
			},
			want: &pbsvck8s.DeletePostgreSqlResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "not found",
			req: &pbsvck8s.DeletePostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			want: &pbsvck8s.DeletePostgreSqlResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s, err := NewService(&k8s.KubernetesClient{Client: c}, "" /*endpoint*/, "" /*caCertBase64*/)
			require.NoError(t, err)

			got, err := s.DeletePostgreSql(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_UpdatePostgreSql(t *testing.T) {
	generatePsql := func(generation string) *acidv1.Postgresql {
		proto := getPostgresqlSpecProto()
		spec, err := conversion.FromPostgreSQLSpecProto(proto)
		require.NoError(t, err)
		return &acidv1.Postgresql{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "postgresql",
				Namespace: "namespace",
				Annotations: map[string]string{
					providerk8s.RwGenerationAnnotation: generation,
				},
			},
			Spec: *spec,
		}
	}
	resourceMeta := &pbresource.Meta{
		Namespace: "namespace",
		Id:        "postgresql",
	}

	tests := []struct {
		name     string
		req      *pbsvck8s.UpdatePostgreSqlRequest
		initObjs []k8sclient.Object
		want     *pbsvck8s.UpdatePostgreSqlResponse
		wantErr  bool
	}{
		{
			name: "update",
			req: &pbsvck8s.UpdatePostgreSqlRequest{
				ResourceMeta: resourceMeta,
				PostgresqlSpec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Volume = &pbpostgresql.PostgreSqlVolume{
						Size: "20Gi",
					}
				}),
			},
			initObjs: []k8sclient.Object{
				generatePsql("1"),
			},
			want: &pbsvck8s.UpdatePostgreSqlResponse{
				Status: pbpostgresql.UpdateStatusCode_UPDATE_SCHEDULED,
			},
		},
		{
			name: "already exist",
			req: &pbsvck8s.UpdatePostgreSqlRequest{
				ResourceMeta:   resourceMeta,
				PostgresqlSpec: generateSpec(),
			},
			initObjs: []k8sclient.Object{
				generatePsql("2"),
			},
			want: &pbsvck8s.UpdatePostgreSqlResponse{
				Status: pbpostgresql.UpdateStatusCode_UPDATE_ALREADY_EXISTS,
			},
		},
		{
			name: "not found",
			req: &pbsvck8s.UpdatePostgreSqlRequest{
				ResourceMeta: resourceMeta,
				PostgresqlSpec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Volume = &pbpostgresql.PostgreSqlVolume{
						Size: "20Gi",
					}
				}),
			},
			initObjs: []k8sclient.Object{},
			want: &pbsvck8s.UpdatePostgreSqlResponse{
				Status: pbpostgresql.UpdateStatusCode_UPDATE_NOT_FOUND,
			},
		},
		{
			name: "invalid argument",
			req: &pbsvck8s.UpdatePostgreSqlRequest{
				ResourceMeta: resourceMeta,
				PostgresqlSpec: generateSpec(func(spec *pbpostgresql.PostgreSqlSpec) {
					spec.Volume = &pbpostgresql.PostgreSqlVolume{
						Size: "",
					}
				}),
			},
			initObjs: []k8sclient.Object{
				generatePsql("1"),
			},
			wantErr: true,
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s, err := NewService(&k8s.KubernetesClient{Client: c}, "" /*endpoint*/, "" /*caCertBase64*/)
			require.NoError(t, err)

			got, err := s.UpdatePostgreSql(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_GetPostgreSql(t *testing.T) {
	proto := getPostgresqlSpecProto()
	spec, err := conversion.FromPostgreSQLSpecProto(proto)
	require.NoError(t, err)
	psqlWithStatus := func(status string, generation string) *acidv1.Postgresql {
		return &acidv1.Postgresql{
			ObjectMeta: metav1.ObjectMeta{
				Name:      "postgresql",
				Namespace: "namespace",
				Annotations: map[string]string{
					providerk8s.RwGenerationAnnotation: generation,
				},
			},
			Spec: *spec,
			Status: acidv1.PostgresStatus{
				PostgresClusterStatus: status,
			},
		}
	}
	secret := &v1.Secret{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "risingwave.postgresql.credentials",
			Namespace: "namespace",
		},
		Data: map[string][]byte{
			providerk8s.SecretPasswordKey: []byte("test-password"),
		},
	}
	service := &v1.Service{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "postgresql",
			Namespace: "namespace",
			Annotations: map[string]string{
				providerk8s.ObservedRwGenerationAnnotation: "1",
			},
		},
	}
	resourceMeta := &pbresource.Meta{
		Namespace: "namespace",
		Id:        "postgresql",
	}
	tests := []struct {
		name     string
		req      *pbsvck8s.GetPostgreSqlRequest
		initObjs []k8sclient.Object
		want     *pbsvck8s.GetPostgreSqlResponse
		wantErr  bool
	}{
		{
			name: "ready postgresql",
			req: &pbsvck8s.GetPostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusRunning, "1"),
				secret,
				service,
			},
			want: &pbsvck8s.GetPostgreSqlResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PostgresqlSpec: proto,
				SecretRef: &pbresource.Meta{
					Namespace: "namespace",
					Id:        "risingwave.postgresql.credentials",
				},
				Credentials: &pbpostgresql.Credentials{
					Username: providerk8s.PostgreSQLUsername,
					Password: "test-password",
				},
			},
		},
		{
			name: "creating postgresql",
			req: &pbsvck8s.GetPostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusCreating, "1"),
			},
			want: &pbsvck8s.GetPostgreSqlResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				PostgresqlSpec: proto,
			},
		},
		{
			name: "updating postgresql",
			req: &pbsvck8s.GetPostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusUpdating, "1"),
			},
			want: &pbsvck8s.GetPostgreSqlResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				PostgresqlSpec: proto,
			},
		},
		{
			name: "updating postgresql (not observed)",
			req: &pbsvck8s.GetPostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			initObjs: []k8sclient.Object{
				psqlWithStatus(acidv1.ClusterStatusRunning, "2"),
				secret,
				service,
			},
			want: &pbsvck8s.GetPostgreSqlResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
				PostgresqlSpec: proto,
			},
		},
		{
			name: "not found",
			req: &pbsvck8s.GetPostgreSqlRequest{
				ResourceMeta: resourceMeta,
			},
			initObjs: []k8sclient.Object{},
			want: &pbsvck8s.GetPostgreSqlResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s, err := NewService(&k8s.KubernetesClient{Client: c}, "" /*endpoint*/, "" /*caCertBase64*/)
			require.NoError(t, err)

			got, err := s.GetPostgreSql(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}
