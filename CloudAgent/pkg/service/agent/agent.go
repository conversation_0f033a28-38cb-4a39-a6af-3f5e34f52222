package agent

import (
	"context"

	pbsvcagent "github.com/risingwavelabs/cloudagent/pbgen/services/agent"
	"github.com/risingwavelabs/cloudagent/pkg"
)

type Service struct {
	pbsvcagent.UnimplementedAgentServer
}

func NewService() *Service {
	return &Service{}
}

func (s *Service) GetVersion(context.Context, *pbsvcagent.GetVersionRequest) (*pbsvcagent.GetVersionResponse, error) {
	return &pbsvcagent.GetVersionResponse{
		Version: pkg.CurrentVersion,
	}, nil
}
