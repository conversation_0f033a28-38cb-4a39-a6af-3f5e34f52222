package azr

import (
	"context"
	"path"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/risingwavelabs/eris"

	pbcommondata "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) GetManifest(ctx context.Context, req *pbcommondata.GetManifestRequest) (*pbcommondata.GetManifestResponse, error) {
	key := path.Join(req.GetBackupDir(), "manifest.json")
	manifest, err := s.provider.GetFile(ctx, req.GetStorageAccountName(), req.GetBucket(), key)
	if err != nil {
		if utils.IsErrNotFound(err) {
			return nil, status.Errorf(codes.NotFound, "manifest not found for %s/%s", req.GetBucket(), req.GetBackupDir())
		}
		return nil, eris.Wrapf(err, "failed to get manifest for %s/%s/%s", req.GetStorageAccountName(), req.GetBucket(), req.GetBackupDir())
	}
	return &pbcommondata.GetManifestResponse{
		ManifestJson: string(manifest),
	}, nil
}
