package azr

import (
	"context"
	"fmt"
	"testing"

	asoauthorization "github.com/Azure/azure-service-operator/v2/api/authorization/v1api20220401"
	asomanagedidentity "github.com/Azure/azure-service-operator/v2/api/managedidentity/v1api20230131"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime/conditions"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbk8s "github.com/risingwavelabs/cloudagent/pbgen/common/k8s"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreateUserAssignedIdentity(t *testing.T) {
	const (
		resourceID         = "resource"
		namespace          = "ns"
		testSubscriptionID = "sid"
		testResourceGroup  = "rg"
		testLocation       = "loc"
	)
	client := fake.NewClient()
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider:       provider,
		subscriptionID: testSubscriptionID,
		resourceGroup:  testResourceGroup,
		location:       testLocation,
	}
	ctx := context.Background()

	req := &pbsvcazr.CreateUserAssignedIdentityRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
	}
	res, err := svc.CreateUserAssignedIdentity(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)

	uai := asomanagedidentity.UserAssignedIdentity{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &uai))
	assert.Equal(t, *uai.Spec.Location, testLocation)
	assert.Equal(t, uai.Spec.Owner.ARMID, fmt.Sprintf("/subscriptions/%s/resourceGroups/%s", testSubscriptionID, testResourceGroup))

	res, err = svc.CreateUserAssignedIdentity(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_ALREADY_EXISTS)
}

func TestDeleteUserAssignedIdentity(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	uai := asomanagedidentity.UserAssignedIdentity{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&uai)
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider: provider,
	}
	ctx := context.Background()

	req := &pbsvcazr.DeleteUserAssignedIdentityRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
	}

	res, err := svc.DeleteUserAssignedIdentity(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_SCHEDULED)

	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	uai = asomanagedidentity.UserAssignedIdentity{}
	assert.True(t, k8sErrors.IsNotFound(client.Get(ctx, objKey, &uai)))

	res, err = svc.DeleteUserAssignedIdentity(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_NOT_FOUND)
}

func TestGetUserAssignedIdentity(t *testing.T) {
	const (
		resourceID  = "resource"
		namespace   = "ns"
		principalID = "pid"
		clientID    = "cid"
	)
	tests := map[string]struct {
		uai     *asomanagedidentity.UserAssignedIdentity
		res     *pbsvcazr.GetUserAssignedIdentityResponse
		errCode *eris.Code
	}{
		"Normal case, resource is ready": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.UserAssignedIdentity_STATUS{
					PrincipalId: utils.Ptr(principalID),
					ClientId:    utils.Ptr(clientID),
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
				},
			},
			res: &pbsvcazr.GetUserAssignedIdentityResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_READY,
				},
				PrincipalId: principalID,
				ClientId:    clientID,
			},
		},
		"Normal case, resource is NOT ready": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.UserAssignedIdentity_STATUS{},
			},
			res: &pbsvcazr.GetUserAssignedIdentityResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_READY,
				},
			},
		},
		"Normal case, resource is in error state": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.UserAssignedIdentity_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			res: &pbsvcazr.GetUserAssignedIdentityResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_ERROR,
				},
			},
		},
		"Normal case, resource is not found": {
			uai: &asomanagedidentity.UserAssignedIdentity{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			res: &pbsvcazr.GetUserAssignedIdentityResponse{
				Status: &pbresource.Status{
					Code: pbresource.StatusCode_NOT_FOUND,
				},
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			client := fake.NewClient(tt.uai)
			provider := azr.CreateFakeProvider(client, nil)
			const (
				resourceID = "resource"
				namespace  = "ns"
			)
			svc := &Service{
				provider: provider,
			}

			req := &pbsvcazr.GetUserAssignedIdentityRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
			}

			ctx := context.Background()
			res, err := svc.GetUserAssignedIdentity(ctx, req)
			require.NoError(t, err)
			assert.Equal(t, res.GetStatus().GetCode(), tt.res.GetStatus().GetCode())
			assert.Equal(t, res.GetPrincipalId(), tt.res.GetPrincipalId())
		})
	}
}

func TestCreateFederatedIdentityCredential(t *testing.T) {
	const (
		resourceID            = "resource"
		namespace             = "ns"
		testLocation          = "loc"
		oidcIssuer            = "oidc"
		uaiName               = "uai"
		k8sServiceAccountName = "sa"
	)
	client := fake.NewClient()
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider:   provider,
		location:   testLocation,
		oidcIssuer: oidcIssuer,
	}
	ctx := context.Background()

	req := &pbsvcazr.CreateFederatedIdentityCredentialRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
		UserAssignedIdentityName: uaiName,
		ServiceAccount: &pbk8s.ServiceAccount{
			Name:      k8sServiceAccountName,
			Namespace: namespace,
		},
	}
	res, err := svc.CreateFederatedIdentityCredential(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)

	fic := asomanagedidentity.FederatedIdentityCredential{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &fic))
	assert.Equal(t, fic.Spec.Owner.Name, uaiName)
	assert.Equal(t, fic.Spec.Audiences, []string{azr.FederatedIdentityCredentialAudience})
	assert.Equal(t, *fic.Spec.Issuer, oidcIssuer)
	assert.Equal(t, *fic.Spec.Subject, fmt.Sprintf("system:serviceaccount:%s:%s", namespace, k8sServiceAccountName))

	res, err = svc.CreateFederatedIdentityCredential(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_ALREADY_EXISTS)
}

func TestDeleteFederatedIdentityCredential(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	fic := asomanagedidentity.FederatedIdentityCredential{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&fic)
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider: provider,
	}
	ctx := context.Background()

	req := &pbsvcazr.DeleteFederatedIdentityCredentialRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
	}

	res, err := svc.DeleteFederatedIdentityCredential(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_SCHEDULED)

	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	fic = asomanagedidentity.FederatedIdentityCredential{}
	assert.True(t, k8sErrors.IsNotFound(client.Get(ctx, objKey, &fic)))

	res, err = svc.DeleteFederatedIdentityCredential(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_NOT_FOUND)
}

func TestGetFederatedIdentityCredential(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	tests := map[string]struct {
		fic     *asomanagedidentity.FederatedIdentityCredential
		status  *pbresource.Status
		errCode *eris.Code
	}{
		"Normal case, resource is ready": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.FederatedIdentityCredential_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
		},
		"Normal case, resource is NOT ready": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.FederatedIdentityCredential_STATUS{},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_READY,
			},
		},
		"Normal case, resource is in error state": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asomanagedidentity.FederatedIdentityCredential_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_ERROR,
			},
		},
		"Normal case, resource is not found": {
			fic: &asomanagedidentity.FederatedIdentityCredential{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			client := fake.NewClient(tt.fic)
			provider := azr.CreateFakeProvider(client, nil)
			const (
				resourceID = "resource"
				namespace  = "ns"
			)
			svc := &Service{
				provider: provider,
			}

			req := &pbsvcazr.GetFederatedIdentityCredentialRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
			}

			ctx := context.Background()
			res, err := svc.GetFederatedIdentityCredential(ctx, req)
			require.NoError(t, err)
			assert.Equal(t, res.GetStatus().GetCode(), tt.status.GetCode())
		})
	}
}

func TestCreateRoleAssignment(t *testing.T) {
	const (
		resourceID     = "resource"
		namespace      = "ns"
		subscriptionID = "sid"
		principleID    = "pid"
		storageAccount = "sa"
		container      = "container"
		dir1           = "dir1"
		dir2           = "dir2"
	)

	expectedOwnerARMID := "/subscriptions/sid"
	expectedPrincipalID := principleID
	expectedRoleARMID := "/subscriptions/sid/providers/Microsoft.Authorization/roleDefinitions/b7e6dc6d-f1e8-4753-8033-0f276bb0955b"
	expectedCondition := `(
	(
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/filter/action'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/tags/read'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/delete'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'} AND NOT SubOperationMatches{'Blob.List'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/write'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/add/action'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/runAsSuperUser/action'})
	)
	OR
	(
		@Resource[Microsoft.Storage/storageAccounts:name] StringEquals 'sa'
		AND
		@Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringEquals 'container'
		AND
		(
@Resource[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:path] StringStartsWith 'dir1/'
OR
@Resource[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:path] StringStartsWith 'dir2/'
		)
	)
)
AND
(
	(
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/read'} AND SubOperationMatches{'Blob.List'})
		AND
		!(ActionMatches{'Microsoft.Storage/storageAccounts/blobServices/containers/blobs/runAsSuperUser/action'})
	)
	OR
	(
		@Resource[Microsoft.Storage/storageAccounts:name] StringEquals 'sa'
		AND
		@Resource[Microsoft.Storage/storageAccounts/blobServices/containers:name] StringEquals 'container'
		AND
		(
@Request[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:prefix] StringStartsWith 'dir1/'
OR
@Request[Microsoft.Storage/storageAccounts/blobServices/containers/blobs:prefix] StringStartsWith 'dir2/'
		)
	)
)`
	client := fake.NewClient()
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider:       provider,
		subscriptionID: subscriptionID,
	}
	ctx := context.Background()

	req := &pbsvcazr.CreateRoleAssignmentRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
		PrincipalId: principleID,
		RoleAssignment: &pbsvcazr.RoleAssignment{
			AccessOption: &pbsvcazr.RoleAssignment_BlobAccessOption{
				BlobAccessOption: &pbsvcazr.BlobAccessOption{
					StorageAccount: storageAccount,
					Container:      container,
					Dirs:           []string{dir1, dir2},
				},
			},
		},
	}
	res, err := svc.CreateRoleAssignment(context.Background(), req)
	require.NoError(t, err, "expect no error for test %v")
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)

	rs := asoauthorization.RoleAssignment{}
	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	require.NoError(t, client.Get(ctx, objKey, &rs))

	assert.Equal(t, rs.Spec.Owner.ARMID, expectedOwnerARMID)
	assert.Equal(t, *rs.Spec.PrincipalId, expectedPrincipalID)
	assert.Equal(t, rs.Spec.RoleDefinitionReference.ARMID, expectedRoleARMID)
	assert.Equal(t, *rs.Spec.Condition, expectedCondition)
	assert.Equal(t, *rs.Spec.ConditionVersion, "2.0")

	res, err = svc.CreateRoleAssignment(context.Background(), req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_ALREADY_EXISTS)
}

func TestDeleteRoleAssignment(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	fic := asoauthorization.RoleAssignment{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&fic)
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider: provider,
	}
	ctx := context.Background()

	req := &pbsvcazr.DeleteRoleAssignmentRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
	}

	res, err := svc.DeleteRoleAssignment(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_SCHEDULED)

	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	fic = asoauthorization.RoleAssignment{}
	assert.True(t, k8sErrors.IsNotFound(client.Get(ctx, objKey, &fic)))

	res, err = svc.DeleteRoleAssignment(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_NOT_FOUND)
}

func TestGetRoleAssignment(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	tests := map[string]struct {
		fic     *asoauthorization.RoleAssignment
		status  *pbresource.Status
		errCode *eris.Code
	}{
		"Normal case, resource is ready": {
			fic: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asoauthorization.RoleAssignment_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
		},
		"Normal case, resource is NOT ready": {
			fic: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asoauthorization.RoleAssignment_STATUS{},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_READY,
			},
		},
		"Normal case, resource is in error state": {
			fic: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asoauthorization.RoleAssignment_STATUS{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_ERROR,
			},
		},
		"Normal case, resource is not found": {
			fic: &asoauthorization.RoleAssignment{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			client := fake.NewClient(tt.fic)
			provider := azr.CreateFakeProvider(client, nil)
			const (
				resourceID = "resource"
				namespace  = "ns"
			)
			svc := &Service{
				provider: provider,
			}

			req := &pbsvcazr.GetRoleAssignmentRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
			}

			ctx := context.Background()
			res, err := svc.GetRoleAssignment(ctx, req)
			require.NoError(t, err)
			assert.Equal(t, res.GetStatus().GetCode(), tt.status.GetCode())
		})
	}
}
