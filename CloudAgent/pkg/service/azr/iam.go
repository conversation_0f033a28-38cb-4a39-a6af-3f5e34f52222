package azr

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/risingwavelabs/eris"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateUserAssignedIdentity(ctx context.Context, req *pbsvcazr.CreateUserAssignedIdentityRequest) (*pbsvcazr.CreateUserAssignedIdentityResponse, error) {
	err := s.provider.CreateUserAssignedIdentity(ctx, azr.CreateUserAssignedIdentityOption{
		Namespace:      req.GetResourceMeta().GetNamespace(),
		ResourceID:     req.GetResourceMeta().GetId(),
		SubscriptionID: s.subscriptionID,
		ResourceGroup:  s.resourceGroup,
		Location:       s.location,
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcazr.CreateUserAssignedIdentityResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.CreateUserAssignedIdentityResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetUserAssignedIdentity(ctx context.Context, req *pbsvcazr.GetUserAssignedIdentityRequest) (*pbsvcazr.GetUserAssignedIdentityResponse, error) {
	meta, err := s.provider.GetUserAssignedIdentity(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.GetUserAssignedIdentityResponse{
		Status:      meta.Status,
		PrincipalId: meta.PrincipalID,
		ClientId:    meta.ClientID,
	}, nil
}

func (s *Service) DeleteUserAssignedIdentity(ctx context.Context, req *pbsvcazr.DeleteUserAssignedIdentityRequest) (*pbsvcazr.DeleteUserAssignedIdentityResponse, error) {
	if err := s.provider.DeleteUserAssignedIdentity(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcazr.DeleteUserAssignedIdentityResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.DeleteUserAssignedIdentityResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) CreateFederatedIdentityCredential(ctx context.Context, req *pbsvcazr.CreateFederatedIdentityCredentialRequest) (*pbsvcazr.CreateFederatedIdentityCredentialResponse, error) {
	err := s.provider.CreateFederatedIdentityCredential(ctx, azr.CreateFederatedIdentityCredentialOption{
		Namespace:                req.GetResourceMeta().GetNamespace(),
		ResourceID:               req.GetResourceMeta().GetId(),
		UserAssignedIdentityName: req.GetUserAssignedIdentityName(),
		OIDCIssuer:               s.oidcIssuer,
		ServiceAccount: azr.KubernetesServiceAccount{
			Name:      req.GetServiceAccount().GetName(),
			Namespace: req.GetServiceAccount().GetNamespace(),
		},
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcazr.CreateFederatedIdentityCredentialResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.CreateFederatedIdentityCredentialResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetFederatedIdentityCredential(ctx context.Context, req *pbsvcazr.GetFederatedIdentityCredentialRequest) (*pbsvcazr.GetFederatedIdentityCredentialResponse, error) {
	meta, err := s.provider.GetFederatedIdentityCredential(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.GetFederatedIdentityCredentialResponse{
		Status: meta.Status,
	}, nil
}

func (s *Service) DeleteFederatedIdentityCredential(ctx context.Context, req *pbsvcazr.DeleteFederatedIdentityCredentialRequest) (*pbsvcazr.DeleteFederatedIdentityCredentialResponse, error) {
	if err := s.provider.DeleteFederatedIdentityCredential(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcazr.DeleteFederatedIdentityCredentialResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.DeleteFederatedIdentityCredentialResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) CreateRoleAssignment(ctx context.Context, req *pbsvcazr.CreateRoleAssignmentRequest) (*pbsvcazr.CreateRoleAssignmentResponse, error) {
	accessOption, err := s.toProviderAccessOption(req.GetRoleAssignment())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	err = s.provider.CreateRoleAssignment(ctx, azr.CreateRoleAssignmentOption{
		Namespace:      req.GetResourceMeta().GetNamespace(),
		ResourceID:     req.GetResourceMeta().GetId(),
		SubscriptionID: s.subscriptionID,
		PrincipleID:    req.GetPrincipalId(),
		AccessOption:   accessOption,
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcazr.CreateRoleAssignmentResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.CreateRoleAssignmentResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetRoleAssignment(ctx context.Context, req *pbsvcazr.GetRoleAssignmentRequest) (*pbsvcazr.GetRoleAssignmentResponse, error) {
	meta, err := s.provider.GetRoleAssignment(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.GetRoleAssignmentResponse{
		Status: meta.Status,
	}, nil
}

func (s *Service) DeleteRoleAssignment(ctx context.Context, req *pbsvcazr.DeleteRoleAssignmentRequest) (*pbsvcazr.DeleteRoleAssignmentResponse, error) {
	if err := s.provider.DeleteRoleAssignment(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcazr.DeleteRoleAssignmentResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.DeleteRoleAssignmentResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) toProviderAccessOption(pb *pbsvcazr.RoleAssignment) (azr.AccessOption, error) {
	switch pb.GetAccessOption().(type) {
	case *pbsvcazr.RoleAssignment_BlobAccessOption:
		return azr.BlobAccessOption{
			StorageAccount: pb.GetBlobAccessOption().GetStorageAccount(),
			Container:      pb.GetBlobAccessOption().GetContainer(),
			Dirs:           pb.GetBlobAccessOption().GetDirs(),
		}, nil
	default:
		return nil, eris.Errorf("invalid option: %v", pb)
	}
}
