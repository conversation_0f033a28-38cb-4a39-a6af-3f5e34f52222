package azr

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreatePGServer(ctx context.Context, req *pbsvcazr.CreatePGServerRequest) (*pbsvcazr.CreatePGServerResponse, error) {
	err := s.provider.CreatePGServer(ctx, azr.CreatePGServerOption{
		Namespace:      req.GetResourceMeta().GetNamespace(),
		ResourceID:     req.GetResourceMeta().GetId(),
		Location:       s.location,
		Spec:           req.GetSpec(),
		SubscriptionID: s.subscriptionID,
		ResourceGroup:  s.resourceGroup,
	})
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcazr.CreatePGServerResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.CreatePGServerResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeletePGServer(ctx context.Context, req *pbsvcazr.DeletePGServerRequest) (*pbsvcazr.DeletePGServerResponse, error) {
	err := s.provider.DeletePGServer(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcazr.DeletePGServerResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.DeletePGServerResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) StartPGServer(ctx context.Context, req *pbsvcazr.StartPGServerRequest) (*pbsvcazr.StartPGServerResponse, error) {
	err := s.provider.StartPGServer(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcazr.StartPGServerResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcazr.StartPGServerResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.StartPGServerResponse{
		Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
	}, nil
}

func (s *Service) StopPGServer(ctx context.Context, req *pbsvcazr.StopPGServerRequest) (*pbsvcazr.StopPGServerResponse, error) {
	err := s.provider.StopPGServer(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcazr.StopPGServerResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcazr.StopPGServerResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.StopPGServerResponse{
		Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
	}, nil
}

func (s *Service) GetPGServer(ctx context.Context, req *pbsvcazr.GetPGServerRequest) (*pbsvcazr.GetPGServerResponse, error) {
	m, err := s.provider.GetPGServer(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.GetPGServerResponse{
		Status:      m.Status,
		DomainName:  m.DomainName,
		ServerState: m.ServerState,
	}, nil
}
