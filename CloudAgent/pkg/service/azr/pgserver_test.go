package azr

import (
	"context"
	"testing"

	azrpg "github.com/Azure/azure-sdk-for-go/sdk/resourcemanager/postgresql/armpostgresqlflexibleservers/v4"
	asodb4pg "github.com/Azure/azure-service-operator/v2/api/dbforpostgresql/v1api20221201"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbazr "github.com/risingwavelabs/cloudagent/pbgen/common/azr"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func generateSpec(modifiers ...func(spec *pbazr.PGServerSpec)) *pbazr.PGServerSpec {
	proto := &pbazr.PGServerSpec{
		AzureName: "test-pgserver",
		Owner: &pbazr.ResourceReference{
			ArmId: "/subscriptions/test-subscription/resourceGroups/test-rg",
		},
		Version:  "14",
		Location: "eastus",
		Sku: &pbazr.PGServerSku{
			Name: "Standard_B2s",
			Tier: "Burstable",
		},
		Storage: &pbazr.PGServerStorage{
			StorageSizeGb: 32,
		},
		AdministratorLogin: "testuser",
		AdministratorLoginPassword: &pbazr.SecretKeyRef{
			Key:  "password",
			Name: "test-secret",
		},
		Network: &pbazr.PGServerNetwork{
			DelegatedSubnet: &pbazr.ResourceReference{
				ArmId: "/subscriptions/test-subscription/resourceGroups/test-rg/providers/Microsoft.Network/virtualNetworks/test-vnet/subnets/test-subnet",
			},
			PrivateDnsZone: &pbazr.ResourceReference{
				ArmId: "/subscriptions/test-subscription/resourceGroups/test-rg/providers/Microsoft.Network/privateDnsZones/test-private-dns-zone",
			},
		},
		Tags: map[string]string{
			"key1": "value1",
			"key2": "value2",
		},
	}
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func genPGServer(modifiers ...func(pgServer *asodb4pg.FlexibleServer)) *asodb4pg.FlexibleServer {
	spec, err := conversion.FromPGServerSpecProto(generateSpec())
	if err != nil {
		panic(err)
	}
	pgServer := &asodb4pg.FlexibleServer{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pgserver",
			Namespace: "test-namespace",
		},
		Spec: spec,
	}
	for _, m := range modifiers {
		m(pgServer)
	}
	return pgServer
}

func TestService_CreatePGServer(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcazr.CreatePGServerRequest
		initObjs []k8sclient.Object
		want     *pbsvcazr.CreatePGServerResponse
		wantErr  bool
	}{
		{
			name: "regular",
			req: &pbsvcazr.CreatePGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
				Spec: generateSpec(),
			},
			want: &pbsvcazr.CreatePGServerResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "already exist",
			req: &pbsvcazr.CreatePGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
				Spec: generateSpec(),
			},
			initObjs: []k8sclient.Object{
				genPGServer(),
			},
			want: &pbsvcazr.CreatePGServerResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s := &Service{
				provider: azr.CreateFakeProvider(c, nil),
			}
			got, err := s.CreatePGServer(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_DeletePGServer(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcazr.DeletePGServerRequest
		initObjs []k8sclient.Object
		want     *pbsvcazr.DeletePGServerResponse
		wantErr  bool
	}{
		{
			name: "regular",
			req: &pbsvcazr.DeletePGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			},
			initObjs: []k8sclient.Object{
				genPGServer(),
			},
			want: &pbsvcazr.DeletePGServerResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "not found",
			req: &pbsvcazr.DeletePGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			},
			want: &pbsvcazr.DeletePGServerResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s := &Service{
				provider: azr.CreateFakeProvider(c, nil),
			}
			got, err := s.DeletePGServer(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_StartPGServer(t *testing.T) {
	tests := []struct {
		name               string
		req                *pbsvcazr.StartPGServerRequest
		initObjs           []k8sclient.Object
		mockPGServerClient func(client *azr.MockPGServerClient)
		want               *pbsvcazr.StartPGServerResponse
		wantErr            bool
	}{
		{
			name: "not found",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &pbsvcazr.StartPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
			},
		},
		{
			name: "already exists (starting)",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStarting),
					},
				}, nil)
			},
			want: &pbsvcazr.StartPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "already exists (ready)",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateReady),
					},
				}, nil)
			},
			want: &pbsvcazr.StartPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "scheduled",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStopped),
					},
				}, nil)
				c.EXPECT().StartPGServer(gomock.Any(), "test-pgserver").Return(nil)
			},
			want: &pbsvcazr.StartPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			pgServerClient := azr.NewMockPGServerClient(ctrl)
			p := azr.CreateFakeProvider(c, pgServerClient)
			s := Service{
				provider: p,
			}

			if tt.mockPGServerClient != nil {
				tt.mockPGServerClient(pgServerClient)
			}

			got, err := s.StartPGServer(ctx, &pbsvcazr.StartPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			})
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_StopPGServer(t *testing.T) {
	tests := []struct {
		name               string
		req                *pbsvcazr.StopPGServerRequest
		initObjs           []k8sclient.Object
		mockPGServerClient func(client *azr.MockPGServerClient)
		want               *pbsvcazr.StopPGServerResponse
		wantErr            bool
	}{
		{
			name: "not found",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &pbsvcazr.StopPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
			},
		},
		{
			name: "already exists (stopping)",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStopping),
					},
				}, nil)
			},
			want: &pbsvcazr.StopPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "already exists (stopped)",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateStopped),
					},
				}, nil)
			},
			want: &pbsvcazr.StopPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
			},
		},
		{
			name: "scheduled",
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State: utils.Ptr(azrpg.ServerStateReady),
					},
				}, nil)
				c.EXPECT().StopPGServer(gomock.Any(), "test-pgserver").Return(nil)
			},
			want: &pbsvcazr.StopPGServerResponse{
				Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			pgServerClient := azr.NewMockPGServerClient(ctrl)
			p := azr.CreateFakeProvider(c, pgServerClient)
			s := Service{
				provider: p,
			}

			if tt.mockPGServerClient != nil {
				tt.mockPGServerClient(pgServerClient)
			}

			got, err := s.StopPGServer(ctx, &pbsvcazr.StopPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			})
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_GetPGServer(t *testing.T) {
	tests := []struct {
		name               string
		req                *pbsvcazr.GetPGServerRequest
		initObjs           []k8sclient.Object
		mockPGServerClient func(client *azr.MockPGServerClient)
		want               *pbsvcazr.GetPGServerResponse
		wantErr            bool
	}{
		{
			name: "ready",
			req: &pbsvcazr.GetPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			},
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State:                    utils.Ptr(azrpg.ServerStateReady),
						FullyQualifiedDomainName: utils.Ptr("test.domain"),
					},
				}, nil)
			},
			want: &pbsvcazr.GetPGServerResponse{
				Status:      &pbresource.Status{Code: pbresource.StatusCode_READY},
				DomainName:  utils.Ptr("test.domain"),
				ServerState: pbazr.PGServerState_READY,
			},
		},
		{
			name: "not ready",
			req: &pbsvcazr.GetPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			},
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State:                    utils.Ptr(azrpg.ServerStateStarting),
						FullyQualifiedDomainName: utils.Ptr("test.domain"),
					},
				}, nil)
			},
			want: &pbsvcazr.GetPGServerResponse{
				Status:      &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				DomainName:  utils.Ptr("test.domain"),
				ServerState: pbazr.PGServerState_UPDATING,
			},
		},
		{
			name: "stopped",
			req: &pbsvcazr.GetPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			},
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(&azrpg.Server{
					Properties: &azrpg.ServerProperties{
						State:                    utils.Ptr(azrpg.ServerStateStopped),
						FullyQualifiedDomainName: utils.Ptr("test.domain"),
					},
				}, nil)
			},
			want: &pbsvcazr.GetPGServerResponse{
				Status:      &pbresource.Status{Code: pbresource.StatusCode_NOT_READY},
				DomainName:  utils.Ptr("test.domain"),
				ServerState: pbazr.PGServerState_STOPPED,
			},
		},
		{
			name: "not found",
			req: &pbsvcazr.GetPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			},
			mockPGServerClient: func(c *azr.MockPGServerClient) {
				c.EXPECT().DescribePGServer(gomock.Any(), "test-pgserver").Return(
					nil, eris.New("not found").WithCode(eris.CodeNotFound),
				)
			},
			want: &pbsvcazr.GetPGServerResponse{
				Status: &pbresource.Status{Code: pbresource.StatusCode_NOT_FOUND},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()

			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)

			pgServerClient := azr.NewMockPGServerClient(ctrl)
			p := azr.CreateFakeProvider(c, pgServerClient)
			s := Service{
				provider: p,
			}

			if tt.mockPGServerClient != nil {
				tt.mockPGServerClient(pgServerClient)
			}

			got, err := s.GetPGServer(ctx, &pbsvcazr.GetPGServerRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-pgserver",
					Namespace: "test-namespace",
				},
			})
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}
