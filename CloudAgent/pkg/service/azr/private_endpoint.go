package azr

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreatePrivateEndpoint(ctx context.Context, req *pbsvcazr.CreatePrivateEndpointRequest) (*pbsvcazr.CreatePrivateEndpointResponse, error) {
	err := s.provider.CreatePrivateEndpoint(ctx, azr.CreatePrivateEndpointOption{
		Namespace:            req.GetResourceMeta().GetNamespace(),
		ExtraTags:            req.GetExtraTags(),
		ResourceID:           req.GetResourceMeta().GetId(),
		PrivateLinkServiceID: req.GetPrivateLinkServiceId(),
		PrivateLinkSubnetID:  req.GetPrivateLinkSubnetId(),
		SubscriptionID:       s.subscriptionID,
		ResourceGroup:        s.resourceGroup,
		Location:             s.location,
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcazr.CreatePrivateEndpointResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.CreatePrivateEndpointResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeletePrivateEndpoint(ctx context.Context, req *pbsvcazr.DeletePrivateEndpointRequest) (*pbsvcazr.DeletePrivateEndpointResponse, error) {
	if err := s.provider.DeletePrivateEndpoint(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcazr.DeletePrivateEndpointResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcazr.DeletePrivateEndpointResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetPrivateEndpoint(ctx context.Context, req *pbsvcazr.GetPrivateEndpointRequest) (*pbsvcazr.GetPrivateEndpointResponse, error) {
	pe, err := s.provider.GetPrivateEndpoint(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}

	return &pbsvcazr.GetPrivateEndpointResponse{
		Status:                pe.Status,
		PrivateEndpointStatus: pe.PrivateEndpointStatus,
		PrivateEndpointIp:     pe.PrivateEndpointIP,
	}, nil
}
