package azr

import (
	"github.com/risingwavelabs/eris"

	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
)

type Service struct {
	pbsvcazr.UnimplementedAzrResourceManagerServer
	provider       *azr.Provider
	subscriptionID string
	resourceGroup  string
	location       string
	oidcIssuer     string
}

type NewServiceOption struct {
	Provider       *azr.Provider
	SubscriptionID string
	ResourceGroup  string
	Location       string
	OIDCIssuer     string
	StorageAccount string
}

func NewService(option NewServiceOption) (*Service, error) {
	if option.Provider == nil {
		return nil, eris.Errorf("AZR provider cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	return &Service{
		provider:       option.Provider,
		subscriptionID: option.SubscriptionID,
		resourceGroup:  option.ResourceGroup,
		location:       option.Location,
		oidcIssuer:     option.OIDCIssuer,
	}, nil
}
