package azr

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batch "k8s.io/api/batch/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdata "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
)

const (
	testID        = "testID"
	testNamespace = "ns"
	testBucket    = "bucket"
	testDirectory = "dir"
)

func TestCreateDataDirectoryDeletionTask(t *testing.T) {
	c := fake.NewClient()
	p := azr.CreateFakeProvider(c, nil)

	s := Service{
		provider: p,
	}

	res, err := s.CreateDataDirectoryDeletionTask(context.Background(), &pbdata.CreateDataDirectoryDeletionTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		BucketName:    testBucket,
		DirectoryName: testDirectory,
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	job, err := k8s.GetResource[batch.Job](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, job.Name)
	assert.Equal(t, job.Namespace, testNamespace)

	res, err = s.CreateDataDirectoryDeletionTask(context.Background(), &pbdata.CreateDataDirectoryDeletionTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		BucketName:    testBucket,
		DirectoryName: testDirectory,
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}

func TestCreateCloneDataDirectoryTask(t *testing.T) {
	c := fake.NewClient()
	p := azr.CreateFakeProvider(c, nil)

	s := Service{
		provider: p,
	}

	res, err := s.CreateDataDirectoryCloneTask(context.Background(), &pbdata.CreateDataDirectoryCloneTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	job, err := k8s.GetResource[batch.Job](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, job.Name)
	assert.Equal(t, job.Namespace, testNamespace)

	res, err = s.CreateDataDirectoryCloneTask(context.Background(), &pbdata.CreateDataDirectoryCloneTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
