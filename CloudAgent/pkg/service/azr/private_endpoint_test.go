package azr

import (
	"context"
	"fmt"
	"testing"

	asonetwork "github.com/Azure/azure-service-operator/v2/api/network/v1api20220701"
	"github.com/Azure/azure-service-operator/v2/pkg/genruntime/conditions"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	corev1 "k8s.io/api/core/v1"
	k8sErrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcazr "github.com/risingwavelabs/cloudagent/pbgen/services/azr"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/azr"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func TestCreatePrivateEndpoint(t *testing.T) {
	const (
		testPrivateLinkServiceID = "testIP"
		testPrivateLinkSubnetID  = "testSubnet"
		testID                   = "testID"
		subscriptionID           = "subID"
		testNamespace            = "testNamespace"
		resourceGroup            = "resourceGroup"
	)

	c := fake.NewClient()
	p := azr.CreateFakeProvider(c, nil)

	s := Service{
		provider:       p,
		subscriptionID: subscriptionID,
		resourceGroup:  resourceGroup,
	}

	res, err := s.CreatePrivateEndpoint(context.Background(), &pbsvcazr.CreatePrivateEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		PrivateLinkSubnetId:  testPrivateLinkSubnetID,
		PrivateLinkServiceId: testPrivateLinkServiceID,
		ExtraTags:            map[string]string{"envid": "env"},
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)
	privateEndpoint, err := k8s.GetResource[asonetwork.PrivateEndpoint](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, privateEndpoint.Name)
	assert.Equal(t, privateEndpoint.Namespace, testNamespace)
	assert.Equal(t, testPrivateLinkServiceID, privateEndpoint.Spec.ManualPrivateLinkServiceConnections[0].PrivateLinkServiceReference.ARMID)
	assert.Equal(t, testPrivateLinkSubnetID, privateEndpoint.Spec.Subnet.Reference.ARMID)
	assert.Equal(t, map[string]string{"project": "risingwave", "envid": "env"}, privateEndpoint.Spec.Tags)

	res, err = s.CreatePrivateEndpoint(context.Background(), &pbsvcazr.CreatePrivateEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		PrivateLinkServiceId: testPrivateLinkServiceID,
		PrivateLinkSubnetId:  testPrivateLinkSubnetID,
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
func TestDeletePrivateEndpoint(t *testing.T) {
	const (
		resourceID = "resource"
		namespace  = "ns"
	)
	privateEndpoint := asonetwork.PrivateEndpoint{
		ObjectMeta: metav1.ObjectMeta{
			Name:      resourceID,
			Namespace: namespace,
		},
	}
	client := fake.NewClient(&privateEndpoint)
	provider := azr.CreateFakeProvider(client, nil)
	svc := &Service{
		provider: provider,
	}
	ctx := context.Background()

	req := &pbsvcazr.DeletePrivateEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        resourceID,
			Namespace: namespace,
		},
	}

	res, err := svc.DeletePrivateEndpoint(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_SCHEDULED)

	objKey := k8sclient.ObjectKey{
		Name:      resourceID,
		Namespace: namespace,
	}
	privateEndpoint = asonetwork.PrivateEndpoint{}
	assert.True(t, k8sErrors.IsNotFound(client.Get(ctx, objKey, &privateEndpoint)))

	res, err = svc.DeletePrivateEndpoint(ctx, req)
	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbdeletion.StatusCode_NOT_FOUND)
}

func TestGetPrivateEndpoint(t *testing.T) {
	const (
		resourceID            = "resource"
		namespace             = "ns"
		nicID                 = "nic"
		testPrivateEndpointIP = "10.0.0.0"
	)
	tests := map[string]struct {
		privateEndpoint *asonetwork.PrivateEndpoint
		configMap       *corev1.ConfigMap
		status          *pbresource.Status
		errCode         *eris.Code
	}{
		"Normal case, resource is ready": {
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{
					Conditions: []conditions.Condition{
						{
							Type:   conditions.ConditionTypeReady,
							Status: metav1.ConditionTrue,
						},
					},
					ProvisioningState: utils.Ptr(asonetwork.ApplicationGatewayProvisioningState_STATUS_Succeeded),
					NetworkInterfaces: []asonetwork.NetworkInterface_STATUS_PrivateEndpoint_SubResourceEmbedded{
						{
							Id: utils.Ptr(nicID),
						},
					},
				},
			},
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      fmt.Sprintf("peip-%s", resourceID),
				},
				Data: map[string]string{
					"ip": testPrivateEndpointIP,
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_READY,
			},
		},
		"Normal case, resource is NOT ready": {
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{},
			},
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      fmt.Sprintf("peip-%s", resourceID),
				},
				Data: map[string]string{
					"ip": testPrivateEndpointIP,
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_READY,
			},
		},
		"Normal case, resource is in error state": {
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      resourceID,
				},
				Status: asonetwork.PrivateEndpoint_STATUS_PrivateEndpoint_SubResourceEmbedded{
					Conditions: []conditions.Condition{
						{
							Type:     conditions.ConditionTypeReady,
							Status:   metav1.ConditionFalse,
							Severity: conditions.ConditionSeverityError,
						},
					},
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_ERROR,
			},
		},
		"Normal case, resource is not found": {
			privateEndpoint: &asonetwork.PrivateEndpoint{
				ObjectMeta: metav1.ObjectMeta{
					Namespace: namespace,
					Name:      "random resource",
				},
			},
			status: &pbresource.Status{
				Code: pbresource.StatusCode_NOT_FOUND,
			},
		},
	}

	for name, tt := range tests {
		t.Run(name, func(t *testing.T) {
			objs := []k8sclient.Object{tt.privateEndpoint}
			if tt.configMap != nil {
				objs = append(objs, tt.configMap)
			}
			client := fake.NewClient(objs...)
			provider := azr.CreateFakeProvider(client, nil)
			const (
				resourceID = "resource"
				namespace  = "ns"
			)
			svc := &Service{
				provider: provider,
			}

			req := &pbsvcazr.GetPrivateEndpointRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        resourceID,
					Namespace: namespace,
				},
			}

			ctx := context.Background()
			res, err := svc.GetPrivateEndpoint(ctx, req)
			require.NoError(t, err)
			assert.Equal(t, res.GetStatus().GetCode(), tt.status.GetCode())
		})
	}
}
