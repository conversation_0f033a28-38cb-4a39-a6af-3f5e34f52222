package telemetry

import (
	"context"
	"fmt"
	"net/http"

	"github.com/risingwavelabs/eris"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbtelemetrycfg "github.com/risingwavelabs/cloudagent/pbgen/config/telemetry"
	pbsvcprom "github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus"
	"github.com/risingwavelabs/cloudagent/pkg/providers/telemetry"
)

type PrometheusService struct {
	pbsvcprom.UnimplementedPrometheusServer
	provider telemetry.PrometheusProvider
	puller   telemetry.MetricsPuller

	cfg *pbcfg.Config
}

type NewPrometheusServiceOption struct {
	Provider telemetry.PrometheusProvider
	Puller   telemetry.MetricsPuller
	Config   *pbcfg.Config
}

func NewPrometheusService(option NewPrometheusServiceOption) (*PrometheusService, error) {
	if option.Puller == nil {
		return nil, eris.Errorf("Metrics puller cannot be nil").WithCode(eris.CodeInvalidArgument)
	}

	return &PrometheusService{
		provider: option.Provider,
		puller:   option.Puller,
		cfg:      option.Config,
	}, nil
}

func (s *PrometheusService) Proxy(ctx context.Context, req *pbsvcprom.ProxyRequest) (*pbsvcprom.ProxyResponse, error) {
	if s.provider == nil {
		return nil, eris.Errorf("Prometheus provider doesn't support proxy function").WithCode(eris.CodeUnimplemented)
	}

	baseURL, err := getPrometheusBaseURL(s.cfg)
	if err != nil {
		return nil, eris.Wrap(err, "failed to construct Prometheus base URL")
	}

	httpMethod, err := toHTTPMethod(req.GetHttpMethod())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	endpoint, err := toPromEndpoint(req.GetEndpoint(), req.GetEndpointParams())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}
	result, err := s.provider.ProxyPrometheus(ctx, telemetry.ProxyPrometheusOption{
		Method:   httpMethod,
		Endpoint: endpoint,
		Payload:  req.GetPayload(),
		BaseURL:  baseURL,
	})
	if err != nil {
		code := eris.GetCode(err).ToGrpc()
		if code == codes.Unknown {
			code = codes.Internal
		}
		return nil, status.Error(code, err.Error())
	}
	return &pbsvcprom.ProxyResponse{
		StatusCode: uint32(result.Code),
		Payload:    result.Payload,
	}, nil
}

func (s *PrometheusService) Scrape(ctx context.Context, req *pbsvcprom.ScrapeRequest) (*pbsvcprom.ScrapeResponse, error) {
	result, err := s.puller.Scrape(ctx, telemetry.ScrapeRequest{
		Namespace:            req.GetNamespace(),
		AcceptEncodingHeader: req.GetAcceptEncodingHeader(),
		Include:              req.GetInclude(),
		Exclude:              req.GetExclude(),
	})
	if err != nil {
		code := eris.GetCode(err).ToGrpc()
		if code == codes.Unknown {
			code = codes.Internal
		}
		return nil, status.Error(code, err.Error())
	}
	resp := &pbsvcprom.ScrapeResponse{
		Payload:               result.Payload,
		ContentTypeHeader:     result.ContentTypeHeader,
		ContentEncodingHeader: result.ContentEncodingHeader,
	}

	respSize := proto.Size(resp)
	if req.GetMaxResponseSize() > 0 && respSize > int(req.GetMaxResponseSize()) {
		return nil, status.Errorf(codes.FailedPrecondition, "response size exceeds the specified max size, max is %d, got %d", req.GetMaxResponseSize(), respSize)
	}
	return resp, nil
}

func toHTTPMethod(m pbsvcprom.HTTPMethod) (string, error) {
	switch m {
	case pbsvcprom.HTTPMethod_GET:
		return http.MethodGet, nil
	case pbsvcprom.HTTPMethod_POST:
		return http.MethodPost, nil
	case pbsvcprom.HTTPMethod_METHOD_UNSPECIFIED:
		return "", eris.Errorf("unsupported http method %v", m)
	}
	return "", eris.New("unreachable")
}

func toPromEndpoint(ep pbsvcprom.Endpoint, epParams *pbsvcprom.EndpointParams) (string, error) {
	switch ep {
	case pbsvcprom.Endpoint_QUERY:
		return "/api/v1/query", nil
	case pbsvcprom.Endpoint_QUERY_RANGE:
		return "/api/v1/query_range", nil
	case pbsvcprom.Endpoint_LABELS:
		return "/api/v1/labels", nil
	case pbsvcprom.Endpoint_SERIES:
		return "/api/v1/series", nil
	case pbsvcprom.Endpoint_LABEL_VALUES:
		switch epParams.GetParams().(type) {
		case *pbsvcprom.EndpointParams_LabelValuesParams:
			return fmt.Sprintf("/api/v1/label/%s/values", epParams.GetLabelValuesParams().GetLabelName()), nil
		default:
			return "", eris.Errorf("missing label values endpoint params %v", epParams)
		}
	case pbsvcprom.Endpoint_ENDPOINT_UNSPECIFIED:
		return "", eris.Errorf("unsupported endpoint %v", ep)
	}
	return "", eris.New("unreachable")
}

func getPrometheusBaseURL(cfg *pbcfg.Config) (string, error) {
	metricsCfg := cfg.GetTelemetryConfig().GetMetricsConfig()
	switch metricsCfg.(type) {
	case *pbtelemetrycfg.Config_AmpConfig:
		if cfg.GetAwsConfig() == nil {
			return "", eris.New("missing AWS config for AMP metrics service").WithCode(eris.CodeInvalidArgument)
		}
		return getAMPBaseURL(cfg.GetAwsConfig().GetRegion(), cfg.GetTelemetryConfig().GetAmpConfig().GetWorkspaceId()), nil
	case *pbtelemetrycfg.Config_GmpConfig:
		if cfg.GetGcpConfig() == nil {
			return "", eris.New("missing GCP config for GMP metrics service").WithCode(eris.CodeInvalidArgument)
		}
		return getGMPBaseURL(cfg.GetGcpConfig().GetProjectId()), nil
	case *pbtelemetrycfg.Config_AzmpConfig:
		if cfg.GetAzrConfig() == nil {
			return "", eris.New("missing AZR config for AZP metrics service").WithCode(eris.CodeInvalidArgument)
		}
		return cfg.GetTelemetryConfig().GetAzmpConfig().GetQueryEndpoint(), nil
	case *pbtelemetrycfg.Config_LocalPrometheusConfig:
		return cfg.GetTelemetryConfig().GetLocalPrometheusConfig().GetUrl(), nil
	default:
		return "", eris.Errorf("invalid metrics config type: %v", metricsCfg).WithCode(eris.CodeInvalidArgument)
	}
}

func getGMPBaseURL(projectID string) string {
	return fmt.Sprintf("https://monitoring.googleapis.com/v1/projects/%s/location/global/prometheus", projectID)
}

func getAMPBaseURL(region, workspaceID string) string {
	return fmt.Sprintf("https://aps-workspaces.%s.amazonaws.com/workspaces/%s", region, workspaceID)
}
