package telemetry

import (
	"context"
	"fmt"
	"net/http"
	"strings"
	"testing"

	"github.com/jinzhu/copier"
	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"go.uber.org/mock/gomock"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcfg "github.com/risingwavelabs/cloudagent/pbgen/config"
	pbcfgaws "github.com/risingwavelabs/cloudagent/pbgen/config/aws"
	pbcfgazr "github.com/risingwavelabs/cloudagent/pbgen/config/azr"
	pbcfggcp "github.com/risingwavelabs/cloudagent/pbgen/config/gcp"
	pbcfgtelemetry "github.com/risingwavelabs/cloudagent/pbgen/config/telemetry"
	pbsvcprom "github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus"
	"github.com/risingwavelabs/cloudagent/pkg/providers/telemetry"
)

func endpointBaseURL(cloud string) string {
	switch strings.ToLower(cloud) {
	case "aws":
		return "https://aps-workspaces.us-east-1.amazonaws.com/workspaces/12345"
	case "gcp":
		return "https://monitoring.googleapis.com/v1/projects/projectid/location/global/prometheus"
	case "local":
		return "http://mimir-nginx.mimir.svc"
	case "azure":
		return "https://endpoint"
	default:
		panic("unsupported cloud provider")
	}
}

func newConfigFactory(cloud string) func() *pbcfg.Config {
	switch strings.ToLower(cloud) {
	case "aws":
		return func() *pbcfg.Config {
			return &pbcfg.Config{
				CloudProviderConfig: &pbcfg.Config_AwsConfig{
					AwsConfig: &pbcfgaws.Config{
						Region: "us-east-1",
					},
				},
				TelemetryConfig: &pbcfgtelemetry.Config{
					MetricsConfig: &pbcfgtelemetry.Config_AmpConfig{
						AmpConfig: &pbcfgtelemetry.AWSManagedPrometheusConfig{
							WorkspaceId: "12345",
						},
					},
				},
			}
		}
	case "gcp":
		return func() *pbcfg.Config {
			return &pbcfg.Config{
				CloudProviderConfig: &pbcfg.Config_GcpConfig{
					GcpConfig: &pbcfggcp.Config{
						ProjectId: "projectid",
					},
				},
				TelemetryConfig: &pbcfgtelemetry.Config{
					MetricsConfig: &pbcfgtelemetry.Config_GmpConfig{
						GmpConfig: &pbcfgtelemetry.GoogleManagedPrometheusConfig{},
					},
				},
			}
		}
	case "local":
		return func() *pbcfg.Config {
			return &pbcfg.Config{
				TelemetryConfig: &pbcfgtelemetry.Config{
					MetricsConfig: &pbcfgtelemetry.Config_LocalPrometheusConfig{
						LocalPrometheusConfig: &pbcfgtelemetry.LocalManagedPrometheusConfig{
							Url: "http://mimir-nginx.mimir.svc",
						},
					},
				},
			}
		}
	case "azure":
		return func() *pbcfg.Config {
			return &pbcfg.Config{
				CloudProviderConfig: &pbcfg.Config_AzrConfig{
					AzrConfig: &pbcfgazr.Config{},
				},
				TelemetryConfig: &pbcfgtelemetry.Config{
					MetricsConfig: &pbcfgtelemetry.Config_AzmpConfig{
						AzmpConfig: &pbcfgtelemetry.AzureManagedPrometheusConfig{
							QueryEndpoint: "https://endpoint",
						},
					},
				},
			}
		}
	default:
		panic("unsupported cloud provider")
	}
}

type mockProviderCall struct {
	request  telemetry.ProxyPrometheusOption
	response telemetry.ProxyPrometheusResult
	resErr   error
}

func newCopyWithModifiers[E any](base *E, modifiers ...func(call *E)) *E {
	var out E
	err := copier.Copy(&out, base)
	if err != nil {
		panic(err)
	}
	for _, mod := range modifiers {
		mod(&out)
	}
	return &out
}

// Meta test cases, use for test case generation later.
var metaTestCases = []struct {
	name     string
	req      *pbsvcprom.ProxyRequest
	mockCall *mockProviderCall
	res      *pbsvcprom.ProxyResponse
	resErr   error
}{
	// Instant query success
	{
		name: "/api/v1/query - success",
		req: &pbsvcprom.ProxyRequest{
			Endpoint: pbsvcprom.Endpoint_QUERY,
			Payload:  []byte("query"),
		},
		mockCall: &mockProviderCall{
			request: telemetry.ProxyPrometheusOption{
				Endpoint: "/api/v1/query",
				Payload:  []byte("query"),
			},
			response: telemetry.ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("query result"),
			},
		},
		res: &pbsvcprom.ProxyResponse{
			StatusCode: 200,
			Payload:    []byte("query result"),
		},
	},
	// Range query success
	{
		name: "/api/v1/query_range - success",
		req: &pbsvcprom.ProxyRequest{
			Endpoint: pbsvcprom.Endpoint_QUERY_RANGE,
			Payload:  []byte("query"),
		},
		mockCall: &mockProviderCall{
			request: telemetry.ProxyPrometheusOption{
				Endpoint: "/api/v1/query_range",
				Payload:  []byte("query"),
			},
			response: telemetry.ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("query result"),
			},
		},
		res: &pbsvcprom.ProxyResponse{
			StatusCode: 200,
			Payload:    []byte("query result"),
		},
	},
	// Labels query success
	{
		name: "/api/v1/labels - success",
		req: &pbsvcprom.ProxyRequest{
			Endpoint: pbsvcprom.Endpoint_LABELS,
			Payload:  []byte("query"),
		},
		mockCall: &mockProviderCall{
			request: telemetry.ProxyPrometheusOption{
				Endpoint: "/api/v1/labels",
				Payload:  []byte("query"),
			},
			response: telemetry.ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("query result"),
			},
		},
		res: &pbsvcprom.ProxyResponse{
			StatusCode: 200,
			Payload:    []byte("query result"),
		},
	},
	// Label values query success
	{
		name: "/api/v1/label/{label}/values - success",
		req: &pbsvcprom.ProxyRequest{
			Endpoint: pbsvcprom.Endpoint_LABEL_VALUES,
			Payload:  []byte("query"),
			EndpointParams: &pbsvcprom.EndpointParams{
				Params: &pbsvcprom.EndpointParams_LabelValuesParams{
					LabelValuesParams: &pbsvcprom.LabelValuesEndpointParams{
						LabelName: "label",
					},
				},
			},
		},
		mockCall: &mockProviderCall{
			request: telemetry.ProxyPrometheusOption{
				Endpoint: "/api/v1/label/label/values",
				Payload:  []byte("query"),
			},
			response: telemetry.ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("query result"),
			},
		},
		res: &pbsvcprom.ProxyResponse{
			StatusCode: 200,
			Payload:    []byte("query result"),
		},
	},
	// Series query success
	{
		name: "/api/v1/series - success",
		req: &pbsvcprom.ProxyRequest{
			Endpoint: pbsvcprom.Endpoint_SERIES,
			Payload:  []byte("query"),
		},
		mockCall: &mockProviderCall{
			request: telemetry.ProxyPrometheusOption{
				Endpoint: "/api/v1/series",
				Payload:  []byte("query"),
			},
			response: telemetry.ProxyPrometheusResult{
				Code:    200,
				Payload: []byte("query result"),
			},
		},
		res: &pbsvcprom.ProxyResponse{
			StatusCode: 200,
			Payload:    []byte("query result"),
		},
	},
}

func TestProxy(t *testing.T) {
	testcases := map[string]struct {
		config   func() *pbcfg.Config
		req      *pbsvcprom.ProxyRequest
		mockCall *mockProviderCall

		res    *pbsvcprom.ProxyResponse
		resErr error
	}{
		"missing endpoint": {
			config: newConfigFactory("gcp"),
			req: &pbsvcprom.ProxyRequest{
				HttpMethod: pbsvcprom.HTTPMethod_POST,
				Payload:    []byte("query"),
			},
			resErr: status.Error(codes.InvalidArgument, ""),
		},
		"missing method": {
			config: newConfigFactory("gcp"),
			req: &pbsvcprom.ProxyRequest{
				Endpoint: pbsvcprom.Endpoint_QUERY_RANGE,
				Payload:  []byte("query"),
			},
			resErr: status.Error(codes.InvalidArgument, ""),
		},
		"provider error": {
			config: newConfigFactory("gcp"),
			req: &pbsvcprom.ProxyRequest{
				HttpMethod: pbsvcprom.HTTPMethod_POST,
				Endpoint:   pbsvcprom.Endpoint_QUERY_RANGE,
				Payload:    []byte("query"),
			},
			mockCall: &mockProviderCall{
				request: telemetry.ProxyPrometheusOption{
					Method:   http.MethodPost,
					Endpoint: "/api/v1/query_range",
					Payload:  []byte("query"),
					BaseURL:  endpointBaseURL("gcp"),
				},
				resErr: eris.New(""),
			},
			resErr: status.Error(codes.Internal, ""),
		},
	}

	// Generate test cases from meta test cases.
	for _, meta := range metaTestCases {
		for _, cloud := range []string{"aws", "gcp", "azure", "local"} {
			for _, httpMethod := range []string{
				http.MethodGet, http.MethodPost,
			} {
				// CLOUD - HTTP METHOD - NAME
				testcaseName := fmt.Sprintf("%s_%s_%s", cloud, httpMethod, meta.name)
				testcases[testcaseName] = struct {
					config   func() *pbcfg.Config
					req      *pbsvcprom.ProxyRequest
					mockCall *mockProviderCall

					res    *pbsvcprom.ProxyResponse
					resErr error
				}{
					config: newConfigFactory(cloud),
					req: newCopyWithModifiers(meta.req, func(call *pbsvcprom.ProxyRequest) {
						switch httpMethod {
						case http.MethodGet:
							call.HttpMethod = pbsvcprom.HTTPMethod_GET
						case http.MethodPost:
							call.HttpMethod = pbsvcprom.HTTPMethod_POST
						default:
							panic("unsupported http method")
						}
					}),
					mockCall: newCopyWithModifiers(meta.mockCall, func(call *mockProviderCall) {
						call.request.Method = httpMethod
						call.request.BaseURL = endpointBaseURL(cloud)
					}),
					res:    meta.res,
					resErr: meta.resErr,
				}
			}
		}
	}

	for name, tt := range testcases {
		t.Run(name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockProvider := telemetry.NewMockPrometheusProvider(ctrl)
			svc, err := NewPrometheusService(NewPrometheusServiceOption{
				Provider: mockProvider,
				Puller:   telemetry.NewMockMetricsPuller(ctrl),
				Config:   tt.config(),
			})
			require.NoError(t, err)

			if tt.mockCall != nil {
				mockProvider.EXPECT().ProxyPrometheus(
					gomock.Any(),
					gomock.Eq(tt.mockCall.request),
				).Times(1).Return(tt.mockCall.response, tt.mockCall.resErr)
			}

			res, err := svc.Proxy(context.Background(), tt.req)
			if tt.resErr != nil {
				assert.Equal(t, status.Code(err), status.Code(tt.resErr))
				return
			}
			require.NoError(t, err)
			assert.Equal(t, res, tt.res)
		})
	}
}

func TestScrape(t *testing.T) {
	defaultConfig := &pbcfg.Config{
		CloudProviderConfig: &pbcfg.Config_AwsConfig{
			AwsConfig: &pbcfgaws.Config{
				Region: "us-east-1",
			},
		},
		TelemetryConfig: &pbcfgtelemetry.Config{
			MetricsConfig: &pbcfgtelemetry.Config_AmpConfig{
				AmpConfig: &pbcfgtelemetry.AWSManagedPrometheusConfig{
					WorkspaceId: "12345",
				},
			},
		},
	}
	type mockProviderCall struct {
		request  telemetry.ScrapeRequest
		response telemetry.ScrapeResponse
		resErr   error
	}

	testcases := map[string]struct {
		mockCall *mockProviderCall

		req *pbsvcprom.ScrapeRequest
		res *pbsvcprom.ScrapeResponse

		resErr error
	}{
		"success": {
			mockCall: &mockProviderCall{
				request: telemetry.ScrapeRequest{
					Namespace: "test-namespace",
				},
				response: telemetry.ScrapeResponse{
					Payload: []byte("test-payload"),
				},
				resErr: nil,
			},

			req: &pbsvcprom.ScrapeRequest{
				Namespace: "test-namespace",
			},
			res: &pbsvcprom.ScrapeResponse{
				Payload: []byte("test-payload"),
			},
			resErr: nil,
		},
		"within max size": {
			mockCall: &mockProviderCall{
				request: telemetry.ScrapeRequest{
					Namespace: "test-namespace",
				},
				response: telemetry.ScrapeResponse{
					Payload: []byte("test-payload"),
				},
				resErr: nil,
			},

			req: &pbsvcprom.ScrapeRequest{
				Namespace:       "test-namespace",
				MaxResponseSize: 20,
			},
			res: &pbsvcprom.ScrapeResponse{
				Payload: []byte("test-payload"),
			},
			resErr: nil,
		},
		"exceeds max response size": {
			mockCall: &mockProviderCall{
				request: telemetry.ScrapeRequest{
					Namespace: "test-namespace",
				},
				response: telemetry.ScrapeResponse{
					Payload: []byte("test-payload"),
				},
				resErr: nil,
			},

			req: &pbsvcprom.ScrapeRequest{
				Namespace:       "test-namespace",
				MaxResponseSize: 1,
			},
			resErr: status.Error(codes.FailedPrecondition, "exceeds max response size"),
		},

		"namespace_not_found": {
			mockCall: &mockProviderCall{
				request: telemetry.ScrapeRequest{
					Namespace: "not-exist-namespace",
				},
				response: telemetry.ScrapeResponse{},
				resErr:   eris.New("namespace not found").WithCode(eris.CodeNotFound),
			},
			req: &pbsvcprom.ScrapeRequest{
				Namespace: "not-exist-namespace",
			},
			res:    &pbsvcprom.ScrapeResponse{},
			resErr: status.Error(codes.NotFound, "not found"),
		},
	}

	for name, tt := range testcases {
		t.Run(name, func(t *testing.T) {
			ctrl := gomock.NewController(t)
			defer ctrl.Finish()
			mockPuller := telemetry.NewMockMetricsPuller(ctrl)
			svc, err := NewPrometheusService(NewPrometheusServiceOption{
				Provider: telemetry.NewMockPrometheusProvider(ctrl),
				Puller:   mockPuller,
				Config:   defaultConfig,
			})
			require.NoError(t, err)

			if tt.mockCall != nil {
				mockPuller.EXPECT().Scrape(
					gomock.Any(),
					gomock.Eq(tt.mockCall.request),
				).Times(1).Return(&tt.mockCall.response, tt.mockCall.resErr)
			}

			res, err := svc.Scrape(context.Background(), tt.req)
			if tt.resErr != nil {
				assert.Equal(t, status.Code(err), status.Code(tt.resErr))
				return
			}
			require.NoError(t, err)
			assert.EqualExportedValues(t, res, tt.res)
		})
	}
}
