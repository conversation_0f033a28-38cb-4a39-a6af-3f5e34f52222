package gcp

import (
	"context"
	"testing"

	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
)

const (
	testSubnet = "testSubnet"
)

func TestCreateIP(t *testing.T) {
	c := fake.NewClient()
	p := gcp.CreateFakeProvider(c)

	s := Service{
		provider:  p,
		projectID: testProjectID,
		region:    testregion,
	}

	res, err := s.CreateIPAddress(context.Background(), &pbsvcgcp.CreateIPAddressRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		IpSubnet: testSubnet,
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)
	ip, err := k8s.GetResource[gcccomputeg.ComputeAddress](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, ip.Name)
	assert.Equal(t, ip.Namespace, testNamespace)
	assert.Equal(t, ip.Spec.SubnetworkRef.External, testSubnet)

	res, err = s.CreateIPAddress(context.Background(), &pbsvcgcp.CreateIPAddressRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		IpSubnet: testSubnet,
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
