package gcp

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	batchv1 "k8s.io/api/batch/v1"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbcommondata "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
)

func TestCreateDataDirectoryDeletionTask(t *testing.T) {
	c := fake.NewClient()
	p := gcp.CreateFakeProvider(c)

	s := Service{
		provider:  p,
		projectID: testProjectID,
		region:    testregion,
	}

	res, err := s.CreateDataDirectoryDeletionTask(context.Background(), &pbcommondata.CreateDataDirectoryDeletionTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		BucketName:    "my-bucket",
		DirectoryName: "my-dir",
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	job, err := k8s.GetResource[batchv1.Job](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, job.Name)
	assert.Equal(t, job.Namespace, testNamespace)

	res, err = s.CreateDataDirectoryDeletionTask(context.Background(), &pbcommondata.CreateDataDirectoryDeletionTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		BucketName:    "my-bucket",
		DirectoryName: "my-dir",
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}

func TestCreateCloneDataDirectoryTask(t *testing.T) {
	c := fake.NewClient()
	p := gcp.CreateFakeProvider(c)

	s := Service{
		provider:  p,
		projectID: testProjectID,
		region:    testregion,
	}

	res, err := s.CreateDataDirectoryCloneTask(context.Background(), &pbcommondata.CreateDataDirectoryCloneTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_CREATED)
	job, err := k8s.GetResource[batchv1.Job](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, job.Name)
	assert.Equal(t, job.Namespace, testNamespace)

	res, err = s.CreateDataDirectoryCloneTask(context.Background(), &pbcommondata.CreateDataDirectoryCloneTaskRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		SourceDirectoryName:      "my-dir",
		SourceBucketName:         "my-bucket",
		DestinationDirectoryName: "their-dir",
		DestinationBucketName:    "their-bucket",
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
