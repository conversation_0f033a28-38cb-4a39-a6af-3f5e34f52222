package gcp

import (
	"context"
	"errors"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateIPAddress(ctx context.Context, req *pbsvcgcp.CreateIPAddressRequest) (*pbsvcgcp.CreateIPAddressResponse, error) {
	subnet := req.GetIpSubnet()
	if subnet == "" {
		err := errors.New("subnet must be set")
		return nil, status.Error(codes.Internal, err.Error())
	}

	err := s.provider.CreateIPAddress(ctx, gcp.CreateIPOption{
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ResourceID: req.GetResourceMeta().GetId(),
		ProjectID:  s.projectID,
		Region:     s.region,
		IPSubnet:   subnet,
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcgcp.CreateIPAddressResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.CreateIPAddressResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteIPAddress(ctx context.Context, req *pbsvcgcp.DeleteIPAddressRequest) (*pbsvcgcp.DeleteIPAddressResponse, error) {
	if err := s.provider.DeleteIPAddress(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcgcp.DeleteIPAddressResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.DeleteIPAddressResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetIPAddress(ctx context.Context, req *pbsvcgcp.GetIPAddressRequest) (*pbsvcgcp.GetIPAddressResponse, error) {
	m, err := s.provider.GetIPAddress(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.GetIPAddressResponse{
		Status:     m.Status,
		IpSelflink: m.Selflink,
		IpAddress:  m.IP,
	}, nil
}
