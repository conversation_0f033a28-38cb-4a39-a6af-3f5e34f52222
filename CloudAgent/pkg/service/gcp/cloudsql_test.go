package gcp

import (
	"context"
	"testing"

	gccsql "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/sql/v1beta1"
	"github.com/stretchr/testify/require"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	k8sclient "sigs.k8s.io/controller-runtime/pkg/client"

	pbgcp "github.com/risingwavelabs/cloudagent/pbgen/common/gcp"
	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/conversion"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func generateSpec(modifiers ...func(*pbgcp.SQLInstanceSpec)) *pbgcp.SQLInstanceSpec {
	proto := &pbgcp.SQLInstanceSpec{
		ResourceId:      "test-sqlinstance",
		InstanceType:    "CLOUD_SQL_INSTANCE",
		DatabaseVersion: "POSTGRES_14",
		Region:          "region1",
		RootPassword: &pbgcp.RootPassword{
			Value: utils.Ptr("password"),
		},
		Settings: &pbgcp.SQLInstanceSettings{
			Tier:     "test-tier",
			DiskSize: 20,
			IpConfiguration: &pbgcp.IPConfiguration{
				Ipv4Enabled: false,
				PrivateNetworkRef: &pbgcp.PrivateNetworkRef{
					External: "external-private-network",
				},
				AllocatedIpRange: "ip-range-name",
			},
			DeletionProtectionEnabled: false,
			DatabaseFlags: []*pbgcp.DatabaseFlag{
				{
					Name:  "flag-name",
					Value: "flag-value",
				},
			},
		},
	}
	for _, m := range modifiers {
		m(proto)
	}
	return proto
}

func genSQLInstance(modifiers ...func(sqlinstance *gccsql.SQLInstance)) *gccsql.SQLInstance {
	spec, err := conversion.FromSQLInstanceSpecProto(generateSpec())
	if err != nil {
		panic(err)
	}
	sqlinstance := &gccsql.SQLInstance{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-sqlinstance",
			Namespace: "test-namespace",
		},
		Spec: *spec,
	}
	for _, m := range modifiers {
		m(sqlinstance)
	}
	return sqlinstance
}

func TestService_CreateSQLInstance(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcgcp.CreateSQLInstanceRequest
		initObjs []k8sclient.Object
		want     *pbsvcgcp.CreateSQLInstanceResponse
		wantErr  bool
	}{
		{
			name: "regular",
			req: &pbsvcgcp.CreateSQLInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-sqlinstance",
					Namespace: "test-namespace",
				},
				Spec: generateSpec(),
				Labels: map[string]string{
					"key1": "val1",
					"key2": "val2",
				},
			},
			want: &pbsvcgcp.CreateSQLInstanceResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "already exist",
			req: &pbsvcgcp.CreateSQLInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-sqlinstance",
					Namespace: "test-namespace",
				},
				Spec: generateSpec(),
				Labels: map[string]string{
					"key1": "val1",
					"key2": "val2",
				},
			},
			initObjs: []k8sclient.Object{
				genSQLInstance(),
			},
			want: &pbsvcgcp.CreateSQLInstanceResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s := &Service{
				provider: gcp.CreateFakeProvider(c),
			}
			got, err := s.CreateSQLInstance(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}

func TestService_DeleteSQLInstance(t *testing.T) {
	tests := []struct {
		name     string
		req      *pbsvcgcp.DeleteSQLInstanceRequest
		initObjs []k8sclient.Object
		want     *pbsvcgcp.DeleteSQLInstanceResponse
		wantErr  bool
	}{
		{
			name: "regular",
			req: &pbsvcgcp.DeleteSQLInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-sqlinstance",
					Namespace: "test-namespace",
				},
			},
			initObjs: []k8sclient.Object{
				genSQLInstance(),
			},
			want: &pbsvcgcp.DeleteSQLInstanceResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_SCHEDULED,
				},
			},
		},
		{
			name: "not found",
			req: &pbsvcgcp.DeleteSQLInstanceRequest{
				ResourceMeta: &pbresource.Meta{
					Id:        "test-sqlinstance",
					Namespace: "test-namespace",
				},
			},
			want: &pbsvcgcp.DeleteSQLInstanceResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			},
		},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			ctx := context.Background()
			c := fake.NewClient(tt.initObjs...)
			s := &Service{
				provider: gcp.CreateFakeProvider(c),
			}

			got, err := s.DeleteSQLInstance(ctx, tt.req)
			if tt.wantErr {
				require.Error(t, err)
				return
			}
			require.NoError(t, err)
			require.EqualValues(t, tt.want, got)
		})
	}
}
