package gcp

import (
	"context"

	"google.golang.org/grpc/codes"
	grpcstatus "google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbupdate "github.com/risingwavelabs/cloudagent/pbgen/common/resource/update"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateSQLInstance(ctx context.Context, req *pbsvcgcp.CreateSQLInstanceRequest) (*pbsvcgcp.CreateSQLInstanceResponse, error) {
	err := s.provider.CreateSQLInstance(ctx, gcp.CreateSQLInstanceOption{
		ResourceID: req.GetResourceMeta().GetId(),
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ProjectID:  s.projectID,
		Region:     s.region,
		Spec:       req.GetSpec(),
		Labels:     req.GetLabels(),
	})

	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcgcp.CreateSQLInstanceResponse{
			Status: &pbcreation.Status{
				Code: pbcreation.StatusCode_ALREADY_EXISTS,
			},
		}, nil
	}
	if err != nil && utils.IsInvalidArgument(err) {
		return nil, grpcstatus.Error(codes.InvalidArgument, err.Error())
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.CreateSQLInstanceResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteSQLInstance(ctx context.Context, req *pbsvcgcp.DeleteSQLInstanceRequest) (*pbsvcgcp.DeleteSQLInstanceResponse, error) {
	err := s.provider.DeleteSQLInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcgcp.DeleteSQLInstanceResponse{
			Status: &pbdeletion.Status{
				Code: pbdeletion.StatusCode_NOT_FOUND,
			},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.DeleteSQLInstanceResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) StartSQLInstance(ctx context.Context, req *pbsvcgcp.StartSQLInstanceRequest) (*pbsvcgcp.StartSQLInstanceResponse, error) {
	err := s.provider.StartSQLInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcgcp.StartSQLInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcgcp.StartSQLInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.StartSQLInstanceResponse{
		Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
	}, nil
}

func (s *Service) StopSQLInstance(ctx context.Context, req *pbsvcgcp.StopSQLInstanceRequest) (*pbsvcgcp.StopSQLInstanceResponse, error) {
	err := s.provider.StopSQLInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil && utils.IsErrNotFound(err) {
		return &pbsvcgcp.StopSQLInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_NOT_FOUND},
		}, nil
	}
	if err != nil && utils.IsErrAlreadyExists(err) {
		return &pbsvcgcp.StopSQLInstanceResponse{
			Status: &pbupdate.Status{Code: pbupdate.StatusCode_ALREADY_EXISTS},
		}, nil
	}
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.StopSQLInstanceResponse{
		Status: &pbupdate.Status{Code: pbupdate.StatusCode_SCHEDULED},
	}, nil
}

func (s *Service) GetSQLInstance(ctx context.Context, req *pbsvcgcp.GetSQLInstanceRequest) (*pbsvcgcp.GetSQLInstanceResponse, error) {
	m, err := s.provider.GetSQLInstance(ctx, req.GetResourceMeta().GetId(), req.GetResourceMeta().GetNamespace())
	if err != nil {
		return nil, grpcstatus.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.GetSQLInstanceResponse{
		Status:           m.Status,
		PrivateIpAddress: m.PrivateIPAddress,
		InstanceStatus:   m.InstanceStatus,
	}, nil
}
