package gcp

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreatePrivateServiceConnectEndpoint(ctx context.Context, req *pbsvcgcp.CreatePrivateServiceConnectEndpointRequest) (*pbsvcgcp.CreatePrivateServiceConnectEndpointResponse, error) {
	err := s.provider.CreatePrivateServiceConnect(ctx, gcp.CreatePSCOption{
		Namespace:               req.GetResourceMeta().GetNamespace(),
		ExtraLabels:             req.GetExtraLabels(),
		ResourceID:              req.GetResourceMeta().GetId(),
		ProjectID:               s.projectID,
		Region:                  s.region,
		Target:                  req.GetTarget(),
		NetworkVPC:              s.tenantVPC,
		PrivateServiceConnectIP: req.GetPrivateServiceIp(),
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcgcp.CreatePrivateServiceConnectEndpointResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.CreatePrivateServiceConnectEndpointResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeletePrivateServiceConnectEndpoint(ctx context.Context, req *pbsvcgcp.DeletePrivateServiceConnectEndpointRequest) (*pbsvcgcp.DeletePrivateServiceConnectEndpointResponse, error) {
	if err := s.provider.DeletePrivateServiceConnect(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcgcp.DeletePrivateServiceConnectEndpointResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.DeletePrivateServiceConnectEndpointResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetPrivateServiceConnectEndpoint(ctx context.Context, req *pbsvcgcp.GetPrivateServiceConnectEndpointRequest) (*pbsvcgcp.GetPrivateServiceConnectEndpointResponse, error) {
	psc, err := s.provider.GetPrivateServiceConnect(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.GetPrivateServiceConnectEndpointResponse{
		Status:    psc.Status,
		PscStatus: psc.PSCStatus,
	}, nil
}
