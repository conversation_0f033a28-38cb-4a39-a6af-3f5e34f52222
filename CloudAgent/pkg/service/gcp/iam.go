package gcp

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/risingwavelabs/eris"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) FromRoleBindingProto(p *pbsvcgcp.RoleBinding) (gcp.AccessOption, error) {
	switch p.GetAccessOption().(type) {
	case *pbsvcgcp.RoleBinding_GcsAccessOption:
		return gcp.FromGCSAccessOptionProto(p.GetGcsAccessOption())
	default:
		return nil, eris.Errorf("invalid option: %v", p)
	}
}

func (s *Service) CreateIAMPolicyRoleBinding(ctx context.Context, req *pbsvcgcp.CreateIAMPolicyRoleBindingRequest) (*pbsvcgcp.CreateIAMPolicyRoleBindingResponse, error) {
	accessOption, err := s.FromRoleBindingProto(req.GetRoleBinding())
	if err != nil {
		return nil, status.Error(codes.InvalidArgument, err.Error())
	}

	err = s.provider.CreateIAMPolicyMember(ctx, gcp.CreateIAMPolicyMemberOption{
		Namespace:         req.GetResourceMeta().GetNamespace(),
		ResourceID:        req.GetResourceMeta().GetId(),
		ProjectID:         s.projectID,
		IAMServiceAccount: req.GetIAMServiceAccountName(),
		AccessOption:      accessOption,
	})

	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcgcp.CreateIAMPolicyRoleBindingResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.CreateIAMPolicyRoleBindingResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteIAMPolicyRoleBinding(ctx context.Context, req *pbsvcgcp.DeleteIAMPolicyRoleBindingRequest) (*pbsvcgcp.DeleteIAMPolicyRoleBindingResponse, error) {
	if err := s.provider.DeleteIAMPolicyMember(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcgcp.DeleteIAMPolicyRoleBindingResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.DeleteIAMPolicyRoleBindingResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetIAMPolicyRoleBinding(ctx context.Context, req *pbsvcgcp.GetIAMPolicyRoleBindingRequest) (*pbsvcgcp.GetIAMPolicyRoleBindingResponse, error) {
	m, err := s.provider.GetIAMPolicyMember(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.GetIAMPolicyRoleBindingResponse{
		Status: m.Status,
	}, nil
}

func (s *Service) CreateIAMPolicyKSABinding(ctx context.Context, req *pbsvcgcp.CreateIAMPolicyKSABindingRequest) (*pbsvcgcp.CreateIAMPolicyKSABindingResponse, error) {
	err := s.provider.CreateIAMPolicyKSABinding(ctx, gcp.CreateIAMPolicyKSABindingOption{
		Namespace:         req.GetResourceMeta().GetNamespace(),
		ResourceID:        req.GetResourceMeta().GetId(),
		IAMServiceAccount: req.GetIAMServiceAccountName(),
		ProjectID:         s.projectID,
		KSA: &gcp.KubernetesServiceAccount{
			Name:      req.GetServiceAccount().GetName(),
			Namespace: req.GetServiceAccount().GetNamespace(),
		},
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcgcp.CreateIAMPolicyKSABindingResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.CreateIAMPolicyKSABindingResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteIAMPolicyKSABinding(ctx context.Context, req *pbsvcgcp.DeleteIAMPolicyKSABindingRequest) (*pbsvcgcp.DeleteIAMPolicyKSABindingResponse, error) {
	if err := s.provider.DeleteIAMPolicy(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcgcp.DeleteIAMPolicyKSABindingResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.DeleteIAMPolicyKSABindingResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetIAMPolicyKSABinding(ctx context.Context, req *pbsvcgcp.GetIAMPolicyKSABindingRequest) (*pbsvcgcp.GetIAMPolicyKSABindingResponse, error) {
	m, err := s.provider.GetIAMPolicy(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.GetIAMPolicyKSABindingResponse{
		Status: m.Status,
	}, nil
}

func (s *Service) CreateIAMServiceAccount(ctx context.Context, req *pbsvcgcp.CreateIAMServiceAccountRequest) (*pbsvcgcp.CreateIAMServiceAccountResponse, error) {
	err := s.provider.CreateIAMServiceAccount(ctx, gcp.CreateIAMServiceAccountOption{
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ResourceID: req.GetResourceMeta().GetId(),
		ProjectID:  s.projectID,
	})
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbsvcgcp.CreateIAMServiceAccountResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.CreateIAMServiceAccountResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) DeleteIAMServiceAccount(ctx context.Context, req *pbsvcgcp.DeleteIAMServiceAccountRequest) (*pbsvcgcp.DeleteIAMServiceAccountResponse, error) {
	if err := s.provider.DeleteIAMServiceAccount(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId()); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvcgcp.DeleteIAMServiceAccountResponse{
				Status: &pbdeletion.Status{
					Code: pbdeletion.StatusCode_NOT_FOUND,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.DeleteIAMServiceAccountResponse{
		Status: &pbdeletion.Status{
			Code: pbdeletion.StatusCode_SCHEDULED,
		},
	}, nil
}

func (s *Service) GetIAMServiceAccount(ctx context.Context, req *pbsvcgcp.GetIAMServiceAccountRequest) (*pbsvcgcp.GetIAMServiceAccountResponse, error) {
	m, err := s.provider.GetIAMServiceAccount(ctx, req.GetResourceMeta().GetNamespace(), req.GetResourceMeta().GetId())
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvcgcp.GetIAMServiceAccountResponse{
		Status:       m.Status,
		AccountEmail: m.AccountEmail,
	}, nil
}
