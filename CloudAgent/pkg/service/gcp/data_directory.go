package gcp

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbcommondata "github.com/risingwavelabs/cloudagent/pbgen/services/data"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/shared/clone"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

func (s *Service) CreateDataDirectoryDeletionTask(ctx context.Context, req *pbcommondata.CreateDataDirectoryDeletionTaskRequest) (*pbcommondata.CreateDataDirectoryDeletionTaskResponse, error) {
	option := gcp.DeleteDirectoryOptions{
		TaskID:        req.GetResourceMeta().GetId(),
		TaskNamespace: req.GetResourceMeta().GetNamespace(),
		DirectoryName: req.GetDirectoryName(),
		BucketName:    req.GetBucketName(),
	}

	err := s.provider.RunDataDirectoryDeletionTask(ctx, option)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbcommondata.CreateDataDirectoryDeletionTaskResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbcommondata.CreateDataDirectoryDeletionTaskResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}

func (s *Service) CreateDataDirectoryCloneTask(ctx context.Context, req *pbcommondata.CreateDataDirectoryCloneTaskRequest) (*pbcommondata.CreateDataDirectoryCloneTaskResponse, error) {
	option := clone.Options{
		TaskID:                   req.GetResourceMeta().GetId(),
		TaskNamespace:            req.GetResourceMeta().GetNamespace(),
		SourceDirectoryName:      req.GetSourceDirectoryName(),
		SourceBucketName:         req.GetSourceBucketName(),
		DestinationDirectoryName: req.GetDestinationDirectoryName(),
		DestinationBucketName:    req.GetDestinationBucketName(),
		Cursor:                   req.GetCursor(),
		CloneSize:                req.GetCloneSize(),
	}

	err := s.provider.RunDataDirectoryCloneTask(ctx, option)
	if err != nil {
		if utils.IsErrAlreadyExists(err) {
			return &pbcommondata.CreateDataDirectoryCloneTaskResponse{
				Status: &pbcreation.Status{
					Code: pbcreation.StatusCode_ALREADY_EXISTS,
				},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbcommondata.CreateDataDirectoryCloneTaskResponse{
		Status: &pbcreation.Status{
			Code: pbcreation.StatusCode_CREATED,
		},
	}, nil
}
