package gcp

import (
	"context"
	"testing"

	gcccomputeg "github.com/GoogleCloudPlatform/k8s-config-connector/pkg/clients/generated/apis/compute/v1beta1"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	pbresource "github.com/risingwavelabs/cloudagent/pbgen/common/resource"
	pbcreation "github.com/risingwavelabs/cloudagent/pbgen/common/resource/creation"
	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/k8s"
	"github.com/risingwavelabs/cloudagent/pkg/k8s/fake"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
)

const (
	testIP     = "testIP"
	testTarget = "testTarget"
)

func TestCreatePrivateLinkPolicy(t *testing.T) {
	c := fake.NewClient()
	p := gcp.CreateFakeProvider(c)

	s := Service{
		provider:  p,
		projectID: testProjectID,
		region:    testregion,
	}

	res, err := s.CreatePrivateServiceConnectEndpoint(context.Background(), &pbsvcgcp.CreatePrivateServiceConnectEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		ExtraLabels:      map[string]string{"envid": "env"},
		PrivateServiceIp: testIP,
		Target:           testTarget,
	})

	require.NoError(t, err)
	assert.Equal(t, res.GetStatus().GetCode(), pbcreation.StatusCode_SCHEDULED)
	psc, err := k8s.GetResource[gcccomputeg.ComputeForwardingRule](context.Background(), c, testID, testNamespace)

	require.NoError(t, err)
	assert.Equal(t, testID, psc.Name)
	assert.Equal(t, psc.Namespace, testNamespace)
	assert.Equal(t, testTarget, psc.Spec.Target.TargetTCPProxyRef.External)
	assert.Equal(t, testIP, *psc.Spec.IpAddress.Ip)
	assert.Equal(t, map[string]string{"project": "risingwave", "envid": "env"}, psc.Labels)

	res, err = s.CreatePrivateServiceConnectEndpoint(context.Background(), &pbsvcgcp.CreatePrivateServiceConnectEndpointRequest{
		ResourceMeta: &pbresource.Meta{
			Id:        testID,
			Namespace: testNamespace,
		},
		PrivateServiceIp: testIP,
		Target:           testTarget,
	})
	require.NoError(t, err)
	assert.Equal(t, pbcreation.StatusCode_ALREADY_EXISTS, res.GetStatus().GetCode())
}
