package gcp

import (
	"github.com/risingwavelabs/eris"

	pbsvcgcp "github.com/risingwavelabs/cloudagent/pbgen/services/gcp"
	"github.com/risingwavelabs/cloudagent/pkg/providers/gcp"
)

type Service struct {
	pbsvcgcp.UnimplementedGcpResourceManagerServer
	provider  *gcp.Provider
	projectID string
	region    string
	tenantVPC string
}

type NewServiceOption struct {
	Provider  *gcp.Provider
	ProjectID string
	Region    string
	TenantVPC string
}

func NewService(option NewServiceOption) (*Service, error) {
	// TODO(junfeng): check other inputs -- this is blocked by E2E currently GCP values are not populated in E2E.
	if option.Provider == nil {
		return nil, eris.Errorf("GCP provider cannot be nil").WithCode(eris.CodeInvalidArgument)
	}
	return &Service{
		projectID: option.ProjectID,
		provider:  option.Provider,
		region:    option.Region,
		tenantVPC: option.TenantVPC,
	}, nil
}
