package task

import (
	"context"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	pbdeletion "github.com/risingwavelabs/cloudagent/pbgen/common/resource/deletion"
	pbtask "github.com/risingwavelabs/cloudagent/pbgen/common/resource/task"
	pbsvctask "github.com/risingwavelabs/cloudagent/pbgen/services/task"
	"github.com/risingwavelabs/cloudagent/pkg/providers/task"
	"github.com/risingwavelabs/cloudagent/pkg/utils"
	utilstaskrunner "github.com/risingwavelabs/cloudagent/pkg/utils/taskrunner"
)

type Service struct {
	pbsvctask.UnimplementedTaskManagerServer
	taskProvider *task.Provider
}

func NewService(provider *task.Provider) *Service {
	return &Service{
		taskProvider: provider,
	}
}

func (s *Service) GetTaskStatus(ctx context.Context, req *pbsvctask.GetTaskStatusRequest) (*pbsvctask.GetTaskStatusResponse, error) {
	taskStatus, err := s.taskProvider.GetTaskStatus(ctx, task.Meta{
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ResourceID: req.GetResourceMeta().GetId(),
	})
	if err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvctask.GetTaskStatusResponse{
				Status: &pbtask.Status{Code: pbtask.StatusCode_NOT_FOUND},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	pbtaskStatus, err := utilstaskrunner.TaskStatusToProtoTaskStatus(taskStatus)
	if err != nil {
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvctask.GetTaskStatusResponse{
		Status: pbtaskStatus,
	}, nil
}

func (s *Service) CleanupTask(ctx context.Context, req *pbsvctask.CleanupTaskRequest) (*pbsvctask.CleanupTaskResponse, error) {
	if err := s.taskProvider.CleanupTask(ctx, task.Meta{
		Namespace:  req.GetResourceMeta().GetNamespace(),
		ResourceID: req.GetResourceMeta().GetId(),
	}); err != nil {
		if utils.IsErrNotFound(err) {
			return &pbsvctask.CleanupTaskResponse{
				Status: &pbdeletion.Status{Code: pbdeletion.StatusCode_NOT_FOUND},
			}, nil
		}
		return nil, status.Error(codes.Internal, err.Error())
	}
	return &pbsvctask.CleanupTaskResponse{
		Status: &pbdeletion.Status{Code: pbdeletion.StatusCode_SCHEDULED},
	}, nil
}
