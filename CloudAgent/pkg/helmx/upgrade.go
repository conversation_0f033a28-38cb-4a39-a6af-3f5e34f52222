package helmx

import (
	"context"
	"time"

	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chart"
	"helm.sh/helm/v3/pkg/chart/loader"
	"helm.sh/helm/v3/pkg/cli"
	"helm.sh/helm/v3/pkg/storage/driver"
	"k8s.io/client-go/rest"
)

const (
	upgradeTimeout   = 10 * time.Minute
	upgradeOnly      = true
	upgradeResueVals = true
)

type UpgradeOption struct {
	Namespace   string
	ReleaseName string
	ChartURL    string
	Values      map[string]any
	Install     bool
	// optional default 2 minutes
	Timeout *time.Duration

	// if `restConfig` is not provided, this is required
	actionCfg *action.Configuration
	// if `actionCfg` is not provided, this is required to construct
	// actionCfg using `getActionConfig` function
	restConfig *rest.Config
}

func loadChart(chartURL string, chartPathOptions action.ChartPathOptions) (*chart.Chart, error) {
	archiveFile, err := chartPathOptions.LocateChart(chartURL, cli.New())
	if err != nil {
		return nil, eris.Wrap(err, "helm failed to locate chart")
	}
	chart, err := loader.Load(archiveFile)
	if err != nil {
		return nil, eris.Wrap(err, "helm failed to load archive")
	}
	return chart, nil
}

func upgrade(ctx context.Context, options UpgradeOption) error {
	if options.actionCfg == nil {
		actionCfg, err := getActionConfig(ctx, options.Namespace, options.restConfig)
		if err != nil {
			return err
		}
		options.actionCfg = actionCfg
	}

	upgrade := action.NewUpgrade(options.actionCfg)
	// mark release installed already, do not perform install if the release is not installed.
	upgrade.Install = options.Install || !upgradeOnly
	upgrade.Wait = true
	if options.Timeout != nil {
		upgrade.Timeout = *options.Timeout
	} else {
		upgrade.Timeout = upgradeTimeout
	}
	upgrade.ReuseValues = upgradeResueVals
	upgrade.Namespace = options.Namespace

	chart, err := loadChart(options.ChartURL, upgrade.ChartPathOptions)
	if err != nil {
		return eris.Wrap(err, "failed to load chart")
	}

	_, err = upgrade.RunWithContext(ctx, options.ReleaseName, chart, options.Values)
	if err != nil {
		if eris.Is(err, driver.ErrNoDeployedReleases) {
			return ErrNotFound
		}
		return eris.Wrapf(err, "helmx failed to upgrade %s in %s", options.ReleaseName, options.Namespace)
	}
	return nil
}
