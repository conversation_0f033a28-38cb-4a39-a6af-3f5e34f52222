package helmx

import (
	"context"
	"time"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chart/loader"
	"helm.sh/helm/v3/pkg/cli"
	"helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/releaseutil"
	"k8s.io/client-go/rest"
)

var (
	settings = cli.New()
)

const (
	installTimeout = 10 * time.Minute
)

func getPreviousRelease(actionCfg *action.Configuration, releaseName string) *release.Release {
	h, err := actionCfg.Releases.History(releaseName)
	if err != nil || len(h) < 1 {
		return nil
	}
	releaseutil.Reverse(h, releaseutil.SortByRevision)
	rel := h[0]
	return rel
}

type InstallOption struct {
	ChartURL    string
	Namespace   string
	ReleaseName string
	Values      map[string]any
	// optional default 2 minutes
	Timeout *time.Duration

	// if `restConfig` is not provided, this is required
	actionCfg *action.Configuration
	// if `actionCfg` is not provided, this is required to construct
	// actionCfg using `getActionConfig` function
	restConfig *rest.Config
}

// simialr to `helm install <release name> <chart URL> --namespace <namespace> --set ...{val}`.
// return nil if the release with the same name is already installed.
func install(ctx context.Context, options InstallOption) error {
	l := logger.FromCtx(ctx)
	if options.actionCfg == nil {
		actionCfg, err := getActionConfig(ctx, options.Namespace, options.restConfig)
		if err != nil {
			return err
		}
		options.actionCfg = actionCfg
	}
	rel := getPreviousRelease(options.actionCfg, options.ReleaseName)
	if rel != nil {
		st := rel.Info.Status
		// consider the release is installed successfully if it is not in one of the following states
		alreadyInstalled := st != release.StatusFailed && st != release.StatusUninstalled && st != release.StatusUninstalling && st != release.StatusUnknown
		l.Infof(
			"installing helm release %s, status: %s, installed: %t",
			options.ReleaseName,
			st.String(),
			alreadyInstalled,
		)
		if alreadyInstalled {
			return ErrAlreadyInstalled
		}
	}

	install := action.NewInstall(options.actionCfg)
	install.ReleaseName = options.ReleaseName
	install.Wait = true
	if options.Timeout != nil {
		install.Timeout = *options.Timeout
	} else {
		install.Timeout = installTimeout
	}
	install.Namespace = options.Namespace
	install.Replace = true // this is used to replace the existing release which is in failed state
	archiveFile, err := install.LocateChart(options.ChartURL, settings)
	if err != nil {
		return eris.Wrap(err, "helmx failed to locate chart")
	}
	chart, err := loader.Load(archiveFile)
	if err != nil {
		return eris.Wrap(err, "helmx failed to load archive")
	}

	installedRls, err := install.RunWithContext(ctx, chart, options.Values)
	if err != nil {
		return eris.Wrapf(err, "helmx failed to install %s in %s", options.ReleaseName, options.Namespace)
	}
	if installedRls.Info == nil {
		return eris.Wrapf(err, "no release information found: %s in %s", options.ReleaseName, options.Namespace)
	}
	l.Infof(
		"installing release %s in namespace %s, description: %s, status: %s",
		options.ReleaseName,
		options.Namespace,
		installedRls.Info.Description,
		installedRls.Info.Status.String(),
	)
	if installedRls.Info.Status != release.StatusDeployed {
		return eris.Errorf("release status is %s", installedRls.Info.Status.String())
	}
	return nil
}
