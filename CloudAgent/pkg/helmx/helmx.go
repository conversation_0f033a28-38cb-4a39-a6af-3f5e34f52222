package helmx

import (
	"context"
	"regexp"
	"strings"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage/driver"
	"k8s.io/client-go/rest"
)

type ServiceInterface interface {
	Install(context.Context, InstallOption) error
	Upgrade(context.Context, UpgradeOption) error
	Get(context.Context, GetOption) (*release.Release, error)
	Uninstall(context.Context, UninstallOption) error
}

type Service struct {
	restConfig            *rest.Config
	allowChartURLPatterns []string
}

func NewService(restConfig *rest.Config, allowChartURLs []string) (*Service, error) {
	patterns, err := parseAllowChartURLs(allowChartURLs)
	if err != nil {
		return nil, errors.Wrap(err, "failed to parse allowChartURLs")
	}
	return &Service{
		restConfig:            restConfig,
		allowChartURLPatterns: patterns,
	}, nil
}

func parseAllowChartURLs(allowChartURLs []string) ([]string, error) {
	var rtn []string
	for _, item := range allowChartURLs {
		rtn = append(rtn, strings.ReplaceAll(strings.Trim(item, "\r\n\t "), "*", "[^ \\r\\n\\t]+"))
	}
	return rtn, nil
}

func (s *Service) validateChartURL(chartURL string) error {
	for _, pattern := range s.allowChartURLPatterns {
		matched, err := regexp.Match(pattern, []byte(chartURL))
		if err != nil {
			return errors.Wrap(err, "failed to match chart URL pattern")
		}
		if matched {
			return nil
		}
	}
	return eris.Wrap(ErrInvalidChartURL, "chart URL does not match any allowed pattern")
}

// Install triggers an asynchronous helm release installation workflow.
func (s *Service) Install(ctx context.Context, option InstallOption) error {
	if err := s.validateChartURL(option.ChartURL); err != nil {
		return errors.Wrap(err, "failed to validate chart URL")
	}
	option.restConfig = s.restConfig
	return install(ctx, option)
}

// Upgrade triggers an asynchronous helm release upgrade workflow.
func (s *Service) Upgrade(ctx context.Context, option UpgradeOption) error {
	option.restConfig = s.restConfig
	return upgrade(ctx, option)
}

var ErrReleaseNotFound = eris.New("release is not found")

// Get return helmx.ErrReleaseNotFound if the release is not found.
func (s *Service) Get(ctx context.Context, option GetOption) (*release.Release, error) {
	option.restConfig = s.restConfig

	rls, err := get(ctx, option)
	if err != nil {
		if err == driver.ErrReleaseNotFound {
			return nil, ErrReleaseNotFound
		}
		return nil, eris.Wrapf(err, "failed to get release %s in namespace %s", option.ReleaseName, option.Namespace)
	}
	return rls, nil
}

// Uninstall triggers an asynchronous helm release uninstalling workflow.
func (s *Service) Uninstall(ctx context.Context, option UninstallOption) error {
	option.restConfig = s.restConfig
	return uninstall(ctx, option)
}
