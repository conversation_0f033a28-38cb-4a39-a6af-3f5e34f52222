package helmx

import (
	"context"
	"errors"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chartutil"
	"helm.sh/helm/v3/pkg/kube/fake"
	"helm.sh/helm/v3/pkg/registry"
	"helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage"
	"helm.sh/helm/v3/pkg/storage/driver"
)

type GetTestSetup struct {
	rlsName       string
	rlsData       []ReleaseTestData
	outputHandler func(*release.Release, error)
}

func RunGetTest(t *testing.T, test GetTestSetup) {
	storage := storage.Init(driver.NewMemory())
	registryClient, err := registry.NewClient()
	if err != nil {
		t.Fatal(err)
	}
	actionCfg := &action.Configuration{
		Releases:       storage,
		KubeClient:     &fake.PrintingKubeClient{Out: io.Discard},
		Capabilities:   chartutil.DefaultCapabilities,
		RegistryClient: registryClient,
		Log: func(f string, s ...interface{}) {
			t.Logf(f, s)
		},
	}
	chart, err := getChart("https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz")
	require.NoError(t, err)
	for _, rls := range test.rlsData {
		rls.Chart = chart
		require.NoError(t, storage.Create(rls.ToRelease()))
	}

	rls, err := get(context.Background(), GetOption{
		Namespace:   "test",
		ReleaseName: test.rlsName,
		actionCfg:   actionCfg,
	})
	test.outputHandler(rls, err)
}

func TestGet_notfound(t *testing.T) {
	RunGetTest(t, GetTestSetup{
		rlsData: []ReleaseTestData{},
		rlsName: "risingwave-etcd-notfound",
		outputHandler: func(_ *release.Release, err error) {
			assert.True(t, errors.Is(err, ErrNotFound))
		},
	})
}

func TestGet_normal(t *testing.T) {
	RunGetTest(t, GetTestSetup{
		rlsData: []ReleaseTestData{
			{
				Name:    "risingwave-etcd-normal",
				Version: 1,
				Status:  release.StatusDeployed,
				Config: map[string]any{
					"logLevel": "debug",
				},
			},
		},
		rlsName: "risingwave-etcd-normal",
		outputHandler: func(rls *release.Release, err error) {
			assert.NoError(t, err)
			assert.Equal(t, rls.Name, "risingwave-etcd-normal")
		},
	})
}
