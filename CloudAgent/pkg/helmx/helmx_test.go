package helmx

import (
	"context"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestWildcastAllowChartURLs_multipleRules(t *testing.T) {
	patterns, err := parseAllowChartURLs([]string{"https://*.risingwave.cloud", "https://www.risingwave.cloud/*"})
	require.NoError(t, err)

	s := &Service{
		allowChartURLPatterns: patterns,
	}

	err = s.validateChartURL("https://test.risingwave.cloud")
	assert.NoError(t, err)

	err = s.validateChartURL("https://www.risingwave.cloud/test")
	assert.NoError(t, err)
}

func TestWildcastAllowChartURLs(t *testing.T) {
	patterns, err := parseAllowChartURLs([]string{"https://*.risingwave.cloud/rwc/etcd-*.tgz"})
	require.NoError(t, err)

	s := &Service{
		allowChartURLPatterns: patterns,
	}

	// normal case
	err = s.validateChartURL("https://test.risingwave.cloud/rwc/etcd-v9.0.4.tgz")
	assert.NoError(t, err)

	// cannot wildcast empty
	err = s.validateChartURL("https://test.risingwave.cloud/rwc/etcd-.tgz")
	assert.True(t, eris.Is(err, ErrInvalidChartURL))
	err = s.validateChartURL("https://.risingwave.cloud/rwc/etcd-v9.0.4.tgz")
	assert.True(t, eris.Is(err, ErrInvalidChartURL))
}

func TestInstall_invalidURL(t *testing.T) {
	patterns, err := parseAllowChartURLs([]string{"https://*.risingwave.cloud"})
	require.NoError(t, err)

	s := &Service{
		allowChartURLPatterns: patterns,
	}

	err = s.Install(context.Background(), InstallOption{
		ChartURL:    "https://www.ddease.com",
		Namespace:   "ns",
		ReleaseName: "test",
		Values:      map[string]any{},
	})
	assert.True(t, eris.Is(err, ErrInvalidChartURL))
}
