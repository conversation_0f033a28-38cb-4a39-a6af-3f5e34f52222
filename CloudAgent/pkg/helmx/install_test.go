package helmx

import (
	"context"
	"io"
	"testing"

	"github.com/stretchr/testify/require"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chart"
	"helm.sh/helm/v3/pkg/chartutil"
	"helm.sh/helm/v3/pkg/kube/fake"
	"helm.sh/helm/v3/pkg/registry"
	rspb "helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage"
	"helm.sh/helm/v3/pkg/storage/driver"
)

type ReleaseTestData struct {
	Name      string
	Version   int
	Manifest  string
	Namespace string
	Status    rspb.Status
	Config    map[string]any
	Chart     *chart.Chart
}

func (test ReleaseTestData) ToRelease() *rspb.Release {
	return &rspb.Release{
		Name:      test.Name,
		Version:   test.Version,
		Manifest:  test.Manifest,
		Namespace: test.Namespace,
		Info:      &rspb.Info{Status: test.Status},
		Config:    test.Config,
		Chart:     test.Chart,
	}
}

func TestGetPreviousRelease(t *testing.T) {
	releaseName := "risingwave-etcd-test"
	actionCfg := &action.Configuration{}
	storage := storage.Init(driver.NewMemory())

	rls1 := ReleaseTestData{
		Name:    releaseName,
		Version: 1,
		Status:  rspb.StatusFailed,
	}.ToRelease()

	rls2 := ReleaseTestData{
		Name:    releaseName,
		Version: 2,
		Status:  rspb.StatusFailed,
	}.ToRelease()

	require.NoError(t, storage.Create(rls1))
	require.NoError(t, storage.Create(rls2))

	actionCfg.Releases = storage
	rls := getPreviousRelease(actionCfg, releaseName)
	require.Equal(t, rls2, rls)
}

func TestInstall_normal(t *testing.T) {
	registryClient, err := registry.NewClient()
	if err != nil {
		t.Fatal(err)
	}
	actionCfg := &action.Configuration{
		Releases:       storage.Init(driver.NewMemory()),
		KubeClient:     &fake.PrintingKubeClient{Out: io.Discard},
		Capabilities:   chartutil.DefaultCapabilities,
		RegistryClient: registryClient,
		Log: func(f string, s ...interface{}) {
			t.Logf(f, s)
		},
	}
	err = install(context.Background(), InstallOption{
		ChartURL:    "https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz",
		Namespace:   "test",
		ReleaseName: "risingwave-etcd-test",
		Values: map[string]interface{}{
			"logLevel": "debug",
		},
		actionCfg: actionCfg,
	})
	require.NoError(t, err)
}

func TestInstall_previousFailed(t *testing.T) {
	releaseName := "risingwave-etcd-test"
	storage := storage.Init(driver.NewMemory())

	rls1 := ReleaseTestData{
		Name:    releaseName,
		Version: 1,
		Status:  rspb.StatusFailed,
	}.ToRelease()

	rls2 := ReleaseTestData{
		Name:    releaseName,
		Version: 2,
		Status:  rspb.StatusFailed,
	}.ToRelease()

	require.NoError(t, storage.Create(rls1))
	require.NoError(t, storage.Create(rls2))

	registryClient, err := registry.NewClient()
	require.NoError(t, err)

	actionCfg := &action.Configuration{
		Releases:       storage,
		KubeClient:     &fake.PrintingKubeClient{Out: io.Discard},
		Capabilities:   chartutil.DefaultCapabilities,
		RegistryClient: registryClient,
		Log: func(f string, s ...interface{}) {
			t.Logf(f, s)
		},
	}
	err = install(context.Background(), InstallOption{
		ChartURL:    "https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz",
		Namespace:   "test",
		ReleaseName: releaseName,
		Values: map[string]interface{}{
			"logLevel": "debug",
		},
		actionCfg: actionCfg,
	})
	require.NoError(t, err)
}

func TestInstall_previousInstalled(t *testing.T) {
	releaseName := "risingwave-etcd-test"
	storage := storage.Init(driver.NewMemory())

	rls := ReleaseTestData{
		Name:    releaseName,
		Version: 1,
		Status:  rspb.StatusDeployed,
	}.ToRelease()

	require.NoError(t, storage.Create(rls))

	registryClient, err := registry.NewClient()
	require.NoError(t, err)

	actionCfg := &action.Configuration{
		Releases:       storage,
		KubeClient:     &fake.PrintingKubeClient{Out: io.Discard},
		Capabilities:   chartutil.DefaultCapabilities,
		RegistryClient: registryClient,
		Log: func(f string, s ...interface{}) {
			t.Logf(f, s)
		},
	}

	err = install(context.Background(), InstallOption{
		ChartURL:    "https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz",
		Namespace:   "test",
		ReleaseName: releaseName,
		Values: map[string]interface{}{
			"logLevel": "debug",
		},
		actionCfg: actionCfg,
	})
	require.Equal(t, err, ErrAlreadyInstalled)
}
