// Code generated by MockGen. DO NOT EDIT.
// Source: github.com/risingwavelabs/cloudagent/pkg/helmx (interfaces: ServiceInterface)
//
// Generated by this command:
//
//	mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/helmx -package=helmx -destination=pkg/helmx/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/helmx ServiceInterface
//

// Package helmx is a generated GoMock package.
package helmx

import (
	context "context"
	reflect "reflect"

	gomock "go.uber.org/mock/gomock"
	release "helm.sh/helm/v3/pkg/release"
)

// MockServiceInterface is a mock of ServiceInterface interface.
type MockServiceInterface struct {
	ctrl     *gomock.Controller
	recorder *MockServiceInterfaceMockRecorder
	isgomock struct{}
}

// MockServiceInterfaceMockRecorder is the mock recorder for MockServiceInterface.
type MockServiceInterfaceMockRecorder struct {
	mock *MockServiceInterface
}

// NewMockServiceInterface creates a new mock instance.
func NewMockServiceInterface(ctrl *gomock.Controller) *MockServiceInterface {
	mock := &MockServiceInterface{ctrl: ctrl}
	mock.recorder = &MockServiceInterfaceMockRecorder{mock}
	return mock
}

// EXPECT returns an object that allows the caller to indicate expected use.
func (m *MockServiceInterface) EXPECT() *MockServiceInterfaceMockRecorder {
	return m.recorder
}

// Get mocks base method.
func (m *MockServiceInterface) Get(arg0 context.Context, arg1 GetOption) (*release.Release, error) {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Get", arg0, arg1)
	ret0, _ := ret[0].(*release.Release)
	ret1, _ := ret[1].(error)
	return ret0, ret1
}

// Get indicates an expected call of Get.
func (mr *MockServiceInterfaceMockRecorder) Get(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Get", reflect.TypeOf((*MockServiceInterface)(nil).Get), arg0, arg1)
}

// Install mocks base method.
func (m *MockServiceInterface) Install(arg0 context.Context, arg1 InstallOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Install", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Install indicates an expected call of Install.
func (mr *MockServiceInterfaceMockRecorder) Install(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Install", reflect.TypeOf((*MockServiceInterface)(nil).Install), arg0, arg1)
}

// Uninstall mocks base method.
func (m *MockServiceInterface) Uninstall(arg0 context.Context, arg1 UninstallOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Uninstall", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Uninstall indicates an expected call of Uninstall.
func (mr *MockServiceInterfaceMockRecorder) Uninstall(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Uninstall", reflect.TypeOf((*MockServiceInterface)(nil).Uninstall), arg0, arg1)
}

// Upgrade mocks base method.
func (m *MockServiceInterface) Upgrade(arg0 context.Context, arg1 UpgradeOption) error {
	m.ctrl.T.Helper()
	ret := m.ctrl.Call(m, "Upgrade", arg0, arg1)
	ret0, _ := ret[0].(error)
	return ret0
}

// Upgrade indicates an expected call of Upgrade.
func (mr *MockServiceInterfaceMockRecorder) Upgrade(arg0, arg1 any) *gomock.Call {
	mr.mock.ctrl.T.Helper()
	return mr.mock.ctrl.RecordCallWithMethodType(mr.mock, "Upgrade", reflect.TypeOf((*MockServiceInterface)(nil).Upgrade), arg0, arg1)
}
