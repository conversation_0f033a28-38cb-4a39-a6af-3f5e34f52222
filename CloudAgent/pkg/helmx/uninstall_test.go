package helmx

import (
	"context"
	"io"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chartutil"
	"helm.sh/helm/v3/pkg/kube/fake"
	"helm.sh/helm/v3/pkg/registry"
	rspb "helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage"
	"helm.sh/helm/v3/pkg/storage/driver"
)

type UninstallTestSetup struct {
	rlsName       string
	rlsData       []ReleaseTestData
	outputHandler func(error)
}

func RunUninstallTest(t *testing.T, setup UninstallTestSetup) {
	storage := storage.Init(driver.NewMemory())
	registryClient, err := registry.NewClient()
	if err != nil {
		t.Fatal(err)
	}
	actionCfg := &action.Configuration{
		Releases:       storage,
		KubeClient:     &fake.PrintingKubeClient{Out: io.Discard},
		Capabilities:   chartutil.DefaultCapabilities,
		RegistryClient: registryClient,
		Log: func(f string, s ...interface{}) {
			t.Logf(f, s)
		},
	}
	chart, err := getChart("https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz")
	require.NoError(t, err)
	for _, rls := range setup.rlsData {
		rls.Chart = chart
		require.NoError(t, storage.Create(rls.ToRelease()))
	}

	err = uninstall(context.Background(), UninstallOption{
		Namespace:   "test",
		ReleaseName: setup.rlsName,
		actionCfg:   actionCfg,
	})
	setup.outputHandler(err)
}

func TestUninstall_normal(t *testing.T) {
	RunUninstallTest(t, UninstallTestSetup{
		rlsName: "risingwave-etcd-test",
		rlsData: []ReleaseTestData{
			{
				Name:    "risingwave-etcd-normal",
				Version: 1,
				Status:  rspb.StatusDeployed,
				Config: map[string]any{
					"logLevel": "debug",
				},
			},
		},
		outputHandler: func(err error) {
			assert.NoError(t, err)
		},
	})
}

func TestUninstall_notfound(t *testing.T) {
	RunUninstallTest(t, UninstallTestSetup{
		rlsName: "risingwave-etcd-test",
		rlsData: []ReleaseTestData{},
		outputHandler: func(err error) {
			assert.NoError(t, err)
		},
	})
}

func TestUninstall_previous_uninstalling(t *testing.T) {
	RunUninstallTest(t, UninstallTestSetup{
		rlsName: "risingwave-etcd-test",
		rlsData: []ReleaseTestData{
			{
				Name:    "risingwave-etcd-normal",
				Version: 1,
				Status:  rspb.StatusUninstalling,
				Config: map[string]any{
					"logLevel": "debug",
				},
			},
		},
		outputHandler: func(err error) {
			assert.NoError(t, err)
		},
	})
}

func TestUninstall_previous_uninstalled(t *testing.T) {
	RunUninstallTest(t, UninstallTestSetup{
		rlsName: "risingwave-etcd-test",
		rlsData: []ReleaseTestData{},
		outputHandler: func(err error) {
			assert.NoError(t, err)
		},
	})
}
