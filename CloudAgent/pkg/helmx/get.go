package helmx

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage/driver"
	"k8s.io/client-go/rest"
)

type GetOption struct {
	ReleaseName string
	Namespace   string

	// optional default 2 minutes
	Timeout *time.Duration
	// if `restConfig` is not provided, this is required
	actionCfg *action.Configuration
	// if `actionCfg` is not provided, this is required to construct
	// actionCfg using `getActionConfig` function
	restConfig *rest.Config
}

func get(ctx context.Context, option GetOption) (*release.Release, error) {
	if option.actionCfg == nil {
		actionCfg, err := getActionConfig(ctx, option.Namespace, option.restConfig)
		if err != nil {
			return nil, eris.Wrap(err, "failed to get action config from rest config")
		}
		option.actionCfg = actionCfg
	}

	get := action.NewGet(option.actionCfg)
	release, err := get.Run(option.ReleaseName)
	if err != nil {
		if errors.Is(err, driver.ErrReleaseNotFound) {
			return nil, ErrNotFound
		}
		return nil, eris.Wrapf(err, "helmx failed to get release %s", option.ReleaseName)
	}
	if release.Chart == nil {
		return nil, eris.Errorf("chart is nil in release: %s", option.ReleaseName)
	}
	return release, nil
}
