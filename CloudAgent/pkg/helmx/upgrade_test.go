package helmx

import (
	"context"
	"fmt"
	"io"
	"testing"

	"github.com/risingwavelabs/eris"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chartutil"
	"helm.sh/helm/v3/pkg/kube/fake"
	"helm.sh/helm/v3/pkg/registry"
	rspb "helm.sh/helm/v3/pkg/release"
	"helm.sh/helm/v3/pkg/storage"
	"helm.sh/helm/v3/pkg/storage/driver"
)

type UpgradeTestSetup struct {
	rlsName       string
	rlsData       []ReleaseTestData
	outputHandler func(error)
}

func RunUpgradeTest(t *testing.T, test UpgradeTestSetup) {
	storage := storage.Init(driver.NewMemory())
	registryClient, err := registry.NewClient()
	if err != nil {
		t.Fatal(err)
	}
	actionCfg := &action.Configuration{
		Releases:       storage,
		KubeClient:     &fake.PrintingKubeClient{Out: io.Discard},
		Capabilities:   chartutil.DefaultCapabilities,
		RegistryClient: registryClient,
		Log: func(f string, s ...interface{}) {
			t.Logf(f, s)
		},
	}
	chart, err := getChart("https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz")
	require.NoError(t, err)
	for _, rls := range test.rlsData {
		rls.Chart = chart
		require.NoError(t, storage.Create(rls.ToRelease()))
	}

	rootPassword := "rootPassword"
	err = upgrade(
		context.Background(),
		UpgradeOption{
			Namespace:   "test",
			ReleaseName: test.rlsName,
			ChartURL:    "https://charts.bitnami.com/bitnami/etcd-8.5.10.tgz",
			Values: map[string]any{
				"logLevel": "info",
				"auth": map[string]any{
					"rbac": map[string]any{
						"rootPassword": rootPassword,
					},
				},
			},
			actionCfg: actionCfg,
		})
	test.outputHandler(err)
}

func TestUpgrade_normal(t *testing.T) {
	RunUpgradeTest(t, UpgradeTestSetup{
		rlsData: []ReleaseTestData{
			{
				Name:    "risingwave-etcd-normal",
				Version: 1,
				Status:  rspb.StatusDeployed,
				Config: map[string]any{
					"logLevel": "debug",
				},
			},
		},
		rlsName: "risingwave-etcd-normal",
		outputHandler: func(err error) {
			assert.NoError(t, err)
		},
	})
}

func TestUpgrade_notfound(t *testing.T) {
	RunUpgradeTest(t, UpgradeTestSetup{
		rlsData: []ReleaseTestData{},
		rlsName: "risingwave-etcd-uninstalled",
		outputHandler: func(err error) {
			fmt.Println(err)
			assert.True(t, eris.Is(err, ErrNotFound))
		},
	})
}
