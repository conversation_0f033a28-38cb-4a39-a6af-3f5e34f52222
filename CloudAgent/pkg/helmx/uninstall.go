package helmx

import (
	"context"
	"time"

	"github.com/pkg/errors"
	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/storage/driver"
	"k8s.io/client-go/rest"

	"github.com/risingwavelabs/cloudagent/pkg/utils"
)

const (
	uninstallTimeout = 10 * time.Minute
)

type UninstallOption struct {
	Namespace   string
	ReleaseName string
	// optional default 2 minutes
	Timeout *time.Duration

	// if `restConfig` is not provided, this is required
	actionCfg *action.Configuration
	// if `actionCfg` is not provided, this is required to construct
	// actionCfg using `getActionConfig` function
	restConfig *rest.Config
}

func uninstall(ctx context.Context, option UninstallOption) error {
	if option.actionCfg == nil {
		actionCfg, err := getActionConfig(ctx, option.Namespace, option.restConfig)
		if err != nil {
			return err
		}
		option.actionCfg = actionCfg
	}

	uninstall := action.NewUninstall(option.actionCfg)
	uninstall.Wait = true
	if option.Timeout == nil {
		option.Timeout = utils.Ptr(uninstallTimeout)
	}
	uninstall.Timeout = *option.Timeout

	_, err := uninstall.Run(option.ReleaseName)
	if err != nil && !errors.Is(err, driver.ErrReleaseNotFound) {
		return eris.Wrapf(err, "failed to uninstall release %s in namespace %s", option.ReleaseName, option.Namespace)
	}
	return nil
}
