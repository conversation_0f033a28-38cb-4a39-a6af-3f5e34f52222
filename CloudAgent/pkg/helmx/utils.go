package helmx

import (
	"context"
	"errors"
	"fmt"

	"github.com/risingwavelabs/cloudagent/pkg/logger"

	"github.com/risingwavelabs/eris"
	"helm.sh/helm/v3/pkg/action"
	"helm.sh/helm/v3/pkg/chart"
	"helm.sh/helm/v3/pkg/chart/loader"
	"helm.sh/helm/v3/pkg/cli"
	"k8s.io/client-go/rest"
)

func getActionConfig(ctx context.Context, namespace string, restConfig *rest.Config) (*action.Configuration, error) {
	if namespace == "" {
		namespace = "default"
	}
	if restConfig == nil {
		return nil, eris.New("missing RestConfig and ActionCfg")
	}
	helmRestClientGetter := NewRESTClientGetter(namespace, restConfig)
	actionCfg := &action.Configuration{}
	err := actionCfg.Init(helmRestClientGetter, namespace, "secret", func(format string, v ...interface{}) {
		logger.FromCtx(ctx).Debugf(
			"init helm actions config for namespace %s: %s",
			namespace,
			fmt.Sprintf(format, v...),
		)
	})
	if err != nil {
		return nil, err
	}
	return actionCfg, nil
}

func getChart(chartStr string) (*chart.Chart, error) {
	opt := action.ChartPathOptions{}
	archiveFile, err := opt.LocateChart(chartStr, cli.New())
	if err != nil {
		return nil, err
	}
	chart, err := loader.Load(archiveFile)
	if err != nil {
		return nil, err
	}
	return chart, nil
}

var (
	ErrAlreadyInstalled = errors.New("already installed")
	ErrNotFound         = errors.New("not found")
	ErrInvalidChartURL  = errors.New("invalid chart URL")
)

func IgnoreAlreadyInstalled(err error) error {
	if err == ErrAlreadyInstalled {
		return nil
	}
	return err
}

func IgnoreNotFound(err error) error {
	if err == ErrReleaseNotFound {
		return nil
	}
	return err
}

func IsErrNotFound(err error) bool {
	return errors.Is(err, ErrNotFound)
}
