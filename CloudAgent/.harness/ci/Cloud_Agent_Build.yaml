pipeline:
  name: Cloud Agent Build
  identifier: Cloud_Agent_Build
  projectIdentifier: Rising_Wave_Cloud
  orgIdentifier: default
  tags: {}
  properties:
    ci:
      codebase:
        connectorRef: Cloud_Agent
        build: <+input>
  stages:
    - stage:
        name: Generate Tags
        identifier: Generate_Tags
        template:
          templateRef: Generate_Build_Tags
          versionLabel: V1
    - parallel:
        - stage:
            name: Build and Push Linux_amd64
            identifier: build_and_push_linux_amd64
            type: CI
            spec:
              cloneCodebase: true
              platform:
                os: Linux
                arch: Amd64
              runtime:
                type: Cloud
                spec: {}
              execution:
                steps:
                  - step:
                      type: Action
                      name: Set up Golang
                      identifier: Set_up_Golang
                      spec:
                        uses: actions/setup-go@v4
                        with:
                          go-version: "1.24"
                  - step:
                      type: Action
                      name: Configure AWS Credentials
                      identifier: Configure_AWS_Credentials
                      spec:
                        uses: aws-actions/configure-aws-credentials@v2
                        with:
                          aws-access-key-id: AKIA3FLD3OXOPFNQPG6P
                          aws-secret-access-key: <+secrets.getValue("awsrwcartifacts")>
                          aws-region: us-east-1
                  - step:
                      name: docker-login
                      identifier: dockerlogin
                      template:
                        templateRef: dockerlogin
                        versionLabel: V1
                  - step:
                      type: Run
                      name: Build and push docker image
                      identifier: Build_and_push_docker_image
                      spec:
                        shell: Bash
                        command: |-
                          DOCKER_HOST=unix:///var/run/docker.sock docker buildx create --name multi-platform --use --platform linux/amd64 --driver docker-container

                          # login to ECR
                          echo "ECR docker login:"
                          aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY

                          # login to GAR
                          echo "GAR docker login:"
                          cat <<EOT >> /tmp/application_default_credentials.json
                          <+secrets.getValue("gcprwcartifacts")>
                          EOT
                          cat /tmp/application_default_credentials.json | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
                          # Clean up
                          rm /tmp/application_default_credentials.json

                          # login to ACR
                          echo "ACR docker login:"
                          docker login $ACR_REGISTRY --username $ACR_SERVICE_PRINCIPAL --password <+secrets.getValue("azurerwcartifactsharness")>

                          # Build and push images
                          echo "Build and push images"
                          docker buildx build --platform linux/amd64 --build-arg VERSION=$BRANCH_TAG --progress=plain -f <+matrix.build.dockerfile> \
                            -t $ECR_REGISTRY/$ECR_REPOSITORY:${GIT_TAG}-amd64 \
                            -t $GAR_REPOSITORY/$GAR_IMAGE:${GIT_TAG}-amd64 \
                            -t $ACR_REGISTRY/$ACR_REPOSITORY:${GIT_TAG}-amd64 \
                            --push \
                            .
                        envVariables:
                          ECR_REGISTRY: 767397950940.dkr.ecr.us-east-1.amazonaws.com
                          ECR_REPOSITORY: <+matrix.build.ecr_repo>
                          GAR_REPOSITORY: us-docker.pkg.dev/rwc-artifacts/rwc
                          GAR_IMAGE: <+matrix.build.gar_img>
                          ACR_SERVICE_PRINCIPAL: 89c03220-b710-44d3-8794-be78cf6860df
                          ACR_REGISTRY: rwcartifacts.azurecr.io
                          ACR_REPOSITORY: <+matrix.build.acr_repo>
                          GIT_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.GIT_TAG>
                          BRANCH_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.BRANCH_TAG>
            strategy:
              matrix:
                build:
                  - ecr_repo: rwc/cloudagent
                    gar_img: cloudagent
                    acr_repo: rwc/cloudagent
                    dockerfile: service.Dockerfile
                    name: agent
                  - ecr_repo: rwc/cloudagent-taskrunner
                    gar_img: cloudagent-taskrunner
                    acr_repo: rwc/cloudagent-taskrunner
                    dockerfile: task.Dockerfile
                    name: task
                nodeName: <+matrix.build.name>
            description: ""
        - stage:
            name: Build and Push Linux_arm64
            identifier: build_and_push_linux_arm64
            type: CI
            spec:
              cloneCodebase: true
              platform:
                os: Linux
                arch: Arm64
              runtime:
                type: Cloud
                spec: {}
              execution:
                steps:
                  - step:
                      type: Action
                      name: Set up Golang
                      identifier: Set_up_Golang
                      spec:
                        uses: actions/setup-go@v4
                        with:
                          go-version: "1.24"
                  - step:
                      type: Action
                      name: Configure AWS Credentials
                      identifier: Configure_AWS_Credentials
                      spec:
                        uses: aws-actions/configure-aws-credentials@v2
                        with:
                          aws-access-key-id: AKIA3FLD3OXOPFNQPG6P
                          aws-secret-access-key: <+secrets.getValue("awsrwcartifacts")>
                          aws-region: us-east-1
                  - step:
                      name: docker-login
                      identifier: dockerlogin
                      template:
                        templateRef: dockerlogin
                        versionLabel: V1
                  - step:
                      type: Run
                      name: Build and push docker image
                      identifier: Build_and_push_docker_image
                      spec:
                        shell: Bash
                        command: |-
                          DOCKER_HOST=unix:///var/run/docker.sock docker buildx create --name multi-platform --use --platform linux/arm64 --driver docker-container

                          # login to ECR
                          echo "ECR docker login:"
                          aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY

                          # login to GAR
                          echo "GAR docker login:"
                          cat <<EOT >> /tmp/application_default_credentials.json
                          <+secrets.getValue("gcprwcartifacts")>
                          EOT
                          cat /tmp/application_default_credentials.json | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
                          # Clean up
                          rm /tmp/application_default_credentials.json

                          # login to ACR
                          echo "ACR docker login:"
                          docker login $ACR_REGISTRY --username $ACR_SERVICE_PRINCIPAL --password <+secrets.getValue("azurerwcartifactsharness")>

                          # Build and push images
                          echo "Build and push images"
                          docker buildx build --platform linux/arm64 --build-arg VERSION=$BRANCH_TAG --progress=plain -f <+matrix.build.dockerfile> \
                            -t $ECR_REGISTRY/$ECR_REPOSITORY:${GIT_TAG}-arm64 \
                            -t $GAR_REPOSITORY/$GAR_IMAGE:${GIT_TAG}-arm64 \
                            -t $ACR_REGISTRY/$ACR_REPOSITORY:${GIT_TAG}-arm64 \
                            --push \
                            .
                        envVariables:
                          ECR_REGISTRY: 767397950940.dkr.ecr.us-east-1.amazonaws.com
                          ECR_REPOSITORY: <+matrix.build.ecr_repo>
                          GAR_REPOSITORY: us-docker.pkg.dev/rwc-artifacts/rwc
                          GAR_IMAGE: <+matrix.build.gar_img>
                          ACR_SERVICE_PRINCIPAL: 89c03220-b710-44d3-8794-be78cf6860df
                          ACR_REGISTRY: rwcartifacts.azurecr.io
                          ACR_REPOSITORY: <+matrix.build.acr_repo>
                          GIT_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.GIT_TAG>
                          BRANCH_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.BRANCH_TAG>
            strategy:
              matrix:
                build:
                  - ecr_repo: rwc/cloudagent
                    gar_img: cloudagent
                    acr_repo: rwc/cloudagent
                    dockerfile: service.Dockerfile
                    name: agent
                  - ecr_repo: rwc/cloudagent-taskrunner
                    gar_img: cloudagent-taskrunner
                    acr_repo: rwc/cloudagent-taskrunner
                    dockerfile: task.Dockerfile
                    name: task
                nodeName: <+matrix.build.name>
            description: ""
    - stage:
        name: Build and Push Manifest
        identifier: build_and_push_manifest
        type: CI
        spec:
          cloneCodebase: false
          platform:
            os: Linux
            arch: Arm64
          runtime:
            type: Cloud
            spec: {}
          execution:
            steps:
              - step:
                  type: Action
                  name: Configure AWS Credentials
                  identifier: Configure_AWS_Credentials
                  spec:
                    uses: aws-actions/configure-aws-credentials@v2
                    with:
                      aws-access-key-id: AKIA3FLD3OXOPFNQPG6P
                      aws-secret-access-key: <+secrets.getValue("awsrwcartifacts")>
                      aws-region: us-east-1
              - step:
                  name: docker-login
                  identifier: dockerlogin
                  template:
                    templateRef: dockerlogin
                    versionLabel: V1
              - step:
                  type: Run
                  name: Build and push manifest
                  identifier: Build_and_push_manifest
                  spec:
                    shell: Bash
                    command: |-
                      export DOCKER_CLI_EXPERIMENTAL=enabled

                      # login to ECR
                      echo "ECR docker login:"
                      aws ecr get-login-password | docker login --username AWS --password-stdin $ECR_REGISTRY

                      # login to GAR
                      echo "GAR docker login:"
                      cat <<EOT >> /tmp/application_default_credentials.json
                      <+secrets.getValue("gcprwcartifacts")>
                      EOT
                      cat /tmp/application_default_credentials.json | docker login -u _json_key --password-stdin https://us-docker.pkg.dev
                      # Clean up
                      rm /tmp/application_default_credentials.json

                      # login to ACR
                      echo "ACR docker login:"
                      docker login $ACR_REGISTRY --username $ACR_SERVICE_PRINCIPAL --password <+secrets.getValue("azurerwcartifactsharness")>

                      TAGS=("$GIT_TAG" "$BRANCH_TAG" "latest")

                      # Create manifests.
                      for TAG in "${TAGS[@]}"; do
                        docker manifest create $ECR_REGISTRY/$ECR_REPOSITORY:$TAG \
                          $ECR_REGISTRY/$ECR_REPOSITORY:${GIT_TAG}-amd64 \
                          $ECR_REGISTRY/$ECR_REPOSITORY:${GIT_TAG}-arm64

                        docker manifest create $GAR_REPOSITORY/$GAR_IMAGE:$TAG \
                          $GAR_REPOSITORY/$GAR_IMAGE:${GIT_TAG}-amd64 \
                          $GAR_REPOSITORY/$GAR_IMAGE:${GIT_TAG}-arm64

                        docker manifest create $ACR_REGISTRY/$ACR_REPOSITORY:$TAG \
                          $ACR_REGISTRY/$ACR_REPOSITORY:${GIT_TAG}-amd64 \
                          $ACR_REGISTRY/$ACR_REPOSITORY:${GIT_TAG}-arm64
                      done

                      # Push manifests
                      REPOSITORIES=("$ECR_REGISTRY/$ECR_REPOSITORY" "$GAR_REPOSITORY/$GAR_IMAGE" "$ACR_REGISTRY/$ACR_REPOSITORY")
                      for REPOSITORY in "${REPOSITORIES[@]}"; do
                          for TAG in "${TAGS[@]}"; do
                          docker manifest push $REPOSITORY:$TAG
                          done
                      done
                    envVariables:
                      ECR_REGISTRY: 767397950940.dkr.ecr.us-east-1.amazonaws.com
                      ECR_REPOSITORY: <+matrix.build.ecr_repo>
                      GAR_REPOSITORY: us-docker.pkg.dev/rwc-artifacts/rwc
                      GAR_IMAGE: <+matrix.build.gar_img>
                      ACR_SERVICE_PRINCIPAL: 89c03220-b710-44d3-8794-be78cf6860df
                      ACR_REGISTRY: rwcartifacts.azurecr.io
                      ACR_REPOSITORY: <+matrix.build.acr_repo>
                      GIT_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.GIT_TAG>
                      BRANCH_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.BRANCH_TAG>
        strategy:
          matrix:
            build:
              - ecr_repo: rwc/cloudagent
                gar_img: cloudagent
                acr_repo: rwc/cloudagent
                dockerfile: service.Dockerfile
                name: agent
              - ecr_repo: rwc/cloudagent-taskrunner
                gar_img: cloudagent-taskrunner
                acr_repo: rwc/cloudagent-taskrunner
                dockerfile: task.Dockerfile
                name: task
            nodeName: <+matrix.build.name>
        description: ""
    - stage:
        name: Send Release Triggers
        identifier: Send_Release_Triggers
        description: ""
        type: CI
        spec:
          cloneCodebase: false
          platform:
            os: Linux
            arch: Amd64
          runtime:
            type: Cloud
            spec: {}
          execution:
            steps:
              - step:
                  type: Run
                  name: Send Release Triggers
                  identifier: Send_Release_Triggers
                  spec:
                    shell: Bash
                    command: |-
                      set -e
                      echo $BRANCH_TAG
                      trigger_v2(){
                          name=$1
                          pipeline_id=$2
                          trigger_id=$3
                          echo "sending request to trigger $name..."
                          result=$(curl -i -w "\nresponse_status_code:%{response_code}"  --http1.1 -X POST -H 'content-type: application/json' --url "https://app.harness.io/gateway/pipeline/api/webhook/custom/v2?accountIdentifier=$ACCOUNT_ID&orgIdentifier=$ORG_ID&projectIdentifier=$PROJ_ID&pipelineIdentifier=$pipeline_id&triggerIdentifier=$trigger_id" -d "{\"imagetag\": \"$BRANCH_TAG\"}")
                          echo $result
                          echo $result | grep '"status":"SUCCESS"' > /dev/null
                      }
                      # 01. CloudAgent DEV CD
                      trigger_v2 "Cloud Agent Dev Release" Cloud_Agent_Dev_Release new_main_branch_image
                      trigger_v2 "Cloud Agent Test Release" Cloud_Agent_Test_Release new_main_branch_image
                    envVariables:
                      BRANCH_TAG: <+pipeline.stages.Generate_Tags.spec.execution.steps.Generate_Tags.output.outputVariables.BRANCH_TAG>
                      PROJ_ID: Rising_Wave_Cloud
                      ORG_ID: default
                      ACCOUNT_ID: 6P8wiTFqSPul99qcIkRUNQ
  timeout: 20m
  fixedInputsOnRerun: true
