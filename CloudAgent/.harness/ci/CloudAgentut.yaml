pipeline:
  name: CloudAgent-ut
  identifier: CloudAgentut
  projectIdentifier: Rising_Wave_Cloud
  orgIdentifier: default
  tags: {}
  properties:
    ci:
      codebase:
        connectorRef: Cloud_Agent
        build: <+input>
  stages:
    - stage:
        name: unit test
        identifier: unit_test
        description: ""
        type: CI
        spec:
          cloneCodebase: true
          platform:
            os: Linux
            arch: Amd64
          runtime:
            type: Cloud
            spec: {}
          execution:
            steps:
              - step:
                  type: Action
                  name: Set up Golang
                  identifier: Set_up_Golang
                  spec:
                    uses: actions/setup-go@v4
                    with:
                      go-version: "1.24"
              - step:
                  type: Run
                  name: unit test
                  identifier: unit_test
                  spec:
                    shell: Bash
                    command: |-
                      echo "Running unit tests at commit $(git rev-parse HEAD)"
                      make ut
