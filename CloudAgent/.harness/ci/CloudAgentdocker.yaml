pipeline:
  name: CloudAgent-docker
  identifier: CloudAgentdocker
  projectIdentifier: Rising_Wave_Cloud
  orgIdentifier: default
  tags: {}
  properties:
    ci:
      codebase:
        connectorRef: Cloud_Agent
        build: <+input>
  stages:
    - stage:
        name: docker build test
        identifier: docker_build_test
        description: ""
        type: CI
        spec:
          cloneCodebase: true
          platform:
            os: Linux
            arch: Amd64
          runtime:
            type: Cloud
            spec: {}
          execution:
            steps:
              - step:
                  name: docker-login
                  identifier: dockerlogin
                  template:
                    templateRef: dockerlogin
                    versionLabel: V1
              - step:
                  type: Run
                  name: docker build
                  identifier: docker_build
                  spec:
                    shell: Bash
                    command: |-
                      docker build -f service.Dockerfile .
                      docker build -f task.Dockerfile .
