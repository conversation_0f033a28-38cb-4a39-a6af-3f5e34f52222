# https://github.com/protocolbuffers/protobuf/releases/latest
PROTOC_VERSION="30.1"
# https://pkg.go.dev/google.golang.org/protobuf/cmd/protoc-gen-go
PROTO_GEN_GO_VERSION="1.36.5"
# https://pkg.go.dev/google.golang.org/grpc/cmd/protoc-gen-go-grpc
PROTO_GEN_GO_GRPC_VERSION="1.5.1"
# https://github.com/golangci/golangci-lint/releases/latest
GOLANGCI_LINT_VERSION="2.0.2"
# https://github.com/uber-go/mock/releases
MOCK_GEN_VERSION="0.5.0"

PROTO_DIR="proto3"
PROTO_GEN_DIR="pbgen"
CURR_DIR=$(shell pwd)



RED    = \033[0;31m
GREEN  = \033[0;32m
YELLOW = \033[0;33m
BLUE   = \033[0;36m
RESET  = \033[0m

.PHONY: help
help: ## Display this help.
	@awk 'BEGIN {FS = ":.*##"; printf "\nUsage:\n  make ${BLUE}<target>${RESET}\n\n"} /^[a-zA-Z_0-9-]+:.*?##/ { printf "  ${BLUE}%-30s${RESET} %s\n", $$1, $$2 } /^##@/ { printf "\n\033[1m%s${RESET}\n", substr($$0, 5) }' $(MAKEFILE_LIST)

clean-proto-gen-files:
	@rm -rf ${PROTO_GEN_DIR}

##@ Codegen

cleanup-protoc: ## cleanup proto files
	rm -rf ./protoc/bin
	rm -rf ./protoc/include
	rm -f ./protoc/readme.txt

cleanup-protoc-gen-go: ## delete protoc-gen-proto
	rm -f ./protoc/bin/protoc-gen-go

cleanup-protoc-gen-go-grpc: ## delete protoc-gen-go-grpc
	rm -f ./protoc/bin/protoc-gen-go-grpc

install-protoc: cleanup-protoc ## Install protoc tool
	./protoc/install_protoc.sh $(PROTOC_VERSION)

install-protoc-gen-go: cleanup-protoc-gen-go ## Install protoc generation for golang
	GOBIN=${CURR_DIR}/protoc/bin go install google.golang.org/protobuf/cmd/protoc-gen-go@v${PROTO_GEN_GO_VERSION}

install-protoc-gen-go-grpc: cleanup-protoc-gen-go-grpc ## install protoc gRPC generation for golang
	GOBIN=${CURR_DIR}/protoc/bin go install google.golang.org/grpc/cmd/protoc-gen-go-grpc@v${PROTO_GEN_GO_GRPC_VERSION}

check-protoc:
	@./protoc/bin/protoc --version | grep -qF "$(PROTOC_VERSION)" || { \
		echo "protoc not found or version mismatch. Installing..."; \
		$(MAKE) install-protoc; \
	}

check-protoc-gen-go:
	@./protoc/bin/protoc-gen-go --version | grep -qF "$(PROTO_GEN_GO_VERSION)" || { \
		echo "protoc-gen-go not found or version mismatch. Installing..."; \
		$(MAKE) install-protoc-gen-go; \
	}

check-protoc-gen-go-grpc:
	@./protoc/bin/protoc-gen-go-grpc --version | grep -qF "$(PROTO_GEN_GO_VERSION_GRPC)" || { \
		echo "protoc-gen-go-grpc not found or version mismatch. Installing..."; \
		$(MAKE) install-protoc-gen-go-grpc; \
	}

check-toolchains: check-protoc check-protoc-gen-go check-protoc-gen-go-grpc ## Validate if toolchain is setup correctly


cleanup-mock: ## Remove mock dir
	rm -rf ./bin/mockgen

check-mock:
	@./bin/mockgen --version | grep -qF "$(MOCK_GEN_VERSION)" || { \
		echo "mockgen not found or version mismatch. Installing..."; \
		$(MAKE) install-mock; \
	}

install-mock: ## Install mock tool
	GOBIN=${CURR_DIR}/bin go install go.uber.org/mock/mockgen@v$(MOCK_GEN_VERSION)

clean-gen-mock: ## install generated mock files
	@rm -f pkg/helmx/mock_gen.go
	@rm -f pkg/providers/aws/mock_gen.go
	@rm -f pkg/providers/gcp/mock_gen.go
	@rm -f pkg/providers/azr/mock_gen.go
	@rm -f pkg/providers/k8s/mock_gen.go
	@rm -f pkg/providers/psql/mock_gen.go
	@rm -f pkg/providers/telemetry/mock_gen.go
	@rm -f pbgen/services/aws/mock_gen.go
	@rm -f pbgen/services/gcp/mock_gen.go
	@rm -f pbgen/services/azr/mock_gen.go
	@rm -f pbgen/services/byoc/mock_gen.go
	@rm -f pbgen/services/k8s/mock_gen.go
	@rm -f pbgen/services/rwc/mock_gen.go
	@rm -f pbgen/services/task/mock_gen.go
	@rm -f pbgen/services/telemetry/prometheus/mock_gen.go
	@rm -f pbgen/services/psql/mock_gen.go

gen-mock: check-mock clean-gen-mock ## generate mocks
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/helmx -package=helmx -destination=pkg/helmx/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/helmx ServiceInterface
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/aws -package=aws -destination=pkg/providers/aws/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/aws S3ClientInterface,EC2ClientInterface,RDSClientInterface
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/gcp -package=gcp -destination=pkg/providers/gcp/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/gcp GcsClient,ObjectIterator,BucketHandle,ObjectHandle,SQLClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/azr -package=azr -destination=pkg/providers/azr/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/azr AzBlobClientInterface,AzNicClientInterface,PGServerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/k8s -package=k8s -destination=pkg/providers/k8s/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/k8s ProviderInterface
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/psql -package=psql -destination=pkg/providers/psql/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/psql Provider
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/rwc -package=rwc -destination=pkg/providers/rwc/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/rwc ProviderInterface
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/providers/telemetry -package=telemetry -destination=pkg/providers/telemetry/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/providers/telemetry PrometheusProvider,MetricsPuller
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/aws -package=aws -destination=pbgen/services/aws/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/aws AwsResourceManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/gcp -package=gcp -destination=pbgen/services/gcp/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/gcp GcpResourceManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/azr -package=azr -destination=pbgen/services/azr/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/azr AzrResourceManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/byoc -package=byoc -destination=pbgen/services/byoc/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/byoc ByocResourceManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/k8s -package=k8s -destination=pbgen/services/k8s/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/k8s K8SResourceManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/rwc -package=rwc -destination=pbgen/services/rwc/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/rwc RisingwaveControlClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/task -package=task -destination=pbgen/services/task/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/task TaskManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus -package=prometheus -destination=pbgen/services/telemetry/prometheus/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/telemetry/prometheus PrometheusClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pbgen/services/psql -package=psql -destination=pbgen/services/psql/mock_gen.go github.com/risingwavelabs/cloudagent/pbgen/services/psql PsqlManagerClient
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/risectl -package=risectl -destination=pkg/risectl/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/risectl Executor
	@./bin/mockgen -self_package=github.com/risingwavelabs/cloudagent/pkg/k8s -package=k8s -destination=pkg/k8s/mock_gen.go github.com/risingwavelabs/cloudagent/pkg/k8s KubernetesClientInterface

gen-proto: check-toolchains clean-proto-gen-files ## generate proto files
	@mkdir -p ${PROTO_GEN_DIR}
	@./protoc/bin/protoc \
		--plugin=protoc-gen-go=protoc/bin/protoc-gen-go \
		--plugin=protoc-gen-go-grpc=protoc/bin/protoc-gen-go-grpc \
		--go_out=${PROTO_GEN_DIR} --go_opt=paths=import \
		--go_opt=module=github.com/risingwavelabs/cloudagent/pbgen \
		--go-grpc_out=${PROTO_GEN_DIR} --go-grpc_opt=paths=import \
		--go-grpc_opt=module=github.com/risingwavelabs/cloudagent/pbgen \
		--proto_path=$(PROTO_DIR) \
		$(shell find $(PROTO_DIR) -name "*.proto")

codegen: prepare-venv gen-proto gen-mock gen-python-proto3-client ## Run codegen

##@ linting and formatting

check-golangci-lint:
	@./bin/golangci-lint --version | grep -qF "$(GOLANGCI_LINT_VERSION)" || { \
		echo "golangci-lint not found or version mismatch. Installing..."; \
		$(MAKE) install-golangci-lint; \
	}

install-golangci-lint: ## install golang lint
	curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s v${GOLANGCI_LINT_VERSION}

install-black: ## install python formatter
	pip3 install black

black-setup: install-black ## setup python formatter
	python3 -m black . --exclude venv  # Exclude virtual environment if present

format-py: install-black black-setup ## fromatl all python file
	find . -name '*.py' -exec black {} \;

lint: check-golangci-lint ## Run golang lint
	./bin/golangci-lint run --config .golangci.yaml --timeout=30m

lint-fix: check-golangci-lint ## Fix golang lint errors
	./bin/golangci-lint run --config .golangci.yaml --fix --timeout=30m


##@ build


build: codegen ## Build the CloudAgent
	go build -o bin/agent ./cmd/agent/main.go

bin: build ## alias for build

binary: build ## alias for build

ut: codegen ## Run unittests
	COLOR=ALWAYS go test -race -covermode=atomic -coverprofile=coverage.out -tags ut ./...
	go tool cover -html coverage.out -o coverage.html

check-diff:
	@echo =======uncommitted changes========
	@echo $$(git diff --name-only);
	@echo $$(git clean -d --dry-run | grep -o '\S*$$');
	@echo ============= end ================
	@if [ -n "$$(git diff --name-only)" ] || [ -n "$$(git clean -d --dry-run | grep -o '\S*$$')" ]; then \
		echo "Error: There are uncommitted changes"; \
		exit 1; \
	fi

codegen-check: codegen check-diff

##@ mod tidy

mod-tidy: ## Run go mod tidy
	go mod tidy

mod-tidy-check: mod-tidy check-diff

##@ tests

E2E_DIR=test/e2e
VENV_DIR=${E2E_DIR}/venv
PYTHON3_BIN=${VENV_DIR}/bin/python3
SHELL=/bin/bash # this is needed to use `source` command
PYTHON_GRPC_OUTPUT_PATH=${E2E_DIR}/protopy
venv-freeze:
	$(PYTHON3_BIN) -m pip freeze > ${E2E_DIR}/requirements.txt


clean:
	@source ${VENV_DIR}/bin/activate && cd test/e2e && python3 run.py cleanup

e2e-aws: clean ## Run AWS e2e tests
	@source ${VENV_DIR}/bin/activate && cd test/e2e && pwd && python3 -m pytest -s testing/aws

e2e-k8s: clean ## Run K8s e2e tests
	@source ${VENV_DIR}/bin/activate && cd test/e2e && pwd && python3 -m pytest -s testing/k8s

e2e-rwc: clean ## Run AWS RWC tests
	@source ${VENV_DIR}/bin/activate && cd test/e2e && pwd && python3 -m pytest -s testing/rwc

pytest: ## Run a single test, with 'make pytest K=my_test'
	@source ${VENV_DIR}/bin/activate && cd test/e2e && pwd && python3 -m pytest -s testing -k "${K}"

e2e: gen-python-proto3-client e2e-aws e2e-rwc e2e-k8s


##@ local dev env

clean-python-proto3-client: ## remove python client
	@rm -rf ${PYTHON_GRPC_OUTPUT_PATH}

gen-python-proto3-client: clean-python-proto3-client ## Generate python clinet
	@mkdir -p ${PYTHON_GRPC_OUTPUT_PATH}
# TODO: A temporary fix for import statements inside protopy package
	@printf "import sys\nimport os\nfrom environment.base import E2E_TEST_FOLDER_PATH\nsys.path.insert(1, os.path.join(E2E_TEST_FOLDER_PATH, \"protopy\"))\n" >> ${PYTHON_GRPC_OUTPUT_PATH}/__init__.py
	@source ${VENV_DIR}/bin/activate && python3 -m grpc_tools.protoc -I$(PROTO_DIR) \
	--python_out=${PYTHON_GRPC_OUTPUT_PATH} \
	--grpc_python_out=${PYTHON_GRPC_OUTPUT_PATH} \
	--pyi_out=${PYTHON_GRPC_OUTPUT_PATH} \
	$(shell find $(PROTO_DIR) -name "*.proto")

cleanup-venv: ## cleanup python virtual environment
	rm -rf ${VENV_DIR}

prepare-venv: cleanup-venv ## Setup the python virtual environment
	python3 -m venv ${VENV_DIR}
	source ${VENV_DIR}/bin/activate
	$(PYTHON3_BIN) -m pip install  --upgrade --force-reinstall -r ${E2E_DIR}/requirements.txt
	@echo
	@echo "=============================================="
	@echo 'use the following statements to activate venv:'
	@echo
	@echo 'source ${VENV_DIR}/bin/activate'
	@echo "=============================================="

setup: prepare-venv ## Setup the local cluster
	@source ${VENV_DIR}/bin/activate && \
	$(PYTHON3_BIN) test/e2e/run.py cleanup && \
	$(PYTHON3_BIN) test/e2e/run.py shutdown && \
	$(PYTHON3_BIN) test/e2e/run.py setup

log: ## Receive logs from cloud agent
	kubectl --context kind-cloudagent-test wait --for=condition=Available --timeout=5m -n cloudagent deployment/cloudagent
	kubectl --context kind-cloudagent-test logs -l app=cloudagent -n cloudagent --follow

reload: ## Reload the cloud agent
	kubectl --context kind-cloudagent-test rollout restart deploy/cloudagent -n cloudagent
	kubectl --context kind-cloudagent-test wait --for=condition=Available --timeout=5m -n cloudagent deployment/cloudagent
	kubectl --context kind-cloudagent-test logs -l app=cloudagent -n cloudagent --follow

reload-task-runner: ## Reload task runner
	@source ${VENV_DIR}/bin/activate && python3 test/e2e/run.py step reload_task_runner

teardown: ## Remove the current dev cluster
	@source ${VENV_DIR}/bin/activate && python3 test/e2e/run.py shutdown

step: ## Step to NAME
	@source ${VENV_DIR}/bin/activate && python3 test/e2e/run.py step ${NAME}

shfmt:  ## format sh scripts
	GOBIN=$(CURR_DIR)/bin go install mvdan.cc/sh/v3/cmd/shfmt@latest
	sudo shfmt -s -w .

