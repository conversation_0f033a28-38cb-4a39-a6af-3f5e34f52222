# Cloud Agent
The Cloud Agent is a cloud resources managing agent. It is initially designed for BYOC 
use case. BYOC users use the control plane managed by RisingWave Cloud to manage their 
RisingWave clusters. Users can deploy the Agent in their own Kubernetes cluster at 
their own cloud, to let the Agent manage the necessary cloud resources without putting 
the cloud account credentials in the RisingWave Cloud. 

The Agent also works with cloud resources controller for Kubernetes solution (e.g. 
crossplane, AWS controller for Kubernetes) to safely manage the cloud resources. 

## Deployment
The Cloud Agent should be run without replicas, since it uses goroutine to guarantee 
guarantee the idempotency of the `Create` functions. Check `taskqueue` for more details. 

## Development

### Prerequisites
Linux or MacOS, go 1.20+, docker, helm, AWS CLI, kind

### Generate code
The Agent uses gRPC protocol to interact with the control plane in RisingWave Cloud. The 
first step of development is generating the gRPC code. The following command will install 
`protoc`, `protoc-gen-go`, and `protoc-gen-go-grpc` in the `proto3` folder. All generateed 
code is placed in the `pbgen` folder.  
```bash
make codegen
```

### Setup Local Development Environment

Create a AWS IAM user for the CloudAgent. Login to the `AWS - RWC Test`  via the 
[Microsoft Login](https://myapplications.microsoft.com). Go to [IAM users](https://us-east-1.console.aws.amazon.com/iam/home?region=us-east-1#/users)
and cerate a new user. The user should have the 
[admin](https://us-east-1.console.aws.amazon.com/iam/home?region=us-east-1#/groups/details/admin?section=users) user group. 
After creating the user also create access keys and then login via `aws configure` using the new credentials.


The local development framework also depends on gcloud for authentication. Unlike AWS, GCP auth,
by default, relies on short-lived tokens that have to be renewed every hour. Before running ```make
setup```, if you are not yet logged in, use the following to refresh your 
[GCP ADC](https://cloud.google.com/docs/authentication/provide-credentials-adc).
```bash
gcloud auth application-default login
```

Furthermore, your GCP account must also have the [service-account-token-creator role](https://cloud.google.com/iam/docs/service-account-overview#token-creator-role) for
impersonating the necessary service account. To add this role, use the following snippet. If you encounter any problems feel free to contact [me](<EMAIL>)
or [junfeng](<EMAIL>)
```bash
gcloud projects add-iam-policy-binding rwcdev \
    --member=user:${GCP_ACC} \
    --role=roles/iam.serviceAccountTokenCreator
```

Since the Cloud Agent uses Kubernetes cluster as the control plane, it lives in a cluster. 
It also requires crossplane runtime and ACK resource controllers running in the same cluster.
The following command will help you setup a kind cluster in the local Docker engine, then it 
will start crossplane runtime and ACK resource controllers in the cluster, and also install
the Cloud Agent using a shell container. The shell container has a Go development environment, 
it uses volumes to mount the source code and Go cache to the container, so that you can easily
apply the change in code to the running Cloud Agent. 
```bash
make setup
```

Use the following command to re-build the Cloud Agent in the kind cluster
```bash
make reload
``` 

Note that this command will not reload the task runner. If you want to reload the task runner, please run
```bash
make reload-task-runner
```

You may want to check the log of the Cloud Agent:
```bash
kubectl logs -l app=cloudagent -n cloudagent --follow
```

### Testing with `grpcurl`

On mac you can install the tool via 
```bash
brew install grpcurlc
```

```bash
# get the CloudAgent version
grpcurl -cacert certs/ca.crt -cert certs/tls.crt -key certs/tls.key -d '{}' localhost:30120 services.agent.Agent/GetVersion

# Find an example cerate command in example_create.sh
```

Receive the needed payload for testing by utilizing the pytests, e.g. `make pytest K=test_get_create_payload`. You can also re-write these tests to create/update/delete a rw instance for you.


### Make changes to the development environment
All local development dependencies are loaded in `test/e2e/environment/base.py`, if you want 
to just re-run one of the step instead of re-run the whole setup procedure, you can just run
```bash
make step NAME=<function name>
```
For instance, if you want to re-run `deploy_cloud_agent` function in `base.py`, then
```bash
make step NAME=deploy_cloud_agent
```
And if you want to reload the taskrunner image, just run
```bash
make step NAME=build_task_runner
make step NAME=load_task_runner
```

### End to end tests
The end to end tests requires a functional Cloud Agent running in a Kubernetes cluster. Before
run end to end tests, make sure the development enviornment is setup at first:
```bash
make setup
```
The end to end tests are written in Python. The following command will setup a Python virtual
environment, install the dependencies, and run the tests. 
```bash
make e2e
```
If some bugs occur during the test, you can use the following command to clean up the test
```bash
make clean
```
This command will first delete everything presenting in the `test` namespace, and then use cloud 
client ([boto3](https://boto3.amazonaws.com/v1/documentation/api/latest/reference/services/ec2.html), ...)
to directly delete all resources created by the test.

If you want to reuse the end to end tests in other CloudAgent enviornment, you can specify the endpoint by
```bash
$ cd test/e2e
$ source venv/bin/activate
$ (venv) python3 -m pytest -q --endpoint=localhost:40001
```

### Run a single test
The end to end tests are built based on the `pytest` framework. To run a single test, it is recommended to use `-k` flag in `pytest`.
This is also wrapped in Makefile
```bash
make pytest K=test_validate_source
```

### mTLS
A self-signed root CA and server certificate are already generated and placed in ./certs.
These files are generated by the following steps:
```bash
openssl req -x509 -nodes -new -sha256 -days 1024 -newkey rsa:2048 -keyout ca.key -out ca.pem -subj /C=US/CN=Example-Root-CA
openssl x509 -outform pem -in ca.pem -out ca.crt
openssl req -new -nodes -newkey rsa:2048 -keyout tls.key -out tls.csr -subj /C=US/ST=YourState/L=YourCity/O=Example-Certificates/CN=localhost.local
openssl x509 -req -sha256 -days 1024 -in tls.csr -CA ca.pem -CAkey ca.key -CAcreateserial -extfile domains.ext -out tls.crt
```
The content of domains.ext is like
```
authorityKeyIdentifier=keyid,issuer
basicConstraints=CA:FALSE
keyUsage = digitalSignature, nonRepudiation, keyEncipherment, dataEncipherment
subjectAltName = @alt_names
[alt_names]
DNS.1 = localhost
DNS.2 = fake1.local
DNS.3 = fake2.local
```
Note that this key pair cannot be used in production.


