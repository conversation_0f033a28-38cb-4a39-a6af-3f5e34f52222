# Refer to golangci-lint's example config file for more options and information:
# https://github.com/golangci/golangci-lint/blob/master/.golangci.reference.yml

version: "2"

run:
  timeout: 5m
  modules-download-mode: readonly
  go: "1.24"

staticcheck:
  go: "1.24"
  # https://staticcheck.io/docs/options#checks
  checks: [ "all" ]

stylecheck:
  go: "1.24"

linters-settings:
  goimports:
    # Put imports beginning with prefix after 3rd-party packages.
    # It's a comma-separated list of prefixes.
    local-prefixes: github.com/risingwavelabs/cloudagent

linters:
  enable:
    - bodyclose
    - errcheck
    - exhaustive
    - exptostd
    - gocritic
    - godot
    - govet
    - musttag
    - noctx
    - protogetter
    - revive
    - rowserrcheck
    - sqlclosecheck
    - staticcheck
    - unused
    - usestdlibvars
    - whitespace

  exclusions:
    presets:
      - comments
      - std-error-handling

formatters:
  enable:
    - gofmt
    - goimports

issues:
  fix: false
