grpcurl -cacert certs/ca.crt -cert certs/tls.crt -key certs/tls.key -d '{
  "resource_meta": {
    "id": "test",
    "namespace": "test"
  },
  "risingwave_spec": {
    "image": "ghcr.io/risingwavelabs/risingwave:v1.4.0",
    "enable_default_service_monitor": true,
    "state_store": {
      "data_directory": "test",
      "memory_state_store": {}
    },
    "meta_store_spec": {
      "etcd_backend": {
        "endpoint": "risingwave-etcd:2388"
      }
    },
    "config": {
      "node_config": {
        "node_configuration_config_map": {
          "name": "test-cm",
          "key": "key"
        }
      }
    },
    "frontend_service_type": "SERVICE_TYPE_NODE_PORT",
    "components": {
      "meta_spec": {
        "node_groups": [
          {
            "name": "default",
            "replicas": 1,
            "upgrade_strategy": {
              "type": "ROLLING_UPDATE"
            },
            "node_pod_spec": {
              "affinity": {},
              "container_spec": {
                "resources": {
                  "cpu_request": "1",
                  "cpu_limit": "1",
                  "memory_request": "2Gi",
                  "memory_limit": "2Gi"
                },
                "envs": [
                  {
                    "key": "key",
                    "value": "value"
                  }
                ]
              },
              "labels": {
                "key": "test-pod",
                "value": "1"
              }
            }
          }
        ]
      },
      "frontend_spec": {
        "node_groups": [
          {
            "name": "default",
            "replicas": 1,
            "upgrade_strategy": {
              "type": "ROLLING_UPDATE"
            },
            "node_pod_spec": {
              "affinity": {},
              "container_spec": {
                "resources": {
                  "cpu_request": "1",
                  "cpu_limit": "1",
                  "memory_request": "2Gi",
                  "memory_limit": "2Gi"
                },
                "envs": [
                  {
                    "key": "key",
                    "value": "value"
                  }
                ]
              },
              "labels": {
                "key": "test-pod",
                "value": "1"
              }
            }
          }
        ]
      },
      "compute_spec": {
        "node_groups": [
          {
            "name": "default",
            "replicas": 1,
            "upgrade_strategy": {
              "type": "ROLLING_UPDATE"
            },
            "node_pod_spec": {
              "affinity": {},
              "container_spec": {
                "resources": {
                  "cpu_request": "1",
                  "cpu_limit": "1",
                  "memory_request": "2Gi",
                  "memory_limit": "2Gi"
                },
                "envs": [
                  {
                    "key": "key",
                    "value": "value"
                  }
                ]
              },
              "labels": {
                "key": "test-pod",
                "value": "1"
              }
            }
          }
        ]
      },
      "compactor_spec": {
        "node_groups": [
          {
            "name": "default",
            "replicas": 1,
            "upgrade_strategy": {
              "type": "ROLLING_UPDATE"
            },
            "node_pod_spec": {
              "affinity": {},
              "container_spec": {
                "resources": {
                  "cpu_request": "1",
                  "cpu_limit": "1",
                  "memory_request": "2Gi",
                  "memory_limit": "2Gi"
                },
                "envs": [
                  {
                    "key": "key",
                    "value": "value"
                  }
                ]
              },
              "labels": {
                "key": "test-pod",
                "value": "1"
              }
            }
          }
        ]
      },
      "connector_spec": {
        "node_groups": [
          {
            "name": "default",
            "replicas": 1,
            "upgrade_strategy": {
              "type": "ROLLING_UPDATE"
            },
            "node_pod_spec": {
              "affinity": {},
              "container_spec": {
                "resources": {
                  "cpu_request": "1",
                  "cpu_limit": "1",
                  "memory_request": "2Gi",
                  "memory_limit": "2Gi"
                },
                "envs": [
                  {
                    "key": "key",
                    "value": "value"
                  }
                ]
              },
              "labels": {
                "key": "test-pod",
                "value": "1"
              }
            }
          }
        ]
      },
      "standalone_component": {
        "log_level": "info",
        "replicas": 1,
        "upgrade_strategy": {
          "type": "ROLLING_UPDATE"
        },
        "node_pod_spec": {
          "affinity": {},
          "container_spec": {
            "resources": {
              "cpu_request": "250m",
              "cpu_limit": "500m",
              "memory_request": "64Mi",
              "memory_limit": "128Mi"
            },
            "envs": [
              {
                "key": "envName",
                "value": "envVal"
              }
            ]
          },
          "labels": {
            "key": "test-pod",
            "value": "1"
          },
          "annotations": {
            "key": "annotationKey",
            "value": "annotationVal"
          }
        }
      }
    },
    "enable_standalone_mode": true
  },
  "labels": {
    "cloud.risingwavelabs.com/tier": "Dummy-Tier",
    "cloud.risingwavelabs.com/tenant-user": "1",
    "cloud.risingwavelabs.com/tenant-resource-namespace": "test",
    "cloud.risingwavelabs.com/tenant-name": "testrwinstance"
  },
  "annotations": {
    "risingwave.risingwavelabs.com/inherit-label-prefix": "cloud.risingwavelabs.com"
  }
}' localhost:30120 services.k8s.K8sResourceManager/CreateRisingWave