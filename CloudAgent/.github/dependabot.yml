# To get started with Dependabot version updates, you'll need to specify which
# package ecosystems to update and where the package manifests are located.
# Please see the documentation for all configuration options:
# https://docs.github.com/github/administering-a-repository/configuration-options-for-dependency-updates

version: 2
updates:
   # Maintain dependencies for GitHub Actions
  - package-ecosystem: "github-actions"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "sunday"
    reviewers:
      - "risingwavelabs/cloud-agent-dependabot-review"
  # Maintain dependencies for Go modules
  - package-ecosystem: "gomod"
    directory: "/"
    schedule:
      interval: "weekly"
      day: "sunday"
    reviewers:
      - "risingwavelabs/cloud-agent-dependabot-review"
    groups:
      aws:
        patterns:
        - github.com/aws/*
      google-cloud:
        patterns:
        - github.com/googleapis/*
        - google.golang.org/api
        - google.golang.org/grpc
        - cloud.google.com/go/*
      azure:
        patterns:
        - github.com/Azure/*
      k8s:
        patterns:
        - k8s.io/*
        - sigs.k8s.io/*
      ack:
        patterns:
        - github.com/aws-controllers-k8s/*
