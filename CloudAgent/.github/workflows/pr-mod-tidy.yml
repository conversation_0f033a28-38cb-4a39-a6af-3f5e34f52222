name: pr-mod-tidy
on:
  repository_dispatch:
    types: [pr-mod-tidy-command]
jobs:
  pr-mod-tidy:
    runs-on: ubuntu-latest
    steps:
      # Checkout the pull request branch
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.ACTIONS_BOT_TOKEN }}
          repository: ${{ github.event.client_payload.pull_request.head.repo.full_name }}
          ref: ${{ github.event.client_payload.pull_request.head.ref }}
          submodules: 'recursive'

      - uses: actions/setup-go@v5
        with:
          go-version: '1.24'

      - name: mod tidy
        run: make mod-tidy

      # Commit the change to the PR branch
      - name: Commit to the PR branch
        run: |
          git config --global user.name 'ci'
          git config --global user.email '<EMAIL>'
          git add -A
          git commit -m "[mod-tidy-pr-command] mod tidy fix"
          git push

      - name: Add reaction
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.ACTIONS_BOT_TOKEN }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          reactions: hooray
