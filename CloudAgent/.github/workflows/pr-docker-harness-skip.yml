name: pr-docker

on:
  pull_request:
    branches:
      - main
      - release-**
    paths-ignore:
      - '.harness/ci/CloudAgentdocker.yaml'
      - '.harness/ci/input-set/docker_pull_request.yaml'
      - 'cmd/**'
      - 'pkg/**'
      - 'proto3/**'
      - 'go.mod'
      - 'go.sum'
jobs:
  CloudAgentdocker-docker_build_test:
    runs-on: ubuntu-22.04
    steps:
      - run: 'echo "always skip"'
