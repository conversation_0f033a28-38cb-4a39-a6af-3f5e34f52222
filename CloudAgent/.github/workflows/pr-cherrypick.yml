name: pr-cherrypick
on:
  repository_dispatch:
    types: [pr-cherrypick-command]
  pull_request_target:
    branches:
      - main
    types: [closed]
jobs:
  pr-cherrypick:
    runs-on: ubuntu-latest
    if: >
      (
      github.event_name == 'repository_dispatch' &&
      github.event.client_payload.pull_request.merged &&
      github.event.client_payload.pull_request.base.ref == 'main'
      ) || (
      github.event_name == 'pull_request_target' &&
      github.event.pull_request.merged
      )
    steps:
      - uses: actions/checkout@v4
        with:
          token: ${{ secrets.ACTIONS_BOT_TOKEN }}
          repository: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.pull_request.head.repo.full_name || github.repository }}
          ref: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.pull_request.merge_commit_sha || '' }}
          submodules: 'recursive'
      - name: Create cherrypick pull requests
        uses: risingwavelabs/backport-action@main
        with:
          github_token: ${{ secrets.ACTIONS_BOT_TOKEN }}
          label_pattern: ^cherrypick ([^ ]+)$
          target_branches: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.slash_command.args.named.branch || '' }}
          pull_number: ${{ github.event_name == 'repository_dispatch' && github.event.client_payload.pull_request.number || '' }}
          pull_title: '[cherrypick ${target_branch}] ${pull_title}'
          pull_description: |-
            Cherrypick of #${pull_number} to `${target_branch}`.
          experimental: >
            {
              "detect_merge_method": true
            }
      - name: Add reaction
        if: github.event_name == 'repository_dispatch'
        uses: peter-evans/create-or-update-comment@v4
        with:
          token: ${{ secrets.ACTIONS_BOT_TOKEN }}
          repository: ${{ github.event.client_payload.github.payload.repository.full_name }}
          comment-id: ${{ github.event.client_payload.github.payload.comment.id }}
          reactions: hooray
