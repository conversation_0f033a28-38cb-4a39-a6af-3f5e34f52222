name: Protobuf Breaking Check

on:
  pull_request:
    branches:
      - main
      - release-**
    paths:
      - 'proto3/**'
  workflow_dispatch:

jobs:
  buf-breaking-check:
    runs-on: ubuntu-latest
    name: Check breaking changes in Protobuf files
    steps:
      - uses: actions/checkout@v4
      - uses: bufbuild/buf-setup-action@v1
        with:
          github_token: ${{ github.token }}
      # Run breaking change detection against the `main` branch
      - uses: bufbuild/buf-breaking-action@v1
        with:
          input: 'proto3'
          against: 'https://github.com/risingwavelabs/CloudAgent.git#branch=main,subdir=proto3'
